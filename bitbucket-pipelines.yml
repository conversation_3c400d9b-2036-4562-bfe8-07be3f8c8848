image: atlassian/default-image:3
pipelines:
  pull-requests:
    '**':
      - step:
          name: Check if master is merged
          script:
            - git fetch origin master
            - >
              if ! git merge-base --is-ancestor origin/master HEAD; then
                echo "ERROR: The branch is not up-to-date with master. Please merge master into your branch before creating a PR."
                exit 1
              fi






