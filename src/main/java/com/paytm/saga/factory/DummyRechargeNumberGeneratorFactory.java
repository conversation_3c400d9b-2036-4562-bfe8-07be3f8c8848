package com.paytm.saga.factory;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.service.DummyRechargeNumberGenerator;
import com.paytm.saga.service.LoanDummyRechargeNumberGenerator;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class DummyRechargeNumberGeneratorFactory implements ApplicationContextAware {
    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public DummyRechargeNumberGenerator getGenerator(String service) {
        switch (service){
            case Constants.LOAN : return applicationContext.getBean(LoanDummyRechargeNumberGenerator.class);
            default:return null;
        }
    }
}