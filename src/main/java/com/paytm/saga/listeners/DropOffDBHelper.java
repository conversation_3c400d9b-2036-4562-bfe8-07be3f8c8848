package com.paytm.saga.listeners;

import java.util.List;

import com.paytm.saga.service.EncryptionDecision;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.DropOffConfig;
import com.paytm.saga.dto.DropOffConfigByService;
import com.paytm.saga.dto.ResponsePage;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.repository.DropOffRepository;
import com.paytm.saga.service.ChannelHistoryService;

@Component
public class DropOffDBHelper {
	private final ChannelHistoryService channelHistoryService;
	private final DropOffRepository dropOffRepository;
	private final DropOffConfigByService dropOffConfigByService;

	@Autowired
	public DropOffDBHelper(@NonNull ChannelHistoryService channelHistoryService,
			@NonNull DropOffRepository dropOffRepository, @NonNull DropOffConfigByService dropOffConfigByService) {
		this.dropOffRepository = dropOffRepository;
		this.channelHistoryService = channelHistoryService;
		this.dropOffConfigByService = dropOffConfigByService;
	}

	public void insertDropOff(DropOff dropOff) {
		if(Constants.FINANCIAL_SERVICES.equalsIgnoreCase(dropOff.getService()) &&  EncryptionDecision.isDataEncryptionRequired(dropOff.getCustomerId())){
			return;
		}
		int ttl = this.getDropOffThreshold(dropOff, Constants.DROP_OFF_WRITE);
		if (ttl > 0) {
			// converting minutes to seconds
			ttl = ttl * 60;
			dropOffRepository.save(dropOff, ttl);
		} else {
			// set default to 7 days
			dropOffRepository.save(dropOff,30*24*60*60);
		}
	}

	public boolean isNewCustomer(DropOff dropOff, String reqType) {
		if (reqType != null && reqType.equals(Constants.DROP_OFF_WRITE)) {
			boolean isNewCustomer = this.isNewCustomer(dropOff.getCustomerId(), dropOff.getRechargeNumber(),
					dropOff.getService());
			if (isNewCustomer) {
				boolean newUser = true;
				dropOff.getBillsObj().put(Constants.NEW_USER_IDENTIFIER, "" + newUser);
				return true;
			}

		} else {
			if (dropOff.getBillsObj() != null) {
				String newUser = dropOff.getBillsObj().get(Constants.NEW_USER_IDENTIFIER);
				if (newUser != null) {
					return true;
				}
			}

		}
		return false;
	}

	public int getDropOffThreshold(DropOff dropOff, String reqType) {
		DropOffConfig dropOffConfig = dropOffConfigByService.getDropOffConfig(dropOff.getService());
		boolean isNewCustomer = isNewCustomer(dropOff, reqType);
		if (dropOffConfig != null) {
			int ttl = 0;
			if (isNewCustomer) {
				ttl = dropOffConfig.getNewUserTimeDropOffTime();
			} else {
				ttl = dropOffConfig.getRecurringUserDropOffTime();
			}
			return ttl;
		}
		return 0;
	}

	public void insertDropOff(List<DropOff> dropOffs) {
		for (DropOff dropOff : dropOffs) {
			this.insertDropOff(dropOff);
		}

	}

	public boolean isNewCustomer(long customerId, String rechargeNumber, String service) {
		ResponsePage<ChannelHistory> responsePage = channelHistoryService.getPageOfHistory(customerId, rechargeNumber,
				service, 1, null);
		if (responsePage == null || responsePage.getCount() == 0) {
			return true;
		} else {
			return false;
		}
	}
}
