package com.paytm.saga.listeners;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import com.paytm.saga.service.*;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.CreditCardUtils;
import com.paytm.saga.util.ServiceWrapperUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.lang.NonNull;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import com.newrelic.api.agent.Trace;
import com.paytm.saga.common.configuration.ValidationKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.CustomerDataResponseModel;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.dto.ValidationKafkaResponseModel;
import com.paytm.saga.dto.ValidationResponseDisplayValues;
import com.paytm.saga.dto.ValidationResponseMetaBenefitsModel;
import com.paytm.saga.dto.ValidationResponseMetaModel;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.common.metrics.MetricsHelper;

@Component
@DependsOn("ValidationKafkaPropertiesConfig")
@Conditional(ValidationKafkaConsumerEnableCondition.class)
public class ValidationListeners {
    private final CustomLogger logger = CustomLogManager.getLogger(ScratchCardListeners.class);
    private final MappingService mappingService;

    private SmartRecentsRepository smartRecentsRepository;
    private SmartRecentsService smartRecentsService;
    private RecentsRepositoryWrapperService recentsRepository;
    private final DCATService dcatService;
    private final DropOffDBHelper dropOffDBHelper;
    private final MetricsHelper metricsHelper;
    public static final String SERVICE_NAME = "VALIDATION_CONSUMER";
    private final RPSService rpsService;
    private static final String NEW_EVENT="NEW_KAFKA_EVENT";

    private KafkaProducerService kafkaProducerService;


    @Autowired
    public ValidationListeners(@NonNull MappingService mappingService,
                               @NonNull DCATService dcatService,
                               @NonNull RecentsRepositoryWrapperService recentsRepository,
                               @NonNull SmartRecentsRepository smartRecentsRepository,
                               @NonNull SmartRecentsService smartRecentsService,
                               @NonNull DropOffDBHelper dropOffDBHelper,
                               @NonNull MetricsHelper metricsHelper,
                               @NonNull RPSService rpsService,
                               @NonNull KafkaProducerService kafkaProducerService) {
        this.mappingService = mappingService;
        this.dcatService = dcatService;
        this.recentsRepository = recentsRepository;
        this.smartRecentsRepository = smartRecentsRepository;
        this.smartRecentsService = smartRecentsService;
        this.dropOffDBHelper = dropOffDBHelper;
        this.metricsHelper = metricsHelper;
        this.rpsService = rpsService;
        this.kafkaProducerService = kafkaProducerService;
    }

    //read this from bean of kafka config properties
    @KafkaListener(topics = "#{ValidationKafkaPropertiesConfig.getTopicName_DEFAULT_VALIDATION()}",
            groupId = "#{ValidationKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory ="ValidationkafkaListenerContainerFactory"
    )
    public void listen_DEFAULT_VALIDATION(@Payload List<String> messages, Acknowledgment acknowledgment) {

        logger.info("[ValidationListeners.listen] Size of batch is {}",messages.size());

        for (String message : messages) {
            metricsHelper.recordSuccessRate(SERVICE_NAME,NEW_EVENT);
            ValidationKafkaResponseModel validationKafkaResponseModel=null;
            //logger.info("[ValidationListeners.listen] temp message" + message );

            if(Objects.nonNull(message)) {
                validationKafkaResponseModel = JsonUtils.parseJson(message, ValidationKafkaResponseModel.class);
                //logger.info("[ValidationListeners.listen] parse done");
                insertDataIntoCassandra(validationKafkaResponseModel);
            }
        }
        logger.info("[ValidationListeners.listen] Acknowledging message");
        acknowledgment.acknowledge();
    }

    @KafkaListener(topics = "#{ValidationKafkaPropertiesConfig.getTopicName_RECHARGE_VALIDATION()}",
            groupId = "#{ValidationKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory ="ValidationkafkaListenerContainerFactory"
    )
    public void listen_RECHARGE_VALIDATION(@Payload List<String> messages, Acknowledgment acknowledgment) {

        logger.info("[ValidationListeners.listen] Size of batch is {}",messages.size());

        for (String message : messages) {
            metricsHelper.recordSuccessRate(SERVICE_NAME,NEW_EVENT);
            ValidationKafkaResponseModel validationKafkaResponseModel=null;
            //logger.info("[ValidationListeners.listen] temp message" + message );

            if(Objects.nonNull(message)) {
                validationKafkaResponseModel = JsonUtils.parseJson(message, ValidationKafkaResponseModel.class);
                //logger.info("[ValidationListeners.listen] parse done");
                insertDataIntoCassandra(validationKafkaResponseModel);
            }
        }
        logger.info("[ValidationListeners.listen] Acknowledging message");
        acknowledgment.acknowledge();
    }
    
    @KafkaListener(topics = "#{ValidationKafkaPropertiesConfig.getTopicName_BFSI_INSURANCE_VALIDATION()}",
            groupId = "#{ValidationKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory ="ValidationkafkaListenerContainerFactory"
    )
    public void listen_BFSI_INSURANCE(@Payload List<String> messages, Acknowledgment acknowledgment) {

        logger.info("[ValidationListeners.listen_BFSI_INSURANCE] Size of batch is {}",messages.size());

        for (String message : messages) {
            metricsHelper.recordSuccessRate(SERVICE_NAME,NEW_EVENT);
            ValidationKafkaResponseModel validationKafkaResponseModel=null;
            //logger.info("[ValidationListeners.listen] temp message" + message );

            if(Objects.nonNull(message)) {
                validationKafkaResponseModel = JsonUtils.parseJson(message, ValidationKafkaResponseModel.class);
                //logger.info("[ValidationListeners.listen_BFSI_INSURANCE] parse done");
                insertDataIntoCassandra(validationKafkaResponseModel);
            }
        }
        logger.info("[ValidationListeners.listen_BFSI_INSURANCE] Acknowledging message");
        acknowledgment.acknowledge();
    }

    @KafkaListener(topics = "#{ValidationKafkaPropertiesConfig.getTopicName_UTILITY_ELECTRICITY_VALIDATION()}",
            groupId = "#{ValidationKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory ="ValidationkafkaListenerContainerFactory"
    )
    public void listen_UTILITY_ELECTRICITY_VALIDATION(@Payload List<String> messages, Acknowledgment acknowledgment) {



        for (String message : messages) {
            metricsHelper.recordSuccessRate(SERVICE_NAME,NEW_EVENT);
            ValidationKafkaResponseModel validationKafkaResponseModel=null;
            //logger.info("[ValidationListeners.listen] temp message" + message );

            if(Objects.nonNull(message)) {
                validationKafkaResponseModel = JsonUtils.parseJson(message, ValidationKafkaResponseModel.class);
                //logger.info("[ValidationListeners.listen] parse done");
                insertDataIntoCassandra(validationKafkaResponseModel);
            }
        }
        logger.info("[ValidationListeners.listen] Acknowledging message");
        acknowledgment.acknowledge();
    }

    @Trace(dispatcher=true)
    public void insertDataIntoCassandra(ValidationKafkaResponseModel validationKafkaResponseModel){

        try{
            if(Objects.isNull(validationKafkaResponseModel) || isSkipable(validationKafkaResponseModel).equals(true)) {
            	metricsHelper.recordSuccessRate(SERVICE_NAME,Constants.IGNORE_TAG);
                logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "ValidationListeners.insertDataIntoCassandra::skipping : data {}", validationKafkaResponseModel);
                return;
            }
            createLag(validationKafkaResponseModel);

            String rechargeNumber  = CommonUtils.getRechargeNumberBasedOnRules(validationKafkaResponseModel.getUserData_recharge_number(), validationKafkaResponseModel.getProductInfo_operator());
            validationKafkaResponseModel.setUserData_recharge_number(rechargeNumber);

            DropOff dropOff = prepareDropOffData(validationKafkaResponseModel);

            SmartRecents smartRecents = prepareSmartRecentData(validationKafkaResponseModel);

            if(Arrays.asList(Constants.CommonConstants.VALIDATION_UNSUCCESSFULL_ERROR_CODES).contains(validationKafkaResponseModel.getRecGw_operatorResCode())
                    && Arrays.asList(Constants.CommonConstants.VALIDATION_UNSUCCESSFULL_SERVICE).contains(validationKafkaResponseModel.getProductInfo_service())){
                smartRecentsService.save(smartRecents);
                logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "saving successful for customerId : {} service : {} operator : {} rechargeNumber {}" ,smartRecents.getKey().getCustomerId() ,smartRecents.getKey().getService(), smartRecents.getKey().getOperator(),rechargeNumber);
                return;
            }

            /* Commenting because of High Priority of DropOff than the Pending transaction.
            // https://jira.mypaytm.com/browse/IN-15835?focusedCommentId=2426030&page=com.atlassian.jira.plugin.system.issuetabpanels%3Acomment-tabpanel#comment-2426030
            if(checkPendingTransaction(validationKafkaResponseModel, dropOff)){
                logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "ValidationListeners.insertDataIntoCassandra::skipping because of pending transaction" + validationKafkaResponseModel);
            }*/
            List<Recents> recents = recentsRepository.findBycustomerIdAndrechargeNumberAndservice(dropOff.getCustomerId(),dropOff.getRechargeNumber(),dropOff.getService());
            if(Objects.nonNull(recents)) {
                if(recents.size()==0){
                    logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "ValidationListeners.insertDataIntoCassandra saving {} validationStatus {}",dropOff,validationKafkaResponseModel.getValidationSuccessful());
                    dropOffDBHelper.insertDropOff(dropOff);
                } else{
                    List<Long> order_ids = new ArrayList<Long>();
                    for ( Recents recent : recents) {
                        order_ids.add(recent.getOrderId());
                    }
                    if(Collections.frequency(order_ids, null) == order_ids.size()){
                        logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "ValidationListeners.insertDataIntoCassandra saving: {} validationStatus {}",dropOff,validationKafkaResponseModel.getValidationSuccessful());
                        dropOffDBHelper.insertDropOff(dropOff);
                    }else{
                        logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "ValidationListeners.insertDataIntoCassandra : Not saving dropoff,user already exists : " + dropOff);

                    }
                }
                if(validationKafkaResponseModel.getValidationSuccessful() != null && validationKafkaResponseModel.getValidationSuccessful() == true && Arrays.asList(Constants.CommonConstants.VALIDATION_UNSUCCESSFULL_SERVICE).contains(validationKafkaResponseModel.getProductInfo_service())){
                    logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "ValidationListeners.insertDataIntoCassandra customer_id: {} recharge_number {}",validationKafkaResponseModel.getCustomerInfo_customer_id(),validationKafkaResponseModel.getUserData_recharge_number());
                    smartRecentsService.delete(smartRecents);
                }
            }
            mappingService.setRechargeNumberMap(
                    validationKafkaResponseModel.getUserData_recharge_number(),
                    validationKafkaResponseModel.getCustomerInfo_customer_id(),
                    validationKafkaResponseModel.getProductInfo_service(),
                    validationKafkaResponseModel.getProductInfo_operator(),
                    new Date());

        }
        catch(Exception e){
        	metricsHelper.recordSuccessRate(SERVICE_NAME,Constants.ERROR_TAG);
            logger.error(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.insertDataIntoCassandra] Exception {} for Data {}",e,validationKafkaResponseModel);
        }
    }
    public Boolean createLag(ValidationKafkaResponseModel validationKafkaResponseModel){
        Date messageTime = null; validationKafkaResponseModel.getTimestamps_init();
        try{
            messageTime = new SimpleDateFormat(Constants.CommonConstants.COMMON_DATE_FORMAT).parse(validationKafkaResponseModel.getTimestamps_init());
        } catch (Exception e){
            logger.error(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.createLag] messageTime parse: Exception", e);
        }

        Integer secondsDiff = Math.toIntExact(new Date().getTime()/1000) - Math.toIntExact(messageTime.getTime()/1000);

        Integer lagTime = Constants.CommonConstants.VALIDATION_CONSUMER_LAG_TIME - secondsDiff ;

        if(lagTime<0){
            return false;
        }

        logger.info("[ValidationListeners.createLag] Adding Lag of " + lagTime );


        try {
            TimeUnit.SECONDS.sleep(lagTime);
            return true;
        } catch (InterruptedException e) {
        	logger.error(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.createLag] Exception",e);
        }
        return false;
    }

    public Boolean isSkipable(ValidationKafkaResponseModel validationKafkaResponseModel){
        Long customerId = validationKafkaResponseModel.getCustomerInfo_customer_id();
        String channelId = validationKafkaResponseModel.getCustomerInfo_channel_id();
        Long categoryId = validationKafkaResponseModel.getProductInfo_category_id();
        String service = validationKafkaResponseModel.getProductInfo_service();

        if(Arrays.asList(Constants.CommonConstants.DROP_OFF_SKIP_CUSTOMER_ID).contains(customerId)){
            logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.isSkippable] skipping customerId" + customerId  );
            return true;
        }
        if(Arrays.asList(Constants.CommonConstants.DROP_OFF_SKIP_CHANNEL_ID).contains(channelId)){
            logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.isSkippable] skipping channelId" + channelId  );
            return true;
        }
        if(!Arrays.asList(Constants.CommonConstants.DROP_OFF_ENABLED_CATEGORY_IDS).contains(categoryId)){
            logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.isSkippable] skipping categoryId" + categoryId );
            return true;
        }
        if (Arrays.asList(Constants.CommonConstants.DROP_OFF_SKIP_VALIDATION_CATEGORY_IDS).contains(categoryId)) {
            logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.isSkippable] skipping validation categoryId {}", categoryId);
            return true;
        }
        //(useRedisData == null && validationSuccessful == false) || (useRedisData == null && validationSuccessful == null)
        if (validationKafkaResponseModel.getUseRedisData() == null
                && (validationKafkaResponseModel.getValidationSuccessful()==null
                    || validationKafkaResponseModel.getValidationSuccessful().equals(false))
                && (validationKafkaResponseModel.getRecGw_operatorResCode().equals(null)
                    || !(Arrays.asList(Constants.CommonConstants.VALIDATION_UNSUCCESSFULL_ERROR_CODES).contains(validationKafkaResponseModel.getRecGw_operatorResCode())
                        && Arrays.asList(Constants.CommonConstants.VALIDATION_UNSUCCESSFULL_SERVICE).contains(validationKafkaResponseModel.getProductInfo_service())))) {
            // Ignore failed Validations
            return true;
        }
        if(validationKafkaResponseModel.getProductInfo_service()!=null &&
            validationKafkaResponseModel.getProductInfo_service().equals(Constants.CommonConstants.CYLINDER_SERVICE)){
                if(validationKafkaResponseModel.getCustomerDataResponse()!=null &&
                    validationKafkaResponseModel.getCustomerDataResponse().getFlowType()!=null &&
                    validationKafkaResponseModel.getCustomerDataResponse().getFlowType().equals("100")){
                    // 100 -> track Delivery , 101 -> Pay IVRS
                    return true;
                }
        }

        if (validationKafkaResponseModel.getProductInfo_paytype() != null && validationKafkaResponseModel
				.getProductInfo_paytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
			if(validationKafkaResponseModel.getMetaData()==null 
                    || validationKafkaResponseModel.getUserData_recharge_number() == null
                    || ((validationKafkaResponseModel.getUserData_recharge_number_4() == null
                        || validationKafkaResponseModel.getMetaData().getPar() == null)
                        && validationKafkaResponseModel.getUserData_recharge_number_3() == null
                        && (validationKafkaResponseModel.getUserData_recharge_number_2() == null || !CreditCardUtils.inFormat(validationKafkaResponseModel.getUserData_recharge_number_2()))
                        && validationKafkaResponseModel.getMetaData().getCin() == null)) {
				return true;
			}
		}
        if(validationKafkaResponseModel.getProductInfo_paytype()!=null){
            if(validationKafkaResponseModel.getProductInfo_paytype().equals(Constants.CommonConstants.PREPAID_PAYTYPE)){
                if(validationKafkaResponseModel.getUserData_amount()==null ||
                        Double.valueOf(validationKafkaResponseModel.getUserData_amount())<=0){
                    return true;
                }
            }
        }

        if(Arrays.asList(Constants.CommonConstants.POSTPAID_PAYTYPES).contains(validationKafkaResponseModel.getProductInfo_paytype())){
            if(validationKafkaResponseModel.getCustomerDataResponse()!=null){
                if(validationKafkaResponseModel.getCustomerDataResponse().getCurrentBillAmount() != null && Double.valueOf(validationKafkaResponseModel.getCustomerDataResponse().getCurrentBillAmount()) <= 0){
                    return true;
                }
            }
        }

        Date transactionTime = null;
        try {
            transactionTime = new SimpleDateFormat(Constants.CommonConstants.COMMON_DATE_FORMAT).parse(validationKafkaResponseModel.getTimestamps_init());
        } catch (Exception e) {
            logger.error("[ValidationListeners.prepareValidationData]transactionTime:  Exception");
        }
        if(transactionTime==null){
            return true;
        }

        if(service!=null && service.equals(Constants.CommonConstants.DTH_SERVICE)){
            if(validationKafkaResponseModel.getMetaData()!=null){
                if(validationKafkaResponseModel.getMetaData().getAdditionalPayload()!=null){
                    if(validationKafkaResponseModel.getMetaData().getAdditionalPayload().getMultipleRMN()!=null &&
                            validationKafkaResponseModel.getMetaData().getAdditionalPayload().getMultipleRMN().equals(true)){
                        return true;
                    }
                }
            }
        }

        return false;

    }

    public SmartRecents prepareSmartRecentData(ValidationKafkaResponseModel validationKafkaResponseModel){
        SmartRecents smartRecents = new SmartRecents();
        Long customerId = validationKafkaResponseModel.getCustomerInfo_customer_id();
        Long productId = validationKafkaResponseModel.getProductId();
        Date createdTime = new Date();
        String service = validationKafkaResponseModel.getProductInfo_service();
        String operator = validationKafkaResponseModel.getProductInfo_operator();
        String paytype = validationKafkaResponseModel.getProductInfo_paytype();
        String circle = validationKafkaResponseModel.getProductInfo_circle();
        Date transactionTime = new Date();
        try {
            transactionTime = new SimpleDateFormat(Constants.CommonConstants.COMMON_DATE_FORMAT).parse(validationKafkaResponseModel.getTimestamps_init());
        } catch (Exception e) {
            logger.error("[ValidationListeners.prepareValidationData]transactionTime: Exception");
        }
        SmartRecentsPrimaryKey smartRecentsPrimaryKey = new SmartRecentsPrimaryKey();
        smartRecentsPrimaryKey.setCustomerId(customerId);
        smartRecentsPrimaryKey.setService(service);
        smartRecentsPrimaryKey.setOperator(operator);
        smartRecents.setKey(smartRecentsPrimaryKey);
        smartRecents.setProductId(productId);
        smartRecents.setPayType(paytype);
        smartRecents.setCircle(circle);
        smartRecents.setCreatedAt(createdTime);
        smartRecents.setUpdatedAt(transactionTime);
        smartRecents.setRechargeNumber("");
        smartRecents.setEventSource(Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION);
        return smartRecents;
    }

    public DropOff prepareDropOffData(ValidationKafkaResponseModel validationKafkaResponseModel){
        DropOff dropOff = new DropOff();

        Long categoryId = validationKafkaResponseModel.getProductInfo_category_id();
        Long customerId = validationKafkaResponseModel.getCustomerInfo_customer_id();
        Long productId = validationKafkaResponseModel.getProductId();
        Date createdTime = new Date();
        String uniqueId = null;
        String recharge_number_1 = validationKafkaResponseModel.getUserData_recharge_number();
        String recharge_number_2 = validationKafkaResponseModel.getUserData_recharge_number_2();
        String recharge_number_3 = validationKafkaResponseModel.getUserData_recharge_number_3();
        String recharge_number_4 = validationKafkaResponseModel.getUserData_recharge_number_4();
        String service = validationKafkaResponseModel.getProductInfo_service();
        String operator = validationKafkaResponseModel.getProductInfo_operator();
        String paytype = validationKafkaResponseModel.getProductInfo_paytype();
        String circle = validationKafkaResponseModel.getProductInfo_circle();
        String eventType = Constants.CommonConstants.VALIDATION_MESSAGE_TYPE;
        Date transactionTime = new Date();
        Map<String, String> customerDataResponse = getCustomerDataResponse(validationKafkaResponseModel);
        validationKafkaResponseModel.setMetaData(prepareMeta(validationKafkaResponseModel.getMetaData()));
        Map<String, String> displayValues = getDisplayValues(validationKafkaResponseModel);
        Map<String, String> billObj = getBillsObj(validationKafkaResponseModel);

        String amount = getDropOffAmount(validationKafkaResponseModel);

        try {
            transactionTime = new SimpleDateFormat(Constants.CommonConstants.COMMON_DATE_FORMAT).parse(validationKafkaResponseModel.getTimestamps_init());
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.prepareValidationData]transactionTime: Exception");
        }

        if(service != null){
            String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service.toLowerCase());

            if (DCATcategoryId != null && paytype.equalsIgnoreCase(Constants.CommonConstants.PREPAID_PAYTYPE) && "mobile".equalsIgnoreCase(service)) {
                if(billObj.get("plan_bucket")==null || displayValues.size()==0){
                    try{
                        logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "not getting plan bucket for recharge number{} customer id {} meta data {}",recharge_number_1,customerId,validationKafkaResponseModel.getMetaData());
                        DCATGetPlanResponse dcatGetPlanResponse = getPlanDetails(validationKafkaResponseModel, DCATcategoryId);
                        if(dcatGetPlanResponse!=null){
                            displayValues = updateDisplayValues(displayValues, dcatGetPlanResponse);
                        }
                        billObj =  updateBillsObj(billObj, dcatGetPlanResponse, validationKafkaResponseModel);
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.dcatGetPlanResponse error ]: Exception",e);
                    }
                }else{
                    logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "valid plan details for recharge number{} customer id {} meta data {} plan_bucket {} display_values {}",recharge_number_1,customerId,validationKafkaResponseModel.getMetaData(),billObj.get("plan_bucket"),displayValues);
                }

            }else if(validationKafkaResponseModel.getProductInfo_service().toLowerCase().equals(Constants.CommonConstants.DTH_SERVICE) && Objects.isNull(billObj.get(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY))) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "dth validation for recharge number{} customer id {} meta data {}",recharge_number_1,customerId,validationKafkaResponseModel.getMetaData());
                if(Arrays.asList(Constants.DTH_GROUP)
                        .contains(validationKafkaResponseModel.getProductInfo_operator().toLowerCase())){
                    billObj.put(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY, Constants.DTH_PLAN_BUCKET);
                }else if(validationKafkaResponseModel.getProductInfo_operator().toLowerCase().equals(Constants.SUNTV)){
                    billObj.put(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY, Constants.DTH_PLAN_BUCKET);
                }
            }
        }

        if(paytype !=null && paytype.equals(Constants.CREDIT_CARD_PAYTYPE)) {

            String mcn = recharge_number_1;
            billObj.put("mcn", mcn);

            if (recharge_number_4 != null && validationKafkaResponseModel.getMetaData().getPar() != null) {
                recharge_number_1 = validationKafkaResponseModel.getMetaData().getPar();
                billObj.put("par", recharge_number_1);
            }
            else if (recharge_number_3 != null) {
                recharge_number_1 = recharge_number_3;
                billObj.put("cin", recharge_number_3);
            }
            else if (recharge_number_2 != null && CreditCardUtils.inFormat(recharge_number_2)) {
                recharge_number_1 = CreditCardUtils.formatAndReturn(recharge_number_2);
                billObj.put("cin", recharge_number_1);
            }
            else if (validationKafkaResponseModel.getMetaData().getCin() != null) {
                recharge_number_1 = validationKafkaResponseModel.getMetaData().getCin();
                billObj.put("cin", validationKafkaResponseModel.getMetaData().getCin());
            }


            uniqueId = recharge_number_1;
            validationKafkaResponseModel.setUserData_recharge_number(uniqueId);


		}else if(service !=null && service.toLowerCase().equals(Constants.CommonConstants.CYLINDER_SERVICE)&& validationKafkaResponseModel.getMetaData() !=null && validationKafkaResponseModel.getMetaData().getCylinder_agency_name() !=null) {
			billObj.put(Constants.CYLINDER_AGENCY_NAME, validationKafkaResponseModel.getMetaData().getCylinder_agency_name());	
        }

        dropOff.setCategoryId(categoryId);
        dropOff.setCustomerId(customerId);
        dropOff.setProductId(productId);
        dropOff.setCreatedTime(createdTime);
        dropOff.setRechargeNumber(recharge_number_1);
        dropOff.setRecharge_number_2(recharge_number_2);
        dropOff.setRecharge_number_3(recharge_number_3);
        dropOff.setRecharge_number_4(recharge_number_4);
        dropOff.setService(service);
        dropOff.setOperator(operator);
        dropOff.setPaytype(paytype);
        dropOff.setAmount(amount);
        dropOff.setCircle(circle);
        dropOff.setEventType(eventType);
        dropOff.setTransactionTime(transactionTime);
        dropOff.setDisplayValues(displayValues);
        dropOff.setBillsObj(billObj);
        dropOff.setCustomerDataResponse(customerDataResponse);

        return dropOff;
    }

    public Map<String, String> updateBillsObj(Map<String, String> billsObj, DCATGetPlanResponse dcatGetPlanResponse, ValidationKafkaResponseModel validationKafkaResponseModel){

        if (dcatGetPlanResponse != null) {
            if (dcatGetPlanResponse.getPlan_id() != null)
                billsObj.put("plan_id", dcatGetPlanResponse.getPlan_id());
            if (dcatGetPlanResponse.getPlan_bucket() != null)
                billsObj.put("plan_bucket", dcatGetPlanResponse.getPlan_bucket());
        }

        return billsObj;
    }

    public Map<String, String> getBillsObj(ValidationKafkaResponseModel validationKafkaResponseModel){
        Map<String, String> billsObj = new HashMap<String, String>();

        if(validationKafkaResponseModel!=null){
            if(validationKafkaResponseModel.getCustomerDataResponse()!=null){
                if(validationKafkaResponseModel.getCustomerDataResponse().getCustomerName()!=null){
                    billsObj.put("consumerName", validationKafkaResponseModel.getCustomerDataResponse().getCustomerName());
                }
                if(validationKafkaResponseModel.getCustomerDataResponse().getRef_id()!=null){
                    billsObj.put("ref_id", validationKafkaResponseModel.getCustomerDataResponse().getRef_id());
                }
            }
            if(validationKafkaResponseModel.getDisplayValues()!=null && validationKafkaResponseModel.getDisplayValues().size()>0){
                for(ValidationResponseDisplayValues displayObj : validationKafkaResponseModel.getDisplayValues()){
                    if(displayObj!=null && displayObj.getLabel()!=null && displayObj.getLabel().equals("Consumer Name")){
                        if(displayObj.getValue()!=null){
                            if(billsObj.get("consumerName")==null){
                                billsObj.put("consumerName", displayObj.getValue());
                            }
                        }
                    }
                }
            }
            if(validationKafkaResponseModel.getMetaData()!=null){
                if (validationKafkaResponseModel.getMetaData().getPlan_bucket() != null)
                    billsObj.put("plan_bucket", validationKafkaResponseModel.getMetaData().getPlan_bucket());
                if(validationKafkaResponseModel.getMetaData().getPlan_id() != null){
                    billsObj.put("plan_id", validationKafkaResponseModel.getMetaData().getPlan_id());
                }
                if(validationKafkaResponseModel.getMetaData().getRMN()!=null){
                    billsObj.put("RMN", validationKafkaResponseModel.getMetaData().getRMN());
                }
            }

            if(validationKafkaResponseModel.getCustomerInfo_channel_id() != null){
                billsObj.put(Constants.CommonConstants.CHANNEL, validationKafkaResponseModel.getCustomerInfo_channel_id());
            }

        }

        return billsObj;
    }

    public Map<String, String> updateDisplayValues(Map<String, String> displayValues , DCATGetPlanResponse dcatGetPlanResponse){

        if(dcatGetPlanResponse!=null){
            if (dcatGetPlanResponse.getValidity() != null)
                displayValues.put("validity", dcatGetPlanResponse.getValidity());
            if (dcatGetPlanResponse.getSms() != null)
                displayValues.put("sms", dcatGetPlanResponse.getSms());
            if (dcatGetPlanResponse.getData() != null)
                displayValues.put("data", dcatGetPlanResponse.getData());
            if (dcatGetPlanResponse.getTalktime() != null)
                displayValues.put("talktime", dcatGetPlanResponse.getTalktime());
            if (dcatGetPlanResponse.getAddon_benefit1() != null)
                displayValues.put("addon_benefit1", dcatGetPlanResponse.getAddon_benefit1());
            if (dcatGetPlanResponse.getAddon_benefit2() != null)
                displayValues.put("addon_benefit2", dcatGetPlanResponse.getAddon_benefit2());
            if (dcatGetPlanResponse.getAddon_benefit3() != null)
                displayValues.put("addon_benefit3", dcatGetPlanResponse.getAddon_benefit3());
            if (dcatGetPlanResponse.getAddon_benefit4() != null)
                displayValues.put("addon_benefit4", dcatGetPlanResponse.getAddon_benefit4());
            if (dcatGetPlanResponse.getDescription() != null)
                displayValues.put("description", dcatGetPlanResponse.getDescription());
        }

        return displayValues;

    }

    public Map<String, String> getDisplayValues(ValidationKafkaResponseModel validationKafkaResponseModel){
        Map<String, String> displayValues = new HashMap<>();
            if(validationKafkaResponseModel!=null && validationKafkaResponseModel.getMetaData()!=null){

            if (validationKafkaResponseModel.getMetaData().getValidity() != null)
                displayValues.put("validity", validationKafkaResponseModel.getMetaData().getValidity());
            if (validationKafkaResponseModel.getMetaData().getSms() != null)
                displayValues.put("sms", validationKafkaResponseModel.getMetaData().getSms());
            if (validationKafkaResponseModel.getMetaData().getData() != null)
                displayValues.put("data", validationKafkaResponseModel.getMetaData().getData());
            if (validationKafkaResponseModel.getMetaData().getTalktime() != null)
                displayValues.put("talktime", validationKafkaResponseModel.getMetaData().getTalktime());
            if (validationKafkaResponseModel.getMetaData().getAddon_benefit1() != null)
                displayValues.put("addon_benefit1", validationKafkaResponseModel.getMetaData().getAddon_benefit1());
            if (validationKafkaResponseModel.getMetaData().getAddon_benefit2() != null)
                displayValues.put("addon_benefit2", validationKafkaResponseModel.getMetaData().getAddon_benefit2());
            if (validationKafkaResponseModel.getMetaData().getAddon_benefit3() != null)
                displayValues.put("addon_benefit3", validationKafkaResponseModel.getMetaData().getAddon_benefit3());
            if (validationKafkaResponseModel.getMetaData().getAddon_benefit4() != null)
                displayValues.put("addon_benefit4", validationKafkaResponseModel.getMetaData().getAddon_benefit4());
            if (validationKafkaResponseModel.getMetaData().getDescription() != null)
                displayValues.put("description", validationKafkaResponseModel.getMetaData().getDescription());

        }

        return displayValues;

    }

    public Map<String , String> getCustomerDataResponse(ValidationKafkaResponseModel validationKafkaResponseModel){

        Map<String, String> customerDataMap = new HashMap<>();

        CustomerDataResponseModel customerDataResponseModel =  validationKafkaResponseModel.getCustomerDataResponse();

        if(customerDataResponseModel!=null) {

            if (customerDataResponseModel.getCurrentBillAmount() != null) {
                customerDataMap.put("currentBillAmount", customerDataResponseModel.getCurrentBillAmount());
            }
            if (customerDataResponseModel.getBillDueDate() != null) {
                customerDataMap.put("billDueDate", customerDataResponseModel.getBillDueDate());
            }
            if (customerDataResponseModel.getTotalAmount() != null) {
                customerDataMap.put("totalAmount", customerDataResponseModel.getTotalAmount());
            }
            if (customerDataResponseModel.getBillDate() != null) {
                customerDataMap.put("billDate", customerDataResponseModel.getBillDate());
            }
        } else {
            logger.error(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.getCustomerDataResponse] Empty CustomerDataResponse" + validationKafkaResponseModel);
        }
        return customerDataMap;

    }

    public DCATGetPlanResponse getPlanDetails(ValidationKafkaResponseModel validationKafkaResponseModel, String DCATcategoryId){
        String service = validationKafkaResponseModel.getProductInfo_service();
        String operator = validationKafkaResponseModel.getProductInfo_operator();
        String amount = validationKafkaResponseModel.getUserData_amount();
        String paytype = validationKafkaResponseModel.getProductInfo_paytype();
        String circle = validationKafkaResponseModel.getProductInfo_circle();
        String productId = null;
        if (Objects.nonNull(validationKafkaResponseModel.getProductId())) {
            productId = validationKafkaResponseModel.getProductId().toString();
        }
        String rechargeNumber = validationKafkaResponseModel.getUserData_recharge_number();
        if(service != null){
            if (DCATcategoryId != null && paytype != null && paytype.equalsIgnoreCase(Constants.CommonConstants.PREPAID_PAYTYPE)) {
                try {
                    if (amount != null) {
                        String amountString = Double.valueOf(amount).intValue() + "";
                        DCATGetPlanResponse dcatGetPlanResponse = rpsService.getPlanDetails(DCATcategoryId, Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, operator, amountString, circle, productId,rechargeNumber,service);
                        return dcatGetPlanResponse;
                    }
                }
                catch (Exception e){
                    logger.error(ServiceWrapperUtil.findServiceWrapper(validationKafkaResponseModel), "[ValidationListeners.getPlanDetails] exception", e);
                }
            }
        }
        return null;
    }

    public ValidationResponseMetaModel prepareMeta(ValidationResponseMetaModel validationMeta){
        if(validationMeta == null) return null;

        List<String> addOnBenefits = null;
        ValidationResponseMetaBenefitsModel validationResponseMetaBenefitsModel  = validationMeta.getRecharge_benefits();

        if(validationResponseMetaBenefitsModel!=null) {
            if (validationResponseMetaBenefitsModel.getData() != null)
                validationMeta.setData(validationResponseMetaBenefitsModel.getData());
            if (validationResponseMetaBenefitsModel.getDescription() != null)
                validationMeta.setDescription(validationResponseMetaBenefitsModel.getDescription());
            if (validationResponseMetaBenefitsModel.getSms() != null)
                validationMeta.setSms(validationResponseMetaBenefitsModel.getSms());
            if (validationResponseMetaBenefitsModel.getTalktime() != null)
                validationMeta.setTalktime(validationResponseMetaBenefitsModel.getTalktime());
            if (validationResponseMetaBenefitsModel.getValidity() != null)
                validationMeta.setValidity(validationResponseMetaBenefitsModel.getValidity());
            addOnBenefits = validationResponseMetaBenefitsModel.getAddon_benefit();
        }
        if(addOnBenefits==null) addOnBenefits = validationMeta.getAddon_benefit();

            if(addOnBenefits!=null){

                for (int i = 0; i < addOnBenefits.size(); i++) {
                    if (addOnBenefits.get(i) != null) {
                        switch (i) {
                            case 0:
                                validationMeta.setAddon_benefit1(addOnBenefits.get(i));
                                break;
                            case 1:
                                validationMeta.setAddon_benefit2(addOnBenefits.get(i));
                                break;
                            case 2:
                                validationMeta.setAddon_benefit3(addOnBenefits.get(i));
                                break;
                            case 3:
                                validationMeta.setAddon_benefit4(addOnBenefits.get(i));
                                break;
                            default:
                        }
                    }
                }
        }


        return validationMeta;
    }

    public String getDropOffAmount(ValidationKafkaResponseModel validationKafkaResponseModel){
        String amount = validationKafkaResponseModel.getUserData_amount();
        String service = validationKafkaResponseModel.getProductInfo_service();
        String paytype = validationKafkaResponseModel.getProductInfo_paytype();
        String dueAmount = null;

        if(validationKafkaResponseModel.getDueAmount()!=null && !validationKafkaResponseModel.getDueAmount().equals("")){
            dueAmount = validationKafkaResponseModel.getDueAmount();
        }

        if(dueAmount!=null){
            amount = dueAmount;
        }

        if(validationKafkaResponseModel.getMetaData()!=null){
            if(validationKafkaResponseModel.getMetaData().getUserIntent()!=null){
                if(validationKafkaResponseModel.getMetaData().getUserIntent().equals(Constants.CommonConstants.DEFAULT_AMOUNT_REQUEST_VALUE)){
                    if(dueAmount!=null){
                        amount = dueAmount;
                    } else{
                        amount = null;
                    }
                }
            }
        }

        return amount;
    }


    /*
    public Boolean checkPendingTransaction(ValidationKafkaResponseModel validationKafkaResponseModel, DropOff dropOffData){
        Long customerId = validationKafkaResponseModel.getCustomerInfo_customer_id();
        String rechargeNumber = validationKafkaResponseModel.getUserData_recharge_number();
        String service = validationKafkaResponseModel.getProductInfo_service();
        List<ChannelHistory> historySlice = channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service);
        if(historySlice!=null && historySlice.size()>0){
            if(historySlice.get(0)!=null){
                String in_code = historySlice.get(0).getInResponseCode();
                String oms_status = historySlice.get(0).getStatus();
                String payment_status = historySlice.get(0).getPaymentStatus();
                Map<String , String> billsObj = historySlice.get(0).getBillsObj();
                String transactionStatus = OMSToRechargeStatus.getRechargeStatusByOMSStatus(oms_status, in_code, payment_status);
                if(dropOffData!=null && dropOffData.getBillsObj()!=null && dropOffData.getBillsObj().get("plan_bucket")!=null){
                    if(billsObj!=null && billsObj.get("plan_bucket")!=null){
                        if(billsObj.get("plan_bucket").equals(dropOffData.getBillsObj().get("plan_bucket"))){
                            // Consider pending transaction of same bucket only.
                            if(transactionStatus.equals("PENDING")){
                                return true;
                            }
                        }
                    }
                } else if(transactionStatus.equals("PENDING")){
                    return true;
                }
            }
        }

        return false;
    }
    */
}


