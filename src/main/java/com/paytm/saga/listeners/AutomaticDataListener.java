package com.paytm.saga.listeners;

import static com.paytm.saga.common.constant.Constants.MetricConstants.CONSUMER_EVENTS_ERROR_METRIC;
import static com.paytm.saga.common.constant.Constants.MetricConstants.CONSUMER_EVENTS_INVALID_METRIC;
import static com.paytm.saga.common.constant.Constants.MetricConstants.CONSUMER_EVENTS_SUCCESS_METRIC;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import jakarta.validation.ConstraintViolationException;

import com.paytm.saga.dao.impl.CustomerBillDao;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.model.*;
import com.paytm.saga.model.primaryKeys.CustomerBillPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.repository.*;
import com.paytm.saga.service.CommonService;
import com.paytm.saga.service.KafkaProducerService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.DateUtil;
import com.timgroup.statsd.StatsDClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.configuration.AutomaticKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.ValidationUtils;

@Component
@DependsOn("AutomaticKafkaConfig")
@Conditional(AutomaticKafkaConsumerEnableCondition.class)
public class AutomaticDataListener {

	private static final Integer TTL_FOR_RECENTS=365*24*60*60;

	private final Logger logger = LogManager.getLogger(AutomaticDataListener.class);

	@Autowired
	private AutomaticDataRepository automaticDataRepository;
	@Autowired
	private RecentsRepository recentsRepository;
    @Autowired
	private KafkaProducerService kafkaProducerService;
	@Autowired
	private RecentService recentService;
	@Autowired
	private CustomerBillRepository customerBillRepository;
	@Autowired
	private CustomerBillDao customerBillDao;
	@Autowired
	private CommonService commonService;
	@Value("${automatic.data.ttl.days : 30}")
	private Integer ttlDays;
	private static final String NEW_EVENT="NEW_KAFKA_EVENT";

	@Autowired
	@Qualifier("monitoringClient")
	private StatsDClient monitoringClient;

	public AutomaticDataListener() {
	}


	// read this from bean of kafka config properties
	@KafkaListener(topics = "#{AutomaticKafkaConfig.getTopicName()}", groupId = "#{AutomaticKafkaConfig.getConsumerGroup()}", containerFactory = "AutomaticListenerContainerFactory")
	public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {



		for (String message : messages) {
			pushToDD(NEW_EVENT);
			AutomaticResponseModel automaticResponseModel = null;

			if (Objects.nonNull(message)) {
				logger.info("[AutomaticDataListener.listen] message = {}",message);
				try {
					automaticResponseModel = JsonUtils.parseJson(message, AutomaticResponseModel.class);
					logger.info("[AutomaticDataListener.listen] parse done");

					ValidationUtils.validate(automaticResponseModel);
					logger.info("[AutomaticDataListener.listen] validation done");

					if(!((automaticResponseModel != null && automaticResponseModel.getAutomaticDate() != null && automaticResponseModel.getAutomaticDate().after(new Date())) 
							|| (automaticResponseModel != null && automaticResponseModel.getIsAutomatic() == 5)
							|| (automaticResponseModel != null && automaticResponseModel.getAutomaticDate() == null && automaticResponseModel.isFulfillmentNullDate()))){
						logger.error("Invalid message = {}, automaticDataResponseModel = {}",message,automaticResponseModel);
						throw new RuntimeException("Violation: (automatic Date should be greater than current date OR is_automatic should be 5 OR automaticDate is null due to special 0000-00-00 case)");
					}
					logger.info("Automatic Data Listener payload: {}", automaticResponseModel);
					insertDataIntoCassandra(automaticResponseModel);
					pushToDD(CONSUMER_EVENTS_SUCCESS_METRIC);
				} catch (ConstraintViolationException exception) {
					
					logger.error("[AutomaticDataListener.listen] Invalid message body {} , message {} ",
							exception.getMessage(), message);
					pushToDD(CONSUMER_EVENTS_INVALID_METRIC);
				
				} catch (Exception exception) {
				
					logger.error("[AutomaticDataListener.listen] Error {}", exception.getMessage(), exception);
					pushToDD(CONSUMER_EVENTS_ERROR_METRIC);

				}

			}
		}
		logger.info("[AutomaticDataListener.listen] Acknowledging message");
		acknowledgment.acknowledge();
	}

	private void pushToDD(String metricName) {
		String[] tags = new String[1];
		tags[0] = Constants.MetricConstants.SERVICE_NAME_KEY + ":" + "automatic";
		monitoringClient.incrementCounter(metricName, tags);
	}

	private void insertDataIntoCassandra(AutomaticResponseModel automaticResponseModel) {
		logger.info("[AutomaticDataListener.listen] insertDataIntoCassandra");

		AutomaticData automaticData = new AutomaticData();

		ReminderHistoryPrimaryKey reminderHistoryPrimaryKey = new ReminderHistoryPrimaryKey();
		reminderHistoryPrimaryKey.setCustomerId(automaticResponseModel.getCustomerId());
		reminderHistoryPrimaryKey.setRechargeNumber(CommonUtils.getRechargeNumberBasedOnRules(automaticResponseModel.getRechargeNumber(), automaticResponseModel.getOperatorName()));
		reminderHistoryPrimaryKey.setOperator(automaticResponseModel.getOperatorName());
		reminderHistoryPrimaryKey.setService(automaticResponseModel.getService());
		reminderHistoryPrimaryKey.setUpdatedAt(new Date());
		automaticData.setKey(reminderHistoryPrimaryKey);
		automaticData.setAutomaticDate(automaticResponseModel.getAutomaticDate());
		Integer TTL= getTTL(automaticData);
		boolean shouldUpdateAutomaticDate = !(Objects.nonNull(automaticResponseModel.getIsAutomatic()) && automaticResponseModel.getIsAutomatic() == 5 && Objects.nonNull(automaticResponseModel.getAutomaticDate()) && automaticResponseModel.getAutomaticDate().before(new Date()));
		if(shouldUpdateAutomaticDate) {
			automaticDataRepository.save(automaticData, TTL);
		}
		updateDataIntoRecents(automaticResponseModel);
		updateDataIntoCustomerBill(automaticResponseModel);
        try {
			kafkaProducerService.sendMessage(Long.toString(automaticResponseModel.getCustomerId()));
		}
		catch (Exception e){
			logger.error("Failed to publish for customer id {} error {}",automaticResponseModel.getCustomerId(),e.getMessage());
			pushToDD("RECO_REFRESH_ERROR_EVENT");
		}

		logger.info("[AutomaticDataListener.insertDataIntoCassandra] Insert Successfully {}", automaticData);

	}
	public void updateDataIntoRecents(AutomaticResponseModel automaticResponseModel){
		Date automaticDate = (automaticResponseModel.getAutomaticDate());
		long customerId = automaticResponseModel.getCustomerId();
		String recharge_number = automaticResponseModel.getRechargeNumber();
		String service = automaticResponseModel.getService();
		String operator = automaticResponseModel.getOperatorName();
		String plan_bucket = "";
		long productId = automaticResponseModel.getProductId();
		Date currentDate=new Date();
		Integer subscriberId = automaticResponseModel.getSubscriberId();
		Double automaticAmount = automaticResponseModel.getAutomaticAmount();
		boolean shouldUpdateAutomaticDate =  !(Objects.nonNull(automaticResponseModel.getIsAutomatic()) && automaticResponseModel.getIsAutomatic() == 5 && Objects.nonNull(automaticResponseModel.getAutomaticDate()) && automaticResponseModel.getAutomaticDate().before(new Date()));
		if(automaticResponseModel.getPayType().equalsIgnoreCase(Constants.PREPAID_PAYTYPE)){
			plan_bucket = Objects.nonNull(automaticResponseModel.getPlanBucket()) ? automaticResponseModel.getPlanBucket() : "";
		}

		try {
			logger.info("Updating recents in for automatic ={} and recharge_number={} and automaticDate={}",customerId,recharge_number,automaticDate);
			//recentsRepository.save(recents);
			recentService.updateAutomaticInfo(customerId, automaticDate, service, operator, plan_bucket, currentDate, currentDate, recharge_number,productId,subscriberId, shouldUpdateAutomaticDate, automaticAmount);
		} catch (Exception e) {
			logger.error("[AutomaticDataListener.updateDataIntoRecents] ReminderClusterKey upsert for cusomter id {}, recharge number {}, service {}, operator {}, Exception {}",
					automaticResponseModel.getCustomerId(),automaticResponseModel.getRechargeNumber(),
					automaticResponseModel.getService(),automaticResponseModel.getOperatorName(),e);
			pushToDD(CONSUMER_EVENTS_ERROR_METRIC);
		}
	}
	private Integer getTTL(AutomaticData automaticData) {
		Date automaticDate = automaticData.getAutomaticDate();
		if(Objects.isNull(automaticDate)){
			return 90 * 24 * 60 * 60;
		}
		Integer secondsDiff = Math.abs(Math.toIntExact(automaticDate.getTime() / 1000 - (new Date()).getTime() / 1000));
		return secondsDiff + ttlDays * 24 * 60 * 60;
	}

	public void updateDataIntoCustomerBill(AutomaticResponseModel automaticResponseModel){
		try {
			if (Objects.nonNull(automaticResponseModel) && Objects.nonNull(automaticResponseModel.getIsAutomatic()) && automaticResponseModel.getIsAutomatic() == 5) {
				CustomerBill reminderClusterKey = prepareAutomaticCustomerBillData(automaticResponseModel);
				Integer ttl = commonService.getSmartReminderTTL(reminderClusterKey);
				if (ttl > 0) {
					customerBillDao.save(reminderClusterKey, ttl);
					logger.info("After saving into customerBill {} {} ", reminderClusterKey.getKey().getCustomerId(), ttl);
				} else {
					logger.info("Ignoring ttl smaller than 0  {}", automaticResponseModel);
				}
			}
		} catch (Exception e){
			logger.error("[AutomaticDataListener.updateDataIntoCustomerBill] Exception", e);
		}
	}

	private CustomerBill prepareAutomaticCustomerBillData(AutomaticResponseModel automaticResponseModel){
		ProductMin automaticPid = CVRProductCache.getInstance().getProductDetails(automaticResponseModel.getProductId().longValue());
		CustomerBill reminderClusterKey =null;

		try {
			reminderClusterKey = new CustomerBill();
			reminderClusterKey.setKey(new CustomerBillPrimaryKey());
			reminderClusterKey.getKey().setCustomerId(automaticResponseModel.getCustomerId());
			reminderClusterKey.getKey().setDue_date(DateUtil.stringToDate(Constants.AUTOMATIC_DUMMY_DUE_DATE, Constants.CommonConstants.DATE_FORMAT));
			reminderClusterKey.getKey().setService(automaticResponseModel.getService());
			reminderClusterKey.getKey().setOperator(automaticPid.getOperator());
			reminderClusterKey.getKey().setRechargeNumber(automaticResponseModel.getRechargeNumber());
			reminderClusterKey.getKey().setPlanBucket("");
			return reminderClusterKey;
		} catch (Exception e) {
			logger.error("[AutomaticDataListener.prepareAutomaticCustomerBillData] Exception", e);
			throw new RuntimeException(e);
		}
	}

}
