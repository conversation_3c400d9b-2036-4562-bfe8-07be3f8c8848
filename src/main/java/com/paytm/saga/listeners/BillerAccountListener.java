package com.paytm.saga.listeners;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.paytm.saga.common.configuration.BillerAccountConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.model.BillerAccountConsumerModel;
import com.paytm.saga.model.BillerAccountKafkaModel;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.ValidationUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import jakarta.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.billerAccountListenerConstants.*;
import static org.bouncycastle.asn1.cms.CMSObjectIdentifiers.data;

@Component
@DependsOn("BillerAccountKafkaConfig")
@Conditional(BillerAccountConsumerEnableCondition.class)
public class BillerAccountListener {

    private final Logger logger = LogManager.getLogger(BillerAccountListener.class);


    @Autowired
    @Qualifier("monitoringClient")
    private StatsDClient monitoringClient;

    @Autowired
    private RecentService recentService;


    @KafkaListener(topics = "#{BillerAccountKafkaConfig.getTopicNames()}", groupId = "#{BillerAccountKafkaConfig.getConsumerGroup()}", containerFactory = "BillerAccountListenerContainerFactory")
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {

        for (String message : messages) {
            logger.info(message);//later we will remove this
            pushToDD(NEW_EVENT);
            BillerAccountConsumerModel billerAccountConsumerModel = null;
            if (Objects.nonNull(message)) {
                try {
                    billerAccountConsumerModel = JsonUtils.parseJson(message, BillerAccountConsumerModel.class);
                    if(billerAccountConsumerModel == null || billerAccountConsumerModel.getData() == null || billerAccountConsumerModel.getOperationType()==null){
                        logger.error("[BillerAccountListener.insertDataIntoCassandra]  invalid data in maxwell payload  , : {}" , billerAccountConsumerModel);
                        pushToDD(INVALID_INPUT_ERROR_METRIC);
                        acknowledgment.acknowledge();
                        return;
                    }
                    BillerAccountKafkaModel billerAccountResponseModel=billerAccountConsumerModel.getData();
                    ValidationUtils.validate(billerAccountResponseModel);
                    if (Objects.nonNull(billerAccountResponseModel) && !isSkippable(billerAccountResponseModel,billerAccountConsumerModel.getOperationType())) {
                        billerAccountResponseModel.setTenantDetailsObj(getTenantDetail(billerAccountResponseModel));
                        recentService.insertBillerAccountRecents(billerAccountResponseModel);
                        pushToDD(CONSUMER_EVENTS_SUCCESS_METRIC);
                    }


                } catch (ConstraintViolationException exception) {
                    logger.error("[BillerAccountListener.listen] Invalid message body {} , message {} ", exception.getMessage(), message);
                    pushToDD(CONSUMER_EVENTS_INVALID_METRIC);

                } catch (Exception exception) {
                    logger.error("[BillerAccountListener.listen] Error {}", exception.getMessage());
                    pushToDD(CONSUMER_EVENTS_ERROR_METRIC);

                }

            }
        }
        logger.info("[BillerAccountListener.listen] Acknowledging message");
        acknowledgment.acknowledge();
    }

    private Map<String, Object> getTenantDetail(BillerAccountKafkaModel billerAccountResponseModel){
        return billerAccountResponseModel.getTenantDetailsObj();
    }

    private void pushToDD(String metricName) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.SERVICE_NAME_KEY + ":" + BILLER_ACCOUNT;
        monitoringClient.incrementCounter(metricName, tags);
    }

    private boolean isSkippable(BillerAccountKafkaModel billerAccountResponseModel, String opType) {
        if(billerAccountResponseModel.getAccountStatus() == BILLER_DELETED_ACCOUNT || StringUtils.equalsIgnoreCase(opType,Constants.OP_DELETE)){
            recentService.deleteValidRecord(billerAccountResponseModel);
            return true;

        }


        return false;
    }


}
