package com.paytm.saga.listeners;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.CDCRecoveryKafkaConsumerEnableCondition;
import com.paytm.saga.common.configuration.CDCReminderKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfig;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.SMSRecoveryPacket;
import com.paytm.saga.dto.cdc.*;
import com.paytm.saga.service.FeatureConfigService;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.*;

@Component
@DependsOn("CDCReminderKafkaConfig")
@Conditional(CDCRecoveryKafkaConsumerEnableCondition.class)
public class CDCSMSRecoveryKafkaConsumer {
    private CustomLogger logger = CustomLogManager.getLogger(CDCSMSRecoveryKafkaConsumer.class);

    @Autowired
    private CDCReminderListener cdcReminderListener;

    @Autowired
    private MetricsHelper metricsHelper;

    @Autowired
    protected ServiceConfig serviceConfig;


    @KafkaListener(topics = "#{CDCReminderKafkaConfig.getTopicNameSmsRecovery()}",
            groupId = "#{CDCReminderKafkaConfig.getConsumerGroup()}",
            containerFactory = "CDCReminderListenerContainerFactory"
    )
    @Conditional(CDCRecoveryKafkaConsumerEnableCondition.class)
    public void listenRecoveryTopic(@Payload List<String> messages, Acknowledgment acknowledgment) {

        for (String message : messages) {
            SMSRecoveryPacket smsRecoveryPacket;
            long startTime = System.currentTimeMillis();
            String logStatus = "success";
            if (Objects.nonNull(message)) {
                metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, NEW_EVENT);
                smsRecoveryPacket = JsonUtils.parseJsonWithCustomMapper(message, SMSRecoveryPacket.class);
                if(Objects.nonNull(smsRecoveryPacket)) {
                    logger.info(ServiceWrapperUtil.findServiceWrapper(smsRecoveryPacket.getService()), "[CDCReminderListener.listenRecoveryTopic] :: listenRecoveryTopic :: customer id : {} :: raw kafka packet {}", smsRecoveryPacket.getCustomerId(), message);
                } else {
                    logger.info("[CDCReminderListener.listenRecoveryTopic] :: listenRecoveryTopic :: raw kafka packet {}", message);
                }
                if(Objects.nonNull(smsRecoveryPacket) && !RecentUtils.isWhitelistedCustId(smsRecoveryPacket.getCustomerId(),serviceConfig)){
                    metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, NON_WHITELISTED_CUSTID);
                    logStatus = "ignore";
                    long endTime = System.currentTimeMillis();
                    logger.info("CDC recovery listener message processed with status {} in ms {}", logStatus, (endTime-startTime));
                    continue;
                }

                metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, WHITELISTED_CUSTID);
                /*if(!this.checkUpdatedRange(smsRecoveryPacket)){
                    continue;
                }*/
                NonPaytmCDC nonPaytmCDC=CDCConsumerUtil.convertRecoveryCodeToCDCFormat(smsRecoveryPacket);
                if(nonPaytmCDC.getAfter().getService().getValue().equalsIgnoreCase(Constants.SERVICE_MOBILE)){
                    try {
                        String rechargeNumber = RecentUtils.getDecryptedRechargeNumber(nonPaytmCDC.getAfter().getRechargeNumber().getValue());
                        nonPaytmCDC.getAfter().getRechargeNumber().setValue(rechargeNumber);
                    } catch (Exception e) {
                        logger.info("[CDCReminderLisener.listenSms] : Failed while decryption of recharge number with error {}",e);
                        logStatus = "exception";
                        long endTime = System.currentTimeMillis();
                        logger.info("CDC recovery listener message processed with status {} in ms {}", logStatus, (endTime-startTime));
                        continue;
                    }
                }
                List<String> tags=new ArrayList<>();
                tags.add("event_type:new_event");
                tags.add("service:"+nonPaytmCDC.getAfter().getService().getValue());
                tags.add("operator:"+nonPaytmCDC.getAfter().getOperator().getValue());
                metricsHelper.recordSuccessRateForMultipleTags(Constants.CDC_CONSUMER_STATS, tags);

                logCDCLatencyMetric(nonPaytmCDC);

                ReminderCDC reminderCDC = CDCConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDC);
                reminderCDC.setRecoveryPacket(true);
                ValidationUtils.validate(reminderCDC);
                cdcReminderListener.insertDataIntoCassandra(reminderCDC);
            } else {
                logger.info("[CDCReminderListener.listenRecoveryTopic] :: listenRecoveryTopic raw kafka packet {}", message);
                metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, IGNORE_EVENT);
                logStatus = "ignore";
            }
            long endTime = System.currentTimeMillis();
            logger.info("CDC recovery listener message processed with status {} in ms {}", logStatus, (endTime-startTime));
        }
        acknowledgment.acknowledge();
    }

    private void logCDCLatencyMetric(NonPaytmCDC nonPaytmCDC) {
        try {

            if (nonPaytmCDC.getAfter() !=null  && nonPaytmCDC.getAfter().getUpdatedAt() != null
                    && ("i".equalsIgnoreCase(nonPaytmCDC.getOp()) || "u".equalsIgnoreCase(nonPaytmCDC.getOp()))) {
                SimpleDateFormat formatter = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);
                long milliSeconds = nonPaytmCDC.getAfter().getUpdatedAt().getValue();
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeInMillis(milliSeconds);
                metricsHelper.recordExecutionTimeOfEvent("CDC_RECOVERY_CONSUMER_EVENT_LATENCY", (System.currentTimeMillis() - milliSeconds));
                logger.info(ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC), "CDCSMSRecoveryKafkaConsumer.listenRecoveryTopic :: customer {} recharge {} service {} eventDate {} currentDate {} diffMillis {}"
                        , nonPaytmCDC.getAfter().getCustomerId().getValue()
                        , nonPaytmCDC.getAfter().getRechargeNumber().getValue()
                        , nonPaytmCDC.getAfter().getService().getValue()
                        , formatter.format(calendar.getTime())
                        , formatter.format(new Date())
                        , (System.currentTimeMillis() - milliSeconds));
            }
        }
        catch(Exception ex){
            logger.info("logCDCLatencyMetric:: error in logging metrics {}",ex);
        }

    }

}
