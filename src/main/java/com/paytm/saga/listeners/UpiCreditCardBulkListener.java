package com.paytm.saga.listeners;

import com.paytm.saga.common.configuration.UpiCreditCardBulkKafkaEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.model.UpiCreditCardMessage;
import com.paytm.saga.service.UpiCreditCardService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@DependsOn("upiCreditCardBulkKafkaConfig")
@Conditional(UpiCreditCardBulkKafkaEnableCondition.class)
public class UpiCreditCardBulkListener {

    private final Logger logger = LogManager.getLogger(UpiCreditCardBulkListener.class);

    private final UpiCreditCardService upiCreditCardService;
    private final MetricsHelper metricsHelper;

    @Autowired
    public UpiCreditCardBulkListener(UpiCreditCardService upiCreditCardService, MetricsHelper metricsHelper) {
        this.upiCreditCardService = upiCreditCardService;
        this.metricsHelper = metricsHelper;
    }

    @KafkaListener(topics = "#{upiCreditCardBulkKafkaConfig.getTopicName()}",
            groupId = "#{upiCreditCardBulkKafkaConfig.getConsumerGroup()}",
            containerFactory ="upiCreditCardBulkKafkaListenerContainerFactory")
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {
        if (!CollectionUtils.isEmpty(messages)) {
            for (String message : messages) {
                metricsHelper.recordSuccessRate(Constants.UPI_CC_BULK_CONSUMER, Constants.NEW_KAFKA_EVENT);
                if (StringUtils.isNotBlank(message)) {
                    processMessage(message);
                }
            }
        }
        acknowledgment.acknowledge();
    }

    private void processMessage(String message) {
        final String LOG_PREFIX = "UPI-CC-Bulk-Listener::ProcessMessage - ";
        try {
            logger.info("{}Processing message {}", LOG_PREFIX, message);
            String[] messageParts = message.split(",");
            if (messageParts.length < 3) {
                logger.error("{}Invalid message format, expected 3 parts but got {}", LOG_PREFIX, messageParts.length);
                metricsHelper.recordSuccessRate(Constants.UPI_CC_BULK_CONSUMER, Constants.ERROR_EVENT);
                return;
            }

            UpiCreditCardMessage upiCreditCardMessage = new UpiCreditCardMessage();
            upiCreditCardMessage.setBankName(messageParts[0].trim());
            upiCreditCardMessage.setCustomerId(messageParts[1].trim());
            upiCreditCardMessage.setLast2CardDigits(messageParts[2].trim());

            upiCreditCardService.processUpiCreditCardMessage(
                    upiCreditCardMessage, message, Constants.UPI_CC_BULK_CONSUMER
            );
        } catch (Exception e) {
            logger.error("{}Error while processing message", LOG_PREFIX, e);
            metricsHelper.recordSuccessRate(Constants.UPI_CC_BULK_CONSUMER, Constants.ERROR_EVENT);
        }
    }

}
