package com.paytm.saga.listeners;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.newrelic.api.agent.Trace;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.ServiceWrapper;
import com.paytm.saga.common.configuration.KafkaConsumerEnableCondition;
import com.paytm.saga.common.configuration.RecentTxnMigrationKafkaConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.PrometheusConstants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.es.ESResponse;
import com.paytm.saga.dto.es.HitsEs;
import com.paytm.saga.enums.Service;
import com.paytm.saga.model.*;
import com.paytm.saga.service.*;
import com.paytm.saga.service.external.EsService;
import com.paytm.saga.util.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.lang.NonNull;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.CommonConstants.*;
import static com.paytm.saga.common.constant.ServiceNameConstants.FAILED_IN_RESPONSE_STATES_SERVICE;
import static com.paytm.saga.util.DateUtil.getTimeUntilEndOfMonth;
import static com.paytm.saga.util.OMSUtils.getFailedInResponseStates;
import static com.paytm.saga.util.OMSUtils.isPaymodeCountSupportEnabled;

@Component
@DependsOn("OMSKafkaPropertiesConfig")
@Conditional(KafkaConsumerEnableCondition.class)
public class OMSListeners {
    public static final String SERVICE_NAME = "OMS_CONSUMER";
    private static final String NEW_EVENT = "NEW_KAFKA_EVENT";
    private final CustomLogger logger = CustomLogManager.getLogger(OMSListeners.class);
    private final DCATService dcatService;
    private final ChannelHistoryRepositoryWrapperService channelHistoryRepository;
    private final MappingService mappingService;
    private final ReminderHistoryService reminderHistoryService;
    private final DropOffDBHelper dropOffDBHelper;
    private final MetricsHelper metricsHelper;
    private final ChannelHistoryService channelHistoryService;
    private final EvictCacheServiceImpl evictCacheService;
    private final EsService esService;
    private RecentService recentService;
    private RPSService rpsService;
    private Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private KafkaTemplate<String, String> txnTimeMigration;

    @Autowired
    private RecentTxnMigrationKafkaConfig recentTxnMigrationKafkaConfig;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    public OMSListeners(@NonNull DCATService dcatService,
                        @NonNull ChannelHistoryRepositoryWrapperService channelHistoryRepository,
                        @NonNull MappingService mappingService,
                        @NonNull ReminderHistoryService reminderHistoryService,
                        @NonNull DropOffDBHelper dropOffDBHelper,
                        @NonNull MetricsHelper metricsHelper,
                        ChannelHistoryService channelHistoryService, EvictCacheServiceImpl evictCacheService, @NonNull RecentService recentService, @NonNull EsService esService, RPSService rpsService) {
        this.dcatService = dcatService;
        this.channelHistoryRepository = channelHistoryRepository;
        this.mappingService = mappingService;
        this.reminderHistoryService = reminderHistoryService;
        this.dropOffDBHelper = dropOffDBHelper;
        this.metricsHelper = metricsHelper;
        this.channelHistoryService = channelHistoryService;
        this.evictCacheService = evictCacheService;
        this.recentService = recentService;
        this.rpsService = rpsService;
        this.esService = esService;
    }

    //read this from bean of kafka config properties
    @KafkaListener(topics = "#{OMSKafkaPropertiesConfig.getTopicName()}",
            groupId = "#{OMSKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory = "OMSListenerContainerFactory")
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {
        for (String message : messages) {
            metricsHelper.recordSuccessRate(SERVICE_NAME, NEW_EVENT);
            OMSResponseModel omsResponseModel = null;
            logger.info("[OMSListeners.listen] Temp message log" + message);
            ServiceWrapper serviceWrapper = null;
            long startTime = System.currentTimeMillis();
            String logStatus = "success";

            if (Objects.nonNull(message)) {
                try {
                    logger.info("OMS kafka raw packet {}",message);
                    omsResponseModel = JsonUtils.parseJson(message, OMSResponseModel.class);
                    serviceWrapper = ServiceWrapperUtil.findServiceWrapper(omsResponseModel);
                    upsertTxnCount(omsResponseModel);
                    insertDataIntoCassandra(omsResponseModel,message);
                    logger.info(serviceWrapper, "[OMSListeners.listen] Process success" + omsResponseModel.getOrderId());
                } catch (Exception e) {
                    metricsHelper.recordSuccessRate(SERVICE_NAME, Constants.ERROR_TAG);
                    logger.error(serviceWrapper, "[OMSListeners.listen]:Exception for message {}", message, e);
                    logStatus = "exception";
                }
            } else {
                logStatus = "ignore";
            }
            long endTime = System.currentTimeMillis();
            logger.info("OMS listener message processed with status {} in ms {}", logStatus, (endTime-startTime));
        }
        try {
            acknowledgment.acknowledge();
        } catch (Exception e) {
            logger.error("[OMSListeners.listen]:ACK Expection", e);
        }
    }

    @Trace(dispatcher = true)
    public void upsertTxnCount(OMSResponseModel omsData) {
        try {
            if (omsData == null) {
                logger.error("[OMSListeners.upsertTxnCount], empty OMSData");
                return;
            }

            if (!omsData.getItems().isEmpty()) {
                OMSItemModel item = omsData.getItems().get(0);

                    if (Objects.isNull(omsData.getCustomerId()) || Objects.isNull(item)
                        || Objects.isNull(item.getProduct()) || Objects.isNull(item.getProduct().getCategoryId())
                        || !isPaymodeCountSupportEnabled(item.getProduct().getCategoryId())|| isCreatedAtNotCurrentMonth(item)) {
                        return;
                    }

                ESResponse esResponse = null;
                String in_response_code = null;

                try {
                    OMSFulfillmentResponseModel fulfillmentResponse = getFulfillmentResponse(omsData, item);
                    if (fulfillmentResponse != null) {
                        in_response_code = fulfillmentResponse.getIn_code();
                    }
                } catch (Exception e) {
                    logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.upsertTxnCount] getIn_code : Exception for itemid {}", item.getItemId());
                }

                if(StringUtils.isBlank(in_response_code)) {
                    return;
                }

                PaymentTxnModel paymentTxnModel = new PaymentTxnModel();
                paymentTxnModel.setCustomerId(omsData.getCustomerId());
                paymentTxnModel.setOrderId(omsData.getOrderId());
                paymentTxnModel.setCategoryId(item.getProduct().getCategoryId());
                paymentTxnModel.setStatus(String.valueOf(item.getStatus()));

                try {
                    esResponse = esService.getEsDetailsForOrderCount(paymentTxnModel);
                } catch (Exception ex) {
                    logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.upsertTxnCount]getEsDetailsForOrderCount : Exception for itemid {}", item.getItemId(), ex);
                }

                upsertCacheValue(esResponse, paymentTxnModel, in_response_code);
            }
        } catch (Exception ex) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(omsData), "[OMSListeners.upsertTxnCount]:Exception for orderId {}", omsData.getOrderId(), ex);
        }
    }

    private boolean isCreatedAtNotCurrentMonth(OMSItemModel item) {
        boolean isNotInCurrentMonth = false;

        if(Objects.isNull(item.getCreatedAt())) return false;
        String createdAt = item.getCreatedAt();


        try {
            // Convert the createdAt string to LocalDateTime
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            LocalDateTime createdAtDateTime = LocalDateTime.parse(createdAt, formatter);

            // Get the current date and month
            LocalDate currentDate = LocalDate.now();
            Month currentMonth = currentDate.getMonth();

            // Check if the createdAt value is in the current month
            isNotInCurrentMonth = !createdAtDateTime.getMonth().equals(currentMonth);
        } catch (DateTimeParseException e) {
            // Handle the case where the createdAt value is not in a valid format
            logger.error("Invalid format for createdAt: " + createdAt);
        }

        return isNotInCurrentMonth;
    }

    private void upsertCacheValue(ESResponse esResponse, PaymentTxnModel paymentTxnModel, String in_response_code) {
        Long cachevalue;
        ArrayList<Long> orderList = new ArrayList<>();
        UpsertCacheRequest paymodeCountCacheRequest = new UpsertCacheRequest();
        paymodeCountCacheRequest.setKeyName(StringUtils.join(paymentTxnModel.getCustomerId(), Constants.Delimiters.UNDERSCORE, paymentTxnModel.getCategoryId()));

        if (Objects.nonNull(esResponse)) {
            for (HitsEs hitsEs : esResponse.getHits().getHits()) {
                Long orderIdFromEs = Long.valueOf(hitsEs.getSource().get(Constants.EsConstatnts.ORDER_ID) + "");
                orderList.add(orderIdFromEs);
            }
            orderList = orderList.stream().distinct().collect(Collectors.toCollection(ArrayList::new));
            logger.info("[OMSListeners.upsertCacheValue],Order List {}", orderList);
        }

        String status = getStatusByOMSStatus(in_response_code);
        logger.info("[OMSListeners.upsertCacheValue],getStatusByOMSStatus {}", status);

        if (StringUtils.containsIgnoreCase(status, "pending") || StringUtils.containsIgnoreCase(status, "success")) {
            if (Objects.isNull(esResponse))
                paymodeCountCacheRequest.setInstruction(AerospikeInstructionsEnum.INCREMENT.label);
            else if (orderList.contains(paymentTxnModel.getOrderId())) {
                cachevalue = (long) orderList.size();
                paymodeCountCacheRequest.setValue(cachevalue);
            } else {
                cachevalue = (long) orderList.size();
                paymodeCountCacheRequest.setInstruction(AerospikeInstructionsEnum.INCREMENT.label);
                paymodeCountCacheRequest.setValue(cachevalue);
            }

        } else if (StringUtils.containsIgnoreCase(status, "failure")) {
            if (Objects.isNull(esResponse)) {
                logger.info("[OMSListeners.upsertCacheValue],received failure event with empty ES response");
                return;
            } else if (orderList.contains(paymentTxnModel.getOrderId())) {
                cachevalue = (long) orderList.size() - 1;
                paymodeCountCacheRequest.setValue(cachevalue);
                paymodeCountCacheRequest.setMinValue(0L);
            } else {
                cachevalue = (long) orderList.size();
                paymodeCountCacheRequest.setValue(cachevalue);
            }
        }
        paymodeCountCacheRequest.setTtl(getTimeUntilEndOfMonth());
        paymodeCountCacheRequest.setBinName("orderCount");
        paymodeCountCacheRequest.setSetName("orderCountSet");

        logger.info("[OMSListeners.upsertCacheValue],paymodeCountCacheRequest {}", paymodeCountCacheRequest);
        evictCacheService.upsertCache(paymodeCountCacheRequest);
    }

    private String getStatusByOMSStatus(String in_response_code) {
        final List<String> FAILURE_STATES = getFailedInResponseStates(FAILED_IN_RESPONSE_STATES_SERVICE);
        if (in_response_code.equals("00")) {
            return "SUCCESS";
        } else if (FAILURE_STATES.contains(in_response_code)) {
            return "FAILURE";
        } else
            return "PENDING";
    }

    @Trace(dispatcher=true)
    public void insertDataIntoCassandra(OMSResponseModel omsData,String rawMessage){
        logger.debug("insertDataIntoCassandra start");
        try{
            if(omsData == null){
            	metricsHelper.recordSuccessRate(SERVICE_NAME,Constants.ERROR_TAG);
                logger.error("[OMSListeners.insertDataIntoCassandra], empty OMSData");
                return;
            }

            for (OMSItemModel item : omsData.getItems()) {
                logger.debug(ServiceWrapperUtil.findServiceWrapper(item), "insertDataIntoCassandra item start {}" , item);
                try {
                    if (!Arrays.asList(Constants.CommonConstants.FS_VERTICAL_IDS).contains(item.getVerticalId())) {
                        continue;
                    }
                    if(new Integer("94").equals(item.getVerticalId())){
                        logger.info(ServiceWrapperUtil.findServiceWrapper(item),"OMS paytm postpaid kafka packet {}",omsData);
                    }

                    if (isSkippable(item)) {
                        metricsHelper.recordSuccessRate(SERVICE_NAME, Constants.IGNORE_TAG);
                        logger.info(ServiceWrapperUtil.findServiceWrapper(item),"[OMSListeners.insertDataIntoCassandra] Ignoring : " + omsData.getCustomerId() + omsData.getOrderId() + omsData.toString());
                        continue;
                    }

                    Long customerId = omsData.getCustomerId();
                    Long productId = item.getProductId();
                    Long orderId = omsData.getOrderId();
                    Date createdTime = new Date();
                    Long categoryId = Long.valueOf(1);
                    Long itemId = item.getItemId();
                    String recharge_number_1 = null;
                    String recharge_number_2 = "";
                    String recharge_number_3 = "";
                    String recharge_number_4 = "";
                    String service = null;
                    String operator = null;
                    String paytype = "";
                    String amount = "";
                    String status = "";
                    String circle = "";
                    String tin = null;
                    String par = null;
                    String cin = null;
                    String mcn = null;
                    String nickName = null;
                    String consumerName = null;
                    String cylinder_agency_name = null;
                    boolean isNickNameUpdated = false;
                    String planBucket = "";
                    String eventType = Constants.CommonConstants.RECHARGE_MESSAGE_TYPE;
                    Date transactionTime = new Date();
                    Date transactionUpdateTime = new Date();
                    Map<String, String> billsObj = new HashMap<String, String>();
                    Map<String, String> displayValues = new HashMap<>();
                    boolean finalisedState = false;
                    String in_response_code = null;
                    String isFreshBooking = "false";
                    String bookingId = null;
                    String pgRespCode = null;
                    String isRetryExhausted = "false";
                    OMSItemMetaModel omsItemMetaModel = null;
                    OMSFulfillmentReqModel omsFulfillmentReqModel = null;
                    OMSFulfillmentResponseModel fulfillmentResponse = null;
                    String omsTxnResponse = null;
                    OMSPGTxnResponseModel omsTxnResponseModel = null;
                    String payment_status = "";
                    Integer notPaidOnPaytm = 0;
                    String l4Digits="";
                    List<OMSPaymentModel> payment;
                    if (omsData.getPayment_status() != null) {
                        payment_status = omsData.getPayment_status().toString();
                    }

                    if (omsData.getPayment() != null) {
                        payment = omsData.getPayment();
                        omsTxnResponse = payment.get(0).getTransactionResponse();
                        if(omsTxnResponse!=null){
                            try {
                                omsTxnResponseModel = JsonUtils.parseJson(omsTxnResponse, OMSPGTxnResponseModel.class);
                                pgRespCode = omsTxnResponseModel.getPgRespCode();
                            } catch (Exception e) {
                                logger.error("[OMSListeners.insertDataIntoCassandra] json parse exception", e);
                            }
                        }
                    }

                    try {
                        categoryId = item.getProduct().getCategoryId();
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]categoryId::Exception for itemid {}", itemId, e);
                    }
                    try {
                        operator = item.getProduct().getAttributes().getOperator();
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]operator:Exception for itemid {}", itemId, e);
                    }
                    try {
                        paytype = item.getProduct().getAttributes().getPaytype();
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]paytype:Exception for itemid {}", itemId, e);
                    }
                    try {
                        service = item.getProduct().getAttributes().getService();
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service),"[OMSListeners.insertDataIntoCassandra]service:Exception for itemid {}", itemId, e);
                    }
                    try {
                        circle = item.getProduct().getAttributes().getCircle();
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra]circle:Exception for itemid {}", itemId, e);
                    }
                    try {
                        amount = String.valueOf(item.getPrice());
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra]amount:Exception for itemid {}", itemId, e);
                    }
                    try {
                        status = Long.toString(item.getStatus());
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra]status:Exception for itemid {}", itemId, e);
                    }
                    try {
                        transactionTime = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX").parse(item.getCreatedAt());
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra]transactionTime: Exception for itemid {}", itemId);
                    }
                    try {
                        transactionUpdateTime = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX").parse(item.getUpdatedAt());
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra]transactionTime: Exception for itemid {}", itemId);
                    }
                    try {
                        fulfillmentResponse = getFulfillmentResponse(omsData, item);
                        if (fulfillmentResponse != null) {
                            in_response_code = fulfillmentResponse.getIn_code();
                            bookingId = fulfillmentResponse.getBookingId();
                            isFreshBooking = "" + fulfillmentResponse.getFreshBooking();
                        }
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] fulfillmentResponse : Exception for itemid {}", itemId);
                    }
                    try {
                        logger.info(ServiceWrapperUtil.findServiceWrapper(service), "processing order {} for customerId {} recharge_number_1 {} service {} operator {}", orderId, customerId, recharge_number_1, service, operator);
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] exception for printing the logs for order id {}", orderId);
                    }
                    try {
                        omsItemMetaModel = getMetaData(item);
                        String metaData = item.getMeta_data();
                        JSONObject jsonObject = new JSONObject(metaData);
                        if (jsonObject.has("nickname")) {
                            isNickNameUpdated = true;
                            nickName = jsonObject.getString("nickname");
                        }
                    } catch (Exception e) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] omsItemMetaModel : Exception for itemid {} {}", itemId, e);
                    }

                    finalisedState = getFinalisedState(transactionUpdateTime, transactionTime);

                    String fullReq = item.getFulfillmentReq();

                    if (fullReq != null && !fullReq.isEmpty()) {
                        try {
                            omsFulfillmentReqModel = JsonUtils.parseJson(fullReq, OMSFulfillmentReqModel.class);
                            recharge_number_1 = omsFulfillmentReqModel.getRecharge_number();
                            recharge_number_1 = CommonUtils.getRechargeNumberBasedOnRules(recharge_number_1, operator);
                            omsFulfillmentReqModel.setRecharge_number(CommonUtils.getRechargeNumberBasedOnRules(recharge_number_1, operator));
                            recharge_number_2 = omsFulfillmentReqModel.getRecharge_number_2();
                            recharge_number_3 = omsFulfillmentReqModel.getRecharge_number_3();
                            recharge_number_4 = omsFulfillmentReqModel.getRecharge_number_4();
                        } catch (Exception e) {
                            logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra]OMSFulfillmentReqModel error:", e);
                        }
                    }

                    if (service != null) {
                        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service.toLowerCase());

                        if (DCATcategoryId != null && paytype.equalsIgnoreCase("prepaid")) {
                            try {
                                if (omsItemMetaModel != null) {
                                    if (omsItemMetaModel.getPlan_bucket() != null) {
                                        billsObj.put("plan_bucket", omsItemMetaModel.getPlan_bucket());
                                        planBucket = omsItemMetaModel.getPlan_bucket();
                                    }
                                    if (omsItemMetaModel.getValidity() != null)
                                        displayValues.put("validity", omsItemMetaModel.getValidity());
                                    if (omsItemMetaModel.getSms() != null)
                                        displayValues.put("sms", omsItemMetaModel.getSms());
                                    if (omsItemMetaModel.getData() != null)
                                        displayValues.put("data", omsItemMetaModel.getData());
                                    if (omsItemMetaModel.getTalktime() != null)
                                        displayValues.put("talktime", omsItemMetaModel.getTalktime());
                                    if (omsItemMetaModel.getAddon_benefit1() != null)
                                        displayValues.put("addon_benefit1", omsItemMetaModel.getAddon_benefit1());
                                    if (omsItemMetaModel.getAddon_benefit2() != null)
                                        displayValues.put("addon_benefit2", omsItemMetaModel.getAddon_benefit2());
                                    if (omsItemMetaModel.getAddon_benefit3() != null)
                                        displayValues.put("addon_benefit3", omsItemMetaModel.getAddon_benefit3());
                                    if (omsItemMetaModel.getAddon_benefit4() != null)
                                        displayValues.put("addon_benefit4", omsItemMetaModel.getAddon_benefit4());
                                    if (omsItemMetaModel.getDescription() != null)
                                        displayValues.put("description", omsItemMetaModel.getDescription());
                                    if (omsItemMetaModel.getPlan_id() != null)
                                        billsObj.put("plan_id", omsItemMetaModel.getPlan_id());

                                }

                                if ("mobile".equalsIgnoreCase(service) && (displayValues.isEmpty() || billsObj.get("plan_bucket") == null)) {
                                    if (item.getPrice() != null && item.getPrice() > 0) {
                                        logger.info(ServiceWrapperUtil.findServiceWrapper(service), "not getting plan bucket for  recharge number{} customer id {} meta data {}",recharge_number_1,customerId,omsItemMetaModel);
                                        String amountString = Double.valueOf(item.getPrice()).intValue() + "";
                                        String pid = null;
                                        if(Objects.nonNull(productId)){
                                            pid = productId.toString();
                                        }
                                        DCATGetPlanResponse dcatGetPlanResponse = rpsService.getPlanDetails(DCATcategoryId, Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, operator, amountString, circle, pid,recharge_number_1,service);

                                        logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] RPS fetch plan success  , :" + recharge_number_1 + ":" + orderId);

                                        if (dcatGetPlanResponse != null) {
                                            if (dcatGetPlanResponse.getPlan_bucket() != null) {
                                                billsObj.put("plan_bucket", dcatGetPlanResponse.getPlan_bucket());
                                            }
                                            if (dcatGetPlanResponse.getValidity() != null)
                                                displayValues.put("validity", dcatGetPlanResponse.getValidity());
                                            if (dcatGetPlanResponse.getSms() != null)
                                                displayValues.put("sms", dcatGetPlanResponse.getSms());
                                            if (dcatGetPlanResponse.getData() != null)
                                                displayValues.put("data", dcatGetPlanResponse.getData());
                                            if (dcatGetPlanResponse.getTalktime() != null)
                                                displayValues.put("talktime", dcatGetPlanResponse.getTalktime());
                                            if (dcatGetPlanResponse.getAddon_benefit1() != null)
                                                displayValues.put("addon_benefit1", dcatGetPlanResponse.getAddon_benefit1());
                                            if (dcatGetPlanResponse.getAddon_benefit2() != null)
                                                displayValues.put("addon_benefit2", dcatGetPlanResponse.getAddon_benefit2());
                                            if (dcatGetPlanResponse.getAddon_benefit3() != null)
                                                displayValues.put("addon_benefit3", dcatGetPlanResponse.getAddon_benefit3());
                                            if (dcatGetPlanResponse.getAddon_benefit4() != null)
                                                displayValues.put("addon_benefit4", dcatGetPlanResponse.getAddon_benefit4());
                                            if (dcatGetPlanResponse.getDescription() != null)
                                                displayValues.put("description", dcatGetPlanResponse.getDescription());
                                            if (dcatGetPlanResponse.getPlan_id() != null) {
                                                billsObj.put("plan_id", dcatGetPlanResponse.getPlan_id());
                                            }
                                        }
                                    }
                                }
                                else if (Constants.CommonConstants.DTH_SERVICE.equalsIgnoreCase(item.getProduct().getAttributes().getService()) && billsObj.get("plan_bucket") == null) {
                                    logger.info(ServiceWrapperUtil.findServiceWrapper(service), "dth validation for recharge number{} customer id {} meta data {}",recharge_number_1,customerId,omsItemMetaModel);
                                    if (Arrays.asList(Constants.DTH_GROUP)
                                            .contains(item.getProduct().getAttributes().getOperator().toLowerCase())) {
                                        billsObj.put(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY, Constants.DTH_PLAN_BUCKET);
                                    } else if (item.getProduct().getAttributes().getOperator().toLowerCase().equals(Constants.SUNTV)) {
                                        billsObj.put(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY, Constants.DTH_PLAN_BUCKET);
                                        logger.info(ServiceWrapperUtil.findServiceWrapper(service), "suntv setting amount as planbucket for orderid {}", orderId);
                                    }
                                }else{
                                    logger.info(ServiceWrapperUtil.findServiceWrapper(service), "valid plan details for recharge number{} customer id {} meta data {} plan_bucket {} display_values {}",recharge_number_1,customerId,omsItemMetaModel,billsObj.get("plan_bucket"),displayValues);
                                }

                            } catch (Exception e) {
                                logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] rpsGetPlanResponse error:" + orderId, e);
                            }
                        }
                        else if (service.equalsIgnoreCase("cylinder booking")) {
                            if (bookingId != null)
                                billsObj.put("cylinderBookingId", bookingId);
                            if (isFreshBooking != null)
                                billsObj.put("isFreshBooking", isFreshBooking);
                            if (omsItemMetaModel != null && omsItemMetaModel.getCylinder_agency_name() != null) {
                                billsObj.put(Constants.CYLINDER_AGENCY_NAME, omsItemMetaModel.getCylinder_agency_name());
                            }

                        }
                    }

                    if (omsItemMetaModel != null) {
                        if (omsItemMetaModel.getAdditionalData() != null && omsItemMetaModel.getAdditionalData().getConsumerName() != null) {
                            consumerName = omsItemMetaModel.getAdditionalData().getConsumerName();
                            billsObj.put("consumerName", consumerName);

                        }
                        if (omsItemMetaModel.getCylinder_agency_name() != null)
                            cylinder_agency_name = omsItemMetaModel.getCylinder_agency_name();

                        isRetryExhausted = String.valueOf(omsItemMetaModel.getIsRetryExhausted());
                    }
                    if (service != null) service = service.toLowerCase();
                    if (omsItemMetaModel != null) {
                        if (service != null && service.equals(Constants.CommonConstants.DTH_SERVICE)) {
                            if (omsItemMetaModel.getRMN() != null) {
                                billsObj.put("RMN", omsItemMetaModel.getRMN());
                            } else {
                                if (omsItemMetaModel.getKey() != null) {
                                    for (OMSItemMetaKeyObject omsItemMetaKeyObject : omsItemMetaModel.getKey()) {
                                        if (omsItemMetaKeyObject != null && omsItemMetaKeyObject.getSubId() != null) {
                                            if (!omsItemMetaKeyObject.getSubId().equals(recharge_number_1)) {
                                                billsObj.put("RMN", recharge_number_1);
                                                recharge_number_1 = omsItemMetaKeyObject.getSubId();
                                                if (omsFulfillmentReqModel != null) {
                                                    omsFulfillmentReqModel.setRecharge_number(recharge_number_1);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (customerId == null || recharge_number_1 == null ||
                            service == null || transactionTime == null ||
                            orderId == null || itemId == null || transactionUpdateTime == null || operator == null) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] :: Primary Key constraint failed customerId:" +
                                customerId + " rechargeNum:" + recharge_number_1 + " service " + service + " transactionTime:" + transactionTime +
                                " orderId " + orderId + " ItemId " + itemId + " transactionUpdate:" + transactionUpdateTime + " operator :" + operator);
                        List<String> tags=new ArrayList<>();
                        tags.add(Constants.MetricConstants.API_KEY+":"+Constants.IGNORE_TAG);
                        tags.add(Constants.MetricConstants.CATEGORY_METRIC_KEY+":"+(StringUtils.isEmpty(service) ? Constants.DEFAULT_CONSUMER_SOURCE : service));
                        metricsHelper.recordSuccessRateForMultipleTags(SERVICE_NAME, tags);
                        continue;
                    }
                    if(service!=null) service = service.toLowerCase();
                    if(operator!=null) operator = operator.toLowerCase();
                    if(paytype!=null) paytype = paytype.toLowerCase();
                    if(circle!=null) circle = circle.toLowerCase();

                    Boolean createEntry = true;
                    if (status.equals("6") && fulfillmentResponse == null) {
                        try {
                            List<ChannelHistory> channelHistoryResponse = channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndTransactionTimeAndOrderIdAndItemIdAndTransactionUpdateTime(customerId, recharge_number_1, service, transactionTime, orderId, itemId, transactionUpdateTime);
                            if (channelHistoryResponse != null && !channelHistoryResponse.isEmpty()) {
                                ChannelHistory channelHistoryObj = channelHistoryResponse.get(0);
                                if (channelHistoryObj != null) {
                                    if (channelHistoryObj.getStatus().equals("6")) {
                                        // In case of transaction failure refund is also started and we receive
                                        // empty fulfillments in kafka payload which overrides the in_response_code with null,
                                        // so it will not contain in_response_code which is already present in DB.
                                        logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] Skip Entry, ItemId: " + itemId);
                                        createEntry = false;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] exception2 : ", e);
                        }
                    }


                    if (paytype.equals(Constants.CREDIT_CARD_PAYTYPE)) {
                        mcn = recharge_number_1;
                        l4Digits=CreditCardUtils.getLast4Digits(mcn);
                        if (omsItemMetaModel == null) {
                            logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] omsItemMetaModel is null Skip Entry, ItemId {}", itemId);
                            createEntry = false;
                        } else if (recharge_number_4 != null && omsItemMetaModel.getPar() != null) {
                            recharge_number_1 = omsItemMetaModel.getPar();
                            billsObj.put("par", recharge_number_1);
                            par = recharge_number_1;
                            tin = recharge_number_4;
                        } else if (recharge_number_3 != null) {
                            recharge_number_1 = recharge_number_3;
                            billsObj.put("cin", recharge_number_1);
                            cin = recharge_number_1;
                        } else if (recharge_number_2 != null && CreditCardUtils.inFormat(recharge_number_2)) {
                            recharge_number_1 = CreditCardUtils.formatAndReturn(recharge_number_2);
                            billsObj.put("cin", recharge_number_1);
                            cin = recharge_number_1;
                        } else if (omsItemMetaModel.getCin() != null) {
                            recharge_number_1 = omsItemMetaModel.getCin();
                            billsObj.put("cin", recharge_number_1);
                            cin = recharge_number_1;
                        } else {
                            logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[OMSListeners.insertDataIntoCassandra] uniqueId for cc missing skip entry, ItemId: {}", itemId);
                            continue;
                        }

                        billsObj.put("mcn", mcn);
                        omsFulfillmentReqModel.setRecharge_number(recharge_number_1);
                    }

                    if (Arrays.asList(Constants.CommonConstants.POSTPAID_PAYTYPES).contains(paytype) || paytype.equals(Constants.CREDIT_CARD_PAYTYPE)) {
                        try {
                            String reminderRechargeNumber = recharge_number_1;
                            String uniqueId = null;
                            if (paytype.equals(Constants.CREDIT_CARD_PAYTYPE)) {
                                reminderRechargeNumber = billsObj.get("mcn");
                                uniqueId = recharge_number_1;
                            }
                            ReminderHistory remiderData = getReminderDetails(customerId, reminderRechargeNumber, uniqueId, service, operator);
                            if (remiderData != null) {
                                if (remiderData.getDue_date() != null) {
                                    String due_date = null;
                                    due_date = formatter.format(remiderData.getDue_date());
                                    billsObj.put(Constants.ReminderConstants.DUE_DATE, due_date);
                                }

                                if (remiderData.getBill_date() != null) {
                                    String bill_date = null;
                                    bill_date = formatter.format(remiderData.getBill_date());
                                    billsObj.put(Constants.ReminderConstants.BILL_DATE, bill_date);
                                }
                            }
                        } catch (Exception e) {
                            logger.error(ServiceWrapperUtil.findServiceWrapper(service), "insertDataIntoCassandra Exception: getReminderDetails", e);
                        }

                    }

                    if (omsData.getChannelId() != null) {
                        billsObj.put(Constants.CommonConstants.CHANNEL, omsData.getChannelId());
                    }

                    if (pgRespCode != null) {
                        billsObj.put(Constants.PG_RESP_CODE, pgRespCode);
                    }

                    if (isRetryExhausted != null) {
                        billsObj.put(Constants.IS_RETRY_EXHAUST, isRetryExhausted);
                    }

                    if (createEntry.equals(true)) {

                        Boolean is_new_biller = null;
                        if((Service.RENT_PAYMENT.value.equalsIgnoreCase(service)|| service.equalsIgnoreCase(Service.BUSINESS_PAYMENT.value)) && !StringUtils.isEmpty(omsFulfillmentReqModel.getRecharge_number_7())){
                            logger.info(ServiceWrapperUtil.findServiceWrapper(service), "RENT_PAYMENT for cust id = {} , rech number 1 = {} , rech number 7 = {}",customerId,recharge_number_1,omsFulfillmentReqModel.getRecharge_number_7());
                            String temp = recharge_number_1;
                            recharge_number_1 = omsFulfillmentReqModel.getRecharge_number_7();
                            omsFulfillmentReqModel.setRecharge_number_7(temp);
                            is_new_biller = true;
                        }

                        try {
                            dropOffHandling(omsData, item, omsFulfillmentReqModel, fulfillmentResponse, in_response_code, status, payment_status, displayValues, billsObj,recharge_number_1);
                        } catch (Exception e) {
                            metricsHelper.recordSuccessRate(SERVICE_NAME, Constants.DROPOFF_INSERTION_TAG);
                            logger.error(ServiceWrapperUtil.findServiceWrapper(service), "dropOffHandling Exception:" + itemId, e);
                        }
                        try {
                            channelHistoryService.save(new ChannelHistory(customerId, recharge_number_1, productId, orderId, createdTime, categoryId, recharge_number_2, recharge_number_3, recharge_number_4, operator, paytype, service, amount, billsObj, status, eventType, transactionTime, transactionUpdateTime, finalisedState, itemId, displayValues, circle, in_response_code, payment_status), service);
                            logger.info(ServiceWrapperUtil.findServiceWrapper(service), "Insert Success , ItemId:" + itemId);
                        } catch (Exception e) {
                            metricsHelper.recordSuccessRate(SERVICE_NAME, Constants.ERROR_TAG);
                            logger.error(ServiceWrapperUtil.findServiceWrapper(service), "Insert Exception:" + itemId, e);
                        }

                        try {
                            if (Objects.nonNull(nickName) || Objects.nonNull(consumerName) || Objects.nonNull(cylinder_agency_name)) {
                                logger.info(ServiceWrapperUtil.findServiceWrapper(service), "updating nickname for customerId {} recharge_number_1 {} service {} operator {}", customerId, recharge_number_1, service, operator);
                            }
                            if (paytype != null && paytype.equals(Constants.CREDIT_CARD_PAYTYPE)) {

                                operator = Constants.CommonConstants.CC_DEFAULT_OPERATOR;
                            }
                            boolean isTokenizedTransaction = false;
                            if (par != null && !par.isEmpty()) {
                                isTokenizedTransaction = true;
                            }

                            List<Recents> recents = null;
                            String state = OMSToRechargeStatus.getRechargeStatusByOMSStatus(status, in_response_code, payment_status);

                            if (billsObj != null && StringUtils.isEmpty(planBucket))
                                planBucket = ObjectUtils.firstNonNull(billsObj.get(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY), "");
                            if (Arrays.asList(OMS_SUCCESS_STATE).contains(state)) {
                                String lastFailureTxn = null;
                                String lastPenidngTxn = null;


                                recents = recentService.fetchRecentsFromDb(customerId,service,recharge_number_1,operator,planBucket);
                                handleTxnTimesMigration(recents, transactionTime);
                                if(Objects.nonNull(recents) && recents.size() > 0){
                                    lastPenidngTxn = recents.get(0).getLastPendingTxn();
                                    lastFailureTxn = recents.get(0).getLastFailureTxn();
                                }

                                recentService.updateRecentTxnDetails(mcn, customerId,
                                        orderId, item.getPrice(), service,
                                        operator, nickName, transactionTime,
                                        planBucket, cin, par, in_response_code, tin,
                                        isTokenizedTransaction, productId, transactionUpdateTime, recharge_number_1,
                                        omsFulfillmentReqModel.getRecharge_number_2(), omsFulfillmentReqModel.getRecharge_number_3(),
                                        omsFulfillmentReqModel.getRecharge_number_4(), omsFulfillmentReqModel.getRecharge_number_5(),
                                        omsFulfillmentReqModel.getRecharge_number_6(), omsFulfillmentReqModel.getRecharge_number_7(),
                                        omsFulfillmentReqModel.getRecharge_number_8(), circle, paytype, consumerName,
                                        cylinder_agency_name, omsData.getChannelId(), isNickNameUpdated, notPaidOnPaytm, rawMessage, lastFailureTxn, lastPenidngTxn, true,status,is_new_biller, pgRespCode, isRetryExhausted);

                            } else if (Arrays.asList(OMS_FAILURE_STATES).contains(state)) {
                                JSONObject lastFailureTxn = new JSONObject();
                                String lastPenidngTxn = null ;
                                lastFailureTxn.put("txn_id", orderId);
                                lastFailureTxn.put("txn_status", in_response_code);
                                lastFailureTxn.put("txn_time", item.getCreatedAt());
                                lastFailureTxn.put("txn_amount", item.getPrice());
                                lastFailureTxn.put("channel_id", omsData.getChannelId());

                                recents = recentService.fetchRecentsFromDb(customerId,service,recharge_number_1,operator,planBucket);
                                if(Objects.nonNull(recents) && recents.size() > 0){
                                    lastPenidngTxn = recents.get(0).getLastPendingTxn();
                                }

                                recentService.updateRecentTxnDetails(mcn, customerId,
                                        orderId, item.getPrice(), service,
                                        operator, nickName, transactionTime,
                                        planBucket, cin, par, in_response_code, tin,
                                        isTokenizedTransaction, productId, transactionUpdateTime, recharge_number_1,
                                        omsFulfillmentReqModel.getRecharge_number_2(), omsFulfillmentReqModel.getRecharge_number_3(),
                                        omsFulfillmentReqModel.getRecharge_number_4(), omsFulfillmentReqModel.getRecharge_number_5(),
                                        omsFulfillmentReqModel.getRecharge_number_6(), omsFulfillmentReqModel.getRecharge_number_7(),
                                        omsFulfillmentReqModel.getRecharge_number_8(), circle, paytype, consumerName,
                                        cylinder_agency_name, omsData.getChannelId(), isNickNameUpdated, notPaidOnPaytm, rawMessage, lastFailureTxn.toString(), lastPenidngTxn, true,status,is_new_biller, pgRespCode, isRetryExhausted);

                            } else if (Arrays.asList(OMS_PEDING_STATES).contains(state)) {
                                JSONObject lastPenidngTxn = new JSONObject();
                                lastPenidngTxn.put("txn_id", orderId);
                                lastPenidngTxn.put("txn_status", in_response_code);
                                lastPenidngTxn.put("txn_time", item.getCreatedAt());
                                lastPenidngTxn.put("txn_amount", item.getPrice());
                                lastPenidngTxn.put("channel_id", omsData.getChannelId());

                                String lastFailureTxn = null;

                                recents = recentService.fetchRecentsFromDb(customerId,service,recharge_number_1,operator,planBucket);
                                if(Objects.nonNull(recents) && recents.size() > 0){
                                    lastFailureTxn = recents.get(0).getLastFailureTxn();
                                }

                                recentService.updateRecentTxnDetails(mcn, customerId,
                                        orderId, item.getPrice(), service,
                                        operator, nickName, transactionTime,
                                        planBucket, cin, par, in_response_code, tin,
                                        isTokenizedTransaction, productId, transactionUpdateTime, recharge_number_1,
                                        omsFulfillmentReqModel.getRecharge_number_2(), omsFulfillmentReqModel.getRecharge_number_3(),
                                        omsFulfillmentReqModel.getRecharge_number_4(), omsFulfillmentReqModel.getRecharge_number_5(),
                                        omsFulfillmentReqModel.getRecharge_number_6(), omsFulfillmentReqModel.getRecharge_number_7(),
                                        omsFulfillmentReqModel.getRecharge_number_8(), circle, paytype, consumerName,
                                        cylinder_agency_name, omsData.getChannelId(), isNickNameUpdated, notPaidOnPaytm, rawMessage, lastFailureTxn, lastPenidngTxn.toString(), true,status,is_new_biller,pgRespCode,isRetryExhausted);
                            }



                        } catch (RuntimeException e) {
                            metricsHelper.recordSuccessRate(SERVICE_NAME, Constants.ERROR_TAG);
                            logger.error(ServiceWrapperUtil.findServiceWrapper(service), "Recent Insert Exception: for item id {} order id {}", itemId, orderId, e);
                        }


                    }


                } catch (Exception e) {
                    metricsHelper.recordSuccessRate(SERVICE_NAME, Constants.ERROR_TAG);
                    logger.error("[OMSListeners.insertDataIntoCassandra] exception1 : ", e);
                }
            }
        } catch (Exception e) {
            metricsHelper.recordSuccessRate(SERVICE_NAME, Constants.ERROR_TAG);
            logger.error("insertDataIntoCassandra Exception", e);
        }
    }


    public boolean isSkippable(OMSItemModel item) {
        try {
            String paytype = item.getProduct().getAttributes().getPaytype();
            if (paytype != null) paytype = paytype.toLowerCase();
            if (Arrays.asList(Constants.CommonConstants.SKIP_PAYTYPES).contains(paytype)) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.isSkippable] skipping credit card item" + item.getItemId());
                return true;
            }
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.isSkippable] Error", e);
        }
        return false;
    }

    public boolean getFinalisedState(Date updatedAt, Date createdAt) {
        long diff = updatedAt.getTime() - createdAt.getTime();

        if (TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS) > Constants.CommonConstants.FINALISED_THRESHOLD_DATE) {
            return true;
        }
        return false;
    }

    public OMSFulfillmentResponseModel getFulfillmentResponse(OMSResponseModel omsData, OMSItemModel item) {
        if (omsData == null || item == null) return null;

        OMSFulfillmentResponseModel omsFulfillmentResponseModelModel = null;

        Long ful_id = item.getFulfillmentId();
        if (ful_id != null) {
            List<OMSFulfillmentsModel> fulfillmentsList = omsData.getFulfillments();

            if (fulfillmentsList != null && !fulfillmentsList.isEmpty()) {

                for (OMSFulfillmentsModel fulfillment : fulfillmentsList) {
                    Long fulfillmentId = fulfillment.getId();

                    if (fulfillmentId != null && fulfillmentId.equals(ful_id)) {
                        String ful_response = fulfillment.getFulfillment_response();
                        if (ful_response != null) {
                            try {
                                omsFulfillmentResponseModelModel = JsonUtils.parseJson(ful_response, OMSFulfillmentResponseModel.class);
                            } catch (Exception e) {
                                logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.getInResponseCode] json parse exception", e);
                            }
                        }
                    }

                }
            } else {
                if (item.getStatus() != null && Long.toString(item.getStatus()).equals("6")) {
                    logger.error(ServiceWrapperUtil.findServiceWrapper(item), "OMSFulfillmentResponseModel getFulfillmentResponse:: empty fulfillment found" + omsData.toString());
                }
            }

        }

        return omsFulfillmentResponseModelModel;
    }

    public OMSItemMetaModel getMetaData(OMSItemModel item) {
        if (item == null) return null;

        OMSItemMetaModel omsItemMetaModel = null;
        String meta = item.getMeta_data();
        List<String> addOnBenefits = null;

        if (meta != null) {
            try {
                omsItemMetaModel = JsonUtils.parseJson(meta, OMSItemMetaModel.class);
            } catch (Exception e) {
                logger.error("[OMSListeners.getMetaData] json parse exception", e);
            }
        }
        if (omsItemMetaModel != null) {
            OMSItemMetaBenefitsModel omsItemMetaBenefitsModel = omsItemMetaModel.getRecharge_benefits();

            if (omsItemMetaBenefitsModel != null) {
                if (omsItemMetaBenefitsModel.getData() != null)
                    omsItemMetaModel.setData(omsItemMetaBenefitsModel.getData());
                if (omsItemMetaBenefitsModel.getDescription() != null)
                    omsItemMetaModel.setDescription(omsItemMetaBenefitsModel.getDescription());
                if (omsItemMetaBenefitsModel.getSms() != null)
                    omsItemMetaModel.setSms(omsItemMetaBenefitsModel.getSms());
                if (omsItemMetaBenefitsModel.getTalktime() != null)
                    omsItemMetaModel.setTalktime(omsItemMetaBenefitsModel.getTalktime());
                if (omsItemMetaBenefitsModel.getValidity() != null)
                    omsItemMetaModel.setValidity(omsItemMetaBenefitsModel.getValidity());

                addOnBenefits = omsItemMetaBenefitsModel.getAddon_benefit();
            }
            if (addOnBenefits == null) addOnBenefits = omsItemMetaModel.getAddon_benefit();

            if (addOnBenefits != null) {

                for (int i = 0; i < addOnBenefits.size(); i++) {
                    if (addOnBenefits.get(i) != null) {
                        switch (i) {
                            case 0:
                                omsItemMetaModel.setAddon_benefit1(addOnBenefits.get(i));
                                break;
                            case 1:
                                omsItemMetaModel.setAddon_benefit2(addOnBenefits.get(i));
                                break;
                            case 2:
                                omsItemMetaModel.setAddon_benefit3(addOnBenefits.get(i));
                                break;
                            case 3:
                                omsItemMetaModel.setAddon_benefit4(addOnBenefits.get(i));
                                break;
                            default:
                        }
                    }

                }

            }
        }


        return omsItemMetaModel;
    }

    public void dropOffHandling(OMSResponseModel omsData,
                                OMSItemModel item,
                                OMSFulfillmentReqModel omsFulfillmentReqModel,
                                OMSFulfillmentResponseModel fulfillmentResponse,
                                String in_response_code,
                                String status,
                                String payment_status,
                                Map<String, String> displayValues,
                                Map<String, String> billsObj,
                                String recharge_number_1) {
        String rechargeNumber = "";
        String operator = "";
        String service = "";
        String paytype = "";
        Long customerId = omsData.getCustomerId();

        if (item == null || item.getProduct() == null || item.getProduct().getCategoryId() == null) {
            logger.info(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.isSkippable] skipping dropOff" + item);
            return;
        } else {
            if (!Arrays.asList(Constants.CommonConstants.DROP_OFF_ENABLED_CATEGORY_IDS).contains(item.getProduct().getCategoryId())) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.isSkippable] skipping categoryId" + item);
                return;
            }

        }


        if (item != null && item.getProduct() != null && item.getProduct().getAttributes() != null) {
            operator = item.getProduct().getAttributes().getOperator();
            service = item.getProduct().getAttributes().getService();
            paytype = item.getProduct().getAttributes().getPaytype();
        }
        if (operator != null) operator = operator.toLowerCase();
        if (service != null) service = service.toLowerCase();
        if (paytype != null) paytype = paytype.toLowerCase();

        if (omsFulfillmentReqModel != null) {
            rechargeNumber = omsFulfillmentReqModel.getRecharge_number();
            if (paytype != null && paytype.equals(Constants.CREDIT_CARD_PAYTYPE)) {
                rechargeNumber = CreditCardUtils.getUniqueIdForCC(billsObj);
            }
            if(Service.RENT_PAYMENT.value.equalsIgnoreCase(service) || service.equalsIgnoreCase(Service.BUSINESS_PAYMENT.value)){
                rechargeNumber = recharge_number_1;
            }
        }


        DropOff dropOffData = prepareDropOffData(omsData, item, omsFulfillmentReqModel, fulfillmentResponse, displayValues, billsObj,rechargeNumber);

        mappingService.setRechargeNumberMap(rechargeNumber, customerId, service, operator, new Date());

        String state = OMSToRechargeStatus.getRechargeStatusByOMSStatus(status, in_response_code, payment_status);
        if (state != null && state.equals("SUCCESS") && paytype != null && paytype.equals("prepaid")) {
            List<DropOff> insertDropOffList = new ArrayList<>();
            insertDropOffList.add(dropOffData);
            Long actualCustomerId = dropOffData.getCustomerId();


            List<RechargeNumberToCustIdMap> customerIdsList = mappingService.getCustomerIds(rechargeNumber, service, operator);

            if (customerIdsList.size() >= RN_TO_CUSTID_SIZE_LOGTHRESHOLD) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(item), "dropOffHandling:: RN TO CUSTID Mapping exceed threshhold  count {} rechargeNumber {} service {} operator {} ", customerIdsList.size(), rechargeNumber, service, operator);
            }

            if (customerIdsList.size() > RECHARGENUMBER_TO_CUSTOMER_LIST_THRESHOLD) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(item), "dropOffHandling:: CustomerList for rechargeNumber exceed threshold value {} actual size {} ", RECHARGENUMBER_TO_CUSTOMER_LIST_THRESHOLD, customerIdsList.size());
                return;
            }

            for (RechargeNumberToCustIdMap rechMap : customerIdsList) {
                DropOff dropOffNew = createClone(dropOffData);
                if (dropOffNew != null) {
                    if (!actualCustomerId.equals(rechMap.getCustomerId())) {
                        dropOffNew.setCustomerId(rechMap.getCustomerId());
                        dropOffNew.setDummyRecharge(true);
                        insertDropOffList.add(dropOffNew);
                    }
                }
            }


            logger.info(ServiceWrapperUtil.findServiceWrapper(item), "dropOffHandling:: Saving DropOff(SUCCESS)" + insertDropOffList);
            dropOffDBHelper.insertDropOff(insertDropOffList);
        } else {
            logger.info(ServiceWrapperUtil.findServiceWrapper(item), "dropOffHandling:: insert success for service {} , order id {}", dropOffData.getService(), dropOffData.getOrderId());
            dropOffDBHelper.insertDropOff(dropOffData);
        }

    }

    public DropOff prepareDropOffData(OMSResponseModel omsData,
                                      OMSItemModel item,
                                      OMSFulfillmentReqModel omsFulfillmentReqModel,
                                      OMSFulfillmentResponseModel fulfillmentResponse,
                                      Map<String, String> displayValues,
                                      Map<String, String> billsObj,
                                      String recharge_number_1) {
        DropOff dropOff = new DropOff();
        dropOff.setAmount(String.valueOf(item.getPrice()));

        Long customerId = omsData.getCustomerId();
        Long productId = item.getProductId();
        Date createdTime = new Date();
        Long categoryId = Long.valueOf(1);
        Long itemId = item.getItemId();
        String service = null;
        String operator = null;
        String paytype = "";
        String status = "";
        String circle = "";
        String eventType = Constants.CommonConstants.RECHARGE_MESSAGE_TYPE;
        Date transactionTime = null;


        if (omsFulfillmentReqModel != null) {
            dropOff.setRechargeNumber(omsFulfillmentReqModel.getRecharge_number());
            dropOff.setRecharge_number_2(omsFulfillmentReqModel.getRecharge_number_2());
            dropOff.setRecharge_number_3(omsFulfillmentReqModel.getRecharge_number_3());
            dropOff.setRecharge_number_4(omsFulfillmentReqModel.getRecharge_number_4());
            dropOff.setRecharge_number_5(omsFulfillmentReqModel.getRecharge_number_5());
            dropOff.setRecharge_number_6(omsFulfillmentReqModel.getRecharge_number_6());
            dropOff.setRecharge_number_7(omsFulfillmentReqModel.getRecharge_number_7());
            dropOff.setRecharge_number_8(omsFulfillmentReqModel.getRecharge_number_8());
        }

        try {
            categoryId = item.getProduct().getCategoryId();
            dropOff.setCategoryId(categoryId);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]categoryId::Exception" + itemId, e);
        }
        try {
            operator = item.getProduct().getAttributes().getOperator();
            if (operator != null) operator = operator.toLowerCase();
            dropOff.setOperator(operator);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]operator:Exception" + itemId, e);
        }
        try {
            paytype = item.getProduct().getAttributes().getPaytype();
            if (paytype != null) paytype = paytype.toLowerCase();
            dropOff.setPaytype(paytype);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]paytype:Exception" + itemId, e);
        }
        try {
            service = item.getProduct().getAttributes().getService();
            if (service != null) service = service.toLowerCase();
            dropOff.setService(service);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]service:Exception" + itemId, e);
        }
        try {
            circle = item.getProduct().getAttributes().getCircle();
            dropOff.setCircle(circle);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]circle:Exception" + itemId, e);
        }
        try {
            transactionTime = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX").parse(item.getCreatedAt());
            dropOff.setTransactionTime(DateUtil.secondsIncrDecr(transactionTime, 1));
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]transactionTime: Exception" + itemId);
        }
        try {
            status = Long.toString(item.getStatus());
            dropOff.setStatus(status);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(item), "[OMSListeners.insertDataIntoCassandra]status:Exception" + itemId, e);
        }


        if (omsData.getPayment_status() != null) {
            dropOff.setPayment_status(omsData.getPayment_status().toString());
        }

        fulfillmentResponse = getFulfillmentResponse(omsData, item);
        if (fulfillmentResponse != null) {
            dropOff.setIn_response_code(fulfillmentResponse.getIn_code());
        }

        if (paytype != null && paytype.equals(Constants.CREDIT_CARD_PAYTYPE)) {
            dropOff.setRechargeNumber(CreditCardUtils.getUniqueIdForCC(billsObj));
        }

        if(service.equalsIgnoreCase(Constants.RENT_PAYMENT) || service.equalsIgnoreCase(Service.BUSINESS_PAYMENT.value)){
            dropOff.setRechargeNumber(recharge_number_1);
        }


        dropOff.setOrderId(omsData.getOrderId());
        dropOff.setItemId(item.getItemId());

        dropOff.setProductId(productId);
        dropOff.setEventType(eventType);
        dropOff.setCreatedTime(createdTime);
        dropOff.setCustomerId(customerId);
        dropOff.setBillsObj(billsObj);
        dropOff.setDisplayValues(displayValues);
        dropOff.setDummyRecharge(false);

        return dropOff;

    }

    public DropOff createClone(DropOff dropOffObj) {
        ObjectMapper objectMapper = new ObjectMapper();

        DropOff dropOffObjNew = null;
        try {
            dropOffObjNew = objectMapper
                    .readValue(objectMapper.writeValueAsString(dropOffObj), DropOff.class);
        } catch (IOException e) {
            logger.error("[OMSListeners.createClone] exception", e);
        }
        return dropOffObjNew;
    }

    public ReminderHistory getReminderDetails(Long customerId, String rechargeNumber, String uniqueId, String service, String operator) {
        List<ReminderHistory> reminderRecords = null;
        ReminderHistory result = null;
        if (customerId == null || rechargeNumber == null || service == null || operator == null) return null;
        try {
            reminderRecords = reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber, service, operator);
            if (!CollectionUtils.isEmpty(reminderRecords)) {
                if (uniqueId != null) {
                    for (ReminderHistory reminderHistory : reminderRecords) {
                        if (reminderHistory.getPar() != null && reminderHistory.getPar().equals(uniqueId)) {
                            return reminderHistory;
                        }
                        if (reminderHistory.getReference_id() != null && reminderHistory.getReference_id().equals(uniqueId)) {
                            return reminderHistory;
                        }
                    }
                }
            } else {
                result = reminderRecords.get(0);
            }
        } catch (Exception e) {
            logger.error("[OMSListeners.getReminderDetails] exception", e);
        }
        return result;
    }

    public void handleTxnTimesMigration(List<Recents> existingRecents, Date transactionTime) {
        try {
            logger.debug("inside handleTxnTimesMigration");

            Set<Date> txnTimes = null;
            Recents recent = null;
            // Use existing recents data if available
            if (existingRecents != null && !existingRecents.isEmpty()) {
                recent = existingRecents.get(0);
                txnTimes = recent.getTxnTimes(); // Assuming getTxnTimes() exists
            }
            if (recent == null) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "handleTxnTimesMigration  No existing recents data found");
                List<String> tags=new ArrayList<>();
                tags.add(PrometheusConstants.TAG_KEY.STATUS +":"+ PrometheusConstants.TAG_VALUE.NOT_FOUND);
                metricsHelper.recordSuccessRateForMultipleTags(PrometheusConstants.METRIC_NAME.TXN_TIME_MIGRATION_PRODUCER, tags);
                return;
            }

            if(!recentService.isValidToUpdateTxnTimes(recent, PrometheusConstants.METRIC_NAME.TXN_TIME_MIGRATION_PRODUCER)) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "handleTxnTimesMigration skipping update for recent {}", recent);
                return;
            }
            if (txnTimes == null || txnTimes.isEmpty()) {
                // Push to migration topic only if no existing data
                RecentTxnMigrationDTO dto = new RecentTxnMigrationDTO(
                        recent.getKey().getCustomerId(), recent.getKey().getRechargeNumber(), recent.getKey().getService(), recent.getKey().getOperator(), recent.getKey().getPlanBucket(), transactionTime);

                txnTimeMigration.send(recentTxnMigrationKafkaConfig.getTopic(), objectMapper.writeValueAsString(dto));
                logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "Pushed to migration topic {}",
                    objectMapper.writeValueAsString(dto));
                List<String> tags=new ArrayList<>();
                tags.add(PrometheusConstants.TAG_KEY.STATUS +":"+ PrometheusConstants.TAG_VALUE.SUCCESS);
                metricsHelper.recordSuccessRateForMultipleTags(PrometheusConstants.METRIC_NAME.TXN_TIME_MIGRATION_PRODUCER, tags);

            }
        } catch (Exception e) {
            logger.error("Error in handleTxnTimesMigration {}", e);
        }
    }

}


