package com.paytm.saga.listeners;

import com.newrelic.api.agent.Trace;
import com.paytm.saga.common.configuration.PrepaidBillsKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.builder.RecentsBuilder;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.service.RPSService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.TTLUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.util.Pair;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.lang.NonNull;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
@DependsOn("PrepaidBillsKafkaPropertiesConfig")
@Conditional(PrepaidBillsKafkaConsumerEnableCondition.class)
public class PrepaidBillsListener {

    private final Logger logger = LogManager.getLogger(PrepaidBillsListener.class);

    private RPSService rpsService;

    private PlanExpiryHistoryRepository planExpiryHistoryRepository;

    private RecentService recentService;

    private RecentsRepositoryWrapperService recentsRepository;

    private DropOffDBHelper dropOffDBHelper;

    private MetricsHelper metricsHelper;
    @Autowired
    public PrepaidBillsListener(@NonNull RPSService rpsService, @NonNull PlanExpiryHistoryRepository planExpiryHistoryRepository, @NonNull RecentService recentService, @NonNull RecentsRepositoryWrapperService recentsRepository, @NonNull DropOffDBHelper dropOffDBHelper, @NonNull MetricsHelper metricsHelper){
        this.rpsService = rpsService;
        this.planExpiryHistoryRepository = planExpiryHistoryRepository;
        this.recentService = recentService;
        this.recentsRepository = recentsRepository;
        this.dropOffDBHelper = dropOffDBHelper;
        this.metricsHelper = metricsHelper;
    }

    @KafkaListener(topics = "#{PrepaidBillsKafkaPropertiesConfig.getTopicName()}",
            groupId = "#{PrepaidBillsKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory ="PrepaidBillsKafkaListenerContainerFactory"
    )
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment){
        for(String message : messages){

            logger.info("[PrepaidBillsListener.listen] :: Prepaid Bills listener starting , message ={}",message);
            metricsHelper.recordSuccessRate(Constants.PREPAID_BILLS_CONSUMER, Constants.NEW_KAFKA_EVENT);
            PrepaidBillsListenerModel prepaidBillsListenerModel = null;
            if(Objects.nonNull(message)){
                try{
                    prepaidBillsListenerModel = JsonUtils.parseJson(message, PrepaidBillsListenerModel.class);
                    insertDataIntoCassandra(prepaidBillsListenerModel);
                }
                catch (Exception e){
                    logger.error("[PrepaidBillsListener.listen] :: Exception in parsing json",e);
                    metricsHelper.recordSuccessRate(Constants.PREPAID_BILLS_CONSUMER, Constants.ERROR_TAG);
                }
            }
            try{
                acknowledgment.acknowledge();
            }
            catch (Exception e){
                logger.error("[PrepaidBillsListener.listen] :: Exception in acknowledgment",e);

            }
        }
    }

    @Trace(dispatcher=true)
    public void insertDataIntoCassandra(PrepaidBillsListenerModel prepaidBillsListenerModel){
        logger.info("[PrepaidBillsListener.insertDataIntoCassandra] :: Starting insertDataIntoCassandra with data ={}",prepaidBillsListenerModel);
        if(Boolean.TRUE.equals(isSkippable(prepaidBillsListenerModel))){
        logger.info("[PrepaidBillsListener.insertDataIntoCassandra] :: skipping data");
            metricsHelper.recordSuccessRate(Constants.PREPAID_BILLS_CONSUMER, Constants.IGNORE_TAG);
            return;
        }
        String rechargeNumber = prepaidBillsListenerModel.getData().getRecharge_number();
        String service = prepaidBillsListenerModel.getData().getService();
        String operator = prepaidBillsListenerModel.getData().getOperator();
        String circle = ObjectUtils.firstNonNull(prepaidBillsListenerModel.getData().getCircle(),"");
        Double amount = prepaidBillsListenerModel.getData().getAmount();
        Long customerId = prepaidBillsListenerModel.getData().getCustomer_id();
        Date dueDate = null;
        Date createdAt = null;
        Date updatedAt = null;
        String planBucket = "";
        Map<String,Object> metaData = prepaidBillsListenerModel.getData().getMetaData();
        Long productId = prepaidBillsListenerModel.getData().getProductId();
        Map<String, String> displayValues = null;

        dueDate = DateUtil.stringToDate(prepaidBillsListenerModel.getData().getDueDate(), DateFormats.RECENTS_DATE_FORMAT);
        createdAt = DateUtil.stringToDate(prepaidBillsListenerModel.getData().getCreated_at(), DateFormats.RECENTS_DATE_FORMAT);
        updatedAt = DateUtil.stringToDate(prepaidBillsListenerModel.getData().getUpdated_at(), DateFormats.RECENTS_DATE_FORMAT);


        if(Objects.nonNull(metaData) && metaData.containsKey("plan_bucket")){
            planBucket = metaData.get("plan_bucket").toString();
        }

        if(Objects.nonNull(updatedAt)) {
            updatedAt = DateUtils.addHours(updatedAt, 5);
            updatedAt = DateUtils.addMinutes(updatedAt, 30);
        }
        if(Objects.nonNull(createdAt)) {
            createdAt = DateUtils.addHours(createdAt, 5);
            createdAt = DateUtils.addMinutes(createdAt, 30);
        }

        if(StringUtils.equalsIgnoreCase(prepaidBillsListenerModel.getOperationType(),Constants.OP_DELETE)){
            try {
                recentService.unsetAutomaticForDeleteEventFromPrepaidBills(prepaidBillsListenerModel,planBucket);
            }
            catch (Exception e){
                metricsHelper.recordSuccessRate(Constants.PREPAID_BILLS_CONSUMER, Constants.PREPAID_BILLS_DELETE_EVENT_ERROR_TAG);
                logger.error("[PrepaidBillsListener.insertDataIntoCassandra] :: Exception occurred while unsetting automatic for delete event from prepaidBills, e = ",e);
            }
            return;
        }

        String categoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service);
        if(Objects.nonNull(categoryId) && amount > 0) {
            DCATGetPlanResponse dcatGetPlanResponse = null;
            try {
                dcatGetPlanResponse = rpsService.getPlanDetails(categoryId, Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, operator, amount.toString(), circle, productId.toString(), rechargeNumber, service);
                if (Objects.nonNull(dcatGetPlanResponse)) {
                    displayValues = getDisplayValuesMapFromDCATGetPlanResponse(dcatGetPlanResponse);
                }
            } catch (Exception e) {
                metricsHelper.recordSuccessRate(Constants.PREPAID_BILLS_CONSUMER, Constants.DCAT_API_ERROR);
                logger.error("[PrepaidBillsListener.insertDataIntoCassandra] :: Exception occurred while getting plandetails from RPS, e = ", e);
            }
        }

            Integer ttl = TTLUtils.getPlanValidityTTL(dueDate);
            PlanExpiryHistory planExpiryHistory = new PlanExpiryHistory(rechargeNumber, service, circle, operator, planBucket, updatedAt, customerId, null, dueDate, createdAt,displayValues,amount);
            try{
                planExpiryHistoryRepository.save(planExpiryHistory,ttl);
                logger.info("[PrepaidBillsListener.insertDataIntoCassandra] :: Succesfully saved into PlanExpiryHistory table ={}",planExpiryHistory);
            }
            catch (Exception e){
                metricsHelper.recordSuccessRate(Constants.PREPAID_BILLS_CONSUMER, Constants.PLAN_EXPIRY_HISTORY_SAVE_ERROR);
                logger.error("[PrepaidBillsListener.insertDataIntoCassandra] :: Exception occurred while saving planExpiryHistory to cassandra, e = ",e);
            }

            try {
                Recents recents = RecentsBuilder.fromPrepaidBillsListenerData.apply(Pair.of(planExpiryHistory, prepaidBillsListenerModel));
                logger.info("[PrepaidBillsListener.insertDataIntoCassandra] :: Recents obj created from planexpiryhistoruyObj , recent = {}",recents);
                recentService.insertIntoRecentAndCustomerBill(recents, planExpiryHistory);
            }
            catch (Exception e){
                metricsHelper.recordSuccessRate(Constants.PREPAID_BILLS_CONSUMER, Constants.RECENT_UPDATE_ERROR);
                logger.error("[PrepaidBillsListener.insertDataIntoCassandra] :: Exception occurred while saving recents to cassandra, e={}",e.getMessage());
            }
            addToDropOff(planExpiryHistory,prepaidBillsListenerModel);


    }

    private Boolean isSkippable(PrepaidBillsListenerModel prepaidBillsListenerModel){
        if(Objects.isNull(prepaidBillsListenerModel) || Objects.isNull(prepaidBillsListenerModel.getData()))
        {
            logger.info("[PrepaidBillsListener.isSkippable] :: Skipping due to null data or no new data");
            return true;
        }

        if(StringUtils.equalsIgnoreCase(Constants.OP_UPDATE,prepaidBillsListenerModel.getOperationType()) ){
            if(Boolean.FALSE.equals(isOldDataUpdated(prepaidBillsListenerModel.getOld()))){
                logger.info("[PrepaidBillsListener.isSkippable] :: Skipping due to old data not updated");
                return true;
            }
        }
        return false;
    }

    private Boolean isOldDataUpdated(PrepaidBillsListenerDataModel prepaidListenerOldData){
        if(Objects.isNull(prepaidListenerOldData)){
            logger.info("[PrepaidBillsListener.isOldDataUpdated] :: Ignoring because Old data is  null");
            return false;
        }
        if(Objects.isNull(prepaidListenerOldData.getRecharge_number()) && Objects.isNull(prepaidListenerOldData.getAmount()) && Objects.isNull(prepaidListenerOldData.getCustomer_id()) && Objects.isNull(prepaidListenerOldData.getService()) && Objects.isNull(prepaidListenerOldData.getOperator()) && Objects.isNull(prepaidListenerOldData.getCircle()) &&  Objects.isNull(prepaidListenerOldData.getBillDate()) && Objects.isNull(prepaidListenerOldData.getDueDate()) && Objects.isNull(prepaidListenerOldData.getBillFetchDate()) && Objects.isNull(prepaidListenerOldData.getNextBillFetchDate()) && Objects.isNull(prepaidListenerOldData.getStatus()) && Objects.isNull(prepaidListenerOldData.getIsAutomatic()))
        {
            logger.info("[PrepaidBillsListener.isOldDataUpdated] :: Ignoring because No change in data");
            return false;
        }
        return true;
    }

    private Map<String, String> getDisplayValuesMapFromDCATGetPlanResponse(DCATGetPlanResponse dcatGetPlanResponse) {
        Map<String, String> displayValues = new HashMap<>();
        if (dcatGetPlanResponse.getValidity() != null)
            displayValues.put("validity", dcatGetPlanResponse.getValidity());
        if (dcatGetPlanResponse.getSms() != null)
            displayValues.put("sms", dcatGetPlanResponse.getSms());
        if (dcatGetPlanResponse.getData() != null)
            displayValues.put("data", dcatGetPlanResponse.getData());
        if (dcatGetPlanResponse.getTalktime() != null)
            displayValues.put("talktime", dcatGetPlanResponse.getTalktime());
        if (dcatGetPlanResponse.getAddon_benefit1() != null)
            displayValues.put("addon_benefit1", dcatGetPlanResponse.getAddon_benefit1());
        if (dcatGetPlanResponse.getAddon_benefit2() != null)
            displayValues.put("addon_benefit2", dcatGetPlanResponse.getAddon_benefit2());
        if (dcatGetPlanResponse.getAddon_benefit3() != null)
            displayValues.put("addon_benefit3", dcatGetPlanResponse.getAddon_benefit3());
        if (dcatGetPlanResponse.getAddon_benefit4() != null)
            displayValues.put("addon_benefit4", dcatGetPlanResponse.getAddon_benefit4());
        if (dcatGetPlanResponse.getDescription() != null)
            displayValues.put("description", dcatGetPlanResponse.getDescription());

        return displayValues;
    }

    private  void dropOffHandling(PlanExpiryHistory planExpiryHistory) {
        DropOff dropOff = prepareDropOff(planExpiryHistory);
        try {
            dropOffDBHelper.insertDropOff(dropOff);
            logger.info("[PlanValidityListeners.dropOffhandling] :: Insert into dropOff success for cust_id: {} , rechargeNumber: {}",dropOff.getCustomerId(),dropOff.getRechargeNumber());
        } catch (Exception e) {
            metricsHelper.recordSuccessRate(Constants.PREPAID_BILLS_CONSUMER, Constants.DROP_OFF_INSERTION_ERROR);
            logger.error("[PlanValidityListeners.dropOffhandling] :: failed to insert into dropOff");
        }
    }

    private void addToDropOff(PlanExpiryHistory planExpiryHistory, PrepaidBillsListenerModel prepaidBillsListenerModel){
        try {
            if(prepaidBillsListenerModel.getOperationType().equals(Constants.OP_INSERT) || (prepaidBillsListenerModel.getOperationType().equals(Constants.OP_UPDATE) && Objects.nonNull(prepaidBillsListenerModel.getOld().getDueDate()))){
                List<Recents> recents = recentsRepository.findBycustomerIdAndrechargeNumberAndservice(planExpiryHistory.getCustomerid(), planExpiryHistory.getRechargeNumber(), planExpiryHistory.getService());
                if (!CollectionUtils.isEmpty(recents)) {
                    for (Recents recent : recents) {
                        if (Objects.nonNull(recent.getOrderId())) {
                            dropOffHandling(planExpiryHistory);
                            break;
                        }
                    }
                }
            }
        }
        catch (Exception e){
            logger.error("[PlanValidityListeners.addToDropOff] ::Insert into dropOff for cusomter id {}, recharge number {}, service {}, operator {} failed, Exception : {}",
                    planExpiryHistory.getCustomerid(),planExpiryHistory.getRechargeNumber(),
                    planExpiryHistory.getService(),planExpiryHistory.getOperator(),e);

        }
    }

    private DropOff prepareDropOff(PlanExpiryHistory planExpiryHistory) {
        DropOff dropOff = new DropOff();
        Map<String, String> billsObj = new HashMap<>();
        dropOff.setCustomerId(planExpiryHistory.getCustomerid());
        dropOff.setRechargeNumber(planExpiryHistory.getRechargeNumber());
        dropOff.setService(planExpiryHistory.getService());
        dropOff.setOperator(planExpiryHistory.getOperator());
        dropOff.setCircle(planExpiryHistory.getCircle());
        dropOff.setTransactionTime(planExpiryHistory.getUpdated_at());
        dropOff.setAmount(Constants.DROPOFF_DEFAULT_AMOUNT);
        dropOff.setEventType(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE);
        dropOff.setPaytype(Constants.PREPAID_PAYTYPE);
        dropOff.setStatus(Constants.RECHARGE_SUCCESS_STATUS);
        dropOff.setIn_response_code(Constants.SUCCESS_IN_CODE);
        dropOff.setIsAutomatic(Boolean.TRUE);
        billsObj.put(Constants.PLANVALIDITY_NEW_EXPIRY,Constants.TRUE_STRING);
        billsObj.put(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY,planExpiryHistory.getPlan_bucket());
        dropOff.setBillsObj(billsObj);
        return dropOff;
    }


}
