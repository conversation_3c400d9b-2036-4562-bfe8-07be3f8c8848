package com.paytm.saga.listeners;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.ServiceWrapper;
import com.paytm.saga.common.configuration.CDCReminderKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.cdc.*;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.CDCConsumerUtil;
import com.paytm.saga.util.ServiceWrapperUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.RecentUtils;
import com.paytm.saga.util.ValidationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import com.paytm.saga.producers.CDCRollbackKafkaProducer;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.IGNORE_EVENT;
import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.NEW_EVENT;
import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.CDC_ROLLBACK_EVENT;
import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.NOT_PROCESSING_WHITELISTED;

@Component
@DependsOn("CDCReminderKafkaConfig")
@Conditional(CDCReminderKafkaConsumerEnableCondition.class)
public class CDCReminderKafkaConsumer {
    private CustomLogger logger = CustomLogManager.getLogger(CDCReminderListener.class);

    @Autowired
    private CDCReminderListener cdcReminderListener;

    @Autowired
    private CDCRollbackKafkaProducer cdcRollbackKafkaProducer;

    @Autowired
    private MetricsHelper metricsHelper;

    @Autowired
    protected ServiceConfig serviceConfig;

    @KafkaListener(topics = "#{CDCReminderKafkaConfig.getTopicName_P2P()}",
            groupId = "#{CDCReminderKafkaConfig.getConsumerGroup()}",
            containerFactory = "CDCReminderListenerContainerFactory"
    )
    public void listenP2P(@Payload List<String> messages, Acknowledgment acknowledgment) {


        for (String message : messages) {
            ReminderCDC reminderCDC;
            logger.info("[CDCReminderListener.listenP2p] :: p2p cdc raw kafka packet {}", message);

            if (Objects.nonNull(message)) {
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, NEW_EVENT);
                reminderCDC = JsonUtils.parseJsonWithCustomMapper(message, ReminderCDC.class);
                StringEntity eventSourceEntity = new StringEntity();
                eventSourceEntity.setValue(Constants.EVENTSOURCE_P2P);
                eventSourceEntity.setSet(true);
                eventSourceEntity.setDeletionTs(null);
                reminderCDC.getAfter().setEventSource(eventSourceEntity);
                ValidationUtils.validate(reminderCDC);
                cdcReminderListener.insertDataIntoCassandra(reminderCDC);
            } else {
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, IGNORE_EVENT);
            }
        }
        acknowledgment.acknowledge();
    }

    @KafkaListener(topics = "#{CDCReminderKafkaConfig.getTopicName_SMS()}",
            groupId = "#{CDCReminderKafkaConfig.getConsumerGroup()}",
            containerFactory = "CDCReminderListenerContainerFactory"
    )
    public void listenSms(@Payload List<String> messages, Acknowledgment acknowledgment) {

        for (String message : messages) {
            NonPaytmCDC nonPaytmCDC;
            if(Objects.isNull(message)) {
                logger.info("[CDCReminderListener.listenSms] :: Sms cdc raw kafka packet {}", message);
            }
            if (Objects.nonNull(message)) {
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, NEW_EVENT);
                nonPaytmCDC = JsonUtils.parseJsonWithCustomMapper(message, NonPaytmCDC.class);
                logger.info(ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC),"[CDCReminderListener.listenSms] :: Sms cdc raw kafka packet {}", message);
                try{
                    if(Objects.nonNull(nonPaytmCDC)&&Objects.nonNull(nonPaytmCDC.getAfter()) && Objects.nonNull(nonPaytmCDC.getAfter().getCustomerId())  && RecentUtils.isWhitelistedCustId(nonPaytmCDC.getAfter().getCustomerId().getValue(),serviceConfig)){
                        if(isRedirectionToRollbackTopic()){
                            cdcRollbackKafkaProducer.sendMessage(message,getPartitionKey(nonPaytmCDC));
                            logger.info(ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC), "[CDCReminderListener.listenSms] :: sending to rollback topic {}", message);
                            metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, CDC_ROLLBACK_EVENT);
                        }

                        metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, NOT_PROCESSING_WHITELISTED);
                        logger.info(ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC), "[CDCReminderListener.listenSms] :: not processing whitlisted {}", message);


                        continue;
                    }

                }
                catch(Exception ex){
                    logger.info(ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC), "[CDCReminderLisener.listenSms] : Failed to push to CDC_ROLLBACK topic  {}",ex);
                }


                if(nonPaytmCDC.getAfter().getService().getValue().equalsIgnoreCase(Constants.SERVICE_MOBILE)){
                    try {
                        String rechargeNumber = RecentUtils.getDecryptedRechargeNumber(nonPaytmCDC.getAfter().getRechargeNumber().getValue());
                        nonPaytmCDC.getAfter().getRechargeNumber().setValue(rechargeNumber);
                    } catch (Exception e) {
                        logger.info(ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC), "[CDCReminderLisener.listenSms] : Failed while decryption of recharge number with error {}",e);
                        return;
                    }
                }
                if(nonPaytmCDC.getAfter().getUpdateAt() !=null
                        && ("i".equalsIgnoreCase(nonPaytmCDC.getOp()) || "u".equalsIgnoreCase(nonPaytmCDC.getOp()))){
                    SimpleDateFormat formatter = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);
                    long milliSeconds= nonPaytmCDC.getAfter().getUpdateAt().getValue();
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTimeInMillis(milliSeconds);
                    metricsHelper.recordExecutionTimeOfEvent("CDC_EVENT_LATENCY",(new Date().getTime()-calendar.getTime().getTime()));
                    logger.info(ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC), "test logs customer {} recharge {} service {} eventDate {} currentDate {} diffMillis {}"
                            ,nonPaytmCDC.getAfter().getCustomerId().getValue()
                            ,nonPaytmCDC.getAfter().getRechargeNumber().getValue()
                            ,nonPaytmCDC.getAfter().getService().getValue()
                            ,formatter.format(calendar.getTime())
                            ,formatter.format(new Date())
                            ,(new Date().getTime()-calendar.getTime().getTime()));
                }
                ReminderCDC reminderCDC = CDCConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDC);
                ValidationUtils.validate(reminderCDC);
                cdcReminderListener.insertDataIntoCassandra(reminderCDC);
            } else {
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, IGNORE_EVENT);
            }
        }
        logger.info("sending  ack");
        acknowledgment.acknowledge();
    }

    private String getPartitionKey(NonPaytmCDC nonPaytmCDC){
        StringBuilder partitionKey=new StringBuilder();
        try{
            partitionKey=partitionKey.append(nonPaytmCDC.getAfter().getCustomerId().getValue()).append("_").append(nonPaytmCDC.getAfter().getRechargeNumber().getValue());
        }
        catch(Exception ex){
            logger.error(ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC), "Error occurred while creating partition key ",ex);

        }
        return partitionKey.toString();
    }

    private boolean isRedirectionToRollbackTopic(){
        return serviceConfig.isRedirectionToRollbackTopic();
    }


}
