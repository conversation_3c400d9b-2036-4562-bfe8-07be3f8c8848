package com.paytm.saga.listeners;

import com.newrelic.api.agent.Trace;
import com.paytm.saga.common.configuration.ScratchCardKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.model.ScratchCardHistory;
import com.paytm.saga.dto.ScratchCardResponseModel;
import com.paytm.saga.repository.ScratchCardHistoryRepository;
import com.paytm.saga.util.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.lang.NonNull;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.*;


@Component
@DependsOn("ScratchCardKafkaPropertiesConfig")
@Conditional(ScratchCardKafkaConsumerEnableCondition.class)
public class ScratchCardListeners {
    private final Logger logger = LogManager.getLogger(ScratchCardListeners.class);
    private final ScratchCardHistoryRepository scratchCardHistoryRepository;

    @Autowired
    public ScratchCardListeners(@NonNull ScratchCardHistoryRepository scratchCardHistoryRepository) {
        this.scratchCardHistoryRepository = scratchCardHistoryRepository;
    }

    //read this from bean of kafka config properties
    @KafkaListener(topics = "#{ScratchCardKafkaPropertiesConfig.getTopicName()}",
            groupId = "#{ScratchCardKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory ="ScratchCardkafkaListenerContainerFactory"
    )
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {

        logger.info("[ScratchCardListeners.listen] Size of batch is {}",messages.size());

        for (String message : messages) {
            ScratchCardResponseModel scratchCardResponseModel=null;

            if(Objects.nonNull(message)) {
                scratchCardResponseModel = JsonUtils.parseJson(message, ScratchCardResponseModel.class);
                logger.info("[ScratchCardListeners.listen] parse done");
                insertDataIntoCassandra(scratchCardResponseModel);
            }
        }
        logger.info("[ScratchCardListeners.listen] Acknowledging message");
        acknowledgment.acknowledge();
    }

    @Trace(dispatcher=true)
    public void insertDataIntoCassandra(ScratchCardResponseModel scratchCardData){
        try{
            Long orderId = 1L;
            String status = scratchCardData.getStatus();
            Long scratchCardId = scratchCardData.getScratchCardId();
            String userId = scratchCardData.getUserId();
            Date promoCreatedAt = new Date(scratchCardData.getCreatedAt());
            Date promoUpdatedAt = new Date(scratchCardData.getUpdatedAt());
            Date createdAt = new Date();
            String txnSource = scratchCardData.getTxnSource();
            boolean deleted = scratchCardData.isDeleted();

            if(status==null){
                return;
            }

            if(isSkippable(scratchCardData)){
                return;
            }
            if(isDeletable(scratchCardData)){
                deleteDataFromCassandra(scratchCardData);
                return;
            }

            try{
                orderId = Long.parseLong(scratchCardData.getOrderId());
            } catch (Exception e){
                logger.error("[ScratchCardListeners.insertDataIntoCassandra]: Long parse exception", e);
            }

            try {
                scratchCardHistoryRepository.save(new ScratchCardHistory(orderId, status, scratchCardId, userId, createdAt, promoCreatedAt, promoUpdatedAt, deleted));
                logger.info("[ScratchCardListeners.insertDataIntoCassandra]: Insert Success , :" + orderId);
            } catch (Exception e){
                logger.error("[ScratchCardListeners.insertDataIntoCassandra]: Insert Exception", e);
            }

        }
        catch(Exception e){
            logger.error("[ScratchCardListeners.insertDataIntoCassandra] Exception", e);
        }
    }

    public Boolean isSkippable(ScratchCardResponseModel scratchCardData){
        if(scratchCardData!=null){
            if (Arrays.asList(Constants.CommonConstants.SCRATCH_CARD_ALLOWED_SOURCE).contains(scratchCardData.getTxnSource())) {

                if(Arrays.asList(Constants.CommonConstants.SCRATCH_CARD_SKIP_STATUS).contains(scratchCardData.getStatus())){
                    return true;
                }

                HashSet<Integer> FS_VERTICAL_IDS = new HashSet<>();
                FS_VERTICAL_IDS.addAll(Arrays.asList(Constants.CommonConstants.FS_VERTICAL_IDS));
                List<Integer> verticalIds = scratchCardData.getTxnInfo().getVertical_id();

                FS_VERTICAL_IDS.retainAll(verticalIds);
                if(FS_VERTICAL_IDS.isEmpty()){
                    logger.info("[ScratchCardListeners.insertDataIntoCassandra] skipping record "+ scratchCardData.getTxnSource() + scratchCardData.getTxnInfo() + scratchCardData.getScratchCardId());
                    return true;
                } else {
                    return false;
                }
            } else{
                return true;
            }
        } else{
            return true;
        }
    }

    public Boolean isDeletable(ScratchCardResponseModel scratchCardData){
        if(scratchCardData!=null){
            if (Arrays.asList(Constants.CommonConstants.SCRATCH_CARD_TRIGGER_DELETE_STATUS).contains(scratchCardData.getStatus())) {
                return true;
            }
        }
        return false;
    }


    private void deleteDataFromCassandra(ScratchCardResponseModel scratchCardData){
        Long orderId = null;
        Long scratchCardId = scratchCardData.getScratchCardId();

        try{
            orderId = Long.parseLong(scratchCardData.getOrderId());
        } catch (Exception e){
            logger.error("[ScratchCardListeners.deleteDataFromCassandra]: Long parse exception", e);
        }
        if(orderId!=null && scratchCardId!=null){
            logger.info("[ScratchCardListeners.deleteDataFromCassandra] Deleting : " + orderId + " " + scratchCardId);
            scratchCardHistoryRepository.deleteScratchCardsBasedOnOrderIdScratchCardId(orderId, scratchCardId);
        }

    }

}


