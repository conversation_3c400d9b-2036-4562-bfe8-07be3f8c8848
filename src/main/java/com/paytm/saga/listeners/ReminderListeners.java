package com.paytm.saga.listeners;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.newrelic.api.agent.Trace;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.ServiceWrapper;
import com.paytm.saga.common.configuration.ReminderKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.exception.ReminderListenerException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.CustomerBillDao;
import com.paytm.saga.dto.ExtraInfo;
import com.paytm.saga.dto.ReminderCustomerOtherInfo;
import com.paytm.saga.dto.ReminderDataResponseModel;
import com.paytm.saga.dto.ReminderResponseModel;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.enums.Service;
import com.paytm.saga.model.*;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.CustomerBillPrimaryKey;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.repository.ChannelHistoryRepository;
import com.paytm.saga.service.*;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.ServiceWrapperUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.RecentUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.util.Pair;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.lang.NonNull;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.*;
import static com.paytm.saga.common.constant.Constants.ReminderConstants.SYNC_BILL_AUTOMATIC_EVENT;

@Component
@DependsOn("ReminderKafkaPropertiesConfig")
@Conditional(ReminderKafkaConsumerEnableCondition.class)
public class ReminderListeners {
    private final CustomLogger logger = CustomLogManager.getLogger(ReminderListeners.class);

    private final ReminderHistoryRepositoryWrapperService reminderHistoryRepository;
    private final ChannelHistoryRepository channelHistoryRepository;
    private final ChannelHistoryService channelHistoryService;
    private final MetricsHelper metricsHelper;
    private final RecentsRepositoryWrapperService recentsRepository;
    private CustomerBillRepository customerBillRepository;
    private final DropOffDBHelper dropOffDBHelper;
    private final RecentService recentService;
    private final CustomerBillDao customerBillDao;


    @Autowired
    @Qualifier("monitoringClient")
    private StatsDClient monitoringClient;

    @Autowired
    public ServiceConfig serviceConfig;


    private static final String IGNORE_EVENT="IGNORE";
    private static final String ERROR_EVENT="ERROR";
    private static final String REMINDER_AUTOMATIC_ONLY="REMINDER_AUTOMATIC_ONLY";
    private static final String RECENT_AUTOMATIC_ONLY="RECENT_AUTOMATIC_ONLY";
    private static final String RECENT_CONSENT_VALID_TILL_ONLY="RECENT_CONSENT_VALID_TILL_ONLY";

    private static final String NOT_PAID_ON_PAYTM_AMOUNT_NULL="NOT_PAID_ON_PAYTM_AMOUNT_NULL";
    private static final String NEW_EVENT="NEW_KAFKA_EVENT";
    private static final String REMINDER_CONSUMER = "REMINDER_CONSUMER";
    private static final String PAYMENT_DATE_NULL="PAYMENT_DATE_NULL";
    private static final String RECO_REFRESH_ERROR_EVENT="RECO_REFRESH_ERROR_EVENT";
    private static final String NOTIFICATION_STATUS_ONLY = "NOTIFICATION_STATUS_ONLY";



    private KafkaProducerService kafkaProducerService;

    private CommonService commonService;

    private ReminderResponseModelEncryptionHandler encryptionHandlerReminderResponseModel;

//    private CVRProductCache cvrProductCache;



    @Autowired
    public ReminderListeners(@NonNull ReminderHistoryRepositoryWrapperService reminderHistoryRepository,
                             @NonNull ChannelHistoryRepository channelHistoryRepository, ChannelHistoryService channelHistoryService, @NonNull MetricsHelper metricsHelper, @NonNull RecentsRepositoryWrapperService recentsRepository, @NonNull CustomerBillRepository customerBillRepository, DropOffDBHelper dropOffDBHelper, RecentService recentService, @NonNull KafkaProducerService kafkaProducerService, @NonNull CommonService commonService, @NonNull ServiceConfig serviceConfig, @NonNull ReminderResponseModelEncryptionHandler encryptionHandlerReminderResponseModel,@NonNull CustomerBillDao customerBillDao) {
        this.reminderHistoryRepository = reminderHistoryRepository;
        this.channelHistoryRepository = channelHistoryRepository;
        this.channelHistoryService = channelHistoryService;
        this.metricsHelper = metricsHelper;
        this.recentsRepository = recentsRepository;
        this.customerBillRepository = customerBillRepository;
        this.dropOffDBHelper = dropOffDBHelper;
        this.recentService = recentService;
        this.customerBillDao = customerBillDao;
        this.kafkaProducerService = kafkaProducerService;
        this.commonService = commonService;
        this.serviceConfig = serviceConfig;
        this.encryptionHandlerReminderResponseModel = encryptionHandlerReminderResponseModel;
//        this.cvrProductCache = cvrProductCache;
    }
    //read this from bean of kafka config properties
    @KafkaListener(topics = "#{ReminderKafkaPropertiesConfig.getTopicName()}",
            groupId = "#{ReminderKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory ="ReminderkafkaListenerContainerFactory"
    )
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {

        for (String message : messages) {
            ServiceWrapper serviceWrapper = null;
            Long startTime = System.currentTimeMillis();
            String logStatus = "success";
            try {
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, NEW_EVENT);
                ReminderResponseModel reminderResponseModel = null;
                if (Objects.nonNull(message)) {
                    reminderResponseModel = JsonUtils.parseJson(message, ReminderResponseModel.class);
                    //find service from reminderResponseModel
                    serviceWrapper = ServiceWrapperUtil.findServiceWrapper(reminderResponseModel);
                    if(Objects.nonNull(reminderResponseModel)) {
                        logger.info(serviceWrapper, "[ReminderListeners.listen] :: listen :: customer id: {} :: raw kafka packet {}", reminderResponseModel.getData().getCustomer_id(), message);
                    } else{
                        logger.info("[ReminderListeners.listen] :: listen :: raw kafka packet {}", message);
                    }
                    if(Objects.nonNull(reminderResponseModel) && Objects.nonNull(reminderResponseModel.getData()) && Constants.FINANCIAL_SERVICES.equalsIgnoreCase(reminderResponseModel.getData().getService())) {
                        boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(reminderResponseModel.getData().getCustomer_id());
                        if(isCCEncryptionRequired) {
                            reminderResponseModel = encryptionHandlerReminderResponseModel.decryptReminderResponseModel(reminderResponseModel);
                        }
                    }

                    // Set the subscriptionIsAutomatic to sync in extra column
                    if( reminderResponseModel != null && reminderResponseModel.getData() != null && reminderResponseModel.getData().getIs_automatic() != null){
                        reminderResponseModel.getData().setSubscriptionIsAutomatic(reminderResponseModel.getData().getIs_automatic());
                    }
                    if (reminderResponseModel != null && reminderResponseModel.getData() != null && reminderResponseModel.getData().getIs_automatic() != null && reminderResponseModel.getData().getIs_automatic() == 3) {
                        reminderResponseModel.getData().setIs_automatic(1);
                    }
                    // As we do not update data in case of null, but due_date,amount and minDueAmount can be null already in DB in any partial sms case so this check is added.
                    Boolean partialSms = getPartialSMSFlag(reminderResponseModel, message);
                    if (partialSms != null && partialSms) {
                        logger.info(serviceWrapper, "partialSms = {}", partialSms);
                    }
                    insertDataIntoCassandra(reminderResponseModel, partialSms);
                } else {
                    logger.info("reminder listener raw kafka packet {}", message);
                    metricsHelper.recordSuccessRate(REMINDER_CONSUMER, IGNORE_EVENT);
                    logStatus = "ignore";
                }
            } catch (Exception e){
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
                logStatus = "exception";
                logger.error(serviceWrapper, "[ReminderListeners.listen] Exception occurred for message {}, Exception {}", message, e);
            }
            Long endTime = System.currentTimeMillis();
            logger.info("reminder listener message processed with status {} in ms {}", logStatus, (endTime-startTime));
        }
        acknowledgment.acknowledge();
    }


    public Boolean getPartialSMSFlag(ReminderResponseModel reminderResponseModel, String message) {
        try{
            if (reminderResponseModel == null || Objects.isNull(reminderResponseModel.getOld()) || Objects.isNull(reminderResponseModel.getData()))
                return false;
            String customerOtherInfoNew=reminderResponseModel.getData().getCustomerOtherInfo();
            ReminderCustomerOtherInfo reminderCustomerOtherInfoNew = null;
            if (customerOtherInfoNew != null) {
                reminderCustomerOtherInfoNew= JsonUtils.parseJson(customerOtherInfoNew, ReminderCustomerOtherInfo.class);
            }
            String customerOtherInfoOld=reminderResponseModel.getOld().getCustomerOtherInfo();
            ReminderCustomerOtherInfo reminderCustomerOtherInfoOld = null;
            if(customerOtherInfoOld != null) {
                reminderCustomerOtherInfoOld= JsonUtils.parseJson(customerOtherInfoOld, ReminderCustomerOtherInfo.class);
            }
            if(!StringUtils.isEmpty(reminderResponseModel.getData().getExtra())) {
                Map<String, String> extraInfo = JsonUtils.parseJson(reminderResponseModel.getData().getExtra(), Map.class);
                JSONObject messageJsonObject = new JSONObject(message);
                return extraInfo != null && Constants.BILL_TYPE_SMS.equalsIgnoreCase(extraInfo.get(Constants.UPDATED_SOURCE)) && (messageJsonObject.getJSONObject("old").has("amount") || (messageJsonObject.getJSONObject("old").has("customerOtherInfo") && reminderCustomerOtherInfoNew != null && reminderCustomerOtherInfoOld != null && !Objects.equals(reminderCustomerOtherInfoNew.getCurrentMinBillAmount(), reminderCustomerOtherInfoOld.getCurrentMinBillAmount())));
            }
        } catch (Exception e){
            logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.getPartialSMSFlag] Exception occurred for message {}, Exception {}", message, e);
            return false;
        }
        return false;
    }

    public void updateAmountForServiceAndOperator(ReminderResponseModel reminderResponseModel) {
        if (reminderResponseModel == null || reminderResponseModel.getData() == null) {
            logger.warn(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel),"[ReminderListeners.updateAmountForServiceAndOperator]  empty data in reminder payload  , : {}", reminderResponseModel);
            return;
        }
        if (Objects.isNull(reminderResponseModel.getData().getAmount()) ) {
            if(Constants.CommonConstants.DTH_SERVICE.equals(reminderResponseModel.getData().getService()) && Constants.SUNTV.equals(reminderResponseModel.getData().getOperator())){
                Long productId = reminderResponseModel.getData().getProduct_id();
                if(productId == null){
                    metricsHelper.recordSuccessRate(REMINDER_CONSUMER, Constants.ReminderConstants.PRODUCT_ID_NULL);
                    return;
                }
                Double amount = CVRProductCache.getInstance().getProductDetails(productId).getMinAmount();
                if(amount == null){
                    metricsHelper.recordSuccessRate(REMINDER_CONSUMER, Constants.ReminderConstants.CVR_AMOUNT_NULL);
                    return;
                }
                reminderResponseModel.getData().setAmount(amount);
            }
        }
    }

    @Trace(dispatcher=true)
    public void  insertDataIntoCassandra(ReminderResponseModel reminderResponseModel, Boolean partialSms){
        try{
            if(reminderResponseModel == null || reminderResponseModel.getData() == null){
                logger.warn(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel),"[ReminderListeners.insertDataIntoCassandra]  empty data in reminder payload  , : {}" , reminderResponseModel);
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, IGNORE_EVENT);
                return;
            }
            if(Objects.nonNull(reminderResponseModel.getData().getDataSource())){
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra] data source is non-null in reminder payload  , : {}" , reminderResponseModel);
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, IGNORE_EVENT);
                return;
            }
            updateAmountForServiceAndOperator(reminderResponseModel);

            if(isSkipable(reminderResponseModel, partialSms).equals(true)) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra] ::  skipped reminder payload, but going to updateRecentsAutomaticState , updateReminderHistoryAutomatic and checkForNotificationStatus");
                updateRecentsAutomaticState(reminderResponseModel);
                updateReminderHistoryAutomatic(reminderResponseModel);
                checkForNotificationStatus(reminderResponseModel);
                updateRecentsConsentValidTill(reminderResponseModel);
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, IGNORE_EVENT);
                return;
            }

            if (reminderResponseModel.getData().getPaytype() != null
                && reminderResponseModel.getData().getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
                String mcn = reminderResponseModel.getData().getRecharge_number();
                reminderResponseModel.getData().setRecharge_number(mcn);
            }

            String customerOtherInfo=reminderResponseModel.getData().getCustomerOtherInfo();
            if(customerOtherInfo !=null) {
                ReminderCustomerOtherInfo reminderCustomerOtherInfo= JsonUtils.parseJson(customerOtherInfo, ReminderCustomerOtherInfo.class);
                if(reminderCustomerOtherInfo !=null) {
                    reminderResponseModel.getData().setCurrentMinBillAmount(reminderCustomerOtherInfo.getCurrentMinBillAmount());
                    reminderResponseModel.getData().setEarlyPaymentDate(reminderCustomerOtherInfo.getEarlyPaymentDate());
                    reminderResponseModel.getData().setCurrentOutstandingAmount(reminderCustomerOtherInfo.getCurrentOutstandingAmount());
                    if(reminderCustomerOtherInfo.getAdditionalFees() != null) {
                        reminderResponseModel.getData().setEarlyPaymentAmount(reminderCustomerOtherInfo.getAdditionalFees().getEarlyPaymentAmount());
                    }
                    else {
                        reminderResponseModel.getData().setEarlyPaymentAmount(null);
                    }
                    if(StringUtils.isNotEmpty(reminderCustomerOtherInfo.getConsumerName()) && Objects.nonNull(reminderCustomerOtherInfo.getConsumerName())) {
                        reminderResponseModel.getData().setConsumerName(reminderCustomerOtherInfo.getConsumerName());
                    }
                    else {
                        reminderResponseModel.getData().setConsumerName(null);
                    }
                }
            }
            String extraInfo=reminderResponseModel.getData().getExtra();
            if(extraInfo !=null) {
                ExtraInfo extraInfo1=null;
                try {
                     extraInfo1 = JsonUtils.parseJson(extraInfo, ExtraInfo.class);
                }catch(Exception e) {
                    logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "Exception: in parsing extraInfo {} error {}",extraInfo, e);
                }
                if(extraInfo1 !=null) {
                    reminderResponseModel.getData().setLastPaidAmount(extraInfo1.getLast_paid_amount());
                }
                Map<String,String> extras = null;
                try {
                    extras = JsonUtils.parseJson(extraInfo, Map.class);
                }
                catch (Exception e){
                    logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra] Unable to parse extras due to, error ",e);
                }
                if(extras != null && extras.containsKey("recon_id")){
                    reminderResponseModel.getData().setRecondId(extras.get("recon_id"));
                }
                if(extras !=null && extras.containsKey("skin_url") && extras.containsKey("skin_source")){
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("url", extras.get("skin_url"));
                    jsonObject.put("source", extras.get("skin_source"));
                    reminderResponseModel.getData().setCardSkin(jsonObject.toString());
                }
                if(extras!=null && extras.containsKey("issuingBankCardVariant")){
                    reminderResponseModel.getData().setCardVariant(extras.get("issuingBankCardVariant"));
                }
            }

            if(reminderResponseModel.getOld()!=null && reminderResponseModel.getOld().getCustomerOtherInfo() !=null) {
            	customerOtherInfo=reminderResponseModel.getOld().getCustomerOtherInfo();
            	ReminderCustomerOtherInfo reminderCustomerOtherInfoOld= JsonUtils.parseJson(customerOtherInfo, ReminderCustomerOtherInfo.class);
            	if(reminderCustomerOtherInfoOld !=null) {
                	reminderResponseModel.getOld().setCurrentMinBillAmount(reminderCustomerOtherInfoOld.getCurrentMinBillAmount());
                    reminderResponseModel.getOld().setCurrentOutstandingAmount(reminderCustomerOtherInfoOld.getCurrentOutstandingAmount());
            	}
            }

            ReminderHistory reminderHistory = prepareReminderData(reminderResponseModel);
            Boolean is_new_biller = null ;
            if(Service.TUITION_FEES.value.equalsIgnoreCase(reminderResponseModel.getData().getService())){
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "TUITION_FEES customer id ={}  ,recharge number 1 = {}",reminderResponseModel.getData().getCustomer_id(),reminderResponseModel.getData().getRecharge_number());
                List<Recents> rentRecents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(reminderResponseModel.getData().getCustomer_id(), reminderResponseModel.getData().getService(),reminderResponseModel.getData().getRecharge_number());
                if(!CollectionUtils.isEmpty(rentRecents)){
                    rentRecents = rentRecents.stream()
                            .sorted((o1, o2) -> o2.getUpdatedAt().compareTo(o1.getUpdatedAt()))
                            .collect(Collectors.toList());
                    reminderHistory.setOperator(rentRecents.get(0).getKey().getOperator());
                    reminderHistory.setProductId(rentRecents.get(0).getProductId());
                }
            }
            String tempRechargeNumber1 = null;
            List<Recents> rentRecents = null;
            boolean isBillerFound = false;
            String recharge_number_7 = null ;
            String recharge_number_2 = null;
            if (Service.RENT_PAYMENT.value.equalsIgnoreCase(reminderResponseModel.getData().getService()) || Service.BUSINESS_PAYMENT.value.equalsIgnoreCase(reminderResponseModel.getData().getService())) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "RENT_PAYMENT customer id ={}  ,recharge number 1 = {}",reminderResponseModel.getData().getCustomer_id(),reminderResponseModel.getData().getRecharge_number());
                tempRechargeNumber1 = reminderHistory.getRechargeNumber();
                    try {
                        rentRecents=recentsRepository.findByCustomerIdAndService(reminderResponseModel.getData().getCustomer_id(), reminderResponseModel.getData().getService());
                    }
                    catch(Exception ex) {
                        logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.findByCustomerIdAndService] recents find for cusomter id {}, recharge number {}, service {}, operator {}, Exception");
                    }
                    recharge_number_7 = this.getUserData(reminderHistory).getRechargeNumber7();
                    recharge_number_2 = this.getUserData(reminderHistory).getRechargeNumber2();
                    if (!CollectionUtils.isEmpty(rentRecents)) {

                        rentRecents = rentRecents.stream()
                                .sorted((o1, o2) -> {
                                    if (o1.getUpdatedAt() == null && o2.getUpdatedAt() == null) {
                                        return 0;
                                    } else if (o1.getUpdatedAt() == null) {
                                        return 1;
                                    } else if (o2.getUpdatedAt() == null) {
                                        return -1;
                                    } else {

                                        return o2.getUpdatedAt().compareTo(o1.getUpdatedAt());
                                    }
                                })
                                .collect(Collectors.toList());

                        for (Recents recent : rentRecents) {
                            if (StringUtils.isEmpty(recharge_number_7) && Objects.nonNull(recent.getIsNewBiller()) && Boolean.TRUE.equals(recent.getIsNewBiller()) && !StringUtils.isEmpty(recent.getRechargeNumber7()) && recent.getRechargeNumber7().equals(tempRechargeNumber1) && recent.getRechargeNumber2().equals(recharge_number_2)) {
                                reminderHistory.setOperator(recent.getKey().getOperator());
                                reminderHistory.setProductId(recent.getProductId());
                                reminderHistory.setRechargeNumber(recent.getKey().getRechargeNumber());
                                is_new_biller = true;
                                isBillerFound = true;
                                break;

                            }
                            if (StringUtils.isEmpty(recharge_number_7) && (Objects.isNull(recent.getIsNewBiller()) || Boolean.FALSE.equals(recent.getIsNewBiller())) && !StringUtils.isEmpty(recent.getRechargeNumber7()) && recent.getKey().getRechargeNumber().equals(tempRechargeNumber1) && recent.getRechargeNumber2().equals(recharge_number_2)) {

                                reminderHistory.setOperator(recent.getKey().getOperator());
                                reminderHistory.setProductId(recent.getProductId());
                                reminderHistory.setRechargeNumber(recent.getRechargeNumber7());
                                is_new_biller = true;
                                isBillerFound = true;
                                break;


                            }


                            if (!StringUtils.isEmpty(recharge_number_7) && Objects.nonNull(recent.getIsNewBiller()) && Boolean.TRUE.equals(recent.getIsNewBiller()) && !StringUtils.isEmpty(recent.getKey().getRechargeNumber()) && recent.getKey().getRechargeNumber().equals(recharge_number_7)) {
                                reminderHistory.setOperator(recent.getKey().getOperator());
                                reminderHistory.setProductId(recent.getProductId());
                                reminderHistory.setRechargeNumber(recent.getKey().getRechargeNumber());
                                is_new_biller = true;
                                isBillerFound = true;
                                break;
                            }

                            if (!StringUtils.isEmpty(recharge_number_7) && Objects.nonNull(recent.getIsNewBiller()) && Boolean.TRUE.equals(recent.getIsNewBiller()) && recent.getKey().getRechargeNumber().equals(recharge_number_7)) {

                                reminderHistory.setOperator(recent.getKey().getOperator());
                                reminderHistory.setProductId(recent.getProductId());
                                reminderHistory.setRechargeNumber(recent.getRechargeNumber7());
                                is_new_biller = true;
                                isBillerFound = true;
                                break;


                            }

                            if (StringUtils.isEmpty(recharge_number_7) && (Objects.isNull(recent.getIsNewBiller()) || Boolean.FALSE.equals(recent.getIsNewBiller())) && recent.getKey().getRechargeNumber().equals(tempRechargeNumber1) && recent.getRechargeNumber2().equals(recharge_number_2)) {
                                if (!StringUtils.isEmpty(recent.getRechargeNumber7())) {
                                    reminderHistory.setOperator(recent.getKey().getOperator());
                                    reminderHistory.setProductId(recent.getProductId());
                                    reminderHistory.setRechargeNumber(recent.getRechargeNumber7());
                                    is_new_biller = true;
                                    isBillerFound = true;
                                    break;
                                }


                            }
                            if (!StringUtils.isEmpty(recharge_number_7) && (Objects.isNull(recent.getIsNewBiller()) || Boolean.FALSE.equals(recent.getIsNewBiller())) && !StringUtils.isEmpty(recent.getRechargeNumber7()) && recent.getRechargeNumber7().equals(recharge_number_7)) {
                                reminderHistory.setOperator(recent.getKey().getOperator());
                                reminderHistory.setProductId(recent.getProductId());
                                reminderHistory.setRechargeNumber(recent.getRechargeNumber7());
                                is_new_biller = true;
                                isBillerFound = true;
                                break;
                            }


                            if (!StringUtils.isEmpty(recharge_number_7) && (Objects.isNull(recent.getIsNewBiller()) || Boolean.FALSE.equals(recent.getIsNewBiller())) && StringUtils.isEmpty(recent.getRechargeNumber7()) && recent.getKey().getRechargeNumber().equals(tempRechargeNumber1) && recent.getRechargeNumber2().equals(recharge_number_2)) {

                                reminderHistory.setOperator(recent.getKey().getOperator());
                                reminderHistory.setProductId(recent.getProductId());
                                reminderHistory.setRechargeNumber(recharge_number_7);
                                is_new_biller = true;
                                isBillerFound = true;
                                break;
                            }


                            if (!StringUtils.isEmpty(recharge_number_7) && Objects.nonNull(recent.getIsNewBiller()) && Boolean.TRUE.equals(recent.getIsNewBiller()) && recent.getKey().getRechargeNumber().equals(recharge_number_7)) {

                                reminderHistory.setOperator(recent.getKey().getOperator());
                                reminderHistory.setProductId(recent.getProductId());
                                reminderHistory.setRechargeNumber(recharge_number_7);
                                is_new_biller = true;
                                isBillerFound = true;
                                break;
                            }
                            if (!StringUtils.isEmpty(recharge_number_7) && (Objects.isNull(recent.getIsNewBiller()) || Boolean.FALSE.equals(recent.getIsNewBiller())) && recent.getKey().getRechargeNumber().equals(tempRechargeNumber1) && recent.getRechargeNumber2().equals(recharge_number_2)) {
                                reminderHistory.setOperator(recent.getKey().getOperator());
                                reminderHistory.setProductId(recent.getProductId());
                                reminderHistory.setRechargeNumber(recharge_number_7);
                                is_new_biller = true;
                                isBillerFound = true;
                                break;

                            }

                        }

                    }
                    if (!isBillerFound && !StringUtils.isEmpty(tempRechargeNumber1) && !StringUtils.isEmpty(recharge_number_7)) {
                        reminderHistory.setRechargeNumber(recharge_number_7);
                    }
            }




            Integer TTL = getTTL(reminderHistory, reminderResponseModel);
            try {
                if(reminderHistory.getAmount() != null) {
                    reminderHistoryRepository.save(reminderHistory, TTL);
                }
            } catch (Exception e){
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
            	logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "ReminderHistory insert for cusomter id {}, recharge number {}, service {}, operator {}, Exception",
                		reminderHistory.getCustomerId(),reminderHistory.getRechargeNumber(),
                		reminderHistory.getService(),reminderHistory.getOperator(),e);
            }
            if(isNotPaidOnPaytm(reminderResponseModel)){
                ChannelHistory channelHistory = prepareChannelData(reminderHistory, reminderResponseModel);
                if (channelHistory != null) {
                    try {
                        channelHistoryService.save(channelHistory, reminderResponseModel.getData().getService());
                    } catch (Exception e) {
                        metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
                        logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "ChannelHistory insert for cusomter id {}, recharge number {}, service {}, operator {}, Exception",
                                reminderHistory.getCustomerId(), reminderHistory.getRechargeNumber(),
                                reminderHistory.getService(), reminderHistory.getOperator(), e);
                    }
                }
            }

            try {
                if (isEligibleForCustomerBill(reminderHistory, reminderResponseModel)) {
                    CustomerBill reminderClusterKey = prepareReminderClusterData(reminderHistory);
                    Integer ttl = commonService.getSmartReminderTTL(reminderClusterKey);
                    if (ttl > 0) {
                        customerBillDao.save(reminderClusterKey, ttl);

                        logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "After saving into customerBill {} {} ", reminderClusterKey.getKey().getCustomerId(), ttl);
                    } else {
                        logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "Ignoring ttl smaller than 0  {}", reminderResponseModel);
                    }
                }
            } catch (Exception e) {
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra] ReminderClusterKey insert for cusomter id {}, recharge number {}, service {}, operator {}, Exception",
                        reminderHistory.getCustomerId(), reminderHistory.getRechargeNumber(),
                        reminderHistory.getService(), reminderHistory.getOperator(), e);
            }

            try {
                Integer notificationStatus = reminderResponseModel.getData().getNotificationStatus();

                //record not found
                //record does not match
                //incoming payload not have rec 7
                //record recharge number set

                Recents recents = prepareRecentsData(reminderHistory, reminderResponseModel);
                if(Service.RENT_PAYMENT.value.equalsIgnoreCase(reminderResponseModel.getData().getService()) || Service.BUSINESS_PAYMENT.value.equalsIgnoreCase(reminderResponseModel.getData().getService())){
                    if( !isBillerFound && StringUtils.isEmpty(recharge_number_7) && !StringUtils.isEmpty(tempRechargeNumber1)){

                        recents.setRechargeNumber7(null);
                    }else{
                        recents.setRechargeNumber7(tempRechargeNumber1);
                    }
                }



                //run upsert query instead of save
                //recentsRepository.save(recents);
                recentService.updateRecentBillFromReminderListener(recents.getKey().getCustomerId(), recents.getKey().getService(), recents.getKey().getOperator(), recents.getKey().getPlanBucket(), recents.getCin(),
                        recents.getPar(), recents.getTin(), recents.getIsTokenizedTransaction(), recents.getBillDate(), recents.getDueDate(), recents.getDueAmount(),
                        recents.getMinDueAmount(), recents.getOriginalDueAmount(), recents.getOriginalMinDueAmount(), recents.getProductId(),
                        recents.getAutomaticStatus(), recents.getBillUpdateTime(), recents.getIsMarkAsPaid(), recents.getMarkAsPaidTime(), recents.getMarkAsPaidAmount(),
                        recents.getUpdatedAt(), recents.getKey().getRechargeNumber(), recents.getRechargeNumber2(), recents.getRechargeNumber3(),
                        recents.getRechargeNumber4(), recents.getRechargeNumber5(), recents.getRechargeNumber6(), recents.getRechargeNumber7(),
                        recents.getRechargeNumber8(), recents.getCircle(), recents.getPayType(), recents.getMcn(), notificationStatus, recents.getNotPaidOnPaytm(), recents.getTxnTime(), recents.getTxnAmount(), recents.getNewBillUpdatedAt(), recents.getIsSavedCard(), recents.getIsValidation(), recents.getIsTransaction(), recents.getEventSource(), reminderResponseModel,is_new_biller, recents.getEarlyPaymentDate(), recents.getEarlyPaymentAmount(), recents.getPgCardId(),recents.getReconId(), recents.getNextBillFetchDateFlag(),recents.getReminderStatus(),recents.getOldBillFetchDate(), recents.getCardVariant(), recents.getCardSkin(), recents.getExtra(),recents.getRemindLaterDate(), recents.getCurrentOutstandingAmount(), recents.getConsentValidTill());
                recentService.checkAndDeleteLoanRecentDummyRecords(recents);
            } catch (Exception e) {
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra] recents upsert for cusomter id {}, recharge number {}, service {}, operator {}, Exception",
                        reminderHistory.getCustomerId(), reminderHistory.getRechargeNumber(),
                        reminderHistory.getService(), reminderHistory.getOperator(), e);
            }
            if (Objects.nonNull(reminderResponseModel.getOld()) && Objects.nonNull(reminderResponseModel.getOld().getDue_date())) {
                List<Recents> recents = recentsRepository.findBycustomerIdAndrechargeNumberAndservice(reminderHistory.getCustomerId(), reminderHistory.getRechargeNumber(), reminderHistory.getService());
                if (Objects.nonNull(recents) && recents.size() != 0) {
                    for (Recents recent : recents) {
                        if (Objects.nonNull(recent.getOrderId())) {
                            dropOffHandling(reminderHistory);
                            break;
                        }
                    }
                }
            }
            try {
                kafkaProducerService.sendMessage(Long.toString(reminderResponseModel.getData().getCustomer_id()));
            }
            catch (Exception e){
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECO_REFRESH_ERROR_EVENT);
            	logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra] kafka cache clean event pushlish error for cusomter id {}, recharge number {}, service {}, operator {}, Exception",
                		reminderHistory.getCustomerId(),reminderHistory.getRechargeNumber(),
                		reminderHistory.getService(),reminderHistory.getOperator(),e);
            }
        }
        catch(Exception e){
            metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
            logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra] insertDataIntoCassandra error for data {} Exception",reminderResponseModel, e);
        }
    }

    private String getReminderPlanBucket(String operator) {
        if(operator != null && (Constants.REMINDER_PLAN_BUCKET_OPERATOR.stream().anyMatch(s -> s.equalsIgnoreCase(operator)))) {
            return Constants.DTH_PLAN_BUCKET;
        }
        return "";
    }

    public Recents prepareRecentsData(ReminderHistory reminderHistory, ReminderResponseModel reminderResponseModel){
        try {
            Date bill_update_time = new Date();
            Recents recents = null;
            recents = this.getUserData(reminderHistory);
            if(recents == null) {
                recents = new Recents();
            }
            recents.setKey(new RecentsPrimaryKey());
            recents.getKey().setRechargeNumber(reminderHistory.getRechargeNumber());
            recents.getKey().setService(reminderHistory.getService());
            recents.getKey().setOperator(reminderHistory.getOperator());
            recents.setPayType(reminderHistory.getPaytype());
            recents.getKey().setCustomerId(reminderHistory.getCustomerId());
            recents.setTin(reminderHistory.getTin());
            recents.setCin(reminderHistory.getReference_id());
            recents.setPar(reminderHistory.getPar());
            recents.setDueDate(reminderHistory.getDue_date());
            recents.setBillDate(reminderHistory.getBill_date());
            recents.setDueAmount(reminderHistory.getAmount());
            recents.setMinDueAmount(reminderHistory.getCurrentMinBillAmount());
            if(Constants.FINANCIAL_SERVICES.equalsIgnoreCase(recents.getKey().getService())) {
                recents.setCurrentOutstandingAmount(reminderResponseModel.getData().getCurrentOutstandingAmount());
            }
            recents.setOriginalDueAmount(reminderHistory.getOriginalAmount());
            recents.setOriginalMinDueAmount(reminderHistory.getOriginalMinBillAmount());
            recents.setAutomaticStatus(reminderHistory.getIs_automatic());
            recents.getKey().setPlanBucket(getReminderPlanBucket(reminderHistory.getOperator()));
            recents.setBillUpdateTime(bill_update_time);
            recents.setEarlyPaymentDate(reminderHistory.getEarlyPaymentDate());
            recents.setEarlyPaymentAmount(reminderHistory.getEarlyPaymentAmount());
            recents.setReminderStatus(reminderResponseModel.getData().getStatus());
            Date remindMeLaterDate;

            try{
                SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                sdf.setLenient(false);
                if(Objects.nonNull(reminderResponseModel.getData().getRemindLaterDate())) {
                    remindMeLaterDate = sdf.parse(reminderResponseModel.getData().getRemindLaterDate());
                    recents.setRemindLaterDate(remindMeLaterDate);
                }
            } catch (Exception e) { logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra]  remindMeLaterDate::Exception", e); }

            Map<String,String> extra=null;
            if(Objects.nonNull(reminderResponseModel.getData()) && Objects.nonNull(reminderResponseModel.getData().getExtra()) && !StringUtils.isEmpty(reminderResponseModel.getData().getExtra())) {
                try {
                    extra = JsonUtils.parseJson(reminderResponseModel.getData().getExtra(), Map.class);
                }
                catch (Exception e){
                    logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.prepareRecentsData] Unable to parse extra due to, error ",e);
                }
            }
            JSONObject extraObject = new JSONObject();
            if(Objects.nonNull(extra)) {
                if (extra.containsKey(AMBIGUOUS_MULTI_PID_DEMERGER)) {
                    extraObject.put(AMBIGUOUS_MULTI_PID_DEMERGER, extra.get(AMBIGUOUS_MULTI_PID_DEMERGER));
                }
                if (extra.containsKey(PARTIAL_BILL_EVENT_KEY)) {
                    extraObject.put(PARTIAL_BILL_EVENT_KEY, extra.get(PARTIAL_BILL_EVENT_KEY));
                }

                if (extra.get(IS_PREPAID_KEY) != null) {
                    String isPrepaid = extra.get(IS_PREPAID_KEY);
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.prepareRecentsData], keys:: {} and  isPrepaid flag - {} ",recents.getKey(), isPrepaid);

                    if ("1".equalsIgnoreCase(isPrepaid)) {
                        extraObject.put(IS_PREPAID_KEY, Boolean.TRUE);
                    } else if ("0".equalsIgnoreCase(isPrepaid)){
                        extraObject.put(IS_PREPAID_KEY, Boolean.FALSE);
                    }
                }

                if (extra.containsKey("isGroupDisplayEnabled") && extra.containsKey("isAmountEditable")) {
                    extraObject.put("isGroupDisplayEnabled", extra.get("isGroupDisplayEnabled"));
                    extraObject.put("isAmountEditable", extra.get("isAmountEditable"));
                }
                Date billFetchDate = DateUtil.stringToDate(reminderResponseModel.getData().getBillFetchDate(), DateFormats.DATE_TIME_FORMAT_2);
                Date paymentDate = DateUtil.stringToDate(reminderResponseModel.getData().getPaymentDate(), DateFormats.DATE_TIME_FORMAT_2);
                if (extra.containsKey("updated_data_source")) {
                    extraObject.put("updated_data_source", extra.get("updated_data_source"));
                    if (billFetchDate != null)
                        extraObject.put("billFetchDate", new Long(billFetchDate.getTime()).toString());
                    if (paymentDate != null)
                        extraObject.put("payment_date", new Long(paymentDate.getTime()).toString());
                }
                if(extra.containsKey(Constants.IS_PAYMODE_CHANGED)){
                    extraObject.put(Constants.IS_PAYTM_VPA,0);
                }
            }
            //checking if extra is already set
            try {
                if (!StringUtils.isEmpty(reminderResponseModel.getData().getConsumerName())) {
                    extraObject.put(SUBSCRIBER_DETAILS, Collections.singletonMap(CONSUMER_NAME, reminderResponseModel.getData().getConsumerName()));
                }else{
                    extraObject.put(SUBSCRIBER_DETAILS, Collections.singletonMap(CONSUMER_NAME, null));
                }
                if (!StringUtils.isEmpty(recents.getExtra())) {
                    Map<String, String> recentsExtra = JsonUtils.parseJson(recents.getExtra(), Map.class);
                    for (Map.Entry<String, String> entry : recentsExtra.entrySet()) {
                        extraObject.put(entry.getKey(), entry.getValue());
                    }
                }
            } catch (Exception e) {
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.prepareRecentsData] Unable to parse or merge recents extra due to error: ", e);
            }
            if (!extraObject.isEmpty()) {
                recents.setExtra(extraObject.toString());
            }

            //Sync the subscriptionUpdatedAt and is_automatic status from reminder
            if(Objects.nonNull(extra)) {
                if (extra.containsKey(Constants.SUBSCRIPTION_UPDATED_AT)) {
                    Map<String, Object> recentsExtra = JsonUtils.parseJson(recents.getExtra(), Map.class);
                    if (recentsExtra.isEmpty()) {
                        recentsExtra = new HashMap<>();
                    }
                    recentsExtra.put(Constants.SUBSCRIPTION_UPDATED_AT, extra.get(Constants.SUBSCRIPTION_UPDATED_AT));
                    recentsExtra.put(Constants.SUBSCRIPTION_IS_AUTOMATIC, reminderResponseModel.getData().getSubscriptionIsAutomatic());
                    String extraJsonString = new ObjectMapper().writeValueAsString(recentsExtra);
                    recents.setExtra(extraJsonString);
                }
            }

            if(reminderResponseModel.getData().getOldBillFetchDate() != null){
                SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                sdf.setLenient(false);
                recents.setOldBillFetchDate(sdf.parse(reminderResponseModel.getData().getOldBillFetchDate()));
            }

            if (reminderResponseModel != null && reminderResponseModel.getData() != null && reminderResponseModel.getData().getRecondId() != null ) {
                recents.setReconId(reminderResponseModel.getData().getRecondId());
            }
            if ((Objects.nonNull(reminderResponseModel.getOld()) && Objects.nonNull(reminderResponseModel.getOld().getDue_date())) || StringUtils.equalsIgnoreCase(reminderResponseModel.getOperationType(), Constants.OP_INSERT)) {
                recents.setMarkAsPaidTime(null);
                recents.setIsMarkAsPaid(false);
                recents.setMarkAsPaidAmount(null);
                recents.setMarkAsPaidSource(null);
            }
            recents.setNextBillFetchDateFlag(Objects.nonNull(reminderResponseModel.getData()) && Objects.nonNull(reminderResponseModel.getData().getNextBillFetchDate()) && !Objects.equals(reminderResponseModel.getData().getNextBillFetchDate(), ""));
            if(Objects.nonNull(reminderResponseModel.getData().getConsentValidTill())) {
                recents.setConsentValidTill(parseConsentValidTill(reminderResponseModel.getData().getConsentValidTill()));
            }
            recents.setUpdatedAt(bill_update_time);
            recents.setProductId(reminderHistory.getProductId());
            recents.setPayType(reminderHistory.getPaytype());
            recents.setCircle((reminderHistory.getCircle()));
            recents.getKey().setOperator(reminderHistory.getOperator());
            recents.setCardSkin(reminderResponseModel.getData().getCardSkin());
            recents.setCardVariant(reminderResponseModel.getData().getCardVariant());
            if(reminderHistory.getPaytype()!=null
            		&& reminderHistory.getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
                String mcn=reminderHistory.getRechargeNumber();
                recents.setMcn(mcn);
                recents.getKey().setOperator(Constants.CommonConstants.CC_DEFAULT_OPERATOR);
//            	ProductMin productMin = CVRProductCache.getInstance().getProductDetails(reminderHistory.getProductId());
//            	if(productMin!=null) {
//                    String customOperator = CVRProductCache.getInstance().getCustomOperatorForCreditCard(reminderHistory.getProductId());
//                    if(customOperator !=null) {
//                        recents.getKey().setOperator(customOperator);
//                    }
//            	}

                Map<String,String> extraInfo=null;
                if(Objects.nonNull(reminderResponseModel.getData()) && Objects.nonNull(reminderResponseModel.getData().getExtra()) && !StringUtils.isEmpty(reminderResponseModel.getData().getExtra())) {
                    extraInfo =  JsonUtils.parseJson(reminderResponseModel.getData().getExtra(), Map.class);
                }

                if(RecentUtils.isPGDeltedCard(reminderResponseModel)){
                    recents.getKey().setRechargeNumber(reminderHistory.getReference_id());
                    recents.setCin(null);
                    if(Objects.nonNull(extraInfo)){
                        recents.setPgCardId(extraInfo.get("pgCardId"));
                    }
                }else if(reminderHistory.getPar() !=null) {
                    recents.getKey().setRechargeNumber(reminderHistory.getPar());
            		recents.setIsTokenizedTransaction(true);
                    if(Objects.nonNull(extraInfo)){
                        recents.setPgCardId(extraInfo.get("pgCardId"));
                    }
            	}else {
                    recents.getKey().setRechargeNumber(reminderHistory.getReference_id());
            	}
            }
            recentService.reminderCheckForNotPaidOnPaytm(recents,reminderHistory,reminderResponseModel);
            if (extraObject.has(IS_PREPAID_KEY) && extraObject.get(IS_PREPAID_KEY).equals(Boolean.TRUE)){
                if (Boolean.TRUE.equals(isNewBillUpdatedForPrepaid(reminderResponseModel)))
                    recents.setNewBillUpdatedAt(new Date());
            }
            else {
                if (Boolean.TRUE.equals(isNewBillUpdated(reminderResponseModel)))
                    recents.setNewBillUpdatedAt(new Date());
            }
            if (reminderResponseModel.getData().getExtra() != null)
                updateBillSourceInRecent(reminderResponseModel,recents);
            return recents;
    	}catch(Exception e) {
    		logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.prepareRecentsData]  reminderHistory {} Exception {}",reminderHistory, e.getMessage());
    		throw new RuntimeException(e);
    	}
	}
    private void updateBillSourceInRecent(ReminderResponseModel reminderResponseModel, Recents recents){
            Map<String,String> extraInfo;
            if(!StringUtils.isEmpty(reminderResponseModel.getData().getExtra())) {
                extraInfo = JsonUtils.parseJson(reminderResponseModel.getData().getExtra(), Map.class);
                if (!CollectionUtils.isEmpty(extraInfo) && Objects.nonNull(extraInfo.get(Constants.CREATED_SOURCE))) {
                    if (!Constants.BILL_TYPE_TRANSACTION.equalsIgnoreCase(extraInfo.get(Constants.CREATED_SOURCE)) && !Constants.BILL_TYPE_SMS.equalsIgnoreCase(extraInfo.get(Constants.CREATED_SOURCE)))
                        recents.setEventSource(extraInfo.get(Constants.CREATED_SOURCE));
                }
                if (!CollectionUtils.isEmpty(extraInfo) && Objects.nonNull(extraInfo.get(Constants.UPDATED_SOURCE))) {
                    if (Constants.BILL_TYPE_SAVED_CARD.equalsIgnoreCase(extraInfo.get(Constants.UPDATED_SOURCE)))
                        recents.setIsSavedCard(true);
                    if (Constants.BILL_TYPE_VALIDATION.equalsIgnoreCase(extraInfo.get(Constants.UPDATED_SOURCE))) {
                        recents.setIsValidation(true);
                    }
                    if (Constants.BILL_TYPE_TRANSACTION.equalsIgnoreCase(extraInfo.get(Constants.UPDATED_SOURCE)))
                        recents.setIsTransaction(true);
                    if (Constants.BILL_TYPE_SMS.equalsIgnoreCase(extraInfo.get(Constants.UPDATED_SOURCE))) {
                            recents.setEventSource("ru_sms");
                            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "updating event source for ru_sms and dueAmount : {}, minDueAmount: {}", recents.getDueAmount(), recents.getMinDueAmount());
                    }
                    if ("PG_Deleted_AMW".equalsIgnoreCase(extraInfo.get(Constants.UPDATED_SOURCE))) {
                        recents.setEventSource( "PG_Deleted_AMW");
                        logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "updating event source for ru_sms and dueAmount : {}, minDueAmount: {}", recents.getDueAmount(), recents.getMinDueAmount());
                    }
                }
                processAirtelPrepaidRecord(reminderResponseModel, recents);
                processElectricityPrepaidRecord(extraInfo, recents);
            }
    }

    private void processAirtelPrepaidRecord(ReminderResponseModel reminderResponseModel, Recents recents) {
        Pair<Boolean, Boolean> isAirtelPrepaidRecordPair = isAirtelPrepaidRecord(reminderResponseModel);
        if(isAirtelPrepaidRecordPair.getFirst()) {
            recents.setEventSource(Constants.EVENT_SOURCE.CSV);

            if(isAirtelPrepaidRecordPair.getSecond()) {
                //reco dismiss condition
                recents.setDueDate(null);
                recents.setDueAmount(0.0);
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, Constants.AIRTEL_PREPAID_BILL_FETCH_RECO_DISMISS);
            } else if(reminderResponseModel.getData().getAmount() != null && reminderResponseModel.getData().getAmount() == 0) {
                //to enable reco by utilising partial bill flow.
                recents.setDueAmount(null);
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, Constants.AIRTEL_PREPAID_BILL_FETCH);
            }
        }
    }

    private boolean isEligibleForCustomerBill(ReminderHistory reminderHistory, ReminderResponseModel reminderResponseModel) {
        boolean prepaidQualified=false;
        try {
            prepaidQualified = RecentUtils.isPrepaidQualified(reminderHistory.getService(), reminderHistory.getOperator(), JsonUtils.parseJson(reminderResponseModel.getData().getExtra(), Map.class));
        } catch (Exception ex){
            logger.error("isPrepaidQualified, exception occurred for data {} Exception",reminderResponseModel.getData(), ex);
        }
       return (reminderHistory.getAmount() == null || reminderHistory.getAmount() >    0 || isAirtelPrepaidRecord(reminderResponseModel).getFirst() || prepaidQualified) && (reminderHistory.getDue_date()!=null || isRUPartialBill(reminderHistory));
    }

    private void processElectricityPrepaidRecord(Map<String,String> extraInfo, Recents recents) {
        if(!CollectionUtils.isEmpty(extraInfo) && extraInfo.get(IS_PREPAID_KEY) != null &&
                extraInfo.get(IS_PREPAID_KEY).equalsIgnoreCase("1") &&
                Objects.nonNull(extraInfo.get(Constants.UPDATED_DATA_SOURCE))) {
            if (Constants.BILL_TYPE_TRANSACTION.equalsIgnoreCase(extraInfo.get(Constants.UPDATED_DATA_SOURCE))) {
                recents.setIsTransaction(true);
                recents.setEventSource(null);
            }
        }
    }

    private boolean isRUPartialBill(ReminderHistory reminderHistory) {
        List<String> ruPartialBillRecoServices = serviceConfig.getRUPartialBillRecoServices();
        return RecentUtils.isRUPartialBill(reminderHistory.getService(), reminderHistory.getDue_date(), ruPartialBillRecoServices);

    }

    private Pair<Boolean, Boolean> isAirtelPrepaidRecord(ReminderResponseModel reminderResponseModel) {
        if(reminderResponseModel == null || reminderResponseModel.getData() == null
                || reminderResponseModel.getData().getStatus() == null || !serviceConfig.isAirtelPrepaidCSVRecentEnabled()) {
            return Pair.of(false, false);
        }
        Map<String,String> extraInfo = null;
        if(!StringUtils.isEmpty(reminderResponseModel.getData().getExtra())) {
            extraInfo = JsonUtils.parseJson(reminderResponseModel.getData().getExtra(), Map.class);
        }
        if(extraInfo == null || extraInfo.isEmpty()) {
            return Pair.of(false, false);
        }

        boolean isValidBillFetch = Constants.CommonConstants.REMINDER_VALID_AIRTEL_BILL_FETCH_STATUS.contains(reminderResponseModel.getData().getStatus());
        boolean isInvalidBillFetch = Constants.CommonConstants.REMINDER_INVALID_AIRTEL_BILL_FETCH_STATUS.contains(reminderResponseModel.getData().getStatus());

        Boolean isAirtelPrepaid = Constants.AIRTEL_PREPAID_BILL_FETCH.equalsIgnoreCase(extraInfo.get(Constants.UPDATED_DATA_SOURCE))
                && (isValidBillFetch || isInvalidBillFetch);

        Boolean isRecoToDismiss = Constants.AIRTEL_PREPAID_BILL_FETCH.equalsIgnoreCase(extraInfo.get(Constants.UPDATED_DATA_SOURCE))
                && isInvalidBillFetch;

        return Pair.of(isAirtelPrepaid, isRecoToDismiss);
    }

    public CustomerBill prepareReminderClusterData(ReminderHistory reminderHistory){
        Long customerid = reminderHistory.getCustomerId();
        Date due_date = reminderHistory.getDue_date();
        String service =reminderHistory.getService();
        String operator = reminderHistory.getOperator();
        String recharge_number = reminderHistory.getRechargeNumber();
        CustomerBill reminderClusterKey =null;

        if(isRUPartialBill(reminderHistory)) {
            due_date = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), serviceConfig.getRUPartialBillRecoDueDateOffset()));
        }

        try {
            reminderClusterKey = new CustomerBill();
            reminderClusterKey.setKey(new CustomerBillPrimaryKey());
            reminderClusterKey.getKey().setCustomerId(customerid);
            reminderClusterKey.getKey().setDue_date(due_date);
            reminderClusterKey.getKey().setService(service);
            reminderClusterKey.getKey().setOperator(operator);
            reminderClusterKey.getKey().setRechargeNumber(recharge_number);
            reminderClusterKey.getKey().setPlanBucket(getReminderPlanBucket(reminderHistory.getOperator()));
            if(reminderHistory.getPaytype()!=null
            		&& reminderHistory.getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {

            	//in case of cc update the operator too - set it to default cc operator i.e 'ccbp'
                reminderClusterKey.getKey().setOperator(Constants.CommonConstants.CC_DEFAULT_OPERATOR);
//                ProductMin productMin = CVRProductCache.getInstance().getProductDetails(reminderHistory.getProductId());
//                if(productMin!=null) {
//                    String customOperator = CVRProductCache.getInstance().getCustomOperatorForCreditCard(reminderHistory.getProductId());
//                    if(customOperator !=null) {
//                        reminderClusterKey.getKey().setOperator(customOperator);
//                    }
//            	}
            	if(reminderHistory.getPar() !=null) {
            		reminderClusterKey.getKey().setRechargeNumber(reminderHistory.getPar());
            	}else {
            		reminderClusterKey.getKey().setRechargeNumber(reminderHistory.getReference_id());
            	}
            }

            return reminderClusterKey;
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[ReminderListeners.prepareReminderClusterData] Exception", e);
            throw new RuntimeException(e);
        }
    }
    public ReminderHistory prepareReminderData(ReminderResponseModel reminderResponseModel){

        String recharge_number = reminderResponseModel.getData().getRecharge_number();
        String service = reminderResponseModel.getData().getService();
        String circle = reminderResponseModel.getData().getCircle();
        String operator = reminderResponseModel.getData().getOperator();
        Double amount = reminderResponseModel.getData().getAmount();
        Date updated_at = new Date();
        Long customerid = reminderResponseModel.getData().getCustomer_id();
        Date created_at = null;
        Date due_date = null;
        Date bill_date = null;
        Integer is_automatic = reminderResponseModel.getData().getIs_automatic();
        Integer status = reminderResponseModel.getData().getStatus();
        Integer isPartial = 0;
        String paytype = reminderResponseModel.getData().getPaytype();
        Long productId = reminderResponseModel.getData().getProduct_id();
        recharge_number = CommonUtils.getRechargeNumberBasedOnRules(recharge_number, operator);

        try{
            updated_at = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(reminderResponseModel.getData().getUpdated_at());
        } catch (Exception e) { logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra]  updated_at::Exception", e); }

        try{
            created_at = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(reminderResponseModel.getData().getCreated_at());
        } catch (Exception e) { logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra]  created_at::Exception", e); }

        try{
        	SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        	sdf.setLenient(false);
            if(Objects.nonNull(reminderResponseModel.getData().getDue_date()))
                due_date = sdf.parse(reminderResponseModel.getData().getDue_date());
        } catch (Exception e) { logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra]  due_date::Exception", e); }

        try{
        	SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        	sdf.setLenient(false);
            if(Objects.nonNull(reminderResponseModel.getData().getBill_date()))
                bill_date = sdf.parse(reminderResponseModel.getData().getBill_date());
        } catch (Exception e) { logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra]  bill_date::Exception", e); }

        // Since Maxwell is sending -5:30 in updated, createdAt.
        updated_at = DateUtils.addHours(updated_at, 5);
        updated_at = DateUtils.addMinutes(updated_at, 30);
        created_at = DateUtils.addHours(created_at, 5);
        created_at = DateUtils.addMinutes(created_at, 30);

        if(reminderResponseModel.getOperationType().equals("update")){
            // Partial Payment Flag  : Amount is changed  and Current amount>0 and due date same, bill date is same -> stamp a partial payment flag
            if(reminderResponseModel.getOld()!=null && reminderResponseModel.getData()!=null){
                if(reminderResponseModel.getOld().getDue_date()==null &&
                        reminderResponseModel.getOld().getAmount()!=null &&
                        reminderResponseModel.getOld().getAmount() > 0 &&
                        reminderResponseModel.getData().getAmount()!=null &&
                        reminderResponseModel.getData().getAmount() > 0
                ){
                    isPartial = 1;
                }
            }
        }
        Double originalAmount=null;
        Double originalMinAmount=null;
        if(due_date != null && reminderResponseModel.getData().getPaytype() !=null
        		&& reminderResponseModel.getData().getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)
        		&& isPartial==1) {
        	List<ReminderHistory> reminderHistories = reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndService(customerid, recharge_number, service);
        	if(reminderHistories !=null && !reminderHistories.isEmpty()) {
        		for(ReminderHistory reminderHistory:reminderHistories) {
                    if (reminderHistory.getDue_date() != null
                            && reminderHistory.getDue_date().compareTo(due_date) == 0
                            && reminderHistory.getOriginalAmount() != null
                            && compareUniqueCCId(reminderResponseModel, reminderHistory)) {
                        originalAmount = reminderHistory.getOriginalAmount();
                        originalMinAmount = reminderHistory.getOriginalMinBillAmount();
                        break;
                    }
                }
        	}
        }else if(reminderResponseModel.getData().getPaytype() !=null && reminderResponseModel.getData().getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
        	originalAmount = reminderResponseModel.getData().getAmount();
			originalMinAmount = reminderResponseModel.getData().getCurrentMinBillAmount();
        }
        if(reminderResponseModel.getOperationType().equals("delete")){
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.insertDataIntoCassandra] delete operation : {}",reminderResponseModel);
            // updated_at = new Date();
            if(reminderResponseModel.getData().getPaytype()!=null
                    && reminderResponseModel.getData().getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
                amount = 0.0;
                reminderResponseModel.getData().setCurrentMinBillAmount(0.0);
            }else{
                due_date = null;
                bill_date = null;
            }
        }
        Date earlyPaymentDate = this.formatEarlyPaytmentDate(reminderResponseModel.getData().getEarlyPaymentDate());

        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setRechargeNumber(recharge_number);
        reminderHistory.setService(service);
        reminderHistory.setCircle(circle);
        reminderHistory.setOperator(operator);
        reminderHistory.setAmount(amount);
        reminderHistory.setUpdatedAt(updated_at);
        reminderHistory.setOriginalAmount(originalAmount);
        reminderHistory.setOriginalMinBillAmount(originalMinAmount);
        reminderHistory.setCreated_at(created_at);
        reminderHistory.setDue_date(due_date);
        reminderHistory.setBill_date(bill_date);
        reminderHistory.setIs_automatic(is_automatic);
        reminderHistory.setStatus(status);
        reminderHistory.setCustomerId(customerid);
        reminderHistory.setIsPartial(isPartial);
        reminderHistory.setPaytype(paytype);
        reminderHistory.setProductId(productId);
        reminderHistory.setCurrentMinBillAmount(reminderResponseModel.getData().getCurrentMinBillAmount());
        reminderHistory.setLastPaidAmount(reminderResponseModel.getData().getLastPaidAmount());

        if(RecentUtils.isPGDeltedCard(reminderResponseModel)){
            reminderHistory.setReference_id(reminderResponseModel.getData().getReference_id());
            reminderHistory.setPar(reminderResponseModel.getData().getPar());
        }
        else if (reminderResponseModel.getData().getPar() != null) {
            reminderHistory.setPar(reminderResponseModel.getData().getPar());
            reminderHistory.setTin(reminderResponseModel.getData().getTin());
        }
        else if (reminderResponseModel.getData().getReference_id() != null) {
            reminderHistory.setReference_id(reminderResponseModel.getData().getReference_id());
        }
        reminderHistory.setUserData(reminderResponseModel.getData().getUser_data());
        reminderHistory.setEarlyPaymentDate(earlyPaymentDate);
        reminderHistory.setEarlyPaymentAmount(reminderResponseModel.getData().getEarlyPaymentAmount());
        return reminderHistory;
    }
    private Date formatEarlyPaytmentDate(String earlyPaymentDateStr){
        Date earlyPaymentDate =null;
        if(Objects.nonNull(earlyPaymentDateStr)){
            try{
                earlyPaymentDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(earlyPaymentDateStr);
            } catch (Exception e) { logger.warn("[ReminderListeners.insertDataIntoCassandra]  updated_at:: date {} Exception {}", earlyPaymentDateStr,e.getMessage()); }

            try{
                if(earlyPaymentDate==null)
                    earlyPaymentDate = new SimpleDateFormat("yyyy-MM-dd").parse(earlyPaymentDateStr);
            } catch (Exception e) { logger.warn("[ReminderListeners.insertDataIntoCassandra]  updated_at:: date {} Exception {}", earlyPaymentDateStr,e.getMessage()); }
        }
        return  earlyPaymentDate;
    }

    public ChannelHistory prepareChannelData(ReminderHistory reminderHistory, ReminderResponseModel reminderResponseModel){
        ChannelHistory channelHistory = new ChannelHistory();

        channelHistory.setEventType(Constants.CommonConstants.NOT_PAID_ON_PAYTM_MESSAGE_TYPE);

        channelHistory.setRechargeNumber(reminderHistory.getRechargeNumber());

        channelHistory.setService(reminderHistory.getService());
        channelHistory.setCircle(reminderHistory.getCircle());
        channelHistory.setOperator(reminderHistory.getOperator());
        channelHistory.setAmount(reminderHistory.getAmount() != null ? Double.toString(reminderHistory.getAmount()) : null);
        channelHistory.setTransactionTime(reminderHistory.getUpdatedAt());
        channelHistory.setTransactionUpdateTime(reminderHistory.getUpdatedAt());
        channelHistory.setCustomerId(reminderHistory.getCustomerId());
        channelHistory.setOrderId(1L);
        channelHistory.setItemId(1L);
        channelHistory.setPaytype(reminderHistory.getPaytype());
        channelHistory.setProductId(reminderHistory.getProductId());

        if(reminderResponseModel.getOld()!=null){
            if(reminderResponseModel.getOld().getAmount()!=null){
                channelHistory.setAmount(Double.toString(reminderResponseModel.getOld().getAmount()));
            }
        }


        Map<String, String> billObj = new HashMap<>();

        String bill_date = null;
        String due_date = null;
        Double minDueAmount = null;

        if(reminderResponseModel.getOld()!=null && reminderResponseModel.getData()!=null){

            if(reminderResponseModel.getOld().getCurrentMinBillAmount() != null) {
                minDueAmount = reminderResponseModel.getOld().getCurrentMinBillAmount();
            }
            else {
                minDueAmount = reminderResponseModel.getData().getCurrentMinBillAmount();
            }

            if(reminderResponseModel.getOld().getBill_date()!=null){
                bill_date = reminderResponseModel.getOld().getBill_date();
            } else{
                bill_date = reminderResponseModel.getData().getBill_date();
            }

            if(reminderResponseModel.getOld().getDue_date()!=null){
                due_date = reminderResponseModel.getOld().getDue_date();
            } else{
                due_date = reminderResponseModel.getData().getDue_date();
            }

        } else{
            if(reminderResponseModel.getData()!=null){
                if (reminderResponseModel.getData().getCurrentMinBillAmount() != null) {
                    minDueAmount = reminderResponseModel.getData().getCurrentMinBillAmount();
                }
                if(reminderResponseModel.getData().getDue_date()!=null){
                    due_date = reminderResponseModel.getData().getDue_date();
                }
                if(reminderResponseModel.getData().getBill_date()!=null){
                    bill_date = reminderResponseModel.getData().getBill_date();
                }
            }
        }


        if (minDueAmount != null) {
            billObj.put(Constants.ReminderConstants.MIN_DUE_AMOUNT , minDueAmount.toString());
        }

        if(bill_date!=null){
            billObj.put(Constants.ReminderConstants.BILL_DATE , bill_date);
        }
        if(due_date!=null){
            billObj.put(Constants.ReminderConstants.DUE_DATE , due_date);
        }
        if(reminderHistory.getPaytype() !=null
        		&& reminderHistory.getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
        	String mcn=reminderHistory.getRechargeNumber();

        	if(reminderHistory.getPar() !=null) {
            	channelHistory.setRechargeNumber(reminderHistory.getPar());
            	channelHistory.setRecharge_number_4(reminderHistory.getTin());
            	billObj.put("par", reminderHistory.getPar());
            	billObj.put("tin", reminderHistory.getTin());

        	}else {

                channelHistory.setRechargeNumber(reminderHistory.getReference_id());
        		channelHistory.setRecharge_number_3(reminderHistory.getReference_id());
                billObj.put("cin", reminderHistory.getReference_id());
                billObj.put("mcn", mcn);
        	}

        }

        channelHistory.setBillsObj(billObj);

        if (channelHistory.getAmount() == null) {
            metricsHelper.recordSuccessRate(REMINDER_CONSUMER, NOT_PAID_ON_PAYTM_AMOUNT_NULL);
            return null;
        }
        return channelHistory;

    }

    public Boolean  isNotPaidOnPaytm(ReminderResponseModel reminderResponseModel){
        if(reminderResponseModel.getOperationType().equals("update")){
            // If Due Date is changed, amount > 0 -> Create an entry in channel_history with event : “NOT_PAID_ON_PAYTM” with orderId =1 and itemId = 1
            if(reminderResponseModel.getOld()!=null){
                if((Service.RENT_PAYMENT.value.equalsIgnoreCase(reminderResponseModel.getData().getService()) || Service.BUSINESS_PAYMENT.value.equalsIgnoreCase(reminderResponseModel.getData().getService()))  && Objects.nonNull(reminderResponseModel.getOld().getAmount())
                        && reminderResponseModel.getOld().getAmount() > 0){
                    reminderResponseModel.getOld().setAmount(0.0);
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "isNotPaidOnPaytm RENT_PAYMENT for cust id = {}",reminderResponseModel.getData().getCustomer_id());
                }
                if((reminderResponseModel.getOld().getDue_date()!=null )){
                    if(reminderResponseModel.getOld().getAmount() == null){
                        if(reminderResponseModel.getData().getAmount() != null && reminderResponseModel.getData().getAmount() > 0){
                            return true;
                        }
                    } else {
                        if(reminderResponseModel.getOld().getAmount() > 0){
                            return true;
                        }
                    }
                } else {
                    if (!Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(reminderResponseModel.getData().getService()) && (reminderResponseModel.getOld().getAmount() != null && reminderResponseModel.getOld().getAmount() > 0) && reminderResponseModel.getOld().getStatus() != null) {
                        return notPaidOnPaytmAmountAndStatusCheck(reminderResponseModel);
                    }
                }
            }
        }
        return false;
    }

    public Boolean notPaidOnPaytmAmountAndStatusCheck(ReminderResponseModel reminderResponseModel) {
        if (reminderResponseModel.getData().getAmount() != null && reminderResponseModel.getData().getAmount() == 0 && Constants.ReminderConstants.NOT_PAID_ON_PAYTM_ALLOWED_STATUS_LIST.indexOf(reminderResponseModel.getData().getStatus()) > -1)
            return true;
        return false;
    }

    public void updateRecentsAutomaticState(ReminderResponseModel reminderResponseModel){
        Map<String,String> newExtra = null;
        if(reminderResponseModel.getData()!=null && reminderResponseModel.getData().getExtra()!=null){
            newExtra =  JsonUtils.parseJson(reminderResponseModel.getData().getExtra(),Map.class);
        }
        if(reminderResponseModel.getOperationType().equals("update") && ((reminderResponseModel.getOld() != null && reminderResponseModel.getOld().getIs_automatic() != null) || (Objects.nonNull(newExtra) && newExtra.containsKey(IS_PAYMODE_CHANGED)))) {
            Integer automatic_state = reminderResponseModel.getData().getIs_automatic();
            String recharge_number = reminderResponseModel.getData().getRecharge_number();
            String operator = reminderResponseModel.getData().getOperator();
            if(reminderResponseModel.getData().getPaytype()!=null
                    && reminderResponseModel.getData().getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
                operator = Constants.CommonConstants.CC_DEFAULT_OPERATOR;
                if(reminderResponseModel.getData().getPar() !=null) {
                    recharge_number = reminderResponseModel.getData().getPar();
                }else {
                    recharge_number = reminderResponseModel.getData().getReference_id();
                }
            }
            try {
                recentService.updateRecentAutomaticStateInfo(automatic_state,reminderResponseModel.getData().getCustomer_id(), reminderResponseModel.getData().getService(),recharge_number, operator,"");
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "Updating recents automatic_status for customer id : {}, recharge number : {}, service : {}, operator : {}",reminderResponseModel.getData().getCustomer_id(), recharge_number, reminderResponseModel.getData().getService(),
                        operator);
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECENT_AUTOMATIC_ONLY);
            }catch (Exception e) {
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "Updating recents automatic_status for customer id : {}, recharge number : {}, service : {}, operator : {}, Exception",
                        reminderResponseModel.getData().getCustomer_id(), recharge_number, reminderResponseModel.getData().getService(),
                        operator, e);
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
            }
            try {
                kafkaProducerService.sendMessage(Long.toString(reminderResponseModel.getData().getCustomer_id()));
            }
            catch (Exception e){
                metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECO_REFRESH_ERROR_EVENT);
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.updateRecentsAutomaticState] kafka cache clean event publish error for reminderResponseModel {}, Exception",
                        reminderResponseModel,e);
            }
        }

        return;

    }

    public void updateRecentsConsentValidTill(ReminderResponseModel reminderResponseModel) {
        if (reminderResponseModel.getOperationType().equals("update")
            && reminderResponseModel.getOld() != null
            && !Objects.equals(reminderResponseModel.getOld().getConsentValidTill(), reminderResponseModel.getData().getConsentValidTill())) {
            Date consentValidTill = parseConsentValidTill(reminderResponseModel.getData().getConsentValidTill());
            String rechargeNumber = determineRechargeNumber(reminderResponseModel);
            String operator = determineOperator(reminderResponseModel);
            updateRecentConsentValidTill(reminderResponseModel, consentValidTill, rechargeNumber, operator);
            sendKafkaMessage(reminderResponseModel);
        }
    }

    private Date parseConsentValidTill(String consentValidTillStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);
            sdf.setLenient(false);
            return sdf.parse(consentValidTillStr);
        } catch (Exception e) {
            logger.error("[ReminderListeners.parseConsentValidTill] consentValidTill Date Parsing Exception", e);
            return null;
        }
    }

    private String determineRechargeNumber(ReminderResponseModel reminderResponseModel) {
        String rechargeNumber = reminderResponseModel.getData().getRecharge_number();
        if (reminderResponseModel.getData().getPaytype() != null
            && reminderResponseModel.getData().getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
            if (reminderResponseModel.getData().getReference_id() != null) {
                rechargeNumber = reminderResponseModel.getData().getReference_id();
            } else {
                rechargeNumber = reminderResponseModel.getData().getPar();
            }
        }
        return rechargeNumber;
    }

    private String determineOperator(ReminderResponseModel reminderResponseModel) {
        String operator = reminderResponseModel.getData().getOperator();
        if (reminderResponseModel.getData().getPaytype() != null
            && reminderResponseModel.getData().getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
            operator = Constants.CommonConstants.CC_DEFAULT_OPERATOR;
        }
        return operator;
    }

    private void updateRecentConsentValidTill(ReminderResponseModel reminderResponseModel, Date consentValidTill, String rechargeNumber, String operator) {
        try {
            recentService.updateRecentConsentValidTillInfo(consentValidTill, reminderResponseModel.getData().getCustomer_id(),
                reminderResponseModel.getData().getService(), rechargeNumber, operator, "");
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "Updating recents consent valid till for customer id : {}, recharge number : {}, service : {}, operator : {}",
                reminderResponseModel.getData().getCustomer_id(), rechargeNumber, reminderResponseModel.getData().getService(), operator);
            metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECENT_CONSENT_VALID_TILL_ONLY);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "Updating recents consent valid till for customer id : {}, recharge number : {}, service : {}, operator : {}, Exception",
                reminderResponseModel.getData().getCustomer_id(), rechargeNumber, reminderResponseModel.getData().getService(), operator, e);
            metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
        }
    }
    private void sendKafkaMessage(ReminderResponseModel reminderResponseModel) {
        try {
            kafkaProducerService.sendMessage(Long.toString(reminderResponseModel.getData().getCustomer_id()));
        } catch (Exception e) {
            metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECO_REFRESH_ERROR_EVENT);
            logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.sendKafkaMessage] kafka cache clean event publish error for reminderResponseModel {}, Exception",
                reminderResponseModel, e);
        }
    }

    public void updateReminderHistoryAutomatic(ReminderResponseModel reminderResponseModel){
        if(reminderResponseModel.getOperationType().equals("update") && reminderResponseModel.getOld() != null && reminderResponseModel.getOld().getIs_automatic() != null) {
            List<ReminderHistory> reminderHistories = null;
            try {
                reminderHistories = reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndService(reminderResponseModel.getData().getCustomer_id(),
                        reminderResponseModel.getData().getRecharge_number(), reminderResponseModel.getData().getService());
                if (reminderHistories != null && reminderHistories.size()>0 && reminderHistories.get(0) != null) {
                    reminderHistories.get(0).setIs_automatic(reminderResponseModel.getData().getIs_automatic());
                    Integer TTL = getTTL(reminderHistories.get(0), reminderResponseModel);
                    try {
                        reminderHistoryRepository.save(reminderHistories.get(0), TTL);
                        logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "ReminderHistory : updated automatic state and  insert data successfully {}",reminderHistories.get(0));
                        metricsHelper.recordSuccessRate(REMINDER_CONSUMER, REMINDER_AUTOMATIC_ONLY);
                    } catch (Exception e){
                        metricsHelper.recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
                        logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "ReminderHistory : updated automatic state and  insert for cusomter id {}, recharge number {}, service {}, operator {}, Exception",
                                reminderHistories.get(0).getCustomerId(),reminderHistories.get(0).getRechargeNumber(),
                                reminderHistories.get(0).getService(),reminderHistories.get(0).getOperator(),e);
                    }
                }else{
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "ReminderHistory : no record in reminder for reminderResponseModel {}",reminderResponseModel);
                }
            } catch (Exception e) {
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "Error while fetching the reminder history data for cusomter id : {}, recharge number : {}, service : {} , Exception",
                        reminderResponseModel.getData().getCustomer_id(),
                        reminderResponseModel.getData().getRecharge_number(), reminderResponseModel.getData().getService(), e);
            }

        }
        return ;
    }



    private Integer getTTL(ReminderHistory reminderHistory, ReminderResponseModel reminderResponseModel){
        Date due_date = reminderHistory.getDue_date();
        if(due_date==null){
            if(reminderResponseModel.getOperationType()!=null && reminderResponseModel.getOperationType().equals("delete")){
                Date due_date_old = null;
                try{
                    due_date_old = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(reminderResponseModel.getData().getDue_date());
                } catch (Exception e) { logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.getTTL]  due_date::Exception", e); }

                if(due_date_old!=null){
                    Integer secondsDiff = Math.abs(Math.toIntExact(due_date_old.getTime()/1000 - (new Date()).getTime()/1000));
                    return secondsDiff + Constants.CommonConstants.REMINDER_TTL_ADD_SECONDS;
                }
            }
            return Constants.CommonConstants.REMINDER_TTL_ADD_SECONDS;
        } else {
            Integer secondsDiff = Math.abs(Math.toIntExact(due_date.getTime()/1000 - (new Date()).getTime()/1000));
            return secondsDiff + Constants.CommonConstants.REMINDER_TTL_ADD_SECONDS;
        }
    }

    public Boolean isSkipable(ReminderResponseModel reminderResponseModel, Boolean partialSms){
        if(reminderResponseModel==null ||
                reminderResponseModel.getData()==null){
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"DUE_DATE_NOT_EXIST");
            return true;
        }


        if(reminderResponseModel.getData().getStatus() == null || reminderResponseModel.getData().getStatus() == 0) {
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"REMINDER_STATUS_0");
            return true;
        }

        if(reminderResponseModel.getData().getCustomer_id()==null || reminderResponseModel.getData().getService()==null || reminderResponseModel.getData().getRecharge_number()==null) {
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"NO_PRIMARY_KEY_DATA");
            return true;
        }


        if(reminderResponseModel.getData().getDue_date() == null)
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "due date is null");

        if(reminderResponseModel.getData().getPaytype() != null
            && reminderResponseModel.getData().getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {

            if (reminderResponseModel.getOperationType().equals("delete") && reminderResponseModel.getData().getAmount() <= 0){
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"OPERATION_TYPE_DELETE");
                return true;
            }
            if(reminderResponseModel.getData().getRecharge_number() == null) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"RECHARGE_NUMBER_NOT_EXIST");
                return true;
            }
            if(reminderResponseModel.getData().getPar() == null && reminderResponseModel.getData().getReference_id() == null) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"PAR_AND_REFERENCE_ID_NOT_EXIST");
                return true;
            }

        }

        Integer status = reminderResponseModel.getData().getStatus();
        String tableName = reminderResponseModel.getTable();

        if(!isAirtelPrepaidRecord(reminderResponseModel).getFirst() && Arrays.asList(Constants.CommonConstants.REMINDER_SKIP_STATUS).contains(status)){
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"REMINDER_SKIP_STATUS");
            return true;
        }

        for(String skipTable: Constants.CommonConstants.REMINDER_SKIP_TABLES) {
            if (tableName.startsWith(skipTable)) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}", reminderResponseModel, "REMINDER_SKIP_TABLES");
                return true;
            }
        }

        Map<String,String> extra = null;
        if(reminderResponseModel.getOld()!=null && reminderResponseModel.getOld().getExtra()!=null){
            extra =  JsonUtils.parseJson(reminderResponseModel.getOld().getExtra(),Map.class);
        }

        Map<String,String> newExtra = null;
        if(reminderResponseModel.getData()!=null && reminderResponseModel.getData().getExtra()!=null){
            newExtra =  JsonUtils.parseJson(reminderResponseModel.getData().getExtra(),Map.class);
        }

        Boolean isOldBill = isOldBill(reminderResponseModel);
        if(reminderResponseModel.getOperationType()!=null && reminderResponseModel.getOperationType().equals("update")){
            if(reminderResponseModel.getOld()==null){
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"OLD_DETAILS_NOT_EXIST");
                return true;
            } else{
                Double currentOutstandingAmountNew = getCurrentOutstandingAmount(reminderResponseModel.getData());
                Double currentOutstandingAmountOld = getCurrentOutstandingAmount(reminderResponseModel.getOld());
                if(partialSms || isOldBill || reminderResponseModel.getOld().getBill_date()!=null ||
                        reminderResponseModel.getOld().getDue_date()!=null ||
                        reminderResponseModel.getOld().getAmount()!=null ||
                        reminderResponseModel.getOld().getCircle()!=null ||
                        reminderResponseModel.getOld().getCustomer_id()!=null ||
                        reminderResponseModel.getOld().getIs_automatic()!=null ||
                        reminderResponseModel.getOld().getOperator()!=null ||
                        reminderResponseModel.getOld().getService()!=null ||
                        reminderResponseModel.getOld().getPaytype()!=null ||
                        reminderResponseModel.getOld().getRecharge_number()!=null ||
                        reminderResponseModel.getOld().getProduct_id()!=null ||
                        reminderResponseModel.getOld().getNotificationStatus()!=null ||
                        reminderResponseModel.getOld().getRemindLaterDate()!=null ||
                        reminderResponseModel.getOld().getConsentValidTill()!=null ||
                        !Objects.equals(reminderResponseModel.getOld().getConsentValidTill(), reminderResponseModel.getData().getConsentValidTill()) ||
                        (extra !=null && ((extra.containsKey("skin_url") && extra.containsKey("skin_source")) || extra.containsKey("issuingBankCardVariant"))) ||
                        (extra==null &&  (newExtra!=null && ((newExtra.containsKey("skin_url") && newExtra.containsKey("skin_source")) || newExtra.containsKey("issuingBankCardVariant"))))
                ){

                    // If any of the field that we are storing in DB is not changing then ignore that payload.
                } else if(!Objects.equals(currentOutstandingAmountOld, currentOutstandingAmountNew)) {
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "current outstanding is changed");
                } else if(reminderResponseModel.getOld().getUpdated_at()!=null
                        && "paytm postpaid".equalsIgnoreCase(reminderResponseModel.getData().getService())
                        && reminderResponseModel.getData().getDue_date() !=null){
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "updating data postpaid only updatedAt data {}",reminderResponseModel);
                }
                else if(reminderResponseModel.getOld().getUpdated_at()!=null
                        && reminderResponseModel.getOld().getIs_automatic() == null
                        && (Arrays.asList(Constants.SUBSCRIPTION_SERVICES).contains(StringUtils.lowerCase(reminderResponseModel.getData().getService()))
                        || (StringUtils.containsIgnoreCase(Constants.ServiceTypeConstants.MOBILE,reminderResponseModel.getData().getService()) && reminderResponseModel.getData().getPaytype() != null &&
                        StringUtils.containsIgnoreCase(Constants.CommonConstants.POSTPAID_PAYTYPE,reminderResponseModel.getData().getPaytype())))
                        && reminderResponseModel.getData().getDue_date() !=null){
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "updating data for subscription services on changing updatedAt data  {}",reminderResponseModel);
                    metricsHelper.recordSuccessRate(REMINDER_CONSUMER, SYNC_BILL_AUTOMATIC_EVENT);
                }else{
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"NO_FIELD_CHANGED_WHILE_STORING_IN_DB");
                    return true;
                }
            }
        }

        if(isPaymentEvent(reminderResponseModel)){
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "not skipping data {} reason {}",reminderResponseModel,"PAYMENT_EVENT");
            return false;
        }
        if(isCCNoBillDue(reminderResponseModel)) {
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "not skipping data {} reason {}",reminderResponseModel,"CC_NO_BILL_DUE");
            return false;
        }
        if(oldDueDateSkipCheck(reminderResponseModel)){
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping data {} reason {}",reminderResponseModel,"OLD_DUE_DATE_CHECK");
            return true;
        }
        if(Objects.nonNull(reminderResponseModel.getData().getNotificationStatus()) && (reminderResponseModel.getData().getNotificationStatus().equals(0) || reminderResponseModel.getData().getNotificationStatus().equals(2))){
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "sikpping as notification status is 0 or 2");
            return true;
        }

        return false;

    }

    //if service is electricity and status is 5, we need to store old bills
    public Boolean isOldBill(ReminderResponseModel reminderResponseModel){
        List<String> oldBillEnabledServices = serviceConfig.getOldBillEnabledServices();
        List<String> oldBillDisabledOperators = serviceConfig.getOldBillDisabledOperators();
        return oldBillEnabledServices != null && oldBillDisabledOperators != null && reminderResponseModel.getData().getService() != null && reminderResponseModel.getData().getOperator() != null && reminderResponseModel.getData().getOldBillFetchDate() != null && reminderResponseModel.getData().getStatus() != null && oldBillEnabledServices.contains(reminderResponseModel.getData().getService()) && !CommonUtils.containIgnoreCase(oldBillDisabledOperators,reminderResponseModel.getData().getOperator()) && reminderResponseModel.getData().getStatus().equals(OLD_BILL_REMINDER_STATUS) && reminderResponseModel.getData().getAmount() != null && reminderResponseModel.getData().getAmount() > 0;
    }

    public Boolean isPaymentEvent(ReminderResponseModel reminderResponseModel){
        return reminderResponseModel.getData().getStatus().equals(PAYMENT_DONE_REMINDER_STATUS);
    }

    public Boolean oldDueDateSkipCheck(ReminderResponseModel reminderResponseModel){
        if(reminderResponseModel==null || reminderResponseModel.getData()==null) return true;

        if("paytm postpaid".equalsIgnoreCase(reminderResponseModel.getData().getService())){
            return false;
        }

        if(isOldBill(reminderResponseModel)){
            return false;
        }

        String due_date = reminderResponseModel.getData().getDue_date();
        if(due_date==null && reminderResponseModel.getOperationType()!=null && reminderResponseModel.getOperationType().equals("delete") && reminderResponseModel.getOld()!=null){
            due_date = reminderResponseModel.getOld().getDue_date();
        }

        Date dueDate =null;
        try{
            if(due_date != null)
                dueDate = new SimpleDateFormat(Constants.CommonConstants.DATE_FORMAT).parse(due_date);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.oldDueDateSkipCheck] due_date::Exception", e);
            return true;
        }
        if(dueDate != null){
            long dueDateTimeInSeconds = dueDate.getTime()/1000;
            if(dueDateTimeInSeconds < 0) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "skipping due to invalid due date {}", due_date);
                return true;
            }
            Integer secondsDiff = Math.toIntExact(dueDateTimeInSeconds - (new Date()).getTime()/1000);
            if(secondsDiff<0){
                secondsDiff = -1 * secondsDiff;
                if(secondsDiff > Constants.CommonConstants.REMINDER_OLD_DUE_DATE_THRESHOLD){
                    return  true;
                }
            }
        }
        return false;

    }

    public boolean compareUniqueCCId(ReminderResponseModel reminderResponseModel, ReminderHistory reminderHistory) {

        if (reminderResponseModel.getData().getPar() != null &&
                reminderResponseModel.getData().getPar().equals(reminderHistory.getPar())) {
            return true;
        }

        if (reminderResponseModel.getData().getReference_id() != null &&
                reminderResponseModel.getData().getReference_id().equals(reminderHistory.getReference_id())) {
            return true;
        }

        return false;

    }
    public Boolean isNewBillUpdated(ReminderResponseModel reminderResponseModel){
        if(reminderResponseModel.getOperationType().equalsIgnoreCase(Constants.OP_INSERT) || (reminderResponseModel.getOperationType().equalsIgnoreCase(Constants.OP_UPDATE) && Objects.nonNull(reminderResponseModel.getOld()) && reminderResponseModel.getOld().getDue_date() != null)){
            if(reminderResponseModel.getData().getDue_date() != null && (reminderResponseModel.getData().getAmount() != null || (Objects.nonNull(reminderResponseModel.getOld()) && reminderResponseModel.getOld().getAmount() != null)))
                return true;
        }
        return false;
    }

    public Boolean isNewBillUpdatedForPrepaid(ReminderResponseModel reminderResponseModel){
        if(reminderResponseModel.getOperationType().equalsIgnoreCase(Constants.OP_INSERT)){
            return reminderResponseModel.getData().getDue_date() != null;
        } else if (reminderResponseModel.getOperationType().equalsIgnoreCase(Constants.OP_UPDATE)) {
            if(reminderResponseModel.getData().getDue_date() != null && reminderResponseModel.getOld().getDue_date() != null) {
                Date newDueDate = DateUtil.stringToDate(reminderResponseModel.getData().getDue_date(), Constants.CommonConstants.DATE_FORMAT);
                Date oldDueDate = DateUtil.stringToDate(reminderResponseModel.getOld().getDue_date(), Constants.CommonConstants.DATE_FORMAT);
                if(newDueDate != null && oldDueDate != null) {
                    return newDueDate.after(oldDueDate);
                }
            }
        }
        return false;
    }

    public void dropOffHandling(ReminderHistory reminderHistory) {
        DropOff dropOff = prepareDropOff(reminderHistory);
        try {
            dropOffDBHelper.insertDropOff(dropOff);
        } catch (Exception e) {
            logger.error( "ReminderListener.dropOffhandling: failed to insert into dropOff");
        }
    }

    public DropOff prepareDropOff(ReminderHistory reminderHistory) {
        DropOff dropOff = new DropOff();
        Map<String, String> billsObj = new HashMap<>();
        dropOff.setCustomerId(reminderHistory.getCustomerId());
        dropOff.setRechargeNumber(reminderHistory.getRechargeNumber());
        dropOff.setService(reminderHistory.getService());
        dropOff.setPaytype(reminderHistory.getPaytype());
        dropOff.setOperator(reminderHistory.getOperator());
        dropOff.setCircle(reminderHistory.getCircle());
        dropOff.setTransactionTime(reminderHistory.getUpdatedAt());
        dropOff.setAmount("1");
        dropOff.setEventType(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE);
        dropOff.setStatus("7");
        dropOff.setIn_response_code("00");
        dropOff.setIsAutomatic(null);
        if (reminderHistory.getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
            billsObj.put("mcn", reminderHistory.getRechargeNumber());
        }
        billsObj.put(Constants.REMINDER_NEW_BILL,"true");
        dropOff.setBillsObj(billsObj);
        return dropOff;
    }

    public void checkForNotificationStatus(ReminderResponseModel reminderResponseModel) throws ReminderListenerException{
        if((reminderResponseModel.getOperationType().equals("update") && Objects.nonNull(reminderResponseModel.getOld()) && Objects.nonNull(reminderResponseModel.getOld().getNotificationStatus()) && reminderResponseModel.getData().getNotificationStatus()!=0 && reminderResponseModel.getData().getNotificationStatus()!=2)) {
            Integer notificationStatus = reminderResponseModel.getData().getNotificationStatus();
            String rechargeNumber = reminderResponseModel.getData().getRecharge_number();
            String operator = reminderResponseModel.getData().getOperator();
            if(reminderResponseModel.getData().getPaytype()!=null
                    && reminderResponseModel.getData().getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
                operator = Constants.CommonConstants.CC_DEFAULT_OPERATOR;
                if(reminderResponseModel.getData().getPar() !=null) {
                    rechargeNumber = reminderResponseModel.getData().getPar();
                }else {
                    rechargeNumber = reminderResponseModel.getData().getReference_id();
                }
            }
            try{
                recentService.updateNotificationStatusInRecents(notificationStatus,operator,rechargeNumber,reminderResponseModel);
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListener.checkForNotificationStatus] :: Update notification status in recents success for customerId :{}, rechargeNumber : {}",reminderResponseModel.getData().getCustomer_id(),reminderResponseModel.getData().getRecharge_number());
                try {
                    kafkaProducerService.sendMessage(Long.toString(reminderResponseModel.getData().getCustomer_id()));
                }
                catch (Exception e){
                    metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECO_REFRESH_ERROR_EVENT);
                    logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListeners.updateRecentsNotificationStatus] kafka cache clean event publish error for reminderResponseModel {}, Exception",
                            reminderResponseModel,e);
                }
            }catch (Exception e){
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[ReminderListener.checkForNotificationStatus] :: Update notification status in recents failed for customerId :{}, rechargeNumber : {}",reminderResponseModel.getData().getCustomer_id(),reminderResponseModel.getData().getRecharge_number());
            }
        }
    }

    public Recents getUserData(ReminderHistory reminderHistory) {
        Recents recents=null;
        if (reminderHistory.getUserData() != null) {
            try {
                recents = JsonUtils.parseJson(reminderHistory.getUserData(), Recents.class);
            } catch (Exception e) {
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderHistory), "[ReminderListeners.prepareRecentsData]  Exception in parsing user data reminderHistory {} error {}",reminderHistory, e.getMessage());
            }
        }
        return recents;
    }

    public Double getCurrentOutstandingAmount(ReminderDataResponseModel reminderDataResponseModel) {
        if(Objects.isNull(reminderDataResponseModel)) {
            return null;
        }
        Double currentOutstandingAmount = null;
        String customerOtherInfo = reminderDataResponseModel.getCustomerOtherInfo();
        if (customerOtherInfo != null) {
            ReminderCustomerOtherInfo reminderCustomerOtherInfo = JsonUtils.parseJson(customerOtherInfo, ReminderCustomerOtherInfo.class);
            if (reminderCustomerOtherInfo != null) {
                currentOutstandingAmount = reminderCustomerOtherInfo.getCurrentOutstandingAmount();
            }
        }
        return currentOutstandingAmount;
    }

    private boolean isCCNoBillDue(ReminderResponseModel reminderResponseModel) {
        if (Objects.isNull(reminderResponseModel) || Objects.isNull(reminderResponseModel.getData())) {
            return false;
        }
        String service = reminderResponseModel.getData().getService();
        Integer status = reminderResponseModel.getData().getStatus();
        return StringUtils.equalsIgnoreCase(FINANCIAL_SERVICES, service)
            && Objects.equals(status, NO_BILL_DUE_REMINDER_STATUS);
    }
}


