package com.paytm.saga.listeners;

import com.newrelic.api.agent.Trace;
import com.paytm.saga.common.configuration.PlanValidityKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.model.*;
import com.paytm.saga.dto.PlanValidityResponseModel;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;
import com.paytm.saga.model.primaryKeys.CustomerBillPrimaryKey;
import com.paytm.saga.service.*;
import com.paytm.saga.service.DCATService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.service.RPSService;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.lang.NonNull;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import com.paytm.saga.common.metrics.MetricsHelper;


import java.text.SimpleDateFormat;
import java.util.*;

import static com.paytm.saga.common.constant.Constants.DATA_EXHAUST.*;
import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.DATA_EXHAUST_EVENT;


@Component
@DependsOn("PlanValidityKafkaPropertiesConfig")
@Conditional(PlanValidityKafkaConsumerEnableCondition.class)
public class PlanValidityListeners {
    private final Logger logger = LogManager.getLogger(PlanValidityListeners.class);

    private final DCATService dcatService;
    private final RPSService rpsService;
    private final PlanExpiryHistoryRepository planExpiryHistoryRepository;

    private RecentsRepositoryWrapperService recentsRepository;
    private final MetricsHelper metricsHelper;

    private CustomerBillRepository customerBillRepository;
    private final DropOffDBHelper dropOffDBHelper;

    private static final Integer TTL_FOR_RECENTS=365*24*60*60;

    private StatsDClient monitoringClient;
    private final RecentService recentService;
    private final CommonService commonService;

    private static final String IGNORE_EVENT="IGNORE";
    private static final String ERROR_EVENT="ERROR";
    private static final String NEW_EVENT="NEW_KAFKA_EVENT";
    private static final String PLAN_VALIDITY_CONSUMER = "PLAN_VALIDITY_CONSUMER";
    private static final String DCAT_API_ERROR="DCAT_API_ERROR";


    @Autowired
    public PlanValidityListeners(@NonNull DCATService dcatService, @NonNull RPSService rpsService, @NonNull PlanExpiryHistoryRepository planExpiryHistoryRepository,
								 @NonNull RecentsRepositoryWrapperService recentsRepository, @NonNull MetricsHelper metricsHelper,
								 @NonNull CustomerBillRepository customerBillRepository,
								 @NonNull DropOffDBHelper dropOffDBHelper, @NonNull StatsDClient monitoringClient,
								 @NonNull RecentService recentService, @NonNull CommonService commonService) {
        this.dcatService = dcatService;
        this.rpsService = rpsService;
        this.planExpiryHistoryRepository = planExpiryHistoryRepository;
        this.recentsRepository = recentsRepository;
        this.metricsHelper = metricsHelper;
        this.customerBillRepository = customerBillRepository;
        this.dropOffDBHelper = dropOffDBHelper;
        this.monitoringClient = monitoringClient;
        this.recentService = recentService;
        this.commonService = commonService;
    }
    //read this from bean of kafka config properties
    @KafkaListener(topics = "#{PlanValidityKafkaPropertiesConfig.getTopicName()}",
            groupId = "#{PlanValidityKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory ="PlanValiditykafkaListenerContainerFactory"
    )
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {
        for (String message : messages) {
            metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER, NEW_EVENT);
            logger.info("[PlanValidityListeners] Kafka Raw message {}", message);
            PlanValidityResponseModel planValidityResponseModel = null;
            long startTime = System.currentTimeMillis();
            String logStatus = "success";

            if(Objects.nonNull(message)) {
                planValidityResponseModel = JsonUtils.parseJson(message, PlanValidityResponseModel.class);
                boolean status = insertDataIntoCassandra(planValidityResponseModel);
                if(status == false) {
                    metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER, IGNORE_EVENT);
                    logger.info("[PlanValidityListeners] Message ignored Raw message {}", message);
                    logStatus = "ignore";
                }
            } else {
                logStatus = "ignore";
            }
            long endTime = System.currentTimeMillis();
            logger.info("Plan validity listener message processed with status {} in ms {}", logStatus, (endTime-startTime));
        }
        acknowledgment.acknowledge();
    }

    @Trace(dispatcher=true)
    public boolean insertDataIntoCassandra(PlanValidityResponseModel planValidityData){
        try{
            if( planValidityData.getData() == null){
                logger.error("[PlanValidityListeners.insertDataIntoCassandra]  empty data in plan validity  message {}, error {} :" ,planValidityData,"EMPTY_DATA_IN_PLAN_VALIDITY");
                return false;
            }
            if(planValidityData.getOperationType().equals("delete")){
                logger.error("[PlanValidityListeners.insertDataIntoCassandra]  empty data in plan validity  message {}, error {} :" ,planValidityData,"OPERATION_TYPE_DELETE");
                return false;
            }
            
            if(planValidityData.getOperationType().equals("update")
            		&& planValidityData.getOld().getAmount()==0 
            		&& planValidityData.getOld().getCircle() ==null
            		&& planValidityData.getOld().getCreated_at() ==null
            		&& planValidityData.getOld().getCustomer_id() ==null
            		&& planValidityData.getOld().getOperator() ==null
            		&& planValidityData.getOld().getPlan_bucket() ==null
            		&& planValidityData.getOld().getRecharge_number() ==null
            		&& planValidityData.getOld().getValidity_expiry_date() ==null
            		&& planValidityData.getOld().getCust_mobile() == null
            		&& planValidityData.getOld().getCust_email() == null
            		&& planValidityData.getOld().getOrder_ids() ==null
            		&& planValidityData.getOld().getLatest_recharge_date() ==null
            		&& planValidityData.getOld().getNotification_status() ==null
                    && planValidityData.getOld().getRemindLaterDate() == null
            		&& ((planValidityData.getData().getCategory_name() !=null
            				&& planValidityData.getData().getCategory_name().equals(Constants.PLAN_VALIDITY_DUMMY_CATEGORY))
            				|| (planValidityData.getOld().getCategory_name() !=null 
            						&& planValidityData.getOld().getCategory_name().equals(Constants.PLAN_VALIDITY_DUMMY_CATEGORY)))) {
                logger.error("[PlanValidityListeners.insertDataIntoCassandra]  empty data in plan validity  message {}, error {} :" ,planValidityData,"OLD_DETAILS_NOT_EXIST");
            	return false;
            }
            updatePlanBucketAndReconId(planValidityData);
            String recharge_number = planValidityData.getData().getRecharge_number();
            String service = planValidityData.getData().getService();
            String circle = planValidityData.getData().getCircle();
            String operator = planValidityData.getData().getOperator();
            String plan_bucket = planValidityData.getData().getPlan_bucket();
            Double amount = planValidityData.getData().getAmount();
            Date updated_at = new Date();
            Long customerid = planValidityData.getData().getCustomer_id();
            String  category_name = planValidityData.getData().getCategory_name();
            Date validity_expiry_date = null;
            Date created_at = null;
            Map<String , String> display_values = new HashMap<String, String>();
            String amountString = Double.valueOf(amount).intValue() + "";

            try{
                updated_at = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityData.getData().getUpdated_at());
            } catch (Exception e) { logger.error("[PlanValidityListeners.insertDataIntoCassandra]  updated_at::Exception", e); }

            try{
                validity_expiry_date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityData.getData().getValidity_expiry_date());
            } catch (Exception e) { logger.error("[PlanValidityListeners.insertDataIntoCassandra]  validity_expiry_date::Exception", e); }

            try{
                created_at = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityData.getData().getCreated_at());
            } catch (Exception e) { logger.error("[PlanValidityListeners.insertDataIntoCassandra]  created_at::Exception", e); }

            updated_at = DateUtils.addHours(updated_at, 5);
            updated_at = DateUtils.addMinutes(updated_at, 30);
            created_at = DateUtils.addHours(created_at, 5);
            created_at = DateUtils.addMinutes(created_at, 30);

            String categoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service);

            if(categoryId!=null){
                try{
                    if(amount > 0){
                        DCATGetPlanResponse dcatGetPlanResponse = rpsService.getPlanDetails(categoryId, Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, operator, amountString, circle, null,recharge_number,service);
                        //DCATCategoryResponseModel dcatCategoryResponseModel = dcatService.hitCategoryApi(categoryId, Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, operator, amountString, circle);
                        if(dcatGetPlanResponse!=null){
                            logger.info("[PlanValidityListeners.insertDataIntoCassandra] RPS fetch plan success   , :" + recharge_number + dcatGetPlanResponse);

                            if(dcatGetPlanResponse.getValidity()!=null) display_values.put("validity", dcatGetPlanResponse.getValidity());
                            if(dcatGetPlanResponse.getSms()!=null) display_values.put("sms",dcatGetPlanResponse.getSms() );
                            if(dcatGetPlanResponse.getData()!=null) display_values.put("data", dcatGetPlanResponse.getData());
                            if(dcatGetPlanResponse.getTalktime()!=null) display_values.put("talktime", dcatGetPlanResponse.getTalktime());
                            if(dcatGetPlanResponse.getAddon_benefit1() != null) display_values.put("addon_benefit1", dcatGetPlanResponse.getAddon_benefit1());
                            if(dcatGetPlanResponse.getAddon_benefit2() != null) display_values.put("addon_benefit2", dcatGetPlanResponse.getAddon_benefit2());
                            if(dcatGetPlanResponse.getAddon_benefit3() != null) display_values.put("addon_benefit3", dcatGetPlanResponse.getAddon_benefit3());
                            if(dcatGetPlanResponse.getAddon_benefit4() != null) display_values.put("addon_benefit4", dcatGetPlanResponse.getAddon_benefit4());
                            if(dcatGetPlanResponse.getDescription() != null) display_values.put("description", dcatGetPlanResponse.getDescription());

                        } else{
                            logger.error("[PlanValidityListeners.insertDataIntoCassandra] dcatGetPlanResponse {} for recharge_number {} , :",dcatGetPlanResponse, recharge_number);
                            metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER, DCAT_API_ERROR);
                        }
                    }

                } catch(Exception e){
                    logger.error("[PlanValidityListeners.insertDataIntoCassandra] dcatGetPlanResponse error:", e);
                    metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER, DCAT_API_ERROR);
                }
            }

            Integer TTL = getTTL(validity_expiry_date);
            PlanExpiryHistory planExpiryHistory= new PlanExpiryHistory(recharge_number, service, circle, operator, plan_bucket, updated_at, customerid, category_name,validity_expiry_date,created_at,display_values, amount);
            // not saving data exhaust cases in PlanExpiryHistory table
            if(!plan_bucket.equals(DATA_EXHAUST_PLAN_BUCKET)) {
                try {
                    planExpiryHistoryRepository.save(planExpiryHistory, TTL);
                } catch (Exception e) {
                    metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER, ERROR_EVENT);
                    logger.error("[PlanValidityListeners.insertDataIntoCassandra] Insert ERROR for data {} and ttl {} Exception {} ", planExpiryHistory, TTL, e.getMessage());
                    return false;
                }
            }

            Integer notificationStatus = planValidityData.getData().getNotification_status();
            Recents recentObj = prepareRecentsData(planExpiryHistory,planValidityData);
            recentObj.setNotificationStatus(notificationStatus);
            recentService.insertIntoRecentAndCustomerBill(recentObj, planExpiryHistory);

            try {
                if(planValidityData.getOperationType().equals(Constants.OP_INSERT) || (planValidityData.getOperationType().equals(Constants.OP_UPDATE) && Objects.nonNull(planValidityData.getOld().getValidity_expiry_date()))){
                    List<Recents> recents = recentsRepository.findBycustomerIdAndrechargeNumberAndservice(planExpiryHistory.getCustomerid(), planExpiryHistory.getRechargeNumber(), planExpiryHistory.getService());
                    if (Objects.nonNull(recents) && recents.size() != 0) {
                        for (Recents recent : recents) {
                            if (Objects.nonNull(recent.getOrderId())) {
                                dropOffHandling(planExpiryHistory);
                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception e){
                logger.error("[PlanValidityListeners.insertDataIntoCassandra] Insert into dropOff for cusomter id {}, recharge number {}, service {}, operator {} failed, Exception : {}",
                        planExpiryHistory.getCustomerid(),planExpiryHistory.getRechargeNumber(),
                        planExpiryHistory.getService(),planExpiryHistory.getOperator(),e);
                metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER, ERROR_EVENT);
            }
        }
        catch(Exception e){
            logger.error("[PlanValidityListeners.insertDataIntoCassandra] insertDataIntoCassandra data {} Exception {}", planValidityData,e.getMessage());
            metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER, ERROR_EVENT);
        }
        return true;
    }

    public CustomerBill prepareReminderClusterData(PlanExpiryHistory planExpiryHistory) {
        try {
        	CustomerBill customerBill = new CustomerBill();
            customerBill.setKey(new CustomerBillPrimaryKey());
            customerBill.getKey().setCustomerId(planExpiryHistory.getCustomerid());
            customerBill.getKey().setDue_date(planExpiryHistory.getValidity_expiry_date());
            customerBill.getKey().setService(planExpiryHistory.getService());
            customerBill.getKey().setOperator(planExpiryHistory.getOperator());
            customerBill.getKey().setRechargeNumber(planExpiryHistory.getRechargeNumber());
            customerBill.getKey().setPlanBucket(planExpiryHistory.getPlan_bucket());
            return customerBill;
        } catch (Exception e) {
            logger.error("[ReminderListeners.insertDataIntoCassandra]  prepareReminderClusterData::Exception", e);
            throw new RuntimeException(e);
        }
        
    }


    private Recents prepareRecentsData(PlanExpiryHistory planExpiryHistory, PlanValidityResponseModel planValidityData){
    	try {
            Date bill_update_time = new Date();
            Recents recents = new Recents();
            recents.setKey(new RecentsPrimaryKey());
            recents.getKey().setRechargeNumber(planExpiryHistory.getRechargeNumber());
            recents.getKey().setService(planExpiryHistory.getService());
            recents.getKey().setOperator(planExpiryHistory.getOperator());
            recents.getKey().setCustomerId(planExpiryHistory.getCustomerid());
            recents.setDueDate(planExpiryHistory.getValidity_expiry_date());
            recents.setDueAmount(planExpiryHistory.getAmount());
            if(Objects.nonNull(planValidityData.getData()) && Objects.nonNull(planValidityData.getData().getRecondId())){
                recents.setReconId(planValidityData.getData().getRecondId());
            }
            if(StringUtils.isEmpty(planExpiryHistory.getPlan_bucket()))
                recents.getKey().setPlanBucket("");
            else{
                recents.getKey().setPlanBucket(planExpiryHistory.getPlan_bucket());
                if(planExpiryHistory.getPlan_bucket().equals(DATA_EXHAUST_PLAN_BUCKET)){
                    recents.setExtra(planValidityData.getData().getExtras());
                }
            }
            recents.setBillUpdateTime(bill_update_time);
            if ((Objects.nonNull(planValidityData.getOld()) && Objects.nonNull(planValidityData.getOld().getValidity_expiry_date()) || StringUtils.equalsIgnoreCase(planValidityData.getOperationType(), Constants.OP_INSERT)))
            {
                logger.info("[PlanValidityListener.prepareRecentsData]:MarkAsPaid Info reset via Planvalidity with key={}",recents.getKey());
                recents.setMarkAsPaidTime(null);
                recents.setIsMarkAsPaid(false);
                recents.setMarkAsPaidAmount(null);
                recents.setMarkAsPaidSource(null);
            }
            recents.setUpdatedAt(bill_update_time);
            recents.setPayType(Constants.CommonConstants.PREPAID_PAYTYPE);
            recents.setCircle(planExpiryHistory.getCircle());
            recents.setPlanName(planExpiryHistory.getCategory_name());
            if(Boolean.TRUE.equals(isNewBillUpdated(planValidityData))){
                recents.setNewBillUpdatedAt(new Date());
            }
            recents.setProductId(planValidityData.getData().getProductId());
            Date remindMeLaterDate;
            try{
                SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                sdf.setLenient(false);
                if(Objects.nonNull(planValidityData.getData().getRemindLaterDate())) {
                    remindMeLaterDate = sdf.parse(planValidityData.getData().getRemindLaterDate());
                    recents.setRemindLaterDate(remindMeLaterDate);
                }
            } catch (Exception e) { logger.error("[PlanValidityListener.prepareRecentsData]  remindMeLaterDate::Exception", e); }

            return recents;
    	}catch(Exception e) {
    		logger.error("[ReminderListeners.prepareRecentsData]  Exception", e);
    		throw new RuntimeException(e);
    	}
	}

    public Integer getTTL(Date validity_expiry_date){
        if(validity_expiry_date == null){
            return Constants.CommonConstants.PLAN_VALIDITY_DEFAULT_TTL;
        }
        Integer secondsDiff = Math.abs(Math.toIntExact(validity_expiry_date.getTime()/1000 - (new Date()).getTime()/1000));
        return secondsDiff + 7*24*60*60;
    }
    public Boolean isNewBillUpdated(PlanValidityResponseModel planValidityData){
        if(planValidityData.getOperationType().equals(Constants.OP_INSERT) || (planValidityData.getOperationType().equals(Constants.OP_UPDATE) && Objects.nonNull(planValidityData.getOld().getValidity_expiry_date()))){
            if(planValidityData.getData().getValidity_expiry_date() != null)
                return true;
        }
        return false;
    }
    public void dropOffHandling(PlanExpiryHistory planExpiryHistory) {
        DropOff dropOff = prepareDropOff(planExpiryHistory);
        try {
            dropOffDBHelper.insertDropOff(dropOff);
            logger.info("PlanValidityListeners.dropOffhandling: Insert into dropOff success for cust_id: {} , rechargeNumber: {}",dropOff.getCustomerId(),dropOff.getRechargeNumber());
        } catch (Exception e) {
            logger.error("PlanValidityListeners.dropOffhandling: failed to insert into dropOff");
        }
    }
    public DropOff prepareDropOff(PlanExpiryHistory planExpiryHistory) {
        DropOff dropOff = new DropOff();
        Map<String, String> billsObj = new HashMap<>();
        dropOff.setCustomerId(planExpiryHistory.getCustomerid());
        dropOff.setRechargeNumber(planExpiryHistory.getRechargeNumber());
        dropOff.setService(planExpiryHistory.getService());
        dropOff.setOperator(planExpiryHistory.getOperator());
        dropOff.setCircle(planExpiryHistory.getCircle());
        dropOff.setTransactionTime(planExpiryHistory.getUpdated_at());
        dropOff.setAmount(Constants.DROPOFF_DEFAULT_AMOUNT);
        dropOff.setEventType(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE);
        dropOff.setPaytype(Constants.PREPAID_PAYTYPE);
        dropOff.setStatus(Constants.RECHARGE_SUCCESS_STATUS);
        dropOff.setIn_response_code(Constants.SUCCESS_IN_CODE);
        dropOff.setIsAutomatic(null);
        billsObj.put(Constants.PLANVALIDITY_NEW_EXPIRY,Constants.TRUE_STRING);
        billsObj.put(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY,planExpiryHistory.getPlan_bucket());
        dropOff.setBillsObj(billsObj);
        return dropOff;
    }

    private boolean isDataExhaust(PlanValidityResponseModel planValidityData){
        try {
            if (planValidityData.getData().getExtras() != null) {
                JSONObject extras = new JSONObject(planValidityData.getData().getExtras());
                if (extras.has(EXHAUSTED_DATE)){
                    Date exhaustedDate = DateUtil.stringToDate((String) extras.get(EXHAUSTED_DATE), Constants.DATA_EXHAUST.EXHAUSTED_DATE_FORMAT);
                    if(exhaustedDate != null && !exhaustedDate.after(DateUtil.getEndOfDayDate(new Date()))){
                        planValidityData.getData().setPlan_bucket(DATA_EXHAUST_PLAN_BUCKET);
                        planValidityData.getData().setValidity_expiry_date(DateUtil.formatDate(exhaustedDate,"yyyy-MM-dd HH:mm:ss"));
                       return true;
                    }
                }
            }
        }catch (Exception ex){
            logger.info("PlanValidityListeners, getPlanBucket: exception while finding plan bucket, plan bucket - {}", planValidityData.getData().getPlan_bucket(), ex);
        }
        return false;
    }

    private void updatePlanBucketAndReconId(PlanValidityResponseModel planValidityData){
        try {
            if (planValidityData.getData().getExtras() != null) {
                JSONObject extras = new JSONObject(planValidityData.getData().getExtras());
                if (extras.has(EXHAUSTED_DATE)){
                    Date exhaustedDate = DateUtil.stringToDate((String) extras.get(EXHAUSTED_DATE), Constants.DATA_EXHAUST.EXHAUSTED_DATE_FORMAT);
                    if(exhaustedDate != null && !exhaustedDate.after(DateUtil.getEndOfDayDate(new Date()))){
                        planValidityData.getData().setPlan_bucket(DATA_EXHAUST_PLAN_BUCKET);
                        planValidityData.getData().setValidity_expiry_date(DateUtil.formatDate(exhaustedDate,"yyyy-MM-dd HH:mm:ss"));
                        metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER, DATA_EXHAUST_EVENT);
                    }
                }
                if (extras.has("recon_id")) {
                    planValidityData.getData().setReconId(extras.getString("recon_id"));
                }
            }
        }catch (Exception ex){
            logger.info("PlanValidityListeners, getPlanBucket: exception while finding plan bucket, plan bucket - {}", planValidityData.getData().getPlan_bucket(), ex);
        }
    }
}


