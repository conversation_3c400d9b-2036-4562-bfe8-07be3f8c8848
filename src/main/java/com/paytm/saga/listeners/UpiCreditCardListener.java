package com.paytm.saga.listeners;

import com.paytm.saga.common.configuration.UpiCreditCardKafkaEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.model.UpiCreditCardMessage;
import com.paytm.saga.service.UpiCreditCardService;
import com.paytm.saga.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Component
@DependsOn("upiCreditCardKafkaConfig")
@Conditional(UpiCreditCardKafkaEnableCondition.class)
public class UpiCreditCardListener {

    private final Logger logger = LogManager.getLogger(UpiCreditCardListener.class);

    private final UpiCreditCardService upiCreditCardService;
    private final MetricsHelper metricsHelper;

    @Autowired
    public UpiCreditCardListener(UpiCreditCardService upiCreditCardService, MetricsHelper metricsHelper) {
        this.upiCreditCardService = upiCreditCardService;
        this.metricsHelper = metricsHelper;
    }

    @KafkaListener(topics = "#{upiCreditCardKafkaConfig.getTopicName()}",
            groupId = "#{upiCreditCardKafkaConfig.getConsumerGroup()}",
            containerFactory ="upiCreditCardKafkaListenerContainerFactory")
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {
        if (!CollectionUtils.isEmpty(messages)) {
            for (String message : messages) {
                metricsHelper.recordSuccessRate(Constants.UPI_CREDIT_CARD_CONSUMER, Constants.NEW_KAFKA_EVENT);
                if (StringUtils.isNotBlank(message)) {
                    processMessage(message);
                }
            }
        }
        acknowledgment.acknowledge();
    }

    private void processMessage(String message){
        try {
            logger.info("UPI-CC-Listener::ProcessMessage - Processing message {}", message);
            UpiCreditCardMessage upiCreditCardMessage = JsonUtils.parseJson(message, UpiCreditCardMessage.class);
            Integer rp = FeatureConfigCache.getInstance().getInteger(Constants.UPI_CREDIT_CARD_ROLLOUT_PERCENTAGE);
            if (Objects.nonNull(rp) && Long.parseLong(upiCreditCardMessage.getCustomerId()) % 100 > rp) {
                logger.info("UPI-CC-Listener::ProcessMessage - Skipping message for customerId {} as it is not in the rollout percentage", upiCreditCardMessage.getCustomerId());
                metricsHelper.recordSuccessRate(Constants.UPI_CREDIT_CARD_CONSUMER, Constants.SKIPPED_EVENT);
                return;
            }
            upiCreditCardService.processUpiCreditCardMessage(upiCreditCardMessage, message, Constants.UPI_CREDIT_CARD_CONSUMER);
        } catch (Exception e) {
            logger.error("UPI-CC-Listener::ProcessMessage - Error while processing message", e);
            metricsHelper.recordSuccessRate(Constants.UPI_CREDIT_CARD_CONSUMER, Constants.ERROR_EVENT);
        }
    }


}
