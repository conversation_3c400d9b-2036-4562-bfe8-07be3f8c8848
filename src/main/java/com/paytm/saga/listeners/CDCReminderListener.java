package com.paytm.saga.listeners;

import com.newrelic.api.agent.Trace;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.PrometheusConstants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.CustomerBillDao;
import com.paytm.saga.dto.cdc.DoubleEntity;
import com.paytm.saga.dto.cdc.LongEntity;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.Recents;
import com.paytm.saga.service.*;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.*;
import com.paytm.saga.model.primaryKeys.CustomerBillPrimaryKey;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;

import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET;
import static com.paytm.saga.common.constant.Constants.DATA_EXHAUST.EXHAUSTED_DATE;
import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.*;

@Component
public class CDCReminderListener {
    private final CustomLogger logger = CustomLogManager.getLogger(CDCReminderListener.class);
    private final MetricsHelper metricsHelper;
    private final CommonService commonService;
    private final RecentsRepositoryWrapperService recentsRepository;
    private final RecentService recentService;
    private final CustomerBillRepository customerBillRepository;
    private ServiceConfig serviceConfig;
    private CustomerBillDao customerBillDao;
    private AESUtil aesUtil;


    private final RecentDataToKafkaService recentDataToKafkaService;

    private KafkaProducerService kafkaProducerService;

    @Autowired
    public CDCReminderListener(@NonNull MetricsHelper metricsHelper, CommonService commonService, RecentsRepositoryWrapperService recentsRepository, RecentService recentService, CustomerBillRepository customerBillRepository, RecentDataToKafkaService recentDataToKafkaService, @NonNull KafkaProducerService kafkaProducerService, @NonNull ServiceConfig serviceConfig,@NonNull CustomerBillDao customerBillDao, AESUtil aesUtil) {
        this.metricsHelper = metricsHelper;
        this.commonService = commonService;
        this.recentsRepository = recentsRepository;
        this.recentService = recentService;
        this.customerBillRepository = customerBillRepository;
        this.recentDataToKafkaService = recentDataToKafkaService;
        this.kafkaProducerService = kafkaProducerService;
        this.serviceConfig = serviceConfig;
        this.customerBillDao=customerBillDao;
        this.aesUtil = aesUtil;
    }


    @Trace(dispatcher = true)
    public void insertDataIntoCassandra(ReminderCDC reminderCDC) {
        try {
            Long customerId = reminderCDC.getAfter().getCustomerId().getValue();
            String rechargeNumber = reminderCDC.getAfter().getRechargeNumber().getValue();
            String service = reminderCDC.getAfter().getService().getValue();
            String operator = reminderCDC.getAfter().getOperator().getValue();
            boolean prepaidQualified = RecentUtils.isPrepaidQualified(service, operator, RecentUtils.getExtraForReminder(reminderCDC));

            if (Boolean.TRUE.equals(isSkippable(reminderCDC))) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[CDCReminderListener.insertDataIntoCassandra] : skipping payload {}", reminderCDC);
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, IGNORE_EVENT);
                return;
            }
            if (RecentUtils.isFastagService(reminderCDC)) {
                recentService.updateRecentFromReminderCdcConsumerForSMSparsing(reminderCDC);
                if(!RecentUtils.isFastaglowBalanceSkippble(reminderCDC)){
                    reminderCDC.getAfter().getOperator().setValue(Constants.FASTAG_LOW_BALANCE_OPERATOR);
                    if (reminderCDC.getAfter().getDueDate() == null) {
                        reminderCDC.getAfter().setDueDate(new LongEntity(new Date().getTime()));
                    } else if (reminderCDC.getAfter().getDueDate().getValue() == null) {
                        reminderCDC.getAfter().getDueDate().setValue(new Date().getTime());
                    }
                    if (reminderCDC.getAfter().getAmount() == null) {
                        reminderCDC.getAfter().setAmount(new DoubleEntity());
                        if(!prepaidQualified) {
                            reminderCDC.getAfter().getAmount().setValue(Constants.FASTAG_LOW_BALANCE_DEFAULT_AMOUNT);
                        }
                    } else if (!prepaidQualified && (reminderCDC.getAfter().getAmount().getValue() == null || reminderCDC.getAfter().getAmount().getValue() < Constants.FASTAG_LOW_BALANCE_MIN_AMOUNT)) {
                        reminderCDC.getAfter().getAmount().setValue(Constants.FASTAG_LOW_BALANCE_DEFAULT_AMOUNT);
                    }
                    List<String> tags=new ArrayList<>();
                    tags.add("event_type:low_balance_new_event");
                    tags.add("operator:"+reminderCDC.getAfter().getOperator());
                    tags.add("service:"+reminderCDC.getAfter().getService());
                    metricsHelper.recordSuccessRateForMultipleTags(Constants.CDC_CONSUMER_STATS, tags);
                }else{
                    metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, PrometheusConstants.FASTAG.LOW_BALANCE_SKIPPABLE);
                    logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[CDCReminderListener.insertDataIntoCassandra] : fastag skipping payload {}", reminderCDC);
                    return;
                }
            }
            if (reminderCDC.getOp().equals(Constants.CDC.DELETE_OP) && reminderCDC.getAfter().getService().getValue().equalsIgnoreCase(Constants.FINANCIAL_SERVICES)) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[CDCReminderListenre.insertDataIntoCassandra] :: Deleting CC record as Op type is delete for SMS data in case of CC");
                Recents recents = new Recents();
                RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
                recentsPrimaryKey.setOperator(operator);
                recentsPrimaryKey.setCustomerId(customerId);
                if(reminderCDC.getAfter().getIsEncrypted() != null && reminderCDC.getAfter().getIsEncrypted() == 1) {
                    recentsPrimaryKey.setRechargeNumber(aesUtil.encrypt(rechargeNumber));
                } else {
                    recentsPrimaryKey.setRechargeNumber(rechargeNumber);
                }
                recentsPrimaryKey.setService(service);
                recentsPrimaryKey.setPlanBucket("");
                recents.setKey(recentsPrimaryKey);
                recents.setUpdatedAt(new Date());

                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, DELETE_EVENT);
                recentDataToKafkaService.setRecentConsumerSource(recents,Constants.CDC_REMINDER_CONSUMER);
                recentsRepository.deleteRecentByCustomerIdAndServiceAndRechargeCdcListener(recents, customerId, service, rechargeNumber, operator, "");
                removeCacheForCCDeleteEvent(customerId);
                return;
            }
            if (reminderCDC.getOp().equals(Constants.CDC.DELETE_OP)) {
                recentService.unsetBillForDeleteValidation(reminderCDC);
                return;
            }
            if(RecentUtils.checkForDataExhaustPlanInReminderCDC(reminderCDC)){
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, DATA_EXHAUST_EVENT);
                if(reminderCDC.getAfter().getDueDate()==null){
                    reminderCDC.getAfter().setDueDate(new LongEntity());
                }
                Map<String, String> extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
                if (extras != null && extras.containsKey(Constants.DATA_EXHAUST.EXHAUSTED_DATE)) {
                    Date exhaustedDate = DateUtil.stringToDate(extras.get(Constants.DATA_EXHAUST.EXHAUSTED_DATE), Constants.DATA_EXHAUST.EXHAUSTED_DATE_FORMAT);
                    reminderCDC.getAfter().getDueDate().setValue(exhaustedDate.getTime());
                }
                try{
                    if(Objects.nonNull(reminderCDC.getAfter().getCustomerOtherInfo())
                            && Objects.nonNull(reminderCDC.getAfter().getCustomerOtherInfo().getValue())){
                        JSONObject jsonObject=new JSONObject(reminderCDC.getAfter().getCustomerOtherInfo().getValue());
                        if(jsonObject.has("sms_date_time")){
                            extras.put("sms_date_time",new StringBuilder().append(jsonObject.get("sms_date_time")).toString());
                            reminderCDC.getAfter().getExtra().setValue(new JSONObject(extras).toString());

                        }
                    }
                }catch(Exception e){
                    logger.error(ServiceWrapperUtil.findServiceWrapper(service), "unable to parse sms_date_time in data-exhaust flow",e);
                }


            }

            //if validation delete event the unset due date

            if (Boolean.TRUE.equals(checkValidBillForCustomerBills(reminderCDC))) {
                CustomerBill customerBill = prepareCustomerBillsData(reminderCDC);
                Integer ttl = commonService.getSmartReminderTTL(customerBill);

                if (RecentUtils.checkExhaustEvent(reminderCDC)) {
                    ttl = TTLExhaustEventUtil.getTTL(customerBill.getKey().getService(), customerBill.getKey().getDue_date());
                }

                if(ttl > 0) {
                    metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, SAVE_TO_CUSTOMER_BILLS);
                    customerBillDao.save(customerBill, ttl);
                    logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[CDCReminderListenre.insertDataIntoCassandra] :: Inserting bill details success in customerBills for custId {} and rechargeNumber {}", customerId, rechargeNumber);
                }
            }
            recentService.updateRecentFromReminderCdcConsumer(reminderCDC);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListener.insertDataIntoCassandra] :: insertDataIntoCassandra failed for customerId {} and rechargeNumber {} and error {}", reminderCDC.getAfter().getCustomerId().getValue(), reminderCDC.getAfter().getRechargeNumber().getValue(), e);
            metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, ERROR_EVENT);
        }
    }

    private CustomerBill prepareCustomerBillsData(ReminderCDC reminderCDC) {
        CustomerBill customerBill = new CustomerBill();
        customerBill.setKey(new CustomerBillPrimaryKey());
        customerBill.getKey().setCustomerId(reminderCDC.getAfter().getCustomerId().getValue());
        customerBill.getKey().setRechargeNumber(reminderCDC.getAfter().getRechargeNumber().getValue());
        customerBill.getKey().setOperator(reminderCDC.getAfter().getOperator().getValue());
        customerBill.getKey().setService(reminderCDC.getAfter().getService().getValue());
        if(RecentUtils.isWhitelistedCustIdForMobileNonRUPersist(reminderCDC.getAfter().getCustomerId().getValue(),serviceConfig)&& Objects.nonNull(reminderCDC.getAfter().getPaytype())&&Constants.PREPAID_PAYTYPE.equalsIgnoreCase(reminderCDC.getAfter().getPaytype().getValue())&&Objects.nonNull(reminderCDC.getAfter().getService())&&Constants.ServiceTypeConstants.MOBILE.equalsIgnoreCase(reminderCDC.getAfter().getService().getValue())) {
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "prepareCustomerBillsData::setting planbucket for whitelisted custIds {}",reminderCDC.getAfter().getCustomerId().getValue());
            customerBill.getKey().setPlanBucket(RecentUtils.getPlanBucketForCDCPayload(reminderCDC));
        }
        else{
            customerBill.getKey().setPlanBucket("");
        }
        // for data exhaust cases
        if (reminderCDC.getAfter().getExtra() != null && reminderCDC.getAfter().getExtra().getValue() != null) {
            Map<String,String> extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
            if (extras != null && extras.containsKey(EXHAUSTED_DATE)){
                Date exhaustedDate = DateUtil.stringToDate(extras.get(EXHAUSTED_DATE), Constants.DATA_EXHAUST.EXHAUSTED_DATE_FORMAT);
                if(exhaustedDate != null && !exhaustedDate.after(DateUtil.getEndOfDayDate(new Date()))){
                    customerBill.getKey().setPlanBucket(DATA_EXHAUST_PLAN_BUCKET);
                }
            }
        }
        if (reminderCDC.getAfter().getDueDate()==null || reminderCDC.getAfter().getDueDate().getValue()==null ) {
            // set Partial Bill dummy due date
            metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, PARTIAL_BILL);
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListener.prepareCustomerBillsData] : partial  bill with no date, customerId: {}",reminderCDC.getAfter().getCustomerId().getValue());
            customerBill.getKey().setDue_date(DateUtils.addMonths(new Date(), 1));
        } else {
            if(reminderCDC.getAfter().getDueAmount()==null || reminderCDC.getAfter().getDueAmount().getValue()==null) {
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListener.prepareCustomerBillsData] : partial bill with no amount, customerId: {}",reminderCDC.getAfter().getCustomerId().getValue());
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, PARTIAL_BILL);
            }
            customerBill.getKey().setDue_date(new Date(reminderCDC.getAfter().getDueDate().getValue()));
        }

        return customerBill;
    }

    private Boolean isSkippable(ReminderCDC reminderCDC) {
        if (reminderCDC.getOp().equalsIgnoreCase(Constants.CDC.DELETE_OP)
                && (Constants.EVENTSOURCE_P2P.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue()))) {
            //for validation delete event unset the bill details in validation event
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "Not Skipping as op type is delete and event_source = {}", reminderCDC.getAfter().getEventSource().getValue());
            return false;
        }
        if(Objects.nonNull(reminderCDC.getAfter().getStatus()) && Arrays.asList(Constants.CommonConstants.REMINDER_CDC_RECOVERY_SKIP_STATUS).contains(reminderCDC.getAfter().getStatus().getValue())){
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "skipping data reason REMINDER_SKIP_STATUS");
            return true;
        }
        if (reminderCDC.getOp().equalsIgnoreCase(Constants.CDC.INSERT_OP)) {
            if (Constants.EVENTSOURCE_P2P.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue())) {
                if (reminderCDC.getAfter().getPaymentDate() == null && reminderCDC.getAfter().getCreatedAt() == null) {
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "Both paymentDate or createdAt is null");
                    return true;
                } else if (reminderCDC.getAfter().getCreatedAt() == null) {
                    if (reminderCDC.getAfter().getPaymentDate().getValue() == null) {
                        logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "paymentDateValue is null");
                        return true;
                    }
                } else if (reminderCDC.getAfter().getPaymentDate() == null) {
                    if (reminderCDC.getAfter().getCreatedAt().getValue() == null) {
                        logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "createdAt is null");
                        return true;
                    }
                } else if (reminderCDC.getAfter().getPaymentDate().getValue() == null && reminderCDC.getAfter().getCreatedAt().getValue() == null) {
                    logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "Both paymentDate value or createdAt value is null");
                    return true;
                }
            }

            if (Constants.EVENTSOURCE_SMS.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue()) || Constants.EVENTSOURCE_VALIDATION.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue())) {
                if (reminderCDC.getAfter().getCreateAt() == null)
                    return true;
                if (reminderCDC.getAfter().getCreateAt().getValue() == null)
                    return true;
            }
        }
        if (reminderCDC.getAfter().getNotificationStatus() != null && reminderCDC.getAfter().getNotificationStatus().getValue() != null && reminderCDC.getAfter().getNotificationStatus().getValue().equals(0)) {
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "Skipping as notification status is 0");
            return true;
        }
        if (reminderCDC.getAfter().getStatus() != null && reminderCDC.getAfter().getStatus().getValue() != null && reminderCDC.getAfter().getStatus().getValue().equals(15)) {
            logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "Skipping as status 15");
            return true;
        }
        if (RecentUtils.isFastagService(reminderCDC)) {
            return false; // if fastag don't skip else skip
        }

        return false;
    }

    public Boolean checkValidBillForCustomerBills(ReminderCDC reminderCDC) {
        if (Constants.EVENTSOURCE_P2P.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue())) {
            if (reminderCDC.getAfter().getDueDate() != null && reminderCDC.getAfter().getDueDate().getValue() != null &&
                    reminderCDC.getAfter().getDueAmount() != null && reminderCDC.getAfter().getDueAmount().getValue() != null
                    && reminderCDC.getAfter().getDueAmount().getValue() > 0)
                return true;
        }
        if(Constants.EVENTSOURCE_SMS.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue())
        || Constants.EVENTSOURCE_VALIDATION.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue())){
            if(isUpdatedSourceCAIdentified(reminderCDC) && (reminderCDC.getAfter().getDueDate() == null || reminderCDC.getAfter().getDueDate().getValue() == null)){
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, Constants.DATA_SOURCE_CA_IDENTIFY_PARTIAL);
                return false;
            }
            return true;
        }
        logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "checkValidBillForCustomerBills, eventSource - {}, dueDate - {}", reminderCDC.getAfter().getEventSource(), reminderCDC.getAfter());
        return false;
    }

    private void removeCacheForCCDeleteEvent(Long customerId) {
        try {
            kafkaProducerService.sendMessage(Long.toString(customerId));

        } catch (Exception e) {
            metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, Constants.CDC_EVICT_CACHE_ERROR);
            logger.error("[CDCReminderListenre.removeCacheForCCDeleteEvent]: Exception occured in sending delete cache event to evict_cache kafka for customerId = {} , exception = {}", customerId, e.getMessage());
        }

    }

    public Boolean isUpdatedSourceCAIdentified(ReminderCDC reminderCDC){
        Map<String,String> extras = null;
        if(Objects.nonNull(reminderCDC.getAfter().getExtra()) && Objects.nonNull(reminderCDC.getAfter().getExtra().getValue())){
            try {
                extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
            }catch (Exception e){
                logger.error(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListener.isCreatedSourceValidation] Unable to parse extras due to, error ",e);
            }
            if(extras == null)
                return false;
            if (extras.containsKey(Constants.UPDATED_DATA_SOURCE) && extras.get(Constants.UPDATED_DATA_SOURCE).endsWith(Constants.CA_IDENTIFY_SUFFIX)) {
                metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, Constants.DATA_SOURCE_CA_IDENTIFY);
                return true;
            }
        }
        return false;
    }




}
