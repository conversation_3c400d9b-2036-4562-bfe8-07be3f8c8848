package com.paytm.saga.listeners;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.EvictCacheKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.EvictCacheRequest;
import com.paytm.saga.service.EvictCacheService;
import com.paytm.saga.service.GenericRestClient;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.ValidationUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import jakarta.validation.ConstraintViolationException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.MetricConstants.*;

@Component
@DependsOn("EvictCacheKafkaConfig")
@Conditional(EvictCacheKafkaConsumerEnableCondition.class)
public class EvictCacheListener {

    private final CustomLogger logger = CustomLogManager.getLogger(EvictCacheListener.class);

    @Autowired
    private GenericRestClient restTemplateService;

    @Autowired
    private EvictCacheService evictCacheService;

    @Autowired
    @Qualifier("monitoringClient")
    private StatsDClient monitoringClient;

    private static final String NEW_EVENT="NEW_KAFKA_EVENT";
    private static final String EVICT_CACHE_CONSUMER = "EVICTCACHE";

    @KafkaListener(topics = "#{EvictCacheKafkaConfig.getTopicName()}", groupId = "#{EvictCacheKafkaConfig.getConsumerGroup()}", containerFactory = "EvictCacheListenerContainerFactory")
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {
        logger.info("[EvictCacheListener.listen] Size of batch is {}", messages.size());
        for (String message : messages) {
            EvictCacheRequest evictCacheRequest;
            pushToDD(NEW_EVENT);
            if (Objects.nonNull(message)) {
                try {
                    evictCacheRequest = JsonUtils.parseJson(message, EvictCacheRequest.class);
                    ValidationUtils.validate(evictCacheRequest);
                    logger.info("[EvictCacheListener.listen] cleaning cache for cachekey : {} ", evictCacheRequest.getCacheKey());
                    evictCacheService.evictCache(evictCacheRequest);
                    pushToDD(CONSUMER_EVENTS_SUCCESS_METRIC);
                } catch (ConstraintViolationException exception) {
                    logger.error("[EvictCacheListener.listen] Invalid message body {} , message {} ", exception.getMessage(), message);
                    pushToDD(CONSUMER_EVENTS_INVALID_METRIC);
                } catch (Exception exception) {
                    logger.error("[EvictCacheListener.listen] Error {}", exception.getMessage());
                    pushToDD(CONSUMER_EVENTS_ERROR_METRIC);
                }
            } else {
                logger.debug("[EvictCacheListener.listen] NULL OBJECT");
            }
        }
        logger.info("[EvictCacheListener.listen] Acknowledging message");
        acknowledgment.acknowledge();
    }

    private void pushToDD(String metricName) {
        String[] tags = new String[3];
        tags[0] = Constants.MetricConstants.SERVICE_NAME_KEY + ":" + EVICT_CACHE_CONSUMER;
        monitoringClient.incrementCounter(metricName, tags);
    }

}
