package com.paytm.saga.listeners;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.ReconKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.ReconRecordValidationException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.RecentDataToKafkaModel;
import com.paytm.saga.dto.RecentReconDataCache;
import com.paytm.saga.dto.RecentReconResponse;
import com.paytm.saga.dto.builder.RecentsBuilder;
import com.paytm.saga.dto.cdc.OrdersList;
import com.paytm.saga.dto.cdc.RecentCDC;
import com.paytm.saga.dto.cdc.ReconDataBundle;
import com.paytm.saga.dto.es.ESResponse;
import com.paytm.saga.dto.es.HitsEs;
import com.paytm.saga.dto.oms.Fulfillment;
import com.paytm.saga.dto.oms.OMSObject;
import com.paytm.saga.dto.oms.OMSResponse;
import com.paytm.saga.dto.oms.Order;
import com.paytm.saga.model.Recents;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.service.KafkaProducerService;
import com.paytm.saga.service.UserAgentService;
import com.paytm.saga.service.external.EsService;
import com.paytm.saga.service.external.OmsService;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.ServiceWrapperUtil;
import com.paytm.saga.util.ValidationUtils;
import com.paytm.saga.validators.ReconOMSValidator;
import com.paytm.saga.validators.ReconRecentValidator;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.util.*;

import static com.paytm.saga.common.constant.Constants.MetricConstants.RECON_LISTENER;

@Component
@DependsOn("ReconKafkaConfig")
@Conditional(ReconKafkaConsumerEnableCondition.class)
public class ReconDataListener {
    private final CustomLogger logger = CustomLogManager.getLogger(ReconDataListener.class);

    @Autowired
    @Qualifier("monitoringClient")
    private StatsDClient monitoringClient;

    @Autowired
    private OmsService omsService;

    @Autowired
    private EsService esService;

    @Autowired
    private RecentsRepositoryWrapperService recentsRepository;

    @Autowired
    private UserAgentService userAgentService;
    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private MetricsHelper metricsHelper;

    private RetryTemplate retryTemplate = new RetryTemplate();
    @PostConstruct
    public void init() {
        SimpleRetryPolicy simpleRetryPolicy = new SimpleRetryPolicy();
        simpleRetryPolicy.setMaxAttempts(3);
        FixedBackOffPolicy fixedBackOff = new FixedBackOffPolicy();
        fixedBackOff.setBackOffPeriod(50);
        retryTemplate.setBackOffPolicy(fixedBackOff);
    }


    @KafkaListener(topics = "#{ReconKafkaConfig.getTopicName()}", groupId = "#{ReconKafkaConfig.getConsumerGroup()}", containerFactory = "ReconListenerContainerFactory")
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {
        Long batchStartTime = System.currentTimeMillis();
        pushCountToDD(RECON_LISTENER, messages.size(), "BATCH_COUNT");
        RecentCDC recentCDCEvent = null;
        int count = 0;
        OrdersList ordersList=new OrdersList();
        Map<Long,ReconDataBundle> dataBundleMap = new HashMap<>();
        for (String message : messages) {
            try {

                RecentDataToKafkaModel recentDataToKafkaModel = JsonUtils.parseJsonWithCustomMapper(message, RecentDataToKafkaModel.class);
                if(recentDataToKafkaModel == null) {
                    logger.error("[ReconDataListener.listen] Error in payload data {}", message);
                    pushToDD("ERROR_IN_PAYLOAD");
                    continue;
                }

                recentCDCEvent = new RecentCDC(recentDataToKafkaModel);

                ValidationUtils.validate(recentCDCEvent);
                createBundle(recentCDCEvent,ordersList,dataBundleMap);
                count++;
            } catch (Exception exception) {
                logger.error("[ReconDataListener.listen] Error processing data {}", exception.getMessage(), exception);
                pushToDD("ERROR_IN_PROCESSING");
            }
        }
        try{
            createBundleData(ordersList,dataBundleMap);
            this.validateData(dataBundleMap);
        }catch(Exception exception){
            logger.error("[ReconDataListener.listen] Error in validating data {} with error {}", dataBundleMap, exception);
            pushToDD("ERROR_IN_VALIDATING");
        }
        pushCountToDD(RECON_LISTENER, count, "BATCH_SUCCESS");
        Long batchEndTime = System.currentTimeMillis();
        logger.info("[ReconDataListener.listen] Acknowledging message latency {} count {}",(batchEndTime-batchStartTime), count);
        acknowledgment.acknowledge();
    }

    private Recents fetchRecentFromDB(Recents recents,String operationType) {
        Recents recordFromDB = null;
        try {
            if (Constants.CDC.UPDATE_OP.equalsIgnoreCase(operationType) && ReconRecentValidator.isNeedToFetchFromDB(recents)) {
                return recentsRepository.findById(recents.getKey());
            }else{
                logger.info(ServiceWrapperUtil.findServiceWrapper(recents), "fetchRecentFromDB non update operation for key : {}",recents.getKey());
            }
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "Exception inside fetchRecentFromDB for recentPacket {} with error {}",recents, e);
            throw e;
        }
        return recordFromDB;
    }

    private Long fetchOrdersIdForValidationWithOMS(Recents recent) {
        try {
            Date txnCutOffDate = DateUtils.addMonths(new Date(), -Constants.RECON.TXN_CUTOFF_MONTH);
            if(txnCutOffDate.compareTo(recent.getTxnTime()) <= 0){
                return recent.getOrderId();
            }
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(recent), "Exception inside fetchOrdersIdForValidationForOMS recentFromDB {} error {}",recent, e);
        }
        return null;
    }
    private Long fetchOrdersIdForValidationWithES(Recents recent) {
        try {
            Date esCutoffDate = DateUtils.addMonths(new Date(), -Constants.RECON.TXN_CUTOFF_MONTH_ES);
            if(esCutoffDate.compareTo(recent.getTxnTime()) < 0){
                return recent.getOrderId();
            }
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(recent), "Exception inside fetchOrdersIdForValidationFromES recentFromDB {} error {}",recent, e);
        }
        return  null;
    }

    private void preProcess(RecentCDC recentCDCEvent,OrdersList ordersList,Map<Long,ReconDataBundle> dataBundleMap) {
        Recents recents = RecentsBuilder.fromRecentCDC.apply(recentCDCEvent);
        if(Boolean.FALSE.equals(userAgentService.isAgent(recents.getKey().getCustomerId()))){
            markIfCustomerAgent(recents.getKey().getCustomerId());
        }
        Recents recordFromDB = null;
        try {
            if (Constants.CDC.UPDATE_OP.equalsIgnoreCase(recentCDCEvent.getOp()) && ReconRecentValidator.isNeedToFetchFromDB(recents)) {
                recordFromDB = this.fetchRecentFromDB(recents,recentCDCEvent.getOp());
                if (recordFromDB == null) {
                    logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "ReconDataListener reconcileEvent unable to find record {} ", recentCDCEvent);
                    pushToDD("RECORD_NOT_FOUND_DB");
                    return;
                } else {
                    updateFromDBRecord(recents, recordFromDB);
                }
            }
            if (!ReconRecentValidator.isValidForRecon(recents)) {
                pushToDD("IGNORE");
                logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "ReconDataListener reconcileEvent Ignoring as not valid for recon{} ", recentCDCEvent);
                return;
            }
            try{
                ReconRecentValidator.checkMandatoryFields(recents);
            }catch(ReconRecordValidationException reconRecordValidationException){
                /*List<String> errors = new ArrayList<>();
                errors.add(reconRecordValidationException.getMessage());
                RecentReconResponse response = new RecentReconResponse();
                response.setKey(recents.getKey().toString());
                response.setErrors(errors);
                RecentReconDataCache.getInstance().pushReportData(response);*/
                logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "ReconDataListener reconcileEvent Error as not valid for recon{} {} ", reconRecordValidationException, recentCDCEvent);
                pushToDD("MISMATCH");
                return;
            }
            Long orderIdForOMS = fetchOrdersIdForValidationWithOMS(recents);
            ReconDataBundle reconDataBundle=new ReconDataBundle();
            reconDataBundle.setRecordFromDB(recordFromDB);
            reconDataBundle.setRecordFromKafka(recents);
            reconDataBundle.setDbOpType(recentCDCEvent.getOp());
            if(Objects.nonNull(orderIdForOMS)){
                logger.info(ServiceWrapperUtil.findServiceWrapper(recents), " Validating order id for OMS {}",orderIdForOMS);
                ordersList.getOrderIdsForOms().add(orderIdForOMS);
                dataBundleMap.put(orderIdForOMS,reconDataBundle);
            }else {
                Long orderIdForES = fetchOrdersIdForValidationWithES(recents);
                if(Objects.nonNull(orderIdForES)){
                    logger.info(ServiceWrapperUtil.findServiceWrapper(recents), " Validating order id for ES {}",orderIdForES);
                    ordersList.getOrderIdsForEs().add(orderIdForES);
                    reconDataBundle.setCompareWithES(true);
                    dataBundleMap.put(orderIdForES,reconDataBundle);
                }
            }
            //RecentReconDataCache.getInstance().incrementRecordsProcessed();
        } catch (Exception e) {
            logger.error("Exception inside reconcileEvent {} ", e);
            throw new RuntimeException(e);
        }

    }
//,Map<Long,ReconDataBundle> dataBundleMap
    private ESResponse fetchOrderDetailsFromEs(OrdersList ordersList){
        try{
            ESResponse esResponse  =null;
            if(!ordersList.getOrderIdsForEs().isEmpty()){
                esResponse=retryTemplate.execute(context-> esService.getEsDetails(ordersList.getOrderIdsForEs()));
                pushToDD("ES");
            }

            //esResponse  = esService.getEsDetails(ordersList.getOrderIdsForEs());
            return esResponse;
        }catch(Exception e){
            throw new RuntimeException(e);
        }

    }
    private OMSResponse fetchOrderDetailsFromOms(OrdersList ordersList){
        try{
            OMSResponse omsResponse=null;
            if(!ordersList.getOrderIdsForOms().isEmpty()) {
                omsResponse = retryTemplate.execute(context -> omsService.getOmsDetails(ordersList.getOrderIdsForOms()));
                pushToDD("OMS");
            }
            return omsResponse;
        }catch(Exception e){
            throw new RuntimeException(e);
        }

    }
    private void mapData(OMSResponse omsResponse,ESResponse esResponse,Map<Long,ReconDataBundle> dataBundleMap){

        if(Objects.nonNull(omsResponse)){
            for(OMSObject omsObject:omsResponse.getData()){
                Long orderId=omsObject.getOrder_id();
                ReconDataBundle reconDataBundle=dataBundleMap.get(orderId);
                reconDataBundle.setOmsObject(omsObject);
            }
        }
        if(Objects.nonNull(esResponse)){
            for(HitsEs hitsEs:esResponse.getHits().getHits()){
                Long orderId = Long.valueOf(hitsEs.getSource().get(Constants.EsConstatnts.ORDER_ID)+ "");
                ReconDataBundle reconDataBundle=dataBundleMap.get(orderId);
                reconDataBundle.setEsData(hitsEs.getSource());
            }
        }
    }

    private void validateData(Map<Long,ReconDataBundle> dataBundleMap){
        for(Long orderId:dataBundleMap.keySet()){
            RecentReconResponse response = null;
            ReconDataBundle reconDataBundle = dataBundleMap.get(orderId);
            Recents recents=reconDataBundle.getRecordFromKafka();
            if(reconDataBundle.isCompareWithES()){
                response = ReconOMSValidator.validate(recents,convertEsToOms(reconDataBundle.getEsData()),false);
            }else{
                reconDataBundle.getOmsObject();
                response = ReconOMSValidator.validate(recents, reconDataBundle.getOmsObject(),true);
            }
            response.setOrderId(recents.getOrderId());
            response.setOperationType(Constants.CDC.UPDATE_OP.equalsIgnoreCase(reconDataBundle.getDbOpType()) ? Constants.CommonConstants.UPDATE_LITERAL : Constants.CommonConstants.INSERT_LITERAL);
            if (!CollectionUtils.isEmpty(response.getErrors())) {
                pushToDD("MISMATCH");
                RecentReconDataCache.getInstance().pushReportData(response);
            }
            logger.info(ServiceWrapperUtil.findServiceWrapper(recents), "recon response {}", response);
            RecentReconDataCache.getInstance().incrementRecordsProcessed();
        }
    }

    public void createBundle(RecentCDC recentCDCEvent, OrdersList ordersList, Map<Long,ReconDataBundle> dataBundleMap) {

        try {
            this.preProcess(recentCDCEvent,ordersList,dataBundleMap);
        } catch (Exception e) {
            logger.error(ServiceWrapperUtil.findServiceWrapper(recentCDCEvent.getAfter().getService()), "Exception inside createBundle for data {} with error {}",recentCDCEvent, e);
            pushToDD("ERROR_IN_PROCESSING");
        }

    }
    public void createBundleData(OrdersList ordersList, Map<Long,ReconDataBundle> dataBundleMap) {

        try {
            OMSResponse omsResponse = this.fetchOrderDetailsFromOms(ordersList);
            ESResponse esResponse = this.fetchOrderDetailsFromEs(ordersList);
            this.mapData(omsResponse,esResponse,dataBundleMap);
        } catch (Exception e) {
            logger.error("Exception inside createBundleData for data {} with error {}",ordersList, e);
            pushToDD("ERROR_IN_PROCESSING");
        }

    }
    
    public void markIfCustomerAgent(Long customerId) {
        Set<Recents> recents = recentsRepository.findCountByCustomerId(customerId, getAgentLimit() + 1);
        if (recents.size() == getAgentLimit() + 1) {
            logger.info("markIfCustomerAgent customerId is agent {} ", customerId);
            userAgentService.insert(customerId);
            kafkaProducerService.sendMessage(Long.toString(customerId));
            pushToDD("MARK_AS_AGENT");
        }
    }

    private void updateFromDBRecord(Recents recents, Recents recordFromDB) {

        if (recents.getOrderId() == null)
            recents.setOrderId(recordFromDB.getOrderId());

        if (recents.getTxnTime() == null)
            recents.setTxnTime(recordFromDB.getTxnTime());

        if (recents.getMcn() == null)
            recents.setMcn(recordFromDB.getMcn());

        if (recents.getNotPaidOnPaytm() == null)
            recents.setNotPaidOnPaytm(recordFromDB.getNotPaidOnPaytm());
    }

    private void pushToDD(String state) {
        metricsHelper.recordSuccessRate(RECON_LISTENER, state);
    }

    private void pushCountToDD(String metricName, Integer count, String state) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.STATE + ":" + state;
        monitoringClient.count(metricName, count, tags);
    }


    public  int getAgentLimit() {
        Integer dayLimit = FeatureConfigCache.getInstance().getInteger("agentIdentificationLimit");
        if (dayLimit == null) {
            dayLimit = 300;
            pushToDD("INVALID_CONFIG");
        }
        return dayLimit;

    }

    public OMSObject convertEsToOms(Map<String, Object> esSource){
        OMSObject omsObject = new OMSObject();
        try{
            if (Objects.isNull(esSource)) return null;

            omsObject.setOrder_id(Long.parseLong(String.valueOf(esSource.get(Constants.EsConstatnts.ORDER_ID))));

            Order order = new Order();
            order.setCustomer_id(Long.parseLong(String.valueOf(esSource.get(Constants.EsConstatnts.ES_CUTOMER_ID))));
            omsObject.setOrder(order);

            JSONObject fulfilmentReq = new JSONObject();
            fulfilmentReq.put(Constants.EsConstatnts.CATEGORY_ID,esSource.get(Constants.EsConstatnts.ES_CATEGORY_ID));
            fulfilmentReq.put(Constants.EsConstatnts.RECHARGE_NUMBER,esSource.get(Constants.EsConstatnts.ES_RECHARGE_NUMBER));
            fulfilmentReq.put(Constants.EsConstatnts.RECHARGE_NUMBER_2,esSource.get(Constants.EsConstatnts.ES_RECHARGE_NUMBER_2));
            fulfilmentReq.put(Constants.EsConstatnts.RECHARGE_NUMBER_3,esSource.get(Constants.EsConstatnts.ES_RECHARGE_NUMBER_3));
            fulfilmentReq.toString();
            omsObject.setFulfillment_req(String.valueOf(fulfilmentReq));

            JSONObject fulfilmentResponse = new JSONObject();
            fulfilmentResponse.put(Constants.EsConstatnts.OPERATOR,esSource.get(Constants.EsConstatnts.ES_OPERATOR));
            fulfilmentResponse.put(Constants.EsConstatnts.RECHARGE_NUMBER,esSource.get(Constants.EsConstatnts.ES_RECHARGE_NUMBER));
            fulfilmentResponse.toString();
            List<Fulfillment> fulfillment = new ArrayList<>();
            Fulfillment fulfillmentObj = new Fulfillment();
            fulfillment.add(fulfillmentObj);
            fulfillmentObj.setFulfillment_response(String.valueOf(fulfilmentResponse));
            omsObject.setFulfillments((ArrayList<Fulfillment>) fulfillment);

            if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(String.valueOf(esSource.get(Constants.EsConstatnts.ES_SERVICE)))) {
                omsObject.setMeta_data(String.valueOf(esSource.get(Constants.EsConstatnts.ES_META_DATA)));

            }
        }catch(Exception e){
            logger.error("Error while converting ES Response to OMS Response OMSObject(ES) : {} ,exception : {}",omsObject,e);
        }
        return omsObject;
    }

}
