package com.paytm.saga.listeners;

import com.paytm.saga.common.configuration.BulkConsentValidityConsumerEnableCondition;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.exception.ConsentException;
import com.paytm.saga.model.ConsentValidityModel;
import com.paytm.saga.service.ConsentValidityUpdateService;
import com.paytm.saga.util.ConsentUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import static com.paytm.saga.common.constant.Constants.*;
import static com.paytm.saga.common.constant.Constants.CommonConstants.CC_DEFAULT_OPERATOR;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@DependsOn("bulkConsentValidityUpdateKafkaConfig")
@Conditional(BulkConsentValidityConsumerEnableCondition.class)
public class BulkConsentValidityUpdate {

    private final Logger logger = LogManager.getLogger(BulkConsentValidityUpdate.class);

    private final MetricsHelper metricLogger;
    private final ConsentUtil consentUtil;
    private final ConsentValidityUpdateService consentValidityUpdateService;

    @Autowired
    public BulkConsentValidityUpdate(MetricsHelper metricsHelper, ConsentUtil consentUtil, ConsentValidityUpdateService consentValidityUpdateService) {
        this.metricLogger = metricsHelper;
        this.consentUtil = consentUtil;
        this.consentValidityUpdateService = consentValidityUpdateService;
    }

    @KafkaListener(topics = "#{bulkConsentValidityUpdateKafkaConfig.getTopicNames()}",
            groupId = "#{bulkConsentValidityUpdateKafkaConfig.getConsumerGroup()}",
            containerFactory = "bulkConsentValidityUpdateContainerFactory"
    )
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment) {

        long receivedTimestamp = System.currentTimeMillis();
        long readSize = messages.size();
        logger.info("Consumer Record polled Size:: {} from topic:: " + BULK_CONSENT_VALIDITY_KAFKA_TOPIC + " with payload received timestamp: {}", readSize, receivedTimestamp);

        for(String message : messages) {
            try {
                logger.info("[BulkConsentValidityUpdate.listen] Received message: {}", message);
                if(Objects.nonNull(message)){
                    ConsentValidityModel consentValidityModel =  ConsentUtil.parseCsv(message);
                    consentValidityModel.setService(FINANCIAL_SERVICE);
                    consentValidityModel.setOperator(CC_DEFAULT_OPERATOR);
                    logger.info("[BulkConsentValidityUpdate.listen] bulkConsentValidityKafkaModel: {}", consentValidityModel);
                    consentValidityUpdateService.updateConsentValidity(consentValidityModel);
                }
            }catch (ConsentException ex) {
                logger.error("[BulkConsentValidityUpdate.listen] Consent Exception occurred for message {}, Exception {}", message, ex);
                List<String> tags=new ArrayList<>();
                tags.add("service:"+ FINANCIAL_SERVICES);
                tags.add("reason:"+ ex.getReason());
                tags.add("topic:" + BULK_CONSENT_VALIDITY_KAFKA_TOPIC);
                metricLogger.recordSuccessRateForMultipleTags(BULK_CONSENT_VALIDITY_UPDATE_CONSUMER, tags);
            }
            catch (Exception e) {
                logger.error("[BulkConsentValidityUpdate.listen] Exception occurred for message {}, Exception {}", message, e);
            }
        }
        metricLogger.recordExecutionTimeOfEvent("topic:" + BULK_CONSENT_VALIDITY_KAFKA_TOPIC, System.currentTimeMillis() - receivedTimestamp);
        acknowledgment.acknowledge();
    }

    @Scheduled(fixedRate = 60000)
    public void publishHeartbeat() {
        metricLogger.incrementCount(BULK_CONSENT_VALIDITY_UPDATE_CONSUMER,"consumer_heartbeat", "topic:" + BULK_CONSENT_VALIDITY_KAFKA_TOPIC);
        logger.info("Heartbeat metric published for consumer of topic: {}", BULK_CONSENT_VALIDITY_KAFKA_TOPIC);
    }
}

