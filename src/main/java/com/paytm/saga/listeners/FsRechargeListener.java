package com.paytm.saga.listeners;

import com.newrelic.api.agent.Trace;
import com.paytm.saga.common.configuration.FsRechargeKafkaConsumerEnableCondition;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FsRechargeConsumerModel;
import com.paytm.saga.dto.FsRechargeMetaModel;
import com.paytm.saga.service.DCATService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.util.CreditCardUtils;
import com.paytm.saga.util.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.lang.NonNull;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@DependsOn("FsRechargeKafkaPropertiesConfig")
@Conditional(FsRechargeKafkaConsumerEnableCondition.class)
public class FsRechargeListener {
    private final Logger logger = LogManager.getLogger(FsRechargeListener.class);
    private RecentService recentService;
    private final DCATService dcatService;
    private final MetricsHelper metricsHelper;
    @Autowired
    public FsRechargeListener(@NonNull RecentService recentService,@NonNull DCATService dcatService,@NonNull MetricsHelper metricsHelper){
        this.recentService = recentService;
        this.dcatService = dcatService;
        this.metricsHelper = metricsHelper;
    }
    @KafkaListener(topics = "#{FsRechargeKafkaPropertiesConfig.getTopicName()}",
            groupId = "#{FsRechargeKafkaPropertiesConfig.getConsumerGroup()}",
            containerFactory = "FsRechargeListenerContainerFactory")
    public void listen(@Payload List<String> messages, Acknowledgment acknowledgment){
        for (String message : messages){
            logger.trace("[FsRechargeListener.listen]:Message is ={}",message);
            metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.NEW_EVENT);
            FsRechargeConsumerModel fsRechargeConsumerModel;
            if(Objects.nonNull(message)){
                try{
                fsRechargeConsumerModel = JsonUtils.parseJson(message,FsRechargeConsumerModel.class);
                insertDataIntoCassandra(fsRechargeConsumerModel);

                }
                catch (Exception e){
                    logger.error("[FsRechargeListener.listen]:Exception for message = {} with Exception = {}",message,e);
                    metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.ERROR_EVENT);
                }

            }
        }
        try{
            acknowledgment.acknowledge();
        }
        catch (Exception e){
            logger.error("[FsRechargeListener.listen]: Acknowlodegment exception, e={}",e.getMessage());
        }
    }

    @Trace(dispatcher=true)
    public void insertDataIntoCassandra(FsRechargeConsumerModel fsRechargeConsumerModel){
        if (isSkipable(fsRechargeConsumerModel)) {
                logger.trace("[FsRechargeListener.insertDataIntoCassandra]: skipping data = {}", fsRechargeConsumerModel);
                metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.IGNORE_EVENT);
                return;

        }
        prepareRecentsData(fsRechargeConsumerModel);
        if(Objects.isNull(fsRechargeConsumerModel.getRecharge_number())){
            metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.IGNORE_EVENT);
            return;
        }
        try{
            recentService.updateRecentData(fsRechargeConsumerModel);
            logger.info("[FsRechargeListener.insertDataIntoCassandra]: recentData updated in recents table with order_id={}, recharge_number={} and recentData={}",fsRechargeConsumerModel.getOrderId(),fsRechargeConsumerModel.getRecharge_number(),fsRechargeConsumerModel.getRecentData());
        }
        catch (Exception e){
            logger.error("[FsRechargeListener.insertDataIntoCassandra]:Error in inserting data into db for order_id={}, e={}",fsRechargeConsumerModel.getOrderId(),e.getMessage());
            metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.ERROR_EVENT);
        }

    }

    private boolean isSkipable(FsRechargeConsumerModel fsRechargeConsumerModel){
        if(Objects.isNull(fsRechargeConsumerModel)){
            logger.trace("[FsRechargeListener.insertDataIntoCassandra]: skipping data because data is null");
            return true;
        }
        Long customerId = fsRechargeConsumerModel.getCustomerId();
        String service = fsRechargeConsumerModel.getService();
        String paytype = fsRechargeConsumerModel.getPaytype();
        String operator = fsRechargeConsumerModel.getOperator();
        String rechargeNumber = fsRechargeConsumerModel.getRecharge_number();
        Long orderId = fsRechargeConsumerModel.getOrderId();
        Long pid = fsRechargeConsumerModel.getOriginalPid()==null ? fsRechargeConsumerModel.getCatalogProductID() : fsRechargeConsumerModel.getOriginalPid();
        Map<String,Object> recentData = fsRechargeConsumerModel.getRecentData();
        String in_response_code = fsRechargeConsumerModel.getInResponseCode();
        if (Objects.isNull(orderId)){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because order_id is null");
            return true;
        }
        if(Objects.isNull(customerId)){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because custromer_id is null, order_id = {}",orderId);
            return true;
        }
        if(Objects.isNull(service)){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because service is null, order_id = {}",orderId);
            return true;
        }
        if(Objects.isNull(paytype)){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because paytype is null, order_id = {}",orderId);
            return true;
        }
        if(Objects.isNull(operator)){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because operator is null, order_id = {}",orderId);
            return true;
        }

        if(Objects.isNull(rechargeNumber)){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because recharge_number is null, order_id = {}",orderId);
            return true;
        }

        if(Objects.isNull(pid)){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because pid is null, order_id = {}",orderId);
            return true;
        }

        if(Objects.isNull(recentData) || recentData.isEmpty()){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because recentData is null or empty recentData={}, order_id = {}",recentData,orderId);
            return true;
        }

        if(Objects.isNull(in_response_code) || !Constants.FsRechargeConsumerConstants.FS_RECHARGE_CONSUMER_ALLOWED_IN_RESPONSE_CODE.equalsIgnoreCase(in_response_code)){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because in_response_code  is either null or not success, order_id = {}",orderId);
            return true;
        }

        if(Objects.nonNull(FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.RECENT_DATA_UPDATE_ALLOWED_SERVICES_FEATURE_NAME)) && !FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.RECENT_DATA_UPDATE_ALLOWED_SERVICES_FEATURE_NAME).contains(fsRechargeConsumerModel.getService().toLowerCase())){
            logger.trace("[FsRechargeListener.isSkipable]: skipping data because service is not allowed for recentData Update, order_id = {}",orderId);
            metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.RECENT_DATA_FOR_NOT_ALLOWED_SERVICES);
            return true;
        }

        return false;
    }

    private String fetchPlanBucket(FsRechargeConsumerModel fsRechargeConsumerModel, FsRechargeMetaModel fsRechargeMetaModel){

        String planBucket = "";

            String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());
            if (DCATcategoryId != null && Constants.PREPAID_PAYTYPE.equalsIgnoreCase(fsRechargeConsumerModel.getPaytype())) {

                if (Objects.nonNull(fsRechargeMetaModel) && Objects.nonNull(fsRechargeMetaModel.getPlanBucket())) {
                    planBucket = fsRechargeMetaModel.getPlanBucket();
                } else {
                    planBucket=getPlanBucketFromRps(fsRechargeConsumerModel,DCATcategoryId);
                }

            }

        return planBucket;
    }

    private FsRechargeMetaModel parseMetaModel(FsRechargeConsumerModel fsRechargeConsumerModel){
        FsRechargeMetaModel fsRechargeMetaModel=null;
        try{
            fsRechargeMetaModel = JsonUtils.parseJson(fsRechargeConsumerModel.getMetaData(),FsRechargeMetaModel.class);
        }
        catch (Exception e)
        {
            logger.error("[FsRechargeListener.fetchPlanBucket]:Error is parsing meta_data, order_id={} with e={}",fsRechargeConsumerModel.getOrderId(),e.getMessage());
            metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.ERROR_EVENT);
        }
        return fsRechargeMetaModel;
    }

    private void prepareRecentsData(FsRechargeConsumerModel fsRechargeConsumerModel){
        FsRechargeMetaModel fsRechargeMetaModel = parseMetaModel(fsRechargeConsumerModel);
        String plan_bucket = fetchPlanBucket(fsRechargeConsumerModel,fsRechargeMetaModel);
        fsRechargeConsumerModel.setPlanBucket(plan_bucket);
        fsRechargeConsumerModel.getRecentData().put(Constants.CommonConstants.RECENT_DATA_ORDER_ID,fsRechargeConsumerModel.getOrderId().toString());
        if (fsRechargeConsumerModel.getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)){
            fsRechargeConsumerModel.setOperator(Constants.CommonConstants.CC_DEFAULT_OPERATOR);
            fsRechargeConsumerModel.setRecharge_number(getRechargeNumberForCC(fsRechargeMetaModel,fsRechargeConsumerModel));
        }
    }

    private String getPlanBucketFromRps(FsRechargeConsumerModel fsRechargeConsumerModel, String DCATcategoryId){
        String planBucket = "";
        DCATGetPlanResponse dcatGetPlanResponse=null;
        try {
            dcatGetPlanResponse = dcatService.getPlanDetails(DCATcategoryId, Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, fsRechargeConsumerModel.getOperator(), fsRechargeConsumerModel.getAmount(), fsRechargeConsumerModel.getCircle(), fsRechargeConsumerModel.getService());
        } catch (Exception e) {
            logger.error("[FsRechargeListener.getPlanBucketFromRps]:Exception in fetching plan_bucket from DCAT service for order_id={}, e={} ", fsRechargeConsumerModel.getOrderId(), e.getMessage());
            metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME, Constants.FsRechargeConsumerConstants.ERROR_EVENT);
        }
        if (dcatGetPlanResponse != null && dcatGetPlanResponse.getPlan_bucket() != null) {
            planBucket = dcatGetPlanResponse.getPlan_bucket();
        } else if (Constants.CommonConstants.DTH_SERVICE.equalsIgnoreCase(fsRechargeConsumerModel.getService()) && Arrays.asList(Constants.DTH_GROUP).contains(fsRechargeConsumerModel.getOperator().toLowerCase())) {
            planBucket = Constants.DTH_PLAN_BUCKET;
        } else if (Constants.SUNTV.equalsIgnoreCase(fsRechargeConsumerModel.getOperator())) {
            planBucket = Constants.DTH_PLAN_BUCKET;
        }
        return planBucket;
    }

    private String getRechargeNumberForCC(FsRechargeMetaModel fsRechargeMetaModel, FsRechargeConsumerModel fsRechargeConsumerModel){
        String rechargeNumber;
        if (Objects.isNull(fsRechargeMetaModel)){
            rechargeNumber=null;
        }
        else if(Objects.nonNull(fsRechargeConsumerModel.getRecharge_number_4()) && Objects.nonNull(fsRechargeMetaModel.getPar())){
            rechargeNumber= fsRechargeMetaModel.getPar();
        }
        else if (Objects.nonNull(fsRechargeConsumerModel.getRecharge_number_3())) {
            rechargeNumber = fsRechargeConsumerModel.getRecharge_number_3();
        }
        else if(Objects.nonNull(fsRechargeConsumerModel.getRecharge_number_2()) && CreditCardUtils.inFormat(fsRechargeConsumerModel.getRecharge_number_2())){
            rechargeNumber = CreditCardUtils.formatAndReturn(fsRechargeConsumerModel.getRecharge_number_2());
        }
        else if (Objects.nonNull(fsRechargeMetaModel.getCin())) {
            rechargeNumber = fsRechargeMetaModel.getCin();
        }
        else{
            rechargeNumber = null;
        }
        return  rechargeNumber;
    }
}
