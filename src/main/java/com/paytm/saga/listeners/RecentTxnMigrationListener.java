package com.paytm.saga.listeners;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.RecentTxnMigrationDTO;
import com.paytm.saga.dto.ResponsePage;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.service.ChannelHistoryService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.util.OMSToRechargeStatus;
import com.paytm.saga.util.TTLUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Conditional;
import com.paytm.saga.common.configuration.RecentTxnMigrationKafkaConsumerEnableCondition;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@DependsOn("recentTxnMigrationKafkaConfig")
@Conditional(RecentTxnMigrationKafkaConsumerEnableCondition.class)
public class RecentTxnMigrationListener {

    @Autowired
    private ChannelHistoryService channelHistoryService;

    @Autowired
    private RecentService recentService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RecentsRepositoryWrapperService recentsRepository;


    @KafkaListener(topics = "#{recentTxnMigrationKafkaConfig.getTopic()}",
            groupId = "${recent.txn.migration.kafka.consumer-group}",
            containerFactory = "recentTxnMigrationListenerContainerFactory")
    public void listen(List<String> messages, Acknowledgment acknowledgment) {
        try {
            for (String message : messages) {
                try {
                    RecentTxnMigrationDTO dto = objectMapper.readValue(message, RecentTxnMigrationDTO.class);
                    processMigration(dto);
                } catch (Exception e) {
                    log.error("Error processing message: {}", message, e);
                }
            }
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("Error in migration listener", e);
        }
    }

    private void processMigration(RecentTxnMigrationDTO dto) {
        try {

            // Use existing channel history fetch method and filter for successful transactions
            ResponsePage<ChannelHistory> responsePage = channelHistoryService.getPageOfHistory(
                    dto.getCustomerId(),

                    dto.getRechargeNumber(), dto.getService(), 20, null
            );

            List<ChannelHistory> allTxns = responsePage.getContent();
            log.info("RecentTxnMigrationListener allTxns size: {} {}", allTxns.size(), dto);
            // Filter for successful transactions
            List<ChannelHistory> successfulTxns = allTxns.stream()
                    .filter(txn -> txn.getStatus() != null && Constants.DropOffConstants.SUCCESS.equalsIgnoreCase(OMSToRechargeStatus.getRechargeStatusByOMSStatus(txn.getStatus(),
                            txn.getInResponseCode(), txn.getPaymentStatus())))
                    .collect(Collectors.toList());

            log.info("RecentTxnMigrationListener after filter allTxns size: {}", allTxns.size());

            // Create txnTimes map
            Set<Date> txnTimes = new HashSet<>();
            txnTimes.add(dto.getTxnTime());
            for (ChannelHistory txn : successfulTxns) {
                if (txn.getOrderId() != null && txn.getTransactionTime() != null) {
                    txnTimes.add(txn.getTransactionTime());
                }
            }


            // Update recents table
            if (!txnTimes.isEmpty()) {
                List<Recents> recents = recentService.fetchRecentsFromDb(dto.getCustomerId(), dto.getService(), dto.getRechargeNumber(), dto.getOperator(), dto.getPlanBucket());
                Recents recent = null;
                if (!CollectionUtils.isEmpty(recents)) {
                    recent = recents.get(0);
                }
                if (recent == null) {
                    log.info("no recent found for custId {} and service {} and rechargeNumber {}", dto.getCustomerId(), dto.getService(), dto.getRechargeNumber());
                    return;
                }

                recent.setTxnTimes(txnTimes);

                recent.setUpdatedAt(new Date());
                Integer ttl = TTLUtils.getTTL(recent.getKey().getService(), recent.getTxnTime(), recent.getCreatedAt());
                if (ttl < 1)
                    ttl = 1;
                log.info("[RecentTxnMigrationListener.processMigration] :: Update txnSets in recents for custId {} and service {} and rechargeNumber {} and txntimes {}", recent.getKey().getCustomerId(), recent.getKey().getService(), recent.getKey().getRechargeNumber(), recent.getTxnTimes());
                recentsRepository.updateRecentsTxnTimes(recent, ttl);

                log.info("Successfully updated txnTimes for customerId: {}, service: {}, rechargeNumber: {}",
                        dto.getCustomerId(), dto.getService(), dto.getRechargeNumber());
            }
        } catch (Exception e) {
            log.error("Error processing migration for customerId: {}, service: {}, rechargeNumber: {}",
                    dto.getCustomerId(), dto.getService(), dto.getRechargeNumber(), e);
        }
    }
} 