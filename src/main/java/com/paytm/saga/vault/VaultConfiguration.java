package com.paytm.saga.vault;

import com.bettercloud.vault.SslConfig;
import com.bettercloud.vault.Vault;
import com.bettercloud.vault.VaultConfig;
import com.bettercloud.vault.response.AuthResponse;
import com.paytm.recharges.custom_logger.service.LoadAES256ConfigCustomLogger;
import com.paytm.saga.common.constant.VaultKeys;
import com.paytm.saga.model.VaultProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import jakarta.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Configuration("vaultConfiguration")
public class VaultConfiguration {
    private Vault vault;
    @Value("${saga.cloud.vault.uri:}")
    private String url;
    @Value("${saga.cloud.vault.app-role.role-id:}")
    private String roleId;
    @Value("${saga.cloud.vault.app-role.secret-id:}")
    private String secretId;
    @Value("${saga.cloud.vault.application-name:}")
    private String secretsPath;
    @Autowired
    private Environment environment;
    @Autowired
    private VaultProperties vaultProperties;

    @Bean
    public Vault vault() {
        return vault;
    }

    @PostConstruct
    private void init() {
        Map<String, String> data;
        if (environment.getActiveProfiles().length ==0 || (!environment.getActiveProfiles()[0].equals("production")
                && !environment.getActiveProfiles()[0].equals("beta") && !environment.getActiveProfiles()[0].equals("staging"))) {
            log.info("vault:: not fetching vault config");
            vault = new Vault(new VaultConfig());
        } else {
            log.info("vault:: fetching vault config");
            try {
                if (vault != null)
                    return;
                VaultConfig config = new VaultConfig().address(url).sslConfig(new SslConfig().verify(false)).build();
                vault = new Vault(config);
                AuthResponse response = vault.auth().loginByAppRole(roleId, secretId);
                String token = response.getAuthClientToken();
                config.token(token).build();
                config.engineVersion(2).build();
                data = vault.logical().read(secretsPath).getData();
                log.info("vault:: response data {}", data);
                if(Objects.isNull(data)||data.isEmpty())
                    throw new Error("Failed to initialize vault!");
                vaultProperties.getPgProperties().setSecretKey(data.get(VaultKeys.PG_SECRET));
                vaultProperties.getCassandraProperties().setPassword(data.get(VaultKeys.CASSANDRA_PASSWORD));
                vaultProperties.getAes256Configurations().setEncryptionIvHex(data.get(VaultKeys.AES256_IV_HEX));
                vaultProperties.getAes256Configurations().setEncryptionPassword(data.get(VaultKeys.AES256_ENCRYPTION_PASSWORD));
                LoadAES256ConfigCustomLogger.load(vaultProperties.getAes256Configurations().getEncryptionPassword(), vaultProperties.getAes256Configurations().getEncryptionIvHex());
                log.info("Vault has been successfully initialized!");
            } catch (Exception e) {
                log.error("Exception while initializing vault: " + e);
                throw new Error("Failed to initialize vault!");
            }
        }
    }
}
