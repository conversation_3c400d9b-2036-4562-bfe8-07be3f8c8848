package com.paytm.saga.producers;

import com.paytm.saga.common.configuration.RecentTxnMigrationKafkaConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class TxnTimesMigrationKafkaProducer {

    @Autowired
    RecentTxnMigrationKafkaConfig recentTxnMigrationKafkaConfig;

    public Map<String, Object> evictCachePropertiesConfig() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, recentTxnMigrationKafkaConfig.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return props;
    }

    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(evictCachePropertiesConfig());
    }

    @Bean
    public KafkaTemplate<String, String> txnTimeMigration() {
        return new KafkaTemplate<>(producerFactory());
    }

}
