package com.paytm.saga.producers;


import com.paytm.saga.common.configuration.property.CDCRollbackKafkaConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
@Service
public class CDCRollbackKafkaProducer {
    private final Logger logger = LogManager.getLogger(CDCRollbackKafkaProducer.class);
    @Autowired
    public CDCRollbackKafkaConfig cdcRollbackKafkaConfig;
    @Autowired
    public KafkaTemplate<String,String> cdcRollbackKafkaTemplate;
    public CDCRollbackKafkaProducer(){
    }
    public void sendMessage(String msgObj,String partitionKey){
        cdcRollbackKafkaTemplate.send(cdcRollbackKafkaConfig.getTopicName(),partitionKey,msgObj);
    }

}

