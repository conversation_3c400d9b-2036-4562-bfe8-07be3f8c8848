package com.paytm.saga.producers;

import com.paytm.saga.common.configuration.property.RemovePgTokenKafkaProducerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

@Service
public class RemovePgTokenKafkaProducer {

    private final Logger logger = LogManager.getLogger(RemovePgTokenKafkaProducer.class);

    @Autowired
    private RemovePgTokenKafkaProducerConfig removePgTokenKafkaProducerConfig;

    @Autowired
    private KafkaTemplate<String,String> removePgTokenKafkaTemplate;

    public RemovePgTokenKafkaProducer(){}

    public void send(String msgObj) {
        logger.info("Inside send of Kafka Producer");
        removePgTokenKafkaTemplate.send(removePgTokenKafkaProducerConfig.getTopicName(), msgObj)
                .thenAccept(result -> {
                    // Handle success
                    logger.info("Message sent successfully to topic: " + result.getRecordMetadata().topic());
                    logger.info("Partition: " + result.getRecordMetadata().partition());
                    logger.info("Offset: " + result.getRecordMetadata().offset());
                })
                .exceptionally(ex -> {
                    // Handle failure
                    logger.error("Error sending message: " + ex.getMessage());
                    return null;
                });
    }



}
