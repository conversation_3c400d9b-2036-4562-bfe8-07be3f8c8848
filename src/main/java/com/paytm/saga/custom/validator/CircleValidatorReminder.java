package com.paytm.saga.custom.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.ReminderStatusRequest;

public class CircleValidatorReminder implements ConstraintValidator<CircleValidationReminder, ReminderStatusRequest> {
	public boolean isValid(ReminderStatusRequest reminderStatusRequest, ConstraintValidatorContext context) {
		if (reminderStatusRequest.getService() != null
				&& reminderStatusRequest.getService().equals(Constants.ServiceTypeConstants.MOBILE)) {
			if (reminderStatusRequest.getCircle() != null && !reminderStatusRequest.getCircle().isEmpty()) {
				return true;
			}else {
				return false;
			}
		}
		return true;
	}
}
