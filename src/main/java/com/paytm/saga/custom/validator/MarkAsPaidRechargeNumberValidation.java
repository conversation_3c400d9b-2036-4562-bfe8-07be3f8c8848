package com.paytm.saga.custom.validator;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Documented
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {MarkAsPaidRechargeNumberValidator.class})
public @interface MarkAsPaidRechargeNumberValidation {
	String message() default "recharge number can't be null";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
