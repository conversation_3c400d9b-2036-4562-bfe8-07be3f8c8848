package com.paytm.saga.custom.validator;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Documented
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {CircleValidatorReminder.class})
public @interface CircleValidationReminder {
	String message() default "circle cannot be empty or null";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
