package com.paytm.saga.custom.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.paytm.saga.dto.MarkAsPaidRequest;

public class MarkAsPaidRechargeNumberValidator
		implements ConstraintValidator<MarkAsPaidRechargeNumberValidation, MarkAsPaidRequest> {
	public boolean isValid(MarkAsPaidRequest markAsPaidRequest, ConstraintValidatorContext context) {
		if (markAsPaidRequest.getRecharge_number() != null || markAsPaidRequest.getRechargeNumber() != null) {
			return true;
		}
		return false;
	}
}