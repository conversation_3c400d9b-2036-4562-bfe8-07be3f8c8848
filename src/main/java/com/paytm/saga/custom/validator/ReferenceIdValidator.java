package com.paytm.saga.custom.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.MarkAsPaidRequest;

public class ReferenceIdValidator implements ConstraintValidator<ReferenceIdValidation, MarkAsPaidRequest> {
	public boolean isValid(MarkAsPaidRequest markAsPaidRequest, ConstraintValidatorContext context) {
		if (markAsPaidRequest.getPaytype() != null
				&& markAsPaidRequest.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
			if (markAsPaidRequest.getReferenceId() != null && !markAsPaidRequest.getReferenceId().isEmpty()) {
				return true;
			}else {
				return false;
			}
		}
		return true;
	}
}