package com.paytm.saga.custom.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.MarkAsPaidRequest;

public class CircleValidator implements ConstraintValidator<CircleValidation, MarkAsPaidRequest> {
	public boolean isValid(MarkAsPaidRequest markAsPaidRequest, ConstraintValidatorContext context) {
		if (markAsPaidRequest.getService() != null
				&& markAsPaidRequest.getService().equals(Constants.ServiceTypeConstants.MOBILE)) {
			if (markAsPaidRequest.getCircle() != null && !markAsPaidRequest.getCircle().isEmpty()) {
				return true;
			}else {
				return false;
			}
		}
		return true;
	}
}
