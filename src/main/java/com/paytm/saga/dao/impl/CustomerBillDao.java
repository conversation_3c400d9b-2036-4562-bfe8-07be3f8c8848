package com.paytm.saga.dao.impl;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dao.Dao;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.CustomerBillPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.CommonService;
import com.paytm.saga.service.EncryptionDecision;
import com.paytm.saga.util.ServiceWrapperUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class CustomerBillDao implements Dao {

    private final CustomLogger logger = CustomLogManager.getLogger(CustomerBillDao.class);

    @Autowired
    private  CommonService commonService;

    @Autowired
    private CustomerBillRepository customerBillRepository;

    public void saveRecentToCustomerBill(List<Recents> recents, CustomerBill reminderClusterKey) {
        logger.info("saveRecentToCustomerBill starts");
        Integer ttl = commonService.getSmartReminderTTL(reminderClusterKey);

        if (ttl > 0) {
            for (Recents recent : recents) {
                CustomerBill clusterKey = new CustomerBill();
                BeanUtils.copyProperties(reminderClusterKey, clusterKey);
                CustomerBillPrimaryKey key = new CustomerBillPrimaryKey();
                BeanUtils.copyProperties(reminderClusterKey.getKey() , key);
                key.setCustomerId(recent.getKey().getCustomerId());
                clusterKey.setKey(key);

                logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "saving into customerBill {} {} ", recent.getKey().getCustomerId(), ttl);

                customerBillRepository.save(clusterKey, ttl);
            }

           // batchOps.insert(updatedList, WriteOptions.builder().ttl(TTLUtils.getTTL(reminderClusterKey.getKey().getService())).build());
            //batchOps.execute();

        } else {
            logger.info("Ignoring ttl smaller than 0  {}", reminderClusterKey);
        }

    }

    public void save(CustomerBill clusterKey, Integer ttl) {
        if(Constants.FINANCIAL_SERVICES.equalsIgnoreCase(clusterKey.getKey().getService()) &&  EncryptionDecision.isDataEncryptionRequired(clusterKey.getKey().getCustomerId())){
            return;
        }
        customerBillRepository.save(clusterKey, ttl);
    }

}
