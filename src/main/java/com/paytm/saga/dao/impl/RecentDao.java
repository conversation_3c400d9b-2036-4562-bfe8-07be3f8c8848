package com.paytm.saga.dao.impl;


import com.datastax.oss.driver.api.core.ConsistencyLevel;
import com.datastax.oss.driver.api.core.cql.SimpleStatement;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.dao.Dao;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.service.RecentsEncryptionHandler;
import com.paytm.saga.util.TTLUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.cassandra.core.CassandraBatchOperations;
import org.springframework.data.cassandra.core.CassandraOperations;
import org.springframework.data.cassandra.core.cql.QueryOptions;
import org.springframework.data.cassandra.core.cql.WriteOptions;
import org.springframework.data.cassandra.core.query.Criteria;
import org.springframework.data.cassandra.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
public class RecentDao implements Dao {

    private final CustomLogger logger = CustomLogManager.getLogger(RecentDao.class);

    @Autowired
    @Qualifier("keyspaceRecentCassandraTemplate")
    protected CassandraOperations template;

    @Autowired
    private  RecentsEncryptionHandler encryptionHandlerForRecents;



    public List<Recents> findByQuery(String dynamicQuery) {
        logger.debug("Executing dynamic query as SimpleStatement (non-prepared): {}", dynamicQuery);
        
        SimpleStatement simpleStatement = SimpleStatement.newInstance(dynamicQuery)
                .setConsistencyLevel(ConsistencyLevel.LOCAL_ONE);
        
        // Use CqlTemplate to execute without automatic preparation
        // Note: This bypasses CassandraTemplate's automatic preparation behavior
        return template.getCqlOperations().query(simpleStatement, (rs, rowNum) -> {
            return template.getConverter().read(Recents.class, rs);
        });
    }

    public List<Recents> findByParams(Long customerId, List<String> services, String rechargeNumber, String operator, Integer limit, Long readTimeout) {

        Query query = Query.query(Criteria.where("customerid").is(customerId));
        if (!CollectionUtils.isEmpty(services)) {
            logger.trace("RecentWidgetDataService :: adding criteria for serivce");
            query = query.and(Criteria.where("service").in(services));

            if (rechargeNumber != null && (services.size() == 1 && !services.get(0).equalsIgnoreCase(Constants.FINANCIAL_SERVICES))) {
                logger.trace("RecentWidgetDataService :: adding criteria fo rechargeNumber");
                query = query.and(Criteria.where("recharge_number").is(rechargeNumber));

                if (operator != null)
                    query = query.and(Criteria.where("operator").is(Constants.FINANCIAL_SERVICE.equalsIgnoreCase(services.get(0)) ? Constants.CommonConstants.CC_DEFAULT_OPERATOR : operator));
            }
        }
        if(Objects.nonNull(readTimeout)){
            QueryOptions queryOptions = QueryOptions.builder().readTimeout(readTimeout).build();
            query = query.queryOptions(queryOptions);
        }
        if(Objects.nonNull(limit)){
            query = query.limit(limit);
        }

        if (logger.isTraceEnabled())
            logger.trace("RecentWidgetDataService query is {}", query);
        List<Recents> recentsList = template.select(query, Recents.class);

        try {
            if (!recentsList.isEmpty()) {
                return encryptionHandlerForRecents.decryptRecentList(recentsList);
            }
        } catch (AES256Exception e) {
            logger.error("findByParams::Error in decrypting recents", e);
            throw new AES256Exception("findByParams ; Error in decrypting the recents");
        }

        return recentsList;

    }

    public boolean bulkInsert(List<Recents> recents) {

        logger.info("starts bulkUpdate {}", recents.size());

        final CassandraBatchOperations batchOps = template.batchOps();

        for (Recents recentObj : recents) {
            int ttl = TTLUtils.getTTL(recentObj.getKey().getService());
            batchOps.insert(recentObj, WriteOptions.builder().ttl(ttl).build());
        }

        try {
            batchOps.execute();
            logger.info("ends bulkUpdate");
            return true;
        } catch (Exception e) {
            logger.error("Error during bulkUpdate", e);
            return false;
        }
    }
}
