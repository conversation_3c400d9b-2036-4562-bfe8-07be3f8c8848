package com.paytm.saga.validators;

import com.paytm.saga.validators.impl.CreditCardValidator;
import org.springframework.messaging.handler.annotation.Payload;

import jakarta.validation.Constraint;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = CreditCardValidator.class)
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface CreditCardValidate {
    String message() default "PanUniqueReference or ReferenceId is null";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
