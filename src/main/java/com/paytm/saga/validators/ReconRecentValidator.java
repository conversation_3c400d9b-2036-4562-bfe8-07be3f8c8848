package com.paytm.saga.validators;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.ReconRecordValidationException;
import com.paytm.saga.model.Recents;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ReconRecentValidator {

    private static final Logger logger = LogManager.getLogger(ReconRecentValidator.class);

    private ReconRecentValidator() {


    }

    public static void validate(Recents recents) {

        if (recents.getTxnTime() == null
                && recents.getOrderId() == null)
            throw new ReconRecordValidationException("Ignore record as orderId is null");
    }

    public static void checkMandatoryFields(Recents recents) {

        if ((recents.getTxnTime() == null || recents.getOrderId() == null)
                && !new Integer(1).equals(recents.getNotPaidOnPaytm())){
            throw new ReconRecordValidationException("Invalid record as orderId or txn time is null");
        }

        if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(recents.getKey().getService())
                && recents.getMcn() == null)
            throw new ReconRecordValidationException("Invalid record as mcn is null for CC");
    }

    public static boolean isValidForRecon(Recents recents) {
        try {
            validate(recents);
            return true;
        } catch (ReconRecordValidationException exception) {
            logger.warn("validation exception {} for message {}", exception.getMessage(),recents);
            return false;
        }
    }

    public static boolean isNeedToFetchFromDB(Recents recents) {
        if (recents.getTxnTime() == null
                || recents.getOrderId() == null)
            return true;
        if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(recents.getKey().getService())
                && recents.getMcn() == null)
            return true;
        return false;
    }

}
