package com.paytm.saga.validators;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;

import org.springframework.messaging.handler.annotation.Payload;

import com.paytm.saga.validators.impl.MinCurrentDateValidator;

@Documented
@Constraint(validatedBy = MinCurrentDateValidator.class)
@Target( { ElementType.METHOD, ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface MinCurrentDate {
    String message() default "Date should be greater than currentdate";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}