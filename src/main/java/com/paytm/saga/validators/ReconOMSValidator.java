package com.paytm.saga.validators;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.MessageConstants;
import com.paytm.saga.dto.OMSFulfillmentReqModel;
import com.paytm.saga.dto.OMSFulfillmentResponseModel;
import com.paytm.saga.dto.OMSItemMetaModel;
import com.paytm.saga.dto.RecentReconResponse;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.oms.OMSObject;
import com.paytm.saga.enums.Service;
import com.paytm.saga.model.Recents;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.OMSUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ReconOMSValidator {

    private static final Logger logger = LogManager.getLogger(ReconOMSValidator.class);

    public static RecentReconResponse validate(Recents recent, OMSObject omsObject,boolean isOmsData) {
        logger.info("Data Match Info validating for order id {} source isOMS {}",recent.getOrderId(),isOmsData);
        List<String> errors = new ArrayList<>();
        RecentReconResponse response = new RecentReconResponse();
        response.setKey(recent.getKey().toString());
        response.setErrors(errors);
        response.setRecentCustomerId(recent.getKey().getCustomerId() + "");
        response.setRecentRechargeNumber(recent.getKey().getRechargeNumber());
        response.setRecentMcn(recent.getMcn());
        response.setRecentOperator(recent.getKey().getOperator());

        if(omsObject==null){
            if("financial services".equalsIgnoreCase(recent.getKey().getService())
                    && (new Integer(1).equals(recent.getNotPaidOnPaytm()) || (Objects.nonNull(recent.getOrderId()) && recent.getOrderId()<18000000000L))){
                return response;
            }
            logger.info("Data Match Info Data Not found for key {} isOmsData {}",recent.getKey(),isOmsData);
            if(isOmsData)
                errors.add(String.format(MessageConstants.RECON_OMS_DATA_NOT_FOUND_MSG, recent.getKey().getRechargeNumber()));
            else{
                errors.add(String.format(MessageConstants.RECON_ES_DATA_NOT_FOUND_MSG, recent.getKey().getRechargeNumber()));
            }
            return response;
        }
        OMSFulfillmentReqModel omsFulfillmentReqModel = OMSUtils.getOMSFillmentRequest(omsObject.getFulfillment_req());

        logger.trace("omsFulfillmentReqModel is {}", omsFulfillmentReqModel);
        OMSFulfillmentResponseModel omsFulfillmentResponseModel = null;
        if (!CollectionUtils.isEmpty(omsObject.getFulfillments()))
            omsFulfillmentResponseModel = OMSUtils.getOMSFulfillmentResponse(omsObject.getFulfillments().get(0).getFulfillment_response());

        logger.trace("omsFulfillmentResponseModel is {}", omsFulfillmentResponseModel);

        response.setOmsCustomerId(omsObject.getOrder().getCustomer_id() + "");


        response.setOmsOperator(omsFulfillmentResponseModel.getOperator());
        if (!recent.getKey().getCustomerId().toString().equalsIgnoreCase(String.valueOf(omsObject.getOrder().getCustomer_id()))) {
            logger.info("Data Match Info Mismatch in customer id key {} isOmsData {}",recent.getKey(),isOmsData);
            errors.add(String.format(MessageConstants.RECON_OMS_CUSTOMERID_MISMATCH_MSG, recent.getKey().getCustomerId(), omsObject.getOrder().getCustomer_id()));
        }else{
            logger.info("Data Match Info match in customer id key {} isOmsData {} recent customer id {} oms customer id {}",recent.getKey(),isOmsData,recent.getKey().getCustomerId(),String.valueOf(omsObject.getOrder().getCustomer_id()));
        }

        if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(recent.getKey().getService())) {
            validateForFinancialService(omsObject, recent, omsFulfillmentReqModel, errors, response, omsFulfillmentResponseModel);
        } else {
            response.setOmsRechargeNumber(CommonUtils.getRechargeNumberBasedOnRules(omsFulfillmentReqModel.getRecharge_number(), recent.getKey().getOperator()));

            if (!recent.getKey().getRechargeNumber().equalsIgnoreCase(response.getOmsRechargeNumber())) {
                if (!Service.RENT_PAYMENT.value.equalsIgnoreCase(recent.getKey().getService()) || !recent.getKey().getRechargeNumber().equalsIgnoreCase(omsFulfillmentReqModel.getRecharge_number_7())) {
                    logger.info("Data Match Info Mismatch in recharge number key {} isOmsData {}", recent.getKey(), isOmsData);
                    errors.add(String.format(MessageConstants.RECON_OMS_RECHARGENUMBER_MISMATCH_MSG, recent.getKey().getRechargeNumber(), omsFulfillmentReqModel.getRecharge_number()));
                }
            } else {
                logger.info("Data Match Info Match in recharge number key {} isOmsData {} recent recharge number {} oms number {}", recent.getKey(), isOmsData, recent.getKey().getRechargeNumber(), response.getOmsRechargeNumber());
            }


            if (!recent.getKey().getOperator().equalsIgnoreCase(omsFulfillmentResponseModel.getOperator())) {
                if(!Constants.UPPCL_NEW_OPERATORS.contains(recent.getKey().getOperator())){
                    if((Service.RENT_PAYMENT.value.equalsIgnoreCase(recent.getKey().getService()) || Service.TUITION_FEES.value.equalsIgnoreCase(recent.getKey().getService())) ||
                            (Objects.nonNull(CVRProductCache.getInstance().getProductDetails(omsObject.getProduct_id()))
                            && recent.getKey().getOperator().equalsIgnoreCase(CVRProductCache.getInstance().getProductDetails(omsObject.getProduct_id()).getOperator()))){
                        logger.info("Data Match Info Traced in operator key {} product operator {} isOmsData {}",recent.getKey(),CVRProductCache.getInstance().getProductDetails(omsObject.getProduct_id()).getOperator(),isOmsData);
                    }else{
                        logger.info("Data Match Info Mismatch in operator key {} isOmsData {} product {}",recent.getKey(),isOmsData,omsObject.getProduct_id());
                        errors.add(String.format(MessageConstants.RECON_OMS_OPERATOR_MISMATCH_MSG, recent.getKey().getOperator(), omsFulfillmentResponseModel.getOperator()));
                    }

                }else{
                    logger.info("Data Match Info matched in operator key {} isOmsData {} recent operator {}, OMS operator {}",recent.getKey(),isOmsData,recent.getKey().getOperator(),omsFulfillmentResponseModel.getOperator());
                }
            }


        }
        if(Objects.nonNull(response.getErrors())
                && !response.getErrors().isEmpty()){
            logger.info("Data Match Info Mismatched for order id {} isOmsData {} recentData {} oms Data {}",recent.getOrderId(),isOmsData,recent,omsObject);
        }

//        if (!recent.getTxnAmount().equals(omsObject.getPrice())) {
//            errors.add(String.format(MessageConstants.RECON_OMS_TXN_AMT_MISMATCH_MSG, recent.getTxnAmount().toString(), omsObject.getPrice().toString()));
//        }
//        if (!recent.getProductId().equals(omsObject.getProduct_id())) {
//            errors.add(String.format(MessageConstants.RECON_OMS_PID_MISMATCH_MSG, recent.getProductId().toString(), omsObject.getProduct_id().toString()));
//        }

        return response;
    }



    private static void validateForFinancialService(OMSObject omsObject, Recents recent, OMSFulfillmentReqModel omsFulfillmentReqModel, List<String> errors, RecentReconResponse response, OMSFulfillmentResponseModel omsFulfillmentResponseModel) {
        OMSItemMetaModel omsItemMetaModel = OMSUtils.getOMSSiteMetaModel(omsObject.getMeta_data());

        String cin = getCINFromOMS(omsFulfillmentReqModel, omsFulfillmentResponseModel);
        if (!StringUtils.isEmpty(cin))
        omsItemMetaModel.setCin(cin);

        if (StringUtils.isEmpty(omsItemMetaModel.getCin()))
            omsItemMetaModel.setCin(omsFulfillmentReqModel.getRecharge_number_2());

        response.setOmsRechargeNumber(StringUtils.firstNonEmpty(omsItemMetaModel.getPar(), omsItemMetaModel.getCin()));
        response.setOmsMcn(omsFulfillmentReqModel.getRecharge_number());
        if(Objects.nonNull(recent.getOrderId()) && recent.getOrderId()<18000000000L){
            logger.info("Data Match Info Mismatch in recharge number key {} recent recharge number {} oms recharge number {}",recent.getKey(),recent.getKey().getRechargeNumber(),StringUtils.firstNonEmpty(omsItemMetaModel.getPar(), omsItemMetaModel.getCin()));
            return;
        }
        if (!recent.getKey().getRechargeNumber().equalsIgnoreCase(StringUtils.firstNonEmpty(omsItemMetaModel.getPar(), omsItemMetaModel.getCin()))) {
            errors.add(String.format(MessageConstants.RECON_OMS_RECHARGENUMBER_MISMATCH_MSG, recent.getKey().getRechargeNumber(), StringUtils.firstNonEmpty(omsItemMetaModel.getPar(), omsItemMetaModel.getCin())));
            logger.info("Data Match Info Mismatch in recharge number key {} recent recharge number {} oms recharge number {}",recent.getKey(),recent.getKey().getRechargeNumber(),StringUtils.firstNonEmpty(omsItemMetaModel.getPar(), omsItemMetaModel.getCin()));
        }else{
            logger.info("Data Match Info Matched in recharge number key {} recent recharge number {} oms recharge number {}",recent.getKey(),recent.getKey().getRechargeNumber(),StringUtils.firstNonEmpty(omsItemMetaModel.getPar(), omsItemMetaModel.getCin()));
        }


        if (!compareLast4MCNDigit(recent.getMcn(), omsFulfillmentReqModel.getRecharge_number())) {
            errors.add(String.format(MessageConstants.RECON_OMS_MCN_MISMATCH_MSG, recent.getMcn(), omsFulfillmentReqModel.getRecharge_number()));
            logger.info("Data Match Info Mismatch in mcn key {} recent mcn {} oms mcn {}",recent.getKey(),recent.getMcn(),omsFulfillmentReqModel.getRecharge_number());
        }else{
            logger.info("Data Match Info Matched in mcn key {} recent mcn {} oms mcn {}",recent.getKey(),recent.getMcn(),omsFulfillmentReqModel.getRecharge_number());
        }
    }


    private static String getCINFromOMS(OMSFulfillmentReqModel omsFulfillmentReqModel, OMSFulfillmentResponseModel omsFulfillmentResponseModel) {


        if (StringUtils.isEmpty(omsFulfillmentReqModel.getRecharge_number_3())) {
            if (StringUtils.startsWith(omsFulfillmentReqModel.getRecharge_number_2(), "CIN_"))
                return StringUtils.substringAfter(omsFulfillmentReqModel.getRecharge_number_2(), "CIN_");
            else
                return omsFulfillmentResponseModel.getCreditCardId();
        }
        else
            return omsFulfillmentReqModel.getRecharge_number_3();
    }

    private static boolean compareLast4MCNDigit(String mcn1, String mcn2) {
        if (mcn1 == null || mcn2 == null || mcn1.length()<4 || mcn2.length()<4)
            return false;
        if(mcn1.substring(mcn1.length()-4).equals(mcn2.substring(mcn2.length()-4)))
            return true;
        return false;

    }


}
