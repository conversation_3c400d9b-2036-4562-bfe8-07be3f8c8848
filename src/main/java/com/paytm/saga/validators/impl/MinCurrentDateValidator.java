package com.paytm.saga.validators.impl;

import java.util.Date;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.paytm.saga.validators.MinCurrentDate;

public class MinCurrentDateValidator implements ConstraintValidator<MinCurrentDate, Date> {

	@Override
	public boolean isValid(Date date, ConstraintValidatorContext cxt) {
		return date != null && date.after(new Date());

	}

}
