package com.paytm.saga.validators.impl;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.NickNameRequest;
import com.paytm.saga.validators.CreditCardValidate;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class CreditCardValidator implements ConstraintValidator<CreditCardValidate, NickNameRequest> {

    @Override
    public boolean isValid(NickNameRequest nickNameRequest, ConstraintValidatorContext cxt) {
        boolean isValid = true;
        if(StringUtils.equalsIgnoreCase(nickNameRequest.getPaytype(), Constants.CREDIT_CARD_PAYTYPE)){
            if(StringUtils.isNotEmpty(nickNameRequest.getPanUniqueReference()) || StringUtils.isNotEmpty(nickNameRequest.getReferenceId())){
                isValid = true;
            }else{
                isValid = false;
            }
        }
        return isValid;
    }

}
