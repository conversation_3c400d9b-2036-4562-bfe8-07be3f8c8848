package com.paytm.saga.controller;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.CIRRequest;
import com.paytm.saga.dto.CIRResponse;
import com.paytm.saga.dto.DeleteCIRRequest;
import com.paytm.saga.dto.DeleteCIRResponse;
import com.paytm.saga.service.CIRService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.ContentCachingRequestWrapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;
@RestController
public class CIRController {
    private static final Logger logger = LogManager.getLogger(CIRController.class);

    @Autowired
    public CIRService cirService;

    @PostMapping("/deleteCIR")
    public ResponseEntity<DeleteCIRResponse> deleteCIR(@Valid @RequestBody DeleteCIRRequest deleteCIRRequest) throws RechargeSagaBaseException {
        logger.info("[CIRController.deleteCIR] deleteCIR api called with requestBody={}", deleteCIRRequest);
        DeleteCIRResponse deleteCIRResponse = cirService.deleteCIR(deleteCIRRequest);
        return new ResponseEntity<>(deleteCIRResponse, HttpStatus.OK);
    }

    @PostMapping("/getBankNames")
    public ResponseEntity<CIRResponse> getBankNames(@Valid @RequestBody CIRRequest cirRequest) throws RechargeSagaBaseException {
        logger.info("[CIRController.getBankNames] getBankNames api called with requestBody={}", cirRequest);
        CIRResponse cirResponse = cirService.getBankNames(cirRequest);
        return new ResponseEntity<>(cirResponse, HttpStatus.OK);

    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Map<String, String> handleValidationExceptions(HttpServletRequest request, HttpServletResponse response, MethodArgumentNotValidException ex) {
        try {
            ContentCachingRequestWrapper requestWrapper = (ContentCachingRequestWrapper) request;
            if (requestWrapper != null) {
                String requestBody = new String(requestWrapper.getContentAsByteArray());
                logger.info("Bad request ---URL-" + requestWrapper.getRequestURI() + "-request Body" + requestBody);
            }
        } catch (Exception e) {
            logger.error("CIRController :: handleValidationExceptions- error in getting request body", e);
        }

        Map<String, String> errors = new HashMap<>();
        errors.put("statusCode", "400");
        errors.put("errorCode", "ERR_VL_001");

        ex.getBindingResult().getAllErrors().forEach((error) -> {
            errors.put("errorMessage", ((FieldError) error).getField() + " " + error.getDefaultMessage());
        });
        return errors;
    }
}
