package com.paytm.saga.controller;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.FrequentOrderResponse;
import com.paytm.saga.dto.FrequentOrderResponseWrapper;
import com.paytm.saga.dto.LatestOrderResponseWrapper;
import com.paytm.saga.dto.LatestOrdersRequest;
import com.paytm.saga.dto.builder.FrequentOrderRequestBuilder;
import com.paytm.saga.service.LatestOrderServiceImpl;
import com.paytm.saga.service.RecentWidgetDataService;
import com.paytm.saga.service.RecoWidgetDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/")
public class FrequentOrderController {

    private static final CustomLogger logger = CustomLogManager.getLogger(FrequentOrderController.class);

    @Autowired
    private RecoWidgetDataService recoWidgetDataService;

    @Autowired
    private RecentWidgetDataService recentWidgetDataService;

    @Autowired
    private LatestOrderServiceImpl latestOrderService;

    @PostMapping("/frequentOrders")
    public ResponseEntity<FrequentOrderResponse> getFrequentOrders(@RequestBody FrequentOrderRequest request, @RequestHeader("customerId") Long customerId,
                                                                   @RequestParam Map<String, String> queryParams
    ) throws RechargeSagaBaseException {
        if(request.getServices() == null || request.getServices().isEmpty() || request.getServices().contains(Constants.FINANCIAL_SERVICES)) {
            LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        }
        logger.info("FrequentOrderController :: getFrequentOrders customerId : {}, request : {} ", customerId, request);

        request.setCustomerId(customerId);
        FrequentOrderRequestBuilder.addQueryParams(queryParams, request);
        return new ResponseEntity(recoWidgetDataService.getFrequentOrders(request), HttpStatus.OK);
    }

    @PostMapping("v2/frequentOrders")
    public ResponseEntity<FrequentOrderResponseWrapper> getFrequentOrdersV2(@RequestBody FrequentOrderRequest request, @RequestHeader("customerId") Long customerId,
                                                                            @RequestParam Map<String, String> queryParams
    ) throws RechargeSagaBaseException {
        if(request.getServices() == null || request.getServices().isEmpty() || request.getServices().contains(Constants.FINANCIAL_SERVICES)) {
            LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        }
        logger.info("FrequentOrderController :: getFrequentOrdersV2 customerId : {}, request : {} ", customerId, request);

        request.setCustomerId(customerId);
        FrequentOrderRequestBuilder.addQueryParams(queryParams, request);

        return new ResponseEntity(recoWidgetDataService.getFrequentOrderWrapper(request), HttpStatus.OK);
    }

    @PostMapping("recent/frequentOrders")
    public ResponseEntity<FrequentOrderResponse> getRecents(@RequestBody FrequentOrderRequest request, @RequestHeader("customerId") Long customerId,
                                                            @RequestParam Map<String, String> queryParams
    ) throws RechargeSagaBaseException {
        if(request.getServices() == null || request.getServices().isEmpty() || request.getServices().contains(Constants.FINANCIAL_SERVICES)) {
            LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        }
        logger.info("FrequentOrderController :: getRecents customerId : {}, request : {} ", customerId, request);
        logger.trace("queryParams is {}", queryParams);
        request.setCustomerId(customerId);
        FrequentOrderRequestBuilder.addQueryParams(queryParams, request);
        return new ResponseEntity(recentWidgetDataService.getFrequentOrderWrapper(request), HttpStatus.OK);
    }

    @PostMapping("recent/latestTwoOrders")
    public ResponseEntity<?> getLatestTwoOrders(@RequestBody LatestOrdersRequest request, @RequestHeader("customerId") Long customerId, @RequestParam Map<String, String> queryParams) {
        LoggerThreadContext.setServiceName(request.getService());
        logger.info("FrequentOrderController :: getLatestTwoOrders customerId : {}, request : {} ", customerId, request);
        request.setCustomerId(customerId);
        try {
            LatestOrderResponseWrapper wrapper = latestOrderService.getLatestOrderWrapper(request);
            return ResponseEntity.ok(wrapper);
        } catch (RechargeSagaBaseException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred: " + e.getMessage());
        }
    }

}
