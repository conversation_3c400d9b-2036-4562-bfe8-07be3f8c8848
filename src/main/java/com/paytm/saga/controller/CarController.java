package com.paytm.saga.controller;

import com.paytm.saga.dto.CarDetailColourResponse;
import com.paytm.saga.dto.CarDetailResponse;
import com.paytm.saga.service.CarDetailService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api")
public class CarController {

    private static final Logger logger = LogManager.getLogger(CarController.class);

    private final CarDetailService carDetailService;

    @Autowired
    public CarController(final CarDetailService carDetailService) {
        this.carDetailService = carDetailService;
    }

    @GetMapping("/carDetails")
    public ResponseEntity<CarDetailResponse> fetchCarDetails(@RequestParam("make") String make, @RequestParam(value = "lastRecordId", required = false) String lastRecordId) {
        return carDetailService.fetchCardDetails(make, lastRecordId);
    }

    @GetMapping("/carImages")
    public ResponseEntity<CarDetailColourResponse> fetchCarImages(@RequestParam("make") String make, @RequestParam(value = "lastRecordId", required = false) String lastRecordId) {
        return carDetailService.fetchCarColourDetails(make, lastRecordId);
    }

}
