package com.paytm.saga.controller;

import com.paytm.saga.dto.RemovePgTokenKafkaRequest;
import com.paytm.saga.dto.RemovePgTokenResponse;
import com.paytm.saga.service.RemovePgTokenService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class PgCardController {
    private static final Logger logger = LogManager.getLogger(PgCardController.class);

    private final RemovePgTokenService removePgTokenService;

    @Autowired
    public  PgCardController(RemovePgTokenService removePgTokenService){
        this.removePgTokenService=removePgTokenService;
    }

    @PostMapping("/pg/removeToken")
    public ResponseEntity<Object> removePgToken(@Valid  @RequestBody RemovePgTokenKafkaRequest removePgTokenApiRequest, @RequestParam(required = false) Map<String, String> p_request_params){
        logger.info("Inside Pg Token Controller");
        RemovePgTokenResponse response=removePgTokenService.pushTopicToKafka(removePgTokenApiRequest);
        return new ResponseEntity<>(response,HttpStatus.OK);
    }
}
