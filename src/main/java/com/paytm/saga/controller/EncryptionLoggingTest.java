package com.paytm.saga.controller;

import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.service.LoggerHelper;
import com.paytm.saga.service.RecentsEncryptionHandler;
import com.paytm.saga.service.ThreadContext;
import com.paytm.saga.util.AESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICE;

@RestController
public class EncryptionLoggingTest {
	@Autowired
	private LoggerHelper loggerHelper;
	@Autowired
	private RecentsEncryptionHandler recentsEncryptionHandler;
	@Autowired
	private AESUtil aesUtil;
	@PostMapping("/hi")
	public long hi() {
		ThreadContext.setServiceName("electricity");
		ThreadContext.setCustomerId(String.valueOf(1234L));
		ThreadContext.setRechargeNumber("1234567890");


		List<Recents> recents = new ArrayList<>();
		for(int i=0;i<10;i++) {
			recents.add(getRecents());
		}

		long startTime = System.nanoTime();

		for(int j=0;j<10000;j++) {
//			ArrayList<String> stList = new ArrayList<>();
//			for(int k = 0;k<10;k++) {
//				stList.add(aesUtil.encrypt(recents.get(k).toString()));
//			}
			loggerHelper.info("Encrypted recents : {}", aesUtil.encrypt(recents.toString()));
		}

		long endTime = System.nanoTime();
		long duration = (endTime - startTime); // Duration in nanoseconds
		loggerHelper.info("Time taken to log (10000 (iteration) * 10(recent list)) recents : " + duration/1000000 + " milliseconds");
		ThreadContext.clear();
		return duration;
	}

	private Recents getRecents() {
		Recents recents = new Recents();
		recents.setMcn("dummyMcn");
		recents.setIsTokenizedTransaction(true);
		recents.setBillDate(new Date());
		recents.setDueDate(new Date());
		recents.setDueAmount(100.0);
		recents.setMinDueAmount(50.0);
		recents.setCurrentOutstandingAmount(200.0);
		recents.setOriginalDueAmount(150.0);
		recents.setOriginalMinDueAmount(75.0);
		recents.setNickName("dummyNickName");
		recents.setChannelId("dummyChannelId");
		recents.setConsumerName("dummyConsumerName");
		recents.setCylinderAgencyName("dummyCylinderAgencyName");
		recents.setOrderId(12345L);
		recents.setProductId(67890L);
		recents.setTxnAmount(300.0);
		recents.setTxnTime(new Date());
		recents.setTxnStatus("SUCCESS");
		recents.setAutomaticDate(new Date());
		recents.setAutomaticStatus(1);
		recents.setRechargeNumber2("dummyRechargeNumber2");
		recents.setRechargeNumber3("dummyRechargeNumber3");
		recents.setRechargeNumber4("dummyRechargeNumber4");
		recents.setRechargeNumber5("dummyRechargeNumber5");
		recents.setRechargeNumber6("dummyRechargeNumber6");
		recents.setRechargeNumber7("dummyRechargeNumber7");
		recents.setRechargeNumber8("dummyRechargeNumber8");
		recents.setBillUpdateTime(new Date());
		recents.setDismissActionTime(new Date());
		recents.setIsMarkAsPaid(true);
		recents.setMarkAsPaidTime(new Date());
		recents.setLastPendingTxn("dummyLastPendingTxn");
		recents.setLastFailureTxn("dummyLastFailureTxn");
		recents.setCin("dummyCin");
		recents.setPar("dummyPar");
		recents.setTin("dummyTin");
		recents.setUpdatedAt(new Date());
		recents.setPayType("dummyPayType");
		recents.setCircle("dummyCircle");
		recents.setRecentData("dummyRecentData");
		recents.setNotPaidOnPaytm(1);
		recents.setNotificationStatus(1);
		recents.setPlanName("dummyPlanName");
		recents.setMarkAsPaidAmount(400.0);
		recents.setNewBillUpdatedAt(new Date());
		recents.setIsSavedCard(true);
		recents.setIsValidation(true);
		recents.setIsTransaction(true);
		recents.setEventSource("dummyEventSource");
		RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
		recentsPrimaryKey.setService(FINANCIAL_SERVICE);
		recentsPrimaryKey.setRechargeNumber("123342343");
		recentsPrimaryKey.setOperator("hdfc");
		recentsPrimaryKey.setPlanBucket("");
		recents.setKey(recentsPrimaryKey);
		recents.setOnlyTxn(true);
		recents.setRecoEvent(true);
		recents.setAdditionalInfo("dummyAdditionalInfo");
		recents.setTag("dummyTag");
		recents.setExtra("dummyExtra");
		recents.setUserData("dummyUserData");
		recents.setCreatedAt(new Date());
		recents.setOrderIdP2p("dummyOrderIdP2p");
		recents.setIsSmsParsed(true);
		recents.setIsNewBiller(true);
		recents.setRentTFData("dummyRentTFData");
		recents.setEarlyPaymentAmount(500.0);
		recents.setEarlyPaymentDate(new Date());
		recents.setReconId("dummyReconId");
		recents.setOperator("dummyOperator");
		recents.setBbpsRefId("dummyBbpsRefId");
		recents.setPgCardId("dummyPgCardId");
		recents.setTxnUpdatedAt(new Date());
		recents.setAutomaticSubscriptionId(2);
		recents.setNextBillFetchDateFlag(true);
		recents.setCardVariant("dummyCardVariant");
		recents.setCardSkin("dummyCardSkin");
		recents.setAutomaticAmount(600.0);
		recents.setReminderStatus(1);
		recents.setOldBillFetchDate(new Date());
		recents.setInsuranceCard("dummyInsuranceCard");
		recents.setIsNewBillIdentified(true);
		recents.setRemindLaterDate(new Date());
		recents.setMarkAsPaidSource("dummyMarkAsPaidSource");
		recents.setEncDueAmount("dummyEncDueAmount");
		recents.setEncMinDueAmount("dummyEncMinDueAmount");
		recents.setEncDueDate("dummyEncDueDate");
		recents.setEncOriginalDueAmount("dummyEncOriginalDueAmount");
		recents.setEncOriginalMinDueAmount("dummyEncOriginalMinDueAmount");
		recents.setEncCurrentOutstandingAmount("dummyEncCurrentOutstandingAmount");
		recents.setIsEncrypted(0);
		recents.setConsumerSource("dummyConsumerSource");
		recents.setFastagRechargeNumber("dummyFastagRechargeNumber");
		return recents;
	}

}
