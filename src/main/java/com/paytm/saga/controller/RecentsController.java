package com.paytm.saga.controller;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.DBUpdateException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.*;
import com.paytm.saga.service.FetchRecentsService;
import com.paytm.saga.service.RecentsService;
import com.paytm.saga.service.SmartRecentsService;
import com.paytm.saga.service.WhatsappReminderLaterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.ContentCachingRequestWrapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
public class RecentsController {

    private static final CustomLogger logger = CustomLogManager.getLogger(RecentsController.class);

    @Autowired
    RecentsService recentsService;
    @Autowired
    FetchRecentsService fetchRecentsService;

    @Autowired
    SmartRecentsService smartRecentsService;
    @Autowired
    WhatsappReminderLaterService whatsappReminderLaterService;

    @PutMapping(value = "/smartreminder/nickname")
    public ResponseEntity<NickNameResponse> updateNickName(@Valid @RequestBody NickNameRequest nickNameRequest) {
        LoggerThreadContext.setServiceName(nickNameRequest.getService());
        logger.info("[RecentsController] nickname PUT api {}",nickNameRequest);
        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);
        return new ResponseEntity<>(nickNameResponse, HttpStatus.valueOf(nickNameResponse.getStatusCode()));
    }

    @PostMapping(value = "/smartreminder/removerecent")
    public ResponseEntity<NickNameResponse> removeRecent(@Valid @RequestBody DeleteRequestBody deleteRequestBody) {
        LoggerThreadContext.setServiceName(deleteRequestBody.getService());
        logger.info("[RecentsController] removeRecent POST api {}",deleteRequestBody);
        NickNameResponse nickNameResponse = recentsService.removeRecentWrapper(deleteRequestBody);
        return new ResponseEntity<>(nickNameResponse, HttpStatus.valueOf(nickNameResponse.getStatusCode()));
    }

    @PostMapping("/fetchRecents")
    public ResponseEntity<FetchRecentsResponse> fetchRecents(@RequestBody FetchRecentsRequest fetchRecentsRequest) throws RechargeSagaBaseException {
        LoggerThreadContext.setServiceName(fetchRecentsRequest.getService());
        logger.info("[FetchRecentsController.fetchRecents] fetchRecents api called with requestBody={}", fetchRecentsRequest);
        FetchRecentsResponse fetchRecentsResponses = fetchRecentsService.fetchRecents(fetchRecentsRequest);
        return new ResponseEntity<>(fetchRecentsResponses, HttpStatus.OK);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Map<String, String> handleValidationExceptions(HttpServletRequest request, HttpServletResponse response, MethodArgumentNotValidException ex) {
        try {
            ContentCachingRequestWrapper requestWrapper = (ContentCachingRequestWrapper) request;
            if (requestWrapper != null) {
                String requestBody = new String(requestWrapper.getContentAsByteArray());
                logger.info("Bad request ---URL-" + requestWrapper.getRequestURI() + "-request Body" + requestBody);
            }
        } catch (Exception e) {
            logger.error("RecentsController :: handleValidationExceptions- error in getting request body", e);
        }

        Map<String, String> errors = new HashMap<>();
        errors.put("statusCode", "400");
        errors.put("errorCode", "ERR_VL_001");

        ex.getBindingResult().getAllErrors().forEach((error) -> {
            errors.put("errorMessage", ((FieldError) error).getField() + " " + error.getDefaultMessage());
        });
        return errors;
    }

    @PostMapping("/api/dropoff/smartrecents")
    public ResponseEntity<SmartRecentDropoffResponse> saveSmartRecentDropOff(@RequestHeader(value = Constants.X_USER_ID_HEADER,required = true) Long customerId, @Valid @RequestBody SmartRecentDropOffRequest request) throws RechargeSagaBaseException{
        LoggerThreadContext.setServiceName(request.getService());
        logger.info("RecentsController :: saveSmartRecentDropOff customerId = {} , request = {}",customerId,request);
        request.setCustomerId(customerId);
        SmartRecentDropoffResponse smartRecentDropoffResponse = smartRecentsService.saveSmartRecentDropOff(request);
        return new ResponseEntity<>(smartRecentDropoffResponse,HttpStatus.OK);
    }
    @PostMapping("/create/recent")
    public ResponseEntity createRecentForFasttag(@Valid @RequestBody CreateRecentRequest request) throws DBUpdateException {
        LoggerThreadContext.setServiceName(request.getService());
        logger.info(" [RecentsController :: createRecentForFasttag] request received for recent creation  with payload {}",request);
        return ResponseEntity.ok(recentsService.createRecentForInsurance(request));
    }
    @PutMapping("/setRemindLater")
    public ResponseEntity setRemindLater(@RequestBody WhatsappRemindLaterRequest whatsappRemindLaterRequest){
        LoggerThreadContext.setServiceName(whatsappRemindLaterRequest.getService());
        logger.info("[RecentsController.setRemindLater] :: setRemindLater api called with requestBody={}", whatsappRemindLaterRequest);
        return ResponseEntity.ok(whatsappReminderLaterService.setRemindLaterDate(whatsappRemindLaterRequest));
    }
}