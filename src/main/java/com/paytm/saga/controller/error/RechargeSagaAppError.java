package com.paytm.saga.controller.error;

import java.util.HashMap;
import java.util.Map;

public class RechargeSagaAppError {
	private final String status;
	private final String code;
	private final String message;

	public RechargeSagaAppError(final String status, final String code, final String message) {
		this.status = status;
		this.code = code;
		this.message = message;
	}

	public static RechargeSagaAppError fromDefaultAttributeMap(final String apiVersion,
			final Map<String, Object> defaultErrorAttributes, final String sendReportBaseUri) {
		return new RechargeSagaAppError(defaultErrorAttributes.getOrDefault("status", "failure").toString(),
				defaultErrorAttributes.get("code").toString(),
				defaultErrorAttributes.getOrDefault("message", "no message").toString());
	}

	public Map<String, Object> toAttributeMap() {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("status", status);
		response.put("code", code);
		response.put("message", message);
		return response;
	}

	public String getStatus() {
		return status;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

}
