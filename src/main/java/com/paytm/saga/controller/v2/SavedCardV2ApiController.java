package com.paytm.saga.controller.v2;

import java.util.Map;
import java.util.Objects;

import jakarta.validation.Valid;

import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import com.paytm.saga.common.constant.RecentConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.SavedCardServiceException;
import com.paytm.saga.dto.SavedCardApiRequest;
import com.paytm.saga.dto.SavedCardResponse;
import com.paytm.saga.service.SavedCardService;

@RestController
@RequestMapping("/api/v2")
public class SavedCardV2ApiController {

    private static final Logger logger = LogManager.getLogger(com.paytm.saga.controller.v2.SavedCardV2ApiController.class);

    private SavedCardService savedCardService;

    @Autowired
    public SavedCardV2ApiController(final SavedCardService savedCardService) {
        this.savedCardService = savedCardService;
    }

    @PostMapping("/customer/{customer_id}/recents")
    public ResponseEntity<SavedCardResponse> getSavedCardDetail(@Valid @RequestBody SavedCardApiRequest savedCardApiRequest,
                                                                @PathVariable("customer_id") Long customerId,@RequestParam(required = false) Map<String, String> p_request_params) throws SavedCardServiceException {
		if(Objects.nonNull(savedCardApiRequest)) {
			LoggerThreadContext.setServiceName(savedCardApiRequest.getService());
		}
		long startTime = System.currentTimeMillis();
		String appendBillsFlag = Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE;
    	if (p_request_params.get(Constants.SAVED_CARDS_BILLS_APPEND_FLAG) != null) {
    		appendBillsFlag = p_request_params.get(Constants.SAVED_CARDS_BILLS_APPEND_FLAG);
    	}
    	boolean isCoft=false;
		if (p_request_params.get(Constants.SAVED_CARDS_COFT_FLAG) != null) {
			isCoft = Boolean.parseBoolean(p_request_params.get(Constants.SAVED_CARDS_COFT_FLAG));
		}
        boolean onlyActiveBills=false;

        if (p_request_params.get(Constants.ONLY_ACTIVE_BILL_FLAG) != null) {
            onlyActiveBills = Boolean.parseBoolean(p_request_params.get(Constants.ONLY_ACTIVE_BILL_FLAG));
        }

        if (p_request_params.get(RecentConstants.BILL_TYPE) != null) {
            onlyActiveBills = Constants.BillType.DUE_BILLS.equalsIgnoreCase(p_request_params.get(RecentConstants.BILL_TYPE));
        }

        ResponseEntity<SavedCardResponse> response = new ResponseEntity<>(savedCardService.getSavedCards(customerId, savedCardApiRequest, Constants.API_VERISON_V2,appendBillsFlag,isCoft,onlyActiveBills), HttpStatus.OK);
        long endTime = System.currentTimeMillis();
        logger.info("api-v2-customer-recents latency in ms is {}", (endTime - startTime));
        return response;
    }
}
