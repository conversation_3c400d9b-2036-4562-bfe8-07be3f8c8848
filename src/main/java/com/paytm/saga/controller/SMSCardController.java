package com.paytm.saga.controller;

import jakarta.validation.Valid;

import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import com.paytm.saga.common.exception.SavedCardServiceException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.SmsCardException;
import com.paytm.saga.dto.SavedCardResponse;
import com.paytm.saga.dto.SmsCardApiRequest;
import com.paytm.saga.service.SmsCardService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import static com.paytm.saga.common.exception.ErrorMessages.SMS_CARDS_INVALID_REQUEST_BODY_ERROR;

@RestController
@RequestMapping("/api")
public class SMSCardController {

	private static final Logger logger = LogManager.getLogger(SMSCardController.class);

	private SmsCardService smsCardService;

	@Autowired
	public SMSCardController(final SmsCardService smsCardService) {
		this.smsCardService = smsCardService;
	}

	@PostMapping("/customer/{customer_id}/newcards")
	public ResponseEntity<SavedCardResponse> getSmsCardDetail(@Valid @RequestBody SmsCardApiRequest smsCardApiRequest,
			@PathVariable("customer_id") Long customerId) throws SmsCardException {
		LoggerThreadContext.setServiceName(smsCardApiRequest.getService());
		logger.info("SMSCardController :: getSmsCardDetail request for customer id {}", customerId);

		if (smsCardApiRequest.getServices() != null && !smsCardApiRequest.getServices().isEmpty()) {
			if(smsCardApiRequest.getServices().size()==1 && (smsCardApiRequest.getServices().contains("mobile") || smsCardApiRequest.getServices().contains("financial services"))) {
				return new ResponseEntity<>(smsCardService.getSmsCards(customerId, smsCardApiRequest.getServices()), HttpStatus.OK);
			}
			if(smsCardApiRequest.getServices().size()==2 && smsCardApiRequest.getServices().containsAll(new ArrayList<String>(Arrays.asList("mobile","financial services")))){
				return new ResponseEntity<>(smsCardService.getSmsCards(customerId, smsCardApiRequest.getServices()), HttpStatus.OK);
			}
		}
		if (smsCardApiRequest.getService() != null && (smsCardApiRequest.getService().equals("mobile") || smsCardApiRequest.getService().equals("financial services") )) {
			return new ResponseEntity<>(smsCardService.getSmsCards(customerId, Collections.singletonList(smsCardApiRequest.getService())), HttpStatus.OK);
		}
		throw new SmsCardException(SMS_CARDS_INVALID_REQUEST_BODY_ERROR);
	}
}
