package com.paytm.saga.controller;


import com.paytm.saga.util.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.ServletRequest;

@RestController
public class HealthCheckController {

    private static final Logger logger = LogManager.getLogger(HealthCheckController.class);

    @GetMapping(value = "/_status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> ping(ServletRequest p_request) {
        logger.info("[HealthCheckController] Health is getting checked");
        return new ResponseEntity<>(JsonUtils.jsonify("status", "ok"), HttpStatus.OK);
    }

}
