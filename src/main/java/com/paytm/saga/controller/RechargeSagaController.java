package com.paytm.saga.controller;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.exception.MarkAsPaidServiceWithDisplayMessageException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.*;
import com.paytm.saga.enums.Service;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.*;
import com.paytm.saga.service.aggregator.AggregatorService;
import com.paytm.saga.service.aggregator.AggregatorServiceInterface;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.DateUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import com.timgroup.statsd.StatsDClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.ContentCachingRequestWrapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.*;

import static com.paytm.saga.common.constant.Constants.NATIVE_THEME_HASH;

@RestController
@RequestMapping("/api")
public class RechargeSagaController {

	private static final CustomLogger logger = CustomLogManager.getLogger(RechargeSagaController.class);

	private final ChannelHistoryService channelHistoryService;
	private final AggregatorService aggregatorService;
	private final ThemeService themeService;
	private final NewThemeService newThemeService;
	private final MarkAsPaidService markAsPaidService;
	private final AggregatorServiceInterface cylinderAggregatorService;
	private final AggregatorServiceInterface electricityAggregatorService;
	private final DropOffService dropOffService;
	private final RecentsService recentsService;
	private final CustomerNameService customerNameService;

	@Autowired
	private RechargeSagaService rechargeSagaService;

	@Autowired
	@Qualifier("monitoringClient")
	private StatsDClient monitoringClient;




	@Value("#{'${utility.history.services}'.split(',')}")
	private List<String> services;


	@Autowired
	public RechargeSagaController(final ChannelHistoryService channelHistoryService,
								  AggregatorService aggregatorService, MarkAsPaidService markAsPaidService, final ThemeService themeService,
								  @Qualifier("cylinderAggregatorService") final AggregatorServiceInterface cylinderAggregatorService,
								  @Qualifier("electricityAggregatorService") AggregatorServiceInterface electricityAggregatorService,
								  final DropOffService dropOffService, final NewThemeService newThemeService,final RecentsService recentsService,final CustomerNameService customerNameService) {
		this.channelHistoryService = channelHistoryService;
		this.aggregatorService = aggregatorService;
		this.markAsPaidService = markAsPaidService;
		this.themeService = themeService;
		this.cylinderAggregatorService = cylinderAggregatorService;
		this.dropOffService = dropOffService;
		this.electricityAggregatorService = electricityAggregatorService;
		this.newThemeService = newThemeService;
		this.recentsService = recentsService;
		this.customerNameService = customerNameService;
	}

	@PostMapping("/history")
	public ResponseEntity<HistoryPage> getHistory(@Valid @RequestBody GetHistoryPageDTO getHistoryPageDTO,
												  @RequestParam("client") String client
										, @RequestParam("version") String version,
										@RequestParam(value = "isNative",defaultValue = "true",required = false) boolean isNative
										,@RequestParam(required = false) Map<String, String> p_request_params
										,@RequestHeader(value = Constants.X_USER_ID_HEADER) Long customerId)
			throws RechargeSagaBaseException {
		LoggerThreadContext.setServiceName(getHistoryPageDTO.getService());
		getHistoryPageDTO.setCustomerId(customerId);
		logger.info(
				"history request recieved for customer_id " + getHistoryPageDTO.getCustomerId() + " recharge_number "
						+ getHistoryPageDTO.getRecharge_number() + " service " + getHistoryPageDTO.getService());

		HistoryPage historyPage = null;
		if (Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean("disableChatHistory"))) {
			logger.info("Returning empty response as disableChatHistory");
			historyPage = CommonUtils.getDefaultHistoryResponse(getHistoryPageDTO);
		} else {
			List<ChannelHistory> channelHistories = channelHistoryService.getPageOfHistory(
					getHistoryPageDTO.getCustomerId(), getHistoryPageDTO.getRecharge_number(),
					getHistoryPageDTO.getService().toLowerCase());
			String currentOperatorInfo = (getHistoryPageDTO.getOperator() + ":" + getHistoryPageDTO.getService() + ":"
					+ getHistoryPageDTO.getCircle()).toLowerCase();
			String previousCardOperator = null;
			Date previousDate = null;
			if (getHistoryPageDTO.getDedupeids() != null && getHistoryPageDTO.getDedupeids().length == 2) {
				previousCardOperator = getHistoryPageDTO.getDedupeids()[1] != null
						? getHistoryPageDTO.getDedupeids()[1].toLowerCase()
						: getHistoryPageDTO.getDedupeids()[1];
				previousDate = DateUtil.stringToDate(getHistoryPageDTO.getDedupeids()[0], DateFormats.DATE_FORMAT);
			}

			if (getHistoryPageDTO.getService().equalsIgnoreCase(Constants.ServiceTypeConstants.CYLINDER)) {
				historyPage = cylinderAggregatorService.aggregateHistoryInfo(getHistoryPageDTO.getCustomerId(),
						getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService().toLowerCase(),
						getHistoryPageDTO.getPagingState(), currentOperatorInfo, previousCardOperator, previousDate, client,
						version);
			} else if (getHistoryPageDTO.getService().equalsIgnoreCase(Constants.ServiceTypeConstants.ELECTRICITY)) {
				historyPage = electricityAggregatorService.aggregateHistoryInfo(getHistoryPageDTO.getCustomerId(),
						getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService().toLowerCase(),
						getHistoryPageDTO.getPagingState(), currentOperatorInfo, previousCardOperator, previousDate, client,
						version);
			} else {
				if (getHistoryPageDTO.getCircle() == null) {
					return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
				}
				historyPage = aggregatorService.aggregateHistoryInfo(channelHistories, getHistoryPageDTO.getCustomerId(),
						getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService().toLowerCase(),
						getHistoryPageDTO.getPagingState(), currentOperatorInfo, previousCardOperator, previousDate, client,
						version);
			}
			if(isNative){
				if(!(NATIVE_THEME_HASH.equals(getHistoryPageDTO.getThemehash())))
					historyPage.setThemeDetails(newThemeService.getThemesForNativeCategory());
				else
					historyPage.setThemeDetails(new ThemeResponse());
			}else {
				historyPage.setThemeDetails(newThemeService.getThemes(getHistoryPageDTO.getThemehash()));
			}
		}
		boolean isNameRequired=false;
		if (p_request_params.get(Constants.NAME_REQUIRED) != null) {
			isNameRequired = Boolean.parseBoolean(p_request_params.get(Constants.NAME_REQUIRED));
		}
		historyPage.setCustomer_name(recentsService.getCustomerName(getHistoryPageDTO,isNameRequired ));
		historyPage.getChannelDetails().setRechargeNumber2(getHistoryPageDTO.getRecharge_number_2());
		historyPage.getChannelDetails().setRechargeNumber3(getHistoryPageDTO.getRecharge_number_3());
		historyPage.getChannelDetails().setRechargeNumber4(getHistoryPageDTO.getRecharge_number_4());

		logger.info("returning response for customer_id " + getHistoryPageDTO.getCustomerId() + " recharge number "
				+ getHistoryPageDTO.getRecharge_number() + " code " + historyPage.getCode() + " number of records "
				+ historyPage.getCount());
		return new ResponseEntity<>(historyPage, HttpStatus.OK);
	}

	@PostMapping("/markAsPaid")
	public ResponseEntity<MarkAsPaidResponse> markAsPaid(@Valid @RequestBody MarkAsPaidRequest markAsPaidRequest,
														 @RequestHeader(value = Constants.X_USER_ID_HEADER,required = false) Long customerId)
			throws RechargeSagaBaseException, MarkAsPaidServiceWithDisplayMessageException {
		LoggerThreadContext.setServiceName(markAsPaidRequest.getService());
		if(markAsPaidRequest.getCustomerId()==null && customerId == null){
			return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
		}
		if(markAsPaidRequest.getCustomerId()==null)
			markAsPaidRequest.setCustomerId(customerId);
		logger.info(
				"markAsPaid request received for header customer_id " + markAsPaidRequest.getCustomerId() + " recharge_number "
						+ markAsPaidRequest.getRecharge_number() + " service " + markAsPaidRequest.getService());

		logger.info("mark as paid request body" + markAsPaidRequest);
		if (markAsPaidRequest.getRechargeNumber() == null && markAsPaidRequest.getRecharge_number() != null) {
			markAsPaidRequest.setRechargeNumber(markAsPaidRequest.getRecharge_number());
		}
		MarkAsPaidResponse markAsPaidResponse = markAsPaidService.intiateProcess(markAsPaidRequest);
		logger.info("out of markAPaid Service ");
		return new ResponseEntity<>(markAsPaidResponse, markAsPaidResponse.getHttpStatus());
	}

	@PostMapping("/dropoffreminderstatus")
	public ResponseEntity<ReminderStatusResponse> updateDropOffReminderStatus(
			@Valid @RequestBody ReminderStatusRequest reminderStatusRequest) throws RechargeSagaBaseException {
		ReminderStatusResponse reminderStatusResponse = markAsPaidService.createDropOffForDoNotRemindme(reminderStatusRequest);
		return new ResponseEntity<>(reminderStatusResponse, reminderStatusResponse.getHttpStatus());
	}

	@GetMapping("/dropOff/{custId}")
	public ResponseEntity<Map<String, DropOffResponse>> dropOff(
			@RequestParam(required = false) Map<String, String> p_request_params,
			@PathVariable("custId") String custId) {
		try {
			if (Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean("disableDropOff"))) {
				logger.info("Returning empty response as disableDropOff");

				Map<String, DropOffResponse> dropOffResponse = CommonUtils.getDefaultDropOffResponse();
				return new ResponseEntity<>(dropOffResponse, HttpStatus.OK);
			} else {
				List<String> services = null;
				String isCoft = "false";
				Long customerId = Long.parseLong(custId);
				if (p_request_params.get("service") != null) {
					services = Arrays.asList(p_request_params.get("service").split(","));
				}
				if (p_request_params.get("isCoft") != null) {
					isCoft = p_request_params.get("isCoft");
				}
				logger.trace("[DropOff service Controller] CustomerId {} isCoft {}", customerId, isCoft);
				Map<String, DropOffResponse> dropOffResponse = dropOffService.getDropOffResponse(customerId, services, null,
						null, isCoft);
				logger.info(" DropOff size for CustomerId" + customerId + "is" + dropOffResponse.size());
				return new ResponseEntity<>(dropOffResponse, HttpStatus.OK);
			}


		} catch (Exception e) {
			logger.error("exception in dropOff", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler(MethodArgumentNotValidException.class)
	public Map<String, String> handleValidationExceptions(HttpServletRequest request, HttpServletResponse response, MethodArgumentNotValidException ex) {
		try {
			ContentCachingRequestWrapper requestWrapper = (ContentCachingRequestWrapper) request;
			if (requestWrapper != null) {
				String requestBody = new String(requestWrapper.getContentAsByteArray());
				logger.info("Bad request ---URL-" + requestWrapper.getRequestURI() + "-request Body" + requestBody);
			}
		} catch (Exception e) {
			logger.error("RechargeSagaController :: handleValidationExceptions- error in getting request body", e);
		}

		Map<String, String> errors = new HashMap<>();
		errors.put("statusCode", "400");
		errors.put("errorCode", "ERR_VL_001");

		ex.getBindingResult().getAllErrors().forEach((error) -> {
			errors.put("errorMessage", ((FieldError) error).getField() + " " + error.getDefaultMessage());
		});
		return errors;
	}


	@PostMapping("/v2/history")
	public ResponseEntity<HistoryPage> getHistoryV2(@Valid @RequestBody GetHistoryPageDTO getHistoryPageDTO,
													@RequestParam("client") String client, @RequestParam("version") String version,
													@RequestParam(value = "isNative",defaultValue = "true",required = false) boolean isNative,@RequestParam(required = false) Map<String, String> p_request_params,
													@RequestHeader(value = Constants.X_USER_ID_HEADER) Long customerId) {
		LoggerThreadContext.setServiceName(getHistoryPageDTO.getService());
		getHistoryPageDTO.setCustomerId(customerId);
		getHistoryPageDTO.setNative(isNative);
		logger.info(
				"history request received for header customer_id " + getHistoryPageDTO.getCustomerId() + " recharge_number "
						+ getHistoryPageDTO.getRecharge_number() + " service " + getHistoryPageDTO.getService());
		if (getHistoryPageDTO.getPayType() == null)
			throw new IllegalArgumentException("Pay type can't be null");
		if (Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(getHistoryPageDTO.getService()) && !ObjectUtils.anyNotNull(getHistoryPageDTO.getCin(), getHistoryPageDTO.getParId())) {
			throw new IllegalArgumentException("Cin or ParId both can't be blank for financial servicers");
		}
		if (!services.contains(StringUtils.lowerCase(getHistoryPageDTO.getService())) &&
				!(StringUtils.containsIgnoreCase(Constants.ServiceTypeConstants.MOBILE,getHistoryPageDTO.getService()) &&
					StringUtils.containsIgnoreCase(Constants.CommonConstants.POSTPAID_PAYTYPE,getHistoryPageDTO.getPayType())
				))
			throw new IllegalArgumentException("Invalid service");
		HistoryPage historyPage;
		if (Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean("disableChatHistory"))) {
			logger.info("Returning empty response as disableChatHistory");
			historyPage = CommonUtils.getDefaultHistoryResponse(getHistoryPageDTO);
		}else{
			logger.info("V2 history request : CustomerId : {} , RechargeNumber : {}, Service : {}",getHistoryPageDTO.getCustomerId(),getHistoryPageDTO.getRecharge_number(),getHistoryPageDTO.getService());
			historyPage = rechargeSagaService.getHistoryV2(getHistoryPageDTO);
		}
		boolean isNameRequired=false;
		if (p_request_params.get(Constants.NAME_REQUIRED) != null) {
			isNameRequired = Boolean.parseBoolean(p_request_params.get(Constants.NAME_REQUIRED));
		}
		historyPage.setCustomer_name(recentsService.getCustomerName(getHistoryPageDTO,isNameRequired ));
		return new ResponseEntity<>(historyPage, HttpStatus.OK);

	}

	@PostMapping("/customername")
	public ResponseEntity<CustomerNameResponse> getCustomerName(@RequestBody CustomerNameRequest customerNameRequest) {
		LoggerThreadContext.setServiceName(customerNameRequest.getService());
		if (customerNameRequest.getCustomer_id() == null || customerNameRequest.getRecharge_number() == null
				|| customerNameRequest.getService() == null || customerNameRequest.getOperator() == null ) {
			logger.error("RechargeSagaController :: customername api - Bad Request {}",customerNameRequest);
			return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
		}
		CustomerNameResponse customerNameResponse = new CustomerNameResponse();
		try {
			customerNameResponse = customerNameService.getCustomerNameResponse(customerNameRequest);
			logger.info("RechargeSagaController :: customername api - Success {}",customerNameRequest);
			return new ResponseEntity<>(customerNameResponse, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("RechargeSagaController :: customername api - error in getting request body", e);
			return new ResponseEntity<>(customerNameResponse, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
