package com.paytm.saga.controller;

import java.util.Map;

import jakarta.validation.Valid;

import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.SavedCardServiceException;
import com.paytm.saga.dto.SavedCardApiRequest;
import com.paytm.saga.dto.SavedCardResponse;
import com.paytm.saga.service.SavedCardService;

@RestController
@RequestMapping("/api")
public class SavedCardApiController {

	private static final Logger logger = LogManager.getLogger(SavedCardApiController.class);

	private SavedCardService savedCardService;

	@Autowired
	public SavedCardApiController(final SavedCardService savedCardService) {
		this.savedCardService = savedCardService;
	}

	@PostMapping("/customer/{customer_id}/recents")
	public ResponseEntity<SavedCardResponse> getSavedCardDetail(@Valid @RequestBody SavedCardApiRequest savedCardApiRequest,
			@PathVariable("customer_id") Long customerId,@RequestParam(required = false) Map<String, String> p_request_params) throws SavedCardServiceException {
		LoggerThreadContext.setServiceName(savedCardApiRequest.getService());
		String appendBillsFlag = Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE;
    	if (p_request_params.get(Constants.SAVED_CARDS_BILLS_APPEND_FLAG) != null) {
    		appendBillsFlag = p_request_params.get(Constants.SAVED_CARDS_BILLS_APPEND_FLAG);
    	}
    	boolean isCoft=false;
		if (p_request_params.get(Constants.SAVED_CARDS_COFT_FLAG) != null) {
			isCoft = Boolean.parseBoolean(p_request_params.get(Constants.SAVED_CARDS_COFT_FLAG));
		}
		boolean onlyActiveBills=false;
		return new ResponseEntity<>(savedCardService.getSavedCards(customerId, savedCardApiRequest, Constants.API_VERISON_V1,appendBillsFlag,isCoft,onlyActiveBills), HttpStatus.OK);
	}
}
