package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.repository.ChannelHistoryRepository;
import com.paytm.saga.util.AESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static com.paytm.saga.common.constant.EncryptionConstants.*;

@Service
public class ChannelHistoryRepositoryWrapperService {
	private final CustomLogger logger = CustomLogManager.getLogger(ChannelHistoryRepositoryWrapperService.class);
	private final ChannelHistoryRepository channelHistoryRepository;
	private final MetricsHelper metricsHelper;
	private final AESUtil aesUtil;
	private final ChannelHistoryEncryptionHandler encryptionHandlerForChannelHistory;

	@Autowired
	public ChannelHistoryRepositoryWrapperService(
		ChannelHistoryRepository channelHistoryRepository,
		MetricsHelper metricsHelper,
		AESUtil aesUtil,
		ChannelHistoryEncryptionHandler channelHistoryEncryptionHandler
	) {
		this.channelHistoryRepository = channelHistoryRepository;
		this.metricsHelper = metricsHelper;
		this.aesUtil = aesUtil;
		this.encryptionHandlerForChannelHistory = channelHistoryEncryptionHandler;
	}

	public Slice<ChannelHistory> findByCustomerIdAndRechargeNumberAndService(
		Long customerId, String rechargeNumber,
		String service, PageRequest pageRequest
	) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)) {
			try {
				List<String> rechargeNumberList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
				Slice<ChannelHistory> encChannelHistory = channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumberList, service, pageRequest);
				return encryptionHandlerForChannelHistory.decryptChannelHistorySlice(encChannelHistory);
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryRepositoryWrapper : findByCustomerIdAndRechargeNumberAndService : exception in decryption", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerIdAndRechargeNumberAndService: Error while decrypting the data from ChannelHistory table");
			}
		}
		return channelHistoryRepository.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, service, pageRequest);
	}

	public List<ChannelHistory> findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(
		Long customerId, String rechargeNumber,
		String service, boolean finalisedState
	) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)) {
			try {
				List<ChannelHistory> decChannelHistory = channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(customerId, rechargeNumber, service, finalisedState);
				List<ChannelHistory> encChannelHistory = channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(customerId, aesUtil.encrypt(rechargeNumber), service, finalisedState);
				List<ChannelHistory> encChannelHistoryDecList = encryptionHandlerForChannelHistory.decryptChannelHistoryList(encChannelHistory);
				encChannelHistoryDecList.addAll(decChannelHistory);
				return encChannelHistoryDecList;
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryRepositoryWrapper : findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState : exception in decryption", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState: Error while decrypting the data from ChannelHistory table");
			}
		}
		return channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(customerId, rechargeNumber, service, finalisedState);
	}

	public List<ChannelHistory> findByCustomerIdAndRechargeNumberAndService(
		Long customerId, String rechargeNumber,
		String service
	) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)) {
			try {
				List<String> rechargeNumberList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
				List<ChannelHistory> encChannelHistory = channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumberList, service);
				return encryptionHandlerForChannelHistory.decryptChannelHistoryList(encChannelHistory);
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryRepositoryWrapper : findByCustomerIdAndRechargeNumberAndService : exception in decryption", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerIdAndRechargeNumberAndService: Error while decrypting the data from ChannelHistory table");
			}
		}
		return channelHistoryRepository.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, service);
	}

	public List<ChannelHistory> findByCustomerIdAndRechargeNumberInAndService(
		Long customerId, List<String> rechargeNumber,
		String service
	) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)) {
			try {
				List<String> rechargeNumberList = new ArrayList<>(rechargeNumber);
				for (String recNum : rechargeNumber) {
					rechargeNumberList.add(aesUtil.encrypt(recNum));
				}
				List<ChannelHistory> encChannelHistory = channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumberList, service);
				return encryptionHandlerForChannelHistory.decryptChannelHistoryList(encChannelHistory);
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryRepositoryWrapper : findByCustomerIdAndRechargeNumberInAndService : exception in decryption", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerIdAndRechargeNumberInAndService: Error while decrypting the data from ChannelHistory table");
			}
		}
		return channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumber, service);
	}

	public List<ChannelHistory> findByCustomerIdAndRechargeNumberAndServiceAndTransactionTimeAndOrderIdAndItemIdAndTransactionUpdateTime(
		Long customerId,
		String rechargeNumber,
		String service,
		Date transactionTime,
		Long orderId,
		Long itemId,
		Date transactionUpdateTime
	) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)) {
			try {
				List<String> rechargeNumberList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
				List<ChannelHistory> encChannelHistory = channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceAndTransactionTimeAndOrderIdAndItemIdAndTransactionUpdateTime(customerId, rechargeNumberList, service, transactionTime, orderId, itemId, transactionUpdateTime);
				return encryptionHandlerForChannelHistory.decryptChannelHistoryList(encChannelHistory);
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryRepositoryWrapper : findByCustomerIdAndRechargeNumberAndServiceAndTransactionTimeAndOrderIdAndItemIdAndTransactionUpdateTime : exception in decryption", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerIdAndRechargeNumberAndServiceAndTransactionTimeAndOrderIdAndItemIdAndTransactionUpdateTime: Error while decrypting the data from ChannelHistory table");
			}
		}
		return channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndTransactionTimeAndOrderIdAndItemIdAndTransactionUpdateTime(customerId, rechargeNumber, service, transactionTime, orderId, itemId, transactionUpdateTime);
	}

	public List<ChannelHistory> findByCustomerIdAndRechargeNumberAndServiceAndFinalisedStateAndOperator(
		Long customerId, String rechargeNumber,
		String service, boolean finalisedState, String operator
	) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)) {
			try {
				List<ChannelHistory> decChannelHistory = channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedStateAndOperator(customerId, rechargeNumber, service, finalisedState, operator);
				List<ChannelHistory> encChannelHistory = channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedStateAndOperator(customerId, aesUtil.encrypt(rechargeNumber), service, finalisedState, operator);
				decChannelHistory.addAll(encryptionHandlerForChannelHistory.decryptChannelHistoryList(encChannelHistory));
				return decChannelHistory;
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryRepositoryWrapper : findByCustomerIdAndRechargeNumberAndServiceAndFinalisedStateAndOperator : exception in decryption", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerIdAndRechargeNumberAndServiceAndFinalisedStateAndOperator: Error while decrypting the data from ChannelHistory table");
			}
		}
		return channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedStateAndOperator(customerId, rechargeNumber, service, finalisedState, operator);
	}

	public ChannelHistory save(ChannelHistory channelHistory, Integer ttl) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(channelHistory.getService()) && EncryptionDecision.isDataEncryptionRequired(channelHistory.getCustomerId())) {
			try {
				ChannelHistory encryptedChannelHistory = encryptionHandlerForChannelHistory.encryptChannelHistory(channelHistory);
				return channelHistoryRepository.save(encryptedChannelHistory, ttl);
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryRepositoryWrapper : save : exception in encryption", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SAVE_ERROR);
				throw new AES256Exception("save: Error while encrypting the data for ChannelHistory table");
			}
		}
		return channelHistoryRepository.save(channelHistory, ttl);
	}

	public void saveAll(List<ChannelHistory> channelHistoryList, Integer ttl) throws AES256Exception {
		if (Objects.isNull(channelHistoryList) || channelHistoryList.isEmpty()) return;
		List<ChannelHistory> finalChannelHistoryList = new ArrayList<>();
		for (ChannelHistory channelHistory : channelHistoryList) {
			if (FINANCIAL_SERVICES.equalsIgnoreCase(channelHistory.getService()) && EncryptionDecision.isDataEncryptionRequired(channelHistory.getCustomerId())) {
				try {
					ChannelHistory encryptedChannelHistory = encryptionHandlerForChannelHistory.encryptChannelHistory(channelHistory);
					if (encryptedChannelHistory != null) {
						finalChannelHistoryList.add(encryptedChannelHistory);
					}
				} catch (AES256Exception e) {
					logger.error("ChannelHistoryRepositoryWrapperService : saveAll : exception in encryption", e);
					metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SAVE_ERROR);
					throw new AES256Exception("saveAll: Error while encrypting the data for ChannelHistory table");
				}
			} else {
				finalChannelHistoryList.add(channelHistory);
			}
		}
		if (!finalChannelHistoryList.isEmpty()) {
			channelHistoryRepository.saveAll(finalChannelHistoryList, ttl);
		}
	}

	public Slice<ChannelHistory>  findAll(final CassandraPageRequest cassandraPageRequest) {
		try {
			return encryptionHandlerForChannelHistory.decryptChannelHistorySlice(channelHistoryRepository.findAll(cassandraPageRequest));
		} catch (AES256Exception e) {
			logger.error("EncryptionHandlerForChannelHistoryRepositoryWrapper : findAll : exception in decryption", e);
			metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_SELECT_FROM_DB_ERROR);
			throw new AES256Exception("findAll: Error while decrypting the data from ChannelHistory table");
		}
	}
}
