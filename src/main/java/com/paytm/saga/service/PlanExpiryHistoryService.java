package com.paytm.saga.service;

import java.util.ArrayList;
import java.util.List;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;

@Service
public class PlanExpiryHistoryService {

	CustomLogger logger = CustomLogManager.getLogger(PlanExpiryHistoryService.class);

	protected final PlanExpiryHistoryRepository planExpiryHistoryRepository;

	@Autowired
	public PlanExpiryHistoryService(final PlanExpiryHistoryRepository planExpiryHistoryRepository) {
		this.planExpiryHistoryRepository = planExpiryHistoryRepository;
	}

	public List<PlanExpiryHistory> getPlanExpiryByRecharge(final String rechargeNumber, final String operator,
			final String circle, final String service) {
		logger.info("getting plan expiry for rechargeNumber " + rechargeNumber);
		return planExpiryHistoryRepository.findByRechargeNumberAndServiceAndCircleAndOperator(rechargeNumber, service,
				circle, operator);
	}

	public PlanExpiryHistory getPlanExpiry(final String rechargeNumber, final String operator,
															final String circle, final String service, final String plan_bucket, final String amount){
		if(rechargeNumber==null || operator==null || circle==null || service==null||plan_bucket==null || amount==null) return null;
		List <PlanExpiryHistory> planExpiryRecords = null;
		try{
			planExpiryRecords = getPlanExpiryByRecharge(rechargeNumber, operator, circle, service);
		} catch (Exception e){
			logger.error("[ValidationListeners.getExpiryDetails] exception", e);
		}
		planExpiryRecords = filterPlanExpiryRecords(planExpiryRecords, amount, plan_bucket);
		if(planExpiryRecords!=null && planExpiryRecords.size()>0){
			return planExpiryRecords.get(0);
		}
		else return null;

	}
	public List<PlanExpiryHistory> filterPlanExpiryRecords(List<PlanExpiryHistory> records , String amount, String planBucket){
		List<PlanExpiryHistory> filteredList = new ArrayList<PlanExpiryHistory>();
		for (PlanExpiryHistory record: records) {
			if(record.getAmount().equals(amount) && record.getPlan_bucket().equals(planBucket)){
				filteredList.add(record);
			}
		}
		return filteredList;
	}

	public List<PlanExpiryHistory> findByRechargeNumberInAndServiceIn(final List<String> rechargeNumber,
															   final List<String> service){
		return  planExpiryHistoryRepository.findByRechargeNumberInAndServiceIn(rechargeNumber, service);
	}

}
