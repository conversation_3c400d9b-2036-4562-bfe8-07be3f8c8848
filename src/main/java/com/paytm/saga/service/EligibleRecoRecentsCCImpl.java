package com.paytm.saga.service;

import com.paytm.saga.model.Recents;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.DateUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
@Service
public class EligibleRecoRecentsCCImpl implements EligibleRecoRecents {
	@Autowired
	protected ServiceConfig serviceConfig;

	@Override
	public List<Recents> getEligibleRecoRecents(List<Recents> recentsList) {
		Date billDueDateStartRange = getBillDueDateStartRange();
		return CommonUtils.collectionToStream(recentsList).filter(recentObj -> recentObj.getDueDate() != null && recentObj.getDueDate().compareTo(billDueDateStartRange) >= 0).collect(Collectors.toList());
	}

	private Date getBillDueDateStartRange() {
		List<String> oldBillEnabledServices = serviceConfig.getOldBillEnabledServices();
		Integer maxBillVisibilityDays = serviceConfig.getBillVisiblityDays();
		for(String service: oldBillEnabledServices){
			maxBillVisibilityDays = Integer.max(maxBillVisibilityDays, serviceConfig.getBillVisiblityDays(service));
		}
		return DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -maxBillVisibilityDays));
	}
}
