package com.paytm.saga.service.external;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.newrelic.api.agent.Trace;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.configuration.property.PgPropertiesConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.ErrorMessages;
import com.paytm.saga.common.exception.PgServiceException;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.dto.*;
import com.paytm.saga.service.PgRestTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.*;

@Service
public class PGService {
    private final PgRestTemplateService restTemplateService;
    private final PgPropertiesConfig pgPropertiesConfig;
    private final ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
    private final CustomLogger logger = CustomLogManager.getLogger(RecentCRUDService.class);

    @Autowired
    public PGService(@NonNull PgRestTemplateService restTemplateService,
                     @NonNull PgPropertiesConfig pgPropertiesConfig,
                     @NonNull ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig) {
        this.restTemplateService = restTemplateService;
        this.pgPropertiesConfig =pgPropertiesConfig;
        this.externalEndpointsPropertiesConfig = externalEndpointsPropertiesConfig;
    }

    @Trace(dispatcher=true)
    public List<SavedCardDetails> getSavedCards(Long customerId, Boolean filterTokenCards) throws PgServiceException {
        PgSavedCardRequest pgSavedCardRequest;
        try {
            pgSavedCardRequest = generateSavedCardRequest(customerId, filterTokenCards);
        } catch (JWTCreationException | UnsupportedEncodingException | IllegalArgumentException e) {
            throw new PgServiceException(e);
        }

        PgSavedCardResponse pgSavedCardResponse;
        try {
            //String s = "{\"responseStatus\":\"SUCCESS\",\"httpCode\":\"200\",\"httpSubCode\":\"200\",\"codeDetail\":\"Success\",\"response\":[{\"savedCardId\":\"618b6273d83f4545dc117294\",\"cardScheme\":\"VISA\",\"expiryDate\":\"082022\",\"issuingBankName\":\"CITI\",\"issuingBankCardVariant\":\"Regalia\",\"cardSuffix\":\"3216\",\"cardType\":\"CC\",\"displayName\":\"CITI\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013021295362620166880000\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\"},{\"savedCardId\":\"************* c7ef9cd6628a76c1937bf1393d8a414b\",\"cardScheme\":\"VISA\",\"expiryDate\":\"102022\",\"issuingBankName\":\"CITI\",\"issuingBankCardVariant\":\"Regalia\",\"cardSuffix\":\"0456\",\"cardType\":\"DC\",\"displayName\":\"CITI\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"452055\",\"issuerCode\":\"UNI\",\"isEligibleForCoft\":true}]}";
            pgSavedCardResponse = restTemplateService.executePostRequest(externalEndpointsPropertiesConfig.getPgSavedCards(),
                    PgSavedCardResponse.class, pgSavedCardRequest);
            //pgSavedCardResponse = JsonUtils.parseJson(s, PgSavedCardV2Response.class);
            logger.info("[PGService]:getSavedCards PG saved card API response : " + externalEndpointsPropertiesConfig.getPgSavedCards() + " Request Ops: " + pgSavedCardRequest.toString() + pgSavedCardResponse);
        } catch (RestTemplateServiceException e) {
            logger.error("[PGService]:getSavedCards PG saved card API response Error : " + externalEndpointsPropertiesConfig.getPgSavedCards() + " Error " + e.getDetailMessage());
            throw new PgServiceException(e);
        }

        if(Objects.nonNull(pgSavedCardResponse)) {
            //TODO Put API success condition
            if(pgSavedCardResponse.getHttpCode().equals(Constants.CommonConstants.PG_SAVED_CARD_V2_SUCCESS_HTTP_CODE) &&
            (pgSavedCardResponse.getHttpSubCode().equals(Constants.CommonConstants.PG_SAVED_CARD_V2_SUCCESS_HTTP_SUB_CODE) ||
            		pgSavedCardResponse.getHttpSubCode().equals(Constants.CommonConstants.PG_SAVED_CARD_V2_NO_CARDS_HTTP_SUB_CODE))) {
                return pgSavedCardResponse.getResponse();
            }
        }
        throw new PgServiceException(ErrorMessages.PG_SAVED_CARDS_MALFORMED_RESPONSE_ERROR);
    }

    private PgSavedCardRequest generateSavedCardRequest(Long customerId, Boolean filterTokenCards) throws JWTCreationException, UnsupportedEncodingException, IllegalArgumentException{
        PgSavedCardRequest pgSavedCardRequest = new PgSavedCardRequest();
        PgSavedCardHead pgSavedCardHead = new PgSavedCardHead();
        pgSavedCardHead.setTokenType(Constants.CommonConstants.TOKEN_TYPE_JWT);
        pgSavedCardHead.setToken(generateJWTToken(customerId));
        pgSavedCardHead.setClientId(Constants.CommonConstants.PG_SAVED_CARD_V2_CLIENT_ID);
        PgSavedCardBody pgSavedCardBody = new PgSavedCardBody();
        pgSavedCardBody.setUserId(Long.toString(customerId));
        if(filterTokenCards)
            pgSavedCardBody.setFilterTokenCards(filterTokenCards);
        pgSavedCardRequest.setBody(pgSavedCardBody);
        pgSavedCardRequest.setHead(pgSavedCardHead);
        return pgSavedCardRequest;
    }

    private String generateJWTToken(Long customerId) throws JWTCreationException, UnsupportedEncodingException, IllegalArgumentException {
        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.PgConstants.PG_CLAIMS_USERID,Long.toString(customerId));
        claims.put(Constants.PgConstants.PG_CLAIMS_TOKEN_TYPE, Constants.PgConstants.PG_CLAIMS_TOKEN_TYPE_VALUE);

        JWTCreator.Builder builder = JWT.create().withIssuer(Constants.PgConstants.PG_JWT_ISSUER);
        String jwtToken = null;

        Iterator var3 = claims.entrySet().iterator();
        while(var3.hasNext()) {
            Map.Entry<String, String> entry = (Map.Entry)var3.next();
            builder.withClaim((String)entry.getKey(), (String)entry.getValue());
        }
        jwtToken = builder.sign(Algorithm.HMAC256(pgPropertiesConfig.getSecretKey())); // secret key would be shared separately for each environment
        return jwtToken;
    }
}
