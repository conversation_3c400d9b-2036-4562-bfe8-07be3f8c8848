package com.paytm.saga.service.external;

import com.newrelic.api.agent.Trace;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.configuration.property.OmsPropertiesConfig;
import com.paytm.saga.common.exception.ErrorMessages;
import com.paytm.saga.common.exception.OMSServiceException;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.dto.oms.OMSResponse;
import com.paytm.saga.service.OmsRestTemplateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OmsService {
    private final OmsRestTemplateService restTemplateService;
    private final OmsPropertiesConfig omsPropertiesConfig;
    private final ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
    private final CustomLogger logger = CustomLogManager.getLogger(OmsService.class);



    @Autowired
    public OmsService(@NonNull OmsRestTemplateService restTemplateService,
                      @NonNull OmsPropertiesConfig omsPropertiesConfig,
                      @NonNull ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig) {
        this.restTemplateService = restTemplateService;
        this.omsPropertiesConfig = omsPropertiesConfig;
        this.externalEndpointsPropertiesConfig = externalEndpointsPropertiesConfig;
    }

    @Trace(dispatcher = true)
    public OMSResponse getOmsDetails(Long orderId) throws Exception {
        Map<String, String> params = new HashMap<>();
        logger.info("orderId {}", orderId);
        try {
            OMSResponse omsResponse;
            params.put("order_id", orderId.toString());
            params.put("data_coverage", "2");
            params.put("fulfillment", "1");
            params.put("payment", "1");
            omsResponse = restTemplateService.executeGetRequest(externalEndpointsPropertiesConfig.getItemV2Url(),
                    params, OMSResponse.class);
            logger.trace("[OMSService]:getOmsDetails OMS itemv2 API response : URL {} order id {} response {}", externalEndpointsPropertiesConfig.getItemV2Url(), orderId, omsResponse);
            return omsResponse;
        } catch (RestTemplateServiceException e) {
            logger.error("[OMSService]:getOmsDetails OMS itemv2 API response Error : {} , {}, {} {}", externalEndpointsPropertiesConfig.getItemV2Url(), e.getDetailMessage(), e, orderId);
            throw new OMSServiceException(e);
        } catch (Exception e) {
            logger.error("[OMSService]:getOmsDetails OMS itemv2 API response Exception : {}, {}, {} {} ", externalEndpointsPropertiesConfig.getItemV2Url(),  e.getMessage(), e, orderId);
            throw new OMSServiceException(ErrorMessages.OMS_ITEMV2_MALFORMED_RESPONSE_ERROR);
        }

    }
    @Trace(dispatcher = true)
    public OMSResponse getOmsDetails(List<Long> ordersId) throws Exception {
        Map<String, String> params = new HashMap<>();
        logger.info("fetching data from OMS for ordersId {}", ordersId);
        try {
            params.put("order_id", StringUtils.join(ordersId, ','));
            params.put("data_coverage", "2");
            params.put("fulfillment", "1");
            params.put("payment", "1");
            OMSResponse omsResponse = restTemplateService.executeGetRequest(externalEndpointsPropertiesConfig.getItemV2Url(),
                    params, OMSResponse.class);
            logger.trace("[OMSService]:getOmsDetails OMS itemv2 API response : URL {} orders id {} response {}", externalEndpointsPropertiesConfig.getItemV2Url(), ordersId, omsResponse);
            if (omsResponse == null || CollectionUtils.isEmpty(omsResponse.getData()))
                throw new OMSServiceException("Empty oms response");
            return omsResponse;
        } catch (RestTemplateServiceException e) {
            logger.error("[OMSService]:getOmsDetails OMS itemv2 API response Error : {} , {}, {} {}", externalEndpointsPropertiesConfig.getItemV2Url(), e.getDetailMessage(), e, ordersId);
            throw new OMSServiceException(e);
        } catch (Exception e) {
            logger.error("[OMSService]:getOmsDetails OMS itemv2 API response Exception : {}, {}, {} {} ", externalEndpointsPropertiesConfig.getItemV2Url(),  e.getMessage(), e, ordersId);
            throw new OMSServiceException(ErrorMessages.OMS_ITEMV2_MALFORMED_RESPONSE_ERROR);
        }

    }
}
