package com.paytm.saga.service.external;

import com.newrelic.api.agent.Trace;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.property.EsPropertiesConfig;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.dto.PaymentTxnModel;
import com.paytm.saga.dto.es.ESResponse;
import com.paytm.saga.service.EsRestTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.paytm.saga.common.constant.Constants.CommonConstants.ES_RETRY;
import static com.paytm.saga.common.constant.ServiceNameConstants.FAILED_IN_RESPONSE_STATES_SERVICE;
import static com.paytm.saga.util.ESServiceUtil.getEndpointWithCurrentMonthIndex;
import static com.paytm.saga.util.OMSUtils.getFailedInResponseStates;

@Service
public class EsService {

    private final EsRestTemplateService restTemplateService;
    private final EsPropertiesConfig esPropertiesConfig;
    private final ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
    private final CustomLogger logger = CustomLogManager.getLogger(EsService.class);

    @Autowired
    public EsService(@NonNull EsRestTemplateService restTemplateService,
                     @NonNull EsPropertiesConfig esPropertiesConfig,
                     @NonNull ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig) {
        this.restTemplateService = restTemplateService;
        this.esPropertiesConfig = esPropertiesConfig;
        this.externalEndpointsPropertiesConfig = externalEndpointsPropertiesConfig;
    }

    @Trace(dispatcher = true)
    public ESResponse getEsDetails(Long orderId) throws Exception {
        Map<String, String> params = new HashMap<>();
        logger.info("orderId {}", orderId);
        try {
            //remind to create a response obj for this ERResponse
            ESResponse esResponse;
            params.put(Constants.EsConstatnts.PRETTY, String.valueOf(true));
            Map<String,String> requestHeaders = new HashMap<>();
            requestHeaders.put(Constants.EsConstatnts.CONTENT_TYPE, Constants.EsConstatnts.APPLICATION_JSON);
            esResponse = restTemplateService.executePostRequest(externalEndpointsPropertiesConfig.getElasticSearch(),requestHeaders,params,
                    ESResponse.class, getEsRequest(orderId));
            logger.trace("[EsService]:getErDetails ER API response : URL {} order id {} response {}", externalEndpointsPropertiesConfig.getElasticSearch(), orderId, esResponse);
            return esResponse;
        } catch (Exception e) {
            logger.error("[EsService]:getErDetails ER API response Error : {} , {}, {} {}", externalEndpointsPropertiesConfig.getElasticSearch(), e.getMessage(), e, orderId);
            throw new RuntimeException(e);
        }

    }

    @Trace(dispatcher = true)
    public ESResponse getEsDetails(List<Long> ordersId) throws Exception {
        Map<String, String> params = new HashMap<>();
        logger.info("fetching order detail from ES for orders Id {}", ordersId);
        try {
            //remind to create a response obj for this ERResponse
            ESResponse esResponse;
            params.put(Constants.EsConstatnts.PRETTY, String.valueOf(true));
            Map<String,String> requestHeaders = new HashMap<>();
            requestHeaders.put(Constants.EsConstatnts.CONTENT_TYPE, Constants.EsConstatnts.APPLICATION_JSON);
            esResponse = restTemplateService.executePostRequest(externalEndpointsPropertiesConfig.getElasticSearch(),requestHeaders,params,
                    ESResponse.class, getEsRequest(ordersId));
//            if(esResponse.getHits().getHits().isEmpty()){
//                throw new RuntimeException("response from ES empty");
//            }
            logger.info("[EsService]:getEsDetails ES API response : URL {} order id {} response count {}", externalEndpointsPropertiesConfig.getElasticSearch(), ordersId, esResponse.getHits().getHits().size());
            return esResponse;
        } catch (Exception e) {
            logger.error("[EsService]:getEsDetails ES API response Error : {} , {}, {} {}", externalEndpointsPropertiesConfig.getElasticSearch(), e.getMessage(), e, ordersId);
            throw new RuntimeException(e);
        }
    }

    public String getEsRequest(Long orderId){
        //String reqbody = "{\"query\":{\"bool\":{\"must\":{\"term\":{\"orderInfo_order_id\":"+orderId+"}}}}}";
        String reqbody = "{\"size\":1,\"query\":{\"bool\":{\"should\":[{\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\""+orderId+"\"}},{\"match\":{\"inStatusMap_responseCode\":\"00\"}}]}}]}}}";
        logger.info("query for ES : {} ",reqbody);
        return reqbody;
    }
    public String getEsRequest(List<Long> ordersId){
        List<String> innerQuery=new ArrayList<>();
        for(Long orderId:ordersId){
            innerQuery.add("{\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\""+orderId+"\"}},{\"match\":{\"inStatusMap_responseCode\":\"00\"}}]}}");
            innerQuery.add("{\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\""+orderId+"\"}},{\"match\":{\"inStatusMap_responseCode\":\"11\"}}]}}");
            innerQuery.add("{\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\""+orderId+"\"}},{\"match\":{\"productInfo_paytype\":\"credit card\"}},{\"match\":{\"inStatusMap_responseCode\":\"08\"}}]}}");
        }
        String query = "{\"size\":"+ordersId.size()+",\"query\":{\"bool\":{\"should\":"+innerQuery+"}}, \"collapse\": { \"field\": \"orderInfo_order_id\" }}";
        logger.info("query for ES : {} ",query);
        return query;
    }


    @Trace(dispatcher = true)
    @Retryable(value = { Exception.class }, maxAttempts = ES_RETRY, backoff = @Backoff(delay = 1000))
    public ESResponse getEsDetailsForOrderCount(PaymentTxnModel paymentTxnModel) throws Exception {

        Map<String, String> params = new HashMap<>();
        logger.info("orderId {}", paymentTxnModel.getOrderId());
        ESResponse esResponse = null;
        try {
            //remind to create a response obj for this ERResponse
            params.put(Constants.EsConstatnts.PRETTY, String.valueOf(true));
            Map<String, String> requestHeaders = new HashMap<>();
            requestHeaders.put(Constants.EsConstatnts.CONTENT_TYPE, Constants.EsConstatnts.APPLICATION_JSON);
            esResponse = restTemplateService.executePostRequest(getEndpointWithCurrentMonthIndex(externalEndpointsPropertiesConfig.getElasticSearch()), requestHeaders, params,
                    ESResponse.class, getEsRequestForOrderCount(paymentTxnModel, getFailedInResponseStates(FAILED_IN_RESPONSE_STATES_SERVICE)));
            logger.trace("[EsService]:getErDetails ER API response : URL {} order id {} response {}", externalEndpointsPropertiesConfig.getElasticSearch(), paymentTxnModel, esResponse);
            return esResponse;
        } catch (Exception e) {
            logger.error("[EsService]:getErDetails ER API response RestTemplateServiceException : {} , {}, {} {}", externalEndpointsPropertiesConfig.getElasticSearch(), e.getMessage(), e, paymentTxnModel.getOrderId());
            throw e;
        }

    }



    public String getEsRequestForOrderCount(PaymentTxnModel paymentTxnModel, List<String> excludeResponseCodes) {
        String excludeResponseCodesStr = String.join("\", \"", excludeResponseCodes);

        String reqbody = "{\n" +
                "    \"size\": 10000,\n" +
                "    \"_source\": [\"orderInfo_order_id\"],\n" +
                "    \"query\": {\n" +
                "        \"bool\": {\n" +
                "            \"must\": [\n" +
                "                {\n" +
                "                    \"term\": {\n" +
                "                        \"customerInfo_customer_id\": \"" + paymentTxnModel.getCustomerId() + "\"\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"term\": {\n" +
                "                        \"productInfo_categoryId\": \"" + paymentTxnModel.getCategoryId() + "\"\n" +
                "                    }\n" +
                "                }\n" +
                "            ],\n" +
                "            \"must_not\": [\n" +
                "                {\n" +
                "                    \"terms\": {\n" +
                "                        \"inStatusMap_responseCode\": [\"" + excludeResponseCodesStr + "\"]\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    }\n" +
                "}";

        logger.info("query for ES : {} ", reqbody);
        return reqbody;
    }
}
