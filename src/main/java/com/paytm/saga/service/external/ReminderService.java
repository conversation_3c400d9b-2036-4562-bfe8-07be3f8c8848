package com.paytm.saga.service.external;

import java.util.*;
import java.util.concurrent.Callable;

import jakarta.validation.constraints.NotNull;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import org.springframework.web.client.HttpStatusCodeException;

import com.newrelic.api.agent.Trace;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.ErrorMessages;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.common.exception.SmsCardException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.GetSmsCardsResponse;
import com.paytm.saga.dto.ReminderMarkAsPaidRequest;
import com.paytm.saga.dto.ReminderMarkAsPaidResponse;
import com.paytm.saga.dto.SmsCard;
import com.paytm.saga.service.ReminderRestTemplateService;
import com.paytm.saga.service.SmsCardsRestTemplateService;

@Service
public class ReminderService {
	private final ReminderRestTemplateService restTemplateService;
	private final SmsCardsRestTemplateService smsCardsRestTemplateService;
	private final ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
	private final CustomLogger logger = CustomLogManager.getLogger(ReminderService.class);
	private final MetricsHelper metricsHelper;

	@Autowired
	public ReminderService(@NonNull ReminderRestTemplateService restTemplateService,
			@NotNull SmsCardsRestTemplateService smsCardsRestTemplateService,
			@NonNull ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig,
			@NonNull MetricsHelper metricsHelper) {
		this.restTemplateService = restTemplateService;
		this.smsCardsRestTemplateService = smsCardsRestTemplateService;
		this.externalEndpointsPropertiesConfig = externalEndpointsPropertiesConfig;
		this.metricsHelper = metricsHelper;
	}

	@Trace(dispatcher = true)
	@Retryable(value = { RuntimeException.class }, maxAttempts = 2)
	public ReminderMarkAsPaidResponse markAsPaid(String rechargeNumber, Long productID, String operator, String paytype,
			Long customerID, String planBucket, String referenceID, String service, String source) throws RuntimeException {

		ReminderMarkAsPaidRequest reminderMarkAsPaidRequest = new ReminderMarkAsPaidRequest(rechargeNumber,
				String.valueOf(productID), operator, paytype, String.valueOf(customerID), planBucket, referenceID,
				service, source);
		ReminderMarkAsPaidResponse reminderMarkAsPaidResponse;

		try {
			logger.info("[ReminderService]:ReminderMarkAsPaidResponse update: hitAPI: "
					+ externalEndpointsPropertiesConfig.getReminderMarkAsPaid() + " Request Ops: "
					+ reminderMarkAsPaidRequest.toString());
			reminderMarkAsPaidResponse = restTemplateService.executePutRequest(
					externalEndpointsPropertiesConfig.getReminderMarkAsPaid(), ReminderMarkAsPaidResponse.class,
					reminderMarkAsPaidRequest);
			logger.info("[ReminderService]:ReminderMarkAsPaidResponse update: hitAPI response : "
					+ externalEndpointsPropertiesConfig.getReminderMarkAsPaid() + " Request Ops: "
					+ reminderMarkAsPaidRequest.toString() + reminderMarkAsPaidResponse);
			metricsHelper.recordSuccessRate(Constants.CommonConstants.REMINDER_SERVICE_MARK_AS_PAID);
		} catch (HttpStatusCodeException e) {
			metricsHelper.recordErrorRate(Constants.CommonConstants.REMINDER_SERVICE_MARK_AS_PAID);
			logger.info("[ReminderService]:ReminderMarkAsPaidResponse update: hitAPI Exception: "
					+ externalEndpointsPropertiesConfig.getReminderMarkAsPaid() + " Request: "
					+ reminderMarkAsPaidRequest.toString() + " Resp: " + e.getResponseBodyAsString());
			throw new RuntimeException(e);
		} catch (Exception e) {
			metricsHelper.recordErrorRate(Constants.CommonConstants.REMINDER_SERVICE_MARK_AS_PAID);
			logger.error("ReminderService: markAsPaid", e);
			throw new RuntimeException(e);
		}
		return reminderMarkAsPaidResponse;
	}

	@Recover
	public ReminderMarkAsPaidResponse recover(RuntimeException e) {
		ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = null;
		return reminderMarkAsPaidResponse;

	}

	@Trace(dispatcher = true)
	public Callable<Object> markAsPaidTask(String rechargeNumber, Long productID, String operator, String paytype,
			Long customerID, String planBucket, String referenceID, String service, String source) {

		return new Callable<Object>() {
			public ReminderMarkAsPaidResponse call() {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = null;
				try {
					reminderMarkAsPaidResponse = markAsPaid(rechargeNumber, productID, operator, paytype, customerID,
							planBucket, referenceID, service, source);
				} catch (Exception e) {
					logger.error("Exception : markAsPaidTask : ", e);
				}
				return reminderMarkAsPaidResponse;
			}
		};
	}

	@Trace(dispatcher = true)
	@Retryable(value = { RuntimeException.class }, maxAttempts = 2)
	public List<SmsCard> fetchSmsCards(Long customerID, List<String> services) throws SmsCardException {
		GetSmsCardsResponse getSmsCardsResponse = null;
		try {
			logger.info("[ReminderService]:fetchSmsCards get: hitAPI: {} Request Ops customerID: {}, services: {}",
					externalEndpointsPropertiesConfig.getSmsCards(), customerID, services);
			Map<String, String> headers = new HashMap<>();
			headers.put("X-USER-ID", ""+customerID);
			String servicesStr = String.join(",",services);
			Map<String, String> paramsMap = new HashMap<>();
			paramsMap.put(Constants.SMS_API_SERVICE_REQUEST_PARAM, servicesStr);
			String url=externalEndpointsPropertiesConfig.getSmsCards();
			getSmsCardsResponse = smsCardsRestTemplateService.executeGetRequest(url,
					headers, paramsMap, GetSmsCardsResponse.class);
			logger.info("[ReminderService]:fetchSmsCards hitAPI response :{} for customer id {} and services {}",
					getSmsCardsResponse, customerID, services);
			metricsHelper.recordStatusCodes(Constants.SMS_CARD, "" + getSmsCardsResponse.getStatus());
		} catch (RestTemplateServiceException e) {
			metricsHelper.recordStatusCodes(Constants.SMS_CARD, "" + e.getStatusCode());
			logger.error("[ReminderService]:fetchSmsCards hitAPI Exception:{} for customer id {} and services {}",
					e.getDetailMessage(), customerID, services);
			throw new SmsCardException(ErrorMessages.SMS_CARDS_MALFORMED_RESPONSE_ERROR);
		} catch (RuntimeException e) {
			metricsHelper.recordStatusCodes(Constants.SMS_CARD, "RUN_TIME");
			logger.error("[ReminderService]:fetchSmsCards hitAPI Exception:{} for customer id {} and services {}",
					e.getMessage(), customerID, services);
			throw new SmsCardException(ErrorMessages.SMS_CARDS_MALFORMED_RESPONSE_ERROR);
		}
		if (getSmsCardsResponse != null
				&& (getSmsCardsResponse.getStatus() == 200 || getSmsCardsResponse.getStatus() == 204)) {
			List<SmsCard> smsCards = new ArrayList<>();
			if (getSmsCardsResponse.getData() != null) {
				for (Map.Entry<String, List<SmsCard>> smsCardsData : getSmsCardsResponse.getData().entrySet()) {
					for (SmsCard smsCard : smsCardsData.getValue()) {
						smsCards.add(smsCard);
					}
				}
				return smsCards;
			}
			return null;
		}
		logger.error("[ReminderService]:fetchSmsCards hitAPI got invalid response {}", getSmsCardsResponse);
		throw new SmsCardException(ErrorMessages.SMS_CARDS_MALFORMED_RESPONSE_ERROR);
	}
}
