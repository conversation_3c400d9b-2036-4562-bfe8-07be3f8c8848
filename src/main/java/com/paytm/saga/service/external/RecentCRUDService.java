package com.paytm.saga.service.external;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.newrelic.api.agent.Trace;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.RecentCRUDRequest;
import com.paytm.saga.dto.RecentCRUDResponse;
import com.paytm.saga.service.RecentRestTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.Callable;

@Service
public class RecentCRUDService {
    private final RecentRestTemplateService restTemplateService;
    private final ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
    private final CustomLogger logger = CustomLogManager.getLogger(RecentCRUDService.class);
    private final MetricsHelper metricsHelper;

    @Autowired
    public RecentCRUDService(@NonNull RecentRestTemplateService restTemplateService, @NonNull ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig,
                             @NonNull MetricsHelper metricsHelper) {
        this.restTemplateService=restTemplateService;
        this.externalEndpointsPropertiesConfig=externalEndpointsPropertiesConfig;
        this.metricsHelper = metricsHelper;
    }

    @Trace(dispatcher=true)
    @Retryable(value = {RuntimeException.class}, maxAttempts = 2)
    public RecentCRUDResponse markAsPaid(String recharge_number, String operator, Long customer_id, String reference_id, String paytype, String service, String plan_bucket, Double amount, String due_date, String bill_date, String expiry) throws RuntimeException{

         Format formatter = new SimpleDateFormat(Constants.CommonConstants.RECENT_CRUD_NOW_DATE_FORMAT);
        String today = formatter.format(new Date());
        if(expiry!=null) expiry = expiry + Constants.CommonConstants.RECENT_CRUD_STATIC_TIME;
        if(due_date!=null) due_date = due_date + Constants.CommonConstants.RECENT_CRUD_STATIC_TIME;

        if(amount == null) amount = 0.0;
        RecentCRUDRequest recentCRUDRequest = new RecentCRUDRequest(recharge_number, operator ,customer_id, reference_id, paytype, service, "bills", Math.round(amount), due_date, bill_date,today, expiry, plan_bucket );

        RecentCRUDResponse recentCRUDResponse;
        try {
            logger.info("[RecentCRUDService]:RecentCRUDResponse update:  hitAPI: " + externalEndpointsPropertiesConfig.getRecentsCRUDUpdate() + " Request Ops: " + recentCRUDRequest.toString());
            recentCRUDResponse = restTemplateService.executePutRequest(externalEndpointsPropertiesConfig.getRecentsCRUDUpdate(), RecentCRUDResponse.class, recentCRUDRequest);
            logger.info("[RecentCRUDService]:RecentCRUDResponse update: hitAPI Response: " + externalEndpointsPropertiesConfig.getRecentsCRUDUpdate() + " Request Ops: " + recentCRUDRequest.toString() + " Response: " + recentCRUDResponse);
            metricsHelper.recordSuccessRate(Constants.CommonConstants.RECENT_CRUD_UPDATE);           
        } catch (RestTemplateServiceException e) {
            logger.info("[RecentCRUDService]:RecentCRUDResponse update: hitAPI Exception: " + externalEndpointsPropertiesConfig.getRecentsCRUDUpdate() + " Request: " + recentCRUDRequest.toString() + " Resp: " + e.getDetailMessage());
            ObjectMapper mapper = new ObjectMapper();
            try {
            	metricsHelper.recordErrorRate(Constants.CommonConstants.RECENT_CRUD_UPDATE);
                recentCRUDResponse = mapper.readValue(e.getDetailMessage(), RecentCRUDResponse.class);
                if(Arrays.asList(Constants.CommonConstants.ACCEPTABLE_RECENT_ERROR_CODES).contains(recentCRUDResponse.getStatus_code())){
                    return recentCRUDResponse;
                }
                else{
                    throw new RuntimeException(e);
                }
            } catch (IOException ioException) {
                logger.error("RecentCRUDService: markAsPaid ",ioException);
                throw new RuntimeException(e);
            }

        } catch (Exception e){
            metricsHelper.recordErrorRate(Constants.CommonConstants.RECENT_CRUD_UPDATE);
            logger.error("RecentCRUDService: markAsPaid",e);
            throw new RuntimeException(e);
        }
        return recentCRUDResponse;
    }

    @Recover
    public RecentCRUDResponse recover(RuntimeException e){
        RecentCRUDResponse recentCRUDResponse = null;
        return recentCRUDResponse;
    }

    @Trace(dispatcher=true)
    public Callable<Object> markAsPaidTask(String recharge_number, String operator, Long customer_id, String reference_id, String paytype, String service, String plan_bucket, Double amount, String due_date, String bill_date, String expiry) {

        return new Callable<Object>() {
            public RecentCRUDResponse call() {
                RecentCRUDResponse recentCRUDResponse = null;
                try{
                    recentCRUDResponse = markAsPaid(recharge_number, operator, customer_id, reference_id, paytype, service, plan_bucket, amount, due_date, bill_date, expiry);
                } catch (Exception e){
                    logger.error("RecentCRUDService: markAsPaidTask",e);
                }
                return recentCRUDResponse;
            }
        };
    }


}
