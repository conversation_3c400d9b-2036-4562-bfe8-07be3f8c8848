package com.paytm.saga.service.external;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RestClientException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.BillsSyncMarkAsPaidRequest;
import com.paytm.saga.dto.BillsSyncMarkAsPaidResponse;
import com.paytm.saga.service.GenericRestClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.concurrent.Callable;

@Service
public class BillsSyncService {
    private final GenericRestClient genericRestClient;
    private final ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
    private final CustomLogger logger = CustomLogManager.getLogger(BillsSyncService.class);
    private final MetricsHelper metricsHelper;

    @Autowired
    public BillsSyncService(@NonNull GenericRestClient genericRestClient, @NonNull ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig, @NonNull MetricsHelper metricsHelper){
        this.genericRestClient = genericRestClient;
        this.externalEndpointsPropertiesConfig = externalEndpointsPropertiesConfig;
        this.metricsHelper = metricsHelper;
    }

    public Callable<Object> markAsPaidTask(String rechargeNumber, Long customerId, String service, String operator){
        return new Callable<Object>() {
            public BillsSyncMarkAsPaidResponse call(){
                BillsSyncMarkAsPaidResponse billsSyncMarkAsPaidResponse = null;
                try{
                    billsSyncMarkAsPaidResponse = markAsPaid(rechargeNumber,customerId,service,operator);
                }
                catch (Exception e){
                    logger.error("[BillsSyncService.markAsPaidTask]:: Error occurred , e=",e);
                }
                return  billsSyncMarkAsPaidResponse;
            }
        };
    }

    @Retryable(value = { RestClientException.class }, maxAttempts = 3)
    private BillsSyncMarkAsPaidResponse markAsPaid(String rechargeNumber, Long customerId, String service, String operator){
        BillsSyncMarkAsPaidRequest billsSyncMarkAsPaidRequest = new BillsSyncMarkAsPaidRequest(rechargeNumber,String.valueOf(customerId),service,operator);

        BillsSyncMarkAsPaidResponse billsSyncMarkAsPaidResponse = null;
        try{
            billsSyncMarkAsPaidResponse = genericRestClient.post(externalEndpointsPropertiesConfig.getBillsSyncMarkAsPaid(),billsSyncMarkAsPaidRequest,BillsSyncMarkAsPaidResponse.class).getBody();
            metricsHelper.recordSuccessRate(Constants.CommonConstants.BILLS_SYNC_MARK_AS_PAID);
        }
        catch (Exception e){
            metricsHelper.recordErrorRate(Constants.CommonConstants.BILLS_SYNC_MARK_AS_PAID);
            logger.error("Exception occurred while calling billsSyncMarkAsPaid API, url ={}, requestBody = {} and response  = {}",externalEndpointsPropertiesConfig.getBillsSyncMarkAsPaid(),billsSyncMarkAsPaidRequest,billsSyncMarkAsPaidResponse);
            throw e;
        }
        return billsSyncMarkAsPaidResponse;
    }
}
