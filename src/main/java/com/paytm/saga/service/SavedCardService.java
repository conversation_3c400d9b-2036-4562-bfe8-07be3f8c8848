package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.common.exception.PgServiceException;
import com.paytm.saga.common.exception.SavedCardServiceException;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.CommonCache;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.external.PGService;
import com.paytm.saga.util.OMSToRechargeStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.NotNull;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SavedCardService {
    private static final CustomLogger logger = CustomLogManager.getLogger(SavedCardService.class);

    private PGService pgService;
    private DCATService dcatService;
    private CommonCacheService commonCacheService;
    private ReminderHistoryService reminderHistoryService;
    private ChannelHistoryRepositoryWrapperService channelHistoryRepository;

    @Autowired
    public SavedCardService(@NonNull DCATService dcatService,
                            @NonNull PGService pgService,
                            @NotNull CommonCacheService commonCacheService,
                            @NonNull ReminderHistoryService reminderHistoryService,
                            @NonNull ChannelHistoryRepositoryWrapperService channelHistoryRepository
    ) {
        this.dcatService = dcatService;
        this.pgService = pgService;
        this.commonCacheService = commonCacheService;
        this.reminderHistoryService = reminderHistoryService;
        this.channelHistoryRepository = channelHistoryRepository;
    }

    private String isPaytmFirstCard(String displayName) {
        return (displayName !=null && displayName.toLowerCase().contains(Constants.CommonConstants.PAYTM_FIRST_CARD_DISPLAY_NAME))
                ? "1"
                : "0";
    }

    private ProductInfo getCachedData(String key) {
        List<CommonCache> listCommonCache = commonCacheService.getCache(key);
        if (!(listCommonCache == null || listCommonCache.isEmpty())) {
            logger.info("[DCATService.getPlanDetails] hitCategoryApiWrapper, serving from cache: {}", key);
            try {
                ObjectMapper mapper = new ObjectMapper();
                return mapper.readValue(listCommonCache.get(0).getCacheValue(), ProductInfo.class);
            } catch (Exception e) {
                logger.error("[DCATService.getPlanDetails] hitCategoryApiWrapper Exception1:", e);
            }
        }
        return null;
    }

    private ProductInfo getProductInfoFromDcatResponse(DCATCategoryResponseModel dcatCategoryResponseModel, String key,
                                                      String cardType) {
        ProductInfo productInfo = null;
        String service = Constants.FINANCIAL_SERVICES;
        for (DCATCategoryGroupingAggModel dcatCategoryGroupingAggModel : dcatCategoryResponseModel.getGroupings()
                .getAggs()) {
            String isPaytmFirstCard = dcatCategoryGroupingAggModel.getIsPaytmFirstCard() != null
                    && dcatCategoryGroupingAggModel.getIsPaytmFirstCard().equals("1") ? "1" : "0";
            List<DCATCategoryGroupingAggAggModel> dcatCategoryGroupingAggAggModels = dcatCategoryGroupingAggModel
                    .getAggs();
            for (DCATCategoryGroupingAggAggModel dcatCategoryGroupingAggAggModel : dcatCategoryGroupingAggAggModels) {
                DCATCategoryGroupingAggAggProduct dcatCategoryGroupingAggAggProduct = dcatCategoryGroupingAggAggModel
                        .getProductList().get(0);
                String bankCode = dcatCategoryGroupingAggAggProduct.getBankCode();//dcatCategoryGroupingAggModel.getValue();
                String cardNetwork = dcatCategoryGroupingAggAggProduct.getCardNetwork();
                Long productId = dcatCategoryGroupingAggAggProduct.getProductId();
                String productService = dcatCategoryGroupingAggAggProduct.getService();
                if (key.equals(generateFinancialServicesProductMapKey(productService, bankCode, cardNetwork,
                        isPaytmFirstCard))) {
                    productInfo = new ProductInfo(productId, dcatCategoryGroupingAggAggProduct.getOperator(),
                            Long.parseLong(Constants.CommonConstants.DCAT_GROUPING_ID_MAP.get(service)), service,
                            cardType, bankCode, cardNetwork);
                    ObjectMapper mapper = new ObjectMapper();
                    String body;
                    try {
                        body = mapper.writeValueAsString(productInfo);
                        commonCacheService.setCache(key, body, Constants.CommonConstants.DCAT_CATEGORY_CACHE_TTL);
                    } catch (JsonProcessingException e) {
                        logger.error("JsonProcessingException: " + e.getMessage());
                    }
                }
            }
        }
        return productInfo;
    }

    public SavedCardResponse getSavedCards(Long customerId, SavedCardApiRequest savedCardApiRequest, String version, String appendBillsFlag,boolean isCoft, boolean onlyActiveBills)
            throws SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsList = null;
        List<SavedCardDetails> filteredSavedCardDetailsList = null;
        Boolean filterTokenCards=Boolean.FALSE;
        try {
            filterTokenCards = (version.equals(Constants.API_VERISON_V2)) ? Boolean.FALSE : Boolean.TRUE;
            savedCardDetailsList = pgService.getSavedCards(customerId,filterTokenCards);

            filteredSavedCardDetailsList = dedupeSavedCards(savedCardDetailsList, savedCardApiRequest,filterTokenCards,isCoft);
        } catch (RuntimeException|PgServiceException e) {
            logger.error("[DCATService.getSavedCards] Exception:",e);
            throw new SavedCardServiceException("getting exception in saved card api");
        }
        return prepareSavedCardResponse(customerId, savedCardApiRequest, filteredSavedCardDetailsList,appendBillsFlag,isCoft,onlyActiveBills);
    }

    private List<SavedCardDetails> dedupeSavedCards(List<SavedCardDetails> savedCardDetailsList, SavedCardApiRequest savedCardApiRequest, Boolean filterTokenCards,boolean isCoft) {
        // If filterTokenCards = true , that means token cards already filtered in API, no need to filter here
        if(Boolean.TRUE.equals(filterTokenCards)) {
        	List<SavedCardDetails> filteredSavedCardsList=new ArrayList<>();
        	for(SavedCardDetails savedCard : savedCardDetailsList) {
            	if(savedCard != null && savedCard.getCardType() != null
                        && !savedCard.getCardType().equalsIgnoreCase(savedCardApiRequest.getCardType())) {
            		continue;
            	}
            	filteredSavedCardsList.add(savedCard);                    
            }
        	return filteredSavedCardsList;
        }else {
            Map<String, SavedCardDetails> cinCards = new HashMap<>();
            Map<String, SavedCardDetails> tinCards = new HashMap<>();
            List<SavedCardDetails> filteredSavedCardsList;
            for(SavedCardDetails savedCard : savedCardDetailsList) {
            	if(savedCard != null && savedCard.getCardType() != null
                        && !savedCard.getCardType().equalsIgnoreCase(savedCardApiRequest.getCardType())) {
            		continue;
            	}
                if(savedCard.isCardCoft()) {
                    tinCards.put(getDedupeKey(savedCard,true), savedCard);
                } else {
                    cinCards.put(getDedupeKey(savedCard,isCoft), savedCard);
                }
            }
            if(isCoft) {
                for (Map.Entry<String, SavedCardDetails> card : tinCards.entrySet()) {
                    cinCards.put(card.getKey(), card.getValue());
                }
                filteredSavedCardsList = cinCards.entrySet().stream()
                        .map(Map.Entry::getValue)
                        .collect(Collectors.toList());
                
            }else {
            	for (Map.Entry<String, SavedCardDetails> card : cinCards.entrySet()) {
            		String key=getDedupeKey(card.getValue(),true);
            		if(tinCards.containsKey(key)){
            			tinCards.remove(key);
            		}
            		tinCards.put(getDedupeKey(card.getValue(),isCoft), card.getValue()); 	
                }
            	filteredSavedCardsList = tinCards.entrySet().stream()
                        .map(Map.Entry::getValue)
                        .collect(Collectors.toList());
                
            }
            return filteredSavedCardsList;
        }
    }

    private String getDedupeKey(SavedCardDetails savedCardDetails,boolean isCoft) {
        if (Objects.nonNull(savedCardDetails)) {
        	if(isCoft)
	            return savedCardDetails.getCardSuffix() +
	                    savedCardDetails.getIssuingBankName() +
	                    savedCardDetails.getCardScheme();
        	else {
        		if(!savedCardDetails.isCardCoft()) {
            		return savedCardDetails.getCardFirstSixDigits()+ 
            				"_" + savedCardDetails.getCardSuffix() +
                            savedCardDetails.getIssuingBankName() +
                            savedCardDetails.getCardScheme();        			
        		}else {
            		return savedCardDetails.getCardSuffix() +
                            savedCardDetails.getIssuingBankName() +
                            savedCardDetails.getCardScheme();        			
        		}

        	}
        } else
            return "";
    }

    private SavedCardResponse prepareSavedCardResponse(Long customerId, SavedCardApiRequest savedCardApiRequest, List<SavedCardDetails> savedCardDetailsList, String appendBillsFlag, boolean isCoft, boolean onlyActiveBills) {
        SavedCardResponse savedCardResponse = new SavedCardResponse();

        List<SavedCardApiResponse> res = new ArrayList<>();
        savedCardResponse.setSavedCardApiResponses(res);
        if (Objects.nonNull(savedCardDetailsList)) {
            for (SavedCardDetails savedCardDetails : savedCardDetailsList) {
                if (savedCardDetails != null
                        && savedCardDetails.getCardType().equalsIgnoreCase(savedCardApiRequest.getCardType())) {
                    if(!CollectionUtils.isEmpty(savedCardDetails.getIssuerMetadata()) && Objects.nonNull(savedCardDetails.getIssuerMetadata().get("shortDescription"))) {
                        String cardVariant = savedCardDetails.getIssuerMetadata().get("shortDescription").toString();
                        savedCardDetails.setIssuingBankCardVariant(cardVariant);
                    }
                    ProductInfo productInfo = getProductInfoForCard(savedCardDetails, savedCardApiRequest.getCardType());
                    if ((savedCardDetails.isCardCoft()==true && "ACTIVE".equals(savedCardDetails.getTokenStatus())) || (savedCardDetails.isCardCoft()==false)) {
                        if (productInfo != null && (savedCardDetails.getSavedCardId() != null || savedCardDetails.getPanUniqueReference() != null)) {
                            res.add(createResponseObject(savedCardDetails, productInfo, isCoft, savedCardApiRequest));
                        }
                    }
                }
            }
        }
        Map<String, SavedCardIntermediateObj>  savedCardIntermediateObjMap = getReminderDetailsForSavedCards(customerId, savedCardResponse.getSavedCardApiResponses());
        savedCardIntermediateObjMap = getChannelHistoryForSavedCards(customerId, savedCardResponse.getSavedCardApiResponses(), savedCardIntermediateObjMap);
        if (appendBillsFlag.equals(Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE)) {
        	appendBillsObj(savedCardResponse, savedCardIntermediateObjMap, onlyActiveBills);
		}
        return savedCardResponse;
    }

    private ProductInfo getProductInfoForCard(SavedCardDetails savedCardDetails, String cardType) {
        ProductInfo productInfo = null;
        String service = Constants.FINANCIAL_SERVICES;
        String paytmFirstCard = isPaytmFirstCard(savedCardDetails.getDisplayName());
        DCATCategoryResponseModel dcatCategoryResponseModel = null;
        String key = generateFinancialServicesProductMapKey(service, savedCardDetails.getIssuerCode(),
                savedCardDetails.getCardScheme(), paytmFirstCard);
        productInfo = getCachedData(key);
        if (productInfo == null) {
            logger.info("[SavedCardService.getSavedCards] data not found in caching for key : %s {}",key);
            dcatCategoryResponseModel = dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES);
            productInfo = getProductInfoFromDcatResponse(dcatCategoryResponseModel, key,
                    cardType);
        }
        return productInfo;
    }

    private SavedCardApiResponse createResponseObject(SavedCardDetails savedCardDetails, ProductInfo productInfo,boolean isCoft, SavedCardApiRequest savedCardApiRequest) {

        SavedCardApiResponse savedCardApiResponse = new SavedCardApiResponse();
        savedCardApiResponse.setProduct(productInfo);
        savedCardApiResponse.setOperator(productInfo.getOperator());
        savedCardApiResponse.setType("savedCard");
        savedCardApiResponse.setPriority(Constants.SAVED_CARD_PRIORIY);
        SavedCardAdditionalInfo savedCardAdditionalInfo = new SavedCardAdditionalInfo();
        if(savedCardDetails.isCardCoft()) {
            savedCardAdditionalInfo.setPanUniqueReference(savedCardDetails.getPanUniqueReference());
            savedCardAdditionalInfo.setCardCoft(savedCardDetails.isCardCoft());
            savedCardAdditionalInfo.setEligibleForCoft(savedCardDetails.isEligibleForCoft());
            savedCardAdditionalInfo.setTin(savedCardDetails.getSavedCardId());
            savedCardAdditionalInfo.setTokenStatus(savedCardDetails.getTokenStatus());
        } else {
            savedCardAdditionalInfo.setCin(savedCardDetails.getSavedCardId());
        }
        savedCardApiResponse.setAdditionalInfo(savedCardAdditionalInfo);
        String rechargeNumberForDisplay=getCardNumberDisplayFormat(savedCardDetails.getCardSuffix(), savedCardDetails.getCardScheme());
        String rechargeNumber=getRechargeNumberForRecents(savedCardDetails.getCardSuffix(), savedCardDetails.getCardScheme());
        if(!isCoft && !savedCardDetails.isCardCoft()) {
        	rechargeNumber = (savedCardDetails.getCardFirstSixDigits().substring(0,4)+" "+savedCardDetails.getCardFirstSixDigits().substring(4,6))+rechargeNumber.substring(7, rechargeNumber.length());
        	rechargeNumberForDisplay=(savedCardDetails.getCardFirstSixDigits().substring(0,4)+"  "+savedCardDetails.getCardFirstSixDigits().substring(4,6))+rechargeNumberForDisplay.substring(8, rechargeNumberForDisplay.length());
        }
        savedCardApiResponse.setRechargeNumberForDisplay(rechargeNumberForDisplay);
        savedCardApiResponse.setRechargeNumber(rechargeNumber);
        savedCardApiResponse.setCta(getSavedCardCTA()); // CTA to be driven from BFF
        if(savedCardApiRequest.getIsCardSkinRequired() && savedCardDetails.getMediaAssets() != null ){
            CardDetails cardDetails = null;
            if(savedCardDetails.getIssuingBankCardVariant()!=null)
                cardDetails = FeatureConfigCache.getInstance().getCardVariantSkinDetails(savedCardDetails.getIssuingBankCardVariant());
            if(cardDetails!=null){
                if(Boolean.TRUE.equals(cardDetails.getKeepCardSkin())) {
                    savedCardApiResponse.setMediaAssets(savedCardDetails.getMediaAssets());
                    savedCardApiResponse.setColors(savedCardDetails.getColors());
                }
                if(Boolean.TRUE.equals(cardDetails.getKeepCardVariant()))
                    savedCardApiResponse.setIssuingBankCardVariant(savedCardDetails.getIssuingBankCardVariant());
            }else {
                savedCardApiResponse.setMediaAssets(savedCardDetails.getMediaAssets());
                savedCardApiResponse.setIssuingBankCardVariant(savedCardDetails.getIssuingBankCardVariant());
                savedCardApiResponse.setColors(savedCardDetails.getColors());
            }
        }
        return savedCardApiResponse;
    }

    private String getCardNumberDisplayFormat(String cardSuffix, String cardScheme) {
        String cardNumber = getFullMaskedRechargeNumber(cardSuffix, cardScheme);
        switch (cardScheme) {
            case Constants.AMEX:
                String updatedCardNumber = cardNumber;
                if (cardNumber.length() > 6) {
                    String amexCross = "XXXXX";
                    updatedCardNumber = cardNumber.substring(0, 6) + amexCross
                            + cardNumber.substring(cardNumber.length() - 4, cardNumber.length());
                }
                return this.formatTextAmExExtraspace(updatedCardNumber);
            case Constants.MASTERCARD:
            case Constants.VISA:
            case Constants.DISCOVER:
                return this.formatTextVisaMasterCardExtraspace(cardNumber);
            case Constants.DINERS:
                updatedCardNumber = cardNumber;
                if (cardNumber.length() > 6) {
                    String dinerCross = "XXXX";
                    updatedCardNumber = cardNumber.substring(0, 6) + dinerCross
                            + cardNumber.substring(cardNumber.length() - 4, cardNumber.length());
                }
                return this.formatTextAmExExtraspace(updatedCardNumber);
            default:
                return this.formatTextVisaMasterCardExtraspace(cardNumber);
        }
    }

    private String formatTextVisaMasterCardExtraspace(CharSequence text) {
        StringBuilder formatted = new StringBuilder();
        int count = 0;
        for (int i = 0; i < text.length(); ++i) {
            if (Character.isDigit(text.charAt(i)) || text.charAt(i) == 'X') {
                if (count % 4 == 0 && count > 0)
                    formatted.append("  ");
                formatted.append(text.charAt(i));
                ++count;
            }
        }
        return formatted.toString();
    }

    private String formatTextAmExExtraspace(CharSequence charSequence) {
        String text = "";
        text = charSequence.toString();
        StringBuilder formatted = new StringBuilder();
        int count = 0;
        for (int i = 0; i < text.length(); ++i) {
            if (Character.isDigit(text.charAt(i)) || text.charAt(i) == 'X') {
                if (count > 0 && ((count == 4) || (count == 10))) {
                    formatted.append("  ");
                }
                formatted.append(text.charAt(i));
                ++count;
            }
        }
        return formatted.toString();
    }

    private String getRechargeNumberForRecents(String cardSuffix, String cardType) {
        String rechargeNumber = getFullMaskedRechargeNumber(cardSuffix, cardType);
        rechargeNumber = rechargeNumber.replace(" ", "");
        return insertPeriodically(rechargeNumber, " ",4);
    }

    private String getFullMaskedRechargeNumber(String cardSuffix, String cardScheme) {
        int cardLength = getCardLengthOnType(cardScheme);
        StringBuilder formatted = new StringBuilder();
        for(int i=0; i<cardLength-4; i++) {
            formatted.append("X");
        }
        return formatted + cardSuffix;
    }

    private int getCardLengthOnType(String cardScheme) {
        switch (cardScheme) {
            case Constants.AMEX:
                return 15;
            case Constants.DINERS:
                return 14;
            case Constants.MASTERCARD:
            case Constants.VISA:
            case Constants.DISCOVER:
            default:
                return 16;
        }
    }

    public String insertPeriodically(String text, String insert, int period) {
        StringBuilder builder = new StringBuilder(text.length() + insert.length() * (text.length() / period) + 1);
        int index = 0;
        String prefix = "";
        while (index < text.length()) {
            // Don't put the insert in the very first iteration.
            // This is easier than appending it *after* each substring
            builder.append(prefix);
            prefix = insert;
            builder.append(text.substring(index, Math.min(index + period, text.length())));
            index += period;
        }
        return builder.toString();
    }

    private String generateFinancialServicesProductMapKey(String productService, String bankCode, String cardNetwork,
                                                         String isPaytmFirstCard) {
        return (isPaytmFirstCard + ":" + productService + ":" + bankCode + ":" + cardNetwork).toLowerCase();
    }

    private List<SavedCardCTAResponse> getSavedCardCTA() {
        List<SavedCardCTAResponse> cta = new ArrayList<>();
        SavedCardCTAResponse cta1 = new SavedCardCTAResponse();
        cta1.setType("button");
        cta.add(cta1);

        SavedCardCTAResponse cta2 = new SavedCardCTAResponse();
        cta2.setType("childCta");
        SavedCardCTAResponse savedCardCTAResponse2Child = new SavedCardCTAResponse();
        savedCardCTAResponse2Child.setType("deleteCard");
        List<SavedCardCTAResponse> savedCardCTAChilds = new ArrayList<>();
        savedCardCTAChilds.add(savedCardCTAResponse2Child);
        cta2.setCta(savedCardCTAChilds);
        cta.add(cta2);
        return cta;
    }

    private Map<String, SavedCardIntermediateObj> getReminderDetailsForSavedCards(Long customerId, List<SavedCardApiResponse> savedCardResponse){
        List<String> rechargeNumbers = new ArrayList<>();
        List<String> services = new ArrayList<>();
        services.add(Constants.FINANCIAL_SERVICES);

        savedCardResponse.stream().forEach(s-> {
            if(s!=null && s.getRechargeNumber()!=null){
                rechargeNumbers.add(s.getRechargeNumber());
            }
        });

        Map<String, ReminderHistory> reminderHistoryMap = new HashMap<String, ReminderHistory>() {};
        Map<String, SavedCardIntermediateObj> savedCardIntermediateObjMap = new HashMap<String, SavedCardIntermediateObj>() {};
        if(!rechargeNumbers.isEmpty()){
            List<ReminderHistory> reminderHistories = reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumbers, services);
            if (reminderHistories != null) {
                reminderHistories.sort(Comparator.comparing(ReminderHistory::getUpdatedAt).reversed());
            }
            if(Objects.nonNull(reminderHistories)) {
                for(ReminderHistory reminderHistoryRow: reminderHistories){
                    if(reminderHistoryRow.getPar() !=null &&
                            reminderHistoryMap.get(reminderHistoryRow.getPar())==null){
                        reminderHistoryMap.put(reminderHistoryRow.getPar(), reminderHistoryRow);
                    } else if(reminderHistoryRow.getReference_id()!=null &&
                            reminderHistoryMap.get(reminderHistoryRow.getReference_id())==null){
                            reminderHistoryMap.put(reminderHistoryRow.getReference_id(), reminderHistoryRow);
                    }
                }
            }
            reminderHistories.clear();
        }
        for(SavedCardApiResponse savedCardApiResponseRow : savedCardResponse){
            if(savedCardApiResponseRow!=null &&
                    savedCardApiResponseRow.getAdditionalInfo()!=null &&
                    (savedCardApiResponseRow.getAdditionalInfo().getCin()!=null ||
                            savedCardApiResponseRow.getAdditionalInfo().getPanUniqueReference()!=null)){
                String cinOrPan;
                SavedCardIntermediateObj s1 = new SavedCardIntermediateObj();
                if(savedCardApiResponseRow.getAdditionalInfo().isCardCoft()) {
                    cinOrPan = savedCardApiResponseRow.getAdditionalInfo().getPanUniqueReference();
                    s1.setPanUniqueReference(cinOrPan);
                } else {
                    cinOrPan = savedCardApiResponseRow.getAdditionalInfo().getCin();
                    s1.setCin(cinOrPan);
                }
                s1.setCustomerId(customerId);
                s1.setRechargeNumber(savedCardApiResponseRow.getRechargeNumber());
                if(reminderHistoryMap.get(cinOrPan)!=null){
                    s1.setReminderHistoryObj(reminderHistoryMap.get(cinOrPan));
                }
                savedCardIntermediateObjMap.put(cinOrPan, s1);
            }

        }
        return savedCardIntermediateObjMap;
    }

    private Map<String, SavedCardIntermediateObj> getChannelHistoryForSavedCards(Long customerId, List<SavedCardApiResponse> savedCardResponse, Map<String , SavedCardIntermediateObj> savedCardIntermediateObj){
        List<String> rechargeNumbers = new ArrayList<>();
        for(SavedCardApiResponse savedCardApiResponseRow : savedCardResponse){
            if(savedCardApiResponseRow!=null &&
                    savedCardApiResponseRow.getAdditionalInfo()!=null ){
                if(savedCardApiResponseRow.getAdditionalInfo().isCardCoft() ) {
                    rechargeNumbers.add(savedCardApiResponseRow.getAdditionalInfo().getPanUniqueReference());
                } else {
                    rechargeNumbers.add(savedCardApiResponseRow.getAdditionalInfo().getCin());
                }
            }
        }

        if(!rechargeNumbers.isEmpty()){
            List<ChannelHistory> channelHistoryList =  channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumbers, Constants.FINANCIAL_SERVICES);
            for(ChannelHistory channelHistoryRow: channelHistoryList){
                String state = OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistoryRow.getStatus(),
                        channelHistoryRow.getInResponseCode(), channelHistoryRow.getPaymentStatus());

                if(Arrays.asList(Constants.SAVED_CARDS_OMS_STATES).contains(state) &&
                        savedCardIntermediateObj.get(channelHistoryRow.getRechargeNumber())!=null &&
                        savedCardIntermediateObj.get(channelHistoryRow.getRechargeNumber()).getChannelHistoryObj()==null){
                    savedCardIntermediateObj.get(channelHistoryRow.getRechargeNumber()).setChannelHistoryObj(channelHistoryRow);
                }
            }
        }

        return savedCardIntermediateObj;

    }

    private SavedCardResponse appendBillsObj(SavedCardResponse savedCardResponse, Map<String , SavedCardIntermediateObj> savedCardIntermediateObj, boolean onlyActiveBills){
        List<SavedCardApiResponse> row = new ArrayList<>();
        List<SavedCardBillStateEnum> savedCardBillStateEnums = new ArrayList<>();
        savedCardBillStateEnums.add(SavedCardBillStateEnum.NO_BILL);
        savedCardBillStateEnums.add(SavedCardBillStateEnum.FULLY_PAID);
        savedCardBillStateEnums.add(SavedCardBillStateEnum.MARK_AS_PAID);
        for(SavedCardApiResponse savedCardApiResponseRow : savedCardResponse.getSavedCardApiResponses()){
            savedCardApiResponseRow.setBills(getBills(savedCardApiResponseRow, savedCardIntermediateObj));
            List<SavedCardBillsObj> savedCardBillsObjs = savedCardApiResponseRow.getBills();
            if(savedCardBillsObjs!=null&&savedCardBillsObjs.size()>0&&onlyActiveBills){
                if(savedCardBillStateEnums.indexOf(savedCardBillsObjs.get(0).getBillState())>-1){
                    continue;
                }
                else {
                    row.add(savedCardApiResponseRow);
                }
            }
        }
        if (onlyActiveBills) {
            savedCardResponse.setSavedCardApiResponses(row);
        }
        return savedCardResponse;
    }

    private List<SavedCardBillsObj> getBills(SavedCardApiResponse savedCardApiResponseRow, Map<String , SavedCardIntermediateObj> savedCardIntermediateObjMap){
        List<SavedCardBillsObj> bills = new ArrayList<>();
        if(savedCardApiResponseRow==null ||
                savedCardApiResponseRow.getAdditionalInfo()==null ||
                (savedCardApiResponseRow.getAdditionalInfo().getCin()==null && savedCardApiResponseRow.getAdditionalInfo().getPanUniqueReference()==null) ||
                savedCardIntermediateObjMap==null){
            return bills;
        }
        String cinOrPan;
        if(savedCardApiResponseRow.getAdditionalInfo().isCardCoft()) {
            cinOrPan = savedCardApiResponseRow.getAdditionalInfo().getPanUniqueReference();
        } else {
            cinOrPan = savedCardApiResponseRow.getAdditionalInfo().getCin();
        }
        SavedCardIntermediateObj savedCardIntermediateObj = null;
        if(cinOrPan !=null)
        	savedCardIntermediateObj = savedCardIntermediateObjMap.get(cinOrPan);
        SavedCardBillsObj savedCardBillsObj = new SavedCardBillsObj();
        DateFormat dateFormat = new SimpleDateFormat(Constants.CommonConstants.DATE_FORMAT);
        SavedCardBillStateEnum state = null;

        ReminderHistory reminderData = null;
        ChannelHistory channelHistoryData = null;
        if(savedCardIntermediateObj!=null){
            reminderData = savedCardIntermediateObj.getReminderHistoryObj();
            channelHistoryData = savedCardIntermediateObj.getChannelHistoryObj();
        }
        Date billDate=null;
        if(reminderData!=null){
            if(reminderData.getCurrentMinBillAmount()!=null){
                savedCardBillsObj.setMin_due_amount(reminderData.getCurrentMinBillAmount());
                if(reminderData.getCurrentMinBillAmount()>0) {
                    state = SavedCardBillStateEnum.MIN_DUE;
                } else if(reminderData.getCurrentMinBillAmount()<=0){
                    if(channelHistoryData==null){
                        state = SavedCardBillStateEnum.NEW_USER_MIN_DUE_PAID;
                    } else {
                        state = SavedCardBillStateEnum.MIN_DUE_PAID;
                    }
                }
            }
            else{
                if(reminderData.getAmount()!=null && reminderData.getAmount()>0){
                    logger.info("SavedCardService::getBills state is complete due");
                    state = SavedCardBillStateEnum.COMPLETE_DUE;
                }
            }

            if(reminderData.getAmount()!=null){
                if(reminderData.getAmount()>0){
                    savedCardBillsObj.setAmount(reminderData.getAmount());
                }
                if(reminderData.getAmount()<=0){
                    if(channelHistoryData==null || reminderData.getBill_date()==null || reminderData.getDue_date()==null) {
                        state = SavedCardBillStateEnum.NO_BILL;
                    } else{
                        savedCardBillsObj.setAmount(reminderData.getAmount());
                        state = SavedCardBillStateEnum.FULLY_PAID;
                    }
                }
            } else{
                state = SavedCardBillStateEnum.NO_BILL;
            }


            billDate =reminderData.getBill_date();
            Date dueDate = reminderData.getDue_date();

            if(dueDate!=null && !state.equals(SavedCardBillStateEnum.NO_BILL)){
                Date currentDate = new Date();
                if((currentDate.getTime() - dueDate.getTime())/(1000*60*60*24) > Constants.SAVED_CARD_BILL_VISIBLE_THRESHOLD_DAYS){
                    state = SavedCardBillStateEnum.NO_BILL;
                    savedCardBillsObj.setMin_due_amount(null);
                    savedCardBillsObj.setAmount(null);
                }
            }
            if(state!=null && !state.equals(SavedCardBillStateEnum.NO_BILL)){
                if(billDate!=null){
                    String billDateString = dateFormat.format(billDate);
                    savedCardBillsObj.setBill_date(billDateString);
                }
                if(dueDate!=null){
                    String dueDateString = dateFormat.format(dueDate);
                    savedCardBillsObj.setDue_date(dueDateString);
                }

                savedCardBillsObj.setOriginal_due_amount(reminderData.getOriginalAmount());
                savedCardBillsObj.setOriginal_min_due_amount(reminderData.getOriginalMinBillAmount());
            }
            if(Objects.nonNull(reminderData.getStatus()) && reminderData.getStatus()==15){
                    Date createdAt = reminderData.getUpdatedAt();
                    String createdAtString = dateFormat.format(createdAt);
                    savedCardBillsObj.setMark_as_paid_time(createdAtString);
                    state = SavedCardBillStateEnum.MARK_AS_PAID;
                }

        } else{
            state = SavedCardBillStateEnum.NO_BILL;
        }
        if(channelHistoryData!=null && channelHistoryData.getEventType()!=null){
            Date createdAt = channelHistoryData.getCreatedTime();
            if(createdAt == null){
                createdAt=channelHistoryData.getTransactionTime();
            }
            if(channelHistoryData.getEventType().equals(EventTypes.MARKED_AS_PAID) && (Objects.isNull(billDate) || createdAt.compareTo(billDate)>=0 )){
                String createdAtString = dateFormat.format(createdAt);
                savedCardBillsObj.setMark_as_paid_time(createdAtString);
                state = SavedCardBillStateEnum.MARK_AS_PAID;
            } else{
                String createdAtString = dateFormat.format(createdAt);
                String amount = channelHistoryData.getAmount();
                Double amountDouble = Double.parseDouble(amount);
                savedCardBillsObj.setLast_paid_amount(amountDouble);
                savedCardBillsObj.setOrder_date(createdAtString);
                savedCardBillsObj.setOrder_id(channelHistoryData.getOrderId());
            }
        }

        if(Objects.nonNull(reminderData)&&reminderData.getStatus()==14&&reminderData.getLastPaidAmount()!=null){
            savedCardApiResponseRow.setPaidOutside(Constants.PAID_OUTSIDE_PAYTM);
            savedCardBillsObj.setLast_paid_amount(reminderData.getLastPaidAmount());
        }
        savedCardBillsObj.setBillState(state);

        bills.add(savedCardBillsObj);
        return bills;
    }

}