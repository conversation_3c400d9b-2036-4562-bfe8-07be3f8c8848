package com.paytm.saga.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.property.DCATConfig;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.configuration.property.RPSConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.*;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.MetricConstants.RPS_API;

@Service
public class RPSService {
    private final RPSConfig rpsConfig;
    private final ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
    private final RpsRestTemplateService restTemplateService;
    private final DCATService dcatService;
    private final CustomLogger logger = CustomLogManager.getLogger(RPSService.class);

    @Autowired
    @Qualifier("monitoringClient")
    private StatsDClient monitoringClient;

    @Autowired
    public RPSService(@NonNull RpsRestTemplateService restTemplateService, @NonNull RPSConfig rpsConfig,
                      @NonNull ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig,
                      DCATService dcatService) {
        this.restTemplateService = restTemplateService;
        this.externalEndpointsPropertiesConfig = externalEndpointsPropertiesConfig;
        this.dcatService = dcatService;
        this.rpsConfig = rpsConfig;
    }

    @Retryable(value = {RuntimeException.class}, maxAttempts = 2)
    public RpsGetPlansResponse hitSearchAPI(String categoryId, String version, String channel, String operator,
                                            String amount, String circle, String productId, String rechargeNumber, String service) throws RuntimeException {

        String url = externalEndpointsPropertiesConfig.getPlansUrl() + categoryId + "/search";
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("locale", Constants.CommonConstants.DCAT_LOCALE);
        queryMap.put("version", version);
        queryMap.put("channel", channel);
        RPSApiRequest rpsApiRequest = rpsRequestBody(operator, circle, amount);

        rpsApiRequest.setProductId(productId);
        rpsApiRequest.setRechargeNumber(rechargeNumber);
        rpsApiRequest.setUserPlansActive(true);

        Map<String, String> requestHeaders = new HashMap<>();
        requestHeaders.put("Content-Type", "application/json");
        RpsGetPlansResponse rpsGetPlanResponse;
        logger.info("Request body for RPS {}",rpsApiRequest);
        try {
            rpsGetPlanResponse = restTemplateService.executePostRequest(url, requestHeaders, queryMap, RpsGetPlansResponse.class, rpsApiRequest);
            logger.info("[RPSService.hitSearchAPI:Response] : Endpoint: " + url + " queryMap: " + queryMap + " Request : " + rpsApiRequest + " Response : "
                    + rpsGetPlanResponse);
        } catch (Exception e) {
            pushToDD(RPS_API, "FAILURE");
            logger.error("[RPSService.hitSearchAPI] Getting Exception: {} for request : {}, and param : {}", e,rpsApiRequest,queryMap);
            throw new RuntimeException(e);
        }
        return rpsGetPlanResponse;
    }

    @Recover
    public RpsGetPlansResponse recover(RuntimeException e) {
        RpsGetPlansResponse rpsGetPlansResponse = null;
        return rpsGetPlansResponse;
    }

    public DCATGetPlanResponse getPlanDetails(String categoryId, String version, String channel, String operator,
                                              String amount, String circle, String productId, String rechargeNumber, String service) {

        if (categoryId == null || version == null || channel == null || operator == null || amount == null
                || circle == null || service == null) {
            DCATGetPlanResponse dcatGetPlanResponse = null;
            return dcatGetPlanResponse;
        }

        ObjectMapper mapper = new ObjectMapper();
        // String key = dcatService.generateCacheKey(categoryId, version, channel, operator, amount, circle);
        service = service.toLowerCase();
        RpsGetPlansResponse rpsGetPlansResponse = null;
        DCATGetPlanResponse dcatGetPlanResponse = null;
        // Get data from Cache.
        // List<CommonCache> listCommonCache = commonCacheService.getCache(key);
//        if (listCommonCache != null && listCommonCache.isEmpty()) {
        // Hit DCAT and store in cache
        try {
            logger.info("[RPSService.getPlanDetails] Hitting DCAT category Api" + categoryId + ":" + version + ":" + channel
                    + ":" + operator + ":" + amount + ":" + circle);
            DCATCategoryResponseModel dcatCategoryResponseModel = dcatService.hitCategoryApiWrapper(service);

            if (dcatCategoryResponseModel != null) {

                String actual_operator = dcatService.getActualOperatorName(dcatCategoryResponseModel, operator.toLowerCase());

                String actual_circle = dcatService.getActualCircleName(dcatCategoryResponseModel, circle.toLowerCase());

                if (actual_operator != null) {

                    operator = actual_operator;

                }

                if (actual_circle != null) {

                    circle = actual_circle;

                }

            }
            rpsGetPlansResponse = hitSearchAPI(categoryId, version, channel, operator, amount, circle, productId, rechargeNumber, service);
            if (Objects.nonNull(rpsGetPlansResponse.getProductList())) {
                dcatGetPlanResponse = modifyRPSResponse(rpsGetPlansResponse);
                // String data = mapper.writeValueAsString(dcatGetPlanResponse);
                //commonCacheService.setCache(key, data, Constants.CommonConstants.DCAT_CACHE_TTL);
            }
        } catch (Exception e) {
            logger.error("[RPSService.getPlanDetails] getPlanDetails1 Exception:", e);
        }

//        } else {
//            // Return from cache
//            logger.info("[DCATService.getPlanDetails] getPlanDetails, serving from cache: " + key);
//            try {
//                dcatGetPlanResponse = mapper.readValue(listCommonCache.get(0).getCacheValue(),
//                        DCATGetPlanResponse.class);
//            } catch (IOException e) {
//                logger.error("[RPSService.getPlanDetails] getPlanDetails2 Exception:", e);
//            }
//        }
        return dcatGetPlanResponse;

    }

    private DCATGetPlanResponse modifyRPSResponse(RpsGetPlansResponse rpsGetPlansResponse) {
        DCATGetPlanResponse dcatGetPlanResponse = new DCATGetPlanResponse();
        try {
            RpsApiResponse rpsApiResponse = rpsGetPlansResponse.getProductList().get(0);
            List<String> addOnBenefits = new ArrayList<>();
            if (Objects.nonNull(rpsApiResponse.getAddon_benefit())) {
                addOnBenefits = rpsApiResponse.getAddon_benefit();
            }
            if (addOnBenefits != null) {
                for (int i = 0; i < addOnBenefits.size(); i++) {
                    if (addOnBenefits.get(i) != null) {
                        switch (i) {
                            case 0:
                                dcatGetPlanResponse.setAddon_benefit1(addOnBenefits.get(i));
                                break;
                            case 1:
                                dcatGetPlanResponse.setAddon_benefit2(addOnBenefits.get(i));
                                break;
                            case 2:
                                dcatGetPlanResponse.setAddon_benefit3(addOnBenefits.get(i));
                                break;
                            case 3:
                                dcatGetPlanResponse.setAddon_benefit4(addOnBenefits.get(i));
                                break;
                            default:
                        }
                    }
                }
            }
            dcatGetPlanResponse.setAddon_benefit(rpsApiResponse.getAddon_benefit());
            dcatGetPlanResponse.setDescription(rpsApiResponse.getDescription());
            dcatGetPlanResponse.setSms(rpsApiResponse.getSms());
            dcatGetPlanResponse.setTalktime(rpsApiResponse.getTalktime());
            dcatGetPlanResponse.setValidity(rpsApiResponse.getValidity());
            dcatGetPlanResponse.setData(rpsApiResponse.getData());
            dcatGetPlanResponse.setPrice(rpsApiResponse.getPrice());
            if (Objects.nonNull(rpsApiResponse.getPlan_id())) {
                dcatGetPlanResponse.setPlan_id(rpsApiResponse.getPlan_id());
            }
            if (Objects.nonNull(rpsApiResponse.getPlan_bucket())) {
                dcatGetPlanResponse.setPlan_bucket(rpsApiResponse.getPlan_bucket());
            }

        } catch (Exception e) {
            logger.error("[RPSService.modifyRPSResponse]  Exception:", e);
        }
        return dcatGetPlanResponse;
    }

    private RPSApiRequest rpsRequestBody(String operator, String circle, String amount) {
        RPSApiRequest rpsApiRequest = new RPSApiRequest();
        List<RpsFiltersRequest> rpsFiltersRequest = new ArrayList<>();
        List<RpsFiltersRequest> searchFilters = new ArrayList<>();
        RpsFiltersRequest request = new RpsFiltersRequest();
        RpsFiltersRequest request1 = new RpsFiltersRequest();
        RpsFiltersRequest request2 = new RpsFiltersRequest();
        RpsFiltersRequest request3 = new RpsFiltersRequest();
        request.setKey("operator");
        request.setValue(operator);
        rpsFiltersRequest.add(request);

        if(StringUtils.isNotBlank(circle)){
            request1.setKey("circle");
            request1.setValue(circle);
            rpsFiltersRequest.add(request1);
        }

        request2.setKey("price");
        request2.setValue(amount);
        rpsFiltersRequest.add(request2);
        rpsApiRequest.setFilters(rpsFiltersRequest);
        request3.setKey("price");
        request3.setValue(amount);
        searchFilters.add(request3);
        rpsApiRequest.setSearchFilters(searchFilters);
        return rpsApiRequest;
    }

    private void pushToDD(String metricName, String state) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.STATE + ":" + state;
        monitoringClient.incrementCounter(metricName, tags);
    }
}
