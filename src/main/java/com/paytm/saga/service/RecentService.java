package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.PrometheusConstants;
import com.paytm.saga.common.exception.*;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.CustomerBillDao;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.dto.cdc.LongEntity;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.dto.cdc.StringEntity;
import com.paytm.saga.enums.RENTCONSENT;
import com.paytm.saga.model.*;
import com.paytm.saga.model.primaryKeys.CustomerBillPrimaryKey;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.cassandra.core.CassandraTemplate;
import org.springframework.lang.NonNull;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;

import java.util.*;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.CommonConstants.*;
import static com.paytm.saga.common.constant.Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET;
import static com.paytm.saga.common.constant.Constants.DATA_EXHAUST.EXHAUSTED_DATE;
import static com.paytm.saga.common.constant.Constants.EVENTSOURCE_SMS;
import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.*;
import static com.paytm.saga.common.constant.Constants.IS_PAYTM_VPA;
import static com.paytm.saga.common.constant.Constants.billerAccountListenerConstants.*;
import static com.paytm.saga.common.constant.DateFormats.YYYY_MM_DD_T_HH_MM__SS_SSSZ;

@Service
public class RecentService {
	private final CustomLogger logger = CustomLogManager.getLogger(RecentsService.class);


	private RecentsRepositoryWrapperService recentsRepository;

	@Autowired private SmartRecentsRepository smartRecentsRepository;


	@Autowired
	private SmartRecentsService smartRecentsService;
	@Autowired
	private MappingService mappingService;

	@Autowired
	private CustomerBillDao customerBillDao;

	@Autowired
	private ServiceConfig serviceConfig;

	@Autowired
	private MetricsHelper metricsHelper;

	@Autowired
	private RecentDataToKafkaService recentDataToKafkaService;

	@Autowired private KafkaProducerService kafkaProducerService;

	@Autowired
	private RecentsService recentsService;

	@Autowired
	@Qualifier("keyspaceRecentCassandraTemplate")
	private CassandraTemplate template;

	@Autowired
	public RecentService(@NonNull RecentsRepositoryWrapperService recentsRepository, @NonNull ServiceConfig serviceConfig) {
		this.recentsRepository = recentsRepository;
		this.serviceConfig = serviceConfig;
	}

	@Retryable(value = { OMSListenerException.class }, maxAttemptsExpression = "${db.writes.max.attempts}",backoff = @Backoff(delay = 50))
	public void updateRecentTxnDetails(String mcn, Long customerId, Long orderId, Double txn_amount, String service,String operator, String nickName, Date transactionTime, String planBucket, String cin, String par, String in_response_code, String tin, Boolean isTokenizedTransaction, Long productId, Date transactionUpdateTime, String rechargeNumber, String rechargeNumber2, String rechargeNumber3, String rechargeNumber4, String rechargeNumber5, String rechargeNumber6, String rechargeNumber7, String rechargeNumber8, String circle, String paytype, String consumerName, String cylinderAgencyName, String channel,boolean isNickNameUpdated, Integer notPaidOnPaytm,String rawMessage,String lastFailureTxn,String lastPendingTxn,Boolean isTransaction,String status,Boolean is_new_biller,String pgRespCode, String isRetryExhausted) throws OMSListenerException {
		logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateRecentTxnDetails] Order_id : {} customerId {} recharge {} service {} operator {}  planBucket {} nickName {} txn_amount {} mcn {}",orderId,customerId,rechargeNumber,service,operator,planBucket,nickName,txn_amount,mcn);
		try{
			List<Recents> recentsList = recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator);
			boolean recordFound=false;
			boolean isUpdated = false;
			if(Objects.nonNull(recentsList)){
				for(Recents recentsItem:recentsList){
					boolean isValidationRecord = false;
					boolean matchedRecord=false;
					recentsItem.setConsumerName(consumerName);
					if(isNickNameUpdated)
						recentsItem.setNickName(nickName);
					recentsItem.setCylinderAgencyName(cylinderAgencyName);
					setPGFailureFieldsFromOMS(recentsItem, pgRespCode, isRetryExhausted);
					Date oldUpdatedAt=recentsItem.getUpdatedAt();
					if(!Constants.EVENTSOURCE_SMS.equalsIgnoreCase(recentsItem.getEventSource())
							&& Constants.CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(recentsItem.getPayType())
							&& recentsItem.getOrderId()==null
							&& recentsItem.getLastFailureTxn() ==null
							&& recentsItem.getLastPendingTxn() ==null){
						recentsItem.setDueDate(null);
						recentsItem.setDueAmount(0.0);
						isValidationRecord = true;
					}
					boolean partialExtra=this.isPartialDataExists(recentsItem);
					if(Arrays.asList(OMS_SUCCESS_STATE).contains(OMSToRechargeStatus.getRechargeStatusByOMSStatus(status,in_response_code,null))
							&& partialExtra){
						recentsItem.setDueDate(null);
						recentsItem.setDueAmount(0.0);
						isValidationRecord=this.removePartialDetailsFromRecentObject(recentsItem);
					}

					if(recentsItem.getKey().getPlanBucket().equalsIgnoreCase(planBucket) && recentsItem.getTxnUpdatedAt() !=null){
						Date txn_update_time = recentsItem.getTxnUpdatedAt();
						if (txn_update_time != null && DateUtil.compareDate(transactionUpdateTime, txn_update_time) < 0) {
							logger.info(ServiceWrapperUtil.findServiceWrapper(service),"[updateRecentTxnDetails] repeated transaction message for orderId = {} ",orderId);
							return;
						}
					}

					if(recentsItem.getKey().getPlanBucket().equalsIgnoreCase(planBucket)){
						recordFound=true;
						matchedRecord=true;
						isValidationRecord = false;
						if(Arrays.asList(OMS_SUCCESS_STATE).contains(OMSToRechargeStatus.getRechargeStatusByOMSStatus(status,in_response_code,null))) {
							recentsItem = this.buidRecents(recentsItem, mcn, customerId, orderId, txn_amount, service, operator, nickName, transactionTime, planBucket, cin, par, in_response_code, tin, isTokenizedTransaction, productId, transactionUpdateTime, rechargeNumber, rechargeNumber2, rechargeNumber3, rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7, rechargeNumber8, circle, paytype, consumerName, cylinderAgencyName, channel, isNickNameUpdated, notPaidOnPaytm, lastFailureTxn, lastPendingTxn, isTransaction,is_new_biller);
							updateTxnTimes(recentsItem);
						}
					}
					//As discussed internally we should not delete data in OMS flow, and insert logic seems duplicate so removing below code

					/*else if ((Constants.PREPAID_PAYTYPE.equalsIgnoreCase(paytype) && recentsItem.getPayType().equalsIgnoreCase(Constants.PREPAID_PAYTYPE)) && !recentsItem.getKey().getPlanBucket().equalsIgnoreCase(planBucket)) {
						if(isValidationRecord(recentsItem)){
							recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(recentsItem.getKey().getCustomerId(),recentsItem.getKey().getService(),recentsItem.getKey().getRechargeNumber()
									,recentsItem.getKey().getOperator(),recentsItem.getKey().getPlanBucket());
							logger.info("record with no txn details with differant plan bucket is deleted  :: customerId = {},service = {} ,recharge_number = {},operator = {},plan_bucket = {}",customerId,
									service,rechargeNumber,operator,recentsItem.getKey().getPlanBucket());
							recordFound = false;
							if("00".equals(in_response_code)){
								recentsItem=this.buidRecents(recentsItem, mcn, customerId, orderId, txn_amount, service,operator, nickName, transactionTime, planBucket, cin, par,in_response_code, tin, isTokenizedTransaction, productId,transactionUpdateTime, rechargeNumber, rechargeNumber2, rechargeNumber3, rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7, rechargeNumber8, circle, paytype, consumerName, cylinderAgencyName, channel,isNickNameUpdated, notPaidOnPaytm,lastFailureTxn,lastPendingTxn,isTransaction);
							}
						}

					}*/
					if(Objects.isNull(recentsItem.getCreatedAt())){
						recentsItem.setCreatedAt(recentsItem.getTxnTime());
					}
					recentsItem.setUpdatedAt(new Date());

					int ttl = TTLUtils.getTTL(recentsItem.getKey().getService(),recentsItem.getTxnTime(),recentsItem.getCreatedAt());
					if(ttl<1)ttl=1;
					if(matchedRecord || isValidationRecord){
						if(Arrays.asList(OMS_SUCCESS_STATE).contains(OMSToRechargeStatus.getRechargeStatusByOMSStatus(status,in_response_code,null))){
							logger.info(ServiceWrapperUtil.findServiceWrapper(service),"recordFound success state : recent -- : {}",recentsItem);
							recentDataToKafkaService.setRecentConsumerSource(recentsItem, OMS_CONSUMER);
							isUpdated=recentsRepository.updateRecentWhenDataAlreadyExist(recentsItem,oldUpdatedAt,ttl);
						}else{
							if(!isValidationRecord)
								recentsItem=this.buidRecents(recentsItem, recentsItem.getMcn(), customerId, recentsItem.getOrderId(), recentsItem.getTxnAmount(), service,operator, nickName, recentsItem.getTxnTime(), planBucket, recentsItem.getCin(), recentsItem.getPar(),recentsItem.getTxnStatus(), recentsItem.getTin(), recentsItem.getIsTokenizedTransaction(), productId,transactionUpdateTime, rechargeNumber, recentsItem.getRechargeNumber2(),
									recentsItem.getRechargeNumber3(), recentsItem.getRechargeNumber4(), recentsItem.getRechargeNumber5(), recentsItem.getRechargeNumber6(), recentsItem.getRechargeNumber7(), recentsItem.getRechargeNumber8(), recentsItem.getCircle(), paytype, recentsItem.getConsumerName(), recentsItem.getCylinderAgencyName(), recentsItem.getChannelId(),isNickNameUpdated, recentsItem.getNotPaidOnPaytm(),lastFailureTxn,lastPendingTxn,isTransaction,recentsItem.getIsNewBiller());
							recentDataToKafkaService.setRecentConsumerSource(recentsItem, OMS_CONSUMER);
							isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(recentsItem,oldUpdatedAt,ttl);
						}

						if(!isUpdated){
							logger.info(ServiceWrapperUtil.findServiceWrapper(service),"RecentService :: updateRecentTxnDetails error in updating data in updateRecentWhenDataAlreadyExist with recent response for order id {} from DB {} recentsItem {} oldUpdatedAt {} ttl {}",recentsItem.getOrderId(),recentsList,recentsItem,oldUpdatedAt,ttl);
							throw new OMSListenerException("error while updating txn data in recent using function updateRecentWhenDataAlreadyExist for order id "+recentsItem.getOrderId());
						}
					}
					if(isValidationRecord){
						List<Long> customerForCacheClean=new ArrayList<>();
						customerForCacheClean.add(recentsItem.getKey().getCustomerId());
						cleanCache(customerForCacheClean);
					}

				}
			}
			if(!recordFound){
				logger.debug(ServiceWrapperUtil.findServiceWrapper(service), "no record found in recent");
				Recents newRecent = null;
				Integer ttl ;
				if(Arrays.asList(OMS_SUCCESS_STATE).contains(OMSToRechargeStatus.getRechargeStatusByOMSStatus(status,in_response_code,null))){
					newRecent=this.buidRecents(null, mcn, customerId, orderId, txn_amount, service,operator, nickName, transactionTime, planBucket, cin, par,in_response_code, tin, isTokenizedTransaction, productId,transactionUpdateTime, rechargeNumber, rechargeNumber2, rechargeNumber3,rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7,rechargeNumber8, circle, paytype, consumerName, cylinderAgencyName, channel,isNickNameUpdated, notPaidOnPaytm,lastFailureTxn,lastPendingTxn,isTransaction,is_new_biller);
					newRecent.setTxnTimes(new HashSet<>());
					setPGFailureFieldsFromOMS(newRecent, pgRespCode, isRetryExhausted);
					if (transactionTime != null)
						newRecent.getTxnTimes().add(transactionTime);
					ttl = TTLUtils.getTTL(newRecent.getKey().getService(),newRecent.getTxnTime(),newRecent.getCreatedAt());
					if(ttl<1)ttl=1;
					recentDataToKafkaService.setRecentConsumerSource(newRecent, OMS_CONSUMER);
					isUpdated=recentsRepository.updateRecentWhenNoExistingData(newRecent,ttl);
				}else{
					Recents NewRecentNotSuccess=this.buidRecents(null, mcn, customerId, null, null, service,operator, nickName, null, planBucket, cin, par,null, tin, isTokenizedTransaction, productId,transactionUpdateTime, rechargeNumber, rechargeNumber2, rechargeNumber3,rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7,rechargeNumber8, circle, paytype, consumerName, cylinderAgencyName, null,isNickNameUpdated, notPaidOnPaytm,lastFailureTxn,lastPendingTxn,isTransaction,is_new_biller);
					setPGFailureFieldsFromOMS(NewRecentNotSuccess, pgRespCode, isRetryExhausted);
					ttl = TTLUtils.getTTL(NewRecentNotSuccess.getKey().getService(),NewRecentNotSuccess.getTxnTime(),NewRecentNotSuccess.getCreatedAt());
					if(ttl<1)ttl=1;
					recentDataToKafkaService.setRecentConsumerSource(NewRecentNotSuccess, OMS_CONSUMER);
					isUpdated = recentsRepository.updateRecentWhenNoExistingData(NewRecentNotSuccess,ttl);
				}
				if(Arrays.asList(Constants.CommonConstants.VALIDATION_UNSUCCESSFULL_SERVICE).contains(service)){
					SmartRecents smartRecents = new SmartRecents();
					SmartRecentsPrimaryKey smartRecentsPrimaryKey = new SmartRecentsPrimaryKey();
					smartRecentsPrimaryKey.setCustomerId(customerId);
					smartRecentsPrimaryKey.setService(service);
					smartRecentsPrimaryKey.setOperator(operator);
					smartRecents.setKey(smartRecentsPrimaryKey);
					smartRecentsService.delete(smartRecents);
				}

				if(!isUpdated){
					logger.info(ServiceWrapperUtil.findServiceWrapper(service),"RecentService :: updateRecentTxnDetails error in updating data in updateRecentWhenNoExistingData with recent response for order id {} from DB {} recentsItem {} ttl {}",newRecent.getOrderId(),recentsList,newRecent,ttl);
					throw new OMSListenerException("error while updating txn data in recent using function updateRecentWhenNoExistingData for order id "+newRecent.getOrderId());
				}
			}
			cleanFastagLowBalanceData(customerId,service,rechargeNumber,status,in_response_code);
			if(serviceConfig.getSmartRecentsEnabledServices().contains(service) || serviceConfig.getSmartRecentRecoEnabledServices().contains(service)) {
				smartRecentsRepository.deleteRecentByCustomerIdAndServiceAndOperator(customerId, service, operator);
				invalidateCache(customerId);
			}


			if (Arrays.asList(OMS_SUCCESS_STATE).contains(OMSToRechargeStatus.getRechargeStatusByOMSStatus(status, in_response_code, null))
					&& Constants.CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(paytype)
					&& Objects.nonNull(recentsList)) {
				List<Recents> dataExhaustList = recentsList.stream()
						.filter(recent -> {
							RecentsPrimaryKey key = recent.getKey();
							return key != null && key.getPlanBucket() != null && (Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET).equalsIgnoreCase(key.getPlanBucket());
						})
						.collect(Collectors.toList());
				if (!dataExhaustList.isEmpty()) {
					logger.debug(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateRecentTxnDetails] deleteRecentForPrepaidDataExhaust customerId {} service {} rechargeNumber {} operator {}",
							customerId, service, rechargeNumber, operator);
					deleteRecentForPrepaidDataExhaust(dataExhaustList.get(0),customerId, service, rechargeNumber, operator, Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET);
				}
			}
		}catch(OMSListenerException e){
			metricsHelper.recordSuccessRate(OMS_CONSUMER, RECENT_UPDATE_OMS_REATTEMPT);
			logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateRecentTxnDetails] OMSListenerException Order_id : {} with error {}",orderId,e.getMessage());
			OMSListenerException omsListenerException=new OMSListenerException(e);
			omsListenerException.setKafkaPacket(rawMessage);
			throw omsListenerException;
		}catch(Exception e){
			metricsHelper.recordSuccessRate(OMS_CONSUMER, RECENT_UPDATE_OMS_REATTEMPT);
			logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateRecentTxnDetails] Exception Order_id : {} with error {}",orderId,e.getMessage());
			OMSListenerException omsListenerException=new OMSListenerException(e);
			omsListenerException.setKafkaPacket(rawMessage);
			throw omsListenerException;
		}
	}

	private void updateTxnTimes(Recents recentsItem) {
		try {


			if (!isValidToUpdateTxnTimes(recentsItem, PrometheusConstants.METRIC_NAME.TXN_TIME_DB_UPDATE)) {
				return;
			}
			if (!isValidForUpdate(recentsItem)) {
				return;
			}
			Set<Date> txnTimes = recentsItem.getTxnTimes();
			Date currentTxnTime = recentsItem.getTxnTime();

			removeOldTransactions(txnTimes);

			if (canAddNewTransaction(txnTimes, recentsItem)) {
				txnTimes.add(currentTxnTime);
			}

		} catch (Exception e) {
			logError(recentsItem, e);
		}
	}

	public boolean isValidToUpdateTxnTimes(Recents recentsItem, String metricName) {

		if(!serviceConfig.getTxnTimeEnabledCategory().contains(StringUtils.lowerCase(recentsItem.getKey().getService()))) {
			List<String> tags=new ArrayList<>();
			tags.add(PrometheusConstants.TAG_KEY.STATUS +":"+ PrometheusConstants.TAG_VALUE.SKIP);
			tags.add(PrometheusConstants.TAG_KEY.REASON +":"+ PrometheusConstants.TAG_VALUE.CATEGORY_NOT_ENABLED);
			metricsHelper.recordSuccessRateForMultipleTags(metricName, tags);
			logger.debug("[updateTxnTimes] Skipping as category not enabled enabledCategory {} , category {}" , serviceConfig.getTxnTimeEnabledCategory(), recentsItem.getKey().getService());
			return false;
		}
		if (recentsItem.getAutomaticStatus() != null && recentsItem.getAutomaticStatus() == 1) {
			List<String> tags=new ArrayList<>();
			tags.add(PrometheusConstants.TAG_KEY.STATUS +":"+ PrometheusConstants.TAG_VALUE.SKIP);
			tags.add(PrometheusConstants.TAG_KEY.REASON +":"+ PrometheusConstants.TAG_VALUE.AUTOMATIC_ENABLED);
			metricsHelper.recordSuccessRateForMultipleTags(metricName, tags);
			logger.debug("[updateTxnTimes] Skipping as automatic status is 1 for customerId: {}, service: {}, rechargeNumber: {}", recentsItem.getKey().getCustomerId(), recentsItem.getKey().getService(), recentsItem.getKey().getRechargeNumber());
			return false;
		}
		return true;
	}
	private boolean isValidForUpdate(Recents recentsItem) {

		if (recentsItem.getTxnTimes() == null) {
			logger.debug("[updateTxnTimes] Skipping as txnTimes is null");
			return false;
		}

		return true;
	}

	private void removeOldTransactions(Set<Date> txnTimes) {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.MONTH, -serviceConfig.getTxnTimesMonthsThreshold());
		Date thresholdDate = calendar.getTime();

		txnTimes.removeIf(date -> date == null || date.before(thresholdDate));
	}

	private boolean canAddNewTransaction(Set<Date> txnTimes, Recents recentsItem) {
		if (txnTimes.size() >= serviceConfig.getTxnTimesMaxSize()) {
			logger.debug("[updateTxnTimes] TxnTimes size threshold ({}) reached for customerId: {}",
					serviceConfig.getTxnTimesMaxSize(),
					recentsItem.getKey().getCustomerId());
			List<String> tags=new ArrayList<>();
			tags.add(PrometheusConstants.TAG_KEY.STATUS +":"+ PrometheusConstants.TAG_VALUE.SKIP);
			tags.add(PrometheusConstants.TAG_KEY.REASON +":"+ PrometheusConstants.TAG_VALUE.TXN_TIMES_THRESHOLD_REACHED);
			metricsHelper.recordSuccessRateForMultipleTags(PrometheusConstants.METRIC_NAME.TXN_TIME_DB_UPDATE, tags);

			return false;
		}
		return true;
	}

	private void logError(Recents recentsItem, Exception e) {
		String customerId = recentsItem != null && recentsItem.getKey() != null
				? recentsItem.getKey().getCustomerId().toString()
				: "unknown";

		logger.error("[updateTxnTimes] Error updating txn times for customerId: {}, error: {}",
				customerId,
				e.getMessage(),
				e);
	}

	private boolean isPartialDataExists(Recents recentsItem){
		boolean partialExtra=false;
		try{
			Map extras = RecentUtils.getExtras(recentsItem);
			if(extras!= null){
				String partialBillState = RecentUtils.getPartialBill(extras);
				if(!StringUtils.isEmpty(partialBillState)){
					partialExtra=true;
				}
			}
		}catch (Exception e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(recentsItem), "isPartialDataExists ",e);
		}

		return partialExtra;
	}

	private boolean removePartialDetailsFromRecentObject(Recents recentsItem){
		try{
			String extra=recentsItem.getExtra();
			if(!StringUtils.isEmpty(extra)) {
				JSONObject jsonObject=new JSONObject(extra);
				jsonObject.remove(Constants.PARTIAL_BILL_EVENT_KEY);
				recentsItem.setExtra(jsonObject.toString());
				return true;
			}
		}catch (Exception e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(recentsItem), "removePartialDetailsFromRecentObject ",e);
		}
		return false;
	}
		private void cleanFastagLowBalanceData(Long customerId,String service,String rechargeNumber,String status,String in_response_code){
		if(service.equals("fastag recharge") && "SUCCESS".equalsIgnoreCase(OMSToRechargeStatus.getRechargeStatusByOMSStatus(status,in_response_code,null))){
				rechargeNumber=rechargeNumber.toUpperCase();
				List<Recents> recentsList = recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, "LOW_BALANCE");
				if(Objects.nonNull(recentsList)){
					for(Recents recents:recentsList){
						logger.info("cleanFastagLowBalanceData delete fastag low balance record recents {}",recents);
						recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(recents, customerId,recents.getKey().getService(),rechargeNumber
								,recents.getKey().getOperator(),recents.getKey().getPlanBucket());

					}
				}
				recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(new Recents(),customerId,service,RecentUtils.getFastagDefaultRechargeNumber(customerId)
						,"LOW_BALANCE","");
			invalidateCache(customerId);
			}
	}

	private void invalidateCache(Long customerId){
		try {
			kafkaProducerService.sendMessage(Long.toString(customerId));
		} catch (Exception e) {
			logger.error("[RecentService.invalidateCache] kafka cache clean event publish error for data {}, Exception {}",
					Long.toString(customerId), e);
		}
	}

	@Recover
	public void recoverFromOMSListenerException(OMSListenerException ex) {
		metricsHelper.recordSuccessRate(OMS_CONSUMER, RECENT_UPDATE_OMS_FAIL);
		logger.error("[RecentService.recoverFromOMSListenerException] All retry attempts exhausted for updateRecentTxnDetails() after all attempts. message {} error message {} kafka packet {}",ex.getMessage(),ex.getErrorMessages(),ex.getKafkaPacket());
	}

	private Recents buidRecents(Recents recents,String mcn, Long customerId, Long orderId, Double txn_amount, String service,
								String operator, String nickName, Date transactionTime, String planBucket, String cin, String par,
								String in_response_code, String tin, Boolean isTokenizedTransaction, Long productId,
								Date transactionUpdateTime, String rechargeNumber, String rechargeNumber2, String rechargeNumber3,
								String rechargeNumber4, String rechargeNumber5, String rechargeNumber6, String rechargeNumber7,
								String rechargeNumber8, String circle, String paytype, String consumerName, String cylinderAgencyName, String channel,boolean isNickNameUpdated, Integer notPaidOnPaytm,String lastFailureTxn,String lastPendingTxn,Boolean isTransaction,Boolean is_new_biller) throws JsonProcessingException {
		if(Objects.isNull(recents)){
			recents=new Recents();
			RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
			recentsPrimaryKey.setCustomerId(customerId);
			recentsPrimaryKey.setOperator(operator);
			recentsPrimaryKey.setRechargeNumber(rechargeNumber);
			recentsPrimaryKey.setService(service);
			recentsPrimaryKey.setPlanBucket(planBucket);
			recents.setKey(recentsPrimaryKey);
			recents.setCreatedAt(new Date());
		}
		if(isNickNameUpdated){
			recents.setNickName(nickName);
		}
		recents.setMcn(mcn);
		recents.setTxnAmount(txn_amount);
		recents.setNotPaidOnPaytm(notPaidOnPaytm);
		recents.setOrderId(orderId);
		recents.setTxnTime(transactionTime);
		recents.setCin(cin);
		recents.setPar(par);
		recents.setTxnStatus(in_response_code);
		recents.setTin(tin);
		recents.setIsTokenizedTransaction(isTokenizedTransaction);
		recents.setProductId(productId);
		recents.setUpdatedAt(transactionUpdateTime);
		recents.setRechargeNumber2(rechargeNumber2);
		recents.setRechargeNumber3(rechargeNumber3);
		recents.setRechargeNumber4(rechargeNumber4);
		recents.setRechargeNumber5(rechargeNumber5);
		recents.setRechargeNumber6(rechargeNumber6);
		recents.setRechargeNumber7(rechargeNumber7);
		recents.setRechargeNumber8(rechargeNumber8);
		recents.setCircle(circle);
		recents.setPayType(paytype);
		recents.setConsumerName(consumerName);
		recents.setCylinderAgencyName(cylinderAgencyName);
		recents.setChannelId(channel);
		recents.setNotPaidOnPaytm(0);
		recents.setLastFailureTxn(lastFailureTxn);
		recents.setLastPendingTxn(lastPendingTxn);
		recents.setIsTransaction(isTransaction);
		recents.setEventSource(null);
		recents.setIsNewBiller(is_new_biller);
		recents.setTxnUpdatedAt(transactionUpdateTime);
		return recents;
	}

    @Retryable(value = {ReminderListenerException.class},
			maxAttemptsExpression = "${db.writes.max.attempts}")
    public void updateRecentBillFromReminderListener(Long customerId, String service, String operator, String planBucket, String cin, String par, String tin, Boolean isTokenizedTransaction, Date billDate, Date dueDate, Double dueAmount, Double minDueAmount, Double originalDueAmount, Double originalMinDueAmount,
                                                     Long productId, Integer automaticStatus, Date billUpdateTime, Boolean isMarkAsPaid, Date markAsPaidTime, Double markAsPaidAmount, Date updatedAt, String rechargeNumber, String rechargeNumber2, String rechargeNumber3,
                                                     String rechargeNumber4, String rechargeNumber5, String rechargeNumber6, String rechargeNumber7,
													 String rechargeNumber8, String circle, String paytype, String mcn, Integer notificationStatus, Integer notPaidOnPaytm, Date txnTime, Double txnAmount, Date newBillUpdatedAt, Boolean isSavedCard, Boolean isValidation, Boolean isTransaction, String eventSource, ReminderResponseModel reminderResponseModel,Boolean is_new_biller, Date earlyPaymentDate, Double earlyPaymentAmount,String pgCardId, String reconId, Boolean nextBillFetchDateFlag, Integer reminderStatus, Date oldBillFetchDate, String cardVariant, String cardSkin, String extra, Date remindMeLaterDate, Double currentOutstandingAmount, Date consentValidTill) throws ReminderListenerException{
		Boolean isUpdated = null;
		Boolean isCCInsertEvent = null;
		try {
			List<Recents> recents1 = null;
			if(com.paytm.saga.enums.Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(service) && reminderResponseModel.getOperationType().equalsIgnoreCase(Constants.OP_INSERT)){
                recents1 = recentsRepository.findByCustomerIdAndService(customerId,service);
				isCCInsertEvent = true;
			}
			else
				recents1 = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, service, rechargeNumber, operator, planBucket);
			Date finalCreatedTime = null;
			Integer ttl;
			Date finalTxnTime = null;
			Recents finalRecent = new Recents();
			finalRecent.setKey(new RecentsPrimaryKey());
			if (!CollectionUtils.isEmpty(recents1)) {
				if(Boolean.TRUE.equals(isCCInsertEvent)){
                      isUpdated = insertInCCFromReminder(recents1,customerId, service, operator, planBucket, cin,
							  par, tin, isTokenizedTransaction, billDate, dueDate, dueAmount,
							  minDueAmount, originalDueAmount, originalMinDueAmount, productId,
							  automaticStatus, billUpdateTime, isMarkAsPaid, markAsPaidTime, markAsPaidAmount,
							  updatedAt, rechargeNumber, rechargeNumber2, rechargeNumber3,
							  rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7,
							  rechargeNumber8, circle, paytype, mcn, notificationStatus, notPaidOnPaytm,
						      txnTime, txnAmount, newBillUpdatedAt, isSavedCard, isValidation, isTransaction, eventSource,isUpdated,is_new_biller,pgCardId, reconId, nextBillFetchDateFlag, reminderStatus, oldBillFetchDate,  cardVariant, cardSkin, extra, remindMeLaterDate, currentOutstandingAmount, consentValidTill);
				}
				else {
					Recents existingRecord = recents1.get(0);
					Date existingUpdatedAt = existingRecord.getUpdatedAt();
					finalTxnTime = existingRecord.getTxnTime();
					finalCreatedTime = Objects.nonNull(existingRecord.getCreatedAt()) ? existingRecord.getCreatedAt() : ((Objects.nonNull(finalTxnTime) ? finalTxnTime : new Date()));
					boolean isNewBillIdentified=isNewBillIdentified(existingRecord.getDueDate(), dueDate,existingRecord);
					prepareFinalRecents(existingRecord, customerId, service, operator, planBucket, cin,
							par, tin, isTokenizedTransaction, billDate, dueDate, dueAmount,
							minDueAmount, originalDueAmount, originalMinDueAmount, productId,
							automaticStatus, billUpdateTime, isMarkAsPaid, markAsPaidTime, markAsPaidAmount,
							updatedAt, rechargeNumber, rechargeNumber2, rechargeNumber3,
							rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7,
							rechargeNumber8, circle, paytype, mcn, notificationStatus, notPaidOnPaytm,
						    txnTime, txnAmount, finalCreatedTime, newBillUpdatedAt, isSavedCard, isValidation, isTransaction, eventSource,is_new_biller,pgCardId, earlyPaymentDate, earlyPaymentAmount, reconId, nextBillFetchDateFlag, reminderStatus, oldBillFetchDate,  cardVariant, cardSkin, extra,isNewBillIdentified, remindMeLaterDate, currentOutstandingAmount, consentValidTill);
					if (Objects.nonNull(notPaidOnPaytm) && notPaidOnPaytm == 1) {
						ttl = TTLUtils.getTTL(service, txnTime, finalCreatedTime);
					} else {
						ttl = TTLUtils.getTTL(service, finalTxnTime, finalCreatedTime);
					}
					if (ttl < 1) ttl = 1;
					recentDataToKafkaService.setRecentConsumerSource(existingRecord, REMINDER_CONSUMER);
					isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord, existingUpdatedAt, ttl);
				}
			} else {
				if(reminderResponseModel.getOperationType().equalsIgnoreCase("delete")){
					logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateBillFromReminderListener] Update bill details in recent from reminder consumer failed as operation type is Delete for customerId {} and rechargeNumber {}",customerId,rechargeNumber);
					return;
				}
				isUpdated = insertInRecentsFromReminder(customerId, service, operator, planBucket, cin,
						par, tin, isTokenizedTransaction, billDate, dueDate, dueAmount,
						minDueAmount, originalDueAmount, originalMinDueAmount, productId,
						automaticStatus, billUpdateTime, isMarkAsPaid, markAsPaidTime, markAsPaidAmount,
						updatedAt, rechargeNumber, rechargeNumber2, rechargeNumber3,
						rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7,
						rechargeNumber8, circle, paytype, mcn, notificationStatus, notPaidOnPaytm,
					    txnTime, txnAmount, newBillUpdatedAt, isSavedCard, isValidation, isTransaction, eventSource,is_new_biller,pgCardId, earlyPaymentDate, earlyPaymentAmount,reconId, nextBillFetchDateFlag, reminderStatus, oldBillFetchDate,  cardVariant, cardSkin, extra, remindMeLaterDate, currentOutstandingAmount, consentValidTill);
			}
			if (Boolean.FALSE.equals(isUpdated)) {
				throw new ReminderListenerException("[RecentService.updateReminderDataInRecents] :: Update query returning false for customer id :"+customerId+" recharge_number :"+rechargeNumber);
			}
			logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateReminderDataInRecents] :: Update bill info in recents successfull for for customer_id : {} and rechargeNumber : {}", customerId, rechargeNumber);
		} catch (Exception e) {
			metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECENT_UPDATE_REMINDER_REATTEMPT);
			logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateReminderDataInRecents] :: Retrying as update in recents failed for customer_id : {} and rechargeNumber : {}", customerId, rechargeNumber);
			throw new ReminderListenerException(e);
		}
    }

	private void checkAndDeleteUpiCreditCard(String service, String planBucket, Long customerId, List<Recents> recents1, String mcn) {
		Recents upiCardRecent = recents1.stream()
				.filter(r -> filterUpiCard(r, mcn))
				.findFirst().orElse(null);
		if (Objects.nonNull(upiCardRecent)) {
			logger.info("[RecentService.checkAndDeleteUpiCreditCard] :: Deleting UPI Credit Card from recents for customer_id : {} and rechargeNumber : {}",
					customerId, mcn);
			metricsHelper.recordSuccessRate(REMINDER_CONSUMER, DELETE_EVENT_UPI_CC);
			recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(upiCardRecent, customerId,
					service, upiCardRecent.getKey().getRechargeNumber(), upiCardRecent.getKey().getOperator(), planBucket);
		}
	}
	private boolean filterUpiCard(Recents recents, String mcn) {
		return StringUtils.equals(recents.getEventSource(), Constants.EVENT_SOURCE.UPI_CREDIT_CARD)
				&& StringUtils.equals(CreditCardUtils.getLast2Digits(recents.getMcn()), CreditCardUtils.getLast2Digits(mcn));
	}

	public Boolean insertInCCFromReminder(List<Recents> recents, Long customerId, String service, String operator, String planBucket, String cin, String par, String tin, Boolean isTokenizedTransaction, Date billDate, Date dueDate, Double dueAmount, Double minDueAmount, Double originalDueAmount, Double originalMinDueAmount,
										  Long productId, Integer automaticStatus, Date billUpdateTime, Boolean isMarkAsPaid, Date markAsPaidTime, Double markAsPaidAmount, Date updatedAt, String rechargeNumber, String rechargeNumber2, String rechargeNumber3,
										  String rechargeNumber4, String rechargeNumber5, String rechargeNumber6, String rechargeNumber7,
										  String rechargeNumber8, String circle, String paytype, String mcn, Integer notificationStatus, Integer notPaidOnPaytm, Date txnTime, Double txnAmount, Date newBillUpdatedAt, Boolean isSavedCard, Boolean isValidation, Boolean isTransaction, String eventSource, Boolean isUpdated,Boolean is_new_biller, String pgCardId, String reconId, Boolean nextBillFetchDateFlag, Integer reminderStatus, Date oldBillFetchDate, String cardVariant, String cardSkin, String extra, Date remindMeLaterDate, Double currentOutstandingAmount, Date consentValidTill) {
		boolean isExistingCC = false;
		boolean isSameRefId = false;
		Date finalCreatedTime;
		Date finalTxnTime = null;
		Integer ttl;
		ProductMin newPid = CVRProductCache.getInstance().getProductDetails(productId);
		try {
			for (Recents recent : recents) {
				if (Boolean.FALSE.equals(isMarkAsPaid)) {
					recent.setIsMarkAsPaid(Boolean.FALSE);
					recent.setMarkAsPaidAmount(markAsPaidAmount);
					recent.setMarkAsPaidTime(markAsPaidTime);
				}

				if (rechargeNumber.equalsIgnoreCase(recent.getKey().getRechargeNumber()) && operator.equalsIgnoreCase(recent.getKey().getOperator()) && planBucket.equalsIgnoreCase(recent.getKey().getPlanBucket())) {

					logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService:insertInCCFromReminder] :: Credit card with same ref id and cust id found already in db for insert event, hence skipping for customerId {} and rechargeNumber {}", customerId, rechargeNumber);
					metricsHelper.recordSuccessRate(REMINDER_CONSUMER, INSERT_EVENT_CC_FOR_EXISTING_CARD);

					isExistingCC = true;
					finalTxnTime = recent.getTxnTime();
					finalCreatedTime = Objects.nonNull(recent.getCreatedAt()) ? recent.getCreatedAt() : ((Objects.nonNull(finalTxnTime) ? finalTxnTime : new Date()));
					recent.getKey().setRechargeNumber(rechargeNumber);
					recent.setUpdatedAt(updatedAt);
					recent.setCin(cin);
					recent.setPar(par);
					recent.setTin(tin);
					recent.setRechargeNumber2(rechargeNumber2);
					recent.setRechargeNumber3(rechargeNumber3);
					recent.setRechargeNumber4(rechargeNumber4);
					recent.setRechargeNumber5(rechargeNumber5);
					recent.setRechargeNumber6(rechargeNumber6);
					recent.setRechargeNumber7(rechargeNumber7);
					recent.setRechargeNumber8(rechargeNumber8);
					recent.setEventSource(eventSource);
					recent.setDueAmount(dueAmount);
					recent.setIsNewBillIdentified(newBillIdentifiedUpdateForAllowedUser(isNewBillIdentified(recent.getDueDate(), dueDate,recent), recent.getKey().getCustomerId()));
					recent.setDueDate(dueDate);
					recent.setOriginalDueAmount(originalDueAmount);
					recent.setOriginalMinDueAmount(originalMinDueAmount);
					recent.setMinDueAmount(minDueAmount);
					recent.setCurrentOutstandingAmount(currentOutstandingAmount);
					recent.setPgCardId(pgCardId);
					recent.setNextBillFetchDateFlag(nextBillFetchDateFlag);
					recent.setCardVariant(cardVariant);
					recent.setCardSkin(cardSkin);
					recent.setRemindLaterDate(remindMeLaterDate);
					recent.setConsentValidTill(consentValidTill);
					recent.setExtra(extra);

					if (Objects.nonNull(notPaidOnPaytm) && notPaidOnPaytm == 1) {
						ttl = TTLUtils.getTTL(service, txnTime, finalCreatedTime);
					} else {
						ttl = TTLUtils.getTTL(service, finalTxnTime, finalCreatedTime);
					}
					if (ttl < 1) ttl = 1;

					recentDataToKafkaService.setRecentConsumerSource(recent, REMINDER_CONSUMER);
					recentsRepository.updateRecentWhenDataAlreadyExistInInsert(recent, ttl);
					logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.insertInCCFromReminder] CC update event with same ref id of same card updating in recents customerId {} and rechargeNumber {}",recent.getKey().getCustomerId(),recent.getKey().getRechargeNumber());
					logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.insertInCCFromReminder] CC update event with same ref id of same card updating in recents customerId {} and rechargeNumber {}",recent.getKey().getCustomerId(),recent.getKey().getRechargeNumber());
					logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.insertInCCFromReminder]  : Recents : {}", recent);

					return true;
				}
			}
			for (Recents recent : recents) {
				ProductMin existingPid = CVRProductCache.getInstance().getProductDetails(ActiveInactivePidMapCache.getInstance().getActivePid(recent.getProductId()));
				if (mcn.equalsIgnoreCase(recent.getMcn()) && newPid.getBankCode().equalsIgnoreCase(existingPid.getBankCode()) && newPid.getCardNetwork().equalsIgnoreCase(existingPid.getCardNetwork())) {
					isExistingCC = true;
					finalTxnTime = recent.getTxnTime();
					finalCreatedTime = Objects.nonNull(recent.getCreatedAt()) ? recent.getCreatedAt() : ((Objects.nonNull(finalTxnTime) ? finalTxnTime : new Date()));
					recent.getKey().setRechargeNumber(rechargeNumber);
					recent.setUpdatedAt(new Date());
					recent.setCin(cin);
					recent.setPar(par);
					recent.setTin(tin);
					recent.setRechargeNumber2(rechargeNumber2);
					recent.setRechargeNumber3(rechargeNumber3);
					recent.setRechargeNumber4(rechargeNumber4);
					recent.setRechargeNumber5(rechargeNumber5);
					recent.setRechargeNumber6(rechargeNumber6);
					recent.setRechargeNumber7(rechargeNumber7);
					recent.setRechargeNumber8(rechargeNumber8);
					recent.setReconId(reconId);
					recent.setEventSource(eventSource);
					recent.setPgCardId(pgCardId);
					recent.setNextBillFetchDateFlag(nextBillFetchDateFlag);
					recent.setCurrentOutstandingAmount(currentOutstandingAmount);
					recent.setCardSkin(cardSkin);
					recent.setCardVariant(cardVariant);
					recent.setConsentValidTill(consentValidTill);
					recent.setExtra(extra);

					if (Objects.nonNull(notPaidOnPaytm) && notPaidOnPaytm == 1) {
						ttl = TTLUtils.getTTL(service, txnTime, finalCreatedTime);
					} else {
						ttl = TTLUtils.getTTL(service, finalTxnTime, finalCreatedTime);
					}
					if (ttl < 1) ttl = 1;

					if(Objects.nonNull(pgCardId)){
						recent.setRechargeNumber4(null);
					}

					logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.insertInCCFromReminder] CC insert event with different ref id of same card updating in recents customerId {} and rechargeNumber {}",recent.getKey().getCustomerId(),recent.getKey().getRechargeNumber());
					recentDataToKafkaService.setRecentConsumerSource(recent, REMINDER_CONSUMER);
					isUpdated = recentsRepository.updateRecentWhenNoExistingData(recent, ttl);
				}
			}
			if (Boolean.FALSE.equals(isExistingCC)) {
				isUpdated = insertInRecentsFromReminder(customerId, service, operator, planBucket, cin,
						par, tin, isTokenizedTransaction, billDate, dueDate, dueAmount,
						minDueAmount, originalDueAmount, originalMinDueAmount, productId,
						automaticStatus, billUpdateTime, isMarkAsPaid, markAsPaidTime, markAsPaidAmount,
						updatedAt, rechargeNumber, rechargeNumber2, rechargeNumber3,
						rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7,
						rechargeNumber8, circle, paytype, mcn, notificationStatus, notPaidOnPaytm,
					    txnTime, txnAmount, newBillUpdatedAt, isSavedCard, isValidation, isTransaction, eventSource,is_new_biller, pgCardId, null, null, reconId, nextBillFetchDateFlag, reminderStatus, oldBillFetchDate, cardVariant, cardSkin, extra, remindMeLaterDate, currentOutstandingAmount, consentValidTill);
				if (Boolean.TRUE.equals(isUpdated)){
					checkAndDeleteUpiCreditCard(service, planBucket, customerId, recents, mcn);
				}
			}
		} catch (Exception e) {
			logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.insertInCCFromReminder] Error in cc insert - ", e);
		}
		return isUpdated;
	}

	public Boolean insertInRecentsFromReminder(Long customerId, String service, String operator, String planBucket, String cin, String par, String tin, Boolean isTokenizedTransaction, Date billDate, Date dueDate, Double dueAmount, Double minDueAmount, Double originalDueAmount, Double originalMinDueAmount,
											   Long productId, Integer automaticStatus, Date billUpdateTime, Boolean isMarkAsPaid, Date markAsPaidTime, Double markAsPaidAmount, Date updatedAt, String rechargeNumber, String rechargeNumber2, String rechargeNumber3,
											   String rechargeNumber4, String rechargeNumber5, String rechargeNumber6, String rechargeNumber7,
											   String rechargeNumber8, String circle, String paytype, String mcn, Integer notificationStatus, Integer notPaidOnPaytm, Date txnTime, Double txnAmount, Date newBillUpdatedAt, Boolean isSavedCard, Boolean isValidation, Boolean isTransaction, String eventSource,Boolean is_new_biller,String pgCardId, Date earlyPaymentDate, Double earlyPaymentAmount, String reconId, Boolean nextBillFetchDateFlag, Integer reminderStatus, Date oldBillFetchDate, String cardVariant, String cardSkin, String extra, Date remindMeLaterDate, Double currentOutstandingAmount, Date consentValidTill) throws RecentDataToKafkaException {
		Date finalCreatedTime = new Date();
		Date finalTxnTime = null;
		Boolean isUpdated;
		Integer ttl;
		Recents finalRecent = new Recents();
		prepareFinalRecents(finalRecent, customerId, service, operator, planBucket, cin,
				par, tin, isTokenizedTransaction, billDate, dueDate, dueAmount,
				minDueAmount, originalDueAmount, originalMinDueAmount, productId,
				automaticStatus, billUpdateTime, isMarkAsPaid, markAsPaidTime, markAsPaidAmount,
				updatedAt, rechargeNumber, rechargeNumber2, rechargeNumber3,
				rechargeNumber4, rechargeNumber5, rechargeNumber6, rechargeNumber7,
				rechargeNumber8, circle, paytype, mcn, notificationStatus, notPaidOnPaytm,
			    txnTime, txnAmount, finalCreatedTime, newBillUpdatedAt, isSavedCard, isValidation, isTransaction, eventSource,is_new_biller,pgCardId, earlyPaymentDate, earlyPaymentAmount, reconId, nextBillFetchDateFlag, reminderStatus, oldBillFetchDate, cardVariant, cardSkin, extra,Boolean.TRUE, remindMeLaterDate, currentOutstandingAmount, consentValidTill);
		if (Objects.nonNull(notPaidOnPaytm) && notPaidOnPaytm == 1) {
			ttl = TTLUtils.getTTL(service,txnTime, finalCreatedTime);
		} else {
			ttl = TTLUtils.getTTL(service,finalTxnTime, finalCreatedTime);
		}
		if(ttl<1)ttl=1;
		recentDataToKafkaService.setRecentConsumerSource(finalRecent, REMINDER_CONSUMER);
		isUpdated = recentsRepository.updateRecentWhenNoExistingData(finalRecent, ttl);
		return isUpdated;
	}
	@Recover
	public void recoverFromReminderListenerException(ReminderListenerException ex) {
		metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECENT_UPDATE_REMINDER_FAIL);
		logger.error("[RecentService.recoverFromReminderListenerException] All retry attempts exhausted for updateRecentBillsFromReminderListener after all attempts.",ex);
	}

	@Retryable(value = { FsRechargeListenerException.class }, maxAttemptsExpression = "${db.writes.max.attempts}")
	public void updateRecentData(FsRechargeConsumerModel fsRechargeConsumerModel) throws FsRechargeListenerException {
		try{
			String recentDataString = RecentUtils.getRecentDataStringValue(fsRechargeConsumerModel.getRecentData());
			if (recentDataString != null) {
				RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
				recentsPrimaryKey.setCustomerId(fsRechargeConsumerModel.getCustomerId());
				recentsPrimaryKey.setService(fsRechargeConsumerModel.getService());
				recentsPrimaryKey.setRechargeNumber(fsRechargeConsumerModel.getRecharge_number());
				recentsPrimaryKey.setOperator(fsRechargeConsumerModel.getOperator());
				recentsPrimaryKey.setPlanBucket(fsRechargeConsumerModel.getPlanBucket());
				Recents recents = recentsRepository.findById(recentsPrimaryKey);
				Date existingUpdatedDate=null;
				boolean isRepeatingCustomer=false;
				if(Objects.isNull(recents)) {
					recents=new Recents();
					recents.setKey(recentsPrimaryKey);
				}else{
					existingUpdatedDate=recents.getUpdatedAt();
					isRepeatingCustomer=true;
				}
				if(recents.getCreatedAt()==null){
					recents.setCreatedAt(fsRechargeConsumerModel.getTxnInit());
				}
				recents.setRecentData(recentDataString);
				recents.setTxnTime(fsRechargeConsumerModel.getTxnInit());
				recents.setUpdatedAt(new Date());

				Integer ttl = TTLUtils.getTTL(fsRechargeConsumerModel.getService(),fsRechargeConsumerModel.getTxnInit(),recents.getCreatedAt());
				if(ttl<1)ttl=1;
				boolean isDataUpdated=false;
				recentDataToKafkaService.setRecentConsumerSource(recents,Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME);
				if(isRepeatingCustomer){
					isDataUpdated=recentsRepository.updateRecentWhenDataAlreadyExist(recents,existingUpdatedDate,ttl);
				}else{
					isDataUpdated=recentsRepository.updateRecentWhenNoExistingData(recents,ttl);
				}
				if(!isDataUpdated){
					throw new FsRechargeListenerException("error while updating recentData in recent table for order id "+fsRechargeConsumerModel.getOrderId());
				}
			}
		}catch (Exception e){
			metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.RECENT_DATA_UPDATE_REATTEMPT);
			logger.error("[RecentService.updateRecentData] error in recentData update for Order_id : {} with error {}",fsRechargeConsumerModel.getOrderId(),e);
			throw new FsRechargeListenerException(e);
		}
	}

	@Recover
	public void recoverFromFsRechargeListenerException(FsRechargeListenerException ex) {
		metricsHelper.recordSuccessRate(Constants.FsRechargeConsumerConstants.FS_RECHARGE_METRICS_SERVICE_NAME,Constants.FsRechargeConsumerConstants.RECENT_DATA_UPDATE_OMS_FAIL);
		logger.error("[RecentService.updateRecentData] All retry attempts exhausted for FsRechargeListener after all attempts. {}",ex.getMessage());
	}


    public void reminderCheckForNotPaidOnPaytm(Recents recents, ReminderHistory reminderHistory, ReminderResponseModel reminderResponseModel) {
        Date transactionDate = new Date();
		if(Objects.nonNull(reminderResponseModel.getData().getPaymentDate())) {
			try {
				transactionDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(reminderResponseModel.getData().getPaymentDate());
			} catch (Exception e) {
				logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[RecentService.reminderCheckForNotPaidOnPaytm]transactionDate: Exception for paymentDate {}", reminderResponseModel.getData().getPaymentDate());
			}
			if (reminderHistory.getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE) && reminderHistory.getStatus().equals(14) && Objects.nonNull(reminderHistory.getLastPaidAmount())) {
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[RecentService.reminderCheckForNotPaidOnPaytm] Not paid on paytm updating true for customerid {}, recharge number {}, service {}", reminderHistory.getCustomerId(), reminderHistory.getRechargeNumber(), reminderHistory.getService());
				recents.setNotPaidOnPaytm(1);
				recents.setTxnAmount(reminderHistory.getLastPaidAmount());
				recents.setTxnTime(transactionDate);
			}
		}
    }

	@Retryable(value = {AutomaticListenerException.class},
			maxAttemptsExpression = "${db.writes.max.attempts}")
	public void updateAutomaticInfo(Long customerId, Date automaticDate, String service, String operator, String plan_bucket, Date currentDate, Date updatedDate, String recharge_number, Long productId, Integer subscriberId, boolean shouldUpdateAutomaticDate, Double automaticAmount) throws AutomaticListenerException{
		try{
			Boolean isUpdated;
			List<Recents> recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,recharge_number,operator,plan_bucket);
			Date finalCreatedTime;
			Integer ttl;
			Date finalTxnTime = null;
			Recents finalRecent = new Recents();
			if (!CollectionUtils.isEmpty(recents)) {
				Recents existingRecord = recents.get(0);
				Date existingUpdatedAt = existingRecord.getUpdatedAt();
				finalTxnTime = existingRecord.getTxnTime();
				finalCreatedTime = Objects.nonNull(existingRecord.getCreatedAt()) ? existingRecord.getCreatedAt() : ((Objects.nonNull(finalTxnTime) ? finalTxnTime : new Date()));
				if(shouldUpdateAutomaticDate)
					existingRecord.setAutomaticDate(automaticDate);
				existingRecord.setUpdatedAt(updatedDate);
				existingRecord.setProductId(productId);
				existingRecord.setCreatedAt(finalCreatedTime);
				existingRecord.setAutomaticSubscriptionId(subscriberId);
				existingRecord.setAutomaticAmount(automaticAmount);
				ttl = TTLUtils.getTTL(service,finalTxnTime, finalCreatedTime);
				if(ttl<1)ttl=1;
				recentDataToKafkaService.setRecentConsumerSource(existingRecord, AUTOMATIC_CONSUMER);
				isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord,existingUpdatedAt,ttl);
			} else {
				finalCreatedTime = new Date();
				buildRecentPrimaryKey(finalRecent,customerId,service,recharge_number,operator,plan_bucket);
				if(shouldUpdateAutomaticDate)
					finalRecent.setAutomaticDate(automaticDate);
				finalRecent.setUpdatedAt(updatedDate);
				finalRecent.setProductId(productId);
				finalRecent.setCreatedAt(finalCreatedTime);
				finalRecent.setAutomaticSubscriptionId(subscriberId);
				ttl = TTLUtils.getTTL(service,finalTxnTime, finalCreatedTime);
				if(ttl<1)ttl=1;
				recentDataToKafkaService.setRecentConsumerSource(finalRecent, AUTOMATIC_CONSUMER);
				isUpdated = recentsRepository.updateRecentWhenNoExistingData(finalRecent, ttl);
			}
			if (Boolean.FALSE.equals(isUpdated)){
				throw new AutomaticListenerException("[RecentService.updateAutomaticInfo] : Update query returning false for customerId :"+customerId+"and rechargeNumber :"+recharge_number);
			}
			logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateAutomaticInfo] :: Update automatic date in recents successfull for customer_id : {} and rechargeNumber : {}", customerId, recharge_number);
		}catch (Exception e){
			metricsHelper.recordSuccessRate(AUTOMATIC_CONSUMER, RECENT_UPDATE_AUTOMATIC_REATTEMPT);
			logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateAutomaticInfo] :: Retrying again as automatic Date update in recents failed for customerId {}, rechargeNumber {}",customerId,recharge_number);
			throw new AutomaticListenerException(e);
		}
	}

	@Recover
	public void recoverFromAutomaticListenerException(AutomaticListenerException ex) {
		metricsHelper.recordSuccessRate(AUTOMATIC_CONSUMER, RECENT_UPDATE_AUTOMATIC_FAIL);
		logger.error("[RecentService.recoverFromAutomaticListenerException] All retry attempts exhausted for updateAutomaticInfo() after all attempts.",ex);
	}

	public List<Recents> getRecents(FetchRecentsRequest fetchRecentsRequest, List<Long> customerIds){
		List<Recents> recents=null;
		try{
			recents = recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(customerIds, fetchRecentsRequest.getService(), fetchRecentsRequest.getRechargeNumber(),fetchRecentsRequest.getOperator());
		} catch (Exception e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(fetchRecentsRequest.getService()), "[RecentService.getRecents]: error in getting recents , e={}",e.getMessage());
		}
		return recents;

	}

	@Retryable(value = {ReminderListenerException.class},
			maxAttemptsExpression = "${db.writes.max.attempts}")
	public void updateNotificationStatusInRecents(Integer notificationStatus,String operator,String rechargeNumber,ReminderResponseModel reminderResponseModel) throws ReminderListenerException{
		try {
			Boolean isUpdated;
			Date finalTxnTime = null;
			Date finalCreatedTime = null;
			Integer ttl;
			Recents finalRecent = new Recents();
			List<Recents> recents1 = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(reminderResponseModel.getData().getCustomer_id(), reminderResponseModel.getData().getService(), rechargeNumber, operator, "");
			if (!CollectionUtils.isEmpty(recents1)) {
				Recents existingRecord = recents1.get(0);
				Date existingUpdatedAt = existingRecord.getUpdatedAt();
				finalTxnTime = existingRecord.getTxnTime();
				finalCreatedTime = Objects.nonNull(existingRecord.getCreatedAt()) ? existingRecord.getCreatedAt() : ((Objects.nonNull(finalTxnTime) ? finalTxnTime : new Date()));
				existingRecord.setNotificationStatus(notificationStatus);
				existingRecord.setCreatedAt(finalCreatedTime);
				existingRecord.setUpdatedAt(new Date());
				ttl = TTLUtils.getTTL(reminderResponseModel.getData().getService(),finalTxnTime, finalCreatedTime);
				if(ttl<1)ttl=1;
				recentDataToKafkaService.setRecentConsumerSource(existingRecord, REMINDER_CONSUMER);
				isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord, existingUpdatedAt, ttl);
			} else {
				finalCreatedTime = new Date();
				buildRecentPrimaryKey(finalRecent, reminderResponseModel.getData().getCustomer_id(), reminderResponseModel.getData().getService(), rechargeNumber, operator, "");
				finalRecent.setNotificationStatus(notificationStatus);
				finalRecent.setCreatedAt(finalCreatedTime);
				finalRecent.setUpdatedAt(new Date());
				ttl = TTLUtils.getTTL(reminderResponseModel.getData().getService(),finalTxnTime, finalCreatedTime);
				if(ttl<1)ttl=1;
				recentDataToKafkaService.setRecentConsumerSource(finalRecent, REMINDER_CONSUMER);
				isUpdated = recentsRepository.updateRecentWhenNoExistingData(finalRecent, ttl);
			}
			if (Boolean.FALSE.equals(isUpdated))
				throw new ReminderListenerException("[RecentService.updateNotificationStatusInRecents] : Update query returning false for customerId :" + reminderResponseModel.getData().getCustomer_id() + "and rechargeNumber :" + rechargeNumber);
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[RecentService.updateNotificationStatusInRecents] :: Update notification status in recents successfull for customer_id : {} and rechargeNumber : {}",reminderResponseModel.getData().getCustomer_id() , rechargeNumber);
		} catch (Exception e) {
			metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECENT_UPDATE_REMINDER_REATTEMPT);
			logger.error(ServiceWrapperUtil.findServiceWrapper(reminderResponseModel), "[RecentService.updateNotificationStatusInRecents] :: Retrying again as update notification status in recents failed for customerId {}, rechargeNumber {}", reminderResponseModel.getData().getCustomer_id(), rechargeNumber);
			throw new ReminderListenerException(e);
		}

	}

	@Retryable(value = {ReminderListenerException.class},
			maxAttemptsExpression = "${db.writes.max.attempts}")
	public void updateRecentAutomaticStateInfo(Integer automaticStatus, Long customerId, String service, String rechargeNumber, String operator, String planBucket) throws ReminderListenerException{
		Boolean isUpdated;
		try {
			List<Recents> recents1 = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, service, rechargeNumber, operator, planBucket);
			Date finalCreatedTime;
			Integer ttl;
			Date finalTxnTime = null;
			Recents finalRecent = new Recents();
			if (!CollectionUtils.isEmpty(recents1)) {
				Recents existingRecord = recents1.get(0);
				//this is to set isPaytmVPA flag when data is skipped in Reminder Listener so that flag gets updated
				setIsPaytmVPAFlagForAutomaticWhenDataSkipped(existingRecord);
				Date existingUpdatedAt = existingRecord.getUpdatedAt();
				finalTxnTime = existingRecord.getTxnTime();
				finalCreatedTime = Objects.nonNull(existingRecord.getCreatedAt()) ? existingRecord.getCreatedAt() : ((Objects.nonNull(finalTxnTime) ? finalTxnTime : new Date()));
				existingRecord.setAutomaticStatus(automaticStatus);
				existingRecord.setCreatedAt(finalCreatedTime);
				existingRecord.setUpdatedAt(new Date());
				ttl = TTLUtils.getTTL(service,finalTxnTime, finalCreatedTime);
				if(ttl<1)ttl=1;
				recentDataToKafkaService.setRecentConsumerSource(existingRecord, REMINDER_CONSUMER);
				isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord,existingUpdatedAt,ttl);
			} else {
				finalCreatedTime = new Date();
				setIsPaytmVPAFlagForAutomaticWhenDataSkipped(finalRecent);
				finalRecent.setKey(new RecentsPrimaryKey());
				finalRecent.getKey().setService(service);
				finalRecent.getKey().setCustomerId(customerId);
				finalRecent.getKey().setOperator(operator);
				finalRecent.getKey().setRechargeNumber(rechargeNumber);
				finalRecent.getKey().setPlanBucket(planBucket);
				finalRecent.setAutomaticStatus(automaticStatus);
				finalRecent.setCreatedAt(finalCreatedTime);
				finalRecent.setUpdatedAt(new Date());
				ttl = TTLUtils.getTTL(service,finalTxnTime, finalCreatedTime);
				if(ttl<1)ttl=1;
				recentDataToKafkaService.setRecentConsumerSource(finalRecent, REMINDER_CONSUMER);
				isUpdated = recentsRepository.updateRecentWhenNoExistingData(finalRecent, ttl);
			}
			if (Boolean.FALSE.equals(isUpdated))
				throw new ReminderListenerException("[RecentService.updateRecentAutomaticStateInfo] : Retrying as update query returning false for customerId :"+customerId+"and rechargeNumber :"+rechargeNumber);
			logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateRecentAutomaticStateInfo] :: Update automatic status in recents successfull for customer_id : {} and rechargeNumber : {}",customerId, rechargeNumber);
		} catch (Exception e) {
			metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECENT_UPDATE_REMINDER_REATTEMPT);
			logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateRecentAutomaticStateInfo] :: Retrying as update automatic status in recents failed for customerId {}, rechargeNumber {}",customerId,rechargeNumber);
			throw new ReminderListenerException();
		}

	}

	@Retryable(value = {ReminderListenerException.class}, maxAttemptsExpression = "${db.writes.max.attempts}")
	public void updateRecentConsentValidTillInfo(Date consentValidTill, Long customerId, String service, String rechargeNumber, String operator, String planBucket) throws ReminderListenerException {
		try {
			List<Recents> recentsList = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, service, rechargeNumber, operator, planBucket);
			Date existingUpdatedAt = null;
			if (!CollectionUtils.isEmpty(recentsList)) {
				existingUpdatedAt = recentsList.get(0).getUpdatedAt();
			}
			Recents recents = prepareRecentsForUpdate(recentsList, consentValidTill, customerId, service, rechargeNumber, operator, planBucket);
			boolean isUpdated = updateRecents(recents, recentsList, existingUpdatedAt);
			if (!isUpdated) {
				throw new ReminderListenerException("[RecentService.updateRecentConsentValidTillInfo] : Retrying as update query returning false for customerId :" + customerId + " and rechargeNumber :" + rechargeNumber);
			}
			logger.info(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateRecentConsentValidTillInfo] :: Update consent valid till in recents successful for customer_id : {} and rechargeNumber : {}", customerId, rechargeNumber);
		} catch (Exception e) {
			metricsHelper.recordSuccessRate(REMINDER_CONSUMER, RECENT_UPDATE_REMINDER_REATTEMPT);
			logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[RecentService.updateRecentConsentValidTillInfo] :: Retrying as update consent valid till in recents failed for customerId {}, rechargeNumber {}", customerId, rechargeNumber);
			throw new ReminderListenerException();
		}
	}
	private Recents prepareRecentsForUpdate(List<Recents> recentsList, Date consentValidTill, Long customerId, String service, String rechargeNumber, String operator, String planBucket) {
		Recents recents;
		if (!CollectionUtils.isEmpty(recentsList)) {
			recents = recentsList.get(0);
			Date finalCreatedTime = Objects.nonNull(recents.getCreatedAt()) ? recents.getCreatedAt() : (Objects.nonNull(recents.getTxnTime()) ? recents.getTxnTime() : new Date());
			recents.setConsentValidTill(consentValidTill);
			recents.setCreatedAt(finalCreatedTime);
			recents.setUpdatedAt(new Date());
		} else {
			recents = new Recents();
			recents.setKey(new RecentsPrimaryKey());
			recents.getKey().setService(service);
			recents.getKey().setCustomerId(customerId);
			recents.getKey().setOperator(operator);
			recents.getKey().setRechargeNumber(rechargeNumber);
			recents.getKey().setPlanBucket(planBucket);
			recents.setConsentValidTill(consentValidTill);
			recents.setCreatedAt(new Date());
			recents.setUpdatedAt(new Date());
		}
		return recents;
	}

	private boolean updateRecents(Recents recents, List<Recents> recentsList, Date existingUpdatedAt) {
		Integer ttl = TTLUtils.getTTL(recents.getKey().getService(), recents.getTxnTime(), recents.getCreatedAt());
		if (ttl < 1) ttl = 1;
		recentDataToKafkaService.setRecentConsumerSource(recents, REMINDER_CONSUMER);
		if (!CollectionUtils.isEmpty(recentsList)) {
			return recentsRepository.updateRecentWhenDataAlreadyExist(recents, existingUpdatedAt, ttl);
		} else {
			return recentsRepository.updateRecentWhenNoExistingData(recents, ttl);
		}
	}

	@Retryable(value = {NickNameServiceException.class},
			maxAttemptsExpression = "${db.writes.max.attempts}")
	public Boolean fetchAndUpdateNickNameInRecents(NickNameRequest nickNameRequest) throws NickNameServiceException {
		Boolean existingRecent = false;
		try {
			List<Recents> recents;
			if (StringUtils.equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE, nickNameRequest.getPaytype())) {
				String panUniqueReference = nickNameRequest.getPanUniqueReference();
				String referenceId = nickNameRequest.getReferenceId();
				String rechargeNumber = (panUniqueReference != null) ? panUniqueReference : referenceId;
				nickNameRequest.setRechargeNumber(rechargeNumber);
				recents = recentsRepository.selectCreditCardRecents(nickNameRequest);
			} else {
				recents = recentsRepository.selectNonCreditRecents(nickNameRequest);
			}
			if (!CollectionUtils.isEmpty(recents)) {
				for (Recents recent : recents) {
					Date finalTxnTime = recent.getTxnTime();
					Date existingUpdatedAt = recent.getUpdatedAt();
					Date finalCreatedTime = Objects.nonNull(recent.getCreatedAt()) ? recent.getCreatedAt() : ((Objects.nonNull(finalTxnTime) ? finalTxnTime : new Date()));
					Integer ttl = TTLUtils.getTTL(nickNameRequest.getService(),finalTxnTime, finalCreatedTime);
					if(ttl<1)ttl=1;
					recent.setNickName(nickNameRequest.getNickName());
					recent.setCreatedAt(finalCreatedTime);
					recent.setUpdatedAt(new Date());
					recentDataToKafkaService.setRecentConsumerSource(recent, NICKNAME_SERVICE);
					Boolean isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(recent, existingUpdatedAt, ttl);
					if (Boolean.FALSE.equals(isUpdated)) {
						throw new NickNameServiceException("Update in nickname api failed as isUpdated is false");
					}
					logger.info(ServiceWrapperUtil.findServiceWrapper(nickNameRequest.getService()), "[RecentService.fetchAndUpdateNickNameInRecents] :: Updated nickname in recents successfully for nickNameRequest {}",nickNameRequest);
				}
				existingRecent = true;
			}
		}catch (Exception e){
			metricsHelper.recordSuccessRate(NICKNAME_SERVICE, RECENT_UPDATE_NICKNAME_SERVICE_REATTEMPT);
			logger.error(ServiceWrapperUtil.findServiceWrapper(nickNameRequest.getService()), "[RecentsServiceImpl.fetchAndUpdateNickNameInRecents] Retrying as Update nickname in recents failed for request {} error : {}",nickNameRequest,e);
			throw new NickNameServiceException(e);
		}
		return existingRecent;
	}

	@Recover
	public void recoverFromNickNameServiceException(NickNameServiceException ex) {
		metricsHelper.recordSuccessRate(NICKNAME_SERVICE, RECENT_UPDATE_NICKNAME_SERVICE_FAIL);
		logger.error("[RecentService.recoverFromAutomaticListenerException] All retry attempts exhausted for updateAutomaticInfo() after all attempts.",ex);
	}

	@Retryable(value = { PlanValidityListenerException.class }, maxAttemptsExpression = "${db.writes.max.attempts}")
	public void insertIntoRecentAndCustomerBill(Recents recent, PlanExpiryHistory planExpiryHistory) throws PlanValidityListenerException {
		try {
			logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "RecentService::insertIntoRecentAndCustomerBill starts rechargeNumber {} , service {} , operator {}", recent.getKey().getRechargeNumber(), recent.getKey().getService(), recent.getKey().getOperator());
			Integer limit = serviceConfig.getRechargeNumberToCustomerIdLimit();
			List<Long> customerIds = mappingService.getCustomerIdWithLimit(recent.getKey().getRechargeNumber(), recent.getKey().getService(), recent.getKey().getOperator(), limit + 1);
			if(DATA_EXHAUST_PLAN_BUCKET.equalsIgnoreCase(recent.getKey().getPlanBucket())){
				customerIds=new ArrayList<>();
			}
			List<Long> customerIdsForCacheClean = new ArrayList<>(customerIds);
			if(!customerIds.contains(planExpiryHistory.getCustomerid()))
				customerIds.add(planExpiryHistory.getCustomerid());
			if (customerIds.size() > limit) {
				logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "RecentService::insertIntoRecentAndCustomerBill exit customerIds size greater than threshold rechargeNumber {} , service {} , operator {}", recent.getKey().getRechargeNumber(), recent.getKey().getService(), recent.getKey().getOperator());
			} else {
				logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "RecentService::insertIntoRecentAndCustomerBill customerIds is {} count {} rechargeNUmber {} service {} operator {}", customerIds,customerIds.size(),planExpiryHistory.getRechargeNumber(),planExpiryHistory.getService(),planExpiryHistory.getOperator());
				this.updateExpiryInfo(customerIds,recent);
			}
			this.cleanCache(customerIdsForCacheClean);

		} catch (Exception e) {
			logger.error(ServiceWrapperUtil.findServiceWrapper(recent), "[RecentService.insertIntoRecent] recent upsert for recharge number {}, service {}, operator {}, Exception {}",
					planExpiryHistory.getRechargeNumber(), planExpiryHistory.getService(), planExpiryHistory.getOperator(), e);
			metricsHelper.recordSuccessRate(Constants.PlanValidityConsumerConstants.PLAN_VALIDITY_CONSUMER,Constants.PlanValidityConsumerConstants.PLAN_DATA_UPDATE_REATTEMPT);
			throw new PlanValidityListenerException(e);
		}
	}
	private void cleanCache(List<Long> customerIdsForCacheClean){
		try {
			for(Long customerId:customerIdsForCacheClean)
				kafkaProducerService.sendMessage(Long.toString(customerId));
		}
		catch (Exception e){
			logger.error("[PlanValidityListeners.insertDataIntoCassandra] kafka cache clean event pushlish error for cusomter id {}, Exception",
					customerIdsForCacheClean,e);
			metricsHelper.recordSuccessRate(PLAN_VALIDITY_CONSUMER,RECO_REFRESH_ERROR_EVENT);
		}
	}

	private Map<Long,Boolean> txnMap(List<Recents> recentList){
		Map<Long,Boolean> txnMap=new HashMap<>();
		if(Objects.nonNull(recentList)){
			for(Recents recents:recentList){
				if(!txnMap.containsKey(recents.getKey().getCustomerId())
					|| !txnMap.get(recents.getKey().getCustomerId())){
					txnMap.put(recents.getKey().getCustomerId(),false);
					if(recents.getOrderId()!=null
							|| recents.getLastFailureTxn()!=null
							|| recents.getLastPendingTxn()!=null){
						txnMap.put(recents.getKey().getCustomerId(),true);
					}
				}

			}
		}
		return txnMap;
	}

	private void updateExpiryInfo(List<Long> customerIds, Recents recentUpdate) throws PlanValidityListenerException {
		try{
			List<Recents> recentList = recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(customerIds, recentUpdate.getKey().getService(), recentUpdate.getKey().getRechargeNumber(), recentUpdate.getKey().getOperator());
			Map<Long,Boolean> txnMap = txnMap(recentList);
			txnMap.forEach((customerId, txnStatus) -> {
				if(!txnStatus && customerId != recentUpdate.getKey().getCustomerId()){
					logger.info(ServiceWrapperUtil.findServiceWrapper(recentUpdate), "updateExpiryInfo removing customer {} recharge_number {}",customerId,recentUpdate.getKey().getRechargeNumber());
					customerIds.remove(customerId);
				}
			});
			if (Objects.nonNull(recentList)) {
				updatePlanExpiryForExistingCustomerWithSamePlan(recentList,recentUpdate,customerIds);
				updatePlanExpiryForExistingCustomerWithDifferentPlan(recentList,recentUpdate,customerIds);
			}
			insertPlanExpiryForNewCustomer(recentUpdate,customerIds);
			if(recentUpdate.getDueDate() !=null){
				CustomerBill reminderClusterKey = prepareReminderClusterData(recentUpdate);
				customerBillDao.saveRecentToCustomerBill(recentList, reminderClusterKey);
			}
		}catch(Exception e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(recentUpdate), "[RecentService.updateExpiryInfo] customer : {} recharge {} and plan {} with error = ",customerIds,recentUpdate.getKey().getRechargeNumber(),recentUpdate.getKey().getPlanBucket(),e);
			throw new PlanValidityListenerException(e);
		}
	}

	private void updatePlanExpiryForExistingCustomerWithSamePlan(List<Recents> recentList,Recents recentUpdate,List<Long> customerIds) throws PlanValidityListenerException{
		try{
			for (Recents recents : recentList) {
				if(recents.getKey().getPlanBucket().equals(recentUpdate.getKey().getPlanBucket())){
					logger.info(ServiceWrapperUtil.findServiceWrapper(recents), "RecentService::updatePlanExpiryForExistingCustomerWithSamePlan update expiry for customer {} recharge {} service {} operator {}", recents.getKey().getCustomerId(),recents.getKey().getRechargeNumber(),recents.getKey().getService(),recents.getKey().getOperator());
					Date existingUpdatedDate=recents.getUpdatedAt();
					recents = getPlanValidityUpdatedField(recentUpdate, recents);
					if(recentUpdate.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
						recents.setProductId(recentUpdate.getProductId());
					if(recents.getKey().getCustomerId().equals(recentUpdate.getKey().getCustomerId())){
						recents.setNotificationStatus(recentUpdate.getNotificationStatus());
					}
					Integer ttl = 0;
					if(recents.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
						ttl = TTLUtils.getMidnightTTL();
					else
						ttl=TTLUtils.getTTL(recents.getKey().getService(),recents.getTxnTime(),recents.getCreatedAt());
					if(ttl <1)ttl=1;
					recentDataToKafkaService.setRecentConsumerSource(recents, PLAN_VALIDITY_CONSUMER);
					boolean isUpdated=recentsRepository.updateRecentWhenDataAlreadyExist(recents,existingUpdatedDate,ttl);
					if(isUpdated){
						customerIds.remove(recents.getKey().getCustomerId());
					}else{
						throw new PlanValidityListenerException("failure while updating plans info in recent for customer details "+recents.getKey().getCustomerId() +"and "+recents.getKey().getRechargeNumber());
					}
				}
			}
		} catch(PlanValidityListenerException e){
			throw  new PlanValidityListenerException(e);
		}
	}

	private void updatePlanExpiryForExistingCustomerWithDifferentPlan(List<Recents> recentList,Recents recentUpdate,List<Long> customerIds) throws PlanValidityListenerException{
		try{
			for (Recents recents : recentList) {
				if(customerIds.contains(recents.getKey().getCustomerId())){
					logger.info(ServiceWrapperUtil.findServiceWrapper(recentUpdate), "RecentService::updatePlanExpiryForExistingCustomerWithDifferentPlan insert expiry for customer {} recharge {} service {} operator {}", recents.getKey().getCustomerId(),recents.getKey().getRechargeNumber(),recents.getKey().getService(),recents.getKey().getOperator());
					Recents recentNewEvent=createNewPlanValidityRecent(recents,recentUpdate);
					if(recents.getKey().getCustomerId().equals(recentUpdate.getKey().getCustomerId())){
						recents.setNotificationStatus(recentUpdate.getNotificationStatus());
					}
					Integer ttl = 0;
					if(DATA_EXHAUST_PLAN_BUCKET.equalsIgnoreCase(recentUpdate.getKey().getPlanBucket()))
						ttl = TTLUtils.getMidnightTTL();
					else
						ttl=TTLUtils.getTTL(recents.getKey().getService(),null,recents.getCreatedAt());
					if(ttl <1)ttl=1;
					recentDataToKafkaService.setRecentConsumerSource(recentNewEvent, PLAN_VALIDITY_CONSUMER);
					boolean isUpdated=recentsRepository.updateRecentWhenNoExistingData(recentNewEvent,ttl);
					if(isUpdated){
						customerIds.remove(recents.getKey().getCustomerId());
					}else{
						throw new PlanValidityListenerException("failure while inserting plans info in recent for customer details "+recents.getKey().getCustomerId() +"and "+recents.getKey().getRechargeNumber());
					}
				}
			}
		}catch(PlanValidityListenerException e){
			throw  new PlanValidityListenerException(e);
		}
	}

	private void insertPlanExpiryForNewCustomer(Recents recentUpdate,List<Long> customerIds) throws PlanValidityListenerException{
		try{
			if(customerIds.contains(recentUpdate.getKey().getCustomerId())){
				logger.info(ServiceWrapperUtil.findServiceWrapper(recentUpdate), "RecentService::insertPlanExpiryForNewCustomer insert expiry for customer {} recharge {} service {} operator {}", recentUpdate.getKey().getCustomerId(),recentUpdate.getKey().getRechargeNumber(),recentUpdate.getKey().getService(),recentUpdate.getKey().getOperator());
				recentUpdate.setUpdatedAt(new Date());
				recentUpdate.setCreatedAt(new Date());
				Integer ttl = 0;
				if(recentUpdate.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
					ttl = TTLUtils.getMidnightTTL();
				else
					ttl=TTLUtils.getTTL(recentUpdate.getKey().getService(),null,recentUpdate.getCreatedAt());
				if(ttl <1)ttl=1;
				recentDataToKafkaService.setRecentConsumerSource(recentUpdate, PLAN_VALIDITY_CONSUMER);
				boolean isUpdated=recentsRepository.updateRecentWhenNoExistingData(recentUpdate,ttl);
				if(isUpdated){
					customerIds.remove(recentUpdate.getKey().getCustomerId());
				}else{
					throw new PlanValidityListenerException("failure while inserting plans info in recent for customer details "+recentUpdate.getKey().getCustomerId() +", "+recentUpdate.getKey().getRechargeNumber());
				}
			}
		}catch(PlanValidityListenerException e){
			throw  new PlanValidityListenerException(e);
		}
	}

	@Recover
	public void recoverFromFsRechargeListenerException(PlanValidityListenerException ex) {
		metricsHelper.recordSuccessRate(Constants.PlanValidityConsumerConstants.PLAN_VALIDITY_CONSUMER,Constants.PlanValidityConsumerConstants.PLAN_DATA_UPDATE_FAIL);
		logger.error("[RecentService.updateRecentData] All retry attempts exhausted for PlanValidityListener after all attempts. {}",ex.getMessage());
	}

	private Recents createNewPlanValidityRecent(@NonNull Recents existingRecent,@NonNull Recents recentUpdate){
		Recents recentNewEvent=new Recents();
		RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
		recentsPrimaryKey.setCustomerId(existingRecent.getKey().getCustomerId());
		recentsPrimaryKey.setRechargeNumber(recentUpdate.getKey().getRechargeNumber());
		recentsPrimaryKey.setService(recentUpdate.getKey().getService());
		recentsPrimaryKey.setOperator(recentUpdate.getKey().getOperator());
		recentsPrimaryKey.setPlanBucket(recentUpdate.getKey().getPlanBucket());
		recentNewEvent.setKey(recentsPrimaryKey);
		recentNewEvent.setExtra(recentUpdate.getExtra());
		if(recentUpdate.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
			recentNewEvent.setProductId(recentUpdate.getProductId());
		recentNewEvent.setCircle(recentUpdate.getCircle());
		recentNewEvent.setPayType(recentUpdate.getPayType());
		recentNewEvent.setDueDate(recentUpdate.getDueDate());
		recentNewEvent.setDueAmount(recentUpdate.getDueAmount());
		recentNewEvent.setOriginalDueAmount(recentUpdate.getOriginalDueAmount());
		recentNewEvent.setOriginalMinDueAmount(recentUpdate.getOriginalMinDueAmount());
		recentNewEvent.setPlanName(recentUpdate.getPlanName());
		recentNewEvent.setConsumerName(existingRecent.getConsumerName());
		recentNewEvent.setCylinderAgencyName(existingRecent.getCylinderAgencyName());
		recentNewEvent.setNickName(existingRecent.getNickName());
		recentNewEvent.setNotificationStatus(existingRecent.getNotificationStatus());
		recentNewEvent.setBillUpdateTime(new Date());
		recentNewEvent.setRechargeNumber8(existingRecent.getRechargeNumber8());
		recentNewEvent.setRechargeNumber7(existingRecent.getRechargeNumber7());
		recentNewEvent.setRechargeNumber6(existingRecent.getRechargeNumber6());
		recentNewEvent.setRechargeNumber5(existingRecent.getRechargeNumber5());
		recentNewEvent.setRechargeNumber4(existingRecent.getRechargeNumber4());
		recentNewEvent.setRechargeNumber3(existingRecent.getRechargeNumber3());
		recentNewEvent.setRechargeNumber2(existingRecent.getRechargeNumber2());
		recentNewEvent.setUpdatedAt(new Date());
		recentNewEvent.setCreatedAt(new Date());
		recentNewEvent.setNewBillUpdatedAt(recentUpdate.getNewBillUpdatedAt());
		if(recentUpdate.getReconId() != null){
			recentNewEvent.setReconId(recentUpdate.getReconId());
		}
		if(Objects.equals(existingRecent.getKey().getCustomerId(), recentUpdate.getKey().getCustomerId())){
			if(Objects.nonNull(recentUpdate.getAutomaticStatus())){
				recentNewEvent.setAutomaticStatus(recentUpdate.getAutomaticStatus());
			}
			if(Objects.nonNull(recentUpdate.getAutomaticAmount())) {
				recentNewEvent.setAutomaticAmount(recentUpdate.getAutomaticAmount());
			}
		}
		return recentNewEvent;
	}

	private Recents getPlanValidityUpdatedField(Recents recent,Recents existingObject) {
		existingObject.setDueDate(recent.getDueDate());
		existingObject.setDueAmount(recent.getDueAmount());
		existingObject.setOriginalMinDueAmount(recent.getOriginalMinDueAmount());
		existingObject.setOriginalDueAmount(recent.getOriginalDueAmount());
		existingObject.setMinDueAmount(recent.getMinDueAmount());
		existingObject.setIsMarkAsPaid(false);
		existingObject.setMarkAsPaidSource(null);
		existingObject.setMarkAsPaidTime(null);
		existingObject.setMarkAsPaidAmount(0.0);
		existingObject.setUpdatedAt(new Date());
		existingObject.setBillUpdateTime(new Date());
		existingObject.setPlanName(recent.getPlanName());
		existingObject.setNewBillUpdatedAt(recent.getNewBillUpdatedAt());
		existingObject.setExtra(recent.getExtra());
		if(recent.getReconId() != null){
			existingObject.setReconId(recent.getReconId());
		}
		if(existingObject.getCreatedAt() ==null){
			if(existingObject.getTxnTime() !=null){
				existingObject.setCreatedAt(existingObject.getTxnTime());
			}else{
				existingObject.setCreatedAt(new Date());
			}
		}
		if(Objects.equals(existingObject.getKey().getCustomerId(), recent.getKey().getCustomerId())){
			if(Objects.nonNull(recent.getAutomaticStatus())) {
				existingObject.setAutomaticStatus(recent.getAutomaticStatus());
			}
			if(Objects.nonNull(recent.getAutomaticAmount())) {
				existingObject.setAutomaticAmount(recent.getAutomaticAmount());
			}
		}
		return existingObject;
	}


	private CustomerBill prepareReminderClusterData(Recents newRecent) {
		try {
			CustomerBill customerBill = new CustomerBill();
			customerBill.setKey(new CustomerBillPrimaryKey());
			customerBill.getKey().setCustomerId(newRecent.getKey().getCustomerId());
			customerBill.getKey().setDue_date(newRecent.getDueDate());
			customerBill.getKey().setService(newRecent.getKey().getService());
			customerBill.getKey().setOperator(newRecent.getKey().getOperator());
			customerBill.getKey().setRechargeNumber(newRecent.getKey().getRechargeNumber());
			customerBill.getKey().setPlanBucket(newRecent.getKey().getPlanBucket());
			return customerBill;
		} catch (Exception e) {
			logger.error(ServiceWrapperUtil.findServiceWrapper(newRecent), "[ReminderListeners.insertDataIntoCassandra]  prepareReminderClusterData::Exception", e);
			throw new RuntimeException(e);
		}

    }
    private void prepareFinalRecents(Recents recents, Long customerId, String service, String operator, String planBucket, String cin, String par, String tin, Boolean isTokenizedTransaction, Date billDate, Date dueDate, Double dueAmount, Double minDueAmount, Double originalDueAmount, Double originalMinDueAmount,
                                     Long productId, Integer automaticStatus, Date billUpdateTime, Boolean isMarkAsPaid, Date markAsPaidTime, Double markAsPaidAmount, Date updatedAt, String rechargeNumber, String rechargeNumber2, String rechargeNumber3,
                                     String rechargeNumber4, String rechargeNumber5, String rechargeNumber6, String rechargeNumber7,

									 String rechargeNumber8, String circle, String paytype, String mcn, Integer notificationStatus, Integer notPaidOnPaytm, Date txnTime, Double txnAmount, Date createdAt, Date newBillUpdatedAt, Boolean isSavedCard,Boolean isValidation,Boolean isTransaction, String eventSource,Boolean is_new_biller,String pgCardId, Date earlyPaymentDate, Double earlyPaymentAmount, String reconId, Boolean nextBillFetchDateFlag, Integer reminderStatus, Date oldBillFetchDate, String cardVariant, String cardSkin, String extra,Boolean isNewBillIdentified, Date remindMeLateDate, Double currentOutstandingAmount, Date consentValidTill) {

		buildRecentPrimaryKey(recents,customerId,service,rechargeNumber,operator,planBucket);
        recents.setDueDate(dueDate);
        recents.setBillDate(billDate);
        recents.setDueAmount(dueAmount);
        recents.setMinDueAmount(minDueAmount);
		recents.setCurrentOutstandingAmount(currentOutstandingAmount);
		recents.setOriginalDueAmount(originalDueAmount);
        recents.setOriginalMinDueAmount(originalMinDueAmount);
        recents.setPar(par);
        recents.setTin(tin);
        recents.setCin(cin);
        recents.setPayType(paytype);
        recents.setCircle(circle);
        recents.setProductId(productId);
		if(Objects.nonNull(pgCardId)){
			recents.setRechargeNumber4(null);
		}
		this.setRechargeNumbers(recents,rechargeNumber2,rechargeNumber3,rechargeNumber4,rechargeNumber5,rechargeNumber6,rechargeNumber7
				,rechargeNumber8);
        recents.setBillUpdateTime(billUpdateTime);
        recents.setAutomaticStatus(automaticStatus);
        recents.setNotificationStatus(notificationStatus);
        recents.setMcn(mcn);
        recents.setUpdatedAt(updatedAt);
        recents.setCreatedAt(createdAt);
        recents.setIsTokenizedTransaction(isTokenizedTransaction);
		recents.setIsNewBiller(is_new_biller);
		recents.setPgCardId(pgCardId);
		if (Objects.nonNull(notPaidOnPaytm)) {
			if (notPaidOnPaytm == 1) {
				recents.setNotPaidOnPaytm(1);
				recents.setTxnTime(txnTime);
				recents.setTxnAmount(txnAmount);
			}
		}
        if (Boolean.FALSE.equals(isMarkAsPaid)) {
            recents.setIsMarkAsPaid(isMarkAsPaid);
            recents.setMarkAsPaidAmount(markAsPaidAmount);
            recents.setMarkAsPaidTime(markAsPaidTime);
        }
		if (newBillUpdatedAt != null)
			recents.setNewBillUpdatedAt(newBillUpdatedAt);
		if(isSavedCard!=null)
			recents.setIsSavedCard(isSavedCard);
		if(isValidation!=null)
			recents.setIsValidation(isValidation);
		if(isTransaction!=null)
			recents.setIsTransaction(isTransaction);
		if(Boolean.TRUE.equals(isTransaction) || eventSource!=null)
			recents.setEventSource(eventSource);
		recents.setEarlyPaymentDate(earlyPaymentDate);
		recents.setEarlyPaymentAmount(earlyPaymentAmount);
		recents.setReconId(reconId);
		recents.setNextBillFetchDateFlag(nextBillFetchDateFlag);
		recents.setReminderStatus(reminderStatus);
		recents.setOldBillFetchDate(oldBillFetchDate);
		recents.setCardVariant(cardVariant);
		recents.setCardSkin(cardSkin);
		recents.setIsNewBillIdentified(newBillIdentifiedUpdateForAllowedUser(isNewBillIdentified, recents.getKey().getCustomerId()));
		mergeRecentsExtraWithReceivedExtraInfo(recents,extra);
		recents.setRemindLaterDate(remindMeLateDate);
		recents.setConsentValidTill(consentValidTill);
	}

	private void buildRecentPrimaryKey(Recents recents, Long customerId, String service, String rechargeNumber, String operator, String planBucket){
		recents.setKey(new RecentsPrimaryKey());
		recents.getKey().setPlanBucket(planBucket != null ? planBucket : "");
		recents.getKey().setService(service);
		recents.getKey().setOperator(operator);
		recents.getKey().setCustomerId(customerId);
		recents.getKey().setRechargeNumber(rechargeNumber);
	}

	public Boolean updateMarkAsPaidDetailsInRecents(Recents recents, Date existingUpdatedAt) throws RecentDataToKafkaException {
		Date finalTxnTime = Objects.nonNull(recents.getTxnTime()) ? recents.getTxnTime() : null;
		Date finalCreatedTime = Objects.nonNull(recents.getCreatedAt()) ? recents.getCreatedAt() : ((Objects.nonNull(finalTxnTime) ? finalTxnTime : new Date()));
		recents.setCreatedAt(finalCreatedTime);

		Integer ttl = TTLUtils.getTTL(recents.getKey().getService(),finalTxnTime,finalCreatedTime);

		if (recents.getKey().getService().toLowerCase().equals("fastag recharge") && RecentUtils.isFastagLowBalanceForRecents(recents)) {
			ttl = TTLExhaustEventUtil.getTTL(recents.getKey().getService(), recents.getDueDate());
		}
		if ((Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET).equalsIgnoreCase(recents.getKey().getPlanBucket())
				&& Constants.PREPAID_PAYTYPE.equalsIgnoreCase(recents.getPayType())) {
			ttl = TTLUtils.getMidnightTTL();
		}

		if (recents.getKey().getService().toLowerCase().equals("fastag recharge") && RecentUtils.isFastagLowBalanceForRecents(recents)) {
			ttl = TTLExhaustEventUtil.getTTL(recents.getKey().getService(), recents.getDueDate());
		}

		if(ttl<1)ttl=1;

		recentDataToKafkaService.setRecentConsumerSource(recents, MARK_AS_PAID_MESSAGE_TYPE);
		return recentsRepository.updateRecentWhenDataAlreadyExist(recents,existingUpdatedAt,ttl);
	}

	@Retryable(value = {CDCReminderListenerException.class},
			maxAttempts = 4)
	public void updateRecentFromReminderCdcConsumer(ReminderCDC reminderCDC) throws CDCReminderListenerException {
		try {
			boolean isUpdated;
			boolean billPaidEvent=false;
			Map<String,String> extras = null;
			List<Recents> recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(reminderCDC.getAfter().getCustomerId().getValue(), reminderCDC.getAfter().getService().getValue(), reminderCDC.getAfter().getRechargeNumber().getValue(), reminderCDC.getAfter().getOperator().getValue());
			Recents finalRecent = new Recents();
			finalRecent.setKey(new RecentsPrimaryKey());
			if (reminderCDC.getAfter().getExtra() != null && reminderCDC.getAfter().getExtra().getValue() != null) {
				extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
				if(Objects.nonNull(extras) && extras.containsKey(Constants.BILL_PAID_IDENTIFIER_KEY) && StringUtils.equalsIgnoreCase(extras.get(Constants.BILL_PAID_IDENTIFIER_KEY),Constants.BILL_PAID_IDENTIFIER_VALUE)){
					billPaidEvent = true;
				}
			}
			if(billPaidEvent && !CollectionUtils.isEmpty(recents))
			{
				if(reminderCDC.isRecoveryPacket()){
					metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER,BILL_PAID_EVENT);
				}

				return;
			}
			else {
				isUpdated = updateP2pAndSmsBillsInRecent(recents,finalRecent,reminderCDC);
			}
			if (!isUpdated) {
				throw new CDCReminderListenerException("[RecentService.updateRecentFromReminderCdcConsumer] : Update query failed as IsUpdated is false");
			}
			List<String> tags=new ArrayList<>();
			tags.add("event_type:inserted");
			tags.add("operator:"+reminderCDC.getAfter().getOperator());
			tags.add("service:"+reminderCDC.getAfter().getService());
			metricsHelper.recordSuccessRateForMultipleTags(Constants.CDC_CONSUMER_STATS, tags);
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListenre.insertDataIntoCassandra] :: Recents updated for customerId {} and rechargeNumber {} and service {}",reminderCDC.getAfter().getCustomerId().getValue(),reminderCDC.getAfter().getRechargeNumber().getValue(),reminderCDC.getAfter().getService().getValue());
		} catch (Exception e) {
			metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, Constants.RECENT_UPDATE_CDC_REATTEMPT);
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[RecentService.updateP2pBillFromCDCConsumer] : Retrying update in recents again for p2p consumer as update failed for customerId {} and rechargeNumber {} and service {} and error",reminderCDC.getAfter().getCustomerId().getValue(),reminderCDC.getAfter().getRechargeNumber().getValue(),reminderCDC.getAfter().getService().getValue(),e);
			throw new CDCReminderListenerException(e);
		}
	}

	public void updateRecentFromReminderCdcConsumerForSMSparsing(ReminderCDC reminderCDC) throws CDCReminderListenerException{
		//this method is used for fastag sms parsing where update operation is conditional and have to update primary key so works in diffrent way.
		if(StringUtils.startsWithIgnoreCase(reminderCDC.getAfter().getRechargeNumber().getValue(),"default")
				|| StringUtils.equalsIgnoreCase(reminderCDC.getAfter().getOperator().getValue(), "dummy bank")){
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListenre.updateRecentFromReminderCdcConsumerForSMSparsing] :: ignoring payload as invalid rechargeNumber {} received.",reminderCDC.getAfter().getRechargeNumber().getValue());
			metricsHelper.incrementCount(Constants.CDC_REMINDER_CONSUMER, "service:FASTAG_SMS_PARSING","status:invalidpayload");
			return;
		}
		CreateRecentRequest recentRequest = new CreateRecentRequest();
		recentRequest.setPaytype(reminderCDC.getAfter().getPaytype().getValue());
		recentRequest.setService(reminderCDC.getAfter().getService().getValue());
		recentRequest.setOperator(reminderCDC.getAfter().getOperator().getValue());
		recentRequest.setCustomerId(reminderCDC.getAfter().getCustomerId().getValue());
		recentRequest.setProductId(reminderCDC.getAfter().getProductId().getValue());
		recentRequest.setRechargeNumber(reminderCDC.getAfter().getRechargeNumber().getValue());
		recentRequest.setPlanBucket("");
		try {
			List<String> existingOperators = new ArrayList(); // adding all the operator list except low balance
			List<Recents> recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(reminderCDC.getAfter().getCustomerId().getValue(), reminderCDC.getAfter().getService().getValue(), reminderCDC.getAfter().getRechargeNumber().getValue());
			if (Objects.nonNull(recents) && !recents.isEmpty() && recents.size() >=0) {
				for(Recents recent: recents){
					if(!StringUtils.equalsIgnoreCase(recent.getOperator(),Constants.FASTAG_LOW_BALANCE_OPERATOR)){
						existingOperators.add(recent.getOperator().toLowerCase());
					}
				}
			}
//			We will not entertain dummy bank , insert actual bank if no data if any data do nothing
//			empty record -> insert non dummy bank
//			non dummy bank -> do nothing
//			if ( existingOperators.isEmpty() || recents.size() <= 2) {
				if (existingOperators.isEmpty()) {
					recentsService.createRecentForInsurance(recentRequest);
					metricsHelper.incrementCount(Constants.CDC_REMINDER_CONSUMER, "service:FASTAG_SMS_PARSING","status:"+Constants.MetricConstants.SUCCESS);
					return;
				}
//			}
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListenre.updateRecentFromReminderCdcConsumerForSMSparsing] :: Duplicate data for with payload {}",reminderCDC);
			metricsHelper.incrementCount(Constants.CDC_REMINDER_CONSUMER, "service:FASTAG_SMS_PARSING","status:"+Constants.MetricConstants.DUPLICATE_PAYLOAD);
		}catch (Exception e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListenre.updateRecentFromReminderCdcConsumerForSMSparsing] :: Exception while creating recent with payload {} and error {}",reminderCDC,e);
			metricsHelper.incrementCount(Constants.CDC_REMINDER_CONSUMER, "service:FASTAG_SMS_PARSING","status:"+ERROR_EVENT);
		}


	}
	@Retryable(value = {CDCReminderListenerException.class},
			maxAttempts = 4)
	public void unsetBillForDeleteValidation(ReminderCDC reminderCDC) throws CDCReminderListenerException {
		try {
			Boolean isUpdated;
			RecentsPrimaryKey key=new RecentsPrimaryKey();
			key.setCustomerId(reminderCDC.getAfter().getCustomerId().getValue());
			key.setRechargeNumber(reminderCDC.getAfter().getRechargeNumber().getValue());
			key.setOperator(reminderCDC.getAfter().getOperator().getValue());
			key.setService(reminderCDC.getAfter().getService().getValue());
			key.setPlanBucket("");
			List<Recents> recents = null;
			if(RecentUtils.isWhitelistedCustIdForMobileNonRUPersist(reminderCDC.getAfter().getCustomerId().getValue(),serviceConfig)&&Objects.nonNull(reminderCDC.getAfter().getPaytype())&&Constants.PREPAID_PAYTYPE.equalsIgnoreCase(reminderCDC.getAfter().getPaytype().getValue())&&Objects.nonNull(reminderCDC.getAfter().getService())&&Constants.ServiceTypeConstants.MOBILE.equalsIgnoreCase(reminderCDC.getAfter().getService().getValue())){
                logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "unsetBillForDeleteValidation:: unsetting for customer id {}",reminderCDC.getAfter().getCustomerId());
				recents=fetchRecentsCorrespondingToPlanBucket(recents,reminderCDC,key);

			}
			else{
				recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(key.getCustomerId(),key.getService(),
						key.getRechargeNumber(),key.getOperator(),key.getPlanBucket());

			}

			if(Objects.nonNull(recents) && recents.size() > 0){
				for(Recents recent :recents){
                  if(Constants.EVENTSOURCE_VALIDATION.equalsIgnoreCase(recent.getEventSource())
						  || (RecentUtils.isWhitelistedCustIdForMobileNonRUPersist(reminderCDC.getAfter().getCustomerId().getValue(),serviceConfig)
					      &&Constants.EVENTSOURCE_SMS.equalsIgnoreCase(recent.getEventSource())
						  && Constants.CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType())
						  &&  Constants.ServiceTypeConstants.MOBILE.equalsIgnoreCase(recent.getKey().getService())
						  && StringUtils.equals("",recent.getKey().getPlanBucket())
						  && !StringUtils.equals("",RecentUtils.getPlanBucketForCDCPayload(reminderCDC))
						  && RecentUtils.getPlanBucket(recent).equalsIgnoreCase(RecentUtils.getPlanBucketForCDCPayload(reminderCDC)))) {
					Date existingUpdatedDate=recent.getUpdatedAt();
					recent.setEventSource(null);
					recent.setUpdatedAt(new Date());
					recent.setDueDate(null);
					recent.setBillDate(null);
					recent.setDueAmount(0.0);
					recent.setMinDueAmount(0.0);
					recent.setOriginalMinDueAmount(0.0);
					recent.setOriginalDueAmount(0.0);
					recent.setBillUpdateTime(new Date());
					if (reminderCDC.getAfter().getReconId() != null)
						recent.setReconId(reminderCDC.getAfter().getReconId());
				resetPartialBillStateInExtra(recent);
					Integer ttl = TTLUtils.getTTL(recent.getKey().getService(), recent.getTxnTime(), recent.getCreatedAt());
					if (ttl < 1) ttl = 1;
					recentDataToKafkaService.setRecentConsumerSource(recent, Constants.CDC_REMINDER_CONSUMER);
					isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(recent,existingUpdatedDate,ttl);
					if (Boolean.FALSE.equals(isUpdated)) {
						throw new CDCReminderListenerException("[RecentService.updateRecentFromReminderCdcConsumer] : Update query failed as IsUpdated is false");
					}

				}

				metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, UNSET_BILL);
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListenre.unsetBillForDeleteValidation] :: Recents updated for customerId {} and rechargeNumber {} and service {}",reminderCDC.getAfter().getCustomerId().getValue(),reminderCDC.getAfter().getRechargeNumber().getValue(),reminderCDC.getAfter().getService().getValue());
				List<Long> customers=new ArrayList<>();
				customers.add(reminderCDC.getAfter().getCustomerId().getValue());
				cleanCache(customers);
			}
				}
		} catch (Exception e) {
			metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER, Constants.RECENT_UPDATE_CDC_REATTEMPT);
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[RecentService.unsetBillForDeleteValidation] : Retrying update in recents again for p2p consumer as update failed customerId {} and rechargeNumber {} and service {} and error {}",reminderCDC.getAfter().getCustomerId().getValue(),reminderCDC.getAfter().getRechargeNumber().getValue(),reminderCDC.getAfter().getService().getValue(),e);
			throw new CDCReminderListenerException(e);
		}
	}

	@Recover
	public void recoverFromCDCReminderListenerException(CDCReminderListenerException ex) {
		metricsHelper.recordSuccessRate(Constants.CDC_REMINDER_CONSUMER,Constants.RECENT_UPDATE_CDC_FAIL);
		logger.error("[RecentService.recoverFromCDCReminderListenerException] All retry attempts exhausted for updateRecentFromReminderCdcConsumer() after all attempts. message {} error message {} exception   {}",ex.getMessage(),ex.getErrorMessages(),ex);
	}

	public Boolean updateP2pAndSmsBillsInRecent(List<Recents> recents, Recents finalRecent, ReminderCDC reminderCDC) throws RecentDataToKafkaException {
		boolean isUpdated = false;
		Integer ttl;
		boolean txnExists = false;
		boolean isCacheToBeCleaned = false;
		if (reminderCDC.isRecoveryPacket()) {
			metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, TOTAL_ELIGIBLE_RECORDS_FOR_CDC_RECOVERY_UPDATE);

		}
		if (!CollectionUtils.isEmpty(recents)) {
			try{
				String eventSource = reminderCDC.getAfter().getEventSource().getValue();
				String service = reminderCDC.getAfter().getService().getValue();
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "updateP2pAndSmsBillsInRecent, service - {}, eventSource - {}", service, eventSource);
				if (Constants.ServiceTypeConstants.MOBILE.equalsIgnoreCase(service) && StringUtils.equalsIgnoreCase(eventSource, EVENTSOURCE_SMS)) {
					updateReminderCdcCircleAndProductId(recents, reminderCDC);
				}
			} catch (Exception ex) {
				logger.error(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "updateP2pAndSmsBillsInRecent:: error while updating circle and product id, rechargenumber: {}, operation: {}", reminderCDC.getAfter().getRechargeNumber(), reminderCDC.getOp());
				metricsHelper.incrementCount(Constants.MetricConstants.REMINDER_CDC_CIRCLE_UPDATED, "status:failed", "operation:"+reminderCDC.getOp());
			}
			if(reminderCDC.getAfter().getRemindLaterFlow()!=null && Boolean.TRUE.equals(reminderCDC.getAfter().getRemindLaterFlow().getValue()) && reminderCDC.getAfter().getRemindLaterDate()!=null){
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[RecentService.updateP2pAndSmsBillsInRecent] :: Remind me later flow true");
				Recents existingRecord = recents.get(0);
				Date existingUpdatedAt = existingRecord.getUpdatedAt();
				existingRecord.setRemindLaterDate(new Date(reminderCDC.getAfter().getRemindLaterDate().getValue()));
				existingRecord.setUpdatedAt(new Date());
				ttl = TTLUtils.getTTL(existingRecord.getKey().getService(), existingRecord.getTxnTime(), existingRecord.getCreatedAt());
				if(existingRecord.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
					ttl = TTLUtils.getMidnightTTL();
				if (ttl < 1) ttl = 1;
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[RecentService.updateP2pAndSmsBillsInRecent] :: Remind me later flow true, updating remind me later {} for customerid {} , service {} ,recharge number {}",existingRecord.getRemindLaterDate(),existingRecord.getKey().getCustomerId(),existingRecord.getKey().getService(),existingRecord.getKey().getRechargeNumber());
				recentDataToKafkaService.setRecentConsumerSource(existingRecord, Constants.CDC_REMINDER_CONSUMER+"REMIND_LATER");
				isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord, existingUpdatedAt, ttl);
				return isUpdated;
			}

			if (RecentUtils.isWhitelistedCustIdForMobileNonRUPersist(reminderCDC.getAfter().getCustomerId().getValue(), serviceConfig) && Objects.nonNull(reminderCDC.getAfter().getPaytype()) && Constants.CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(reminderCDC.getAfter().getPaytype().getValue()) && Objects.nonNull(reminderCDC.getAfter().getService()) && Constants.ServiceTypeConstants.MOBILE.equalsIgnoreCase(reminderCDC.getAfter().getService().getValue())) {
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "updateP2pAndSmsBillsInRecent :: handling for different plan buckets {}", reminderCDC.getAfter().getCustomerId().getValue());
				isUpdated = handleForDifferentPlanBuckets(reminderCDC, recents, txnExists, finalRecent);
				isCacheToBeCleaned=true;
			} else {
				for (Recents recent : recents) {
					if ((recent.getEventSource() == null && !isNotTxnRecord(recent))) {
						txnExists = true;
					}
				}

				if (!Boolean.TRUE.equals(txnExists) || (serviceConfig.getSMSBillUpdateServices().contains(recents.get(0).getKey().getService()) && !Constants.SERVICE_MOBILE.equalsIgnoreCase(recents.get(0).getKey().getService()) )|| (serviceConfig.getSMSBillUpdateServices().contains(recents.get(0).getKey().getService()) && Constants.SERVICE_MOBILE.equalsIgnoreCase(recents.get(0).getKey().getService()) && RecentUtils.isWhitelistedCustIdForMobileNonRUPersist(reminderCDC.getAfter().getCustomerId().getValue(), serviceConfig))) {
					recents.get(0).getKey().setPlanBucket("");

					List<Recents> existingRecords = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(recents.get(0).getKey().getCustomerId(), recents.get(0).getKey().getService(), recents.get(0).getKey().getRechargeNumber(), recents.get(0).getKey().getOperator(), recents.get(0).getKey().getPlanBucket());
					Recents existingRecord = null;
					if (!existingRecords.isEmpty()) {
						existingRecord = existingRecords.get(0);
					}
					if (Objects.nonNull(existingRecord)) {
						Date existingUpdatedAt = existingRecord.getUpdatedAt();
						if (reminderCDC.isRecoveryPacket()
								&& Objects.nonNull(existingUpdatedAt)
								&& Objects.nonNull(reminderCDC.getAfter().getUpdatedAt())) {
							if (reminderCDC.getAfter().getUpdatedAt().getValue() < existingUpdatedAt.getTime()) {
								logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "ignoring recovery topic as data in outdated {}", reminderCDC);
								metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, OUTDATED_EVENT);
								metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, DEFAULT_UPDATED);

								return true;
							}
						}
						boolean isSkippedDueToExistingRURecord = isSkippedDueToExistingRURecord(existingRecord, reminderCDC);
						if (isSkippedDueToExistingRURecord) {
							logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "ignoring record as existing record is RU record, existing: {}, payload: {}", recents, reminderCDC);
							metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, DEFAULT_UPDATED);
							metricsHelper.recordSuccessRateForMultipleTags(Constants.MetricConstants.MESSAGE_SKIPPED, createTagsForRecentsRecordMetric(Constants.MetricConstants.MESSAGE_SKIPPED_TYPE, existingRecord));
							return true;
						}

					if (reminderCDC.getAfter().getDueDate() != null && reminderCDC.getAfter().getDueDate().getValue()!=null) {
						Boolean isNewBillIdentified = isNewBillIdentified(existingRecord.getDueDate(), new Date(reminderCDC.getAfter().getDueDate().getValue()), existingRecord);
						existingRecord.setIsNewBillIdentified(newBillIdentifiedUpdateForAllowedUser(isNewBillIdentified, existingRecord.getKey().getCustomerId()));
					}

					prepareFinalRecentsForP2pAndSms(reminderCDC, existingRecord, txnExists);
					if (Objects.isNull(existingRecord.getCreatedAt()))
						existingRecord.setCreatedAt(new Date());
					if(existingRecord.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
						ttl = TTLUtils.getMidnightTTL();
					else
						ttl = TTLUtils.getTTL(existingRecord.getKey().getService(), existingRecord.getTxnTime(), existingRecord.getCreatedAt());

					if(RecentUtils.checkExhaustEvent(reminderCDC)){
						ttl = TTLExhaustEventUtil.getTTL(existingRecord.getKey().getService(),existingRecord.getCreatedAt());
					}

					if (ttl < 1) ttl = 1;
					recentDataToKafkaService.setRecentConsumerSource(existingRecord, Constants.CDC_REMINDER_CONSUMER);
					isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord, existingUpdatedAt, ttl);
					if (reminderCDC.isRecoveryPacket() && isUpdated) {
						metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, UPDATED);
					}
				}else{
					prepareFinalRecentsForP2pAndSms(reminderCDC, finalRecent, txnExists);
					finalRecent.setIsNewBillIdentified(newBillIdentifiedUpdateForAllowedUser(Boolean.TRUE, finalRecent.getKey().getCustomerId()));
					if (Objects.isNull(finalRecent.getCreatedAt()))
						finalRecent.setCreatedAt(new Date());

					if(finalRecent.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
						ttl = TTLUtils.getMidnightTTL();
					else
						ttl = TTLUtils.getTTL(finalRecent.getKey().getService(), finalRecent.getTxnTime(), finalRecent.getCreatedAt());


					if(RecentUtils.checkExhaustEvent(reminderCDC)){
						ttl = TTLExhaustEventUtil.getTTL(finalRecent.getKey().getService(), finalRecent.getCreatedAt());
					}

					if (ttl < 1) ttl = 1;
					recentDataToKafkaService.setRecentConsumerSource(finalRecent, Constants.CDC_REMINDER_CONSUMER);
					logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[RecentService.updateP2pAndSmsBillsInRecent] : Final recent being updated - {}",finalRecent);
					isUpdated = recentsRepository.updateRecentWhenNoExistingData(finalRecent, ttl);
					if(reminderCDC.isRecoveryPacket() && isUpdated){
						metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, UPDATED);
					}
				}
				isCacheToBeCleaned = true;
			}else{
				isUpdated=true;
				if(reminderCDC.isRecoveryPacket()){
					metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, DEFAULT_UPDATED);
				}

				}
				//already which get saved with '' and got RU
				//if both planbuckets match then update otherwise existing by taking data of previous and delete previous one

			}
		} else {
			prepareFinalRecentsForP2pAndSms(reminderCDC, finalRecent, Boolean.FALSE);
			finalRecent.setIsNewBillIdentified(newBillIdentifiedUpdateForAllowedUser(Boolean.TRUE, finalRecent.getKey().getCustomerId()));
			if (Objects.isNull(finalRecent.getCreatedAt()))
				finalRecent.setCreatedAt(new Date());
			if(finalRecent.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
				ttl = TTLUtils.getMidnightTTL();
			else
				ttl = TTLUtils.getTTL(finalRecent.getKey().getService(), finalRecent.getTxnTime(), finalRecent.getCreatedAt());

			if(RecentUtils.checkExhaustEvent(reminderCDC)){
				ttl = TTLExhaustEventUtil.getTTL(finalRecent.getKey().getService(), finalRecent.getCreatedAt());
			}

			if (ttl < 1) ttl = 1;
			recentDataToKafkaService.setRecentConsumerSource(finalRecent, Constants.CDC_REMINDER_CONSUMER);
			isUpdated = recentsRepository.updateRecentWhenNoExistingData(finalRecent, ttl);
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "updateP2pAndSmsBillsInRecent :: updating new record for non ru {}",isUpdated);
			if (reminderCDC.isRecoveryPacket() && isUpdated) {
				metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, UPDATED);
			}
			isCacheToBeCleaned = true;
		}
		if (isCacheToBeCleaned && reminderCDC.getAfter() != null && reminderCDC.getAfter().getCustomerId() != null) {
			List<Long> customers = new ArrayList<>();
			customers.add(reminderCDC.getAfter().getCustomerId().getValue());
			cleanCache(customers);
		}
		logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC),"[RecentService.updateP2pAndSmsBillsInRecent] : isUpdated {}", isUpdated);
		return isUpdated;
	}

	private boolean handleForDifferentPlanBuckets(ReminderCDC reminderCDC, List<Recents> recents, boolean txnExists, Recents finalRecent) throws RecentDataToKafkaException {
		boolean isUpdated = false;
		Integer ttl;
		boolean isRecentFound = false;
		boolean isValidationBlock=false;

		metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, MOBILE_PREPAID_RECORD);
		String cdcPlanBucket = RecentUtils.getPlanBucketForCDCPayload(reminderCDC);
		if(RecentUtils.checkForDataExhaustPlanInReminderCDC(reminderCDC))
			cdcPlanBucket = DATA_EXHAUST_PLAN_BUCKET;

		List<Recents> existingRecordWithCDCPlanBuckets = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(recents.get(0).getKey().getCustomerId(), recents.get(0).getKey().getService(), recents.get(0).getKey().getRechargeNumber(), recents.get(0).getKey().getOperator(), cdcPlanBucket);

		for (Recents recent : recents) {
            txnExists = recent.getEventSource() == null && !isNotTxnRecord(recent);


			if (!Boolean.TRUE.equals(txnExists) || ((serviceConfig.getSMSBillUpdateServices().contains(recents.get(0).getKey().getService())) && RecentUtils.isWhitelistedCustIdForMobileNonRUPersist(reminderCDC.getAfter().getCustomerId().getValue(), serviceConfig)) ) {
				if (isRecentFound)
					continue;
				List<Recents> existingRecords = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(recent.getKey().getCustomerId(), recent.getKey().getService(), recent.getKey().getRechargeNumber(), recent.getKey().getOperator(), recent.getKey().getPlanBucket());

				Recents existingRecord = null;
				if (!existingRecords.isEmpty()) {
					existingRecord = existingRecords.get(0);
				}
				if (Objects.nonNull(existingRecord)) {

					if (cdcPlanBucket.equalsIgnoreCase(existingRecord.getKey().getPlanBucket())) {
						metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, ALREADY_NON_EMPTY_PLAN_BUCKET);

						if(txnExists && Constants.EVENTSOURCE_VALIDATION.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue()) && serviceConfig.getIsValidationBlock() ){
							metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, VALIDATION_BLOCK_FOR_CDC);
							isValidationBlock=true;
							break;
						}

						Date existingUpdatedAt = existingRecord.getUpdatedAt();
						if (reminderCDC.isRecoveryPacket()
								&& Objects.nonNull(existingUpdatedAt)
								&& Objects.nonNull(reminderCDC.getAfter().getUpdatedAt())) {
							if (reminderCDC.getAfter().getUpdatedAt().getValue() < existingUpdatedAt.getTime()) {
								logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "ignoring recovery topic as data in outdated {}", reminderCDC);
								metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, OUTDATED_EVENT);
								return true;
							}
						}
						boolean isSkippedDueToExistingRURecord = isSkippedDueToExistingRURecord(existingRecord, reminderCDC);
						if (isSkippedDueToExistingRURecord) {
							logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "ignoring record as existing record is RU record, existing: {}, payload: {}", recents, reminderCDC);
							metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, DEFAULT_UPDATED);
							metricsHelper.recordSuccessRateForMultipleTags(Constants.MetricConstants.MESSAGE_SKIPPED, createTagsForRecentsRecordMetric(Constants.MetricConstants.MESSAGE_SKIPPED_TYPE, existingRecord));
							return true;
						}

						prepareFinalRecentsForP2pAndSms(reminderCDC, existingRecord, txnExists);
						if (Objects.isNull(existingRecord.getCreatedAt()))
							existingRecord.setCreatedAt(new Date());
						if(existingRecord.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
							ttl = TTLUtils.getMidnightTTL();
						else
							ttl = TTLUtils.getTTL(existingRecord.getKey().getService(), existingRecord.getTxnTime(), existingRecord.getCreatedAt());
						if (ttl < 1) ttl = 1;
						recentDataToKafkaService.setRecentConsumerSource(existingRecord, Constants.CDC_REMINDER_CONSUMER);
						isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord, existingUpdatedAt, ttl);
						logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "handleForDifferentPlanBuckets, updateRecentWhenDataAlreadyExist, existingRecord - {}, isUpdated - {}, ttl - {}", existingRecord, isUpdated, ttl);
						if (isUpdated) {
							isRecentFound = true;

						}

					} else if (StringUtils.equals("", existingRecord.getKey().getPlanBucket()) && RecentUtils.getPlanBucket(existingRecord).equalsIgnoreCase(cdcPlanBucket)) {
						metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, EMPTY_PLAN_BUCKET);
						if(txnExists && Constants.EVENTSOURCE_VALIDATION.equalsIgnoreCase(reminderCDC.getAfter().getEventSource().getValue()) && serviceConfig.getIsValidationBlock() ){
							metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, VALIDATION_BLOCK_FOR_CDC);
							isValidationBlock=true;
							break;
						}
						Recents existingRecordWithCDCPlanBucket = null;
						if (!existingRecordWithCDCPlanBuckets.isEmpty()) {
							existingRecordWithCDCPlanBucket = existingRecordWithCDCPlanBuckets.get(0);
							metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, NON_EMPTY_PLAN_BUCKET_RECORDS_FOUND);
							if (existingRecordWithCDCPlanBucket.getEventSource() == null && !isNotTxnRecord(existingRecordWithCDCPlanBucket)) {
								txnExists = true;
							}

							Date existingRecordWithCDCPlanBucketUpdatedAt = existingRecordWithCDCPlanBucket.getUpdatedAt();

							if (reminderCDC.isRecoveryPacket()
									&& Objects.nonNull(existingRecordWithCDCPlanBucketUpdatedAt)
									&& Objects.nonNull(reminderCDC.getAfter().getUpdatedAt())) {
								if (reminderCDC.getAfter().getUpdatedAt().getValue() < existingRecordWithCDCPlanBucketUpdatedAt.getTime()) {
									logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "ignoring recovery topic as data in outdated {}", reminderCDC);
									metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, OUTDATED_EVENT);
									return true;
								}
							}
							boolean isSkippedDueToExistingRURecord = isSkippedDueToExistingRURecord(existingRecordWithCDCPlanBucket, reminderCDC);
							if (isSkippedDueToExistingRURecord) {
								logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "ignoring record as existing record is RU record, existing: {}, payload: {}", recents, reminderCDC);
								metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, DEFAULT_UPDATED);
								metricsHelper.recordSuccessRateForMultipleTags(Constants.MetricConstants.MESSAGE_SKIPPED, createTagsForRecentsRecordMetric(Constants.MetricConstants.MESSAGE_SKIPPED_TYPE, existingRecordWithCDCPlanBucket));
								return true;
							}

							prepareFinalRecentsForP2pAndSms(reminderCDC, existingRecordWithCDCPlanBucket, txnExists);
							if (Objects.isNull(existingRecordWithCDCPlanBucket.getCreatedAt()))
								existingRecordWithCDCPlanBucket.setCreatedAt(new Date());
							if(existingRecord.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
								ttl = TTLUtils.getMidnightTTL();
							else
								ttl = TTLUtils.getTTL(existingRecordWithCDCPlanBucket.getKey().getService(), existingRecordWithCDCPlanBucket.getTxnTime(), existingRecordWithCDCPlanBucket.getCreatedAt());
							if (ttl < 1) ttl = 1;
							recentDataToKafkaService.setRecentConsumerSource(existingRecordWithCDCPlanBucket, Constants.CDC_REMINDER_CONSUMER);

							isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecordWithCDCPlanBucket, existingRecordWithCDCPlanBucketUpdatedAt, ttl);
							logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "handleForDifferentPlanBuckets, updateRecentWhenDataAlreadyExist, existingRecordWithCDCPlanBucket - {}, isUpdated - {}, ttl - {}", existingRecordWithCDCPlanBucket, isUpdated, ttl);

							if (isUpdated) {
								isRecentFound = true;
								metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, DELETING_EMPTY_PLAN_BUCKET_RECORD);
								recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(existingRecord, existingRecord.getKey().getCustomerId(), existingRecord.getKey().getService(), existingRecord.getKey().getRechargeNumber(), existingRecord.getKey().getOperator(), "");

							}

						} else {
							Date existingUpdatedAt = existingRecord.getUpdatedAt();

							if (reminderCDC.isRecoveryPacket()
									&& Objects.nonNull(existingUpdatedAt)
									&& Objects.nonNull(reminderCDC.getAfter().getUpdatedAt())) {
								if (reminderCDC.getAfter().getUpdatedAt().getValue() < existingUpdatedAt.getTime()) {
									logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "ignoring recovery topic as data in outdated {}", reminderCDC);
									metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, OUTDATED_EVENT);
									return true;
								}
							}
							boolean isSkippedDueToExistingRURecord = isSkippedDueToExistingRURecord(existingRecord, reminderCDC);
							if (isSkippedDueToExistingRURecord) {
								logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "ignoring record as existing record is RU record, existing: {}, payload: {}", recents, reminderCDC);
								metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, DEFAULT_UPDATED);
								metricsHelper.recordSuccessRateForMultipleTags(Constants.MetricConstants.MESSAGE_SKIPPED, createTagsForRecentsRecordMetric(Constants.MetricConstants.MESSAGE_SKIPPED_TYPE, existingRecord));
								return true;
							}

							prepareFinalRecentsForP2pAndSms(reminderCDC, existingRecord, txnExists);
							if (Objects.isNull(existingRecord.getCreatedAt()))
								existingRecord.setCreatedAt(new Date());
							if(existingRecord.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
								ttl = TTLUtils.getMidnightTTL();
							else
								ttl = TTLUtils.getTTL(existingRecord.getKey().getService(), existingRecord.getTxnTime(), existingRecord.getCreatedAt());
							if (ttl < 1) ttl = 1;
							recentDataToKafkaService.setRecentConsumerSource(existingRecord, Constants.CDC_REMINDER_CONSUMER);
							existingRecord.getKey().setPlanBucket(cdcPlanBucket);

							isUpdated = recentsRepository.updateRecentWhenNoExistingData(existingRecord, ttl);
							logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "handleForDifferentPlanBuckets, updateRecentWhenNoExistingData, existingRecord - {}, isUpdated - {}, ttl - {}", existingRecord, isUpdated, ttl);
							metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, NEW_RECORD_WITH_NON_EMPTY_PLAN_BUCKET);
							isRecentFound = true;
							recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(existingRecord, existingRecord.getKey().getCustomerId(), existingRecord.getKey().getService(), existingRecord.getKey().getRechargeNumber(), existingRecord.getKey().getOperator(), "");
							metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, DELETING_EMPTY_PLAN_BUCKET_RECORD);

						}


					}


				}

			}

		}
		if (!isRecentFound ) {
			if(isValidationBlock){
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC),"updateP2pAndSmsBillsInRecent :: default updating in case of validation block {}",true);
				return true;
			}
			metricsHelper.recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, NON_EXISTING_RECORD);
			prepareFinalRecentsForP2pAndSms(reminderCDC, finalRecent, false);
			if (Objects.isNull(finalRecent.getCreatedAt())) finalRecent.setCreatedAt(new Date());
			if(finalRecent.getKey().getPlanBucket().equals(DATA_EXHAUST_PLAN_BUCKET))
				ttl = TTLUtils.getMidnightTTL();
			else
				ttl = TTLUtils.getTTL(finalRecent.getKey().getService(), finalRecent.getTxnTime(), finalRecent.getCreatedAt());
			if (ttl < 1) ttl = 1;
			recentDataToKafkaService.setRecentConsumerSource(finalRecent, Constants.CDC_REMINDER_CONSUMER);
			isUpdated = recentsRepository.updateRecentWhenNoExistingData(finalRecent, ttl);
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "handleForDifferentPlanBuckets, updateRecentWhenNoExistingData, finalRecent - {}, isUpdated - {}, ttl - {}", finalRecent, isUpdated, ttl);
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "updateP2pAndSmsBillsInRecent :: updating new record for non ru mobile whitelisted {}",isUpdated);


		}
		 /*else {
			isUpdated = true;
		}*/
		return isUpdated;
	}



	private Boolean newBillIdentifiedUpdateForAllowedUser(Boolean isNewBillIdentified, Long custId){
		if(Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean(Constants.SERVICE_CONFIG_CONSTANTS.NEW_BILL_IDENTIFIED_UPDATE_FOR_ALL_USER))){
			return isNewBillIdentified;
		}
		if(Objects.nonNull(FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.NEW_BILL_IDENTIFIED_UPDATE_USER_LIST))
				&& FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.NEW_BILL_IDENTIFIED_UPDATE_USER_LIST).contains(String.valueOf(custId))){
			return isNewBillIdentified;
		}
		return null;
	}

	private List<String> createTagsForRecentsRecordMetric(String tagName, Recents existingRecord) {
		List<String> tags = new ArrayList<>();
		tags.add(Constants.MetricConstants.API_KEY + ":" + tagName);
		if(existingRecord == null)
			return tags;
		String service = existingRecord.getKey().getService();
		if(service != null) {
			tags.add(Constants.MetricConstants.CATEGORY_METRIC_KEY + ":" + service);
		}
		if(existingRecord.getPayType() != null) {
			tags.add(Constants.MetricConstants.PAYTYPE_METRIC_KEY + ":" + existingRecord.getPayType());
		}
 		return tags;
	}

	private boolean isSkippedDueToExistingRURecord(Recents existingRecord, ReminderCDC reminderCDC) {
		if(reminderCDC == null || !reminderCDC.isRecoveryPacket() || existingRecord == null || reminderCDC.getAfter() == null || Objects.isNull(existingRecord.getEventSource())){
			return false;
		}
		if (existingRecord.getUpdatedAt() != null && reminderCDC.getAfter().getUpdatedAt() != null) {
			// return false if absolute difference between existing record and new record is greater than 60 minute
			if (Math.abs(existingRecord.getUpdatedAt().getTime() - reminderCDC.getAfter().getUpdatedAt().getValue())
					> serviceConfig.getTimeDiffThresholdBetweenRecentsAndCDC() * 60 * 1000) {
				return false;
			}
		}

		if(reminderCDC.getAfter().getService() != null && reminderCDC.getAfter().getPaytype() != null
				&& serviceConfig.getPrepaidServicesEligibleForNonRuValidation().contains(reminderCDC.getAfter().getService().getValue())
				&& reminderCDC.getAfter().getPaytype().getValue().equalsIgnoreCase(Constants.PREPAID_PAYTYPE)){
			//mobile prepaid case
			//RU record - if existing record if existing record eventSource is not equal to SMS and Validation
			if (!existingRecord.getEventSource().equalsIgnoreCase(Constants.EVENTSOURCE_SMS) &&
					!existingRecord.getEventSource().equalsIgnoreCase(Constants.EVENTSOURCE_VALIDATION)) {
				return true;
			}
		} else {
			//non mobile-prepaid case or postpaid cases
			//RU record - if existing record eventSource is not equal to SMS
			if (!StringUtils.equalsAnyIgnoreCase(existingRecord.getEventSource(),Constants.EVENT_SOURCE.SMS,Constants.EVENT_SOURCE.CSV)) {
				return true;
			}
		}
		return false;
	}

	private void prepareFinalRecentsForP2pAndSms(ReminderCDC reminderCDC, Recents recents, Boolean txnExists){
		if (reminderCDC.getAfter().getEventSource().getValue().equalsIgnoreCase(Constants.EVENTSOURCE_SMS) ||
				reminderCDC.getAfter().getEventSource().getValue().equalsIgnoreCase(Constants.EVENTSOURCE_VALIDATION) || reminderCDC.getAfter().getEventSource().getValue().equalsIgnoreCase(Constants.EVENT_SOURCE.CSV))
			prepareSmsDataForRecents(recents, reminderCDC, txnExists);
		if (reminderCDC.getAfter().getEventSource().getValue().equalsIgnoreCase(Constants.EVENTSOURCE_P2P))
			prepareP2pDataForRecents(recents, reminderCDC);

	}


	private void prepareP2pDataForRecents(Recents recent, ReminderCDC reminderCDC) {
		this.getCommonfieldsForSmsAndP2p(recent,reminderCDC);
		this.prepareRechargeNumbersAndAdditionalInfo(recent,reminderCDC);
		recent.setEventSource(Constants.EVENTSOURCE_P2P);
		if (reminderCDC.getAfter().getDueDate() != null && reminderCDC.getAfter().getDueDate().getValue()!=null)
			recent.setDueDate(new Date(reminderCDC.getAfter().getDueDate().getValue()));
		if (reminderCDC.getAfter().getOrderId() != null)
			recent.setOrderIdP2p(reminderCDC.getAfter().getOrderId().getValue());
		if (reminderCDC.getAfter().getPaymentDate() != null && reminderCDC.getAfter().getPaymentDate().getValue()!=null)
			recent.setTxnTime(new Date(reminderCDC.getAfter().getPaymentDate().getValue()));
		if (reminderCDC.getAfter().getCreatedAt() != null && reminderCDC.getAfter().getCreatedAt().getValue()!=null)
			recent.setCreatedAt(new Date(reminderCDC.getAfter().getCreatedAt().getValue()));
		if (reminderCDC.getAfter().getTxnAmount() != null)
			recent.setTxnAmount(reminderCDC.getAfter().getTxnAmount().getValue());
		if (reminderCDC.getAfter().getDueAmount() != null)
			recent.setDueAmount(reminderCDC.getAfter().getDueAmount().getValue());
		if (recent.getDueAmount()!=null && recent.getDueAmount()>0)
			recent.setIsMarkAsPaid(false);
		if (reminderCDC.getAfter().getCustomerOtherInfo() != null && reminderCDC.getAfter().getCustomerOtherInfo().getValue()!=null) {
			JSONObject jsonObject = new JSONObject(reminderCDC.getAfter().getCustomerOtherInfo().getValue());
			if (jsonObject.has(Constants.TAG)) {
				recent.setTag(jsonObject.getString(Constants.TAG));
			}
			if (jsonObject.has(Constants.NICKNAME)) {
				recent.setNickName(jsonObject.getString(Constants.NICKNAME));
			}
			if (jsonObject.has(Constants.IS_DEFAULT_AMOUNT)) {
				recent.setExtra(jsonObject.put(Constants.IS_DEFAULT_AMOUNT, jsonObject.getBoolean(Constants.IS_DEFAULT_AMOUNT)).toString());
				metricsHelper.incrementCount(Constants.CDC_REMINDER_CONSUMER, "source:p2p","status:"+Constants.MetricConstants.SUCCESS);
			}
		}
	}

	private void prepareSmsDataForRecents(Recents recent, ReminderCDC reminderCDC, Boolean txnExists) {
		logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "prepareSmsDataForRecents reminderCDC {}",reminderCDC);
		logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "prepareSmsDataForRecents recent {}",recent);
		this.getCommonfieldsForSmsAndP2p(recent,reminderCDC);
		this.getCCDetails(recent,reminderCDC);
		boolean billPaidEvent=false;
		Map<String,String> extras = null;
		if (reminderCDC.getAfter().getExtra() != null && reminderCDC.getAfter().getExtra().getValue() != null) {
			extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
			if(Objects.nonNull(extras) && extras.containsKey(Constants.BILL_PAID_IDENTIFIER_KEY) && StringUtils.equalsIgnoreCase(extras.get(Constants.BILL_PAID_IDENTIFIER_KEY),Constants.BILL_PAID_IDENTIFIER_VALUE)){
				billPaidEvent = true;
			}
		}
		if(Boolean.FALSE.equals(txnExists)) {
			if (isUpdatedSourceValidation(reminderCDC)) {
				recent.setIsValidation(true);
			} else {
				recent.setIsSmsParsed(true);
			}
			if (reminderCDC.getAfter().getEventSource().getValue().equalsIgnoreCase(Constants.EVENTSOURCE_VALIDATION)) {
				recent.setEventSource(Constants.EVENTSOURCE_VALIDATION);
			} else if(reminderCDC.getAfter().getEventSource().getValue().equalsIgnoreCase(Constants.EVENT_SOURCE.CSV)) {
				recent.setEventSource(Constants.EVENT_SOURCE.CSV);
			}
			else {
				recent.setEventSource(Constants.EVENTSOURCE_SMS);
			}
		}
		if (reminderCDC.getAfter().getDueDate() != null && reminderCDC.getAfter().getDueDate().getValue()!=null) {
			recent.setDueDate(new Date(reminderCDC.getAfter().getDueDate().getValue()));
		} else {
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "Partial bill received for customer Id: " + recent.getKey().getCustomerId());
			recent.setDueDate(null);
		}
		if (reminderCDC.getAfter().getCreateAt() != null && reminderCDC.getAfter().getCreateAt().getValue()!=null)
			recent.setCreatedAt(new Date(reminderCDC.getAfter().getCreateAt().getValue()));
		if (reminderCDC.getAfter().getPaymentDate() != null && reminderCDC.getAfter().getPaymentDate().getValue()!=null)
			recent.setTxnTime(new Date(reminderCDC.getAfter().getPaymentDate().getValue()));
		if (!billPaidEvent && reminderCDC.getAfter().getDueAmount() != null && reminderCDC.getAfter().getDueAmount().getValue()!=null)
			recent.setDueAmount(reminderCDC.getAfter().getDueAmount().getValue());
		else {
			if (reminderCDC.getAfter().getAmount() != null && reminderCDC.getAfter().getAmount().getValue()!=null) {
				boolean prepaidQualified = RecentUtils.isPrepaidQualified(reminderCDC.getAfter().getService().getValue(), reminderCDC.getAfter().getOperator().getValue(), extras);
				if (prepaidQualified) {
					recent.setTxnAmount(Math.abs(reminderCDC.getAfter().getAmount().getValue()));
					recent.setDueAmount(reminderCDC.getAfter().getAmount().getValue());
				} else if (billPaidEvent) {
					recent.setTxnAmount(Math.abs(reminderCDC.getAfter().getAmount().getValue()));
				} else {
					recent.setDueAmount(reminderCDC.getAfter().getAmount().getValue());
				}
			} else {
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "Partial bill received for customer Id: " + recent.getKey().getCustomerId());
				recent.setDueAmount(null);
			}
		}
		if (reminderCDC.getAfter().getRemindLaterDate() != null && reminderCDC.getAfter().getRemindLaterDate().getValue()!=null)
			recent.setRemindLaterDate(new Date(reminderCDC.getAfter().getRemindLaterDate().getValue()));
		if (recent.getDueAmount()!=null && recent.getDueAmount()>0){
			recent.setIsMarkAsPaid(false);
			recent.setMarkAsPaidSource(null);
			recent.setMarkAsPaidTime(null);
			recent.setMarkAsPaidAmount(0.0);
			recent.setNewBillUpdatedAt(new Date());
			recent.setBillUpdateTime(new Date());
		}

		if (reminderCDC.getAfter().getCustomerOtherInfo() != null && reminderCDC.getAfter().getCustomerOtherInfo().getValue() != null) {
			JSONObject jsonObject = new JSONObject(reminderCDC.getAfter().getCustomerOtherInfo().getValue());
			if (jsonObject.has(Constants.IS_DEFAULT_AMOUNT)) {
				recent.setExtra(jsonObject.put(Constants.IS_DEFAULT_AMOUNT, jsonObject.getBoolean(Constants.IS_DEFAULT_AMOUNT)).toString());
				metricsHelper.incrementCount(Constants.CDC_REMINDER_CONSUMER, "source:sms","status:"+Constants.MetricConstants.SUCCESS);
			}
			if (jsonObject.has(Constants.CURRENT_MIN_BILL_AMOUNT) && !jsonObject.isNull(Constants.CURRENT_MIN_BILL_AMOUNT)) {
				recent.setMinDueAmount(jsonObject.getDouble(Constants.CURRENT_MIN_BILL_AMOUNT));
			}
			else
				recent.setMinDueAmount(null);
		}
		if (reminderCDC.getAfter().getCircle() != null)
			recent.setCircle(reminderCDC.getAfter().getCircle().getValue());
		recent.setReminderStatus(reminderCDC.getAfter().getStatus() != null? reminderCDC.getAfter().getStatus().getValue(): null);
		if (reminderCDC.getAfter().getOldBillFetchDate() != null){
			SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			sdf.setLenient(false);
			try {
				recent.setOldBillFetchDate(sdf.parse(reminderCDC.getAfter().getOldBillFetchDate().getValue()));
			} catch (ParseException e) {
				logger.error(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "Error while parsing oldBillFetchDate for customer Id: " + recent.getKey().getCustomerId(), e);
			}
		}
		logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "prepareSmsDataForRecents, recents at end - {}", recent);
	}

	public void updateReminderCdcCircleAndProductId(List<Recents> recents, ReminderCDC reminderCDC) {
		if(Objects.isNull(reminderCDC.getAfter().getPaytype())) {
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "updateReminderCdcCircleAndProductId, payType is null");
			return;
		}
		String reminderCdcPayType = reminderCDC.getAfter().getPaytype().getValue();
		Optional<Recents> latestRecent = recents.stream().max(Comparator.comparing(Recents::getUpdatedAt));

		if(latestRecent.isPresent() && StringUtils.equalsIgnoreCase(reminderCdcPayType, latestRecent.get().getPayType())){
			String reminderCdcCircle = Objects.nonNull(reminderCDC.getAfter().getCircle()) ? reminderCDC.getAfter().getCircle().getValue() : "";
			String recentCircle = Objects.nonNull(latestRecent.get().getCircle()) ? latestRecent.get().getCircle() : "";
			boolean isCircleMisMatch = !recentCircle.equalsIgnoreCase(reminderCdcCircle);
			if(isCircleMisMatch){
				logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "updateReminderCdcCircleAndProductId:: updated circle and productId from latest recent. rechargeNumber: {}", reminderCDC.getAfter().getRechargeNumber());
				metricsHelper.incrementCount(Constants.MetricConstants.REMINDER_CDC_CIRCLE_UPDATED, "source:sms");
			}

			reminderCDC.getAfter().setProductId(new LongEntity(latestRecent.get().getProductId()));
			reminderCDC.getAfter().setCircle(new StringEntity(latestRecent.get().getCircle()));
		}
	}

	private void getCommonfieldsForSmsAndP2p(Recents recent, ReminderCDC reminderCDC){
		recent.getKey().setCustomerId(reminderCDC.getAfter().getCustomerId().getValue());
		recent.getKey().setOperator(reminderCDC.getAfter().getOperator().getValue());
		recent.getKey().setRechargeNumber(reminderCDC.getAfter().getRechargeNumber().getValue());
		recent.getKey().setService(reminderCDC.getAfter().getService().getValue());
		recent.getKey().setPlanBucket("");
		if (RecentUtils.isWhitelistedCustIdForMobileNonRUPersist(reminderCDC.getAfter().getCustomerId().getValue(),serviceConfig)&&Objects.nonNull(reminderCDC.getAfter().getPaytype())&&Constants.CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(reminderCDC.getAfter().getPaytype().getValue()) && Objects.nonNull(reminderCDC.getAfter().getService()) && Constants.ServiceTypeConstants.MOBILE.equalsIgnoreCase(reminderCDC.getAfter().getService().getValue())) {
			logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "getCommonfieldsForSmsAndP2p::setting planBucket {}",reminderCDC.getAfter().getCustomerId());
			recent.getKey().setPlanBucket(RecentUtils.getPlanBucketForCDCPayload(reminderCDC));
		}
		// for data exhaust cases
		if (reminderCDC.getAfter().getExtra() != null && reminderCDC.getAfter().getExtra().getValue() != null) {
			Map<String,String> extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
			if (extras != null && extras.containsKey(EXHAUSTED_DATE)){
				Date exhaustedDate = DateUtil.stringToDate(extras.get(EXHAUSTED_DATE), Constants.DATA_EXHAUST.EXHAUSTED_DATE_FORMAT);
				if(exhaustedDate != null && !exhaustedDate.after(DateUtil.getEndOfDayDate(new Date()))){
					recent.getKey().setPlanBucket(DATA_EXHAUST_PLAN_BUCKET);
				}
			}
		}
		if (reminderCDC.getAfter().getBillDate() != null && reminderCDC.getAfter().getBillDate().getValue()!=null)
			recent.setBillDate(new Date(reminderCDC.getAfter().getBillDate().getValue()));
		if (reminderCDC.getAfter().getProductId() != null)
			recent.setProductId(reminderCDC.getAfter().getProductId().getValue());
		if (reminderCDC.getAfter().getPaytype() != null)
			recent.setPayType(reminderCDC.getAfter().getPaytype().getValue());
		if (reminderCDC.getAfter().getNotificationStatus() != null)
			recent.setNotificationStatus(reminderCDC.getAfter().getNotificationStatus().getValue());


		if (reminderCDC.getAfter().getExtra() != null){
			mergeRecentsExtraWithReceivedExtraInfo(recent,reminderCDC.getAfter().getExtra().getValue());
		}

		else if (RecentUtils.checkIfPartialBillStateIsPresentInRecents(recent) && reminderCDC.getAfter().getService() != null && reminderCDC.getAfter().getService().getValue().equalsIgnoreCase(Constants.SERVICE_MOBILE)) { // resetting only partialBillState in extra
			resetPartialBillStateInExtra(recent);
		}
//		setExtrasInAdditiontoDataExhaust(recent,reminderCDC);
		recent.setUpdatedAt(new Date());
		recent.setBillUpdateTime(new Date());
		if (reminderCDC.getAfter().getReconId() != null)
			recent.setReconId(reminderCDC.getAfter().getReconId());
		logger.info(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "getCommonfieldsForSmsAndP2p, recents at end - {}", recent);
	}

	private void resetPartialBillStateInExtra(Recents recent) {
		try {
			if (recent.getExtra() != null) {
				JsonNode extraNode = JsonUtils.fromStringToJsonNode(recent.getExtra());
				logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "resetPartialBillState, recent extra before reset {}", extraNode);
				if (extraNode != null && extraNode.get("partialBillState") != null) {
					recent.setExtra(String.valueOf(((ObjectNode) extraNode).without("partialBillState")));
					logger.info(ServiceWrapperUtil.findServiceWrapper(recent), "resetPartialBillState, recent after reset {}", recent.getExtra());
				}
			}
		}catch (Exception ex){
			logger.error(ServiceWrapperUtil.findServiceWrapper(recent), "resetPartialBillState, exception occurred, ex message {}",ex.getMessage(),ex);
		}
	}

	private void prepareRechargeNumbersAndAdditionalInfo(Recents recent, ReminderCDC reminderCDC){
		if (reminderCDC.getAfter().getUserData() != null && reminderCDC.getAfter().getUserData().getValue() != null) {
			JSONObject jsonObject = new JSONObject(reminderCDC.getAfter().getUserData().getValue());
			if (jsonObject.has(Constants.RECHARGE_NUMBER_2)) {
				recent.setRechargeNumber2(jsonObject.getString(Constants.RECHARGE_NUMBER_2));
			}
			if (jsonObject.has(Constants.RECHARGE_NUMBER_3)) {
				recent.setRechargeNumber3(jsonObject.getString(Constants.RECHARGE_NUMBER_3));
			}
			if (jsonObject.has(Constants.RECHARGE_NUMBER_4)) {
				recent.setRechargeNumber4(jsonObject.getString(Constants.RECHARGE_NUMBER_4));
			}
			if (jsonObject.has(Constants.RECHARGE_NUMBER_5)) {
				recent.setRechargeNumber5(jsonObject.getString(Constants.RECHARGE_NUMBER_5));
			}
			if (jsonObject.has(Constants.RECHARGE_NUMBER_6)) {
				recent.setRechargeNumber6(jsonObject.getString(Constants.RECHARGE_NUMBER_6));
			}
		}
		if (reminderCDC.getAfter().getAdditionalInfo() != null)
			recent.setAdditionalInfo(reminderCDC.getAfter().getAdditionalInfo().getValue());
	}

	private void getCCDetails(Recents recent,ReminderCDC reminderCDC){
		if (reminderCDC.getAfter().getService().getValue().equalsIgnoreCase(Constants.FINANCIAL_SERVICES)) {
			JSONObject userData = new JSONObject();
			if (reminderCDC.getAfter().getCardNetwork() != null && reminderCDC.getAfter().getCardNetwork().getValue()!=null) {
				userData.put(Constants.CARD_NETWORK, reminderCDC.getAfter().getCardNetwork().getValue());
			}
			if (reminderCDC.getAfter().getBankName() != null && reminderCDC.getAfter().getBankName().getValue()!=null) {
				userData.put(Constants.BANK_NAME, reminderCDC.getAfter().getBankName().getValue());
			}
			recent.setUserData(userData.toString());
		}
	}

	private Recents buildBillerAccountRecentsForRent(BillerAccountKafkaModel billerAccountModel, ProductMin productDetails) {
		logger.info("buildBillerAccountRecentsForRent::billerAccount model  {}",billerAccountModel);
		Recents recent = new Recents();
		RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
		recentsPrimaryKey.setOperator(productDetails.getOperator());
		recentsPrimaryKey.setPlanBucket("");
		recentsPrimaryKey.setRechargeNumber(String.valueOf(billerAccountModel.getId()));
		recentsPrimaryKey.setCustomerId(billerAccountModel.getCustomerId());
		recentsPrimaryKey.setService(productDetails.getService());
		recent.setKey(recentsPrimaryKey);
		try {
			SimpleDateFormat sdf=new SimpleDateFormat(RECENTS_DATE_FORMAT);
			sdf.setLenient(false);
			sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
			Date createdAt=sdf.parse(billerAccountModel.getCreatedAt());
			if(billerAccountModel.getAccountStatus()==3){
				createdAt = getOldCreatedAtForOldBillers(billerAccountModel);
			}
			if(Objects.isNull(createdAt)){
				createdAt = new Date();
			}
			//createdAt = DateUtils.addHours(createdAt, 5);
			//createdAt = DateUtils.addMinutes(createdAt, 30);
			recent.setCreatedAt(createdAt);

		} catch (Exception e) {
			recent.setCreatedAt(new Date());
			logger.error("[RecentService.buildBillerAccountRecents] createdAt: Exception for customerId  and billerId {} {}, e=" , billerAccountModel.getCustomerId(),billerAccountModel.getId(),e);
		}

		try {
			SimpleDateFormat sdf1=new SimpleDateFormat(RECENTS_DATE_FORMAT);
			sdf1.setTimeZone(TimeZone.getTimeZone("GMT"));
			sdf1.setLenient(false);
			Date updatedAt=sdf1.parse(billerAccountModel.getUpdatedAt());
			//updatedAt = DateUtils.addHours(updatedAt, 5);
			//updatedAt = DateUtils.addMinutes(updatedAt, 30);
			recent.setUpdatedAt(updatedAt);
		} catch (Exception e) {
			recent.setUpdatedAt(new Date());
			logger.error("[RecentService.buildBillerAccountRecents] updatedAt: Exception for customerId {}" , billerAccountModel.getCustomerId(),billerAccountModel.getId());
		}

		recent.setProductId(productDetails.getProductId());
		recent.setRechargeNumber2(billerAccountModel.getIfscCode());
		recent.setRechargeNumber3(billerAccountModel.getBankAccountName());

		String phoneNumber=null;
		if(Objects.nonNull(billerAccountModel.getPhoneNumber())){
			phoneNumber=String.valueOf(billerAccountModel.getPhoneNumber());
		}
		recent.setRechargeNumber4(phoneNumber);
		recent.setRechargeNumber5(DecryptionUtil.getDecryptedText(billerAccountModel.getPanNumber()));
		recent.setRechargeNumber7(DecryptionUtil.getDecryptedText(billerAccountModel.getAccountId()));
		recent.setRechargeNumber8(billerAccountModel.getMaskAccountId());
		recent.setPayType(productDetails.getPayType());
		recent.setConsumerName(billerAccountModel.getName());
		recent.setRentTFData(RecentUtils.getRentTFData(billerAccountModel));
		recent.setIsNewBiller(true);
		recent.setEventSource("biller");
//		recent.setRentConsent(RENTCONSENT.valueOfString(billerAccountModel.getConsent()).getConstant());
		setRentConsentInRecents(recent,billerAccountModel);
		logger.info("buildBillerAccountRecentsForRent:: recent  {}",recent);
		return recent;
	}


	public void deleteValidRecord(BillerAccountKafkaModel billerAccountModel) {
		ProductMin productDetails = CVRProductCache.getInstance().getProductDetails(billerAccountModel.getProductId());
		if(Objects.isNull(productDetails)){
			logger.error("[RecentService.insertBillerAccountRecents] :: product details are not valid");
			return;
		}
		try {
			List<Recents> existingRecents = null;
			SimpleDateFormat sdf1=new SimpleDateFormat(RECENTS_DATE_FORMAT);
			sdf1.setTimeZone(TimeZone.getTimeZone("GMT"));
			sdf1.setLenient(false);
			if(productDetails.getService().equalsIgnoreCase(Constants.RENT_PAYMENT) || productDetails.getService().equalsIgnoreCase(Constants.TUITION_FEES) || productDetails.getService().equalsIgnoreCase(Constants.BUSINESS_PAYMENT)){
				existingRecents=recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(billerAccountModel.getCustomerId(), productDetails.getService(), String.valueOf(billerAccountModel.getId()),productDetails.getOperator());
			}

			if (!CollectionUtils.isEmpty(existingRecents)) {
				try {
					Date updated_at = sdf1.parse(billerAccountModel.getUpdatedAt());
					updated_at = DateUtils.addHours(updated_at, 5);
					updated_at = DateUtils.addMinutes(updated_at, 30);
					logger.info("deleteing date is {}", updated_at);
					for (Recents existingRecord : existingRecents) {
						if (Objects.nonNull(existingRecord.getUpdatedAt()) && existingRecord.getUpdatedAt().before(updated_at)) {
							logger.info("deleteValidRecord:: going to delete as updated of existing record is before biller record {} {} {} {} ", billerAccountModel.getCustomerId(), productDetails.getService(), String.valueOf(billerAccountModel.getId()), productDetails.getOperator());
							if (productDetails.getService().equalsIgnoreCase(Constants.RENT_PAYMENT) || productDetails.getService().equalsIgnoreCase(Constants.TUITION_FEES)) {
								Recents recents = new Recents();
								recents.setKey(existingRecord.getKey());
								recentDataToKafkaService.setRecentConsumerSource(recents,BILLER_ACCOUNT_CONSUMER);
								recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(recents,existingRecord.getKey().getCustomerId(), existingRecord.getKey().getService(), existingRecord.getKey().getRechargeNumber(), existingRecord.getKey().getOperator(), "");
							}

							metricsHelper.recordSuccessRate(BILLER_ACCOUNT, RECENT_DELETE_SUCCESS);
						}

					}
				}
				catch (Exception ex) {
					metricsHelper.recordSuccessRate(BILLER_ACCOUNT, RECENT_DELETE_FAIL);
					logger.error("RecentService.deleteValidRecord:: Exception while deducing skippability",ex);
				}

			}
		}
		catch(Exception ex){
			logger.error("RecentService.deleteValidRecord::Exception while finding record for deletion",ex);

		}

	}


	private Recents buildBillerAccountRecentsForTuitionFee(BillerAccountKafkaModel billerAccountModel, ProductMin productDetails) {

		Recents recent = new Recents();
		logger.info("buildBillerAccountRecentsForTuitionFee::billerAccount model  {}",billerAccountModel);
		RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
		recentsPrimaryKey.setOperator(productDetails.getOperator());
		recentsPrimaryKey.setPlanBucket("");
		recentsPrimaryKey.setRechargeNumber(String.valueOf(billerAccountModel.getId()));
		recentsPrimaryKey.setCustomerId(billerAccountModel.getCustomerId());
		recentsPrimaryKey.setService(productDetails.getService());
		recent.setKey(recentsPrimaryKey);

		try {
			SimpleDateFormat sdf=new SimpleDateFormat(RECENTS_DATE_FORMAT);
			sdf.setLenient(false);
			sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
			Date createdAt=sdf.parse(billerAccountModel.getCreatedAt());
			//createdAt = DateUtils.addHours(createdAt, 5);
			//createdAt = DateUtils.addMinutes(createdAt, 30);
			recent.setCreatedAt(createdAt);

		} catch (Exception e) {
			recent.setCreatedAt(new Date());
			logger.error("[RecentService.buildBillerAccountRecents] createdAt: Exception for customerId  and billerId {} {}" , billerAccountModel.getCustomerId(),billerAccountModel.getId());
		}

		try {
			SimpleDateFormat sdf1=new SimpleDateFormat(RECENTS_DATE_FORMAT);
			sdf1.setTimeZone(TimeZone.getTimeZone("GMT"));
			sdf1.setLenient(false);
			Date updatedAt=sdf1.parse(billerAccountModel.getUpdatedAt());
			//updatedAt = DateUtils.addHours(updatedAt, 5);
			//updatedAt = DateUtils.addMinutes(updatedAt, 30);
			recent.setUpdatedAt(updatedAt);
		} catch (Exception e) {
			recent.setUpdatedAt(new Date());
			logger.error("[RecentService.buildBillerAccountRecents] updatedAt: Exception for customerId {} {}" , billerAccountModel.getCustomerId(),billerAccountModel.getId());
		}

		recent.setProductId(productDetails.getProductId());
		recent.setRechargeNumber2(billerAccountModel.getIfscCode());
		recent.setRechargeNumber3(DecryptionUtil.getDecryptedText(billerAccountModel.getAccountId()));
		String phoneNumber=null;
		if(Objects.nonNull(billerAccountModel.getPhoneNumber())){
			phoneNumber=String.valueOf(billerAccountModel.getPhoneNumber());
		}
		recent.setRechargeNumber4(phoneNumber);
		recent.setRechargeNumber5(DecryptionUtil.getDecryptedText(billerAccountModel.getPanNumber()));
		recent.setRechargeNumber7(billerAccountModel.getName());
		recent.setRechargeNumber8(billerAccountModel.getMaskAccountId());
		recent.setPayType(productDetails.getPayType());
		recent.setConsumerName(billerAccountModel.getName());
		recent.setRentTFData(RecentUtils.getRentTFData(billerAccountModel));
		recent.setIsNewBiller(true);
		recent.setEventSource(BILLER);
		return recent;
	}

	private void validateRentConsentValue(String consent){
		try{
			RENTCONSENT.valueOf(consent);
		}catch (Exception e){
			logger.error("validateRentConsentValue:: Invalid consent value {}",consent);
			metricsHelper.recordSuccessRate(BILLER_ACCOUNT_CONSUMER, INVALID_CONSENT_VALUE);
		}
	}


	@Retryable(value = {BillerAccountListenerException.class},
			maxAttemptsExpression = "${db.writes.max.attempts}")
	public void insertBillerAccountRecents(BillerAccountKafkaModel billerAccountModel) throws BillerAccountListenerException {
		ProductMin productDetails = CVRProductCache.getInstance().getProductDetails(billerAccountModel.getProductId());
		if(Objects.isNull(productDetails)){
			logger.error("[RecentService.insertBillerAccountRecents] :: product details are not valid");
			return;
		}
		try {
            if (com.paytm.saga.enums.Service.RENT_PAYMENT.value.equalsIgnoreCase(productDetails.getService()) || com.paytm.saga.enums.Service.BUSINESS_PAYMENT.value.equalsIgnoreCase(productDetails.getService())){
				validateRentConsentValue(billerAccountModel.getConsent());
				insertionForRent(productDetails,billerAccountModel);
			}
			else if(com.paytm.saga.enums.Service.TUITION_FEES.value.equalsIgnoreCase(productDetails.getService())){
				insertionForTuitionFee(productDetails,billerAccountModel);
			}


		} catch (Exception e) {
			logger.error("[RecentService.insertBillerAccountRecents] :: Retrying as insering  recents failed for customerId {}, rechargeNumber {} with exception {}", billerAccountModel.getCustomerId(), billerAccountModel.getAccountId(),e);
			throw new BillerAccountListenerException();
		}
	}

	public void insertionForRent(ProductMin productDetails,BillerAccountKafkaModel billerAccountModel) throws BillerAccountListenerException, RecentDataToKafkaException {
		List<Recents> existingRecents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(billerAccountModel.getCustomerId(), productDetails.getService(), String.valueOf(billerAccountModel.getId()), productDetails.getOperator());
		logger.info("insertionForRent :: existing recents for rent {}",existingRecents);
		Boolean isUpdated;
		Integer ttl;
		if (!CollectionUtils.isEmpty(existingRecents)) {
			Recents existingRecord = existingRecents.get(0);
			existingRecord.setProductId(productDetails.getProductId());
			existingRecord.setRechargeNumber2(billerAccountModel.getIfscCode());
			existingRecord.setRechargeNumber3(billerAccountModel.getBankAccountName());
			String phoneNumber=null;
			if(Objects.nonNull(billerAccountModel.getPhoneNumber())){
				phoneNumber=String.valueOf(billerAccountModel.getPhoneNumber());
			}
			existingRecord.setRechargeNumber4(phoneNumber);
			existingRecord.setRechargeNumber5(DecryptionUtil.getDecryptedText(billerAccountModel.getPanNumber()));
			existingRecord.setRechargeNumber7(DecryptionUtil.getDecryptedText(billerAccountModel.getAccountId()));
			existingRecord.setRechargeNumber8(billerAccountModel.getMaskAccountId());
			existingRecord.setPayType(productDetails.getPayType());
			existingRecord.setConsumerName(billerAccountModel.getName());
//			existingRecord.setRentConsent(RENTCONSENT.valueOfString(billerAccountModel.getConsent()).getConstant());

			existingRecord.setRentTFData(RecentUtils.getRentTFData(billerAccountModel));
			Date existingUpdatedAt = existingRecord.getUpdatedAt();
			Date createdAtTime = Objects.nonNull(existingRecord.getCreatedAt()) ? existingRecord.getCreatedAt() : ((Objects.nonNull(existingRecord.getTxnTime()) ? existingRecord.getTxnTime() : new Date()));
			if(Objects.nonNull(existingRecord.getRentConsent()) && existingRecord.getRentConsent()==5 && billerAccountModel.getAccountStatus()!=3){
				try {
					SimpleDateFormat sdf=new SimpleDateFormat(RECENTS_DATE_FORMAT);
					sdf.setLenient(false);
					sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
					Date createdAt=sdf.parse(billerAccountModel.getCreatedAt());
					existingRecord.setCreatedAt(createdAt);

				} catch (Exception e) {
					existingRecord.setCreatedAt(new Date());
					logger.error("[RecentService.insertionForRent] createdAt: Exception for customerId  and billerId {} {}" , billerAccountModel.getCustomerId(),billerAccountModel.getId());
				}
			}
			setRentConsentInRecents(existingRecord,billerAccountModel);
			ttl = TTLUtils.getTTL(productDetails.getService(),billerAccountModel.getAccountStatus()==3 ? new Date() : existingRecord.getTxnTime(), createdAtTime);
			if(ttl<1)ttl=1;
			logger.info("updating existing  record for rent ::{}",existingRecord);
			recentDataToKafkaService.setRecentConsumerSource(existingRecord, BILLER_ACCOUNT_CONSUMER);
			isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord,existingUpdatedAt,ttl);
		} else {
			Recents recent=this.buildBillerAccountRecentsForRent(billerAccountModel,productDetails);
			logger.info("inserting new record for rent ::{}",recent);
			ttl = TTLUtils.getTTL(productDetails.getService(),null,billerAccountModel.getAccountStatus()==3 ? new Date() : recent.getCreatedAt());
			if(ttl<1){
				logger.info("insertionForRent :: ttl negative detected {}",recent);
				recent.setCreatedAt(new Date());
				recent.setUpdatedAt(new Date());
				ttl = TTLUtils.getTTL(productDetails.getService(),null,recent.getCreatedAt());
				metricsHelper.recordSuccessRate(BILLER_ACCOUNT_CONSUMER, NEGATIVE_TTL);

			}
			recentDataToKafkaService.setRecentConsumerSource(recent, BILLER_ACCOUNT_CONSUMER);
			isUpdated = recentsRepository.updateRecentWhenNoExistingData(recent, ttl);

		}
		if (Boolean.FALSE.equals(isUpdated))
			throw new BillerAccountListenerException("[RecentService.insertBillerAccountRecents] : Retrying as update query returning false for customerId :" + billerAccountModel.getCustomerId() + "and rechargeNumber :" + billerAccountModel.getAccountId());
		logger.info("[RecentService.insertBillerAccountRecents] :: Inserting  recents successfull for customer_id : {} and rechargeNumber : {}", billerAccountModel.getCustomerId(), billerAccountModel.getAccountId());
	}

	public void insertionForTuitionFee(ProductMin productDetails,BillerAccountKafkaModel billerAccountModel) throws BillerAccountListenerException, RecentDataToKafkaException {
		List<Recents> existingRecents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(billerAccountModel.getCustomerId(), productDetails.getService(),String.valueOf(billerAccountModel.getId()), productDetails.getOperator());
		logger.info("insertionForRent :: existing recents for tf {}",existingRecents);
		Boolean isUpdated;
		Integer ttl;
		if (!CollectionUtils.isEmpty(existingRecents)) {
			Recents existingRecord = existingRecents.get(0);
			existingRecord.setProductId(productDetails.getProductId());
			existingRecord.setRechargeNumber2(billerAccountModel.getIfscCode());
			existingRecord.setRechargeNumber3(DecryptionUtil.getDecryptedText(billerAccountModel.getAccountId()));

			String phoneNumber=null;
			if(Objects.nonNull(billerAccountModel.getPhoneNumber())){
				phoneNumber=String.valueOf(billerAccountModel.getPhoneNumber());
			}

			existingRecord.setRechargeNumber4(phoneNumber);
			existingRecord.setRechargeNumber5(DecryptionUtil.getDecryptedText(billerAccountModel.getPanNumber()));
			existingRecord.setRechargeNumber7(String.valueOf(billerAccountModel.getName()));
			existingRecord.setRechargeNumber8(billerAccountModel.getMaskAccountId());
			existingRecord.setPayType(productDetails.getPayType());
			existingRecord.setConsumerName(billerAccountModel.getName());
			existingRecord.setRentTFData(RecentUtils.getRentTFData(billerAccountModel));
			Date existingUpdatedAt = existingRecord.getUpdatedAt();
			Date createdAtTime = Objects.nonNull(existingRecord.getCreatedAt()) ? existingRecord.getCreatedAt() : ((Objects.nonNull(existingRecord.getTxnTime()) ? existingRecord.getTxnTime() : new Date()));
			ttl = TTLUtils.getTTL(productDetails.getService(),existingRecord.getTxnTime(), createdAtTime);
			if(ttl<1)ttl=1;
			logger.info("updating existing  record for tf ::{}",existingRecord);
			recentDataToKafkaService.setRecentConsumerSource(existingRecord, BILLER_ACCOUNT_CONSUMER);
			isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecord,existingUpdatedAt,ttl);
		} else {
			Recents recent=this.buildBillerAccountRecentsForTuitionFee(billerAccountModel,productDetails);
			logger.info("inserting new record for rent ::{}",recent);
			ttl = TTLUtils.getTTL(productDetails.getService(),null,recent.getCreatedAt());
			if(ttl<1){
				logger.info("insertionForTuitionFee :: ttl negative detected {}",recent);
				recent.setCreatedAt(new Date());
				recent.setUpdatedAt(new Date());
				ttl = TTLUtils.getTTL(productDetails.getService(),null,recent.getCreatedAt());
				metricsHelper.recordSuccessRate(BILLER_ACCOUNT_CONSUMER, NEGATIVE_TTL);
			}
			recentDataToKafkaService.setRecentConsumerSource(recent, BILLER_ACCOUNT_CONSUMER);
			isUpdated = recentsRepository.updateRecentWhenNoExistingData(recent, ttl);

		}
		if (Boolean.FALSE.equals(isUpdated))
			throw new BillerAccountListenerException("[RecentService.insertBillerAccountRecents] : Retrying as update query returning false for customerId :" + billerAccountModel.getCustomerId() + "and rechargeNumber :" + billerAccountModel.getAccountId());
		logger.info("[RecentService.insertBillerAccountRecents] :: Inserting  recents successfull for customer_id : {} and rechargeNumber : {}", billerAccountModel.getCustomerId(), billerAccountModel.getAccountId());
	}


	@Recover
	public void recoverFromBillerAccountListenerException(BillerAccountListenerException ex) {
		metricsHelper.recordSuccessRate(BILLER_ACCOUNT_CONSUMER, RECENT_UPDATE_BILLER_FAIL);
		logger.error("[RecentService.recoverFromBillerAccountListenerException] All retry attempts exhausted for insertBillerAccountRecents() after all attempts.", ex);
	}
	public Boolean isUpdatedSourceValidation(ReminderCDC reminderCDC){
		Map<String,String> extras = null;
		if(Objects.nonNull(reminderCDC.getAfter().getExtra()) && Objects.nonNull(reminderCDC.getAfter().getExtra().getValue())){
			try {
				extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
			}catch (Exception e){
				logger.error(ServiceWrapperUtil.findServiceWrapper(reminderCDC), "[CDCReminderListener.isCreatedSourceValidation] Unable to parse extras due to, error ",e);
			}
			if(extras == null)
				return false;
			if (!extras.containsKey(Constants.UPDATED_SOURCE) ||
					(extras.containsKey(Constants.UPDATED_SOURCE) && extras.get(Constants.UPDATED_SOURCE).equalsIgnoreCase(Constants.EVENTSOURCE_SMS)))
				return false;
			else if (extras.get(Constants.UPDATED_SOURCE).equalsIgnoreCase(Constants.VALIDATION_SYNC))
				return true;
		}
		return false;
	}

	public Boolean isNotTxnRecord(Recents recents){
		if(Objects.isNull(recents.getOrderId())){
			if((Objects.isNull(recents.getLastFailureTxn())) && (Objects.isNull(recents.getLastFailureTxn()))){
				return true;
			}else if(Objects.isNull(getOrderIdFromPendingAndFailureTxn(recents.getLastFailureTxn())) &&
					Objects.isNull(getOrderIdFromPendingAndFailureTxn(recents.getLastPendingTxn()))){
				return true;
			}
		}
		return false;
	}

	public Long getOrderIdFromPendingAndFailureTxn(String lastTxn){
		Long orderId = null;
		if(Objects.nonNull(lastTxn)){
			Map<String,Object> lastTxnDetails = JsonUtils.parseJson(lastTxn,Map.class);
			orderId = (Long) lastTxnDetails.get(Constants.TXN_ID);
		}
		return orderId;

	}



	public List<Recents> fetchRecentsFromDb(Long customerId,String service,String recharge_number,String operator,String plan_bucket){
		List<Recents> recents = null ;
		try{
			recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,recharge_number,operator,plan_bucket);

		}catch (RuntimeException e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[fetchRecentsFromDb] error while fetching recents from db for customer id : {},service : {},recharge_number : {},operator :{},plan_bucket :{}",
					customerId,service,recharge_number,operator,plan_bucket);
		}
		return recents;

	}

	private void setRechargeNumbers(Recents recents,String rechargeNumber2, String rechargeNumber3,
									String rechargeNumber4, String rechargeNumber5, String rechargeNumber6, String rechargeNumber7,
									String rechargeNumber8){

		if (Objects.nonNull(rechargeNumber2))
			recents.setRechargeNumber2(rechargeNumber2);
		if (Objects.nonNull(rechargeNumber3))
			recents.setRechargeNumber3(rechargeNumber3);
		if (Objects.nonNull(rechargeNumber4))
			recents.setRechargeNumber4(rechargeNumber4);
		if (Objects.nonNull(rechargeNumber5))
			recents.setRechargeNumber5(rechargeNumber5);
		if (Objects.nonNull(rechargeNumber6))
			recents.setRechargeNumber6(rechargeNumber6);
		if (Objects.nonNull(rechargeNumber7))
			recents.setRechargeNumber7(rechargeNumber7);
		if (Objects.nonNull(rechargeNumber8))
			recents.setRechargeNumber8(rechargeNumber8);

	}

	public List<Recents> fetchLatestMatchingRecent(Long customerId, String service){
		List<Recents> recents =null;
		try{
			recents = recentsRepository.findByCustomerIdAndService(customerId,service);
		}catch (RuntimeException e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(service), "[fetchRecentsFromDb] error while fetching recents from db for customer id : {},service : {}",
					customerId,service);
		}
		return recents;
	}

	private boolean isNewBillIdentified(Date oldDuedate, Date newDuedate, Recents recents){
		if(Boolean.FALSE.equals(recents.getIsNewBillIdentified())){
			return false;
		}
		if(Objects.isNull(oldDuedate) || Objects.isNull(newDuedate)){
			if(Objects.isNull(oldDuedate) && Objects.nonNull(newDuedate)) {
				logger.info(ServiceWrapperUtil.findServiceWrapper(recents), "isNewBillIdentified: oldDuedate is null and newDuedate is non null for customer id : {},service : {},recharge_number : {},operator :{}",
						recents.getKey().getCustomerId(),recents.getKey().getService(),recents.getKey().getRechargeNumber(),recents.getOperator());
				String metricConstant= String.format(Constants.MetricConstants.OLD_DUE_DATE_NULL_NEW_DUE_DATE_NON_NULL, recents.getKey().getCustomerId(), recents.getKey().getRechargeNumber(), recents.getKey().getService(), recents.getOperator());
				metricsHelper.pushToDD(metricConstant, Constants.MetricConstants.SERVICE);
			}
			return false;
		}

        return 0 == DateUtil.compareDateWithoutTime(oldDuedate, newDuedate);

    }

	@Retryable(value = {PrepaidBillsListenerException.class},
			maxAttemptsExpression = "${db.writes.max.attempts}")
	public void unsetAutomaticForDeleteEventFromPrepaidBills(PrepaidBillsListenerModel prepaidBillsListenerModel, String planBucket) throws PrepaidBillsListenerException {
		List<Recents> recents = null;
		Boolean isUpdated = false;
		try{
			recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(prepaidBillsListenerModel.getData().getCustomer_id(), prepaidBillsListenerModel.getData().getService(),prepaidBillsListenerModel.getData().getRecharge_number(),prepaidBillsListenerModel.getData().getOperator(),planBucket);
		}
		catch (Exception e){
			logger.error("unsetAutomaticForDeleteEventFromPrepaidBills: error while fetching recents from db for customer id : {},service : {},recharge_number : {},operator :{}",
					prepaidBillsListenerModel.getData().getCustomer_id(),prepaidBillsListenerModel.getData().getService(),prepaidBillsListenerModel.getData().getRecharge_number(),prepaidBillsListenerModel.getData().getOperator());


		}
		if(!CollectionUtils.isEmpty(recents)){
			Recents existingRecent = recents.get(0);
			if(Objects.nonNull(FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED)) && FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED).contains(String.valueOf(ActiveInactivePidMapCache.getInstance().getActivePid(existingRecent.getProductId())))){
				existingRecent.setDueDate(null);
			}

			existingRecent.setAutomaticStatus(0);
			existingRecent.setAutomaticAmount(null);
			existingRecent.setAutomaticDate(null);
			Integer ttl=TTLUtils.getTTL(existingRecent.getKey().getService(),existingRecent.getTxnTime(),existingRecent.getCreatedAt());
			if(ttl<1)
				ttl=1;
			isUpdated = recentsRepository.updateRecentWhenDataAlreadyExist(existingRecent,existingRecent.getUpdatedAt(),ttl);
		}
		else{
			logger.info("unsetAutomaticForDeleteEventFromPrepaidBills: recent does not exist for customer id : {},service : {},recharge_number : {},operator :{}",
					prepaidBillsListenerModel.getData().getCustomer_id(),prepaidBillsListenerModel.getData().getService(),prepaidBillsListenerModel.getData().getRecharge_number(),prepaidBillsListenerModel.getData().getOperator());
			return;
		}

		if(Boolean.TRUE.equals(isUpdated)){
			List<Long> customerIdForCleanCache = new ArrayList<>();
			customerIdForCleanCache.add(prepaidBillsListenerModel.getData().getCustomer_id());
			cleanCache(customerIdForCleanCache);
			logger.info("unsetAutomaticForDeleteEventFromPrepaidBills: SUCCESFULLY updating recents for customer id : {},service : {},recharge_number : {},operator :{}",
					prepaidBillsListenerModel.getData().getCustomer_id(),prepaidBillsListenerModel.getData().getService(),prepaidBillsListenerModel.getData().getRecharge_number(),prepaidBillsListenerModel.getData().getOperator());

		}
		else{
			throw new PrepaidBillsListenerException();
		}


	}


	private List<Recents>  fetchRecentsCorrespondingToPlanBucket(List<Recents> recents,ReminderCDC reminderCDC,RecentsPrimaryKey key){
		String planBucket=RecentUtils.getPlanBucketForCDCPayload(reminderCDC);
		key.setPlanBucket(planBucket);

		//do check they belong to sms
		if(!StringUtils.isEmpty(planBucket)){
				recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(key.getCustomerId(),key.getService(),
						key.getRechargeNumber(),key.getOperator(),key.getPlanBucket());
				List<Recents> emptyPlanBucketRecents=recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(key.getCustomerId(),key.getService(),
						key.getRechargeNumber(),key.getOperator(),"");
				if(Objects.nonNull(emptyPlanBucketRecents)&&!emptyPlanBucketRecents.isEmpty()&&planBucket.equalsIgnoreCase(RecentUtils.getPlanBucket(emptyPlanBucketRecents.get(0))))
					recents.addAll(emptyPlanBucketRecents);

		}
		//if remindercdc come with empty plan bucket that cse should also get handled
		if(Objects.isNull(recents) || recents.isEmpty()){
				key.setPlanBucket("");
				recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(key.getCustomerId(),key.getService(),
						key.getRechargeNumber(),key.getOperator(),key.getPlanBucket());
		}
		return recents;

	}

	public void deleteRecentForPrepaidDataExhaust(Recents recents,Long customerId, String service, String recharge_number, String operator, String plan_bucket) {
		try {
			recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(recents,customerId, service, recharge_number, operator, plan_bucket);
			invalidateCache(customerId);
		} catch (Exception e) {
			logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "[RecentService.deleteRecentForPrepaidDataExhaust] error occurred for customerId {} service {} rechargeNumber {} operator {} plan_bucket {}, Exception {}", customerId, service, recharge_number, operator, plan_bucket, e);
		}
	}
	@Retryable(value = {RemindLaterServiceException.class},
			maxAttemptsExpression = "${db.writes.max.attempts}")
	public void updateRemindLater(Recents recents, Integer remindLaterOffset){
		try {
			List<Recents> recents1 = recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(recents.getKey().getCustomerId(), recents.getKey().getService(), recents.getKey().getRechargeNumber());
			if (!CollectionUtils.isEmpty(recents1)) {
				Recents existingRecent = recents1.get(0);
				if(recents.getRemindLaterDate()!=null)
					existingRecent.setRemindLaterDate(recents.getRemindLaterDate());
				else
					existingRecent.setRemindLaterDate(DateUtils.addDays(existingRecent.getDueDate(),-remindLaterOffset));
				Date oldUpdatedAt = existingRecent.getUpdatedAt();
				existingRecent.setUpdatedAt(new Date());
				Integer ttl = TTLUtils.getTTL(existingRecent.getKey().getService(), existingRecent.getTxnTime(), existingRecent.getCreatedAt());
				if (ttl < 1)
					ttl = 1;
				logger.info(ServiceWrapperUtil.findServiceWrapper(recents), "[RecentService.updateRemindLater] :: Update remind later date in recents for custId {} and service {} and rechargeNumber {} and remindLaterDate {}",existingRecent.getKey().getCustomerId(),existingRecent.getKey().getService(),existingRecent.getKey().getRechargeNumber(),existingRecent.getRemindLaterDate());
				recentsRepository.updateRecentWhenDataAlreadyExist(existingRecent, oldUpdatedAt, ttl);
			}
		}catch (Exception e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "[RecentService.updateRemindLater] :: error while updating remind me later with e :",e);
		}
	}
	@Recover
	public void recoverFromRemindLaterServiceException(RemindLaterServiceException ex) {
		metricsHelper.recordSuccessRate(REMIND_LATER_SERVICE, RECENT_UPDATE_REMINDLATER_SERVICE_FAIL);
		logger.error("[RecentService.recoverFromRemindLaterServiceException] All retry attempts exhausted for updateRemindLater() after all attempts.",ex);
	}

	private void setIsPaytmVPAFlagForAutomaticWhenDataSkipped(Recents existingRecord){
		Map<String,Object> existingRecordExtra = null;
		if(!StringUtils.isEmpty(existingRecord.getExtra())){
			existingRecordExtra =  JsonUtils.parseJson(existingRecord.getExtra(),Map.class);
			if(Objects.nonNull(existingRecordExtra)){
				existingRecordExtra.put(Constants.IS_PAYTM_VPA,0);
			}
		}
		else{
			existingRecordExtra = new HashMap<>();
			existingRecordExtra.put(Constants.IS_PAYTM_VPA,0);
		}
		if(Objects.nonNull(existingRecordExtra)) {
			ObjectMapper objectMapper = new ObjectMapper();
			try {
				String jsonString = objectMapper.writeValueAsString(existingRecordExtra);
				existingRecord.setExtra(jsonString);
			} catch (JsonProcessingException e) {
				logger.error(ServiceWrapperUtil.findServiceWrapper(existingRecord), "[RecentService.setIsPaytmVPAFlagForAutomatic] ::Error converting extraInfo map to JSON string", e);
			}
		}
	}

	private void mergeRecentsExtraWithReceivedExtraInfo(Recents recents,String extra){
		if(!StringUtils.isEmpty(recents.getExtra())){
			Map<String,Object> existingRecentExtraMap = null;
			Map<String,Object> recievedExtraMap = null;
			try{
				existingRecentExtraMap = JsonUtils.parseJson(recents.getExtra(),Map.class);
                if(Objects.nonNull(existingRecentExtraMap) && existingRecentExtraMap.containsKey(IS_PAYTM_VPA)){
					Object isPaytmVpaValue = existingRecentExtraMap.get(IS_PAYTM_VPA);
					existingRecentExtraMap = new HashMap<>();
					existingRecentExtraMap.put(IS_PAYTM_VPA, isPaytmVpaValue);
				}
				else{
					existingRecentExtraMap=null;
				}
			}
			catch (Exception e){
				logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "[RecentService.prepareFinalRecents] :: Json parsing exception in parsing existingRecent Extra, recentPrimaryKey = {}, e={}",recents.getKey(),e);
			}
			try{
				recievedExtraMap = JsonUtils.parseJson(extra,Map.class);
			}
			catch (Exception e){
				logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "[RecentService.prepareFinalRecents] :: Json parsing exception in parsing received Extra, recentPrimaryKey = {}, e={}",recents.getKey(),e);
			}
			if(Objects.nonNull(existingRecentExtraMap) && Objects.nonNull(recievedExtraMap)){
				existingRecentExtraMap.putAll(recievedExtraMap);
				ObjectMapper objectMapper = new ObjectMapper();
				try {
					String extraJsonString = objectMapper.writeValueAsString(existingRecentExtraMap);
					recents.setExtra(extraJsonString);
				} catch (JsonProcessingException e) {
					logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "[RecentService.setIsPaytmVPAFlagInRecentTable] ::Error converting extraInfo map to JSON string", e);
				}
			}
			else if(Objects.isNull(existingRecentExtraMap)){
				recents.setExtra(extra);
			}

		}
		else {
			recents.setExtra(extra);
		}
	}

	private void setPGFailureFieldsFromOMS(Recents existingRecord, String pgRespCode, String isRetryExhausted){
		Map<String,Object> existingRecordExtra = null;
		if(!StringUtils.isEmpty(existingRecord.getExtra())){
			existingRecordExtra =  JsonUtils.parseJson(existingRecord.getExtra(),Map.class);
			if(Objects.nonNull(existingRecordExtra)){
				existingRecordExtra.put(Constants.PG_RESP_CODE, pgRespCode);
				existingRecordExtra.put(Constants.IS_RETRY_EXHAUST, isRetryExhausted);
			}
		}
		else{
			existingRecordExtra = new HashMap<>();
			existingRecordExtra.put(Constants.PG_RESP_CODE, pgRespCode);
			existingRecordExtra.put(Constants.IS_RETRY_EXHAUST, isRetryExhausted);
		}
		if(Objects.nonNull(existingRecordExtra)) {
			ObjectMapper objectMapper = new ObjectMapper();
			try {
				String jsonString = objectMapper.writeValueAsString(existingRecordExtra);
				existingRecord.setExtra(jsonString);
			} catch (JsonProcessingException e) {
				logger.error(ServiceWrapperUtil.findServiceWrapper(existingRecord),"[RecentService.setPGFailureFieldsFromOMS] ::Error converting extraInfo map to JSON string", e);
			}
		}
	}

	public void checkAndDeleteLoanRecentDummyRecords(Recents recents){
		try {
			if (recents.getKey().getService().equalsIgnoreCase(Constants.ServiceTypeConstants.LOAN)) {
				String dummyRN = RecentUtils.generateDummyRNForLoan(recents.getKey().getCustomerId().toString(), recents.getKey().getOperator());
				recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(recents, recents.getKey().getCustomerId(), recents.getKey().getService(), dummyRN, recents.getKey().getOperator(), "");
				metricsHelper.recordSuccessRate(Constants.CommonConstants.REMINDER_CONSUMER, LOAN_DUMMY_RN_DELETED);
			}
		} catch (Exception e){
			logger.error(ServiceWrapperUtil.findServiceWrapper(recents), "[RecentService.checkAndDeleteLoanRecentDummyRecords] :: Error while deleting dummy RN from recents for customer id : {}, service : {}, recharge number : {},operator : {}, error = {}",
					recents.getKey().getCustomerId(), recents.getKey().getService(), recents.getKey().getRechargeNumber(), recents.getKey().getOperator(), e);
			metricsHelper.recordSuccessRate(Constants.CommonConstants.REMINDER_CONSUMER, LOAN_DUMMY_RN_DELETED);
		}
	}


	private void setRentConsentInRecents(Recents recents, BillerAccountKafkaModel billerAccountKafkaModel){
		//this check for accountStatus =3 is only for old biller migration
		//TODO Remove this code after old biller migration is complete
		if(billerAccountKafkaModel.getAccountStatus()==3)
			recents.setRentConsent(RENTCONSENT.OLD_BILLER.getConstant());
		else
			recents.setRentConsent(RENTCONSENT.valueOfString(billerAccountKafkaModel.getConsent()).getConstant());
	}

	private Date getOldCreatedAtForOldBillers(BillerAccountKafkaModel billerAccountKafkaModel){
		return  DateUtil.stringToDate((String)billerAccountKafkaModel.getMetaData().get(Constants.OLD_CREATED_AT),YYYY_MM_DD_T_HH_MM__SS_SSSZ);
	}
}
