package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoadSensitiveFieldsCustomLogger;
import com.paytm.saga.common.MethodLatencyMetricsAction;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfig;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FeatureConfigResponse;
import com.paytm.saga.util.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
public class FeatureConfigService {

    private final CustomLogger logger = CustomLogManager.getLogger(FeatureConfigService.class);

    private static final String SERVICE_CONFIG_KEY = "serviceConfig";

    @Value("${ruleEngine.host}")
    private String host;

    @Value("${ruleEngine.endpoints.offloadcpuData}")
    private String offloadcpuData;

    @Autowired
    @Qualifier("GenericRestClient")
    private GenericRestClient genericRestClient;

    @Autowired
    private MobileSuggestedCardConfigService mobileSuggestedCardConfigService;

    @Autowired
    private MetricsHelper metricsHelper;

    private boolean success;

    private boolean onStartUp=true;

    @PostConstruct
    public void onStartup() throws Exception {
        load();
        onStartUp=false;
    }

    @MethodLatencyMetricsAction(metricsName = "load_service_config")
    @Scheduled(cron = "0 */5 * * * *")
    public void load() throws Exception {
        try {
            FeatureConfigResponse sagaConfigResponse = genericRestClient.get(host, offloadcpuData, buildQueryParams(), null,
                new ParameterizedTypeReference<FeatureConfigResponse>() {
                });

            //ObjectMapper mapper = new ObjectMapper();
            //FeatureConfigResponse response = mapper.readValue("{\"status\":200,\"data\":[{\"id\":5,\"service_name\":\"recharges-saga\",\"config\":\"{\\\"disableDropOff\\\": true, \\\"disableChatHistory\\\": true,  \\\"smartReminderPrepaidEndDays\\\": 3, \\\"smartReminderPrepaidStartDays\\\": -5, \\\"smartReminderPostpaidEndDays\\\": 3, \\\"smartReminderCustomerBillLimit\\\": 30 }\"}]}", FeatureConfigResponse.class);
            logger.trace("response is {}", sagaConfigResponse);
            if (sagaConfigResponse.getStatus() == HttpStatus.OK.value() && Objects.nonNull(sagaConfigResponse.getData())) {
                for (FeatureConfig fc : sagaConfigResponse.getData()) {
                    FeatureConfigCache.getInstance().set(fc);
                }
                success = true;
            } else {
                logger.error("loadFeactureConfig:: error received responseCode {}, body {}", sagaConfigResponse.getStatus(),
                    JsonUtils.serialiseJson(sagaConfigResponse));
                metricsHelper.pushToDD("service_config_sync_error", null);
            }
            mobileSuggestedCardConfigService.setMobileSuggestedCardConfig();
            LoadSensitiveFieldsCustomLogger.load(JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject("sensitiveFields_custom_logger")));
        } catch (Exception ex) {
            logger.error("fetchCvrData:: error while loading feature_config ", ex);
            metricsHelper.pushToDD("service_config_sync_error", null);
            if (onStartUp)
                throw ex;
        }
    }

    private Map<String, Object> buildQueryParams() {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("key", SERVICE_CONFIG_KEY);
        queryParams.put("service_name", "bff_saga_common_config");
        return queryParams;
    }

}