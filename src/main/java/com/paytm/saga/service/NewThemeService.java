package com.paytm.saga.service;

import com.datastax.oss.driver.api.core.cql.PagingState;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.*;
import com.paytm.saga.repository.CommonCacheRepository;
import com.paytm.saga.repository.NewThemeRepository;
import com.paytm.saga.util.JsonUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.Base64;

import static com.paytm.saga.common.constant.Constants.NATIVE_THEME_HASH;

@Service
public class NewThemeService {
    private static final CustomLogger logger = CustomLogManager.getLogger(com.paytm.saga.service.ThemeService.class);
    private final NewThemeRepository newthemeRepository;
    private final CommonCacheRepository commonCacheRepository;
    private final MetricsHelper metricsHelper;

    @Autowired
    public NewThemeService(final NewThemeRepository newThemeRepository,
                           final CommonCacheRepository commonCacheRepository, MetricsHelper metricsHelper) {
        this.newthemeRepository = newThemeRepository;
        this.commonCacheRepository = commonCacheRepository;
        this.metricsHelper = metricsHelper;
    }

    public void saveTheme(NewTheme theme) {
        NewTheme hashObj = new NewTheme();
        hashObj.setViewItem(UUID.randomUUID().toString());
        hashObj.setThemeType("theme_hash_key");
        List<NewTheme> themes = new ArrayList<NewTheme>();
        themes.add(theme);
        themes.add(hashObj);
        newthemeRepository.saveAll(themes);
    }

    public ThemeResponse getThemesForNativeCategory() {
        ThemeResponse themeResponse = new ThemeResponse();
        String filePath = null;
        ClassLoader classLoader = NewThemeService.class.getClassLoader();
        InputStream inputStream = classLoader.getResourceAsStream("themes.txt");
        List<ThemeDto> themes = null;
        themes = JsonUtils.fromInputStreamToJson(inputStream,
                new TypeReference<List<ThemeDto>>() {
                });
        themeResponse.setThemes(themes);
        themeResponse.setThemehash(NATIVE_THEME_HASH);
        return themeResponse;
    }

    public ThemeResponse getThemes(String themeHashKey) {
        logger.info("Theme fetch request received for themeHashKey", themeHashKey);
        ThemeResponse themeResponse = new ThemeResponse();
        List<NewTheme> themeHash = fetchThemes("theme_hash_key", "theme_hash_key");
        if (themeHash != null && themeHash.size() > 0) {
            if (themeHashKey == null || !themeHash.get(0).getThemeHash().equals(themeHashKey)) {

                List<CommonCache> commonCaches = commonCacheRepository
                        .findByCacheKey("theme_details_" + themeHash.get(0).getThemeHash());
                if (commonCaches != null && !commonCaches.isEmpty()) {
                    themeResponse.setThemes(JsonUtils.parseJsonToList(commonCaches.get(0).getCacheValue(), ThemeDto.class));
                    themeResponse.setThemehash(themeHash.get(0).getThemeHash());
                } else {
                    List<NewTheme> newThemes = new ArrayList<>();
                    List<NewTheme> newThemes1 = fetchThemes("theme", "v1_themes");
                    List<NewTheme> newThemes2 = fetchThemes("theme", "v2_utilities_themes");
                    newThemes.addAll(newThemes1);
                    newThemes.addAll(newThemes2);
                    Map<String, List<NewTheme>> themesMap = this.createMapByThemeType(newThemes);
                    List<ThemeDto> themeDtos = new ArrayList<ThemeDto>();
                    for (String key : themesMap.keySet()) {
                        if (!key.equals("theme_hash_key")) {
                            ThemeDto themeDto = new ThemeDto();
                            themeDto.setThemeType(key);
                            List<NewTheme> themes = themesMap.get(key);
                            ThemeDetails themeDetails = new ThemeDetails();
                            for (NewTheme theme : themes) {
                                if (theme.getViewItem().contains("cta")) {
                                    themeDetails = this.setCta(theme, themeDetails);
                                } else if (theme.getViewItem().contains("ds")) {
                                    themeDetails = this.setDisplayValues(theme, themeDetails);
                                } else if (theme.getViewItem().contains("header")) {
                                    themeDetails = this.setHeadings(theme, themeDetails);
                                } else if (theme.getViewItem().contains("footer")) {
                                    themeDetails = this.setFooters(theme, themeDetails);
                                } else if (theme.getViewItem().contains("card")) {
                                    themeDto.setDisplayValuesAlignment(theme.getDisplayValuesAlignment());
                                    themeDto.setBgColor(theme.getBgColor());
                                    themeDto.setThemeType(theme.getThemeType());
                                    themeDto.setShowDisplayValue(theme.getShowDisplayValue());
                                    themeDto.setCardAlignment(theme.getCardAlignment());
                                    if (NumberUtils.isParsable(theme.getWidth()))
                                        themeDto.setWidth(Integer.parseInt(theme.getWidth()));
                                    themeDto.setBorderColor(theme.getBorderColor());
                                    themeDto.setValueColor(theme.getValueColor());
                                }
                            }
                            themeDto.setThemeDetail(themeDetails);
                            themeDtos.add(themeDto);
                        }
                    }
                    themeResponse.setThemes(themeDtos);
                    themeResponse.setThemehash(themeHash.get(0).getThemeHash());
                    try {
                        CommonCache commonCache = new CommonCache("theme_details_" + themeHash.get(0).getThemeHash(),
                                JsonUtils.serialiseJson(themeDtos));
                        commonCacheRepository.save(commonCache, 12 * 60 * 60);
                    } catch (JsonProcessingException e) {
                        logger.error(e.getMessage());
                    }

                }
                metricsHelper.recordStatusCodes("themes_count",""+themeResponse.getThemes().size());
            }
        }
        else {
            metricsHelper.recordStatusCodes("themes_count","Error");
        }


        return themeResponse;
    }
    private Map<String, List<NewTheme>> createMapByThemeType(List<NewTheme> themes) {
        Map<String, List<NewTheme>> map = new HashMap<String, List<NewTheme>>();
        for (NewTheme theme : themes) {
            List<NewTheme> themeInfo = null;
            if (map.containsKey(theme.getThemeType())) {
                themeInfo = map.get(theme.getThemeType());
                themeInfo.add(theme);
            } else {
                themeInfo = new ArrayList<NewTheme>();
                themeInfo.add(theme);
            }
            map.put(theme.getThemeType(), themeInfo);
        }
        return map;
    }

    public List<NewTheme> fetchThemes(final String themeKey,
                                      final String themeCategory) {
        NewThemePage newThemePage = new NewThemePage();
        List<NewTheme> newThemes = new ArrayList<>();
        formatResponseFromTable(themeKey, themeCategory, newThemePage, newThemes);
        while (newThemePage.getAvailableNext() != false) {
            formatResponseFromTable(themeKey, themeCategory, newThemePage, newThemes);
        }
        return newThemes;
    }

    public void formatResponseFromTable(String themeKey, String themeCategory, NewThemePage newThemePage, List<NewTheme> newThemes) {
        ResponsePage<NewTheme> responsePage = null;
        responsePage = getNewThemes(themeKey, themeCategory, newThemePage.getLastCardId());
        newThemePage.setAvailableNext(responsePage.getHasNext());
        newThemePage.setNewThemes(responsePage.getContent());
        newThemePage.setLastCardId(responsePage.getPagingState());
        if (!CollectionUtils.isEmpty(responsePage.getContent())) {
            newThemes.addAll(newThemePage.getNewThemes());
        }
    }

    public ResponsePage<NewTheme> getNewThemes(final String themeKey, final String themeCategory,
                                               final String pagingState) {
        int limit = 10;
        final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(limit, pagingState);
        final Slice<NewTheme> themesSlice = newthemeRepository.findByThemeKeyAndThemeCategory(themeKey, themeCategory, cassandraPageRequest);
        return new ResponsePage<>(themesSlice);
    }

    private CassandraPageRequest createCassandraPageRequest(final Integer limit, final String pagingState) {
        final PageRequest pageRequest = PageRequest.of(0, limit);
        if (pagingState != null) {
            try {
                logger.info("Processing paging state: length={}", pagingState.length());

                // Try Base64 decoding first (new format)
                byte[] decodedBytes = Base64.getDecoder().decode(pagingState);
                ByteBuffer pagingStateBuffer = ByteBuffer.wrap(decodedBytes);
                metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_NEW_THEME_SERVICE);
                return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
            } catch (Exception e) {
                logger.error("Failed to parse paging state - length: {}, error: {}",
                        pagingState.length(), e.getMessage());
                // Try fallback to old format for backward compatibility
                try {
                    logger.info("Attempting fallback to PagingState.fromString format");
                    final PagingState pageState = PagingState.fromString(pagingState);
                    ByteBuffer pagingStateBuffer = ByteBuffer.wrap(pageState.toBytes());
                    metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_NEW_THEME_SERVICE);
                    return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
                } catch (Exception fallbackException) {
                    logger.error("Fallback parsing also failed: {}", fallbackException.getMessage());
                    metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR, Constants.MetricConstants.PAGING_STATE_NEW_THEME_SERVICE_DEFAULT);
                    return CassandraPageRequest.of(pageRequest, null);
                }
            }
        }

        metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE_DEFAULT);
        return CassandraPageRequest.of(pageRequest, null);
    }

    private ThemeDetails setCta(NewTheme theme, ThemeDetails themeDetails) {
        if (theme.getViewItem().equals("cta1")) {
            themeDetails.setCta1(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("cta2")) {
            themeDetails.setCta2(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("cta3")) {
            themeDetails.setCta3(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("cta4")) {
            themeDetails.setCta4(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("cta5")) {
            themeDetails.setCta5(this.setViewDetail(theme));

        }
        return themeDetails;
    }

    private ThemeDetails setHeadings(NewTheme theme, ThemeDetails themeDetails) {
        if (theme.getViewItem().equals("header1")) {
            themeDetails.setHeader1(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("header2")) {
            themeDetails.setHeader2(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("header3")) {
            themeDetails.setHeader3(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("header4")) {
            themeDetails.setHeader4(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("header5")) {
            themeDetails.setHeader5(this.setViewDetail(theme));

        }
        return themeDetails;
    }

    private ThemeDetails setFooters(NewTheme theme, ThemeDetails themeDetails) {
        if (theme.getViewItem().equals("footer1")) {
            themeDetails.setFooter1(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("footer2")) {
            themeDetails.setFooter2(this.setViewDetail(theme));

        }
        return themeDetails;
    }

    private ThemeDetails setDisplayValues(NewTheme theme, ThemeDetails themeDetails) {
        if (theme.getViewItem().equals("ds1")) {
            themeDetails.setDs1(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("ds2")) {
            themeDetails.setDs2(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("ds3")) {
            themeDetails.setDs3(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("ds4")) {
            themeDetails.setDs4(this.setViewDetail(theme));

        } else if (theme.getViewItem().equals("ds5")) {
            themeDetails.setDs5(this.setViewDetail(theme));

        }
        return themeDetails;
    }

    private ViewDetail setViewDetail(NewTheme theme) {
        return new ViewDetail(theme.getValueColor(), theme.getKeyColor(),theme.getBgColor(), theme.getBorderColor(),
                theme.getValueFontWeight(),theme.getKeyFontWeight(), theme.getRichTextColor(), theme.getRightThumbImage(),
                theme.getLeftThumbnail(), theme.getIsAmount(), theme.getTheme());
    }
}