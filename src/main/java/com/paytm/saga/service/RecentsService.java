
package com.paytm.saga.service;


import com.paytm.saga.common.exception.DBUpdateException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.model.Recents;


public interface RecentsService {

    public NickNameResponse updateNickName(NickNameRequest nickNameRequest);
    public NickNameResponse removeRecentWrapper(DeleteRequestBody deleteRequestBody);

    public String getCustomerName(GetHistoryPageDTO getHistoryPageDTO, Boolean isNameRequired);

    public FetchRecentsResponse fetchRecentsByRechargeNumber(FetchRecentsRequest fetchRecentsRequest) throws RechargeSagaBaseException;
    public FetchRecentsResponse fetchRecentsByCustomerId(FetchRecentsRequest fetchRecentsRequest) throws RechargeSagaBaseException;
    public Boolean createRecentForInsurance(CreateRecentRequest request) throws DBUpdateException;
    public Boolean updateRecentForInsurance(Recents recents, String insuranceCard);
}