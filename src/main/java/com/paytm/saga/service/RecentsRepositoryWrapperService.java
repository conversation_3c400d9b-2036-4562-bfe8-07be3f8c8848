package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.NickNameRequest;
import com.paytm.saga.factory.DummyRechargeNumberGeneratorFactory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.util.AESUtil;
import com.paytm.saga.util.RecentUtils;
import com.paytm.saga.util.TTLUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static com.paytm.saga.common.constant.Constants.LOAN;
import static com.paytm.saga.common.constant.EncryptionConstants.*;

@Service
public class RecentsRepositoryWrapperService {
	private final CustomLogger logger = CustomLogManager.getLogger(RecentsRepositoryWrapperService.class);
	private final RecentsRepository recentsRepository;
	private final RecentsEncryptionHandler recentsEncryptionHandler;
	private final AESUtil aesUtil;
	private final MetricsHelper metricsHelper;

	private final DummyRechargeNumberGeneratorFactory dummyRechargeNumberGeneratorFactory;
	@Autowired
	private RecentsRepositoryWrapperService(
		RecentsRepository recentsRepository,
		RecentsEncryptionHandler recentsEncryptionHandler,
		AESUtil aesUtil,
		MetricsHelper metricsHelper,
		DummyRechargeNumberGeneratorFactory dummyRechargeNumberGeneratorFactory
	) {
		this.recentsRepository = recentsRepository;
		this.recentsEncryptionHandler = recentsEncryptionHandler;
		this.aesUtil = aesUtil;
		this.metricsHelper = metricsHelper;
		this.dummyRechargeNumberGeneratorFactory = dummyRechargeNumberGeneratorFactory;
	}

	public Recents findById(RecentsPrimaryKey recentsPrimaryKey) {
		if(recentsPrimaryKey == null) {
			return null;
		}
		if(FINANCIAL_SERVICES.equalsIgnoreCase(recentsPrimaryKey.getService())) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(recentsPrimaryKey.getCustomerId());
			if (isCCEncryptionRequired) {
				try {
					RecentsPrimaryKey encRecentsPrimaryKey = new RecentsPrimaryKey();
					BeanUtils.copyProperties(recentsPrimaryKey, encRecentsPrimaryKey);
					encRecentsPrimaryKey.setRechargeNumber(aesUtil.encrypt(recentsPrimaryKey.getRechargeNumber()));
					Recents recents = recentsRepository.findById(encRecentsPrimaryKey).orElse(null);
					if (Objects.nonNull(recents)) {
						return recentsEncryptionHandler.decryptRecent(recents);
					}
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : findById : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION, RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("findById : Error in decrypting the request");
				}
			}
		}
		return recentsRepository.findById(recentsPrimaryKey).orElse(null);
	}

	public Boolean updateRecentWhenNoExistingData(
		Recents recents,
		Integer ttl
	) throws AES256Exception {
		logger.debug("inside updateRecentWhenNoExistingData {}", recents);
		if (Objects.nonNull(recents) && Objects.nonNull(recents.getKey()) && Constants.FINANCIAL_SERVICES.equalsIgnoreCase(recents.getKey().getService())) {
			String service = recents.getKey().getService();
			Long customerid = recents.getKey().getCustomerId();
			String operator = recents.getKey().getOperator();
			String rechargeNumber = recents.getKey().getRechargeNumber();
			String planBucket = recents.getKey().getPlanBucket();
			boolean isCCEncryptionRequiredFlag = EncryptionDecision.isDataEncryptionRequired(recents.getKey().getCustomerId());
			if (isCCEncryptionRequiredFlag) {
				try {
					Recents encRecents = handleEncryptionForDataUpdationRecents(recents, service, customerid, operator, rechargeNumber, planBucket);
					return recentsRepository.updateRecentWhenNoExistingData(encRecents, ttl);
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : updateRecentWhenNoExistingData : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(ENCRYPTION, RECENT_INSERT_TO_DB_ERROR);
					throw new AES256Exception("updateRecentWhenNoExistingData : Error in decrypting the request");
				}
			}
		}
		return recentsRepository.updateRecentWhenNoExistingData(recents, ttl);
	}

	public List<Recents> findByCustomerIdAndService(
		Long customerId,
		String service
	) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if (isCCEncryptionRequired) {
				try {
					List<Recents> recentsList = recentsRepository.findByCustomerIdAndService(customerId, service);
					if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
						return recentsEncryptionHandler.decryptRecentList(recentsList);
					} else {
						return Collections.emptyList();
					}
				} catch (Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : findByCustomerIdAndService : Exception Occurred at the time of Encryption ");
					metricsHelper.pushToDD(DECRYPTION, RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("findByCustomerIdAndService: Error in decrypting the request");
				}
			}
		}
		return recentsRepository.findByCustomerIdAndService(customerId, service);
	}

	public List<Recents> findBycustomerIdAndserviceAndrechargeNumber(Long customerId, String service, String rechargeNumber) {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if (isCCEncryptionRequired) {
				try {
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<Recents> recentsList = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberIN(customerId, service, rechargeNumbersList);
					if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
						return recentsEncryptionHandler.decryptRecentList(recentsList);
					} else {
						return Collections.emptyList();
					}
				} catch (AES256Exception e) {
					logger.info("EncryptionHandlerForRecentsRepository : findBycustomerIdAndserviceAndrechargeNumber : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION,RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("findBycustomerIdAndserviceAndrechargeNumber ; Error in decrypting the request");
				}
			}
		}
		return recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(customerId, service, rechargeNumber);
	}

	public List<Recents> findBycustomerIdAndserviceAndrechargeNumberAndoperator(
		Long customerId,
		String service,
		String rechargeNumber,
		String operator
	) {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if (isCCEncryptionRequired) {
				try{
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<Recents> recentsList = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberINAndoperator(customerId, service, rechargeNumbersList, operator);
					if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
						return recentsEncryptionHandler.decryptRecentList(recentsList);
					} else {
						return Collections.emptyList();
					}
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : findBycustomerIdAndserviceAndrechargeNumberAndoperator : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION,RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("findBycustomerIdAndserviceAndrechargeNumberAndoperator : Error in decrypting the request");
				}
			}
		}
		return recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customerId, service, rechargeNumber, operator);
	}

	public List<Recents> findByCustomerId(Long customerId, int agentLimit) {
		boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
		if (isCCEncryptionRequired) {
			List<Recents> recentsList = recentsRepository.findByCustomerId(customerId, agentLimit);
			try{
				if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
					return recentsEncryptionHandler.decryptRecentList(recentsList);
				} else {
					return Collections.emptyList();
				}
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForRecentsRepository : findByCustomerId : Exception Occurred at the time of Encryption ", e);
				metricsHelper.pushToDD(DECRYPTION,RECENT_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerId : Error in decrypting the request");
			}
		}
		return recentsRepository.findByCustomerId(customerId, agentLimit);
	}

	public void deleteRecentByCustomerIdAndServiceAndRecharge(
		Recents recents,
		Long customerId,
		String service,
		String rechargeNumber,
		String operator,
		String planBucket
	) {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if (isCCEncryptionRequired) {
				try {
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					recentsRepository.deleteRecentByCustomerIdAndServiceAndRechargeIN(recents, customerId, service, rechargeNumbersList, operator, planBucket);
					return;
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : deleteRecentByCustomerIdAndServiceAndRecharge : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION, RECENT_DELETE_FROM_DB_ERROR);
					return;
				}
			}
		}
		recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(recents, customerId, service, rechargeNumber, operator, planBucket);
	}

	public void deleteRecentByCustomerIdAndServiceAndRechargeCdcListener(
		Recents recents,
		Long customerId,
		String service,
		String rechargeNumber,
		String operator,
		String planBucket
	) {
		recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(recents, customerId, service, rechargeNumber, operator, planBucket);
	}

	public List<Recents> findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(
		Long customerId,
		String service,
		String rechargeNumber,
		String operator,
		String planBucket
	) {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if (isCCEncryptionRequired) {
				try {
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<Recents> recentsList = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberINAndoperatorAndplanBucket(customerId, service, rechargeNumbersList, operator, planBucket);
					if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
						return recentsEncryptionHandler.decryptRecentList(recentsList);
					} else {
						return Collections.emptyList();
					}
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION, RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket : Error in decrypting the request");
				}
			}
		}
		return recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, service, rechargeNumber, operator, planBucket);
	}

	public List<Recents> findByCustomerIdAndServiceAndRechargeNumbersAndOperatorAndPlanBucket(
		Long customerId,
		String service,
		List<String> rechargeNumbers,
		String operator,
		String planBucket
	) {
		return recentsRepository.findBycustomerIdAndserviceAndrechargeNumberINAndoperatorAndplanBucket(customerId, service, rechargeNumbers, operator, planBucket);
	}

	public List<Recents> findBycustomerIdAndrechargeNumberAndservice(
		Long customerId,
		String rechargeNumber,
		String service
	) {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if (isCCEncryptionRequired) {
				try {
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<Recents> recentsList = recentsRepository.findBycustomerIdAndrechargeNumberINAndservice(customerId, rechargeNumbersList, service);
					if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
						return recentsEncryptionHandler.decryptRecentList(recentsList);
					} else {
						return Collections.emptyList();
					}
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : findBycustomerIdAndrechargeNumberAndservice : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION, RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("findBycustomerIdAndrechargeNumberAndservice : Error in decrypting the request");
				}

			}
		}
		return recentsRepository.findBycustomerIdAndrechargeNumberAndservice(customerId, rechargeNumber, service);
	}

	public List<Recents> selectNonCreditRecents(NickNameRequest nickNameRequest) {
		return recentsRepository.selectNonCreditRecents(nickNameRequest);
	}

	public List<Recents> selectCreditCardRecents(NickNameRequest nickNameRequest) {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(nickNameRequest.getService())) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(nickNameRequest.getCustomerId());
			if (isCCEncryptionRequired) {
				try {
					String rechargeNumber = nickNameRequest.getRechargeNumber();
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<Recents> recentsList = recentsRepository.selectCreditCardRecentsRechargeNumberIN(nickNameRequest, rechargeNumbersList);
					if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
						return recentsEncryptionHandler.decryptRecentList(recentsList);
					} else {
						return Collections.emptyList();
					}
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : selectCreditCardRecents : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION, RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("selectCreditCardRecents : Error in decrypting the request");
				}
			}
		}
		return recentsRepository.selectCreditCardRecents(nickNameRequest);
	}

	public List<Recents> selectPrepaidRecentsWithoutPlanBucket(
		Long customerid,
		String rechargeNumber,
		String service,
		String operator
	) {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerid);
			if (isCCEncryptionRequired) {
				try {
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<Recents> recentsList = recentsRepository.selectPrepaidRecentsWithoutPlanBucketRechargeNumberIN(customerid, rechargeNumbersList, service, operator);
					if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
						return recentsEncryptionHandler.decryptRecentList(recentsList);
					} else {
						return Collections.emptyList();
					}
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : selectPrepaidRecentsWithoutPlanBucket : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION, RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("selectPrepaidRecentsWithoutPlanBucket : Error in decrypting the request");
				}
			}
		}
		return recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerid, rechargeNumber, service, operator);
	}

	public Set<Recents> findCountByCustomerId(Long customerid, Integer limit) {
		return recentsRepository.findCountByCustomerId(customerid, limit);
	}

	List<Recents> findByCustomerIdINAndServiceAndRechargeNumberAndOperator(
		List<Long> customerid,
		String service,
		String rechargeNumber,
		String operator
	) {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			if (customerid.isEmpty()) {
				return Collections.emptyList();
			}
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerid.get(0));
			if (isCCEncryptionRequired) {
				try {
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<Recents> recentsList = recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberINAndOperator(customerid, service, rechargeNumbersList, operator);
					if (Objects.nonNull(recentsList) && !recentsList.isEmpty()) {
						return recentsEncryptionHandler.decryptRecentList(recentsList);
					} else {
						return Collections.emptyList();
					}
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : findByCustomerIdINAndServiceAndRechargeNumberAndOperator : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(DECRYPTION, RECENT_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("findByCustomerIdINAndServiceAndRechargeNumberAndOperator : Error in decrypting the request");
				}
			}
		}
		return recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(customerid, service, rechargeNumber, operator);
	}

	public Boolean updateRecentWhenDataAlreadyExist(Recents recents, Date oldUpdatedAt, Integer ttl) {
		String service = recents.getKey().getService();
		Long customerid = recents.getKey().getCustomerId();
		String operator = recents.getKey().getOperator();
		String rechargeNumber = recents.getKey().getRechargeNumber();
		String planBucket = recents.getKey().getPlanBucket();

		try {

			Recents encRecents = recents;
			if (FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerid)) {
				encRecents = handleEncryptionForDataUpdationRecents(recents, service, customerid, operator, rechargeNumber, planBucket);
			}

			boolean u = recentsRepository.updateRecentWhenDataAlreadyExist(encRecents, oldUpdatedAt, ttl);
			logger.info("updateRecentWhenDataAlreadyExist : updated recents for customerid {} with service {} and rechargeNumber {} and operator {} and planBucket {} ", customerid, service, rechargeNumber, operator, planBucket);
			return u;
		} catch (AES256Exception e) {
			logger.error("EncryptionHandlerForRecentsRepository : updateRecentWhenDataAlreadyExist : Exception Occurred at the time of Encryption ", e);
			metricsHelper.pushToDD(ENCRYPTION, RECENT_UPDATE_TO_DB_ERROR);
			throw new AES256Exception("updateRecentWhenDataAlreadyExist : Error in decrypting the request");
		}
	}

	public void updateRecentWhenDataAlreadyExistInInsert(Recents recents, Integer ttl) {
		String service = recents.getKey().getService();
		Long customerid = recents.getKey().getCustomerId();
		String operator = recents.getKey().getOperator();
		String rechargeNumber = recents.getKey().getRechargeNumber();
		String planBucket = recents.getKey().getPlanBucket();
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerid);
			if (isCCEncryptionRequired) {
				try {
					Recents encRecents = handleEncryptionForDataUpdationRecents(recents, service, customerid, operator, rechargeNumber, planBucket);
					recentsRepository.updateRecentWhenDataAlreadyExistInInsert(encRecents, ttl);
					return;
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerForRecentsRepository : updateRecentWhenDataAlreadyExistInInsert : Exception Occurred at the time of Encryption ", e);
					metricsHelper.pushToDD(ENCRYPTION, RECENT_UPDATE_TO_DB_ERROR);
					throw new AES256Exception("updateRecentWhenDataAlreadyExistInInsert : Error in decrypting the request");
				}
			}
		}
		recentsRepository.updateRecentWhenDataAlreadyExistInInsert(recents, ttl);
	}

	private Recents handleEncryptionForDataUpdationRecents(Recents recents, String service, Long customerid, String operator, String rechargeNumber, String planBucket) throws AES256Exception {
		List<Recents> recentsList = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerid, service, rechargeNumber, operator, planBucket);
		Recents foundRecent = recentsList.stream().findFirst().orElse(null);
		if (Objects.nonNull(foundRecent) && !recentsEncryptionHandler.isEncrypted(foundRecent)) {
			Integer foundRecentTtl = TTLUtils.getTTL(foundRecent.getKey().getService(), foundRecent.getTxnTime(), foundRecent.getCreatedAt());
			Recents encryptedRecents = recentsEncryptionHandler.encryptRecent(foundRecent);
			Boolean isInserted = recentsRepository.updateRecentWhenNoExistingData(encryptedRecents, foundRecentTtl);
			if (Boolean.TRUE.equals(isInserted)) {
				recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(foundRecent, foundRecent.getKey().getCustomerId(), foundRecent.getKey().getService(), foundRecent.getKey().getRechargeNumber(), foundRecent.getKey().getOperator(), foundRecent.getKey().getPlanBucket());
			}
		}
		return recentsEncryptionHandler.encryptRecent(recents);
	}

	public Boolean updateRecentsTxnTimes(Recents recents, Integer ttl) {
		logger.debug("updateRecentsTxnTimes starts");
		String service = recents.getKey().getService();
		Long customerid = recents.getKey().getCustomerId();
		String operator = recents.getKey().getOperator();
		String rechargeNumber = recents.getKey().getRechargeNumber();
		String planBucket = recents.getKey().getPlanBucket();
		boolean isCCEncryptionRequired = false;
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			 isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerid);
		}
		try {
			if (isCCEncryptionRequired)
				recents = handleEncryptionForDataUpdationRecents(recents, service, customerid, operator, rechargeNumber, planBucket);

			logger.debug("going to update recents  {} with  ttl {}", recents, ttl);
			return recentsRepository.updateRecentsTxnTimes(recents, ttl);
		} catch (AES256Exception e) {
			logger.info("EncryptionHandlerForRecentsRepository : updateRecentWhenDataAlreadyExist : Exception Occurred at the time of Encryption ", e);
			metricsHelper.pushToDD(ENCRYPTION, RECENT_UPDATE_TO_DB_ERROR);
			throw new AES256Exception("updateRecentWhenDataAlreadyExist : Error in decrypting the request");
		}
	}
}
