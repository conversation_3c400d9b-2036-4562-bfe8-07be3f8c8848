package com.paytm.saga.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.RemovePgTokenKafkaRequest;
import com.paytm.saga.dto.RemovePgTokenResponse;
import com.paytm.saga.producers.RemovePgTokenKafkaProducer;
import com.timgroup.statsd.StatsDClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import static com.paytm.saga.common.constant.Constants.MetricConstants.CONSUMER_EVENTS_ERROR_METRIC;
import static com.paytm.saga.common.constant.Constants.MetricConstants.CONSUMER_EVENTS_SUCCESS_METRIC;

@Service
public class RemovePgTokenService {

    private static final CustomLogger logger = CustomLogManager.getLogger(RemovePgTokenService.class);

    @Qualifier("monitoringClient")
    private final StatsDClient monitoringClient;

    private static final String REMOVE_PG_TOKEN_PRODUCER = "REMOVEPGTOKEN";

    private final RemovePgTokenKafkaProducer removePgTokenKafkaProducer;

    @Autowired
    public RemovePgTokenService(RemovePgTokenKafkaProducer removePgTokenKafkaProducer,StatsDClient monitoringClient) {
        this.removePgTokenKafkaProducer = removePgTokenKafkaProducer;
        this.monitoringClient=monitoringClient;
    }

    public RemovePgTokenResponse pushTopicToKafka(RemovePgTokenKafkaRequest payload)  {
        logger.info("Kafka Payload {}",payload);
        RemovePgTokenResponse response=new RemovePgTokenResponse();
        try {

            ObjectMapper objectMapper = new ObjectMapper();
            removePgTokenKafkaProducer.send(objectMapper.writeValueAsString(payload));
            pushToDD(CONSUMER_EVENTS_SUCCESS_METRIC);
            response.setMessage("SUCCESS");
        }
        catch (Exception ex){
            logger.error("Exception while sending to kafka {}",ex.getLocalizedMessage());
            pushToDD(CONSUMER_EVENTS_ERROR_METRIC);
            response.setErrorMessage("Unable to push Kafka Payload");
        }
        return response;

    }

    private void pushToDD(String metricName) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.SERVICE_NAME_KEY + ":" + REMOVE_PG_TOKEN_PRODUCER;
        monitoringClient.incrementCounter(metricName, tags);
    }


}
