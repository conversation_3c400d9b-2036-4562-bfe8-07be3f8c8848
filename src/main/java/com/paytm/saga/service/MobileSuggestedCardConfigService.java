package com.paytm.saga.service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfig;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FeatureConfigResponse;

@Component
public class MobileSuggestedCardConfigService {

    private final CustomLogger logger = CustomLogManager.getLogger(MobileSuggestedCardConfigService.class);

    private static final String SERVICE_CONFIG_KEY = "serviceConfig";

    @Value("${ruleEngine.host}")
    private String host;

    @Value("${ruleEngine.endpoints.offloadcpuData}")
    private String offloadcpuData;

    @Autowired
    @Qualifier("GenericRestClient")
    private GenericRestClient genericRestClient;

    @Autowired
    private MetricsHelper metricsHelper;

    public void setMobileSuggestedCardConfig() {
        try {
            logger.info("setMobileSuggestedCardConfig:: fetching mobile Suggested Card Config");

            FeatureConfigResponse response = genericRestClient.get(host, offloadcpuData, buildQueryParams(), null,
                    new ParameterizedTypeReference<FeatureConfigResponse>() {
                    });

            Integer responseStatus = Objects.nonNull(response) ? response.getStatus() : null;
            logger.info("setMobileSuggestedCardConfig:: mobile suggested Card Config fetched. ResponseStatus: {}", responseStatus);

            FeatureConfig suggestedCardConfig = new FeatureConfig();
            if (Objects.nonNull(response) && responseStatus == HttpStatus.OK.value() && Objects.nonNull(response.getData()) && !response.getData().isEmpty()) {
                suggestedCardConfig = response.getData().get(0);
                logger.info("setMobileSuggestedCardConfig:: mobile Suggested Card Config: {}", suggestedCardConfig);
            }

            FeatureConfigCache.getInstance().setMobileSuggestedCardsConfig(suggestedCardConfig);
        } catch (Exception ex) {
            logger.error("setMobileSuggestedCardConfig:: error while fetching mobile suggested card config", ex);
            metricsHelper.pushToDD("mobile_sugg_service_config_sync_error", null);
        }
    }

    private Map<String, Object> buildQueryParams() {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("key", SERVICE_CONFIG_KEY);
        queryParams.put("service_name", "mobileSuggestedCards");
        return queryParams;
    }
}