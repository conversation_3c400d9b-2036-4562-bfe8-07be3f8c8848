package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.util.AESUtil;
import com.paytm.saga.util.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.paytm.saga.common.constant.EncryptionConstants.*;

@Service
public class ReminderHistoryEncryptionHandler {
	private final CustomLogger logger = CustomLogManager.getLogger(ReminderHistoryEncryptionHandler.class);
	private final AESUtil aesUtil;
	private final MetricsHelper metricsHelper;

	@Autowired
	public ReminderHistoryEncryptionHandler(AESUtil aesUtil, MetricsHelper metricsHelper) {
		this.aesUtil = aesUtil;
		this.metricsHelper = metricsHelper;
	}

	public ReminderHistory encryptReminderHistory(ReminderHistory reminderHistoryToEncrypt) {
		ReminderHistory reminderHistory = new ReminderHistory();
		BeanUtils.copyProperties(reminderHistoryToEncrypt, reminderHistory, ReminderHistory.class);
		if(isEncrypted(reminderHistory)) {
			return reminderHistory;
		}
		if(Objects.nonNull(reminderHistory.getAmount())) {
			reminderHistory.setEncAmount(aesUtil.encrypt(String.valueOf(reminderHistory.getAmount())));
			reminderHistory.setAmount(null);
		}
		if(Objects.nonNull(reminderHistory.getCurrentMinBillAmount())) {
			reminderHistory.setEncCurrentMinBillAmount(aesUtil.encrypt(String.valueOf(reminderHistory.getCurrentMinBillAmount())));
			reminderHistory.setCurrentMinBillAmount(null);
		}
		if(Objects.nonNull(reminderHistory.getDue_date())) {
			reminderHistory.setEncDueDate(aesUtil.encrypt(convertDateToString(reminderHistory.getDue_date())));
			reminderHistory.setDue_date(null);
		}
		if(Objects.nonNull(reminderHistory.getOriginalAmount())) {
			reminderHistory.setEncOriginalAmount(aesUtil.encrypt(String.valueOf(reminderHistory.getOriginalAmount())));
			reminderHistory.setOriginalAmount(null);
		}
		if(Objects.nonNull(reminderHistory.getOriginalMinBillAmount())) {
			reminderHistory.setEncOriginalMinBillAmount(aesUtil.encrypt(String.valueOf(reminderHistory.getOriginalMinBillAmount())));
			reminderHistory.setOriginalMinBillAmount(null);
		}
		reminderHistory.setRechargeNumber(aesUtil.encrypt(reminderHistory.getRechargeNumber()));
		reminderHistory.setReference_id(aesUtil.encrypt(reminderHistory.getReference_id()));
		try {
			reminderHistory.setUserData(encryptUserData(reminderHistory));
		} catch (Exception e) {
			logger.error("Encryption of User Data Failed : ", e);
			metricsHelper.pushToDD(ENCRYPTION, USER_DATA);
		}
		reminderHistory.setIsEncrypted(1);
		return reminderHistory;
	}

	public List<ReminderHistory> decryptReminderHistory(List<ReminderHistory> reminderHistoryList) throws ParseException {
		List<ReminderHistory> decryptedReminderHistoryList = new ArrayList<>();
		for(ReminderHistory reminderHistory : reminderHistoryList) {
			decryptedReminderHistoryList.add(decryptReminderHistory(reminderHistory));
		}
		return decryptedReminderHistoryList;
	}

	public ReminderHistory decryptReminderHistory(ReminderHistory reminderHistory) throws ParseException {
		if(!isEncrypted(reminderHistory)) {
			return reminderHistory;
		}
		if(Objects.nonNull(reminderHistory.getEncAmount())) {
			reminderHistory.setAmount(Double.valueOf(aesUtil.decrypt(reminderHistory.getEncAmount())));
		}
		if(Objects.nonNull(reminderHistory.getEncCurrentMinBillAmount())) {
			reminderHistory.setCurrentMinBillAmount(Double.valueOf(aesUtil.decrypt(reminderHistory.getEncCurrentMinBillAmount())));
		}
		String decryptedDueDate = aesUtil.decrypt(reminderHistory.getEncDueDate());
		if(Objects.nonNull(decryptedDueDate)) {
			reminderHistory.setDue_date(convertStringToDate(decryptedDueDate));
		}
		if(Objects.nonNull(reminderHistory.getEncOriginalAmount())) {
			reminderHistory.setOriginalAmount(Double.valueOf(aesUtil.decrypt(reminderHistory.getEncOriginalAmount())));
		}
		if(Objects.nonNull(reminderHistory.getEncOriginalMinBillAmount())) {
			reminderHistory.setOriginalMinBillAmount(Double.valueOf(aesUtil.decrypt(reminderHistory.getEncOriginalMinBillAmount())));
		}
		reminderHistory.setRechargeNumber(aesUtil.decrypt(reminderHistory.getRechargeNumber()));
		reminderHistory.setReference_id(aesUtil.decrypt(reminderHistory.getReference_id()));
		try {
			reminderHistory.setUserData(decryptUserData(reminderHistory));
		} catch (Exception e) {
			metricsHelper.pushToDD(DECRYPTION, USER_DATA);
			logger.error("Decryption of User Data Failed :", e);
		}
		reminderHistory.setIsEncrypted(0);
		return reminderHistory;
	}

	private String decryptUserData(ReminderHistory reminderHistory) throws JsonProcessingException, AES256Exception {
		String userData = reminderHistory.getUserData();
		if (Objects.isNull(userData)) {
			return userData;
		}
		Set<String> userDataEncryptedKeys = JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject(USER_DATA_ENCRYPTED_KEYS));
		if (Objects.isNull(userDataEncryptedKeys) || userDataEncryptedKeys.isEmpty()) return userData;
		Map<String, Object> userDataMap = JsonUtils.parseJson(userData, Map.class);
		if (Objects.isNull(userDataMap) || userDataMap.isEmpty()) {
			return userData;
		}
		Map<String, Object> updatedUserData = new HashMap<>(userDataMap);
		userDataMap.forEach((key, value) -> {
			if (userDataEncryptedKeys.contains(key)) {
				updatedUserData.put(key, aesUtil.decrypt((String) value));
			}
		});
		return JsonUtils.serialiseJson(updatedUserData);
	}
	private String encryptUserData(ReminderHistory reminderHistory) throws JsonProcessingException,AES256Exception {
		String userData = reminderHistory.getUserData();
		if (Objects.isNull(userData)) {
			return userData;
		}
		Set<String> userDataEncryptedKeys = JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject(USER_DATA_ENCRYPTED_KEYS));
		if (Objects.isNull(userDataEncryptedKeys) || userDataEncryptedKeys.isEmpty()) return userData;
		Map<String, Object> userDataMap = JsonUtils.parseJson(userData, Map.class);
		if (Objects.isNull(userDataMap) || userDataMap.isEmpty()) {
			return userData;
		}
		Map<String, Object> updateduserData = new HashMap<>(userDataMap);
		userDataMap.forEach((key, value) -> {
			if (userDataEncryptedKeys.contains(key)) {
				updateduserData.put(key, aesUtil.encrypt((String) value));
			}
		});
		return JsonUtils.serialiseJson(updateduserData);
	}

	private String convertDateToString(Date date) {
		SimpleDateFormat formatter = new SimpleDateFormat(ENCRYPTION_DATE_FORMAT);
		return formatter.format(date);
	}

	private Date convertStringToDate(String dateString) throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat(ENCRYPTION_DATE_FORMAT);
		return formatter.parse(dateString);
	}

	public boolean isEncrypted(ReminderHistory reminderHistory) {
		return Objects.nonNull(reminderHistory) && reminderHistory.getIsEncrypted() !=null && reminderHistory.getIsEncrypted() == 1;
	}
}
