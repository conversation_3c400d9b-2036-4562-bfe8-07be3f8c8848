package com.paytm.saga.service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.UtilityThemeTypes;
import com.paytm.saga.dto.AutomaticCardView;
import com.paytm.saga.dto.ViewElementInfo;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class AutomaticCardService {

    private static final Logger logger = LogManager.getLogger(AutomaticCardService.class);
    public AutomaticCardView getAutomaticCard(Integer isAutomatic){
        logger.info("AutomaticCardService:addAutomaticCard function starts");
        AutomaticCardView automaticCard = null;
        if(Objects.nonNull(isAutomatic) && (isAutomatic==1 || isAutomatic==5)){
           automaticCard = addAutomaticEnabledCard(isAutomatic);
        }
        return automaticCard;
    }
    public AutomaticCardView addAutomaticEnabledCard(Integer isAutomatic){
        logger.info("AutomaticCardService:addAutomaticEnabledCard function starts");
        AutomaticCardView automaticCardView = new AutomaticCardView();
        automaticCardView.setThemeType(UtilityThemeTypes.CHAT_AUTOMATIC_ENABLED);
        setHeadings(automaticCardView,"header1",isAutomatic==1 ? Constants.AUTOMATIC_ENABLED : Constants.AUTOMATIC_RENEW);
        setCta(automaticCardView,"cta1","Manage", Constants.MANAGE_ACTION_TYPE);
        return automaticCardView;
    }
    public void setHeadings(AutomaticCardView automaticCardView, String type, String value){
        List<ViewElementInfo> headings = new ArrayList<>();
        ViewElementInfo heading = new ViewElementInfo();
        heading.setType(type);
        heading.setValue(value);
        headings.add(heading);
        automaticCardView.setHeadings(headings);
    }
    public void setCta(AutomaticCardView automaticCardView, String type, String value, String actionType){
        List<ViewElementInfo> cta = new ArrayList<>();
        ViewElementInfo cta1 = new ViewElementInfo();
        cta1.setType(type);
        cta1.setValue(value);
        cta1.setActionType(actionType);
        cta.add(cta1);
        automaticCardView.setCta(cta);
    }
}

