package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.repository.ChannelHistoryFinalizedRepository;
import com.paytm.saga.util.AESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static com.paytm.saga.common.constant.EncryptionConstants.*;

@Service
public class ChannelHistoryFinalizedRepositoryWrapperService {
	private final CustomLogger logger = CustomLogManager.getLogger(ChannelHistoryFinalizedRepositoryWrapperService.class);
	private final AESUtil aesUtil;
	private final ChannelHistoryFinalizedEncryptionHandler channelHistoryFinalizedEncryptionHandler;
	private final ChannelHistoryFinalizedRepository channelHistoryFinalizedRepository;
	private final MetricsHelper metricsHelper;

	@Autowired
	public ChannelHistoryFinalizedRepositoryWrapperService(
		AESUtil aesUtil,
		ChannelHistoryFinalizedEncryptionHandler channelHistoryFinalizedEncryptionHandler,
		ChannelHistoryFinalizedRepository channelHistoryFinalizedRepository, MetricsHelper metricsHelper
	) {
		this.aesUtil = aesUtil;
		this.channelHistoryFinalizedEncryptionHandler = channelHistoryFinalizedEncryptionHandler;
		this.channelHistoryFinalizedRepository = channelHistoryFinalizedRepository;
		this.metricsHelper = metricsHelper;
	}

	public Slice<ChannelHistoryFinalized> findByCustomerIdAndRechargeNumberAndService(
		Long customerId, String rechargeNumber,
		String service, PageRequest pageRequest
	) throws AES256Exception {
		if(FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)) {
			try {
				List<String> rechargeNumbers = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
				Slice<ChannelHistoryFinalized> encryptedChannelHistoryFinalizedSlice = channelHistoryFinalizedRepository
					.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumbers, service, pageRequest);
				return channelHistoryFinalizedEncryptionHandler.decryptChannelHistoryFinalizedSlice(encryptedChannelHistoryFinalizedSlice);
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryFinalizedRepositoryWrapper : findByCustomerIdAndRechargeNumberAndService : Error while decrypting the data", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_FINALIZED_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerIdAndRechargeNumberAndService: Error while decrypting the data from ChannelHistoryFinalized table");
			}
		}
		return channelHistoryFinalizedRepository.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, service, pageRequest);
	}

	public List<ChannelHistoryFinalized> findByCustomerIdAndRechargeNumberAndService(
		Long customerId, String rechargeNumber,
		String service
	) throws AES256Exception {
		if (FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)) {
			try {
				List<String> rechargeNumbers = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
				List<ChannelHistoryFinalized> encryptedChannelHistoryFinalizedList = channelHistoryFinalizedRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumbers, service);
				return channelHistoryFinalizedEncryptionHandler.decryptChannelHistoryFinalizedList(encryptedChannelHistoryFinalizedList);
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerForChannelHistoryFinalizedRepositoryWrapper : findByCustomerIdAndRechargeNumberAndService : Error while decrypting the data", e);
				metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_FINALIZED_SELECT_FROM_DB_ERROR);
				throw new AES256Exception("findByCustomerIdAndRechargeNumberAndService : Error while decrypting the data from ChannelHistoryFinalized table");
			}
		}
		return channelHistoryFinalizedRepository.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, service);
	}

	public Slice<ChannelHistoryFinalized> findAll(final CassandraPageRequest cassandraPageRequest) {
		try{
			return channelHistoryFinalizedEncryptionHandler.decryptChannelHistoryFinalizedSlice(channelHistoryFinalizedRepository.findAll(cassandraPageRequest));
		} catch (AES256Exception e) {
			logger.error("EncryptionHandlerForChannelHistoryFinalizedRepositoryWrapper : findAll : Error while decrypting the data", e);
			metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_FINALIZED_SELECT_FROM_DB_ERROR);
			throw new AES256Exception("findAll : Error while decrypting the data from ChannelHistoryFinalized table");
		}
	}

	public void saveAll(List<ChannelHistoryFinalized> channelHistoryFinalizedList, Integer ttl) throws AES256Exception {
		if (Objects.isNull(channelHistoryFinalizedList) || channelHistoryFinalizedList.isEmpty()) return;

		List<ChannelHistoryFinalized> finalChannelHistoryFinalizedList = new ArrayList<>();
		for (ChannelHistoryFinalized channelHistoryFinalized : channelHistoryFinalizedList) {
			if (FINANCIAL_SERVICES.equalsIgnoreCase(channelHistoryFinalized.getService()) && EncryptionDecision.isDataEncryptionRequired(channelHistoryFinalized.getCustomerId())) {
				try {
					ChannelHistoryFinalized encryptedChannelHistoryFinalized = channelHistoryFinalizedEncryptionHandler.encryptChannelHistoryFinalized(channelHistoryFinalized);
					if (encryptedChannelHistoryFinalized != null) {
						finalChannelHistoryFinalizedList.add(encryptedChannelHistoryFinalized);
					}
				} catch (AES256Exception e) {
					logger.error("ChannelHistoryFinalizedRepositoryWrapperService : saveAll : exception in encryption", e);
					metricsHelper.pushToDD(DECRYPTION, CHANNEL_HISTORY_FINALIZED_SAVE_ERROR);
					throw new AES256Exception("saveAll : Error while encrypting the data");
				}
			} else {
				finalChannelHistoryFinalizedList.add(channelHistoryFinalized);
			}
		}
		if (!finalChannelHistoryFinalizedList.isEmpty()) {
			channelHistoryFinalizedRepository.saveAll(finalChannelHistoryFinalizedList, ttl);
		}
	}
}
