package com.paytm.saga.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import com.paytm.saga.model.CommonCache;
import com.paytm.saga.repository.CommonCacheRepository;

@Service
public class CommonCacheService {
    private final CommonCacheRepository commonCacheRepository;


    @Autowired
    public CommonCacheService(@NonNull CommonCacheRepository commonCacheRepository
                       ) {
        this.commonCacheRepository=commonCacheRepository;
    }

    public List<CommonCache> getCache(String cacheKey){
        List<CommonCache> list = commonCacheRepository.findByCacheKey(cacheKey);
        return list;
    }

    public void setCache(String key, String value, Integer ttl){
        commonCacheRepository.save(new CommonCache(key , value), ttl);
    }

}