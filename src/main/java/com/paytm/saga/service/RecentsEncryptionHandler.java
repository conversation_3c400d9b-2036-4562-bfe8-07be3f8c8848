package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.util.AESUtil;
import com.paytm.saga.util.JsonUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.DoubleConsumer;
import java.util.function.Supplier;

import static com.paytm.saga.common.constant.EncryptionConstants.*;

@Service
public class RecentsEncryptionHandler {
	private final CustomLogger logger = CustomLogManager.getLogger(RecentsEncryptionHandler.class);
	private final AESUtil aesUtil;
	private final MetricsHelper metricsHelper;

	@Autowired
	public RecentsEncryptionHandler(AESUtil aesUtil, MetricsHelper metricsHelper) {
		this.aesUtil = aesUtil;
		this.metricsHelper = metricsHelper;
	}

	public List<Recents> decryptRecentList(List<Recents> recentsList) throws AES256Exception {
		if (Objects.isNull(recentsList) || recentsList.isEmpty()) return Collections.emptyList();
		List<Recents> decryptedRecentsList = new ArrayList<>();
		for (Recents recents : recentsList) {
			decryptedRecentsList.add(decryptRecent(recents));
		}
		return decryptedRecentsList;
	}

	public Recents decryptRecent(Recents recentsToDecrypt) throws AES256Exception {
		Recents recents = new Recents();
		BeanUtils.copyProperties(recentsToDecrypt, recents, Recents.class);
		recents.setKey(new RecentsPrimaryKey());
		BeanUtils.copyProperties(recentsToDecrypt.getKey(), recents.getKey(), RecentsPrimaryKey.class);
		if (!isEncrypted(recents)) return recents;
		RecentsPrimaryKey key = recents.getKey();
		if (Objects.nonNull(key) && Objects.nonNull(key.getRechargeNumber())) {
			decryptFieldToString(() -> recents.getKey().getRechargeNumber(), value -> recents.getKey().setRechargeNumber(value));
		}
		decryptFieldToDouble(recents::getEncDueAmount, recents::setDueAmount);
		decryptFieldToDouble(recents::getEncMinDueAmount, recents::setMinDueAmount);
		decryptFieldToDouble(recents::getEncOriginalDueAmount, recents::setOriginalDueAmount);
		decryptFieldToDouble(recents::getEncOriginalMinDueAmount, recents::setOriginalMinDueAmount);
		decryptFieldToDouble(recents::getEncCurrentOutstandingAmount, recents::setCurrentOutstandingAmount);
		decryptFieldToString(recents::getCardVariant, recents::setCardVariant);
		decryptFieldToString(recents::getConsumerName, recents::setConsumerName);
		if (Objects.nonNull(recents.getEncDueDate())) {
			try {
				recents.setDueDate(convertStringToDate(aesUtil.decrypt(recents.getEncDueDate())));
			} catch (ParseException e) {
				logger.error("Error while parsing the decrypted due date: encrypted due date: {}", recents.getEncDueDate());
				metricsHelper.pushToDD(DECRYPT_RECENT, DUE_DATE_PARSE_ERROR);
			}
		}
		decryptFieldToString(recents::getMcn, recents::setMcn);
		decryptFieldToString(recents::getCin, recents::setCin);
		decryptFieldToString(recents::getRechargeNumber2, recents::setRechargeNumber2);
		decryptFieldToString(recents::getRechargeNumber3, recents::setRechargeNumber3);
		decryptFieldToString(recents::getRechargeNumber4, recents::setRechargeNumber4);
		decryptFieldToString(recents::getRechargeNumber5, recents::setRechargeNumber5);
		decryptFieldToString(recents::getRechargeNumber6, recents::setRechargeNumber6);
		decryptFieldToString(recents::getRechargeNumber7, recents::setRechargeNumber7);
		decryptFieldToString(recents::getRechargeNumber8, recents::setRechargeNumber8);
		try {
			recents.setExtra(decryptExtra(recents));
		} catch (Exception e) {
			logger.error("Error while decrypting extra field: encrypted extra: {}", recents.getExtra());
			metricsHelper.pushToDD(DECRYPT_RECENT, PARSE_EXTRA_ERROR);
		}
		recents.setIsEncrypted(0);
		return recents;
	}

	public Recents encryptRecent(Recents recentsToEncrypt) throws AES256Exception {
		Recents recents = new Recents();
		BeanUtils.copyProperties(recentsToEncrypt, recents, Recents.class);
		recents.setKey(new RecentsPrimaryKey());
		BeanUtils.copyProperties(recentsToEncrypt.getKey(), recents.getKey(), RecentsPrimaryKey.class);
		if (isEncrypted(recents)) {
			return recents;
		}
		if (Objects.nonNull(recents.getKey()) && (Objects.nonNull(recents.getKey().getRechargeNumber()))) {
			recents.getKey().setRechargeNumber(aesUtil.encrypt((recents.getKey().getRechargeNumber())));
		}
		encryptFieldToDouble(recents::getDueAmount, recents::setEncDueAmount, recents::setDueAmount);
		encryptFieldToDouble(recents::getMinDueAmount, recents::setEncMinDueAmount, recents::setMinDueAmount);
		encryptFieldToDouble(recents::getOriginalDueAmount, recents::setEncOriginalDueAmount, recents::setOriginalDueAmount);
		encryptFieldToDouble(recents::getOriginalMinDueAmount, recents::setEncOriginalMinDueAmount, recents::setOriginalMinDueAmount);
		encryptFieldToDouble(recents::getCurrentOutstandingAmount, recents::setEncCurrentOutstandingAmount, recents::setCurrentOutstandingAmount);
		encryptFieldToString(recents::getCardVariant, recents::setCardVariant);
		encryptFieldToString(recents::getConsumerName, recents::setConsumerName);
		if (Objects.nonNull(recents.getDueDate())) {
			recents.setEncDueDate((aesUtil.encrypt(convertDateToString(recents.getDueDate()))));
			recents.setDueDate(null);
		}
		encryptFieldToString(recents::getMcn, recents::setMcn);
		encryptFieldToString(recents::getCin, recents::setCin);
		encryptFieldToString(recents::getRechargeNumber2, recents::setRechargeNumber2);
		encryptFieldToString(recents::getRechargeNumber3, recents::setRechargeNumber3);
		encryptFieldToString(recents::getRechargeNumber4, recents::setRechargeNumber4);
		encryptFieldToString(recents::getRechargeNumber5, recents::setRechargeNumber5);
		encryptFieldToString(recents::getRechargeNumber6, recents::setRechargeNumber6);
		encryptFieldToString(recents::getRechargeNumber7, recents::setRechargeNumber7);
		encryptFieldToString(recents::getRechargeNumber8, recents::setRechargeNumber8);
		try {
			recents.setExtra(encryptExtra(recents));
		} catch (Exception e) {
			logger.error("Error while encrypting extra field: extra: {}", recents.getExtra());
			metricsHelper.pushToDD(ENCRYPT_RECENT, PARSE_EXTRA_ERROR);
		}
		recents.setIsEncrypted(1);
		return recents;
	}

	private void decryptFieldToDouble(Supplier<String> getter, DoubleConsumer setter) throws AES256Exception {
		String encryptedValue = getter.get();
		if (Objects.nonNull(encryptedValue)) {
			setter.accept(Double.parseDouble(aesUtil.decrypt(encryptedValue)));
		}
	}

	private void decryptFieldToString(Supplier<String> getter, Consumer<String> setter) throws AES256Exception {
		String encryptedValue = getter.get();
		if (Objects.nonNull(encryptedValue) && !encryptedValue.isEmpty()) {
			setter.accept(aesUtil.decrypt(encryptedValue));
		}
	}

	private void encryptFieldToString(Supplier<String> getter, Consumer<String> setter) throws AES256Exception {
		String value = getter.get();
		if (Objects.nonNull(value) && !value.isEmpty()) {
			setter.accept(aesUtil.encrypt(value));
		}
	}

	private void encryptFieldToDouble(Supplier<Double> getter, Consumer<String> encSetter, Consumer<Double> setter) throws AES256Exception {
		Double value = getter.get();
		if(Objects.isNull(value)) return;
		encSetter.accept(aesUtil.encrypt(String.valueOf(value)));
		setter.accept(null);
	}

	private String encryptExtra(Recents recents) throws JsonProcessingException,AES256Exception {
		String extra = recents.getExtra();
		if (Objects.isNull(extra)) {
			return extra;
		}
		Set<String> extraEncryptedKeys = JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS));
		if (Objects.isNull(extraEncryptedKeys) || extraEncryptedKeys.isEmpty()) return extra;
		Map<String, Object> recentExtra = JsonUtils.parseJson(extra, Map.class);
		if (Objects.isNull(recentExtra) || recentExtra.isEmpty()) {
			return extra;
		}
		Map<String, Object> updatedRecentExtra = new HashMap<>(recentExtra);
		recentExtra.forEach((key, value) -> {
			if (extraEncryptedKeys.contains(key)) {
				updatedRecentExtra.put(key, aesUtil.encrypt((String) value));
			}
		});
		return JsonUtils.serialiseJson(updatedRecentExtra);
	}

	private String decryptExtra(Recents recents) throws JsonProcessingException, AES256Exception {
		String extra = recents.getExtra();
		if (Objects.isNull(extra)) {
			return extra;
		}
		Set<String> extraEncryptedKeys = JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS));
		if (Objects.isNull(extraEncryptedKeys) || extraEncryptedKeys.isEmpty()) return extra;
		Map<String, Object> recentExtra = JsonUtils.parseJson(extra, Map.class);
		if (Objects.isNull(recentExtra) || recentExtra.isEmpty()) {
			return extra;
		}
		Map<String, Object> updatedRecentExtra = new HashMap<>(recentExtra);
		recentExtra.forEach((key, value) -> {
			if (extraEncryptedKeys.contains(key)) {
				updatedRecentExtra.put(key, aesUtil.decrypt((String) value));
			}
		});
		return JsonUtils.serialiseJson(updatedRecentExtra);
	}

	private String convertDateToString(Date date) {
		SimpleDateFormat formatter = new SimpleDateFormat(ENCRYPTION_DATE_FORMAT);
		return formatter.format(date);
	}

	private Date convertStringToDate(String dateString) throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat(ENCRYPTION_DATE_FORMAT);
		return formatter.parse(dateString);
	}

	public boolean isEncrypted(Recents recents) {
		return Objects.nonNull(recents) && Objects.nonNull(recents.getIsEncrypted()) && recents.getIsEncrypted() == 1;
	}
}
