package com.paytm.saga.service;

import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.util.JsonUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Set;

import static com.paytm.saga.common.constant.EncryptionConstants.*;

@Service
public class EncryptionDecision {
	public static boolean isDataEncryptionRequired(Long customerId) {
		if (Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean(ENABLE_DATA_ENCRYPTION_FLAG))) {
			return true;
		} else if (FeatureConfigCache.getInstance().getInteger(ALLOWED_ROLLOUT_PERCENTAGE) != null && (customerId % 100 <= FeatureConfigCache.getInstance().getInteger(ALLOWED_ROLLOUT_PERCENTAGE))) {
			return true;
		} else if (Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean(ENABLE_DATA_ENCRYPTION_ON_CUSTOMER_FLAG))) {
			Set<Long> encryptionOnCustomerIds = JsonUtils.convertObjectToSet(FeatureConfigCache.getInstance().getObject(ENCRYPTION_ON_CUSTOMERIDS));
			return Objects.nonNull(encryptionOnCustomerIds) && encryptionOnCustomerIds.contains(customerId);
		}
		return false;
	}
}
