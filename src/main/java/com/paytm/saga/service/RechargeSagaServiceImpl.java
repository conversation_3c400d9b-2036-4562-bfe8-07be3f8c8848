package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.GetHistoryPageDTO;
import com.paytm.saga.dto.HistoryPage;
import com.paytm.saga.dto.ThemeResponse;
import com.paytm.saga.service.aggregator.AggregatorServiceInterface;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.NATIVE_THEME_HASH;

@Service
public class RechargeSagaServiceImpl implements RechargeSagaService {

    private static final CustomLogger logger = CustomLogManager.getLogger(RechargeSagaServiceImpl.class);

    @Autowired
    private ChannelHistoryService channelHistoryService;

    @Autowired
    private  NewThemeService newthemeService;


    @Autowired
    @Qualifier("UtilityAggregatorServiceImpl")
    private AggregatorServiceInterface utilityAggregator;


    @Override
    public HistoryPage getHistoryV2(GetHistoryPageDTO historyPageRequest) {
        logger.trace("RechargeSagaServiceImpl getHistoryV2 starts");

        String previousCardOperator = null;
        Date previousDate = null;
        HistoryPage historyPage = null;

        if (Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean("disableChatHistory"))) {
            logger.info("Returning empty response as disableChatHistory");
            historyPage = CommonUtils.getDefaultHistoryResponse(historyPageRequest);
        } else {
            historyPageRequest.setOperator(StringUtils.lowerCase(historyPageRequest.getOperator()));
            historyPageRequest.setService(StringUtils.lowerCase(historyPageRequest.getService()));

            if (historyPageRequest.getDedupeids() != null && historyPageRequest.getDedupeids().length >= 2) {
                previousCardOperator = historyPageRequest.getDedupeids()[1] != null
                        ? historyPageRequest.getDedupeids()[1].toLowerCase()
                        : historyPageRequest.getDedupeids()[1];
                previousDate = DateUtil.stringToDate(historyPageRequest.getDedupeids()[0], DateFormats.DATE_FORMAT);
            }
            historyPage = utilityAggregator.aggregateHistoryInfo(historyPageRequest, previousCardOperator, previousDate);
            historyPage.getChannelDetails().setRechargeNumber2(historyPageRequest.getRecharge_number_2());
            historyPage.getChannelDetails().setRechargeNumber3(historyPageRequest.getRecharge_number_3());
            historyPage.getChannelDetails().setRechargeNumber4(historyPageRequest.getRecharge_number_4());

            if(historyPageRequest.isNative()){
                if(!(NATIVE_THEME_HASH.equals(historyPageRequest.getThemehash())))
                    historyPage.setThemeDetails(newthemeService.getThemesForNativeCategory());
                else
                    historyPage.setThemeDetails(new ThemeResponse());
            }else {
                historyPage.setThemeDetails(newthemeService.getThemes(historyPageRequest.getThemehash()));
            }
        }
        logger.trace("returning response for customer_id " + historyPageRequest.getCustomerId() + " recharge number "
                + historyPageRequest.getRecharge_number() + " code " + historyPage.getCode() + " number of records "
                + historyPage.getCount());
        return historyPage;
    }
}
