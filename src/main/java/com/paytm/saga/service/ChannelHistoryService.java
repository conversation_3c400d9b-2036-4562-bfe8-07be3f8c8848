package com.paytm.saga.service;

import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.List;
import java.util.Arrays;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.util.TTLUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;

import com.datastax.oss.driver.api.core.cql.PagingState;
import com.paytm.saga.dto.ResponsePage;
import com.paytm.saga.model.ChannelHistory;

@Service
public class ChannelHistoryService {
	private static final CustomLogger logger = CustomLogManager.getLogger(ChannelHistoryService.class);

	private final ChannelHistoryRepositoryWrapperService channelHistoryRepository;

	@Autowired
	private MetricsHelper metricsHelper;

	@Autowired
	public ChannelHistoryService(final ChannelHistoryRepositoryWrapperService channelHistoryRepository) {
		this.channelHistoryRepository = channelHistoryRepository;
	}

	public List<ChannelHistory> getPageOfHistory(final long customerId, final String recharge_number,
			final String service) {
		
		logger.info("getting history from for customer id" + customerId);
		final List<ChannelHistory> historySlice = channelHistoryRepository
				.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(customerId, recharge_number, service,false);
		return historySlice;
	}

	public List<ChannelHistory> getPageOfHistory(final long customerId, final String recharge_number,
												 final String service, final String operator) {

		logger.info("getting history from for customer id" + customerId);
		final List<ChannelHistory> historySlice = channelHistoryRepository
				.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedStateAndOperator(customerId, recharge_number, service,false, operator);
		return historySlice;
	}

	public ResponsePage<ChannelHistory> getPageOfHistory(final Integer limit, final String pagingState) {
		final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(limit, pagingState);
		return getPageOfHistory(cassandraPageRequest);
	}

	public ResponsePage<ChannelHistory> getPageOfHistory(final CassandraPageRequest cassandraPageRequest) {
		final Slice<ChannelHistory> historySlice = channelHistoryRepository.findAll(cassandraPageRequest);
		return new ResponsePage<>(historySlice);
	}

	private CassandraPageRequest createCassandraPageRequest(final Integer limit, final String pagingState) {
		final PageRequest pageRequest = PageRequest.of(0, limit);
		if (pagingState != null) {
			try {
				logger.info("Processing paging state: length={}", pagingState.length());

				// Try Base64 decoding first (new format)
				byte[] decodedBytes = Base64.getDecoder().decode(pagingState);
				ByteBuffer pagingStateBuffer = ByteBuffer.wrap(decodedBytes);
				metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_SERVICE);
				return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
			} catch (Exception e) {
				logger.error("Failed to parse paging state - length: {}, error: {}",
						pagingState.length(), e.getMessage());
				// Try fallback to old format for backward compatibility
				try {
					logger.info("Attempting fallback to PagingState.fromString format");
					final PagingState pageState = PagingState.fromString(pagingState);
					ByteBuffer pagingStateBuffer = ByteBuffer.wrap(pageState.toBytes());
					metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_SERVICE);
					return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
				} catch (Exception fallbackException) {
					logger.error("Fallback parsing also failed: {}", fallbackException.getMessage());
					metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_SERVICE);
					return CassandraPageRequest.of(pageRequest, null);
				}
			}
		}

		metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_SERVICE_DEFAULT);
		return CassandraPageRequest.of(pageRequest, null);
	}
	public void save(ChannelHistory channelHistory, String service) {
		Integer ttl = TTLUtils.getTTL(service);
		logger.info("[ChannelHistoryService.save]:: TTl : {}",ttl);
		channelHistoryRepository.save(channelHistory,ttl);
	}
	public void saveAll(List<ChannelHistory> channelHistorList, String service) {
		Integer ttl = TTLUtils.getTTL(service);
		logger.info("[ChannelHistoryService.saveAll] CustomerId : {} , RechargeNumber : {} , TTl : {}",channelHistorList.get(0).getCustomerId(),channelHistorList.get(0).getRechargeNumber(),ttl);
		channelHistoryRepository.saveAll(channelHistorList,ttl);
	}
	
	public ResponsePage<ChannelHistory> getPageOfHistory(final long customerId, final String recharge_number,
			final String service, final Integer limit, final String pagingState) {
		logger.info("getting history from finalized table for customer id" + customerId);
		final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(limit, pagingState);
		final Slice<ChannelHistory> historySlice = channelHistoryRepository
				.findByCustomerIdAndRechargeNumberAndService(customerId, recharge_number, service,
						cassandraPageRequest);
		return new ResponsePage<>(historySlice);
	}


}
