package com.paytm.saga.service;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.FetchRecentsRequest;
import com.paytm.saga.dto.FetchRecentsResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class FetchRecentsService {

    @Autowired
    RecentsService recentsService;

    public FetchRecentsResponse fetchRecents(FetchRecentsRequest fetchRecentsRequest) throws RechargeSagaBaseException {
        if (Objects.nonNull(fetchRecentsRequest)){
            if(Objects.nonNull(fetchRecentsRequest.getCustomerId())){
                return recentsService.fetchRecentsByCustomerId(fetchRecentsRequest);
            }
            else{
                return recentsService.fetchRecentsByRechargeNumber(fetchRecentsRequest);
            }
        }
        return null;
    }
}
