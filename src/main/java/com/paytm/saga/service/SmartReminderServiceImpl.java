package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.NumberConstants;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.builder.RecentPrimaryKeyBuilder;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.RecentUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.MetricConstants.FREQUENT_ORDER;

@Service("SmartReminderService")
public class SmartReminderServiceImpl implements SmartReminderService {

    private final CustomLogger logger = CustomLogManager.getLogger(SmartReminderServiceImpl.class);

    @Autowired
    CustomerBillRepository customerBillRepository;

    @Autowired
    @Qualifier("monitoringClient")
    private StatsDClient monitoringClient;

    @Autowired
    private ServiceConfig serviceConfig;

    @Override
    public List<RecentsPrimaryKey> getFilteredRecentTableClusterKeys(FrequentOrderRequest request) {
        logger.info("getRecentTableClusterKeys starts");

        List<String> oldBillEnabledServices = serviceConfig.getOldBillEnabledServices();
        Integer maxBillVisibilityDays = serviceConfig.getBillVisiblityDays();

        for(String service: oldBillEnabledServices){
            maxBillVisibilityDays = Integer.max(maxBillVisibilityDays, serviceConfig.getBillVisiblityDays(service));
        }

        Date billDueDateStartRange = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -maxBillVisibilityDays));

        logger.trace("Query billDueDateStartRange is {}", billDueDateStartRange);

        List<CustomerBill> customerBills = customerBillRepository.findByCustomerIdAndDueDate(request.getCustomerId(), billDueDateStartRange, serviceConfig.getAgentLimit() + 1);

        logger.debug("getRecentTableClusterKeys size is  {} ", customerBills.size());

        List<RecentsPrimaryKey> keys = CommonUtils.collectionToStream(customerBills).filter(RecentUtils.getCustomerBillFilterPredicate(request)).map(customerBill -> RecentPrimaryKeyBuilder.generateRecentsPrimaryKey.apply(customerBill)).collect(Collectors.toList());

        pushCountToDD(FREQUENT_ORDER, customerBills.size() - keys.size(), "CUSTOMER_BILL_RECENT_KEYS_DIFF");


        return keys;

    }

    private void pushCountToDD(String metricName, Integer count, String state) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.STATE + ":" + state;
        monitoringClient.count(metricName, count, tags);
    }

}
