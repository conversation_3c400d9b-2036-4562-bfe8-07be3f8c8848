package com.paytm.saga.service;

import com.google.common.base.Strings;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.util.AESUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ChannelHistoryFinalizedEncryptionHandler {
	private final AESUtil aesUtil;

	@Autowired
	public ChannelHistoryFinalizedEncryptionHandler(AESUtil aesUtil, MetricsHelper metricsHelper) {
		this.aesUtil = aesUtil;
	}

	public List<ChannelHistoryFinalized> encryptChannelHistoryFinalizedList(List<ChannelHistoryFinalized> channelHistoryFinalizedList) throws AES256Exception {
		List<ChannelHistoryFinalized> encryptedChannelHistoryFinalizedList = new ArrayList<>();
		for (ChannelHistoryFinalized channelHistoryFinalized : channelHistoryFinalizedList) {
			encryptedChannelHistoryFinalizedList.add(encryptChannelHistoryFinalized(channelHistoryFinalized));
		}
		return encryptedChannelHistoryFinalizedList;
	}

	public ChannelHistoryFinalized encryptChannelHistoryFinalized(ChannelHistoryFinalized channelHistoryFinalizedToEncrypt) throws AES256Exception {
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		BeanUtils.copyProperties(channelHistoryFinalizedToEncrypt, channelHistoryFinalized, ChannelHistoryFinalized.class);
		if (channelHistoryFinalizedToEncrypt.getBillsObj() != null) {
			channelHistoryFinalized.setBillsObj(new HashMap<>(channelHistoryFinalizedToEncrypt.getBillsObj()));
		}
		if (isEncrypted(channelHistoryFinalized)) {
			return channelHistoryFinalized;
		}
		if (!Strings.isNullOrEmpty(channelHistoryFinalized.getAmount())) {
			channelHistoryFinalized.setAmount(aesUtil.encrypt(channelHistoryFinalized.getAmount()));
		}
		if (!Strings.isNullOrEmpty(channelHistoryFinalized.getRechargeNumber())) {
			channelHistoryFinalized.setRechargeNumber(aesUtil.encrypt(channelHistoryFinalized.getRechargeNumber()));
		}
		if (!Strings.isNullOrEmpty(channelHistoryFinalized.getRecharge_number_3())) {
			channelHistoryFinalized.setRecharge_number_3(aesUtil.encrypt(channelHistoryFinalized.getRecharge_number_3()));
		}
		if (Objects.nonNull(channelHistoryFinalized.getBillsObj())) {
			Map<String, String> encryptedBillsObj = new HashMap<>();
			channelHistoryFinalized.getBillsObj().forEach((key, value) -> encryptedBillsObj.put(key, aesUtil.encrypt(value)));
			channelHistoryFinalized.setBillsObj(encryptedBillsObj);
		}
		channelHistoryFinalized.setIsEncrypted(1);
		return channelHistoryFinalized;
	}

	public Slice<ChannelHistoryFinalized> decryptChannelHistoryFinalizedSlice(Slice<ChannelHistoryFinalized> channelHistoryFinalizedList) throws AES256Exception {
		List<ChannelHistoryFinalized> decryptedChannelHistoryFinalizedList = new ArrayList<>();
		for (ChannelHistoryFinalized channelHistoryFinalized : channelHistoryFinalizedList.getContent()) {
			decryptedChannelHistoryFinalizedList.add(decryptChannelHistoryFinalized(channelHistoryFinalized));
		}
		return new SliceImpl<>(decryptedChannelHistoryFinalizedList, channelHistoryFinalizedList.getPageable(), channelHistoryFinalizedList.hasNext());
	}

	public List<ChannelHistoryFinalized> decryptChannelHistoryFinalizedList(List<ChannelHistoryFinalized> channelHistoryFinalizedList) throws AES256Exception {
		List<ChannelHistoryFinalized> decryptedChannelHistoryFinalizedList = new ArrayList<>();
		for (ChannelHistoryFinalized channelHistoryFinalized : channelHistoryFinalizedList) {
			decryptedChannelHistoryFinalizedList.add(decryptChannelHistoryFinalized(channelHistoryFinalized));
		}
		return decryptedChannelHistoryFinalizedList;
	}

	public ChannelHistoryFinalized decryptChannelHistoryFinalized(ChannelHistoryFinalized channelHistoryFinalizedToDecrypt) throws AES256Exception {
		if (!isEncrypted(channelHistoryFinalizedToDecrypt)) {
			return channelHistoryFinalizedToDecrypt;
		}
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		BeanUtils.copyProperties(channelHistoryFinalizedToDecrypt, channelHistoryFinalized, ChannelHistoryFinalized.class);
		if (channelHistoryFinalizedToDecrypt.getBillsObj() != null) {
			channelHistoryFinalized.setBillsObj(new HashMap<>(channelHistoryFinalizedToDecrypt.getBillsObj()));
		}
		if (!Strings.isNullOrEmpty(channelHistoryFinalized.getAmount())) {
			channelHistoryFinalized.setAmount(aesUtil.decrypt(channelHistoryFinalized.getAmount()));
		}
		if (!Strings.isNullOrEmpty(channelHistoryFinalized.getRechargeNumber())) {
			channelHistoryFinalized.setRechargeNumber(aesUtil.decrypt(channelHistoryFinalized.getRechargeNumber()));
		}
		if (!Strings.isNullOrEmpty(channelHistoryFinalized.getRecharge_number_3())) {
			channelHistoryFinalized.setRecharge_number_3(aesUtil.decrypt(channelHistoryFinalized.getRecharge_number_3()));
		}
		if (Objects.nonNull(channelHistoryFinalized.getBillsObj())) {
			Map<String, String> decryptedBillsObj = new HashMap<>();
			channelHistoryFinalized.getBillsObj().forEach((key, value) -> decryptedBillsObj.put(key, aesUtil.decrypt(value)));
			channelHistoryFinalized.setBillsObj(decryptedBillsObj);
		}
		channelHistoryFinalized.setIsEncrypted(0);
		return channelHistoryFinalized;
	}

	private boolean isEncrypted(ChannelHistoryFinalized channelHistoryFinalized) {
		return Objects.nonNull(channelHistoryFinalized) && Objects.nonNull(channelHistoryFinalized.getIsEncrypted()) && channelHistoryFinalized.getIsEncrypted() == 1;
	}

}
