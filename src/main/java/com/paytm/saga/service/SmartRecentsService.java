package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.SmartRecentDropOffRequest;
import com.paytm.saga.dto.SmartRecentDropoffResponse;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class SmartRecentsService {

    private final CustomLogger logger = CustomLogManager.getLogger(SmartRecentsService.class);

    private SmartRecentsRepository smartRecentsRepository;

    private KafkaProducerService kafkaProducerService;

    @Autowired
    public SmartRecentsService(@NonNull SmartRecentsRepository smartRecentsRepository, KafkaProducerService kafkaProducerService){
        this.smartRecentsRepository = smartRecentsRepository;
        this.kafkaProducerService = kafkaProducerService;

    }
    public List<SmartRecents> getSmartRecentsForFrequentOrderCLP(FrequentOrderRequest frequentOrderRequest){
        List<SmartRecents> smartRecentsList=null;
        try{
            smartRecentsList = smartRecentsRepository.findByCustomerIdAndService(frequentOrderRequest.getCustomerId(),frequentOrderRequest.getServices().get(0));
        }
        catch (Exception e){
            logger.error("[SmartRecentsService.getSmartRecentsForFrequentOrderCLP]:Exception occured while fetching data from smart_recents table, e = ",e.getMessage());
        }
        return smartRecentsList;
    }

    public List<SmartRecents> getSmartRecents(FrequentOrderRequest frequentOrderRequest, List<String> services){
        List<SmartRecents> smartRecentsList=null;
        try{
            smartRecentsList = smartRecentsRepository.findByCustomerIdAndServices(frequentOrderRequest.getCustomerId(),services);
        }
        catch (Exception e){
            logger.error("[SmartRecentsService.getSmartRecentsForCustomer]:Exception occured while fetching data from smart_recents table, e = ",e.getMessage());
        }
        return smartRecentsList;
    }
    public void save(SmartRecents smartRecents){
        try{
            smartRecentsRepository.save(smartRecents);
            logger.info("saving successfull for customerId : " + smartRecents.getKey().getCustomerId() + " service : " + smartRecents.getKey().getService() + " operator : " + smartRecents.getKey().getOperator());
            invalidateCache(smartRecents);
        }
        catch (Exception e){
            logger.error("[SmartRecentsService.save] :: error in saving database for customerId : " + smartRecents.getKey().getCustomerId() + " service : " + smartRecents.getKey().getService() + " operator : " + smartRecents.getKey().getOperator());
        }
    }

    public SmartRecentDropoffResponse saveSmartRecentDropOff(SmartRecentDropOffRequest request) {
        SmartRecents smartRecents = new SmartRecents();
        smartRecents.setProductId(request.getProductId());
        SmartRecentsPrimaryKey smartRecentsPrimaryKey = new SmartRecentsPrimaryKey();
        smartRecentsPrimaryKey.setService(request.getService().toLowerCase());
        smartRecentsPrimaryKey.setOperator(request.getOperator().toLowerCase());
        smartRecentsPrimaryKey.setCustomerId(request.getCustomerId());
        smartRecents.setKey(smartRecentsPrimaryKey);
        smartRecents.setRechargeNumber(request.getRechargeNumber());
        smartRecents.setEventSource(Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF);
        smartRecents.setCreatedAt(new Date());
        smartRecents.setUpdatedAt(new Date());
        ProductMin product = CVRProductCache.getInstance().getProductDetails(request.getProductId());
        if (product != null) {
            smartRecents.setPayType(product.getPayType().toLowerCase());
        }
        SmartRecentDropoffResponse smartRecentDropoffResponse = new SmartRecentDropoffResponse(200, "success");
        try {
            smartRecentsRepository.save(smartRecents);
            invalidateCache(smartRecents);
        } catch (Exception e) {
            logger.error("[SmartRecentsService.saveSmartRecentDropOff] :: Exception occured while saving data in smart_recents table data {}, error message {} ", request, e.getMessage());
            smartRecentDropoffResponse.setStatusCode(500);
            smartRecentDropoffResponse.setErrorMessage("error in saving");
        }
        return smartRecentDropoffResponse;
    }
    public void delete(SmartRecents smartRecents){
        try{
            smartRecentsRepository.delete(smartRecents);
            logger.info("deleting successfull for customerId : " + smartRecents.getKey().getCustomerId() + " service : " + smartRecents.getKey().getService() + " operator : " + smartRecents.getKey().getOperator());
            invalidateCache(smartRecents);
        }
        catch(Exception e){
            logger.error("[SmartRecentsService.save] :: error in deleting database for customerId : " + smartRecents.getKey().getCustomerId() + " service : " + smartRecents.getKey().getService() + " operator : " + smartRecents.getKey().getOperator());
        }
    }

    private void invalidateCache(SmartRecents smartRecents){
        try {
            kafkaProducerService.sendMessage(Long.toString(smartRecents.getKey().getCustomerId()));
        } catch (Exception e) {
            logger.error("[SmartRecentsService.invalidateCache] kafka cache clean event publish error for data {}, Exception {}",
                    smartRecents.toString(), e);
        }
    }
}
