package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 */

@Service("com.paytm.saga.service.InsuranceRestTemplateService")
public class InsuranceRestTemplateService extends RestTemplateService{
    private static final CustomLogger logger = CustomLogManager.getLogger(InsuranceRestTemplateService.class);

    @Autowired
    public InsuranceRestTemplateService(@NonNull @Qualifier("insuranceRestTemplateClient") RestTemplate restTemplate) {
        super(restTemplate);
    }
}


