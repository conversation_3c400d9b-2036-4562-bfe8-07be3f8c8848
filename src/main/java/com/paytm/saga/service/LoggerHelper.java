package com.paytm.saga.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.util.AESUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICE;
import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;

@Service
public class LoggerHelper {
	private Logger logger = LogManager.getLogger(LoggerHelper.class);

	@Autowired
	private AESUtil aesUtil;
	@Autowired
	private RecentsEncryptionHandler recentsEncryptionHandler;
	@Autowired
	private ChannelHistoryEncryptionHandler channelHistoryEncryptionHandler;
	@Autowired
	private ChannelHistoryFinalizedEncryptionHandler channelHistoryFinalizedEncryptionHandler;
	@Autowired
	private ReminderHistoryEncryptionHandler reminderHistoryEncryptionHandler;
	@Autowired
	private ObjectMapper objectMapper;

	private static final HashSet<String> sensitiveFields = new HashSet<>(Arrays.asList(
		"subscriberNumber",
		"subscriberName",
		"subscriberEmailId",
		"subscriberDOB",
		"subscriberAddress",
		"subscriberAltNumber",
		"subscriberGender",
		"subscriberCity",
		"minReloadAmount",
		"currentBillAmount",
		"currentMinBillAmount",
		"current_outstanding_amount",
		"currentOutstandingAmount",
		"billDueDate",
		"billDueAmount",
		"lastCC",
		"rawLastCC",
		"debugKey",
		"rechargeNumber",
		"recharge_number_2",
		"recharge_number_3",
		"recharge_number_4",
		"recharge_number_5",
		"recharge_number_6",
		"amount",
		"mcn",
		"due_date",
		"dueDate",
		"due_amount",
		"dueAmount",
		"min_due_amount",
		"minDueAmount",
		"current_outstanding_amount",
		"original_due_amount",
		"originalDueAmount",
		"original_min_due_amount",
		"originalMinDueAmount",
		"cin"
	));

//	@Autowired
//	public LoggerHelper(Class<?> clazz) {
//		logger = LogManager.getLogger(LoggerHelper.class);
//	}

	public void info(String message, Object... objects) {
		List<Object> finalDecryptedObjects = new ArrayList<>();
		if(FINANCIAL_SERVICES.equalsIgnoreCase(ThreadContext.getServiceName())) {
			for(Object o: objects) {
				if(o instanceof String) {
					finalDecryptedObjects.add(aesUtil.encrypt(o.toString()));
				} else {
					finalDecryptedObjects.add(encryptResponse(o));
				}
			}
			logger.info(message, finalDecryptedObjects.toArray());
		} else {
			for(Object o: objects) {
				finalDecryptedObjects.add(aesUtil.encrypt(o.toString()));
			}
			logger.info(message, finalDecryptedObjects.toArray());
		}
	}

	public List<String> encryptResponse(Object object) {
		List<String> finalList = new ArrayList<>();
		ObjectMapper objectMapper = new ObjectMapper();
		JsonNode jsonNode = objectMapper.valueToTree(object);
		try{
			encryptJsonNode(jsonNode, finalList);
		} catch (Exception e) {
			System.out.println("Error at logging");
		}
		return finalList;
	}


	public void encryptJsonNode(JsonNode jsonNode, List<String> finalList) throws Exception {
		if (jsonNode.isObject()) {
			String decryptedData = jsonNode.toString();
			ServiceWrapper serviceWrapper = new ServiceWrapper(FINANCIAL_SERVICE);
			encryptJsonNodeObject(jsonNode, serviceWrapper);
			if(FINANCIAL_SERVICES.equalsIgnoreCase(serviceWrapper.getService())) {
				finalList.add(jsonNode.toString());
			} else {
				finalList.add(decryptedData);
			}
		} else if (jsonNode.isArray()) {
			for (JsonNode arrayItem : jsonNode) {
				encryptJsonNode(arrayItem, finalList);
			}
		}
	}

	//O(number of fields in json)
	public void encryptJsonNodeObject(JsonNode jsonNode, ServiceWrapper serviceWrapper) {
		if (!FINANCIAL_SERVICES.equalsIgnoreCase(serviceWrapper.getService())) return;
		if (jsonNode.isObject()) {
			ObjectNode objectNode = (ObjectNode) jsonNode;
			Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();
			while (fields.hasNext()) {
				Map.Entry<String, JsonNode> field = fields.next();
				if (field.getKey().equalsIgnoreCase("service")) {
					if (!FINANCIAL_SERVICES.equalsIgnoreCase(field.getValue().asText())) {
						serviceWrapper.setService(field.getValue().asText());
						return;
					} else {
						serviceWrapper.setService(field.getValue().asText());
					}
				}
				if (sensitiveFields.contains(field.getKey())) {
					String encryptedValue = aesUtil.encrypt(field.getValue().asText());
					objectNode.put(field.getKey(), encryptedValue);
				} else {
					encryptJsonNodeObject(field.getValue(), serviceWrapper);
				}
			}
		} else if(jsonNode.isArray()){
			for(JsonNode arrayItem: jsonNode) {
				encryptJsonNodeObject(arrayItem, serviceWrapper);
			}
		}
	}


	public static Recents getRecents() {
		Recents recents = new Recents();
		recents.setMcn("dummyMcn");
		recents.setIsTokenizedTransaction(true);
		recents.setBillDate(new Date());
		recents.setDueDate(new Date());
		recents.setDueAmount(100.0);
		recents.setMinDueAmount(50.0);
		recents.setCurrentOutstandingAmount(200.0);
		recents.setOriginalDueAmount(150.0);
		recents.setOriginalMinDueAmount(75.0);
		recents.setNickName("dummyNickName");
		recents.setChannelId("dummyChannelId");
		recents.setConsumerName("dummyConsumerName");
		recents.setCylinderAgencyName("dummyCylinderAgencyName");
		recents.setOrderId(12345L);
		recents.setProductId(67890L);
		recents.setTxnAmount(300.0);
		recents.setTxnTime(new Date());
		recents.setTxnStatus("SUCCESS");
		recents.setAutomaticDate(new Date());
		recents.setAutomaticStatus(1);
		recents.setRechargeNumber2("dummyRechargeNumber2");
		recents.setRechargeNumber3("dummyRechargeNumber3");
		recents.setRechargeNumber4("dummyRechargeNumber4");
		recents.setRechargeNumber5("dummyRechargeNumber5");
		recents.setRechargeNumber6("dummyRechargeNumber6");
		recents.setRechargeNumber7("dummyRechargeNumber7");
		recents.setRechargeNumber8("dummyRechargeNumber8");
		recents.setBillUpdateTime(new Date());
		recents.setDismissActionTime(new Date());
		recents.setIsMarkAsPaid(true);
		recents.setMarkAsPaidTime(new Date());
		recents.setLastPendingTxn("dummyLastPendingTxn");
		recents.setLastFailureTxn("dummyLastFailureTxn");
		recents.setCin("dummyCin");
		recents.setPar("dummyPar");
		recents.setTin("dummyTin");
		recents.setUpdatedAt(new Date());
		recents.setPayType("dummyPayType");
		recents.setCircle("dummyCircle");
		recents.setRecentData("dummyRecentData");
		recents.setNotPaidOnPaytm(1);
		recents.setNotificationStatus(1);
		recents.setPlanName("dummyPlanName");
		recents.setMarkAsPaidAmount(400.0);
		recents.setNewBillUpdatedAt(new Date());
		recents.setIsSavedCard(true);
		recents.setIsValidation(true);
		recents.setIsTransaction(true);
		recents.setEventSource("dummyEventSource");
		RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
		recentsPrimaryKey.setService(FINANCIAL_SERVICE);
		recentsPrimaryKey.setRechargeNumber("123342343");
		recentsPrimaryKey.setOperator("hdfc");
		recentsPrimaryKey.setPlanBucket("");
		recents.setKey(recentsPrimaryKey);
		recents.setOnlyTxn(true);
		recents.setRecoEvent(true);
		recents.setAdditionalInfo("dummyAdditionalInfo");
		recents.setTag("dummyTag");
		recents.setExtra("dummyExtra");
		recents.setUserData("dummyUserData");
		recents.setCreatedAt(new Date());
		recents.setOrderIdP2p("dummyOrderIdP2p");
		recents.setIsSmsParsed(true);
		recents.setIsNewBiller(true);
		recents.setRentTFData("dummyRentTFData");
		recents.setEarlyPaymentAmount(500.0);
		recents.setEarlyPaymentDate(new Date());
		recents.setReconId("dummyReconId");
		recents.setOperator("dummyOperator");
		recents.setBbpsRefId("dummyBbpsRefId");
		recents.setPgCardId("dummyPgCardId");
		recents.setTxnUpdatedAt(new Date());
		recents.setAutomaticSubscriptionId(2);
		recents.setNextBillFetchDateFlag(true);
		recents.setCardVariant("dummyCardVariant");
		recents.setCardSkin("dummyCardSkin");
		recents.setAutomaticAmount(600.0);
		recents.setReminderStatus(1);
		recents.setOldBillFetchDate(new Date());
		recents.setInsuranceCard("dummyInsuranceCard");
		recents.setIsNewBillIdentified(true);
		recents.setRemindLaterDate(new Date());
		recents.setMarkAsPaidSource("dummyMarkAsPaidSource");
		recents.setEncDueAmount("dummyEncDueAmount");
		recents.setEncMinDueAmount("dummyEncMinDueAmount");
		recents.setEncDueDate("dummyEncDueDate");
		recents.setEncOriginalDueAmount("dummyEncOriginalDueAmount");
		recents.setEncOriginalMinDueAmount("dummyEncOriginalMinDueAmount");
		recents.setEncCurrentOutstandingAmount("dummyEncCurrentOutstandingAmount");
		recents.setIsEncrypted(0);
		recents.setConsumerSource("dummyConsumerSource");
		recents.setExtra("{\"customerId\":\"200\",\"subscriberNumber\":\"Trkajh1AGrsp0IFjNCgdfKjtNQ==\",\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":\"J9Jw5gQ2cdE=\",\"billDueDate\":\"JNFw4xAoc85CwA==\",\"billDate\":\"2024-12-10\",\"isAmountEditable\":\"1\",\"isGroupDisplayEnabled\":\"1\",\"currentMinBillAmount\":\"JNRw5hMocg==\",\"currentOutstandingAmount\":\"J9Jw5gQ2cdE=\"}");
		recents.setFastagRechargeNumber("dummyFastagRechargeNumber");
		return recents;
	}

	public static class ServiceWrapper {
		private String service;

		public ServiceWrapper(String service) {
			this.service = service;
		}

		public String getService() {
			return service;
		}

		public void setService(String service) {
			this.service = service;
		}
	}
}
