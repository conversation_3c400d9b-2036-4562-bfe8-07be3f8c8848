package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.model.CustomerToRechargeNumberMap;
import com.paytm.saga.model.RechargeNumberToCustIdMap;
import com.paytm.saga.repository.CustomerMapRepository;
import com.paytm.saga.repository.RechargeNumberMapRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class MappingService {
    private final CustomLogger logger = CustomLogManager.getLogger(com.paytm.saga.service.MappingService.class);
    private final CustomerMapRepository customerMapRepository;
    private final RechargeNumberMapRepository rechargeNumberMapRepository;




    @Autowired
    public MappingService(@NonNull CustomerMapRepository customerMapRepository,
                          @NonNull RechargeNumberMapRepository rechargeNumberMapRepository) {
        this.customerMapRepository=customerMapRepository;
        this.rechargeNumberMapRepository = rechargeNumberMapRepository;
    }

    // TODO add sanity checks and exception handling

    public List<CustomerToRechargeNumberMap> getRechargeNumbers(Long customerId, String service){
        return customerMapRepository.findByCustomerIdAndService(customerId, service);
    }

    public List<CustomerToRechargeNumberMap> getRechargeNumbers(Long customerId, String service, String operator){
        return customerMapRepository.findByCustomerIdAndServiceAndOperator(customerId, service, operator);
    }

    public List<RechargeNumberToCustIdMap> getCustomerIds(String rechargeNumber, String service){
        return rechargeNumberMapRepository.findByRechargeNumberAndService(rechargeNumber, service);
    }

    public List<RechargeNumberToCustIdMap> getCustomerIds(String rechargeNumber, String service, String operator){
        return rechargeNumberMapRepository.findByRechargeNumberAndServiceAndOperator(rechargeNumber, service, operator);
    }

    public void setRechargeNumberMap(String rechargeNumber, Long customerId, String service, String operator, Date updatedAt){
        if(Constants.FINANCIAL_SERVICES.equalsIgnoreCase(service) && EncryptionDecision.isDataEncryptionRequired(customerId)){
            return ;
        }
        RechargeNumberToCustIdMap rechargeNumberToCustIdMap = new RechargeNumberToCustIdMap();

        rechargeNumberToCustIdMap.setCustomerId(customerId);
        rechargeNumberToCustIdMap.setOperator(operator);
        rechargeNumberToCustIdMap.setRechargeNumber(rechargeNumber);
        rechargeNumberToCustIdMap.setService(service);
        rechargeNumberToCustIdMap.setUpdatedAt(updatedAt);

        rechargeNumberMapRepository.save(rechargeNumberToCustIdMap);
    }

    public List<Long> getCustomerIdWithLimit(String rechargeNumber, String service, String operator, Integer limit){
        return rechargeNumberMapRepository.getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumber, service, operator, limit);
    }




}
