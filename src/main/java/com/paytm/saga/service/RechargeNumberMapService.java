package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.repository.RechargeNumberMapRepository;
import com.paytm.saga.service.impl.ServiceConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RechargeNumberMapService {
    private final CustomLogger logger = CustomLogManager.getLogger(RechargeNumberMapService.class);

    private RechargeNumberMapRepository rechargeNumberMapRepository;

    private ServiceConfig serviceConfig;


    @Autowired
    public RechargeNumberMapService(@NonNull RechargeNumberMapRepository rechargeNumberMapRepository, @NonNull ServiceConfig serviceConfig) {
        this.rechargeNumberMapRepository = rechargeNumberMapRepository;
        this.serviceConfig = serviceConfig;
    }

    public List<Long> fetchCustomerIdsFromDb(String rechargeNumber, String service, String operator) {
        List<Long> customerIds = null;
        Integer limit = serviceConfig.getFetchRecentLimit();

        try {
            customerIds = rechargeNumberMapRepository.getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumber, service, operator,limit );
        } catch (Exception e) {
            logger.error("[RecentsServiceImpl.fetchRecentsV2] error in getting customerId from recharge_to_customer_map table, e={}", e.getMessage());
        }
        return customerIds;
    }
}
