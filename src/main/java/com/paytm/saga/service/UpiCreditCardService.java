package com.paytm.saga.service;

import org.springframework.stereotype.Service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.UpiCreditCardMessage;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.util.CreditCardUtils;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.TTLUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class UpiCreditCardService {

    private final Logger logger = LogManager.getLogger(UpiCreditCardService.class);

    private final RecentsRepositoryWrapperService recentsRepositoryWrapperService;
    private final MetricsHelper metricsHelper;

    @Value("${upi.cc.bankNameMapping}")
    private String bankNameMappingJson;
    @Value("${upi.cc.ifscCodeMapping}")
    private String ifscCodeMappingJson;

    private Map<String, String> bankNameMap;
    private Map<String, String> ifscCodeMap;

    @Autowired
    public UpiCreditCardService(RecentsRepositoryWrapperService recentsRepositoryWrapperService, MetricsHelper metricsHelper) {
        this.recentsRepositoryWrapperService = recentsRepositoryWrapperService;
        this.metricsHelper = metricsHelper;
    }

    @PostConstruct
    public void init() {
        this.ifscCodeMap = JsonUtils.parseJsonToMapWithStringKey(ifscCodeMappingJson, String.class);
        this.bankNameMap = JsonUtils.parseJsonToMapWithStringKey(bankNameMappingJson,  String.class);
    }

    public void processUpiCreditCardMessage(UpiCreditCardMessage upiCreditCardMessage, String message, String consumerName) {
        final String logPrefix = "UPI-CC-Listener::";
        if (notValidData(upiCreditCardMessage, consumerName)) return;

        Recents recents = new Recents();

        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(Long.valueOf(upiCreditCardMessage.getCustomerId()));
        recents.getKey().setOperator(upiCreditCardMessage.getBankName());
        if (bankNameMap.containsKey(upiCreditCardMessage.getBankName())) {
            recents.getKey().setOperator(bankNameMap.get(upiCreditCardMessage.getBankName()));
        } else if (ifscCodeMap.containsKey(upiCreditCardMessage.getBankName())) {
            recents.getKey().setOperator(ifscCodeMap.get(upiCreditCardMessage.getBankName()));
        } else {
            logger.error("{}Bank name mapping not found for bank identifier {}", logPrefix, upiCreditCardMessage.getBankName());
            metricsHelper.incrementCount(consumerName, Constants.BANK_MAPPING_NOT_FOUND,upiCreditCardMessage.getBankName());
            return;
        }

        recents.getKey().setRechargeNumber(Constants.UPI_CARD_MCN_PREFIX + upiCreditCardMessage.getLast2CardDigits());
        recents.getKey().setService(Constants.FINANCIAL_SERVICES);
        recents.getKey().setPlanBucket("");

        recents.setEventSource(Constants.EVENT_SOURCE.UPI_CREDIT_CARD);
        recents.setUpdatedAt(new Date());
        recents.setCreatedAt(new Date());
        recents.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        recents.setMcn(Constants.UPI_CARD_MCN_PREFIX + upiCreditCardMessage.getLast2CardDigits());

        Long productId = CVRProductCache.getInstance().getProductId(recents.getKey().getOperator(), Constants.FINANCIAL_SERVICES, Constants.CREDIT_CARD_PAYTYPE);
        if (productId == null) {
            logger.error("{}Product not found for bank name {}", logPrefix, upiCreditCardMessage.getBankName());
            metricsHelper.incrementCount(consumerName, Constants.PRODUCT_MAPPING_NOT_FOUND, upiCreditCardMessage.getBankName());
            return;
        }
        recents.setProductId(productId);

        int ttl = TTLUtils.getTTL(Constants.FINANCIAL_SERVICES);
        List<Recents> recentsList = recentsRepositoryWrapperService.findByCustomerIdAndService(recents.getKey().getCustomerId(), Constants.FINANCIAL_SERVICES);
        Map<String, Recents> recentsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(recentsList)){
            recentsList.forEach(r -> recentsMap.put(CreditCardUtils.makeDedupKey(r), r));
        }
        if(!recentsMap.containsKey(CreditCardUtils.makeDedupKey(recents))){
            if (Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean(Constants.ENABLE_UPI_CARD_WRITES))) {
                recentsRepositoryWrapperService.updateRecentWhenNoExistingData(recents, ttl);
                metricsHelper.recordSuccessRate(consumerName, Constants.SUCCESS_EVENT);
            } else {
                logger.info("{}UPI Credit card writes are disabled", logPrefix);
            }
        } else {
            metricsHelper.recordSuccessRate(consumerName, Constants.DUPLICATE_EVENT);
            logger.info("{}Duplicate card received in upi credit card kafka payload {} ", logPrefix, message);
        }
    }

    private boolean notValidData(UpiCreditCardMessage upiCreditCardMessage, String consumerName) {
        if (upiCreditCardMessage == null) {
            logger.error("UPI-CC-Listener::Validate - Error while parsing message");
            metricsHelper.recordSuccessRate(consumerName, Constants.ERROR_EVENT);
            return true;
        }else if (!validateLast2CardDigits(upiCreditCardMessage.getLast2CardDigits())){
            logger.error("UPI-CC-Listener::Validate - Invalid last 2 card digits {}", upiCreditCardMessage.getLast2CardDigits());
            metricsHelper.incrementCount(consumerName, Constants.INVALID_LAST_2_CARD_DIGITS);
            return true;
        }else if (!enabledForCustId(upiCreditCardMessage.getCustomerId())) {
            logger.info("UPI-CC-Listener::Validate - Customer not enabled for upi credit card {}", upiCreditCardMessage.getCustomerId());
            metricsHelper.incrementCount(consumerName, Constants.CUSTOMER_NOT_ENABLED);
            return true;
        }
        return false;
    }

    private boolean enabledForCustId(String customerId) {
        return FeatureConfigCache.getInstance().getList(Constants.WHITE_LISTED_CUST_IDS_UPI).contains(customerId)
                || FeatureConfigCache.getInstance().getList(Constants.WHITE_LISTED_CUST_IDS_UPI).contains(Constants.ALL);
    }

    private boolean validateLast2CardDigits(String last2CardDigits) {
        return StringUtils.isNotBlank(last2CardDigits) && last2CardDigits.length() == 2 && StringUtils.isNumeric(last2CardDigits);
    }
}
