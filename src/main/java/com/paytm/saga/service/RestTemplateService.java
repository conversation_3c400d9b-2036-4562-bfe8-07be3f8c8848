package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.Objects;

/**
 * This class is a service for all http request made for external sources. It is designed by singleton pattern.
 *
 * Updated on 15/07/2021 by harish1.bisht
 */

public abstract class RestTemplateService {

    private static final CustomLogger logger = CustomLogManager.getLogger(RestTemplateService.class);

    private final RestTemplate restTemplate;

    RestTemplateService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }
    /**
     * This method is used to execute Get request with headers
     *
     * @param url
     * @param headers
     * @param paramsMap
     * @param clazz
     * @param <T>
     * @return
     */

    public <T> T executeGetRequest(final String url, final Map<String, String> headers, final Map<String, String> paramsMap,
                                   Class<T> clazz) throws RestTemplateServiceException {
        HttpHeaders httpHeaders = null;
        if (Objects.isNull(headers) || headers.isEmpty()) {
            throw new IllegalArgumentException("Headers Can't be null or empty");
        }
        if (Objects.isNull(url) || StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("Url can't be null or blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Class type cant be null");
        }
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            httpHeaders = new HttpHeaders();
            httpHeaders.add(entry.getKey(), entry.getValue());
        }
        HttpEntity<T> httpEntity = new HttpEntity<>(httpHeaders);
        ResponseEntity<T> responseEntity = null;
        String uri = UriComponentsBuilder.fromUriString(url)
                .queryParam(Constants.SMS_API_SERVICE_REQUEST_PARAM,paramsMap.get(Constants.SMS_API_SERVICE_REQUEST_PARAM))
                .build().toString();
        try {
            responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, clazz);
        } catch (Exception e) {
            handleRestServiceException(e);
        }
        return responseEntity.getBody();
    }

    /**
     * This method is used to execute Get request without headers
     *
     * @param url
     * @param paramsMap
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T    executeGetRequest(final String url, final Map<String, String> paramsMap,
                                   Class<T> clazz) throws RestTemplateServiceException {
        HttpHeaders httpHeaders = null;

        if (Objects.isNull(url) || StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("Url can't be null or blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Class type cant be null");
        }
        T object = null;

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        paramsMap.forEach((k,v) -> builder.queryParam(k,v));
        try {
            object = restTemplate.getForObject(builder.toUriString(), clazz);
        } catch (Exception e) {
            handleRestServiceException(e);
        }
        return object;
    }

    /**
     * This methos is used to execute a post request withOut headersand withrequest params.
     *
     * @param url
     * @param headers
     * @param paramsMap
     * @param clazz
     * @param requestBody
     * @param <T>
     * @param <R>
     * @return
     */

    public <T, R> T executePostRequest(final String url, final Map<String, String> headers, final Map<String, String> paramsMap,
                                       Class<T> clazz, R requestBody) throws RestTemplateServiceException {
        HttpHeaders httpHeaders = null;
        if (Objects.isNull(headers) || headers.isEmpty()) {
            throw new IllegalArgumentException("Headers Can't be null or empty");
        }
        if (Objects.isNull(url) || StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("Url can't be null or blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Class type cant be null");
        }

        if (Objects.isNull(requestBody)) {
            throw new IllegalArgumentException("RequestBody cant be null");
        }

        httpHeaders = new HttpHeaders();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            httpHeaders.add(entry.getKey(), entry.getValue());
        }
        HttpEntity<R> httpEntity = new HttpEntity<R>(requestBody, httpHeaders);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        paramsMap.forEach((k,v) -> builder.queryParam(k,v));
        ResponseEntity<T> responseEntity = null;
        try {
            responseEntity = restTemplate.exchange(builder.toUriString(), HttpMethod.POST, httpEntity, clazz);
        } catch (Exception e) {
            handleRestServiceException(e);
        }
        return responseEntity.getBody();
    }

    /**
     * This method is used to execute Post request with params but without HJeaders.
     *
     * @param url
     * @param paramsMap
     * @param clazz
     * @param requestBody
     * @param <T>
     * @param <R>
     * @return
     */

    public <T, R> T executePostRequest(final String url, final MultiValueMap<String, String> paramsMap,
                                       Class<T> clazz, R requestBody) throws RestTemplateServiceException {
        if (Objects.isNull(url) || StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("Url can't be null or blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Class type cant be null");
        }

        if (Objects.isNull(requestBody)) {
            throw new IllegalArgumentException("RequestBody cant be null");
        }

        HttpEntity<R> httpEntity = new HttpEntity<R>(requestBody);
        ResponseEntity<T> responseEntity = null;
        try {
            responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, clazz, paramsMap);
        } catch (Exception e) {
            handleRestServiceException(e);
        }
        return responseEntity.getBody();
    }


    /**
     * This is simple Post request
     *
     * @param url
     * @param clazz
     * @param requestBody
     * @param <T>
     * @return
     */

    public <T> T executePostRequest(final String url, Class<T> clazz, Object requestBody) throws RestTemplateServiceException {
        if (Objects.isNull(url) || StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("Url can't be null or blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Class type cant be null");
        }

        if (Objects.isNull(requestBody)) {
            throw new IllegalArgumentException("RequestBody cant be null");
        }
        T object = null;
        try {

            object = restTemplate.postForObject(url, requestBody, clazz);
            logger.info("result {}", JsonUtils.serialiseJson(object));
        } catch (Exception e) {
            logger.error("[RestTemplateService.executePostRequest] Exception accured",e);
            handleRestServiceException(e);

        }
        return object;

    }

    /**
     * This is simple Post request
     *
     * @param url
     * @param clazz
     * @param requestBody
     * @param <T>
     * @return
     */

    public <T> T executePutRequest(final String url, Class<T> clazz, Object requestBody) throws RestTemplateServiceException {
        if (Objects.isNull(url) || StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("Url can't be null or blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Class type cant be null");
        }

        if (Objects.isNull(requestBody)) {
            throw new IllegalArgumentException("RequestBody cant be null");
        }
        ResponseEntity<T> responseEntity = null;
        try {

            HttpHeaders requestHeaders = new HttpHeaders();
            HttpEntity entity = new HttpEntity(requestBody, null);
            responseEntity = restTemplate.exchange(url, HttpMethod.PUT, entity , clazz);
        } catch (Exception e) {
            logger.error("[RestTemplateService.executePostRequest] Exception accured",e);
            handleRestServiceException(e);
        }
        return responseEntity.getBody();
    }

    private void handleRestServiceException(Exception e) throws RestTemplateServiceException {
        if (e instanceof HttpClientErrorException) {
            HttpClientErrorException ex = (HttpClientErrorException) e;
            logger.error("Details of RestTemplate Exception ..........");
            logger.error("StatusCode : {}", ex.getStatusCode());
            logger.error("RawStatusCode : {}", ex.getRawStatusCode());
            logger.error("Message : {}", ex.getMessage());
            logger.error("StatusText : {}", ex.getStatusText());
            logger.error("responsebody {}", ex.getResponseBodyAsString());
            throw new RestTemplateServiceException(ex.getStatusCode(), ex.getRawStatusCode(), ex.getMessage(), ex.getStatusText(), ex.getResponseBodyAsString());
        } else if ( e instanceof HttpServerErrorException) {
            HttpServerErrorException ex = (HttpServerErrorException) e;
            logger.error("Details of RestTemplate Exception ..........");
            logger.error("StatusCode : {}", ex.getStatusCode());
            logger.error("RawStatusCode : {}", ex.getRawStatusCode());
            logger.error("Message : {}", ex.getMessage());
            logger.error("StatusText : {}", ex.getStatusText());
            logger.error("responsebody {}", ex.getResponseBodyAsString());
            throw new RestTemplateServiceException(ex.getStatusCode(), ex.getRawStatusCode(), ex.getMessage(), ex.getStatusText(), ex.getResponseBodyAsString());
        }else {
            throw new RestTemplateServiceException(e, e.getMessage());
        }
    }


    /**
     * This is simple Post request
     *
     * @param url
     * @param clazz
     * @param requestBody
     * @param <T>
     * @return
     */

    public <T> T executePostRequest(final String url, Class<T> clazz, Object requestBody, final Map<String, String> headers)
            throws RestTemplateServiceException {
        HttpHeaders httpHeaders = null;
        ResponseEntity<T> responseEntity = null;
        if (Objects.isNull(url) || StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("Url can't be null or blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Class type cant be null");
        }

        if (Objects.isNull(requestBody)) {
            throw new IllegalArgumentException("RequestBody cant be null");
        }


        httpHeaders = new HttpHeaders();
        for (Map.Entry<String, String> entry : headers.entrySet()) {

            httpHeaders.add(entry.getKey(), entry.getValue());
        }
        HttpEntity<Object> httpEntity = new HttpEntity<Object>(requestBody, httpHeaders);
        try {
            responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, clazz);
        } catch (Exception e) {
            handleRestServiceException(e);
        }
        return responseEntity.getBody();
    }
}
