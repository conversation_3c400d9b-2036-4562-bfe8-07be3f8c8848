package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> on 15/07/2021
 */

@Service("com.paytm.saga.service.DcatRestTemplateService")
public class DcatRestTemplateService extends RestTemplateService {
    private static final CustomLogger logger = CustomLogManager.getLogger(DcatRestTemplateService.class);

    @Autowired
    public DcatRestTemplateService(@NonNull @Qualifier("DcatRestTemplateClient") RestTemplate restTemplate) {
        super(restTemplate);
    }
}