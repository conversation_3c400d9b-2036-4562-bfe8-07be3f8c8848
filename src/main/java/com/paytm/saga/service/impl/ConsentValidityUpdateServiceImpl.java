package com.paytm.saga.service.impl;

import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.exception.ConsentException;
import com.paytm.saga.model.ConsentValidityModel;
import com.paytm.saga.model.Recents;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.service.ConsentValidityUpdateService;
import com.paytm.saga.util.ConsentUtil;
import com.paytm.saga.util.TTLUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.*;

@Service
public class ConsentValidityUpdateServiceImpl implements ConsentValidityUpdateService {

    private static final Logger logger = LogManager.getLogger(ConsentValidityUpdateServiceImpl.class);

    private final MetricsHelper metricsHelper;
    private final RecentsRepository recentsRepository;

    @Autowired
    public ConsentValidityUpdateServiceImpl(MetricsHelper metricsHelper, RecentsRepository recentRepository) {
        this.metricsHelper = metricsHelper;
        this.recentsRepository = recentRepository;
    }

    @Override
    public void updateConsentValidity(ConsentValidityModel consentValidityModel) {
        logger.info("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Received message: {}", consentValidityModel);
        try{
            Date consentValidTill = ConsentUtil.getConsentValidTill(consentValidityModel.getConsentValidTill());
            logger.info("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Consent Valid Till: {}", consentValidTill);
            logger.info("customer: {}, service: {}, rechargeNumber: {}, operator: {}, consentValidTill: {}",
                    consentValidityModel.getCustomerId(), consentValidityModel.getService(), consentValidityModel.getRechargeNumber(), consentValidityModel.getOperator(), consentValidTill);
            List<Recents> recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(consentValidityModel.getCustomerId(), consentValidityModel.getService(), consentValidityModel.getRechargeNumber(), consentValidityModel.getOperator(),"");

            logger.info("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Recents: {}", recents);

            if(Objects.nonNull(recents)) {
                for (Recents recent : recents) {
                    if(Objects.isNull(recent.getConsentValidTill()) || recent.getConsentValidTill().before(consentValidTill)){
                        recent.setConsentValidTill(consentValidTill);

                        Date oldUpdatedAt = recent.getUpdatedAt();
                        int ttl = TTLUtils.getTTL(recent.getKey().getService(),recent.getTxnTime(),recent.getCreatedAt());
                        if(ttl<1)ttl=1;
                        Boolean isSaved = recentsRepository.updateRecentWhenDataAlreadyExist(recent, oldUpdatedAt, ttl);

                        if(Boolean.TRUE.equals(isSaved)){
                            logger.info("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Updated consent validity for customer: {}, service: {}, rechargeNumber: {}, operator: {}, consentValidTill: {}",
                                    consentValidityModel.getCustomerId(), consentValidityModel.getService(), consentValidityModel.getRechargeNumber(), consentValidityModel.getOperator(), consentValidTill);
                            metricsHelper.incrementCount(BULK_CONSENT_VALIDITY_UPDATE_CONSUMER, "reason:"+ "CONSENT_VALIDITY_UPDATE_SUCCESS");
                        }else{
                            logger.error("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Error while updating consent validity for customer: {}, service: {}, rechargeNumber: {}, operator: {}, consentValidTill: {}",
                                    consentValidityModel.getCustomerId(), consentValidityModel.getService(), consentValidityModel.getRechargeNumber(), consentValidityModel.getOperator(), consentValidTill);
                            metricsHelper.incrementCount(BULK_CONSENT_VALIDITY_UPDATE_CONSUMER, "reason:"+ "CONSENT_VALIDITY_UPDATE_FAILURE");
                        }
                    }else{
                        logger.info("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Consent Validity is already present for customer: {}, service: {}, rechargeNumber: {}, operator: {}, consentValidTill: {}",
                                consentValidityModel.getCustomerId(), consentValidityModel.getService(), consentValidityModel.getRechargeNumber(), consentValidityModel.getOperator(), consentValidTill);
                        metricsHelper.incrementCount(BULK_CONSENT_VALIDITY_UPDATE_CONSUMER, "reason:" + "CONSENT_VALIDITY_ALREADY_PRESENT");
                    }
                }
            }else{
                logger.error("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Consent not present for customer: {}, service: {}, rechargeNumber: {}, operator: {}, consentValidTill: {}",
                        consentValidityModel.getCustomerId(), consentValidityModel.getService(), consentValidityModel.getRechargeNumber(), consentValidityModel.getOperator(), consentValidTill);
                metricsHelper.incrementCount(BULK_CONSENT_VALIDITY_UPDATE_CONSUMER, "reason:" + "RECORD_NOT_PRESENT");
            }
        }catch (ConsentException ex){
            logger.error("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Consent Exception occurred for message {}, Exception {}", consentValidityModel, ex);
            metricsHelper.incrementCount(BULK_CONSENT_VALIDITY_UPDATE_CONSUMER, "reason:" + ex.getReason());
        }catch (Exception e){
            logger.error("[ConsentValidityUpdateServiceImpl.updateConsentValidity] Exception occurred for message {}, Exception {}", consentValidityModel, e);
            metricsHelper.incrementCount(BULK_CONSENT_VALIDITY_UPDATE_CONSUMER, "reason:" + "CONSENT_VALIDITY_UPDATE_FAILURE");
        }
    }
}
