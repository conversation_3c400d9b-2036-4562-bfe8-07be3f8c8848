package com.paytm.saga.service.impl;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.dto.DeleteRequestBody;
import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.DeleteRecentCard;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.service.RecentDataToKafkaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Service("deleteCreditCardRecentCard")
public class DeleteCreditCardRecentCard implements DeleteRecentCard {

    @Autowired
    private RecentsRepositoryWrapperService recentsRepository;
    @Autowired
    private CustomerBillRepository customerBillRepository;
    @Autowired
    private RecentDataToKafkaService recentDataToKafkaService;

    @Override
    public void deleteRecentCard(Recents recents, DeleteRequestBody deleteRequestBody) throws RecentDataToKafkaException {

        ProductMin productByRequestPid = CVRProductCache.getInstance().getProductDetails(deleteRequestBody.getProductId());
        ProductMin productByRecentsPid = CVRProductCache.getInstance().getProductDetails(ActiveInactivePidMapCache.getInstance().getActivePid(recents.getProductId()));
        if (Objects.nonNull(productByRequestPid)) {
            if (Constants.CARD_NETWORK_DUMMY.equalsIgnoreCase(productByRequestPid.getCardNetwork()) || !Constants.CommonConstants.CC_DEFAULT_OPERATOR.equalsIgnoreCase(recents.getKey().getOperator())) {
                deleteRequestBody.setOperator(productByRequestPid.getBankCode().toLowerCase());
                if (recents.getKey().getRechargeNumber().equals(deleteRequestBody.getRechargeNumber()) &&
                        recents.getKey().getOperator().equalsIgnoreCase(deleteRequestBody.getOperator())) {
                    deleteRecent(recents);
                }

            } else if (Objects.nonNull(productByRecentsPid) && StringUtils.equalsIgnoreCase(getUniqueKeyForCC(recents.getMcn(), productByRecentsPid.getCardNetwork(), productByRecentsPid.getBankCode()), getUniqueKeyForCC(deleteRequestBody.getRechargeNumber(), productByRequestPid.getCardNetwork(), productByRequestPid.getBankCode()))) {
                deleteRecent(recents);
            }
        }
    }
    private void deleteRecent(Recents recents) throws RecentDataToKafkaException {
        RecentsPrimaryKey key=recents.getKey();
        Recents newRecents = new Recents();
        newRecents.setKey(key);
        newRecents.setUpdatedAt(new Date());
        recentDataToKafkaService.setRecentConsumerSource(newRecents, Constants.CommonConstants.DELETE_CC_RECENT);
        recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(newRecents,
                key.getCustomerId(),
                key.getService(),
                key.getRechargeNumber(),
                key.getOperator(),
                key.getPlanBucket());
    if(Objects.nonNull(recents.getDueDate()))
        customerBillRepository.deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(
                key.getCustomerId(),
                recents.getDueDate(),
                key.getService(),
                key.getRechargeNumber(),
                key.getOperator(),
                key.getPlanBucket());
    }


    private String getUniqueKeyForCC(String mcn, String cardNetwork, String bankCode){
        return StringUtils.right(mcn,4) + "_" + cardNetwork + "_" + bankCode;
    }
}
