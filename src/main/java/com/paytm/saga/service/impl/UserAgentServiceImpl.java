package com.paytm.saga.service.impl;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.model.UserAgent;
import com.paytm.saga.model.UserAgentPrimaryKey;
import com.paytm.saga.recent.repository.UserAgentRepository;
import com.paytm.saga.service.UserAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service("UserAgentServiceImpl")
public class UserAgentServiceImpl implements UserAgentService {

    private final CustomLogger logger = CustomLogManager.getLogger(UserAgentServiceImpl.class);

    @Autowired
    UserAgentRepository userAgentRepository;

    @Override
    public void insert(Long customerId) {
        logger.info("insert() - customer {} is an agent", customerId);
        UserAgent userAgent = new UserAgent();
        userAgent.setKey(new UserAgentPrimaryKey(customerId));
        userAgentRepository.insert(userAgent);
    }

    @Override
    public Boolean isAgent(Long customerId) {
        Optional<UserAgent> userAgent = userAgentRepository.findById(customerId);
        if (userAgent.isPresent()) {
            logger.info("isAgent() - customer is an agent {} ", customerId);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
