package com.paytm.saga.service.impl;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.dto.DeleteRequestBody;
import com.paytm.saga.factory.DummyRechargeNumberGeneratorFactory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.DeleteRecentCard;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.service.MarkAsPaidService;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.service.RecentDataToKafkaService;
import com.paytm.saga.service.*;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.util.RecentUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.paytm.saga.enums.Service.ELECTRICITY;

@Service("deleteNonCreditCardRecentCard")
public class DeleteNonCreditCardRecentCard implements DeleteRecentCard {
    private static final CustomLogger log = CustomLogManager.getLogger(DeleteNonCreditCardRecentCard.class);
    @Autowired
    private RecentsRepositoryWrapperService recentsRepository;
    @Autowired
    private CustomerBillRepository customerBillRepository;
    @Autowired
    private RecentDataToKafkaService recentDataToKafkaService;
    @Autowired
    ServiceConfig serviceConfig;
    @Autowired
    DummyRechargeNumberGeneratorFactory dummyRechargeNumberGeneratorFactory;

    @Override
    public void deleteRecentCard(Recents recents, DeleteRequestBody deleteRequestBody) throws RecentDataToKafkaException {
        log.info("[DeleteNonCreditCardRecentCard.deleteRecentCard]:: recents: {} , deleteRequestBody: {}", recents, deleteRequestBody);
        log.debug("[DeleteNonCreditCardRecentCard.deleteRecentCard]:: testing: {} {} {} {} {}",
                recents.getKey().getService(),
                recents.getKey().getService().equalsIgnoreCase(Constants.FASTAG),
                recents.getKey().getRechargeNumber(),
                deleteRequestBody.getRechargeNumber(),
                recents.getKey().getRechargeNumber().equalsIgnoreCase(deleteRequestBody.getRechargeNumber())
        );

        boolean matched = false;
        if((recents.getKey().getService().equalsIgnoreCase(Constants.RENT_PAYMENT) || recents.getKey().getService().equalsIgnoreCase(Constants.BUSINESS_PAYMENT)) && recents.getKey().getRechargeNumber()
                .equals(deleteRequestBody.getBillerAccountId().toString())){
            matched=true;
        } else if (recents.getKey().getService().equalsIgnoreCase(Constants.FASTAG) && recents.getKey().getRechargeNumber()
                .equalsIgnoreCase(deleteRequestBody.getRechargeNumber())) {
            matched = true;
        } else if (serviceConfig.getDummyRechargeNumberAllowedServices().contains(recents.getKey().getService())) {
            DummyRechargeNumberGenerator dummyRechargeNumberGenerator = dummyRechargeNumberGeneratorFactory.getGenerator(recents.getKey().getService());
            if(dummyRechargeNumberGenerator != null){
                String generatedDummyRN = dummyRechargeNumberGenerator.generateDummyRN(String.valueOf(recents.getKey().getCustomerId()), recents.getKey().getOperator());
                if(recents.getKey().getRechargeNumber().equalsIgnoreCase(deleteRequestBody.getRechargeNumber()) || recents.getKey().getRechargeNumber().equalsIgnoreCase(generatedDummyRN)){
                    matched = true;
                }
            }
        } else if (recents.getKey().getRechargeNumber()
                .equals(deleteRequestBody.getRechargeNumber()) ||
                ((Objects.nonNull(deleteRequestBody.getService())&&deleteRequestBody.getService().equalsIgnoreCase(ELECTRICITY.value) &&  serviceConfig.getWhielistOperatorServicesForDedup().contains(recents.getKey().getOperator().toLowerCase())) &&
                        RecentUtils.getTrimmedRechargeNumber(recents.getKey().getRechargeNumber()).equals(RecentUtils.getTrimmedRechargeNumber(deleteRequestBody.getRechargeNumber())))) {
            matched = true;

        }
        if(matched){
            this.deleteRecent(recents);
        }
    }
    public void deleteRecent(Recents recents) throws RecentDataToKafkaException {
        log.info("[DeleteNonCreditCardRecentCard.deleteRecent]");

        RecentsPrimaryKey key=recents.getKey();
        Recents newRecents = new Recents();
        newRecents.setKey(key);
        newRecents.setUpdatedAt(new Date());
        recentDataToKafkaService.setRecentConsumerSource(newRecents, Constants.CommonConstants.DELETE_NON_CC_RECENT);

        recentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(newRecents,
                key.getCustomerId(),
                key.getService(),
                key.getRechargeNumber(),
                key.getOperator(),
                key.getPlanBucket());
        if(Objects.nonNull(recents.getDueDate()))
            customerBillRepository.deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(
                    key.getCustomerId(),
                    recents.getDueDate(),
                    key.getService(),
                    key.getRechargeNumber(),
                    key.getOperator(),
                    key.getPlanBucket());
    }

}
