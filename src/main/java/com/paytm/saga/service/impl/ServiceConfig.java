package com.paytm.saga.service.impl;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.NumberConstants;
import com.paytm.saga.common.constant.ServiceNameConstants;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.service.Config;
import com.paytm.saga.util.JsonUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.MetricConstants.*;

@Component
public class ServiceConfig implements Config {

    private final CustomLogger logger = CustomLogManager.getLogger(ServiceConfig.class);

    @Autowired
    @Qualifier("monitoringClient")
    private StatsDClient monitoringClient;

    public int getAgentLimit() {
        Integer agentLimit = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.AGENT_IDENTIFICATION_LIMIT);
        if (agentLimit == null) {
            agentLimit = NumberConstants.AGENT_LIMIT;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return agentLimit;

    }

    public List<String> getOldBillEnabledServices() {
        if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.OLD_BILL_ENABLED_SERVICES) != null)
            return FeatureConfigCache.getInstance().getList(ServiceNameConstants.OLD_BILL_ENABLED_SERVICES);
        return new ArrayList<>();
    }

    public List<String> getOldBillDisabledOperators() {
        if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.OLD_BILL_DISABLED_OPERATORS) != null)
            return FeatureConfigCache.getInstance().getList(ServiceNameConstants.OLD_BILL_DISABLED_OPERATORS);
        return new ArrayList<>();
    }

    private void pushCountToDD(String metricName, String state) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.STATE + ":" + state;
        monitoringClient.incrementCounter(metricName, tags);
    }


    public Integer getBillVisiblityDays() {
        Integer billVisiblityDays = null;
        if (FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_REMINDER_PREPAID_END_DAYS) != null
                && FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_REMINDER_POSTPAID_END_DAYS) != null)
            billVisiblityDays = Integer.max(FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_REMINDER_PREPAID_END_DAYS), FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_REMINDER_POSTPAID_END_DAYS));
        if(FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_PAYTM_POSTPAID_END_DAYS) != null) // this check is for IN-39227
            billVisiblityDays = Integer.max(FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_PAYTM_POSTPAID_END_DAYS), billVisiblityDays);
        if (billVisiblityDays == null) {
            billVisiblityDays = NumberConstants.BILL_VISIBLITY_DAY;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return billVisiblityDays;
    }

    public Integer getBillVisiblityDays(String service) {
        Integer billVisiblityDays = null;
       if(FeatureConfigCache.getInstance().getObject(ServiceNameConstants.SMART_RECO_END_DAYS) != null) { // this check is for IN-39227
           Map<String, Integer>  serviceWiseVisibleEndDays = (Map<String, Integer>)FeatureConfigCache.getInstance().getObject(ServiceNameConstants.SMART_RECO_END_DAYS);
           billVisiblityDays = serviceWiseVisibleEndDays.getOrDefault(service,null);
       } if (billVisiblityDays == null) {
            billVisiblityDays = NumberConstants.BILL_VISIBLITY_DAY;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return billVisiblityDays;
    }

    public Integer getBillVisiblityDaysBefore() {
        Integer billVisiblityDaysBefore = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.BILL_END_RANGE_CURRENT_DATE);
        if (billVisiblityDaysBefore == null) {
            billVisiblityDaysBefore = NumberConstants.BILL_END_RANGE_CURRENT_DATE;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return billVisiblityDaysBefore;
    }

    public Integer getPrepaidElectricityBillVisibilityOffset() {
        Integer prepaidElectricityBillVisibilityOffset = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.PREPAID_ELECTRICITY_BILL_VISIBILITY_OFFSET);
        if (prepaidElectricityBillVisibilityOffset == null) {
            prepaidElectricityBillVisibilityOffset = NumberConstants.PREPAID_ELECTRICITY_BILL_VISIBILITY_OFFSET;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return prepaidElectricityBillVisibilityOffset;
    }

    public Integer getHeuristicPrepaidElectricityBillVisibilityOffset() {
        Integer heuristicPrepaidElectricityBillVisibilityOffset = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.HEURISTIC_PREPAID_ELECTRICITY_BILL_VISIBILITY_OFFSET);
        if (heuristicPrepaidElectricityBillVisibilityOffset == null) {
            heuristicPrepaidElectricityBillVisibilityOffset = NumberConstants.HEURISTIC_PREPAID_ELECTRICITY_BILL_VISIBILITY_OFFSET;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return heuristicPrepaidElectricityBillVisibilityOffset;
    }

    public long getRecentReadTimeout() {
        Long readTimeout = FeatureConfigCache.getInstance().getLong(ServiceNameConstants.RECENT_DB_READ_TIMEOUT);
        if (readTimeout == null || readTimeout == 0) {
            readTimeout = NumberConstants.RECENT_DB_READ_TIMEOUT;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return readTimeout;
    }

    public long getFrequentOrderExecutorTimeOut() {
        Long executionTimeout = FeatureConfigCache.getInstance().getLong(ServiceNameConstants.FREQUENT_ORDER_EXECUTOR_TIMEOUT);
        if (executionTimeout == null || executionTimeout == 0) {
            executionTimeout = NumberConstants.FREQUENT_ORDER_EXECUTOR_TIMEOUT;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return executionTimeout;
    }


    public int getPrepaidEndDay() {

        Integer endDay = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_REMINDER_PREPAID_END_DAYS);
        if (endDay == null) {
            endDay = 3;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return endDay;
    }

    public int getPrepaidStartDay() {

        Integer startDay = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_REMINDER_PREPAID_START_DAYS);
        if (startDay == null) {
            startDay = 5;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return startDay;
    }

    public int getPostpaidEndDay() {

        Integer endDay = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_REMINDER_POSTPAID_END_DAYS);
        if (endDay == null) {
            endDay = 3;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return endDay;
    }

    public int getPostpaidStartDay() {

        Integer endDay = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.SMART_REMINDER_POSTPAID_START_DAYS);
        if (endDay == null) {
            endDay = 3;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return endDay;
    }

    public int getRecentCategoryLimit() {
        Integer allowedLength = FeatureConfigCache.getInstance().getInteger("recentCategoryLimit");
        if (allowedLength == null || allowedLength == 0) {
            allowedLength = 30;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return allowedLength;

    }

    public Integer getBillVisiblityStartDaysByService(String service) {

        Integer allowedLength = FeatureConfigCache.getInstance().getInteger(StringUtils.lowerCase(service) + "_" + ServiceNameConstants.START_DAYS);

        return allowedLength;
    }

    public Integer getBillVisiblityEndDaysByService(String service) {

        Integer allowedLength = FeatureConfigCache.getInstance().getInteger(StringUtils.lowerCase(service) + "_" + ServiceNameConstants.END_DAYS);

        return allowedLength;
    }

     public Integer getFetchRecentLimit() {
        Integer limit = FeatureConfigCache.getInstance().getInteger(Constants.SERVICE_CONFIG_CONSTANTS.FETCH_RECENT_LIMIT);
        if (limit == null) {
            limit = Constants.CommonConstants.FETCH_RECENT_DEFAULT_LIMIT;
            pushCountToDD(FETCH_RECENTS, Constants.INVALID_CONFIG);
        }
        return limit;
    }
    
    public int getRechargeNumberToCustomerIdLimit() {
        Integer limit = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.RECHARGE_NUMBER_TO_CUSTOMER_ID_LIMIT);
        if (limit == null) {
            limit = NumberConstants.RECHARGE_NUMBER_TO_CUSTOMER_ID_LIMIT;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return limit;

    }
    public List<String> newAccountServices() {
        List<String> allowedServices = null;
        if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.NEW_ACCOUNT_SERVICES) != null)
            allowedServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.NEW_ACCOUNT_SERVICES);
        return allowedServices;
    }
    public Boolean disableDropOffService(){
        Boolean disableDropOff = FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.DISABLE_DROPOFF_SERVICE);
        if (disableDropOff != null)
            return disableDropOff;
        return false;
    }

    public List<String> getSMSEnabledServices() {
        List<String> smsEnabledServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.SMS_ENABLED_SERVICE);
        if (!CollectionUtils.isEmpty(smsEnabledServices)) {
                return smsEnabledServices;
        }
        return Collections.emptyList();
    }

    public boolean enableSMSCardInRecent() {

       Boolean enableSMSCardInRecent = FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.ENABLE_SMS_CARD_IN_RECENT);
        if (enableSMSCardInRecent != null) {
            return enableSMSCardInRecent;
        }
        return Boolean.FALSE;
    }

    public boolean getIsValidationBlock() {

        Boolean validationBlockForCDC = FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.VALIDATION_BLOCK_FOR_CDC);
        if (validationBlockForCDC != null) {
            return validationBlockForCDC;
        }
        return Boolean.FALSE;
    }




    public boolean enableSavedCardInReco() {
        Boolean enableSavedCardInReco = FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.ENABLE_SAVED_CARD_IN_RECO);
        if (enableSavedCardInReco != null) {
            return enableSavedCardInReco;
        }
        return Boolean.FALSE;
    }

    public List<String> getSMSBillUpdateServices() {
        List<String> smsBillUpdateServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.SMS_BILL_UPDATE_SERVICES);
        if (!CollectionUtils.isEmpty(smsBillUpdateServices)) {
            logger.info("[ServiceConfig.getSMSBillUpdateService] returning smsBillUpdateService flag");
            return smsBillUpdateServices;
        }
        return Collections.emptyList();
    }
    public Map<String, List<Integer>> getCategoryMapping() {

        Map<String, List<Integer>> categoryMapping = JsonUtils.convertObjectToMap(FeatureConfigCache.getInstance().getObject(ServiceNameConstants.CATEGORY_MAPPING));

        return categoryMapping;
    }

    public List<String> getSmartRecentsEnabledServices(){
        List<String> smartRecentsEnabledServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.SMART_RECENTS_ENABLED_SERVICES);
        if (!CollectionUtils.isEmpty(smartRecentsEnabledServices)) {
            return smartRecentsEnabledServices;
        }
        return Collections.emptyList();
    }

    public List<String> getSmartRecentRecoEnabledServices(){
        List<String> smartRecentsRecoEnabledServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.SMART_RECENTS_RECO_ENABLED_SERVICES);
        if (!CollectionUtils.isEmpty(smartRecentsRecoEnabledServices)) {
            return smartRecentsRecoEnabledServices;
        }
        return Collections.emptyList();
    }

    public Integer getPartialBillVisibilityDays() {
        return FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.PARTIAL_BILL_RECENT_EXPIRY_DAYS);
    }

    public List<String> getWhitelistedCustIdsForCDCRecoveryConsumer() {
        List<String> whitelistedCustIds = null;
        if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.WHITELISTED_CUSTIDS_FOR_CDC_RECOVERY) != null)
            whitelistedCustIds = FeatureConfigCache.getInstance().getList(ServiceNameConstants.WHITELISTED_CUSTIDS_FOR_CDC_RECOVERY);
        return whitelistedCustIds;
    }

    public Integer getPercentOfLiveTraffic() {
        return FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.PERCENT_LIVE_TRAFFIC);
    }

    public List<String> getWhitelistedCustIdsForMobilePrepaidNonRu() {
        List<String> whitelistedCustIds = null;
        if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.WHITELISTED_CUSTIDS_FOR_MOBILE_NON_RU) != null)
            whitelistedCustIds = FeatureConfigCache.getInstance().getList(ServiceNameConstants.WHITELISTED_CUSTIDS_FOR_MOBILE_NON_RU);
        return whitelistedCustIds;
    }

    public Integer getPercentOfLiveTrafficForNonRu() {
        return FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.PERCENT_LIVE_TRAFFIC_FOR_MOBILE_NON_RU);
    }

    public List<String> getWhitelistedCustIdsForMobileNonRUPersist() {
        List<String> whitelistedCustIds = null;
        if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.WHITELISTED_CUSTIDS_FOR_MOBILE_NON_RU_PERSIST) != null)
            whitelistedCustIds = FeatureConfigCache.getInstance().getList(ServiceNameConstants.WHITELISTED_CUSTIDS_FOR_MOBILE_NON_RU_PERSIST);
        return whitelistedCustIds;
    }
    public Integer getPercentOfLiveTrafficForMobileNonRUPersist() {
        return FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.PERCENT_LIVE_TRAFFIC_FOR_MOBILE_NON_RU_PERSIST);
    }



    public Boolean isRedirectionToRollbackTopic(){
        Boolean isRedirection = FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.REDIRECTION_TO_ROLLBACK_TOPIC);
        if (isRedirection != null)
            return isRedirection;
        return false;
    }

    /**
     *
     * @return int value in minutes
     */
    public Integer getTimeDiffThresholdBetweenRecentsAndCDC() {
        Integer value = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.TIME_DIFF_THRESHOLD_BETWEEN_RECENTS_AND_CDC_KEY);
        if(value == null)
            return Constants.TIME_DIFF_THRESHOLD_BETWEEN_RECENTS_AND_CDC_IN_MINUTE;
        return value;
    }

    public List<String> getPrepaidServicesEligibleForNonRuValidation() {
        List<String> prepaidServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.PREPAID_SERVICES_ELIGIBLE_FOR_NONRU_VALIDATION_KEY);
        if (!CollectionUtils.isEmpty(prepaidServices)) {
            logger.info("[ServiceConfig.getPrepaidServicesEligibleForNonRuValidation] returning list of prepaid services eligible for non ru validation");
            return prepaidServices;
        }
        return Collections.singletonList(Constants.ServiceTypeConstants.MOBILE);
    }

    public boolean getFastagMMVCheckOnlyFromDBFlag() {
        try{
            return FeatureConfigCache.getInstance().getBoolean("fastagMMVCheckOnlyFromDBFlag");
        }catch (Exception e){
            logger.warn("exception in getFastagMMVCheckOnlyFromDBFlag",e);
        }
        return false;
    }

    public Long getFastagWaitTime() {
        try{
            return FeatureConfigCache.getInstance().getLong("fastagWaitTime");
        }catch (Exception e){
            logger.warn("exception in getFastagMMVCheckOnlyFromDBFlag",e);
        }
        return 1000L;
    }

    public boolean isSuggestedCardEnabled() {
        try{
            if(Objects.nonNull(FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.ENABLE_SUGGESTED_CARDS))) {
                return FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.ENABLE_SUGGESTED_CARDS);
            }
        }catch (Exception e){
            logger.error("Exception in isSuggestedCardEnabled",e);
        }
        return false;
    }

    public List<String> getSystemFetchEventSources() {
        List<String> nonRuBillSources = FeatureConfigCache.getInstance().getList(ServiceNameConstants.SYSTEM_FETCH_EVENT_SOURCES);
        if (!CollectionUtils.isEmpty(nonRuBillSources)) {
            logger.info("[ServiceConfig.getSMSBillUpdateService] returning nonRuBillSources flag");
            return nonRuBillSources;
        }
        return Collections.emptyList();
    }

    public Boolean isAirtelPrepaidCSVRecentEnabled(){
        Boolean isAirtelPrepaidCSVRecentEnabled = false;
        try{
            isAirtelPrepaidCSVRecentEnabled = FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.AIRTEL_PREPAID_CSV_RECENT_ENABLED);
            if (isAirtelPrepaidCSVRecentEnabled != null)
                return isAirtelPrepaidCSVRecentEnabled;
        } catch(Exception e) {
            logger.warn("exception in isAirtelPrepaidCSVRecentEnabled",e);
        }
        return false;
    }

    public List<String> getSystemFetchBillSources() {
        List<String> billSources = FeatureConfigCache.getInstance().getList(ServiceNameConstants.SYSTEM_FETCH_BILL_SOURCES);
        if (!CollectionUtils.isEmpty(billSources)) {
            logger.info("[ServiceConfig.getSystemFetchBillSources] returning billSources flag");
            return billSources;
        }
        return Collections.emptyList();
    }

    public List<String> getBillSourcesServices() {
        List<String> billSourcesServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.BILL_SOURCES_SERVICES);
        if (!CollectionUtils.isEmpty(billSourcesServices)) {
            logger.info("[ServiceConfig.getBillSourcesServices] returning billSourcesServices flag");
            return billSourcesServices;
        }
        return Collections.emptyList();
    }

    public List<String> getSmsBillSources() {
        List<String> smsBillSources = FeatureConfigCache.getInstance().getList(ServiceNameConstants.SMS_BILL_SOURCES);
        if (!CollectionUtils.isEmpty(smsBillSources)) {
            logger.info("[ServiceConfig.getSystemFetchBillSources] returning smsBillSources flag");
            return smsBillSources;
        }
        return Collections.emptyList();
    }

    public long getFrequentOrderCCRecoExecutorTimeout(long defaultTimeout) {
        Long executionTimeout = FeatureConfigCache.getInstance().getLong(ServiceNameConstants.CC_RECO_EXECUTOR_TIMEOUT);
        if (executionTimeout == null || executionTimeout == 0) {
            executionTimeout = defaultTimeout;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG);
        }
        return executionTimeout;
    }

    public List<String> getWhielistOperatorServicesForDedup() {
        List<String> allowedServices = new ArrayList<>();
        if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.WHITELIST_OPERATOR_SERVICE_FOR_DEDUP) != null)
            allowedServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.WHITELIST_OPERATOR_SERVICE_FOR_DEDUP);
        return allowedServices;
    }

    public List<String> getDummyRechargeNumberAllowedServices() {
        List<String> dummyRechargeNumberAllowedServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.DUMMY_RECHARGE_NUMBER_ALLOWED_SERVICES);
        if (!CollectionUtils.isEmpty(dummyRechargeNumberAllowedServices)) {
            return dummyRechargeNumberAllowedServices;
        }
        return Collections.emptyList();
    }
    public List<String> getRUPartialBillRecoServices() {
        List<String> ruPartialBillRecoServices = FeatureConfigCache.getInstance().getList(Constants.RU_PARTIAL_BILL_RECO_SERVICES);
        if (!CollectionUtils.isEmpty(ruPartialBillRecoServices)) {
            return ruPartialBillRecoServices;
        }
        return Collections.emptyList();
    }

    public Integer getRUPartialBillRecoDueDateOffset() {
        Integer offset = FeatureConfigCache.getInstance().getInteger(Constants.RU_PARTIAL_BILL_CUSTOMER_BILL_DUE_DATE_OFFSET);
        if (offset != null) {
            return offset;
        }
        return Constants.RU_PARTIAL_BILL_CUSTOMER_BILL_DUE_DATE_OFFSET_DEFAULT;
    }


    public int getTxnTimesMaxSize() {
        Integer value = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.TXN_TIMES_MAX_SIZE);
        if(value == null)
            return Constants.NUMBER_CONSTANTS.TWELVE;
        return value;
    }

    public int getTxnTimesMonthsThreshold() {
        Integer value = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.TXN_TIMES_MONTH_THRESHOLD);
        if(value == null)
            return Constants.NUMBER_CONSTANTS.TWENTY;
        return value;
    }


    public List<String> getTxnTimeEnabledCategory() {
        List<String> txnTimesEnabledCategory = FeatureConfigCache.getInstance().getList(ServiceNameConstants.TXN_TIMES_ENABLED_CATEGORY);
        if (!CollectionUtils.isEmpty(txnTimesEnabledCategory)) {
            return txnTimesEnabledCategory;
        }
        return Collections.emptyList();
    }
    public List<String> getRuPartialBillUpdatedSourceList() {
        List<String> ruPartialBillUpdatedSourceList = FeatureConfigCache.getInstance().getList(Constants.RU_PARTIAL_BILL_UPDATED_SOURCE_LIST);
        if (!CollectionUtils.isEmpty(ruPartialBillUpdatedSourceList)) {
            return ruPartialBillUpdatedSourceList;
        }
        return Arrays.asList(Constants.FFR_SOURCE_KEY); // Default values
    }

    private List<String> getConsumerNameFieldAllowedServices(){
        List<String> consumerNameAllowedServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.CONSUMER_NAME_ALLOWED_SERVICES);
        if (!CollectionUtils.isEmpty(consumerNameAllowedServices)) {
            return consumerNameAllowedServices;
        }else {
            return Collections.emptyList();
        }
    }

    public boolean checkIfSubcriberConfigAllowed(String service) {
        List<String> allowedServicesSubscriberName= getConsumerNameFieldAllowedServices();
        return !allowedServicesSubscriberName.isEmpty() && allowedServicesSubscriberName.contains(service.toLowerCase());
    }

}
