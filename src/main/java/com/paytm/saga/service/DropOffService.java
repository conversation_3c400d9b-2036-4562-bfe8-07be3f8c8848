package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.DropOffBillsObject;
import com.paytm.saga.dto.DropOffResponse;
import com.paytm.saga.dto.DropOffResponseAdditionalData;
import com.paytm.saga.listeners.DropOffDBHelper;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.repository.DropOffRepository;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.CreditCardUtils;
import com.paytm.saga.util.OMSToRechargeStatus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.cassandra.core.CassandraTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class DropOffService {
    private final CustomLogger logger = CustomLogManager.getLogger(com.paytm.saga.service.DropOffService.class);
    private final DropOffRepository dropOffRepository;
    private final PlanExpiryHistoryService planExpiryHistoryService;
    private final ReminderHistoryService reminderHistoryService;
    private final DropOffDBHelper dropOffDBHelper;
    private Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    @Qualifier("sagaCassandraTemplate")
    private CassandraTemplate template;

    @Autowired
    public DropOffService(
            @NonNull DropOffRepository dropOffRepository,
            @NonNull PlanExpiryHistoryService planExpiryHistoryService,
            @NonNull ReminderHistoryService reminderHistoryService,
            @NonNull DropOffDBHelper dropOffDBHelper) {
        this.dropOffRepository = dropOffRepository;
        this.planExpiryHistoryService = planExpiryHistoryService;
        this.reminderHistoryService = reminderHistoryService;
        this.dropOffDBHelper = dropOffDBHelper;
    }

    public DropOff saveDropOff(DropOff dropOff){
        if(dropOff==null) return null;
        if(Constants.FINANCIAL_SERVICES.equalsIgnoreCase(dropOff.getService()) && EncryptionDecision.isDataEncryptionRequired(dropOff.getCustomerId())){
            return null;
        }
        return dropOffRepository.save(dropOff);
    }

    public List<DropOff> saveDropOff(List<DropOff> dropOffs){
        if(dropOffs==null || dropOffs.size()==0) return null;
        return dropOffRepository.saveAll(dropOffs);
    }

    public List<DropOff> findByCustomerId(Long customerId){
        return dropOffRepository.findByCustomerId(customerId);
    }

    public List<DropOff> findByCustomerIdInAndRechargeNumberAndService(List<Long> customerIds, String rechargeNumber , String service){
        return dropOffRepository.findTop100ByCustomerIdInAndRechargeNumberAndService(customerIds, rechargeNumber, service);
    }

    public List<DropOff> findByCustomerIdAndServiceIn(Long customerId, List<String> service)
    {
       return  template.select(generateQuery(customerId, service), DropOff.class);
    }

    private String generateQuery(Long customerId, List<String> services) {
        StringBuilder sb = new StringBuilder();

        sb.append("select * from drop_off where customerid = ");
        sb.append(customerId);
        if (!CollectionUtils.isEmpty(services) && services.size() == 1) {
            sb.append(" and service in ");
            sb.append("(");
            services.stream().forEach(
                    service -> {
                        sb.append("'" + service + "'");
                        sb.append(",");
                    });
            sb.deleteCharAt(sb.length() - 1);
            sb.append(") ");
        }

        sb.append(" limit 100");


        logger.trace("fetching from generateQuery using query : {}", sb);


        return sb.toString();

    }

    public List<DropOff> findByCustomerIdAndRechargeNumberAndServiceIn(Long customerId, String rechargeNumber, List<String> service){
        return dropOffRepository.findTop100ByCustomerIdAndRechargeNumberAndServiceIn(customerId, rechargeNumber, service);
    }

    public List<DropOff> findByCustomerIdAndRechargeNumberAndService(Long customerId, String rechargeNumber, String service){
        return dropOffRepository.findTop100ByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, service);
    }

    public DropOff getDropOff(Long customerId, String rechargeNumber, String service, String operator){
        List<String> services = new ArrayList<>();
        services.add(service);
        String paytype = null;
        Map<String , DropOff> dropOffMap = getDropOff(customerId, services , rechargeNumber, operator, Constants.CommonConstants.IS_COFT_FLAG_DISABLED);
        if(dropOffMap==null) return null;
        if (Constants.FINANCIAL_SERVICE.equalsIgnoreCase(service)){
            paytype = Constants.CREDIT_CARD_PAYTYPE;
        }
        DropOff finalDropOff = dropOffMap.get(getMapKey(rechargeNumber, service, operator,paytype));
        
        if(dropOffMap!=null && finalDropOff!=null){
            if(finalDropOff.getEventType()!=null){
                if(finalDropOff.getEventType().equals(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE)){
                    // Do not send Recharge Events for Chat as it already has transaction history.
                    return null;
                }
            }
            return finalDropOff;
        }
        return null;
    }

    public Map<String , DropOffResponse> getDropOffResponse(Long customerId, List<String> services, String rechargeNumber, String operator) {
        Map<String , DropOffResponse> dropOffMap = getDropOffResponse(customerId, services, rechargeNumber, operator, Constants.CommonConstants.IS_COFT_FLAG_DISABLED);
        return dropOffMap;
    }

    public Map<String , DropOffResponse> getDropOffResponse(Long customerId, List<String> services, String rechargeNumber, String operator, String isCoft){

        Map<String , DropOff> actualDropOff = getDropOff(customerId, services, rechargeNumber, operator, isCoft);
        Map<String , DropOffResponse> finalResponse =  new HashMap<String, DropOffResponse>();

        Map<String, DropOff> creditCardLatestDropoffMap = getLatestDropOffMapForCreditCard(actualDropOff) ;
        Map<String, Boolean> creditCardLatestMapKey = getCreditCardLatestMapKey(creditCardLatestDropoffMap);


        if(actualDropOff!=null){
            for (Map.Entry<String,DropOff> record : actualDropOff.entrySet()) {

                String map_key = record.getKey();
                DropOff dropOffRec = record.getValue();
                if(Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(dropOffRec.getPaytype())) {
                    if(creditCardLatestMapKey.get(map_key) == null)
                         continue; // continue as this is not latest transaction
                    map_key = getMapKey(dropOffRec.getBillsObj().get("mcn"), dropOffRec.getService(), dropOffRec.getOperator(), dropOffRec.getPaytype());
                }
                DropOffResponse dropOffResponse = prepareResponse(dropOffRec);
                finalResponse.put(map_key, dropOffResponse);

            }
        }
        return finalResponse;
    }

    private Map<String, Boolean> getCreditCardLatestMapKey(Map<String, DropOff> creditCardLatestDropoffMap) {
       Map<String, Boolean> creditCardLatestMapKey = new HashMap<>();

        if(!CollectionUtils.isEmpty(creditCardLatestDropoffMap)) {
            for (Map.Entry<String, DropOff> record : creditCardLatestDropoffMap.entrySet()) {
                DropOff dropOff = record.getValue();
                creditCardLatestMapKey.put(getMapKey(dropOff.getRechargeNumber(), dropOff.getService(), dropOff.getOperator(),dropOff.getPaytype()), Boolean.TRUE);
            }
        }
        return creditCardLatestMapKey;
    }

    private Map<String, DropOff> getLatestDropOffMapForCreditCard(Map<String, DropOff> actualDropOff) {
        Map<String, DropOff> creditCardLatestDropOff = new HashMap<>();

        if (!CollectionUtils.isEmpty(actualDropOff)) {
            for (Map.Entry<String, DropOff> record : actualDropOff.entrySet()) {
                DropOff dropOffRec = record.getValue();

                if (Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(dropOffRec.getPaytype())) {
                    String map_key = getMapKey(dropOffRec.getBillsObj().get("mcn"), dropOffRec.getService(), dropOffRec.getOperator(), dropOffRec.getPaytype());
                    String[] splitKey = map_key.split("_");

                    if (splitKey.length == 0 || dropOffRec.getTransactionTime() == null)
                        continue;

                    DropOff latestTransaction = creditCardLatestDropOff.get(splitKey[0]);

                    if (latestTransaction == null ||  latestTransaction.getTransactionTime().before(dropOffRec.getTransactionTime())) {
                        creditCardLatestDropOff.put(splitKey[0], dropOffRec);
                    }
                }
            }
        }
        return creditCardLatestDropOff;
    }

    //TODO remove this
    private void insertMostRecentDropOff(String map_key, DropOffResponse dropOffResponse, Map<String , DropOffResponse> finalResponse) {
        if (dropOffResponse.getPaytype() != null &&
                dropOffResponse.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
            if (dropOffResponse.getTimestamp() != null) {
                boolean isAfter = false;
                boolean isMatch = false;
                for (Map.Entry<String,DropOffResponse> entry : finalResponse.entrySet()) {
                    String key = entry.getKey();
                    String[] splitKey = map_key.split("_");
                    String[] splitMapKey = key.split("_");
                    if (splitKey.length == 0 || splitMapKey.length == 0) {
                        return;
                    }
                    if (splitKey[0].equals(splitMapKey[0])) {
                        isMatch = true;
                        if (finalResponse.get(key).getTimestamp() != null
                            && finalResponse.get(key).getTimestamp().before(dropOffResponse.getTimestamp())) {
                            isAfter = true; //existing value for key has older timeStamp than the current record,
                                            // i.e currrent record isAfter than the existing one
                            finalResponse.remove(key);
                        }
                    }
                }
                if (isAfter || !isMatch || finalResponse.isEmpty()) {
                    finalResponse.put(map_key, dropOffResponse);
                }
            }
        }
        else {
            finalResponse.put(map_key, dropOffResponse);
        }

    }

    public DropOffResponse prepareResponse(DropOff dropOff){
        DropOffResponse dropOffResponse = new DropOffResponse();
        if(dropOff!=null){
            dropOffResponse.setRecharge_number(dropOff.getRechargeNumber());
            if(dropOff.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
            	dropOffResponse.setRecharge_number(dropOff.getBillsObj().get("mcn"));
        	}
            dropOffResponse.setRecharge_number_2(dropOff.getRecharge_number_2());
            dropOffResponse.setRecharge_number_3(dropOff.getRecharge_number_3());
            dropOffResponse.setRecharge_number_4(dropOff.getRecharge_number_4());
            dropOffResponse.setRecharge_number_5(dropOff.getRecharge_number_5());
            dropOffResponse.setRecharge_number_6(dropOff.getRecharge_number_6());
            dropOffResponse.setRecharge_number_7(dropOff.getRecharge_number_7());
            dropOffResponse.setRecharge_number_8(dropOff.getRecharge_number_8());
            if(dropOff.getCategoryId()!=null){
                dropOffResponse.setCategory_id(Long.toString(dropOff.getCategoryId()));
            }
            if(dropOff.getProductId()!=null){
                dropOffResponse.setProduct_id(Long.toString(dropOff.getProductId()));
            }
            dropOffResponse.setService(dropOff.getService());
            dropOffResponse.setPaytype(dropOff.getPaytype());
            dropOffResponse.setType(dropOff.getEventType());
            dropOffResponse.setTimestamp(dropOff.getTransactionTime());
            dropOffResponse.setBills(prepareBillsObj(dropOff));
            DropOffResponseAdditionalData dropOffResponseAdditionalData = new DropOffResponseAdditionalData();
            if(dropOff.getBillsObj()!=null){
                if(dropOff.getBillsObj().get("consumerName")!=null){
                    dropOffResponse.setConsumerName(dropOff.getBillsObj().get("consumerName"));
                }
                if(dropOff.getBillsObj().get("RMN")!=null){
                    dropOffResponseAdditionalData.setMobileNumber(dropOff.getBillsObj().get("RMN"));
                }
                dropOffResponseAdditionalData.setCylinderAgencyName(dropOff.getBillsObj().get(Constants.CYLINDER_AGENCY_NAME));
                if(dropOff.getBillsObj().get(Constants.CommonConstants.CHANNEL) != null){
                    dropOffResponse.setChannel(dropOff.getBillsObj().get(Constants.CommonConstants.CHANNEL));
                }
            }
            dropOffResponse.setAdditionalData(dropOffResponseAdditionalData);
            dropOffResponse.setPaidOutside(dropOff.getPaidOutside());
            dropOffResponse.setIsAutomaticActive(dropOff.getIsAutomatic());
        }
        return dropOffResponse;
    }

    public List<DropOffBillsObject> prepareBillsObj(DropOff dropOff){
        DropOffBillsObject billObj = new DropOffBillsObject();
        List<DropOffBillsObject> billObjList = new ArrayList<>();
        if(dropOff!=null){

            try{
                if(dropOff.getAmount()!=null){
                    billObj.setAmount(Double.parseDouble(dropOff.getAmount()));
                }
            } catch(Exception e){
                logger.error("DropOff Service][prepareBillsObj] amount parse exception" , e);
            }
            if(dropOff.getEventType()!=null && dropOff.getEventType().equals(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE)){
                billObj.setOrderId(dropOff.getOrderId());
                String state = OMSToRechargeStatus.getRechargeStatusByOMSStatus(dropOff.getStatus(),
                        dropOff.getIn_response_code(), dropOff.getPayment_status());
                billObj.setState(state);
            }



            if(dropOff.getBillsObj()!=null) {
                if (dropOff.getBillsObj().get("prepaid_expiry_amount") !=null && Double.parseDouble(dropOff.getBillsObj().get("prepaid_expiry_amount")) > 0  ) {
                    if (dropOff.getBillsObj().get("expiry") != null) {
                        billObj.setExpiry(dropOff.getBillsObj().get("expiry"));
                    }
                }
                if (dropOff.getBillsObj().get("reminder_amount") !=null && Double.parseDouble(dropOff.getBillsObj().get("reminder_amount")) > 0  ) {
                    if (dropOff.getBillsObj().get("bill_date") != null) {
                        billObj.setBill_date(dropOff.getBillsObj().get("bill_date"));
                    }
                    if (dropOff.getBillsObj().get("due_date") != null) {
                        billObj.setDue_date(dropOff.getBillsObj().get("due_date"));
                    }
                }
                if(dropOff.getBillsObj().get("plan_bucket")!=null) {
                    billObj.setPlan_bucket(dropOff.getBillsObj().get("plan_bucket"));
                }
                
                try {
                    if(dropOff.getBillsObj().get(Constants.NEW_USER_KEY)==null) {
                        billObj.setPreviousOrderId("exist");
                    }
                }catch(Exception e){
                	logger.error("DropOff Service][prepareBillsObj] previous orderid parse exception" , e);
                }
                if(dropOff.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
                       if(dropOff.getBillsObj().get("reminder_min_amount") !=null)
                               billObj.setMin_due_amount(Double.parseDouble(dropOff.getBillsObj().get("reminder_min_amount")));
                       if (dropOff.getBillsObj().containsKey("par")) {
                           billObj.setPanUniqueReference(dropOff.getBillsObj().get("par"));
                           billObj.setTin(dropOff.getRecharge_number_4());
                       }
                       else if (dropOff.getBillsObj().containsKey("cin")) {
                           billObj.setCin(dropOff.getBillsObj().get("cin"));
                       }
                       if(dropOff.getBillsObj().get("originalAmt") !=null)
                               billObj.setOriginal_due_amount(Double.parseDouble(dropOff.getBillsObj().get("originalAmt")));
                       if(dropOff.getBillsObj().get("originalMinAmt") !=null)
                               billObj.setOriginal_min_due_amount(Double.parseDouble(dropOff.getBillsObj().get("originalMinAmt")));
                       if(dropOff.getBillsObj().get("reminder_amount") !=null)
                           billObj.setReminder_amount(Double.parseDouble(dropOff.getBillsObj().get("reminder_amount")));
               }


                billObj.setChannel(dropOff.getBillsObj().get(Constants.CommonConstants.CHANNEL));
            }
        }
        billObjList.add(billObj);
        return billObjList;
    }

    public Map<String , DropOff> getDropOff(Long customerId, List<String> services, String rechargeNumber, String operator, String isCoft) {
        List<DropOff> dropOffs = null;
        try{
            if(rechargeNumber == null){
                if(services == null || services.size()==0){
                    dropOffs = dropOffRepository.findByCustomerId(customerId);
                }
                else{
                    dropOffs = findByCustomerIdAndServiceIn(customerId, services);
                }
            } else {
            	dropOffs = dropOffRepository.findTop100ByCustomerIdAndRechargeNumberAndServiceIn(customerId, rechargeNumber, services);
            }
        } catch (Exception e){
            logger.error("[DropOff Service][getDropOff]: Exception {} {}", e , e.getMessage());
        }

        logger.debug("DropOff size is {}", dropOffs.size());
        dropOffs = maskMcnInDropOffsAndFilter(dropOffs, isCoft, services);

        logger.debug("DropOff size after filter is {}", dropOffs.size());
        
        if(dropOffs!=null && dropOffs.size() > 0){

            Map<String , List<DropOff>> dropOffListMap = createMap(dropOffs);
            Map<String , DropOff> actualDropOff = new HashMap<String, DropOff>();
            
            for (Map.Entry<String,List<DropOff>> record : dropOffListMap.entrySet()){
                String map_key = record.getKey();
                List<DropOff> recordsList = record.getValue();
                recordsList = filterEligibleDropOff(recordsList, operator);
                if(recordsList!=null && recordsList.size()>0){
                    DropOff firstRecord = recordsList.get(0);
                    if(firstRecord!=null){
                        if(firstRecord.getEventType()!=null){
                            if(firstRecord.getEventType().equals(Constants.CommonConstants.VALIDATION_MESSAGE_TYPE)){
                                actualDropOff.put(map_key, firstRecord);
                            } else if(firstRecord.getEventType().equals(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE)) {

                                String status = OMSToRechargeStatus.getRechargeStatusByOMSStatus(firstRecord.getStatus(), firstRecord.getIn_response_code(), firstRecord.getPayment_status());

                                if(status.equals(Constants.CommonConstants.OMS_SUCCESS_STATE)){
                                    if(firstRecord.getPaytype()!= null && firstRecord.getPaytype().equals(Constants.CommonConstants.PREPAID_PAYTYPE)) {
                                        DropOff prepaidDropOff = prepaidPlanBucketHandling(recordsList);
                                        if(prepaidDropOff!=null){
                                            actualDropOff.put(map_key, prepaidDropOff);
                                        }
                                    }
                                } else {
                                    actualDropOff.put(map_key, firstRecord);
                                }
                            }
                        }
                    }
                }
                else {
                    logger.info("[DropOff Service][getDropOff] : No eligible dropOff found" + customerId + services );
                }
            }
            
            actualDropOff = stampExpiryForPrepaid(actualDropOff);
            actualDropOff = postpaidCheckForPendingAmount(actualDropOff, customerId);
            return actualDropOff;
        } else{
            logger.info("[DropOff Service][getDropOff] : No dropOff found from DB");
        }
        return null;

    }

    public Map<String , DropOff> stampExpiryForPrepaid(Map<String , DropOff> dropOffMap){
        List<String> rechargeNumbers = new ArrayList<>();
        List<String> services = new ArrayList<>();
        for (Map.Entry<String,DropOff> record : dropOffMap.entrySet()){
            DropOff dropOffRecord = record.getValue();
            if(     dropOffRecord.getRechargeNumber()!=null &&
                    dropOffRecord.getService()!=null &&
                    dropOffRecord.getPaytype()!=null &&
                    dropOffRecord.getPaytype().equals(Constants.CommonConstants.PREPAID_PAYTYPE) ){

                if(Arrays.asList(Constants.CommonConstants.PLAN_SERVICES).contains(dropOffRecord.getService())){
                    rechargeNumbers.add(dropOffRecord.getRechargeNumber());
                    services.add(dropOffRecord.getService());
                }
            }
        }
        if(rechargeNumbers.size()==0 || services.size()==0) return dropOffMap;

        List<PlanExpiryHistory> plans = planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechargeNumbers, services);

        // sort plans
        Collections.sort(plans, new Comparator<PlanExpiryHistory>() {
            public int compare(PlanExpiryHistory o1, PlanExpiryHistory o2) {
                if(o1.getUpdated_at().getTime() < o2.getUpdated_at().getTime()){
                    return 1;
                } else return -1;
            }
        });

        for(PlanExpiryHistory planExpiryRecord : plans){
            String service = planExpiryRecord.getService();
            String rechargeNumber = planExpiryRecord.getRechargeNumber();
            String operator = planExpiryRecord.getOperator();
            String planBucket = planExpiryRecord.getPlan_bucket();
            String circle = planExpiryRecord.getCircle();
            String key = getMapKey(rechargeNumber, service, operator, Constants.PREPAID_PAYTYPE);
            Date plan_expiry = planExpiryRecord.getValidity_expiry_date();
            Double prepaid_expiry_amount =planExpiryRecord.getAmount();
            DropOff dropOffObj = dropOffMap.get(key);

            if(dropOffObj!=null){
                if(dropOffObj.getOperator()!=null && dropOffObj.getOperator().equals(operator)){
                    if(dropOffObj.getService()!=null && dropOffObj.getService().equals(service)){
                        if(
                            (dropOffObj.getCircle()!=null && dropOffObj.getCircle().equals(circle)) ||
                            (Arrays.asList(Constants.CommonConstants.WITHOUT_CIRCLE_SERVICES).contains(service))) {
                            if(dropOffObj.getBillsObj() == null)
                                dropOffObj.setBillsObj(new HashMap<String, String>());
                            if (dropOffObj.getBillsObj() != null) {
                                if ((dropOffObj.getBillsObj().get("plan_bucket") != null && dropOffObj.getBillsObj().get("plan_bucket").equals(planBucket)) ||
                                        (Arrays.asList(Constants.CommonConstants.WITHOUT_PLAN_BUCKET_SERVICES).contains(service))) {
                                    if (dropOffObj.getBillsObj().get("expiry") == null) {
                                        // Expiry is not already set, as we need to set only the latest expiry.
                                        if (plan_expiry != null) {
                                            String expiryDateString = formatter.format(plan_expiry);
                                            dropOffObj.getBillsObj().put("expiry", expiryDateString);
                                            dropOffObj.getBillsObj().put("prepaid_expiry_amount", String.valueOf(prepaid_expiry_amount));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return dropOffMap;
    }

    public DropOff prepaidPlanBucketHandling(List<DropOff> dropOffs){

        DropOff finalDropOff = null;
        if(dropOffs!=null){
            DropOff firstRecord = dropOffs.get(0);
            if(firstRecord!=null){

                if(Arrays.asList(Constants.CommonConstants.PLAN_SERVICES).contains(firstRecord.getService())){

                    if(firstRecord.getPaytype()!= null && firstRecord.getPaytype().equals(Constants.CommonConstants.PREPAID_PAYTYPE)){
                        // Iterate through remaining list and check if there is validation
                        // which satisfy the dropOff threshold is of another bucket and
                        // recharge is not done for that bucket by any user.
                        if(firstRecord.getBillsObj()!=null){

                            Map<String, DropOff> planBucketMap = new HashMap<String, DropOff>() {};

                            for(int i=dropOffs.size()-1;i>=0;i--){
                                if(dropOffs.get(i)!=null && dropOffs.get(i).getBillsObj()!=null && dropOffs.get(i).getEventType()!=null){
                                    String plan_bucket = dropOffs.get(i).getBillsObj().get("plan_bucket");
                                    if(plan_bucket==null && dropOffs.get(i).getService().toLowerCase().equals("dth")) {
                                    	plan_bucket=Constants.DTH_PLAN_BUCKET;
                                    }
                                    if(dropOffs.get(i).getEventType().equals(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE)){
                                        if(
                                                dropOffs.get(i).getBillsObj()!=null &&
                                                dropOffs.get(i).getBillsObj().get(Constants.CommonConstants.DONOT_REMINDME_FLAG)!=null &&
                                                dropOffs.get(i).getBillsObj().get(Constants.CommonConstants.DONOT_REMINDME_FLAG).equals("true")){

                                            planBucketMap.clear();
                                        } else{
                                            if(planBucketMap.get(plan_bucket)!=null){
                                                planBucketMap.remove(plan_bucket);
                                            }
                                        }
                                    } else{
                                        planBucketMap.put(plan_bucket, dropOffs.get(i));
                                    }
                                }

                            }

                            for (Map.Entry<String,DropOff> record : planBucketMap.entrySet()){
                                DropOff dropOffRecord = record.getValue();
                                if(finalDropOff==null){
                                    finalDropOff = record.getValue();
                                } else {
                                    if(finalDropOff.getTransactionTime().getTime() < dropOffRecord.getTransactionTime().getTime()){
                                        finalDropOff = record.getValue();
                                    }
                                }
                            }

                        }
                    }
                }
            }
        }
        return finalDropOff;

    }

    public Map<String , DropOff> postpaidCheckForPendingAmount(Map<String , DropOff> dropOffMap, Long customerId){
        List<String> rechargeNumbers = new ArrayList<>();
        List<String> services = new ArrayList<>();
        List<String> cins = new ArrayList<>();
        

        for (Map.Entry<String,DropOff> record : dropOffMap.entrySet()){
            DropOff dropOffRecord = record.getValue();
            if(dropOffRecord.getRechargeNumber()!=null
                    && dropOffRecord.getService()!=null
                    && dropOffRecord.getPaytype()!=null){
                if(Arrays.asList(Constants.CommonConstants.POSTPAID_PAYTYPES).contains(dropOffRecord.getPaytype()) || dropOffRecord.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)){
                    String rechargeNumber=dropOffRecord.getRechargeNumber();
                	if(dropOffRecord.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
                		cins.add(dropOffRecord.getRechargeNumber());
                		rechargeNumber=dropOffRecord.getBillsObj().get("mcn");             	
                    }

                	rechargeNumbers.add(rechargeNumber);
                    services.add(dropOffRecord.getService());
                }
            }
        }

        if(rechargeNumbers.size()==0 || services.size()==0) return dropOffMap;

        List<ReminderHistory> reminderRecords = reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumbers, services);

        // sort Reminder Records.
        Collections.sort(reminderRecords, new Comparator<ReminderHistory>() {
            public int compare(ReminderHistory o1, ReminderHistory o2) {
                Long t1 = o1.getUpdatedAt().getTime();
                Long t2 = o2.getUpdatedAt().getTime();
                return t2.compareTo(t1);
            }
        });
        for(ReminderHistory reminderRecord : reminderRecords){
            String service = reminderRecord.getService();
            String rechargeNumber = reminderRecord.getRechargeNumber();
            if(reminderRecord.getPaytype() !=null && reminderRecord.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
                if (reminderRecord.getPar() != null) {
                    rechargeNumber = reminderRecord.getPar();
                }
                else if (reminderRecord.getReference_id() != null) {
                    rechargeNumber = reminderRecord.getReference_id();
                }
    		}
            String operator = reminderRecord.getOperator();
            String circle = reminderRecord.getCircle();
            String paytype = reminderRecord.getPaytype();
            String key = getMapKey(rechargeNumber, service, operator, paytype);
            String due_date = null;
            if(reminderRecord.getDue_date() != null)
            	due_date = formatter.format(reminderRecord.getDue_date());
            String bill_date = null;
            if(reminderRecord.getBill_date() !=null)
            	bill_date =formatter.format(reminderRecord.getBill_date());
            Double amount = reminderRecord.getAmount();
            Double minAmount = reminderRecord.getCurrentMinBillAmount();
            Double originalMinAmount = reminderRecord.getOriginalMinBillAmount();
            Double originalAmount = reminderRecord.getOriginalAmount();
            Double lastPaidAmount=reminderRecord.getLastPaidAmount();
            Integer status=reminderRecord.getStatus();
            Integer isAutomatic=reminderRecord.getIs_automatic();
//            boolean ignoreRecord=false;
//            if(service.equals(Constants.CommonConstants.FINANCIAL_SERVICE)) {
//            	if(cins.indexOf(reminderRecord.getReference_id())<0) {
//					ignoreRecord=true;
//				}			
//    		}
            DropOff dropOffObj = dropOffMap.get(key);
            if(dropOffObj!=null){
                if(Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(dropOffObj.getPaytype()) || (dropOffObj.getOperator()!=null && dropOffObj.getOperator().equals(operator))){
                    if((dropOffObj.getCircle()==null && (circle == null || circle.equalsIgnoreCase("")))|| (dropOffObj.getCircle()!=null && dropOffObj.getCircle().equalsIgnoreCase(circle))){
                        if (dropOffObj.getBillsObj() != null) {
                            if (dropOffObj.getBillsObj().get("updated_via_reminder") == null) {
                                // Set latest billDate , dueDate , amount is not already set,
                                // as we need to stamp only the latest details.
                                dropOffObj.getBillsObj().put("bill_date", bill_date);
                                dropOffObj.getBillsObj().put("due_date", due_date);
                                try {
                                    dropOffObj.getBillsObj().put("reminder_amount", Double.toString(amount));
                                    if (minAmount != null) {
                                        dropOffObj.getBillsObj().put("reminder_min_amount", Double.toString(minAmount));
                                    }
                                    if (originalMinAmount != null)
                                        dropOffObj.getBillsObj().put("originalMinAmt", Double.toString(originalMinAmount));
                                    if (originalAmount != null)
                                        dropOffObj.getBillsObj().put("originalAmt", Double.toString(originalAmount));
                                    if (lastPaidAmount != null)
                                        dropOffObj.getBillsObj().put("lastPaidAmount", Double.toString(lastPaidAmount));
                                } catch (Exception e) {
                                    logger.error("[DropOff Service][postpaidCheckForPendingAmount] amount toString exception", e);
                                }
                                dropOffObj.getBillsObj().put("updated_via_reminder", "true");
                            }
                        } else {
                            Map<String, String> billObj = new HashMap<String, String>();

                            billObj.put("bill_date", bill_date);
                            billObj.put("due_date", due_date);
                            try {
                                billObj.put("reminder_amount", Double.toString(amount));
                                if (minAmount != null) {
                                    billObj.put("reminder_min_amount", Double.toString(minAmount));
                                }
                                if (originalMinAmount != null)
                                    billObj.put("originalMinAmt", Double.toString(originalMinAmount));
                                if (originalAmount != null)
                                    billObj.put("originalAmt", Double.toString(originalAmount));
                                if (lastPaidAmount != null)
                                    dropOffObj.getBillsObj().put("lastPaidAmount", Double.toString(lastPaidAmount));
                            } catch (Exception e) {
                                logger.error("[DropOff Service][postpaidCheckForPendingAmount] amount toString exception1", e);
                            }
                            billObj.put("updated_via_reminder", "true");
                            dropOffObj.setBillsObj(billObj);
                        }
                        if (status != null && status == 14 && lastPaidAmount != null) {
                            dropOffObj.setPaidOutside(Constants.PAID_OUTSIDE_PAYTM);
                        }
                        if (dropOffObj.getIsAutomatic() == null) {
                            if (isAutomatic != null) {
                                if (isAutomatic == 1) {
                                    dropOffObj.setIsAutomatic(true);
                                } else
                                    dropOffObj.setIsAutomatic(false);
                            }
                        }
                    }
                }
            }
        }

        Map<String , DropOff> actualDropOffMap = new HashMap<String, DropOff>();

        for (Map.Entry<String,DropOff> record : dropOffMap.entrySet()){
            DropOff dropOffRecord = record.getValue();
            String key = record.getKey();
            if(dropOffRecord!=null){
                if(dropOffRecord.getEventType()!=null &&
                        dropOffRecord.getEventType().equals(Constants.CommonConstants.VALIDATION_MESSAGE_TYPE) &&
                        dropOffRecord.getPaytype()!=null &&
                        (Arrays.asList(Constants.CommonConstants.POSTPAID_PAYTYPES).contains(dropOffRecord.getPaytype()) || dropOffRecord.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE))){
                    if (dropOffRecord.getBillsObj()!=null){
                        if(dropOffRecord.getBillsObj().get("updated_via_reminder")!=null){
                            // Only consider dropOff is reminder data exits and amount > 0
                            String amountString = dropOffRecord.getBillsObj().get("reminder_amount");
                            Double amount = null;
                            try{
                            	amount = Double.parseDouble(amountString);
                            } catch (Exception e){
                                logger.error("[DropOff Service][postpaidCheckForPendingAmount] amount parse exception" , e);
                            }
                            if(amount!=null && amount>0){
                                dropOffRecord.setAmount(amountString);
                                actualDropOffMap.put(key, dropOffRecord);
                            }else if(dropOffRecord.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
                            	actualDropOffMap.put(key, dropOffRecord);
                            }
                        }else if(dropOffRecord.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
                        	actualDropOffMap.put(key, dropOffRecord);
                        }
                    }
                } else {
                    actualDropOffMap.put(key, dropOffRecord);
                }
            }
        }
        return actualDropOffMap;

    }

    public Boolean filterByTimeStamp(DropOff dropOff){
        Date validationTime = dropOff.getTransactionTime();
        Date curDate = new Date();
        long timeDiff = curDate.getTime() - validationTime.getTime();
        long differenceInMinutes = (timeDiff / (1000 * 60));
        int serviceThreshold = dropOffDBHelper.getDropOffThreshold(dropOff,Constants.DROP_OFF_READ);

        logger.trace(" filterByTimeStamp serviceThreshold {} differenceInMinutes {}", serviceThreshold, differenceInMinutes);

        if(serviceThreshold<=0) {
        	if(dropOff.getEventType()!=null &&
                    dropOff.getEventType().equals(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE)){
                serviceThreshold = Constants.CommonConstants.RECHARGE_DROP_OFF_DEFAULT_THRESHOLD_IN_MINUTES;
            } else{
                serviceThreshold = Constants.CommonConstants.VALIDATION_DROP_OFF_DEFAULT_THRESHOLD_IN_MINUTES;
            }
        }

        logger.trace("serviceThreshold {}", serviceThreshold);


        if(differenceInMinutes < serviceThreshold){
            return true;
        }

        return false;
    }

    public Boolean filterByOperator(DropOff dropOff, String operator){
        if(dropOff!=null){
            if(operator!=null){
                if(dropOff.getOperator()!=null && dropOff.getOperator().equals(operator)){
                    return true;
                }
            }
        }
        return false;
    }

    public List<DropOff> filterEligibleDropOff(List<DropOff> dropOffs, String operator){
        List<DropOff> filteredDropOffs = new ArrayList<>();

        for(DropOff dropOff : dropOffs){
            if(filterByTimeStamp(dropOff)){
                if(operator==null){
                    filteredDropOffs.add(dropOff);
                } else {
                    if(filterByOperator(dropOff, operator)){
                        filteredDropOffs.add(dropOff);
                    }
                }
            }
        }
        return filteredDropOffs;
    }

    private Map<String , List<DropOff>> createMap(List<DropOff> dropOffs){
        Map<String , List<DropOff>> listMap      = new HashMap<String, List<DropOff>>();
        Map<String , String> rechargeOperatorMap = new HashMap<String, String>();
        List<String> serviceIgnoreOperator = new ArrayList<>(Arrays.asList(Constants.DROPOFF_SERVICES_IGNORE_OPERATOR));
        for (DropOff dropOff : dropOffs) {
            String service = dropOff.getService();
            String operator = dropOff.getOperator();
            String rechargeNumber = dropOff.getRechargeNumber();
            String paytype = dropOff.getPaytype();
            
            if(serviceIgnoreOperator.contains(service.toLowerCase())) {
            	String key= rechargeNumber+"_"+service;
            	Boolean isKeyPresent = rechargeOperatorMap.containsKey(key);
            	if(isKeyPresent && 
            			!StringUtils.equalsIgnoreCase(operator, rechargeOperatorMap.get(key))) {
            		continue;
            	}else if(!isKeyPresent){
            		rechargeOperatorMap.put(key, operator);
            	}
            }
            
            if(listMap.get(getMapKey(rechargeNumber, service, operator, paytype)) == null){
                List<DropOff> newList = new ArrayList<>();
                newList.add(dropOff);
                listMap.put(getMapKey(rechargeNumber, service, operator, paytype), newList);
            } else {
                listMap.get(getMapKey(rechargeNumber, service, operator, paytype)).add(dropOff);
            }
        }
        return listMap;
    }

    private String getMapKey(String rechargeNumber, String service, String operator, String paytype){
        if (Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(paytype)){
            operator = Constants.CommonConstants.CC_DEFAULT_OPERATOR;
        }
        return rechargeNumber + "_" + service + "_" + operator;
    }

    private List<DropOff> maskMcnInDropOffsAndFilter(List<DropOff> dropOffs, String isCoft, List<String> services) {

        boolean filterBasedOnService = false;
        Set<String> servicesSet = null;
        List<DropOff> filteredDropOff = new ArrayList<>();

        if (!CollectionUtils.isEmpty(services) && services.size() > 1) {
            filterBasedOnService = true;
            servicesSet = CommonUtils.convertToSetCaseInsensitive(services);
        }

        for (DropOff dropOff : dropOffs) {
            if (filterBasedOnService && !servicesSet.contains(StringUtils.lowerCase(dropOff.getService())))
                continue;;
            if (dropOff.getBillsObj() != null && dropOff.getPaytype() != null
                && dropOff.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
                String mcn = dropOff.getBillsObj().get("mcn");
                String maskedMcn = mcn;
                if (isCoft.equals(Constants.CommonConstants.IS_COFT_FLAG_ENABLED)) {
                    maskedMcn = CreditCardUtils.returnMaskedMcn(mcn);
                }
                dropOff.getBillsObj().put("mcn", maskedMcn);
            }

            filteredDropOff.add(dropOff);
        }

        return filteredDropOff;

    }

}
