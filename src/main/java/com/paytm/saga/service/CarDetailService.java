package com.paytm.saga.service;

import com.datastax.oss.driver.api.core.cql.PagingState;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.CarDetail;
import com.paytm.saga.model.CarDetailColour;
import com.paytm.saga.recent.repository.CarDetailColourRepository;
import com.paytm.saga.recent.repository.CarDetailRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.cassandra.CassandraUncategorizedException;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import static com.paytm.saga.common.constant.Constants.CAR_DETAIL_MAX_PAGE_SIZE;


@Service
public class CarDetailService {

    private static final Logger logger = LogManager.getLogger(CarDetailService.class);
    private final CarDetailRepository carDetailRepository;
    private final CarDetailColourRepository carDetailColourRepository;

    @Autowired
    private MetricsHelper metricsHelper;

    @Autowired
    public CarDetailService(@NonNull CarDetailRepository carDetailRepository, CarDetailColourRepository carDetailColourRepository) {
        this.carDetailRepository = carDetailRepository;
        this.carDetailColourRepository = carDetailColourRepository;
    }

    public ResponseEntity<CarDetailResponse> fetchCardDetails(String make, @Nullable String lastRecordId){
        try {
            final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(lastRecordId);
            Slice<CarDetail> cardDetails = carDetailRepository.findByMake(make, cassandraPageRequest);
            return new ResponseEntity<>(convertCarDetailsToCarResponse(new ResponsePage<>(cardDetails)), HttpStatus.OK);
        } catch (CassandraUncategorizedException e) {
            if (e.getMessage() != null && e.getMessage().contains(Constants.MetricConstants.INVALID_PAGING_STATE_ERROR_MESSAGE)) {
                logger.warn("[CarDetailService][fetchCardDetails] Invalid paging state detected, falling back to first page. " +
                                "Make: {}, LastRecordId length: {}, Error: {}",
                        make, lastRecordId != null ? lastRecordId.length() : 0, e.getMessage());
                // Record metrics for monitoring
                metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR,
                        Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE + "_INVALID_FALLBACK");
                try {
                    // Create first page request and retry
                    final CassandraPageRequest firstPageRequest = createCassandraPageRequest(null);
                    Slice<CarDetail> cardDetails = carDetailRepository.findByMake(make, firstPageRequest);
                    return new ResponseEntity<>(convertCarDetailsToCarResponse(new ResponsePage<>(cardDetails)), HttpStatus.OK);
                } catch (Exception retryException) {
                    logger.error("[CarDetailService][fetchCardDetails] :: Error during fallback retry :", retryException);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
            logger.error("[CarDetailService][fetchCardDetails] :: Cassandra error :", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e){
            logger.error("[CarDetailService][fetchCardDetails] :: Error while fetching car details :", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private CarDetailResponse convertCarDetailsToCarResponse(ResponsePage<CarDetail> cardDetails){
        CarDetailResponse carDetailResponse = new CarDetailResponse();
        List<CarDetailDTO> carDetailDTOList = new ArrayList<>();
        for (CarDetail carDetail: cardDetails.getContent()){
            CarDetailDTO carDetailDTO = new CarDetailDTO();
            carDetailDTO.setMake(carDetail.getKey().getMake());
            carDetailDTO.setModel(carDetail.getKey().getModel());
            carDetailDTO.setVariantId(carDetail.getKey().getVariantId());
            carDetailDTO.setVariant(carDetail.getVariant());
            carDetailDTO.setDisplayName(carDetail.getDisplayName());
            carDetailDTOList.add(carDetailDTO);
        }
        carDetailResponse.setData(carDetailDTOList);
        carDetailResponse.setAvailableNext(cardDetails.getHasNext());
        carDetailResponse.setLastRecordId(cardDetails.getPagingState());
        return carDetailResponse;
    }

    public ResponseEntity<CarDetailColourResponse> fetchCarColourDetails(String make, @Nullable String lastRecordId){
        try {
            final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(lastRecordId);
            Slice<CarDetailColour> cardDetails = carDetailColourRepository.findByMake(make, cassandraPageRequest);
            return new ResponseEntity<>(convertCarColourDetailsToCarColourResponse(new ResponsePage<>(cardDetails)), HttpStatus.OK);
        } catch (Exception e){
            logger.error("[CarDetailService][fetchCarColourDetails] :: Error while fetching car colour data :", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private CarDetailColourResponse convertCarColourDetailsToCarColourResponse(ResponsePage<CarDetailColour> cardDetails){
        CarDetailColourResponse carDetailColourResponse = new CarDetailColourResponse();
        List<CarDetailColourDTO> carDetailDTOList = new ArrayList<>();
        for (CarDetailColour carDetailColour: cardDetails.getContent()){
            CarDetailColourDTO carDetailDTO = new CarDetailColourDTO();
            carDetailDTO.setMake(carDetailColour.getKey().getMake());
            carDetailDTO.setModel(carDetailColour.getKey().getModel());
            carDetailDTO.setColour(carDetailColour.getKey().getColour());
            carDetailDTO.setImageURL(carDetailColour.getImageURL());
            carDetailDTOList.add(carDetailDTO);
        }
        carDetailColourResponse.setData(carDetailDTOList);
        carDetailColourResponse.setAvailableNext(cardDetails.getHasNext());
        carDetailColourResponse.setLastRecordId(cardDetails.getPagingState());
        return carDetailColourResponse;
    }

    private CassandraPageRequest createCassandraPageRequest(final String pagingState) {
        final PageRequest pageRequest = PageRequest.of(0, CAR_DETAIL_MAX_PAGE_SIZE);
        if (pagingState != null) {
            try {
                logger.info("Processing paging state: length={}", pagingState.length());

                // Try Base64 decoding first (new format)
                byte[] decodedBytes = Base64.getDecoder().decode(pagingState);
                ByteBuffer pagingStateBuffer = ByteBuffer.wrap(decodedBytes);
                metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE);
                return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
            } catch (Exception e) {
                logger.error("Failed to parse paging state - length: {}, error: {}",
                        pagingState.length(), e.getMessage());
                // Try fallback to old format for backward compatibility
                try {
                    logger.info("Attempting fallback to PagingState.fromString format");
                    final PagingState pageState = PagingState.fromString(pagingState);
                    ByteBuffer pagingStateBuffer = ByteBuffer.wrap(pageState.toBytes());
                    metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE);
                    return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
                } catch (Exception fallbackException) {
                    logger.error("Fallback parsing also failed: {}", fallbackException.getMessage());
                    metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR, Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE);
                    return CassandraPageRequest.of(pageRequest, null);
                }
            }
        }

        metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE_DEFAULT);
        return CassandraPageRequest.of(pageRequest, null);

    }



}
