package com.paytm.saga.service;

import com.datastax.oss.driver.api.core.cql.PagingState;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.*;
import com.paytm.saga.executors.FastagVehicleDetailsExecutorConfig;
import com.paytm.saga.model.CarDetail;
import com.paytm.saga.model.CarDetailColour;
import com.paytm.saga.model.CarVariantDetail;
import com.paytm.saga.recent.repository.CarDetailColourRepository;
import com.paytm.saga.recent.repository.CarDetailRepository;
import com.paytm.saga.recent.repository.CarVariantDetailRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.cassandra.CassandraUncategorizedException;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.*;

import static com.paytm.saga.common.constant.Constants.CAR_DETAIL_MAX_PAGE_SIZE;


@Service
public class CarDetailService {

    private static final Logger logger = LogManager.getLogger(CarDetailService.class);
    private final CarDetailRepository carDetailRepository;
    private final CarDetailColourRepository carDetailColourRepository;
    private final CarVariantDetailRepository carVariantDetailRepository;

    private ExecutorService fastagVehicleDetailsExecutor;
    private FastagVehicleDetailsExecutorConfig fastagVehicleDetailsExecutorConfig;

    @Autowired
    private MetricsHelper metricsHelper;

    @Autowired
    public CarDetailService(@NonNull CarDetailRepository carDetailRepository, CarDetailColourRepository carDetailColourRepository, CarVariantDetailRepository carVariantDetailRepository,
                            @Qualifier("fastagVehicleDetailsExecutor") ExecutorService fastagVehicleDetailsExecutor,
                            FastagVehicleDetailsExecutorConfig fastagVehicleDetailsExecutorConfig) {
        this.carDetailRepository = carDetailRepository;
        this.carDetailColourRepository = carDetailColourRepository;
        this.carVariantDetailRepository = carVariantDetailRepository;
        this.fastagVehicleDetailsExecutor = fastagVehicleDetailsExecutor;
        this.fastagVehicleDetailsExecutorConfig = fastagVehicleDetailsExecutorConfig;
    }

    public ResponseEntity<CarDetailResponse> fetchCardDetails(String make, @Nullable String lastRecordId){
        try {
            final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(lastRecordId);
            Slice<CarDetail> cardDetails = carDetailRepository.findByMake(make, cassandraPageRequest);
            return new ResponseEntity<>(convertCarDetailsToCarResponse(new ResponsePage<>(cardDetails)), HttpStatus.OK);
        } catch (CassandraUncategorizedException e) {
            if (e.getMessage() != null && e.getMessage().contains(Constants.MetricConstants.INVALID_PAGING_STATE_ERROR_MESSAGE)) {
                logger.warn("[CarDetailService][fetchCardDetails] Invalid paging state detected, falling back to first page. " +
                           "Make: {}, LastRecordId length: {}, Error: {}",
                           make, lastRecordId != null ? lastRecordId.length() : 0, e.getMessage());

                // Record metrics for monitoring
                metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR,
                    Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE + "_INVALID_FALLBACK");

                try {
                    // Create first page request and retry
                    final CassandraPageRequest firstPageRequest = createCassandraPageRequest(null);
                    Slice<CarDetail> cardDetails = carDetailRepository.findByMake(make, firstPageRequest);
                    return new ResponseEntity<>(convertCarDetailsToCarResponse(new ResponsePage<>(cardDetails)), HttpStatus.OK);
                } catch (Exception retryException) {
                    logger.error("[CarDetailService][fetchCardDetails] :: Error during fallback retry :", retryException);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
            logger.error("[CarDetailService][fetchCardDetails] :: Cassandra error :", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e){
            logger.error("[CarDetailService][fetchCardDetails] :: Error while fetching car details :", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public ResponseEntity<CarVariantImageResponse> fetchCarVariantImageDetails(CarVariantImageRequest carVariantImageRequest){
        CarVariantImageResponse carVariantImageResponse = new CarVariantImageResponse();
        try {
            String makeId = carVariantImageRequest.getMakeId();
            String modelId = carVariantImageRequest.getModelId();
            String variantId = carVariantImageRequest.getVariantId();
            if(StringUtils.isBlank(variantId) && StringUtils.isBlank(makeId) && StringUtils.isBlank(modelId)){
                logger.warn("[fetchCarVariantImageDetails] :: mandatory params are missing, variantId - {}, makeId - {}, modelId - {}", variantId, makeId, modelId);
                return new ResponseEntity<>(carVariantImageResponse, HttpStatus.OK);
            }
            Future<CarVariantDetailDTO> variantDetailDTOFuture = fastagVehicleDetailsExecutor.submit(() -> {
                if (StringUtils.isNotBlank(variantId)) {
                    try {
                        CarVariantDetail carVariantDetail = carVariantDetailRepository.findByVariantId(Long.parseLong(variantId));
                        return convertCarVariantDetailToCarVariantDetailDTO(carVariantDetail);
                    } catch (Exception ex) {
                        metricsHelper.recordSuccessRateForMultipleTags("fetch_variant_details", new ArrayList<>(Arrays.asList("event_type:db_read", "status:error")));
                        logger.warn("[fetchCarVariantImageDetails] :: exception occurred while getting variant info from DB, variantId - {}, ex msg - {}", variantId, ex.getMessage(), ex);
                    }
                }
                return null;
            });
            Future<List<CarColourImageDetailDTO>> colourImageDetailsFuture = fastagVehicleDetailsExecutor.submit(() -> {
                if (StringUtils.isNotBlank(makeId) && StringUtils.isNotBlank(modelId)) {
                    try {
                        List<CarDetailColour> carDetailColourList = carDetailColourRepository.findByMakeIdAndModelId(makeId, modelId);
                        return convertCarColourDetailsToCarColourImageDetailDTO(carDetailColourList);
                    } catch (Exception ex) {
                        metricsHelper.recordSuccessRateForMultipleTags("fetch_image_colour_details", new ArrayList<>(Arrays.asList("event_type:db_read", "status:error")));
                        logger.warn("[fetchCarVariantImageDetails] :: exception occurred while getting colour image info from DB, makeId - {},  modelId - {}, ex msg - {}", variantId, ex.getMessage(), ex);
                    }
                }
                return null;
            });
            CarVariantDetailDTO carVariantDetailDTO = null;
            List<CarColourImageDetailDTO> carColourImageDetailDTOList = null;

            try {
                carVariantDetailDTO = variantDetailDTOFuture.get(fastagVehicleDetailsExecutorConfig.getTimeout(), TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                metricsHelper.recordSuccessRate("fetch_variant_details", "variant_info_timeout");
                logger.error("[fetchCarVariantImageDetails] :: timeout while getting variant info, variantId - {}", variantId);
            }

            try {
                carColourImageDetailDTOList = colourImageDetailsFuture.get(fastagVehicleDetailsExecutorConfig.getTimeout(), TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                metricsHelper.recordSuccessRate("fetch_image_colour_details", "colour_colour_image_info_timeout");
                logger.error("[fetchCarVariantImageDetails] :: timeout while getting colour image info, makeId - {}, modelId - {}", makeId, modelId);
            }
            if (carVariantDetailDTO != null) {
                carVariantImageResponse.setVariantInfo(carVariantDetailDTO);
            } else if (StringUtils.isNotBlank(variantId)) {
                metricsHelper.recordSuccessRate("fetch_variant_details", "variant_info_is_missing");
                logger.warn("[fetchCarVariantImageDetails] :: variant_info_is_missing for variantId - {}", variantId);
            }
            if (carColourImageDetailDTOList != null) {
                carVariantImageResponse.setColourImageInfoList(carColourImageDetailDTOList);
            } else if (StringUtils.isNotBlank(makeId) && StringUtils.isNotBlank(modelId)) {
                metricsHelper.recordSuccessRate("fetch_image_colour_details", "colour_image_info_is_missing");
                logger.warn("[fetchCarVariantImageDetails] :: colour_image_info_is_missing for makeId - {} and modelId - {}", makeId, modelId);
            }
            logger.info("[fetchCarVariantImageDetails] :: returning response for makeId - {}, modelId - {}, variantId - {}, response - {}", makeId, modelId, variantId, carVariantImageResponse);
            return new ResponseEntity<>(carVariantImageResponse, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("[fetchCarVariantImageDetails] :: Error while fetching car variant and image details, ex msg - {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private CarVariantDetailDTO convertCarVariantDetailToCarVariantDetailDTO(CarVariantDetail carVariantDetail) {
        CarVariantDetailDTO carVariantDetailDTO = new CarVariantDetailDTO();
        carVariantDetailDTO.setMake(carVariantDetail.getMake());
        carVariantDetailDTO.setModel(carVariantDetail.getModel());
        carVariantDetailDTO.setVariantId(carVariantDetail.getKey().getVariantId());
        carVariantDetailDTO.setVariant(carVariantDetail.getVariant());
        carVariantDetailDTO.setDisplayName(carVariantDetail.getDisplayName());
        return carVariantDetailDTO;
    }

    private List<CarColourImageDetailDTO> convertCarColourDetailsToCarColourImageDetailDTO(List<CarDetailColour> carDetailColourList){
        List<CarColourImageDetailDTO> carColourImageDetailDTOList = new ArrayList<>();
        for (CarDetailColour carDetailColour: carDetailColourList){
            CarColourImageDetailDTO carColourImageDetailDTO = new CarColourImageDetailDTO();
            carColourImageDetailDTO.setMake(carDetailColour.getKey().getMake());
            carColourImageDetailDTO.setModel(carDetailColour.getKey().getModel());
            carColourImageDetailDTO.setColour(carDetailColour.getKey().getColour());
            carColourImageDetailDTO.setImageURL(carDetailColour.getImageURL());
            carColourImageDetailDTOList.add(carColourImageDetailDTO);
        }
        return carColourImageDetailDTOList;
    }

    private CarDetailResponse convertCarDetailsToCarResponse(ResponsePage<CarDetail> cardDetails){
        CarDetailResponse carDetailResponse = new CarDetailResponse();
        List<CarDetailDTO> carDetailDTOList = new ArrayList<>();
        for (CarDetail carDetail: cardDetails.getContent()){
            CarDetailDTO carDetailDTO = new CarDetailDTO();
            carDetailDTO.setMake(carDetail.getKey().getMake());
            carDetailDTO.setModel(carDetail.getKey().getModel());
            carDetailDTO.setVariantId(carDetail.getKey().getVariantId());
            carDetailDTO.setVariant(carDetail.getVariant());
            carDetailDTO.setDisplayName(carDetail.getDisplayName());
            carDetailDTOList.add(carDetailDTO);
        }
        carDetailResponse.setData(carDetailDTOList);
        carDetailResponse.setAvailableNext(cardDetails.getHasNext());
        carDetailResponse.setLastRecordId(cardDetails.getPagingState());
        return carDetailResponse;
    }

    public ResponseEntity<CarDetailColourResponse> fetchCarColourDetails(String make, @Nullable String lastRecordId){
        try {
            final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(lastRecordId);
            Slice<CarDetailColour> cardDetails = carDetailColourRepository.findByMake(make, cassandraPageRequest);
            return new ResponseEntity<>(convertCarColourDetailsToCarColourResponse(new ResponsePage<>(cardDetails)), HttpStatus.OK);
        } catch (Exception e){
            logger.error("[CarDetailService][fetchCarColourDetails] :: Error while fetching car colour data :", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private CarDetailColourResponse convertCarColourDetailsToCarColourResponse(ResponsePage<CarDetailColour> cardDetails){
        CarDetailColourResponse carDetailColourResponse = new CarDetailColourResponse();
        List<CarDetailColourDTO> carDetailDTOList = new ArrayList<>();
        for (CarDetailColour carDetailColour: cardDetails.getContent()){
            CarDetailColourDTO carDetailDTO = new CarDetailColourDTO();
            carDetailDTO.setMake(carDetailColour.getKey().getMake());
            carDetailDTO.setModel(carDetailColour.getKey().getModel());
            carDetailDTO.setColour(carDetailColour.getKey().getColour());
            carDetailDTO.setImageURL(carDetailColour.getImageURL());
            carDetailDTOList.add(carDetailDTO);
        }
        carDetailColourResponse.setData(carDetailDTOList);
        carDetailColourResponse.setAvailableNext(cardDetails.getHasNext());
        carDetailColourResponse.setLastRecordId(cardDetails.getPagingState());
        return carDetailColourResponse;
    }

    private CassandraPageRequest createCassandraPageRequest(final String pagingState) {
        final PageRequest pageRequest = PageRequest.of(0, CAR_DETAIL_MAX_PAGE_SIZE);
        if (pagingState != null) {
            try {
                logger.info("Processing paging state: length={}", pagingState.length());

                // Try Base64 decoding first (new format)
                byte[] decodedBytes = Base64.getDecoder().decode(pagingState);
                ByteBuffer pagingStateBuffer = ByteBuffer.wrap(decodedBytes);
                metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE);
                return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
            } catch (Exception e) {
                logger.error("Failed to parse paging state - length: {}, error: {}",
                        pagingState.length(), e.getMessage());
                // Try fallback to old format for backward compatibility
                try {
                    logger.info("Attempting fallback to PagingState.fromString format");
                    final PagingState pageState = PagingState.fromString(pagingState);
                    ByteBuffer pagingStateBuffer = ByteBuffer.wrap(pageState.toBytes());
                    metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE);
                    return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
                } catch (Exception fallbackException) {
                    logger.error("Fallback parsing also failed: {}", fallbackException.getMessage());
                    metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR, Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE);
                    return CassandraPageRequest.of(pageRequest, null);
                }
            }
        }

        metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CAR_DETAIL_SERVICE_DEFAULT);
        return CassandraPageRequest.of(pageRequest, null);

    }



}
