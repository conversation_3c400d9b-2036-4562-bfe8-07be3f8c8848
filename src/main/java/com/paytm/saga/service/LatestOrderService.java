package com.paytm.saga.service;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.LatestOrdersRequest;
import com.paytm.saga.dto.LatestOrdersResponse;
import com.paytm.saga.dto.LatestOrderResponseWrapper;

import java.util.List;

public interface LatestOrderService {
    List<LatestOrdersResponse> getLatestOrders(LatestOrdersRequest request) throws RechargeSagaBaseException;

    LatestOrderResponseWrapper getLatestOrderWrapper(LatestOrdersRequest request) throws RechargeSagaBaseException;
}
