package com.paytm.saga.service.aggregator.pidmapmanager;

import com.paytm.saga.model.ActiveInactiveMap;
import com.paytm.saga.dto.CvrActiveInactiveApiResponse;
import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.service.GenericRestClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


@Service
public class ActiveInactivePidMap {

    @Autowired
    @Qualifier("GenericRestClient")
    private GenericRestClient genericRestClient;

    private final Logger logger = LogManager.getLogger(ActiveInactivePidMap.class);

    private static final String ACTIVE_INACTIVE_KEY = "activeInactivePid";

    private Boolean apiErrorFlag = false;

    @Value("${ruleEngineActiveInactive.host}")
    private String host;

    @Value("${ruleEngine.endpoints.offloadcpuData}")
    private String path;

    public Boolean reloadFlag = false;

    private ArrayList<ActiveInactiveMap> activeInactiveMapList = new ArrayList<>();


    @Autowired
    public ActiveInactivePidMap(GenericRestClient genericRestClient) {
        this.genericRestClient = genericRestClient;
    }

    @PostConstruct
    public void onStartup() {
        storeActiveInactivePidMap();
    }

    @Scheduled(cron = "0 0/15 * * * *")
    public void storeActiveInactivePidMap() {
        logger.info("[ActiveInactivePidMap.storeActiveInactivePidMap]: storeActiveInactivePidMap is executing ");
        try {
            CvrActiveInactiveApiResponse response = genericRestClient.get(host, path, buildQueryParams(),
                    null, new ParameterizedTypeReference<CvrActiveInactiveApiResponse>() {
                    });

            ActiveInactivePidMapCache.getInstance().storePidMapByApi(response.getData());
            reloadFlag = true;
            apiErrorFlag = false;
            logger.trace("[ActiveInactivePidMap.storeActiveInactivePidMap]: pidMap stored from API hit, pidMap={}", ActiveInactivePidMapCache.getInstance().getPidMap());

        } catch (Exception e) {
            apiErrorFlag = true;
            logger.error("[ActiveInactivePidMap.storeActiveInactivePidMap]: Error occurred while calling the api with Exception:{}", e.getMessage());
        }


//        if (Boolean.TRUE.equals(apiErrorFlag)) {
//            storeActiveInactivePidMapByDbHit();
//        }
    }

//    private void storeActiveInactivePidMapByDbHit() {
//        logger.info("[ActiveInactivePidMap.storeActiveInactivePidMapByDbHit]: DB Hit for ActiveInactivePidMap");
//        if (Boolean.TRUE.equals(reloadFlag)) {
//            try {
//                activeInactiveMapList = activeInactivePidMapRepository.getActiveInactiveMapWithReloadFlag();
//                logger.trace("[ActiveInactivePidMap.storeActiveInactivePidMapByDbHit]: Fetched from active_inactive_pid_map table for last 15 mins, Fetched Data = {}", activeInactiveMapList);
//            } catch (Exception e) {
//                logger.error("[ActiveInactivePidMap.storeActiveInactivePidMapByDbHit]: Error occurred in DB call with reloadFlag with Exception: {}", e.getMessage());
//
//            }
//
//        } else {
//            try {
//                activeInactiveMapList = activeInactivePidMapRepository.getActiveInactiveMapWithoutReloadFlag();
//                reloadFlag = true;
//                logger.trace("[ActiveInactivePidMap.storeActiveInactivePidMapByDbHit]: Fetched from active_inactive_pid_map table, Fetched Data = {}", activeInactiveMapList);
//            } catch (Exception e) {
//                logger.error("[ActiveInactivePidMap.storeActiveInactivePidMapByDbHit]: Error occurred in DB call without reloadFlag with Exception: {}", e.getMessage());
//            }
//
//        }
//        ActiveInactivePidMapCache.getInstance().storePidMapByDbHit(activeInactiveMapList);
//
//        logger.trace("[ActiveInactivePidMap.storeActiveInactivePidMapByDbHit]: pidMap created By DB hit, pidMap={}", ActiveInactivePidMapCache.getInstance().getPidMap());
//
//    }

    private Map<String, Object> buildQueryParams() {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("key", ACTIVE_INACTIVE_KEY);

        return queryParams;
    }

    public Long getActivePid(Long pid) {
        if (pid == null)
            return null;
        Long activePID = ActiveInactivePidMapCache.getInstance().getActiveInactivePid(String.valueOf(pid));
        return activePID != null ? activePID : Long.valueOf(pid);
    }

}
