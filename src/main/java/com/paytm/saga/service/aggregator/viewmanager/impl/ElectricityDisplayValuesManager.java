package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.DisplayValuesManagerInterface;
import com.paytm.saga.util.DateUtil;

@Component("electricityDisplayValuesManager")
public class ElectricityDisplayValuesManager implements DisplayValuesManagerInterface {

	@Override
	public List<ViewElementInfo> getDisplayValues(HistoryView historyView) {
		List<ViewElementInfo> viewElementInfos = new ArrayList<ViewElementInfo>();
		if (historyView.getBillDate() != null && (!historyView.getEventType().equals(EventTypes.RECHARGE)
				|| (historyView.getEventType().equals(EventTypes.RECHARGE)
						&& !historyView.getStatus().equals("SUCCESS")))) {
			ViewElementInfo viewElementInfo = new ViewElementInfo();
			if (historyView.getEventType().equals(EventTypes.DROPOFF)) {
				viewElementInfo.setValue(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getBillDate(), DateFormats.DATE_FORMAT),
						DateFormats.EEE_DD_MMM));
			} else
				viewElementInfo.setValue(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getBillDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.EEE_DD_MMM));
			viewElementInfo.setKey("Bill Date");
			viewElementInfo.setType("ds1");
			viewElementInfos.add(viewElementInfo);
		}
		if (historyView.getDueDate() != null) {
			ViewElementInfo viewElementInfo = new ViewElementInfo();
			if (historyView.getEventType().equals(EventTypes.DROPOFF)) {
				viewElementInfo.setValue(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_FORMAT_2),
						DateFormats.EEE_DD_MMM));
			} else
				viewElementInfo.setValue(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.EEE_DD_MMM));
			viewElementInfo.setKey("Due Date");
			viewElementInfo.setType("ds2");
			viewElementInfos.add(viewElementInfo);
		}
		return viewElementInfos;
	}

}
