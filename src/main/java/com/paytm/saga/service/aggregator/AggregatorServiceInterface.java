package com.paytm.saga.service.aggregator;

import java.util.Date;

import com.paytm.saga.dto.GetHistoryPageDTO;
import com.paytm.saga.dto.HistoryPage;

public interface AggregatorServiceInterface {
	public HistoryPage aggregateHistoryInfo(final long customerId, final String rechargeNumber, final String service,
											String pagingState, String currentCardOperator, String previousCardOperator, Date previousDate,String client, String version);

	public HistoryPage aggregateHistoryInfo(GetHistoryPageDTO historyPageRequest, String previousCardOperator, Date previousDate);
}
