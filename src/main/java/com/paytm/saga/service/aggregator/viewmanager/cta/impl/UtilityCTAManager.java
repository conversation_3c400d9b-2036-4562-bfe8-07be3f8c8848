package com.paytm.saga.service.aggregator.viewmanager.cta.impl;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.CTAManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.paytm.saga.common.constant.UtilityThemeTypes.*;

@Service("utilityCTAManager")
public class UtilityCTAManager implements CTAManager {
    private static final Logger logger = LogManager.getLogger(UtilityCTAManager.class);

    @Override
    public List<ViewElementInfo> getCTAs(HistoryView historyView) {

        List<ViewElementInfo> response = null;
        String themeType = historyView.getThemeType();
        switch (themeType) {
            case SUGGESTED_RECHARGE:
                response = getSuggestedRechargeCTAs(historyView);
                break;
            case RECHARGE_SUCCESS:
            case RECHARGE_PENDING:
            case BILL_SUCCESS:
            case BILL_PENDING:
            case AUTOMATIC_PAYMENT_SUCCESS:
            case AUTOMATIC_PAYMENT_PENDING:
            case RECHARGE_FAILED:
            case RECHARGE_CANCELLED:
            case BILL_FAILED:
            case BILL_CANCELLED:
            case AUTOMATIC_PAYMENT_FAILED:
            case AUTOMATIC_PAYMENT_CANCELLED:
                response = getViewDetailsCTAs(historyView);
                break;
            case BILL_DUE:
            case BILL_OVERDUE:
            case BILL_DUE_TODAY_OR_TOMORROW:
                response = getReminderCTAs(historyView);
                break;
            case BILL_NOT_PAID_PAYTM:
                response = null ;
                break;
            case AUTOMATIC_PAYMENT:
            case AUTOMATIC_PAYMENT_UNDER_RETRY:
                response = getAutomaticBillCTAs(historyView);
                break;

            default:
                break;
        }

        logger.debug("cta for  card type  {} is {} ", themeType, response);

        return response;
    }

    private List<ViewElementInfo> getSuggestedRechargeCTAs(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo cta = new ViewElementInfo();
        cta.setValue("Pay");
        cta.setType("cta1");
        cta.setActionType("suggested_recharge");
        response.add(cta);
        return response;

    }

    private List<ViewElementInfo> getReminderCTAs(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo cta = new ViewElementInfo();
        cta.setValue("Pay Now");
        cta.setType("cta1");
        cta.setActionType("pay_now");
        response.add(cta);
        if(historyView.isNative()){
            ViewElementInfo cta2 = new ViewElementInfo();
            cta2.setValue("Mark as Paid");
            cta2.setType("cta2");
            cta2.setActionType("mark_as_paid");
            response.add(cta2);
        }
        return response;

    }

    private List<ViewElementInfo> getViewDetailsCTAs(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo cta = new ViewElementInfo();
        if(Boolean.TRUE.equals(historyView.isRetryExhausted()) && historyView.getThemeType().equalsIgnoreCase(AUTOMATIC_PAYMENT_FAILED)){
            cta.setValue("Pay Now");
            cta.setType("cta1");
            cta.setActionType("pay_now");
        }
        else {
            cta.setValue("View Details");
            cta.setType("cta1");
            cta.setActionType("view_details");
        }
        response.add(cta);
        return response;

    }

    private List<ViewElementInfo> getAutomaticBillCTAs(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo cta2 = new ViewElementInfo();
        cta2.setValue("Pay Now");
        cta2.setType("cta1");
        cta2.setActionType("pay_now");
        response.add(cta2);
        return response;

    }

}
