package com.paytm.saga.service.aggregator.cardmanager;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.service.ChannelHistoryRepositoryWrapperService;
import com.paytm.saga.service.RPSService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.service.DCATService;
import com.paytm.saga.service.PlanExpiryHistoryService;
import com.paytm.saga.service.aggregator.viewmanager.CTAHandler;
import com.paytm.saga.service.aggregator.viewmanager.DisplayValuesManager;
import com.paytm.saga.service.aggregator.viewmanager.FooterHandler;
import com.paytm.saga.service.aggregator.viewmanager.HeadersManager;
import com.paytm.saga.util.DateUtil;

@Component
public class PlanExpiry {

	private static final Logger logger = LogManager.getLogger(PlanExpiry.class);

	private final PlanExpiryHistoryService planExpiryHistoryService;
	final DCATService dcatService;
	final RPSService rpsService;
	private final ChannelHistoryRepositoryWrapperService channelHistoryRepository;
	private final RecentsRepository recentsRepository;

	@Autowired
	public PlanExpiry(final PlanExpiryHistoryService planExpiryHistoryService, final DCATService dcatService, final RPSService rpsService, ChannelHistoryRepositoryWrapperService channelHistoryRepository, RecentsRepository recentsRepository) {
		this.planExpiryHistoryService = planExpiryHistoryService;
		this.dcatService = dcatService;
		this.rpsService = rpsService;
		this.channelHistoryRepository = channelHistoryRepository;
		this.recentsRepository = recentsRepository;
	}

	public HistoryView getRechargeSuggestionCard(String rechargeNumber, String currentCardOperator, List<ChannelHistory> channelHistories) {
		String operatorDetail[] = currentCardOperator.split(":");
		if (operatorDetail.length != 3) {
			logger.error("getRechargeSuggestionCard :: not getting valid request parameter currentCardOperator"
					+ currentCardOperator);
			return null;
		} else {
			String operator = operatorDetail[0];
			String service = operatorDetail[1];
			String circle = operatorDetail[2];
			List<PlanExpiryHistory> planExpiryHistories = planExpiryHistoryService
					.getPlanExpiryByRecharge(rechargeNumber, operator, circle, service);
			PlanExpiryHistory planExpiryHistory = this.filterExpiringPlans(planExpiryHistories, channelHistories);
			if (planExpiryHistory != null) {
				return this.formatPlanExpiryToHistory(planExpiryHistory);
			} else {
				return null;
			}
		}

	}

	private Map<String, Date> planBucketToExcludeFromRechargeSuggestion(List<ChannelHistory> channelHistories) {
		Map<String, Date> filteredPlanBucket = new HashMap<String, Date>();
		for (ChannelHistory channelHistory : channelHistories) {
			if (channelHistory.getEventType().equals(EventTypes.MARKED_AS_PAID)) {
				Map<String, String> billsObj = channelHistory.getBillsObj();
				if(billsObj.get("plan_bucket") != null) {
					filteredPlanBucket.put(billsObj.get("plan_bucket"),channelHistory.getTransactionUpdateTime());
				}
			}
		}
		return filteredPlanBucket;
	}

	private Map<String, String> getPlanDetails(DCATGetPlanResponse dcatGetPlanResponse) {
		Map<String, String> display_values = new HashMap<String, String>();

		if (dcatGetPlanResponse.getData() != null)
			display_values.put("data", dcatGetPlanResponse.getData());
		if (dcatGetPlanResponse.getValidity() != null)
			display_values.put("validity", dcatGetPlanResponse.getValidity());
		if (dcatGetPlanResponse.getTalktime() != null)
			display_values.put("talktime", dcatGetPlanResponse.getTalktime());
		if (dcatGetPlanResponse.getSms() != null)
			display_values.put("sms", dcatGetPlanResponse.getSms());
		if (dcatGetPlanResponse.getAddon_benefit1() != null)
			display_values.put("addon_benefit1", dcatGetPlanResponse.getAddon_benefit1());
		if (dcatGetPlanResponse.getAddon_benefit2() != null)
			display_values.put("addon_benefit2", dcatGetPlanResponse.getAddon_benefit2());
		if (dcatGetPlanResponse.getDescription() != null)
			display_values.put("description", dcatGetPlanResponse.getDescription());
		return display_values;
	}

	private HistoryView formatPlanExpiryToHistory(PlanExpiryHistory planExpiryHistory) {
		HistoryView historyView = new HistoryView();
		historyView.setAmount(planExpiryHistory.getAmount());
		historyView.setCircle(planExpiryHistory.getCircle());
		Date eventDate = new Date();
		historyView.setCreatedDate(DateUtil.dateFormatter(eventDate, DateFormats.DATE_TIME_FORMAT));
		historyView.setOperator(planExpiryHistory.getOperator());
		historyView.setPayType("prepaid");
		historyView.setPlanBucket(planExpiryHistory.getPlan_bucket());
		historyView.setThemeType(CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME);
		historyView.setEventType(CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME);
		historyView.setStatus("active");
		historyView.setEventDate(eventDate);
		historyView.setPlanExpiryDays(calculatePlanExpiringDays(planExpiryHistory.getValidity_expiry_date()));
		DCATGetPlanResponse dcatGetPlanResponse = this.getPlanDetailsFromDcat(planExpiryHistory.getService(),
				planExpiryHistory.getOperator(), planExpiryHistory.getAmount(), planExpiryHistory.getCircle(),
				planExpiryHistory.getRechargeNumber());;
		if (dcatGetPlanResponse != null) {
			// add display values with currentPlanDetail
			logger.trace("rps plan bucket " + dcatGetPlanResponse.getPlan_bucket() + "plan validity plan bucket "
					+ planExpiryHistory.getPlan_bucket());
			historyView.setPlanDetail(this.getPlanDetails(dcatGetPlanResponse));
			historyView.setPlanBucket(dcatGetPlanResponse.getPlan_bucket());
			historyView.setPlanExpiryDate(
					DateUtil.dateFormatter(planExpiryHistory.getValidity_expiry_date(), DateFormats.DATE_FORMAT_2));
		} else {
			// add display values with oldPlanDetail
			// and mark the card as plan invalid message
			historyView.setInvalidPlan(true);
			historyView.setPlanDetail(planExpiryHistory.getDisplay_values());
			historyView.setFooter(FooterHandler.getFooters(historyView));
		}
		historyView.setCta(CTAHandler.getCTAs(historyView));
		historyView.setDisplayValues(DisplayValuesManager.getDisplayValues(historyView));
		historyView.setHeadings(HeadersManager.getHeaders(historyView));
		return historyView;
	}

	private int calculatePlanExpiringDays(Date planExpiryDate) {
		long diffInMillies = Math.abs(planExpiryDate.getTime()
				- (DateUtil.stringToDate(DateUtil.dateFormatter(new Date(), DateFormats.DATE_FORMAT),
						DateFormats.DATE_FORMAT)).getTime());// ;
		int diff = (int) TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
		if (planExpiryDate.before(new Date())) {
			return diff == 0 ? -1 : -(diff);
		} else {
			return diff;
		}
	}

	private PlanExpiryHistory filterExpiringPlans(List<PlanExpiryHistory> planExpiryHistories, List<ChannelHistory> channelHistories) {
		PlanExpiryHistory res = null;
		double amount = 0;
		Date expiryDate = null;
		Date fromDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.PLAN_EXPIRY_DAYS));
		Date toDate = DateUtil.dateIncrDecr(new Date(), CommonConsts.PLAN_EXPIRY_DAYS);
		Map<String, Date> planBucketFilter= this.planBucketToExcludeFromRechargeSuggestion(channelHistories);

		Map<String, Double> map = new HashMap<String, Double>();
		for (PlanExpiryHistory planExpiryHistory : planExpiryHistories) {
			String key = planExpiryHistory.getOperator() + ":" + planExpiryHistory.getService() + ":"
					+ planExpiryHistory.getCircle() + ":" + planExpiryHistory.getPlan_bucket();
			if (!map.containsKey(key)) {
				map.put(key, planExpiryHistory.getAmount());
				if (planExpiryHistory.getValidity_expiry_date() != null
						&& planExpiryHistory.getValidity_expiry_date().after(fromDate)
						&& planExpiryHistory.getValidity_expiry_date().before(toDate)
						&& planExpiryHistory.getPlan_bucket() != null && planExpiryHistory.getDisplay_values() != null
						&& !planExpiryHistory.getDisplay_values().isEmpty()
						) {
					//map.put(key, planExpiryHistory.getAmount());
					try {
						if(planBucketFilter.containsKey(planExpiryHistory.getPlan_bucket())) {
							if(planBucketFilter.get(planExpiryHistory.getPlan_bucket()).after(fromDate) && 
							   planBucketFilter.get(planExpiryHistory.getPlan_bucket()).before(toDate)) {
								continue;
							}
						}
						Date newExpiryDate = DateUtil.timestampToDate(planExpiryHistory.getValidity_expiry_date());
						if (expiryDate != null) {
							if (newExpiryDate.before(expiryDate)) {
								res = planExpiryHistory;
							} else if (newExpiryDate.equals(expiryDate)) {
								if (planExpiryHistory.getAmount() >= amount)
									res = planExpiryHistory;
							}
						} else {
							res = planExpiryHistory;
						}
						expiryDate = DateUtil.timestampToDate(planExpiryHistory.getValidity_expiry_date());
						amount = planExpiryHistory.getAmount();

					} catch (ParseException e) {
						logger.error("filterExpiringPlans Date parsing exception");
					}
				}
			}
			
		}
		logger.trace("plan validity info " + (res != null ? res.toString() : res));
		return res;
	}

	private DCATGetPlanResponse getPlanDetailsFromDcat(String service, String operator, double amount, String circle,
			String rechargeNumber) {
		DCATGetPlanResponse dcatGetPlanResponse = rpsService.getPlanDetails(
				Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service), Constants.CommonConstants.DCAT_VERSION,
				Constants.CommonConstants.DCAT_CHANNEL, operator, "" + ((int) amount), circle,null,rechargeNumber, service);
		if (dcatGetPlanResponse != null) {
			logger.trace("[PlanValidityListeners.insertDataIntoCassandra] getPlanResponse from RPS success  , :"
					+ rechargeNumber + (dcatGetPlanResponse.toString()));

			return dcatGetPlanResponse;
		} else {
			logger.error(
					"[PlanValidityListeners.insertDataIntoCassandra] rpsGetPlanResponse null  , :" + rechargeNumber);
		}
		return null;
	}
}
