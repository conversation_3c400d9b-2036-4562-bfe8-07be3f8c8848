package com.paytm.saga.service.aggregator.viewmanager;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;

public class FooterHandler {

	private static final Logger logger = LogManager.getLogger(FooterHandler.class);

	public static List<ViewElementInfo> getFooters(HistoryView historyView) {
		List<ViewElementInfo> response = null;
		String eventType = historyView.getThemeType();
		logger.debug("adding Footer for cardType " + eventType);
		switch (eventType) {
		case CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME:
			response = getSuggestedRechargeFooters(historyView.isInvalidPlan(), historyView.getPlanExpiryDays());
			break;
		default:
			break;
		}
		return response;
	}

	private static List<ViewElementInfo> getSuggestedRechargeFooters(boolean invalidPlan, int planExpiresIn) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setType("footer1");
		if (planExpiresIn > 0) {
			if (planExpiresIn == 1) {
				header1.setValue("Plan expires tomorrow");
			} else {
				header1.setValue(MessageFormat.format("Plan expires in {0} days", planExpiresIn));
			}

		} else if (planExpiresIn == 0) {
			header1.setValue("Plan is going to expire today");
		} else {
			header1.setValue("Plan expired");
		}
		response.add(header1);
		if (invalidPlan) {
			ViewElementInfo header2 = new ViewElementInfo();
			header2.setValue("Please browse through other plans below to make a new recharge");
			header2.setType("footer2");
			response.add(header2);
		}
		return response;
	}
}
