package com.paytm.saga.service.aggregator.cardmanager;

import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.VersionComparison;

@Component("dropOffCard")
public class DropOffCard implements CardManager {
	private static final Logger logger = LogManager.getLogger(CardManager.class);

	private ViewManager ctaManager;
	private ViewManager headersViewManager;
	private ViewManager displayValuesManager;

	@Autowired
	public DropOffCard(final @NotNull @Qualifier("ctaViewManager") ViewManager ctaManager,
			final @NotNull @Qualifier("displayValuesViewManager") ViewManager displayValuesManager,
			final @NotNull @Qualifier("headersViewManager") ViewManager headersViewManager) {
		this.ctaManager = ctaManager;
		this.displayValuesManager = displayValuesManager;
		this.headersViewManager = headersViewManager;
	}

	@Override
	public HistoryView getCardInfo(CardInfoDto cardInfoDto) {
		String version = cardInfoDto.getVersion();
		String client = cardInfoDto.getClient();
		boolean isDropOffAllowed=VersionComparison.isDropOffWhitetlisted(client, version);
		if(!isDropOffAllowed) {
			return null;
		}
		ModelMapper modelMapper = new ModelMapper();
		HistoryView historyView = modelMapper.map(cardInfoDto.getDropOff(), HistoryView.class);
		historyView.setEventDate(new Date());
		historyView.setCreatedDate(DateUtil.dateFormatter(historyView.getEventDate(), DateFormats.DATE_TIME_FORMAT));
		historyView.setService(cardInfoDto.getDropOff().getService());
		historyView.setCircle(cardInfoDto.getDropOff().getCircle());
		historyView.setPayType(cardInfoDto.getDropOff().getPaytype());
		historyView.setPlanDetail(cardInfoDto.getDropOff().getDisplayValues());
		historyView.setEventType(cardInfoDto.getDropOff().getEventType());
		historyView.setThemeType(this.getRechargeThemeType(cardInfoDto.getDropOff()));
		String expiry = null;
		if(cardInfoDto.getDropOff().getBillsObj() !=null)
			expiry = cardInfoDto.getDropOff().getBillsObj().get("expiry");
		if (expiry != null) {
			try {
				Date fromDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.PLAN_EXPIRY_DAYS));
				Date toDate = DateUtil.dateIncrDecr(new Date(), CommonConsts.PLAN_EXPIRY_DAYS);
				Date expiryDate = DateUtil.stringToDate(expiry, DateFormats.DATE_TIME_FORMAT_2);
				if (expiryDate.after(fromDate) && expiryDate.before(toDate)) {
					historyView.setPlanExpiryDays(calculatePlanExpiringDays(expiryDate));
					historyView.setPlanExpiring(true);
				}
				historyView.setPlanExpiryDate(expiry);				
			}catch(RuntimeException e) {
				logger.error("Getting exception in calculating plan expiry Date in dropoff",e.getMessage());
			}

		}
		historyView.setBillDate(this.getBillDate(cardInfoDto.getDropOff()));
		historyView.setDueDate(this.getDueDate(cardInfoDto.getDropOff()));
		historyView.setHeadings(headersViewManager.getViewElements(historyView));
		historyView.setCta(ctaManager.getViewElements(historyView));
		if (!historyView.getService().equals(Constants.ServiceTypeConstants.ELECTRICITY))
			historyView.setDisplayValues(displayValuesManager.getViewElements(historyView));

		try {
			if (historyView.getDueDate() != null) {
				historyView.setDueDate(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.DATE_FORMAT_2));
			}

			if (historyView.getBillDate() != null) {
				historyView.setBillDate(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getBillDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.DATE_FORMAT_2));
			}
		} catch (RuntimeException e) {
			logger.error("Getting exception in parsing billdate duedate in recharge card");
		}

		return historyView;
	}

	private int calculatePlanExpiringDays(Date planExpiryDate) {
		long diffInMillies = Math.abs(planExpiryDate.getTime()
				- (DateUtil.stringToDate(DateUtil.dateFormatter(new Date(), DateFormats.DATE_FORMAT),
						DateFormats.DATE_FORMAT)).getTime());// ;
		int diff = (int) TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
		if (planExpiryDate.before(new Date())) {
			return diff == 0 ? -1 : -(diff);
		} else {
			return diff;
		}
	}

	private String getBillDate(DropOff dropOff) {
		Map<String, String> billsObj = dropOff.getBillsObj();
		if (billsObj != null) {
			return billsObj.get(Constants.DropOffConstants.BILL_DATE);
		}

		return null;
	}

	private String getDueDate(DropOff dropOff) {
		Map<String, String> billsObj = dropOff.getBillsObj();
		if (billsObj != null) {
			return billsObj.get(Constants.DropOffConstants.DUE_DATE);
		}

		return null;
	}

	private String getRechargeThemeType(DropOff dropOff) {
		String cardInfo = (dropOff.getPaytype() + "_" + dropOff.getService() + "_" + dropOff.getEventType())
				.toLowerCase();
		logger.trace("order id" + dropOff.getRechargeNumber() + " event type" + dropOff.getEventType() + "status"
				+ dropOff.getStatus() + "service" + dropOff.getService() + "paytype" + dropOff.getPaytype());
		String response = "";
		switch (cardInfo) {
		case "recharge_cylinder booking_validation":
			response = CardThemeTypes.DROPOFF_CARD_THEME;
			break;
		case "postpaid_electricity_validation":
			response = CardThemeTypes.DROPOFF_CARD_THEME;
			break;
		case "prepaid_mobile_validation":
			response = CardThemeTypes.DROPOFF_CARD_THEME;
			break;
		}
		logger.trace("theme type for drop off card with recharge number" + dropOff.getDummyRecharge() + ": " + response);
		return response;
	}
}
