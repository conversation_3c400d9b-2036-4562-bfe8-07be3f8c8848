package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.util.DateUtil;

@Component("cylinderHeaderManager")
public class CylinderHeaderManager extends CommonHeaderManager {
	private static final Logger logger = LogManager.getLogger(CylinderHeaderManager.class);

	@Override
	public List<ViewElementInfo> getHeaders(HistoryView historyView) {
		logger.debug("setting headers for card type " + historyView.getThemeType());
		List<ViewElementInfo> response = null;
		String themeType = historyView.getThemeType();
		switch (themeType) {
		case CardThemeTypes.DATE_CHANGE_CARD_THEME:
			response = this.getDateChangeHeaders(historyView);
			break;
		case CardThemeTypes.SUCCESS_CYLINDER_BOOKING_CARD_THEME:
			response = this.getCylindeRechargeSuccessHeaders(historyView);
			break;
		case CardThemeTypes.FAILURE_CYLINDER_BOOKING_CARD_THEME:
			response = this.getCylindeRechargeFailureHeaders(historyView);
			break;
		case CardThemeTypes.REVERSAL_FAILURE_CYLINDER_BOOKING_CARD_THEME:
			response = this.getCylindeRechargeReversalFailureHeaders(historyView);
			break;
		case CardThemeTypes.PAYMENT_FAILURE_CYLINDER_BOOKING_CARD_THEME:
			response = this.getCylindeRechargePaymentFailureHeaders(historyView);
			break;
		case CardThemeTypes.PENDING_CYLINDER_BOOKING_CARD_THEME:
			response = this.getCylindeRechargePendingHeaders(historyView);
			break;
		case CardThemeTypes.PAYMENT_PENDING_CYLINDER_BOOKING_CARD_THEME:
			response = this.getCylindeRechargePaymentPendingHeaders(historyView);
			break;
		case CardThemeTypes.CANCELLED_CYLINDER_BOOKING_CARD_THEME:
			response = this.getCylindeRechargeUserCancelledHeaders(historyView);
			break;
		case CardThemeTypes.DROPOFF_CARD_THEME:
			response = this.getDropOffHeaders(historyView);
			break;
		default:
			break;
		}
		return response;
	}

	private List<ViewElementInfo> getCylindeRechargePendingHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		if (historyView.isIvrsBooking()) {
			ViewElementInfo header1 = new ViewElementInfo();
			header1.setValue("Payment Pending");
			header1.setType("header1");
			response.add(header1);
			ViewElementInfo header2 = new ViewElementInfo();
			header2.setValue("₹" + formatNumber(historyView.getAmount()));
			header2.setType("header2");
			response.add(header2);
			ViewElementInfo header3 = new ViewElementInfo();
			if (historyView.getBookingId() == null) {
				header3.setValue("Payment is pending. We will notify you once it is successful.");
			} else {
				header3.setValue("Payment for (Booking ID: " + historyView.getBookingId()
						+ ") is pending. We will notify you once it is successful.");
			}
			header3.setType("header3");
			response.add(header3);
		} else {
			ViewElementInfo header1 = new ViewElementInfo();
			header1.setValue("Cylinder Booking Pending");
			header1.setType("header1");
			response.add(header1);
			ViewElementInfo header2 = new ViewElementInfo();
			header2.setValue("₹" + formatNumber(historyView.getAmount()));
			header2.setType("header2");
			response.add(header2);
			ViewElementInfo header3 = new ViewElementInfo();
			header3.setValue(historyView.getOperatorLabel()
					+ " is taking time to confirm the order. We will continuously try to post your transaction with "
					+ historyView.getOperatorLabel() + " till midnight and notify you once we receive confirmation.");
			header3.setType("header3");
			response.add(header3);
		}

		return response;
	}

	private List<ViewElementInfo> getCylindeRechargeFailureHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		if (!historyView.isIvrsBooking()) {
			ViewElementInfo header1 = new ViewElementInfo();
			header1.setValue("Booking Failed");
			header1.setType("header1");
			response.add(header1);
			ViewElementInfo header2 = new ViewElementInfo();
			header2.setValue("₹" + formatNumber(historyView.getAmount()));
			header2.setType("header2");
			response.add(header2);
			ViewElementInfo header3 = new ViewElementInfo();
			header3.setValue("If any amount was deducted, it will be refunded in 3 working days");
			header3.setType("header3");
			response.add(header3);
		} else {
			ViewElementInfo header1 = new ViewElementInfo();
			header1.setValue("Payment Failed");
			header1.setType("header1");
			response.add(header1);
			ViewElementInfo header2 = new ViewElementInfo();
			header2.setValue("₹" + formatNumber(historyView.getAmount()));
			header2.setType("header2");
			response.add(header2);
			ViewElementInfo header3 = new ViewElementInfo();
			if (historyView.getBookingId() == null) {
				header3.setValue("Payment failed. This may be due to wrong OTP or incorrect password.");
			} else {
				header3.setValue("Payment for (Booking ID: " + historyView.getBookingId()
						+ ") failed. This may be due to wrong OTP or incorrect password.");
			}

			header3.setType("header3");
			response.add(header3);
		}
		return response;
	}

	private List<ViewElementInfo> getCylindeRechargePaymentPendingHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Payment Pending");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		ViewElementInfo header3 = new ViewElementInfo();
		header3.setValue("Your Payment is pending. We will notify you once it is successful.");
		header3.setType("header3");
		response.add(header3);

		return response;
	}

	private List<ViewElementInfo> getCylindeRechargeUserCancelledHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Cylinder Booking Cancelled");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		ViewElementInfo header3 = new ViewElementInfo();
		header3.setValue(
				"Your order has been cancelled. If any amount was deducted, it will be refunded in 3 working days");
		header3.setType("header3");
		response.add(header3);
		return response;
	}

	private List<ViewElementInfo> getCylindeRechargePaymentFailureHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		if (!historyView.isIvrsBooking()) {
			ViewElementInfo header1 = new ViewElementInfo();
			header1.setValue("Payment Failed");
			header1.setType("header1");
			response.add(header1);
			ViewElementInfo header2 = new ViewElementInfo();
			header2.setValue("₹" + formatNumber(historyView.getAmount()));
			header2.setType("header2");
			response.add(header2);
			ViewElementInfo header3 = new ViewElementInfo();
			header3.setValue("This may be due to wrong OTP or incorrect password.");
			header3.setType("header3");
			response.add(header3);
		} else {
			// this is not the possible case- because in payment failwe don't have any info
			// regarding ivrs or non-ivrs
			ViewElementInfo header1 = new ViewElementInfo();
			header1.setValue("Payment Failed");
			header1.setType("header1");
			response.add(header1);
			ViewElementInfo header2 = new ViewElementInfo();
			header2.setValue("₹" + formatNumber(historyView.getAmount()));
			header2.setType("header2");
			response.add(header2);
			ViewElementInfo header3 = new ViewElementInfo();
			if (historyView.getBookingId() == null) {
				header3.setValue("Payment failed. This may be due to wrong OTP or incorrect password.");
			} else {
				header3.setValue("Payment for (Booking ID: " + historyView.getBookingId()
						+ ") failed. This may be due to wrong OTP or incorrect password.");
			}

			header3.setType("header3");
			response.add(header3);
		}
		return response;
	}

	private List<ViewElementInfo> getCylindeRechargeReversalFailureHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Cylinder Booking Cancelled");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);

		ViewElementInfo header3 = new ViewElementInfo();
		header3.setValue(
				"Your order has been cancelled. If any amount was deducted, it will be refunded in 3 working days");
		header3.setType("header3");
		response.add(header3);

		return response;
	}

	private List<ViewElementInfo> getCylindeRechargeSuccessHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		if (historyView.isIvrsBooking()) {
			header1.setValue("Payment Successful");// need to update this message
		} else {
			header1.setValue("Gas Cylinder Booked");
		}

		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);

		ViewElementInfo header3 = new ViewElementInfo();
		try {
			if (historyView.getEventDate().after(DateUtil.dateIncrDecr(DateUtil.timestampToDate(new Date()), -3))) {
				if (historyView.isIvrsBooking()) {
					if (historyView.getBookingId() == null) {
						header3.setValue("Payment was successful on "
								+ DateUtil.dateFormatter(historyView.getEventDate(), DateFormats.DATE_FORMAT_3));
					} else {
						header3.setValue("Payment for Booking ID - " + historyView.getBookingId()
								+ " was successful on "
								+ DateUtil.dateFormatter(historyView.getEventDate(), DateFormats.DATE_FORMAT_3));
					}

				} else {
					header3.setValue("Booking has been confirmed and the cylinder will be delivered in 2-3 days");
				}
			} else {
				if (historyView.isIvrsBooking()) {
					if (historyView.getBookingId() == null) {
						header3.setValue("Payment was successful on "
								+ DateUtil.dateFormatter(historyView.getEventDate(), DateFormats.DATE_FORMAT_3));
					} else {
						header3.setValue("Payment for Booking ID - " + historyView.getBookingId()
								+ " was successful on "
								+ DateUtil.dateFormatter(historyView.getEventDate(), DateFormats.DATE_FORMAT_3));
					}

				} else {
					header3.setValue("Your cylinder was last booked on "
							+ DateUtil.dateFormatter(historyView.getEventDate(), DateFormats.DATE_FORMAT_3));
				}
			}
		} catch (ParseException e) {
			logger.error("getting exception in creating header3 in cylinder",e.getMessage(),e);
			header3.setValue("Payment for was successful on "
					+ DateUtil.dateFormatter(historyView.getEventDate(), DateFormats.DATE_FORMAT_3));
		}
		header3.setType("header3");
		response.add(header3);
		return response;
	}

	protected List<ViewElementInfo> getDropOffHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Complete your Booking");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		ViewElementInfo header3 = new ViewElementInfo();
		header3.setValue("You did not complete your last payment");
		header3.setType("header3");
		response.add(header3);
		return response;
	}

}
