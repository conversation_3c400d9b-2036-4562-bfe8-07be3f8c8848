package com.paytm.saga.service.aggregator.cardmanager;

import java.util.ArrayList;
import java.util.List;

import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.util.ChannelHistoryUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.ChannelHistoryPage;
import com.paytm.saga.dto.ResponsePage;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.service.ChannelHistoryFinalizedService;

@Component
public class ChannelHistoryFinalizedUtil {

	private static final Logger logger = LogManager.getLogger(ChannelHistoryFinalizedUtil.class);

	final private ChannelHistoryFinalizedService channelHistoryFinalizedService;

	@Value("#{'${utility.history.services}'.split(',')}")
	private List<String> services;
	private MetricsHelper metricsHelper;
	@Autowired
	public ChannelHistoryFinalizedUtil(final ChannelHistoryFinalizedService channelHistoryFinalizedService,final MetricsHelper metricsHelper) {
		this.channelHistoryFinalizedService = channelHistoryFinalizedService;
		this.metricsHelper = metricsHelper;
	}

	public ChannelHistoryPage convertFinalizedToHistory(final long customerId, final String recharge_number,
			final String service, int count, String pagingState) {
		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		int nextCount = 4;
		if (count >= 4) {
			nextCount = 1;
		} else {
			nextCount = 4 - count;
		}
		ResponsePage<ChannelHistoryFinalized> responsePage = channelHistoryFinalizedService.getPageOfHistory(customerId,
				recharge_number, service, nextCount, pagingState);
		channelHistoryPage.setAvailableNext(responsePage.getHasNext());
		channelHistoryPage.setChannelHistories(this.formatFinalizeHistoryViewToDTOWithLatestState(responsePage.getContent()));
		channelHistoryPage.setLastCardId(responsePage.getPagingState());
		return channelHistoryPage;
	}

	private List<ChannelHistory> formatFinalizeHistoryViewToDTOWithLatestState(List<ChannelHistoryFinalized> channelHistories) {
		List<ChannelHistory> historyViews = new ArrayList<>();
		Long previousRechargeOrderId = null;
		for (ChannelHistoryFinalized channelHistory : channelHistories) {

			channelHistory.setFinalisedState(true);
			ChannelHistory historyView = this.convertFinalizedHistoryToHistory(channelHistory);
			if (historyView.getEventType().equals(EventTypes.RECHARGE)) {
				Long orderId = historyView.getOrderId();
				if (ChannelHistoryUtils.checkForLatestStateOrders(historyView, previousRechargeOrderId)) {
					historyViews.add(historyView);
				} else {
					metricsHelper.pushToDD(Constants.MetricConstants.DUPLICATE_FINALIZED_EVENTS_METRIC, Constants.MetricConstants.DUPLICATE_FINALIZED_EVENTS_SERVICE);
					logger.debug("[ChannelHistoryFinalizedUtil]::[formatFinalizeHistoryViewToDTOWithLatestState]:Duplicate events found in channel history finalized, channelhistory record={}", historyView);
				}
				previousRechargeOrderId = orderId;
			} else {
				historyViews.add(historyView);
			}

		}
		return historyViews;
	}

	private ChannelHistory convertFinalizedHistoryToHistory(ChannelHistoryFinalized channelHistoryFinalized) {
		ModelMapper modelMapper = new ModelMapper();
//		modelMapper.typeMap(ChannelHistoryFinalized.class, ChannelHistory.class).addMappings(mapper -> {
//			mapper.map(src -> src.getAmount(), ChannelHistory::setAmount);
//		});
		ChannelHistory channelHistory = modelMapper.map(channelHistoryFinalized, ChannelHistory.class);
		return channelHistory;
	}

	public List<ChannelHistoryFinalized> convertHistoryToFinalizedHistory(List<ChannelHistory> channelHistories) {
		List<ChannelHistoryFinalized> channelHistoryFinalized = new ArrayList<ChannelHistoryFinalized>();
		for (ChannelHistory channelHistory : channelHistories) {
			channelHistoryFinalized.add(convertHistoryToFinalizedHistory(channelHistory));
//			channelHistory.setFinalisedState(true);
//			channelHistoryService.save(channelHistory);
		}
		return channelHistoryFinalized;
	}

	private ChannelHistoryFinalized convertHistoryToFinalizedHistory(ChannelHistory channelHistory) {
		ModelMapper modelMapper = new ModelMapper();
		ChannelHistoryFinalized channelHistoryFinalized = modelMapper.map(channelHistory,
				ChannelHistoryFinalized.class);
		channelHistoryFinalized.setFinalisedState(true);
		return channelHistoryFinalized;
	}
}
