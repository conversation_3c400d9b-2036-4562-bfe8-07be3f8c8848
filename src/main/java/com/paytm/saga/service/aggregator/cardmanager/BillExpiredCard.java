package com.paytm.saga.service.aggregator.cardmanager;

import java.util.Map;

import jakarta.validation.constraints.NotNull;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.DateUtil;

@Component("billExpiredCard")
public class BillExpiredCard implements CardManager {
	private static final Logger logger = LogManager.getLogger(BillExpiredCard.class);
	private final ViewManager ctaManager;
	private final ViewManager displayValuesManager;
	private final ViewManager headersViewManager;

	@Autowired
	public BillExpiredCard(final @NotNull @Qualifier("ctaViewManager") ViewManager ctaManager,
			final @NotNull @Qualifier("displayValuesViewManager") ViewManager displayValuesManager,
			final @NotNull @Qualifier("headersViewManager") ViewManager headersViewManager) {
		this.ctaManager = ctaManager;
		this.displayValuesManager = displayValuesManager;
		this.headersViewManager = headersViewManager;
	}

	@Override
	public HistoryView getCardInfo(CardInfoDto cardInfoDto) {
		HistoryView historyView = new HistoryView();
		historyView.setNative(cardInfoDto.isNative());
		historyView.setService(cardInfoDto.getChannelHistory().getService());
		historyView.setOperator(cardInfoDto.getChannelHistory().getOperator());
		historyView.setAmount(Double.parseDouble(cardInfoDto.getChannelHistory().getAmount()));
		historyView.setBillDate(this.getBillDate(cardInfoDto.getChannelHistory()));
		historyView.setDueDate(this.getDueDate(cardInfoDto.getChannelHistory()));
		historyView.setEventType(EventTypes.BILL);
		historyView.setEventDate(cardInfoDto.getChannelHistory().getTransactionTime());
		historyView.setPreviousTxnHistory(cardInfoDto.isPreviousTxnHistory());
		historyView.setCreatedDate(DateUtil.dateFormatter(cardInfoDto.getChannelHistory().getTransactionTime(),
				DateFormats.DATE_TIME_FORMAT));
		historyView.setThemeType(CardThemeTypes.BILL_EXPIRED_CARD_THEME);
		historyView.setHeadings(headersViewManager.getViewElements(historyView));
		historyView.setDisplayValues(displayValuesManager.getViewElements(historyView));
		historyView.setCta(ctaManager.getViewElements(historyView));
		
		try {
			if (historyView.getDueDate() != null) {
				historyView.setDueDate(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.DATE_FORMAT_2));
			}
			
			if (historyView.getBillDate() != null) {
				historyView.setBillDate(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getBillDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.DATE_FORMAT_2));
			}			
		}catch(RuntimeException e) {
			logger.error("Getting exception in parsing billdate duedate in recharge card");
		}
		return historyView;
	}

	private String getBillDate(ChannelHistory channelHistory) {
		Map<String, String> billsObj = channelHistory.getBillsObj();
		if (billsObj != null) {
			return billsObj.get(Constants.ReminderConstants.BILL_DATE);
		}

		return null;
	}

	private String getDueDate(ChannelHistory channelHistory) {
		Map<String, String> billsObj = channelHistory.getBillsObj();
		if (billsObj != null) {
			return billsObj.get(Constants.ReminderConstants.DUE_DATE);
		}

		return null;
	}

}
