package com.paytm.saga.service.aggregator.planmanager;

import com.paytm.saga.dto.plans.PlansMapCache;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import jakarta.annotation.PostConstruct;
import java.io.*;
import java.util.ArrayList;
import java.util.List;

@Service
public class PlansLoader {

    @Autowired private ResourceLoader resourceLoader;
    private final Logger logger = LogManager.getLogger(PlansLoader.class);

    @PostConstruct
    public void onStartup() {
        loadPlansMap();
    }

    public void loadPlansMap() {
        try{
            Resource resource = resourceLoader.getResource("classpath:planbucket.txt");
            InputStream inputStream = resource.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            String line = null;
            while ((line = reader.readLine()) != null) {
                try {
                    String [] values = line.split(",");
                    String price=values[0];
                    String operator=values[1];
                    String circle=values[2];
                    String service=values[3];
                    String plan=values[4];
                    String key=new StringBuilder()
                            .append(service)
                            .append("_")
                            .append(operator)
                            .append("_")
                            .append(circle)
                            .append("_")
                            .append(price).toString().toLowerCase();
                    logger.info("PlansLoader :: loadPlansMap adding key {} plan {} to map",key,plan);
                    PlansMapCache.getInstance().addPlansDetail(key,plan);
                }catch (Exception e){
                    logger.error("PlansLoader :: loadPlansMap error while adding data {}",line);
                }
            }
        }catch (Exception e){
            logger.error("PlansLoader :: loadPlansMap error while processing file",e);
        }
    }
}
