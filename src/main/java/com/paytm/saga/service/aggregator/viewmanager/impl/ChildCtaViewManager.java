package com.paytm.saga.service.aggregator.viewmanager.impl;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.CTAManager;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ChildCTAManager;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("childCtaViewManager")
public class ChildCtaViewManager implements ViewManager {
    @Autowired
    @Qualifier("utilityChildCTAManager")
    private ChildCTAManager utilityChildCTAManager;

    @Override
    public List<ViewElementInfo> getViewElements(HistoryView historyView) {
        if (!historyView.isNative()
                && historyView.isUtility()) {
            return utilityChildCTAManager.getChildCTAs(historyView);
        }
        return null;
    }
}
