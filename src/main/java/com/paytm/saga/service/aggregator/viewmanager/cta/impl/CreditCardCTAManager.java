package com.paytm.saga.service.aggregator.viewmanager.cta.impl;

import static com.paytm.saga.common.constant.UtilityThemeTypes.*;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.CTAManager;

@Service("creditCardCTAManager")
public class CreditCardCTAManager  implements CTAManager {
    private static final Logger logger = LogManager.getLogger(UtilityCTAManager.class);

    @Override
    public List<ViewElementInfo> getCTAs(HistoryView historyView) {

        List<ViewElementInfo> response = null;
        String themeType = historyView.getThemeType();
        switch (themeType) {
            case SUGGESTED_RECHARGE:
                response = getSuggestedRechargeCTAs(historyView);
                break;
            case RECHARGE_SUCCESS:
            case RECHARGE_PENDING:
            case BILL_SUCCESS:
            case BILL_PENDING:
            case RECHARGE_FAILED:
            case RECHARGE_CANCELLED:
            case BILL_FAILED:
            case BILL_CANCELLED:
            case AUTOMATIC_PAYMENT_SUCCESS:
            case AUTOMATIC_PAYMENT_PENDING:
            case AUTOMATIC_PAYMENT_FAILED:
                response = getViewDetailsCTAs(historyView);
                break;
            case BILL_DUE:
            case CREDIT_CARD_BILL_OVERDUE:
            case CREDIT_CARD_BILL_DUE:
            case CREDIT_CARD_BILL_DUE_TODAY_OR_TOMORROW:
            case AUTOMATIC_PAYMENT:
                response = getReminderCTAs(historyView);
                break;
            case BILL_NOT_PAID_PAYTM:
                response = null ;
                break;


            default:
                break;
        }

        logger.debug("cta for  card type  {} is {} ", themeType, response);

        return response;
    }

    private List<ViewElementInfo> getSuggestedRechargeCTAs(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo cta1 = new ViewElementInfo();
        cta1.setValue("Pay Bill");
        cta1.setType("cta1");
        cta1.setActionType("suggested_recharge");
        ViewElementInfo cta2 = new ViewElementInfo();
        cta2.setValue("Get Latest Bill");
        cta2.setType("cta1");
        cta2.setActionType("latest_bill");
        response.add(cta1);
        response.add(cta2);
        return response;

    }

    private List<ViewElementInfo> getReminderCTAs(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo cta = new ViewElementInfo();
        cta.setValue("Select Amount to Pay");
        cta.setType("cta1");
        cta.setActionType("pay_now");
        response.add(cta);

        return response;

    }

    private List<ViewElementInfo> getViewDetailsCTAs(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo cta = new ViewElementInfo();
        cta.setValue("View Details");
        cta.setType("cta1");
        cta.setActionType("view_details");
        response.add(cta);
        return response;

    }


}

