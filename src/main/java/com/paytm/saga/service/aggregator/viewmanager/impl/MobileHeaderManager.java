package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

import com.paytm.saga.common.constant.CardTypes;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;

@Component("mobileHeaderManager")
public class MobileHeaderManager extends CommonHeaderManager {
	private static final Logger logger = LogManager.getLogger(MobileHeaderManager.class);

	@Override
	public List<ViewElementInfo> getHeaders(HistoryView historyView) {
		logger.trace("setting headers for card type " + historyView.getThemeType());
		List<ViewElementInfo> response = null;
		String themeType = historyView.getThemeType();
		switch (themeType) {
		case CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME:
			response = this.getSuggestedRechargeHeaders(historyView);
			break;
		case CardThemeTypes.DATE_CHANGE_CARD_THEME:
			response = this.getDateChangeHeaders(historyView);
			break;
		case CardThemeTypes.MNP_CARD_THEME:
			response = this.getMnpHeaders(historyView);
			break;
		case CardThemeTypes.FAILURE_RECHARGE_CARD_THEME:
			response = this.getRechargeFailureHeaders(historyView);
			break;
		case CardThemeTypes.PENDING_RECHARGE_CARD_THEME:
			response = this.getRechargePendingHeaders(historyView);
			break;
		case CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME:
			response = this.getRechargeCancelledHeaders(historyView);
			break;
		case CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME:
			response = this.getRechargeSuccessHeaders(historyView);
			break;
		case CardThemeTypes.MARKED_AS_PAID_CARD_THEME:
			response = this.getMarkedAsPaidHeaders(historyView);
			break;
		case CardThemeTypes.CASHBACK_CARD_THEME:
			response = this.getScratchCardHeaders(historyView);
			break;
		case CardThemeTypes.DROPOFF_CARD_THEME:
			response = this.getDropOffHeaders(historyView);
			break;
		default:
			break;
		}
		return response;
	}

	private List<ViewElementInfo> getMarkedAsPaidHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Marked as Recharged");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getRechargeCancelledHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Recharge Cancelled");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getRechargePendingHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Recharge Pending");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getRechargeFailureHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Recharge Failed");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getRechargeSuccessHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Recharge Successful");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getMnpHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		if(CardTypes.PAYTYPE_CHANGE_CARD.equals(historyView.getMnpType())){
			header1.setValue("We detected a change from "+capitalizeOperator(historyView.getPreviousOperator())+" "+StringUtils.capitalize(historyView.getPreviousPayType())
					+" to "+capitalizeOperator(historyView.getOperator())+" "+StringUtils.capitalize(historyView.getPayType())+"");
		}else{
			header1.setValue("We detected an operator change from "+capitalizeOperator(historyView.getPreviousOperator())+" "+StringUtils.capitalize(historyView.getPreviousPayType())
					+" to "+capitalizeOperator(historyView.getOperator())+" "+StringUtils.capitalize(historyView.getPayType())+"");
		}
		response.add(header1);
		return response;

	}
	private String capitalizeOperator(String operator){
		int var=0;
		String operatorCapitalize="";
		for(int i=0;i<operator.length();i++){
			if(var==0) {
				operatorCapitalize +=(Character.toUpperCase(operator.charAt(i)));
				var=1;
			}
			else if(operator.charAt(i)==' ') {
				var=0;
				operatorCapitalize +=(operator.charAt(i));
			}
			else operatorCapitalize +=(operator.charAt(i));
		}
		return operatorCapitalize;
	}

	private List<ViewElementInfo> getSuggestedRechargeHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		int planExpiresIn = historyView.getPlanExpiryDays();
		ViewElementInfo header1 = new ViewElementInfo();
		if (historyView.isInvalidPlan()) {
			header1.setValue("Plan Discontinued");
		} else {
			header1.setValue("Suggested Recharge");
		}
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		if (!historyView.isInvalidPlan()) {
			ViewElementInfo header3 = new ViewElementInfo();
			header3.setType("header3");
			if (planExpiresIn > 0) {
				if (planExpiresIn == 1) {
					header3.setValue("Plan expires tomorrow");
				} else {
					header3.setValue(MessageFormat.format("Plan expires in {0} days", planExpiresIn));
				}

			} else if (planExpiresIn == 0) {
				header3.setValue("Plan is going to expire today");
			} else {
				header3.setValue("Plan expired");
			}
			response.add(header3);
		}
		return response;
	}

	@Override
	protected List<ViewElementInfo> getDropOffHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Complete Your Recharge");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		if (historyView.isPlanExpiring()) {
			int planExpiresIn = historyView.getPlanExpiryDays();
			ViewElementInfo header3 = new ViewElementInfo();
			header3.setType("header3");
			if (planExpiresIn > 0) {
				if (planExpiresIn == 1) {
					header3.setValue("Plan expires tomorrow");
				} else {
					header3.setValue(MessageFormat.format("Plan expires in {0} days", planExpiresIn));
				}

			} else if (planExpiresIn == 0) {
				header3.setValue("Plan is going to expire today");
			} else {
				header3.setValue("Plan expired");
			}
			response.add(header3);

		}
		return response;
	}
}
