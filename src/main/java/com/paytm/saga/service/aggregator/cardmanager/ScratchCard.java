package com.paytm.saga.service.aggregator.cardmanager;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.model.ScratchCardHistory;
import com.paytm.saga.service.ScratchCardService;
import com.paytm.saga.service.aggregator.viewmanager.HeadersManager;

@Component
public class ScratchCard {

	private static final Logger logger = LogManager.getLogger(ScratchCard.class);

	private final ScratchCardService scratchCardService;

	@Autowired
	public ScratchCard(final ScratchCardService scratchCardService) {
		this.scratchCardService = scratchCardService;
	}

	public List<HistoryView> getScratchCard(long orderId) {
		List<ScratchCardHistory> scratchCardHistories = scratchCardService.getByOrderId(orderId);
		return this.convertScratchcardToHistoryView(this.getActiveCashBackInfo(scratchCardHistories));
	}

	private List<ScratchCardHistory> getActiveCashBackInfo(List<ScratchCardHistory> scratchCardHistories) {
		List<ScratchCardHistory> finalResponse = new ArrayList<ScratchCardHistory>();
		Long previousScratchCardId = null;
		for (ScratchCardHistory scratchCardHistory : scratchCardHistories) {
			if ((previousScratchCardId == null)) {
				previousScratchCardId = scratchCardHistory.getScratchCardId();
				if (scratchCardHistory.getStatus().equals("UNSCRATCHED")
						|| scratchCardHistory.getStatus().equals("INITIALIZED")) {
					finalResponse.add(scratchCardHistory);
				}
			}

		}
		return finalResponse;
	}

	private List<HistoryView> convertScratchcardToHistoryView(List<ScratchCardHistory> cashbackInfos) {
		List<HistoryView> scratchcards = new ArrayList<HistoryView>();
		for (ScratchCardHistory scratchCardHistory : cashbackInfos) {
			HistoryView scratchcard = new HistoryView();
			scratchcard.setEventType(CardTypes.SCRATCH_CARD);
			scratchcard.setThemeType(CardThemeTypes.CASHBACK_CARD_THEME);
			scratchcard.setEventDate(scratchCardHistory.getPromoCreatedAt());
			scratchcard.setHeadings(HeadersManager.getHeaders(scratchcard));
			scratchcard.setOrderId(scratchCardHistory.getOrderId());
			scratchcards.add(scratchcard);
		}
		return scratchcards;
	}

}
