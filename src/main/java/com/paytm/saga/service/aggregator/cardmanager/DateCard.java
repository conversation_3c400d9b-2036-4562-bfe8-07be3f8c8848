package com.paytm.saga.service.aggregator.cardmanager;

import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.HeadersManager;
import com.paytm.saga.util.DateUtil;

@Component
public class DateCard {

	private static final Logger logger = LogManager.getLogger(DateCard.class);

	public HistoryView addDateInfoCard(Date eventDate, Date previousDate,boolean isLastCard) {
		try {
			HistoryView card = new HistoryView();
			card.setEventType(CardTypes.DATE_CHANGE_CARD);
			card.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME);
			card.setEventDate(eventDate);
			card.setPreviousDate(previousDate);
			card.setLastCard(isLastCard);
			card.setCreatedDate(DateUtil.dateFormatter(eventDate, DateFormats.DATE_TIME_FORMAT));

			List<ViewElementInfo> headers = HeadersManager.getHeaders(card);
			if (headers == null || headers.isEmpty()) {
				return null;
			} else {
				card.setHeadings(headers);
			}
			return card;
		} catch (Exception e) {
			logger.error("DateCard : addDateInfoCard",e);
		}

		return null;
	}
}
