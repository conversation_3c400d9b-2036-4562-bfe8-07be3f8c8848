package com.paytm.saga.service.aggregator;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.paytm.saga.dto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.ChannelHistoryFinalizedService;
import com.paytm.saga.service.ChannelHistoryService;
import com.paytm.saga.service.DropOffService;
import com.paytm.saga.service.ReminderHistoryService;
import com.paytm.saga.service.aggregator.cardmanager.CardManager;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.service.aggregator.cardmanager.DateCard;
import com.paytm.saga.service.aggregator.cardmanager.MarkedAsPaidCard;
import com.paytm.saga.service.aggregator.cardmanager.MnpCard;
import com.paytm.saga.service.aggregator.cardmanager.PlanExpiry;
import com.paytm.saga.service.aggregator.cardmanager.RechargeCard;
import com.paytm.saga.service.aggregator.cardmanager.ScratchCard;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.OMSToRechargeStatus;

@Component("electricityAggregatorService")
public class ElectricityAggregatorService extends CommonAggregatorService {
	private static final Logger logger = LogManager.getLogger(ElectricityAggregatorService.class);

	private final ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
	private final ChannelHistoryService channelHistoryService;
	private final DropOffService dropOffService;

	@Autowired
	public ElectricityAggregatorService(final ChannelHistoryFinalizedService channelHistoryFinalizedService,
			final ChannelHistoryService channelHistoryService,
			final ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil, final DateCard dateCard,
			final MnpCard mnpCard, final PlanExpiry planExpiry,
			@Qualifier("rechargeCard") final RechargeCard rechargeCardService, final ScratchCard scratchCardService,
			final MarkedAsPaidCard markedAsPaidCardService, final DropOffService dropOffService,
			@Qualifier("dropOffCard") final CardManager dropoffCard,
			final ReminderHistoryService reminderHistoryService,
			final @Qualifier("currentBillCard") CardManager currentBillCard,
			final @Qualifier("billExpiredCard") CardManager billExpiredCard) {
		super(channelHistoryFinalizedService, channelHistoryService, channelHistoryFinalizedUtil, dateCard, mnpCard,
				planExpiry, rechargeCardService, dropoffCard, scratchCardService, markedAsPaidCardService,
				reminderHistoryService, currentBillCard, billExpiredCard);
		this.channelHistoryFinalizedUtil = channelHistoryFinalizedUtil;
		this.channelHistoryService = channelHistoryService;
		this.dropOffService = dropOffService;
	}

	@Override
	public HistoryPage aggregateHistoryInfo(long customerId, String rechargeNumber, String service, String pagingState,
			String currentCardOperator, String previousCardOperator, Date previousDate,String client, String version) {
		logger.info("aggregating history data for customerId " + customerId, " rechargeNumber " + rechargeNumber);
		List<ChannelHistory> channelHistories = null;
		Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
		FilteredEvents filteredEvents = new FilteredEvents();
		List<ChannelHistory> events = new ArrayList<ChannelHistory>();
		if (pagingState == null) {
			channelHistories = channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service);
			filteredEvents = this.filterOutLatestStateOfOrders(channelHistories, finalizationDate);
			events.addAll(filteredEvents.getEvents());
		}
		ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId,
				rechargeNumber, service, events.size(), pagingState);
		channelHistoryPage.setMarkFinaliseData(filteredEvents.getMarkFinaliseData());
		channelHistoryPage.setMoveToFinaliseData(filteredEvents.getMoveToFinaliseData());

		List<ChannelHistory> finalizedEvents = channelHistoryPage.getChannelHistories();
		events.addAll(finalizedEvents);
		channelHistoryPage.setChannelHistories(events);
		
		HistoryPage historyPage=this.formatTheHistoryData(channelHistoryPage, customerId, rechargeNumber, service, pagingState,
				previousDate, currentCardOperator, previousCardOperator,client, version);

		return historyPage;
	}

	//TODO
	@Override
	public HistoryPage aggregateHistoryInfo(GetHistoryPageDTO historyPageRequest, String previousCardOperator, Date previousDate) {
		return null;
	}

	private HistoryPage formatTheHistoryData(ChannelHistoryPage channelHistoryPage, final long customerId,
			final String rechargeNumber, final String service, String pagingState, Date previousPageDate,
			String requestedOperator, String previousCardOperator,String client, String version) {
		List<HistoryView> historyViews = new ArrayList<HistoryView>();
		requestedOperator = requestedOperator.split(":")[0];// Getting only operator
		boolean previousTxnHistory = false;

		if (pagingState == null) {
			try {
				previousTxnHistory = false;// we are not hiding mark as paid CTA

//				DropOff dropOff = dropOffService.getDropOff(customerId, rechargeNumber, service, requestedOperator);
				// if (dropOff != null)
				// 	historyViews = this.addDropOffCard(dropOff, historyViews, requestedOperator, previousPageDate,client, version);
				// else {
					List<ReminderHistory> reminderHistories = reminderHistoryService
							.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber, service,
									requestedOperator);
					ReminderHistory currentBill = this.filterOutLatestedBill(reminderHistories);
					if (currentBill != null) {
						boolean ignoreBill = false;
						List<ChannelHistory> channelHistories = channelHistoryPage.getChannelHistories();
						if (channelHistories != null && channelHistories.size() != 0) {
							if (channelHistories.get(0).getEventType().equals(EventTypes.RECHARGE)
									&& (!(channelHistories.get(0).getStatus() != null
											&& channelHistories.get(0).getStatus().equals("7")))) {
								Map<String, String> billsObj = channelHistories.get(0).getBillsObj();
								if (billsObj != null) {
									String dueDate = billsObj.get(Constants.OMSConstants.DUE_DATE);
									try {
										Date date = DateUtil.stringToDate(dueDate, DateFormats.DATE_TIME_FORMAT_2);
										if (currentBill.getDue_date().equals(date)) {
											currentBill.setIsPartial(1);
										}
									} catch (Exception e) {
										logger.error("getting excpetion in date formatting", e);
									}

								}
							}
							String txnStatus = OMSToRechargeStatus.getRechargeStatusByOMSStatus(
									channelHistories.get(0).getStatus(), channelHistories.get(0).getInResponseCode(),
									channelHistories.get(0).getPaymentStatus());
							if (txnStatus != null
									&& (txnStatus.equals("PENDING") || txnStatus.equals("PAYMENT_PENDING"))) {

								Map<String, String> billsObj = channelHistories.get(0).getBillsObj();
								if (billsObj != null) {
									String dueDate = billsObj.get(Constants.OMSConstants.DUE_DATE);
									Date date = DateUtil.stringToDate(dueDate, DateFormats.DATE_TIME_FORMAT_2);
									if (currentBill.getDue_date().equals(date)) {
										ignoreBill = true;
									}
								}

							}
						}
						if (!ignoreBill)
							historyViews = this.addReminderCard(currentBill, historyViews, requestedOperator,
									previousPageDate, previousTxnHistory);
					}
				// }

			} catch (Exception e) {
				logger.error("getting exception in dropoff service ", e);
			}

		} else {
			previousTxnHistory = true;
		}

		for (ChannelHistory channelHistory : channelHistoryPage.getChannelHistories()) {
			if (channelHistory.getEventType().equals(Constants.CommonConstants.NOT_PAID_ON_PAYTM_MESSAGE_TYPE)) {
				historyViews = this.addNotPaidOnPaytmCard(channelHistory, historyViews, requestedOperator,
						previousPageDate, previousTxnHistory,true);
			} else if (channelHistory.getEventType().equals(EventTypes.MARKED_AS_PAID)) {
				historyViews = this.addMarkedAsPaidCard(channelHistory, historyViews, requestedOperator, null,
						previousCardOperator, previousPageDate);
			} else {
				historyViews = this.addRechargeCard(channelHistory, historyViews, requestedOperator, previousPageDate);
				previousCardOperator = channelHistory.getOperator();
			}

		}
		Date previousEventDate = this.getPreviousEventDate(historyViews, previousPageDate);
		if ((!historyViews.isEmpty() || pagingState != null) && (!channelHistoryPage.isAvailableNext()))
			historyViews = this.addDateCard((previousEventDate == null ? new Date() : previousEventDate),
					previousEventDate, true, historyViews);

		markAsFinalized(channelHistoryPage.getMoveToFinaliseData(), channelHistoryPage.getMarkFinaliseData(), service);
		ChannelDetail channelDetail = new ChannelDetail();
		channelDetail.setCustomerId(customerId);
		channelDetail.setOperator(requestedOperator);
		channelDetail.setRechargeNumber(rechargeNumber);
		channelDetail.setService(service);
		HistoryPage historyPage = new HistoryPage(historyViews, historyViews.size(), channelHistoryPage.getLastCardId(),
				channelHistoryPage.isAvailableNext(), "SUCCESS", 200, channelDetail);
		String dedupeDate = "";
		if (previousEventDate != null) {
			dedupeDate = DateUtil.dateFormatter(previousEventDate, DateFormats.DATE_FORMAT);
		} else {
			dedupeDate = DateUtil.dateFormatter(new Date(), DateFormats.DATE_FORMAT);
		}
		String dedupeIds[] = { dedupeDate, previousCardOperator };
		historyPage.setDedupeids(dedupeIds);
		return historyPage;
	}

	@Override
	public List<HistoryView> addMarkedAsPaidCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
			String requestedOperator, String currentCardOperator, String previousCardOperator, Date previousPageDate) {

		logger.info("adding marked as paid card");
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		HistoryView card = markedAsPaidCardService.getMarkedAsPaidCard(channelHistory);
		if (card != null) {
			Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
			historyViews = this.addDateCard(card.getEventDate(), previousDate, false, historyViews);
			historyViews.add(card);

		}
		return historyViews;
	}

}
