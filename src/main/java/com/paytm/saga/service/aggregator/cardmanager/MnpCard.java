package com.paytm.saga.service.aggregator.cardmanager;

import java.util.Arrays;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.service.aggregator.viewmanager.HeadersManager;

@Component
public class MnpCard {

	private static final Logger logger = LogManager.getLogger(MnpCard.class);

	public HistoryView addMnpCard(String previousCardOperator, String currentCardOperator, Date previousPageDate) {
		logger.trace("adding mnp card");
		String oldInfo[] = previousCardOperator.split(":");
		String currentInfo[] = currentCardOperator.split(":");
		if (!oldInfo[0].equals(currentInfo[0])) {
			// add mnp card
			if (!(this.isVIoperator(oldInfo[0]) && this.isVIoperator(currentInfo[0]))) {
				HistoryView mnpCard = new HistoryView();
				mnpCard.setEventType(CardTypes.MNP_CARD);
				mnpCard.setThemeType(CardThemeTypes.MNP_CARD_THEME);
				mnpCard.setEventDate(previousPageDate);
				mnpCard.setOperator(oldInfo[0]);
				mnpCard.setHeadings(HeadersManager.getHeaders(mnpCard));
				return mnpCard;
			}

		}
		return null;
	}

	private boolean isVIoperator(String operator) {
		String vodaOperators[] = { "vodafone idea", "vodafone", "idea" };
		return Arrays.asList(vodaOperators).contains(operator);
	}
}
