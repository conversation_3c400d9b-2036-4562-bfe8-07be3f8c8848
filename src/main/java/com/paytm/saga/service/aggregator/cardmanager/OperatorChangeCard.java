package com.paytm.saga.service.aggregator.cardmanager;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.service.aggregator.viewmanager.HeadersManager;

@Component
public class OperatorChangeCard {

	private static final Logger logger = LogManager.getLogger(OperatorChangeCard.class);

	public HistoryView addOperatorChangeCard(String previousOperator, String currentOperator, Date previousPageDate) {
		logger.trace("adding operator change card");
		
		HistoryView operatorChangeCard = new HistoryView();
		operatorChangeCard.setEventType(CardTypes.OPERATOR_CHANGE_CARD);
		operatorChangeCard.setThemeType(CardThemeTypes.MNP_CARD_THEME);
        operatorChangeCard.setOperator(currentOperator);
        operatorChangeCard.setPreviousOperator(previousOperator);
        operatorChangeCard.setEventDate(previousPageDate);
        operatorChangeCard.setHeadings(HeadersManager.getHeaders(operatorChangeCard));
		return operatorChangeCard;
	}
}
