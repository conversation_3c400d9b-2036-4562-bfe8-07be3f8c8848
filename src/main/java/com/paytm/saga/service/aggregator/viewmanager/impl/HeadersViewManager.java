package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.util.List;

import com.paytm.saga.enums.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.HeaderManagerInterface;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;

@Component("headersViewManager")
public class HeadersViewManager implements ViewManager {
    @Autowired
    @Qualifier("cylinderHeaderManager")
    private HeaderManagerInterface cylinderHeaderManager;
    @Autowired
    @Qualifier("mobileHeaderManager")
    private HeaderManagerInterface mobileHeaderManager;
    @Autowired
    @Qualifier("electricityHeaderManager")
    private HeaderManagerInterface electricityHeaderManager;

    @Autowired
    @Qualifier("utilityHeaderManager")
    private HeaderManagerInterface utilityHeaderManager;

    @Autowired
    @Qualifier("creditCardHeaderManager")
    private HeaderManagerInterface creditCardHeaderManager;


    @Override
    public List<ViewElementInfo> getViewElements(HistoryView historyView) {
        if (historyView.getEventType() != null && historyView.getEventType().equals(EventTypes.RECHARGE)) {
            historyView.setShowInfoIcon(true);
        }

        if (historyView.isUtility() && Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyView.getService())) {
            return creditCardHeaderManager.getHeaders(historyView);
        }
        else if (historyView.isUtility()) {
            return utilityHeaderManager.getHeaders(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.CYLINDER)) {
            return cylinderHeaderManager.getHeaders(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.MOBILE)) {
            return mobileHeaderManager.getHeaders(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.ELECTRICITY)) {
            return electricityHeaderManager.getHeaders(historyView);
        }
        return null;

    }

}
