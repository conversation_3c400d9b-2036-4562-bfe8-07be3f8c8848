package com.paytm.saga.service.aggregator.viewmanager.impl;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.enums.PayType;
import com.paytm.saga.enums.Service;
import com.paytm.saga.service.aggregator.viewmanager.declaration.DisplayValuesManagerInterface;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("displayValuesViewManager")
public class DisplayValuesViewManager implements ViewManager {
    @Autowired
    @Qualifier("cylinderDisplayValuesManager")
    private DisplayValuesManagerInterface cylinderDisplayValuesManager;
    @Autowired
    @Qualifier("mobileDisplayValuesManager")
    private DisplayValuesManagerInterface mobileDisplayValuesManager;
    @Autowired
    @Qualifier("electricityDisplayValuesManager")
    private DisplayValuesManagerInterface electricityDisplayValuesManager;

    @Autowired
    @Qualifier("utilityDisplayValuesManager")
    private DisplayValuesManagerInterface utilityDisplayValuesManager;

    @Autowired
    @Qualifier("creditCardDisplayValuesManager")
    private DisplayValuesManagerInterface creditCardDisplayValuesManager;

    @Override
    public List<ViewElementInfo> getViewElements(HistoryView historyView) {

        if (historyView.isUtility() && Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyView.getService())) {
            return creditCardDisplayValuesManager.getDisplayValues(historyView);
        } else if (historyView.isUtility()) {
            return utilityDisplayValuesManager.getDisplayValues(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.CYLINDER)) {
            return cylinderDisplayValuesManager.getDisplayValues(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.MOBILE)) {
            return mobileDisplayValuesManager.getDisplayValues(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.ELECTRICITY)) {
            return electricityDisplayValuesManager.getDisplayValues(historyView);
        }
        return null;

    }

}
