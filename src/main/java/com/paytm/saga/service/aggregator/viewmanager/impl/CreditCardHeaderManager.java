package com.paytm.saga.service.aggregator.viewmanager.impl;

import static com.paytm.saga.common.constant.UtilityThemeTypes.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.util.DateUtil;

@Service("creditCardHeaderManager")
public class CreditCardHeaderManager extends CommonHeaderManager {
    private static final Logger logger = LogManager.getLogger(CreditCardHeaderManager.class);

    @Override
    public List<ViewElementInfo> getHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = null;
        String themeType = historyView.getThemeType();
        switch (themeType) {

            case BILL_SUCCESS:

            case RECHARGE_SUCCESS:
                response = getRechargeSuccessHeaders(historyView);
                break;

            case RECHARGE_FAILED:
                response = getRechargeFailedHeaders(historyView);
                break;
            case RECHARGE_PENDING:
                response = getRechargePendingHeaders(historyView);
                break;

            case RECHARGE_CANCELLED:
                response = getRechargeCancelledHeaders(historyView);
                break;

            case CREDIT_CARD_BILL_DUE:
                response = getBillDueHeaders(historyView);
                break;

            case CREDIT_CARD_BILL_DUE_TODAY_OR_TOMORROW:
                response = getBillDueTodayOrTomorrowHeaders(historyView);
                break;

            case CREDIT_CARD_BILL_OVERDUE:
                response = getBillOverDueHeaders(historyView);
                break;
            case CREDIT_CARD_NOT_PAID_ON_PAYTM:
                response = getBillNotPaidOnPaytmHeaders(historyView);
                break;
            case CREDIT_CARD_MARKED_AS_PAID:
                response = getBillMarkedPaidHeaders(historyView);
                break;
            case BILL_FAILED:
                response = getBillFailedHeaders(historyView);
                break;
            case BILL_PENDING:
                response = getBillPendingHeaders(historyView);
                break;

            case BILL_CANCELLED:
                response = getBillCancelledHeaders(historyView);
                break;

            case AUTOMATIC_PAYMENT_SUCCESS:
                response = getAutomaticSuccessHeaders(historyView);
                break;
            case AUTOMATIC_PAYMENT_FAILED:
                response = getAutomaticFailedHeaders(historyView);
                break;
            case AUTOMATIC_PAYMENT_PENDING:
                response = getAutomaticPendingHeaders(historyView);
                break;
            case AUTOMATIC_PAYMENT:
                response = getAutomaticHeaders(historyView);
                break;

            default:
                break;
        }

        logger.debug("setting headers for card type {} is {}", historyView.getThemeType(), response);

        return response;
    }

    public List<ViewElementInfo> getRechargeSuccessHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Bill Paid");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    public List<ViewElementInfo> getRechargeFailedHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Bill Payment Failed");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    public List<ViewElementInfo> getRechargePendingHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Bill Payment Pending");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    public List<ViewElementInfo> getRechargeCancelledHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Bill Payment Cancelled");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    public List<ViewElementInfo> getBillDueHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue("Total Due");
        header1.setType("header2");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue(formatNumber(historyView.getAmount()));
        header2.setType("header1");
        response.add(header2);
        return response;
    }

    public List<ViewElementInfo> getBillDueTodayOrTomorrowHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue("Total Due");
        header1.setType("header2");
        response.add(header1);

        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue(formatNumber(historyView.getAmount()));
        header2.setType("header1");
        response.add(header2);
        ViewElementInfo footer = new ViewElementInfo();
        Date dueDate = DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2);
        if(DateUtil.compareDateWithoutTime(dueDate, new Date()) == 0) {
            footer.setValue("Bill Due Today");
        } else {
            footer.setValue("Bill Due Tomorrow");
        }
        footer.setType("footer1");
        response.add(footer);
        return response;
    }

    public List<ViewElementInfo> getBillOverDueHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue("Total Due");
        header1.setType("header2");
        response.add(header1);

        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue(formatNumber(historyView.getAmount()));
        header2.setType("header1");
        response.add(header2);

        ViewElementInfo footer = new ViewElementInfo();
        footer.setValue("Bill Overdue");
        footer.setType("footer1");
        response.add(footer);
        return response;
    }


    public List<ViewElementInfo> getBillNotPaidOnPaytmHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue("Total Due");
        header1.setType("header2");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue(formatNumber(historyView.getAmount()));
        header2.setType("header1");
        response.add(header2);
        ViewElementInfo footer = new ViewElementInfo();
        footer.setValue("Bill Not Paid on Paytm");
        footer.setType("footer1");
        response.add(footer);
        return response;
    }

    public List<ViewElementInfo> getBillMarkedPaidHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();

        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue("Total Due");
        header1.setType("header2");
        response.add(header1);

        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue(formatNumber(historyView.getAmount()));
        header2.setType("header1");
        response.add(header2);

        ViewElementInfo footer = new ViewElementInfo();
        footer.setValue("Bill Marked as Paid");
        footer.setType("footer1");
        response.add(footer);

        return response;
    }


    public List<ViewElementInfo> getBillFailedHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Bill Payment Failed");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    public List<ViewElementInfo> getBillPendingHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Bill Payment Pending");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    public List<ViewElementInfo> getBillCancelledHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Bill Payment Cancelled");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getAutomaticSuccessHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Paid Automatically");
        header2.setType("header2");
        response.add(header2);
        return response;
    }
    private List<ViewElementInfo> getAutomaticFailedHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Automatic Payment Failed");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getAutomaticPendingHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Automatic Payment Pending");
        header2.setType("header2");
        response.add(header2);
        return response;
    }
    private List<ViewElementInfo> getAutomaticHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        if (historyView.getAutomaticDate() != null) {
            ViewElementInfo footer1 = new ViewElementInfo();
            if(historyView.isNative()){
                footer1.setValue("Will be paid automatically on " + DateUtil.dateFormatter(historyView.getAutomaticDate(), DateFormats.DD_MMM));
            }else{
                footer1.setValue("Will be paid automatically on " + DateUtil.dateFormatter(historyView.getAutomaticDate(), DateFormats.D_MMM));
            }
            footer1.setType("footer1");
            response.add(footer1);
        }
        return response;
    }

}
