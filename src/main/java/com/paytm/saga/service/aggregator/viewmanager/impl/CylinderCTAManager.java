package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.CTAManager;
import com.paytm.saga.util.DateUtil;

@Component("cylinderCTAManager")
public class CylinderCTAManager implements CTAManager {
	private static final Logger logger = LogManager.getLogger(CylinderCTAManager.class);

	@Override
	public List<ViewElementInfo> getCTAs(HistoryView historyView) {
		logger.debug("setting cta for card type " + historyView.getThemeType());
		List<ViewElementInfo> response = null;
		String themeType = historyView.getThemeType();
		switch (themeType) {
		case CardThemeTypes.SUCCESS_CYLINDER_BOOKING_CARD_THEME:
			response = this.getSuccessRechargeCTAs(historyView);
			break;
		case CardThemeTypes.FAILURE_CYLINDER_BOOKING_CARD_THEME:
			response = this.getFailureRechargeCTAs();
			break;
		case CardThemeTypes.REVERSAL_FAILURE_CYLINDER_BOOKING_CARD_THEME:
			response = this.getFailureRechargeCTAs();
			break;
		case CardThemeTypes.PENDING_CYLINDER_BOOKING_CARD_THEME:
			response = null;
			break;
		case CardThemeTypes.CANCELLED_CYLINDER_BOOKING_CARD_THEME:
			response = this.getFailureRechargeCTAs();
			break;
		case CardThemeTypes.PAYMENT_FAILURE_CYLINDER_BOOKING_CARD_THEME:
			response = this.getFailureRechargeCTAs();
			break;
		case CardThemeTypes.DROPOFF_CARD_THEME:
			response = this.getDropoffRechargeCTAs();
			break;
		default:
			break;
		}
		return response;
	}

	private List<ViewElementInfo> getDropoffRechargeCTAs() {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		cta.setValue("Continue Payment");
		cta.setType("cta1");
		cta.setActionType("retry_recharge");
		cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.DROPOFF_CARD_THEME));
		response.add(cta);
		return response;

	}

	private List<ViewElementInfo> getFailureRechargeCTAs() {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		cta.setValue("Retry");
		cta.setType("cta1");
		cta.setActionType("retry_recharge");
		cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.FAILURE_CYLINDER_BOOKING_CARD_THEME));
		response.add(cta);
		return response;

	}

	private List<ViewElementInfo> getSuccessRechargeCTAs(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		try {
			cta.setType("cta1");
			if (historyView.getEventDate().after(DateUtil.dateIncrDecr(DateUtil.timestampToDate(new Date()), -3))) {
				cta.setValue("Track Delivery");
				cta.setActionType("track_delivery");
			} else {
				cta.setValue("Book Again");
				cta.setActionType("repeat_recharge");
			}
		} catch (ParseException e) {
			logger.error("getting exception in creating cta1 in cylinder",e);
			cta.setValue("Book Again");
			cta.setActionType("repeat_recharge");
		}
		response.add(cta);
		return response;
	}

}
