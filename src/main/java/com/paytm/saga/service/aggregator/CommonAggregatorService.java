package com.paytm.saga.service.aggregator;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.FilteredEvents;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.ChannelHistoryFinalizedService;
import com.paytm.saga.service.ChannelHistoryService;
import com.paytm.saga.service.ReminderHistoryService;
import com.paytm.saga.service.aggregator.cardmanager.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

abstract public class CommonAggregatorService implements AggregatorServiceInterface {
	private static final Logger logger = LogManager.getLogger(CommonAggregatorService.class);

	protected final ChannelHistoryFinalizedService channelHistoryFinalizedService;
	protected final ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
	protected final DateCard dateCardService;
	protected final ScratchCard scratchCardService;
	protected final CardManager rechargeCard;
	protected final ChannelHistoryService channelHistoryService;
	protected final MarkedAsPaidCard markedAsPaidCardService;
	protected final CardManager dropoffCard;
	protected final ReminderHistoryService reminderHistoryService;
	protected final CardManager currentBillCard;
	protected final CardManager billExpiredCard;

	public CommonAggregatorService(final ChannelHistoryFinalizedService channelHistoryFinalizedService,
			final ChannelHistoryService channelHistoryService,
			final ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil, final DateCard dateCard,
			final MnpCard mnpCard, final PlanExpiry planExpiry, final CardManager rechargeCard,
			final CardManager dropoffCard, final ScratchCard scratchCardService,
			final MarkedAsPaidCard markedAsPaidCardService, final ReminderHistoryService reminderHistoryService,
			final CardManager currentBillCard, final CardManager billExpiredCard) {
		this.channelHistoryFinalizedService = channelHistoryFinalizedService;
		this.channelHistoryFinalizedUtil = channelHistoryFinalizedUtil;
		this.dateCardService = dateCard;
		this.rechargeCard = rechargeCard;
		this.scratchCardService = scratchCardService;
		this.channelHistoryService = channelHistoryService;
		this.markedAsPaidCardService = markedAsPaidCardService;
		this.dropoffCard = dropoffCard;
		this.reminderHistoryService = reminderHistoryService;
		this.currentBillCard = currentBillCard;
		this.billExpiredCard = billExpiredCard;
	}

	public List<HistoryView> addDateCard(Date eventDate, Date previousDate, boolean isLastCard,
			List<HistoryView> historyViews) {
		// System.out.println("eventDate=" + eventDate + ",previousDate=" + previousDate
		// + ",isLastCard=" + isLastCard);
		HistoryView dateCard = dateCardService.addDateInfoCard(eventDate, previousDate, isLastCard);
		if (dateCard != null) {
			historyViews.add(dateCard);
		}
		return historyViews;
	}

	public Date getPreviousEventDate(List<HistoryView> historyViews, Date previousPageDate) {
		int count = historyViews.size();
		if (count > 0) {
			HistoryView historyView = historyViews.get(count - 1);
			return historyView.getEventDate();
		} else {
			return previousPageDate;
		}
	}

	public List<HistoryView> addScratchCard(HistoryView historyView, List<HistoryView> historyViews,
			Date rechargeDate) {
		logger.trace("adding scratch card for order id " + historyView.getOrderId());
		boolean isLastCard = false;
		List<HistoryView> scratchCards = scratchCardService.getScratchCard(historyView.getOrderId());
		for (HistoryView scratchCard : scratchCards) {
			scratchCard.setEventDate(rechargeDate);
//			Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
//			historyViews = this.addDateCard(scratchCard.getEventDate(), previousDate, isLastCard, historyViews);
			historyViews.add(scratchCard);
		}

		return historyViews;
	}

	public List<HistoryView> addDropOffCard(DropOff dropOff, List<HistoryView> historyViews, String requestedOperator,
			Date previousPageDate,String client, String version) {
		logger.trace("adding drop card");
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setDropOff(dropOff);
		cardInfoDto.setClient(client);
		cardInfoDto.setVersion(version);
		HistoryView dropCardInfo = dropoffCard.getCardInfo(cardInfoDto);
		if (dropCardInfo != null) {
			Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
			historyViews.add(dropCardInfo);
			historyViews = this.addDateCard(dropCardInfo.getEventDate(), previousDate, false, historyViews);
		}
		return historyViews;
	}

	public List<HistoryView> addReminderCard(ReminderHistory reminderHistory, List<HistoryView> historyViews,
			String requestedOperator, Date previousPageDate,boolean previousTxnHistory) {
		logger.trace("adding reminder card");
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setReminderHistory(reminderHistory);
		cardInfoDto.setPreviousTxnHistory(previousTxnHistory);
		
		HistoryView card = currentBillCard.getCardInfo(cardInfoDto);
		if (card != null) {
			Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
			historyViews = this.addDateCard(card.getEventDate(), previousDate, false, historyViews);
			historyViews.add(card);
			
		}
		return historyViews;
	}

	public List<HistoryView> addNotPaidOnPaytmCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
			String requestedOperator, Date previousPageDate,boolean previousTxnHistory,boolean isNative) {
		logger.trace("adding bill expired card card");
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setPreviousTxnHistory(previousTxnHistory);
		cardInfoDto.setNative(isNative);
		HistoryView card = billExpiredCard.getCardInfo(cardInfoDto);
		if (card != null) {
			Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
			historyViews = this.addDateCard(card.getEventDate(), previousDate, false, historyViews);
			historyViews.add(card);
			
		}
		return historyViews;
	}

	public List<HistoryView> addRechargeCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
			String requestedOperator, Date previousPageDate) {
		logger.trace("adding recharge card");
		boolean hideCTA = false;
		boolean isLastCard = false;
		// hiding CTA if current(channelHistory) recharge card operator is different
		// from requested operator
		if (!requestedOperator.equalsIgnoreCase(channelHistory.getOperator())) {
			logger.trace("hiding CTA requestedOperator: " + requestedOperator + " card operator: "
					+ channelHistory.getOperator());
			hideCTA = true;
		}
		Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
		historyViews = this.addDateCard(channelHistory.getTransactionTime(), previousDate, isLastCard, historyViews);
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(hideCTA);
		HistoryView rechargeCardView = rechargeCard.getCardInfo(cardInfoDto);

		if (rechargeCardView.getStatus().equals("SUCCESS"))
			historyViews = this.addScratchCard(rechargeCardView, historyViews, rechargeCardView.getEventDate());
		historyViews.add(rechargeCardView);
		return historyViews;
	}

	public FilteredEvents filterOutLatestStateOfOrders(List<ChannelHistory> channelHistories, Date finalizationDate) {
		FilteredEvents filteredEvents = new FilteredEvents();
		Long previousRechargeOrderId = null;
		List<ChannelHistory> filteredData = new ArrayList<ChannelHistory>();// Data to show on chat screen
		List<ChannelHistory> markFinalizeData = new ArrayList<ChannelHistory>();// data to mark as finalized n history
																				// table
		List<ChannelHistory> moveToFinalizeData = new ArrayList<ChannelHistory>();// data to move from history to
																					// finalized
		for (ChannelHistory channelHistory : channelHistories) {
			if (channelHistory.getEventType().equals(EventTypes.RECHARGE)) {
				Long orderId = channelHistory.getOrderId();
				if (((channelHistory.getService().equals(Constants.ServiceTypeConstants.MOBILE) && channelHistory.getPaytype().equals("prepaid"))
						|| (channelHistory.getService().equals(Constants.ServiceTypeConstants.CYLINDER)
								|| channelHistory.getService().equals(Constants.ServiceTypeConstants.ELECTRICITY)))
						&& (previousRechargeOrderId == null || !orderId.equals(previousRechargeOrderId))) {
					filteredData.add(channelHistory);
					if (!channelHistory.isFinalisedState()
							&& channelHistory.getTransactionTime().before(finalizationDate)) {
						channelHistory.setFinalisedState(true);
						moveToFinalizeData.add(channelHistory);
						markFinalizeData.add(channelHistory);
					}
				} else {
					logger.trace("ignoring event");
				}
				previousRechargeOrderId = orderId;
			} else if (channelHistory.getEventType().equals(EventTypes.MARKED_AS_PAID)
					|| channelHistory.getEventType().equals(Constants.CommonConstants.NOT_PAID_ON_PAYTM_MESSAGE_TYPE)) {
				filteredData.add(channelHistory);
			}
			if (!channelHistory.isFinalisedState() && channelHistory.getTransactionTime().before(finalizationDate)) {
				channelHistory.setFinalisedState(true);
				markFinalizeData.add(channelHistory);
			}
		}
		filteredEvents.setEvents(filteredData);
		filteredEvents.setMarkFinaliseData(markFinalizeData);
		filteredEvents.setMoveToFinaliseData(moveToFinalizeData);
		logger.trace("filteredData size" + filteredData.size());
		return filteredEvents;
	}

	public void markAsFinalized(List<ChannelHistory> moveToFinalizedEvents, List<ChannelHistory> markFinalizedEvents, String service) {
		if (moveToFinalizedEvents != null && moveToFinalizedEvents.size() > 0) {
			logger.trace("marking the finalized to orders count ", moveToFinalizedEvents.size());
			List<ChannelHistoryFinalized> channelHistoryFinalized = channelHistoryFinalizedUtil
					.convertHistoryToFinalizedHistory(moveToFinalizedEvents);
			channelHistoryFinalizedService.saveAll(channelHistoryFinalized,service);
			channelHistoryService.saveAll(markFinalizedEvents,service);

		}
	}

	public List<HistoryView> addMarkedAsPaidCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
			String requestedOperator, String currentCardOperator, String previousCardOperator, Date previousPageDate) {
		logger.trace("adding marked as paid card");
		boolean isLastCard = false;
		Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
		historyViews = this.addDateCard(channelHistory.getTransactionTime(), previousDate, isLastCard, historyViews);
		HistoryView markedAsPaidCard = markedAsPaidCardService.getMarkedAsPaidCard(channelHistory);
		historyViews.add(markedAsPaidCard);
		return historyViews;
	}

	public ReminderHistory filterOutLatestedBill(List<ReminderHistory> reminderHistories) {
		for (ReminderHistory reminderHistory : reminderHistories) {
			Date dueDate = reminderHistory.getDue_date();
			// try {
//				if (dueDate != null && DateUtil.dateIncrDecr(DateUtil.timestampToDate(dueDate), 3).after(DateUtil.timestampToDate(new Date()))) {
//					// Bill not paid - From overdue till next bill gen
//				} else if (dueDate != null && DateUtil.timestampToDate(new Date()).before(DateUtil.timestampToDate(dueDate))) {
//					// Bill Overdue
//				} else if (dueDate != null && dueDate.before(DateUtil.timestampToDate(new Date()))) {
//					// New Bill
//				}
//			} catch (ParseException e) {
//				e.printStackTrace();
//			}
			if (reminderHistory.getAmount() > 0 && reminderHistory.getStatus() != 15) {
				return reminderHistory;
			}
			break;
		}
		return null;
	}

	public List<ReminderHistory> filterReminderForCC(List<ReminderHistory> reminderHistories,Long productId){
		List<ReminderHistory> filteredReminder=new ArrayList<>();
		ProductMin historyPageRequestProduct = CVRProductCache.getInstance().getProductDetails(productId);

		for (ReminderHistory reminderHistory : reminderHistories){
			ProductMin reminderHistoryProduct = CVRProductCache.getInstance().getProductDetails(reminderHistory.getProductId());
			if(historyPageRequestProduct != null && reminderHistoryProduct != null && historyPageRequestProduct.getBankCode().equalsIgnoreCase(reminderHistoryProduct.getBankCode()) && historyPageRequestProduct.getCardNetwork().equalsIgnoreCase(reminderHistoryProduct.getCardNetwork())){
				filteredReminder.add(reminderHistory);
			}
		}
		filteredReminder=filteredReminder.stream()
				.sorted((o1, o2) -> o2.getUpdatedAt().compareTo(o1.getUpdatedAt()))
				.collect(Collectors.toList());
		return filteredReminder;

	}
}
