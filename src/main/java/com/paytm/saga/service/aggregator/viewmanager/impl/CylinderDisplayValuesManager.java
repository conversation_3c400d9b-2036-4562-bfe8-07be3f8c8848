package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.util.List;

import org.springframework.stereotype.Component;

import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.DisplayValuesManagerInterface;

@Component("cylinderDisplayValuesManager")
public class CylinderDisplayValuesManager implements DisplayValuesManagerInterface {

	@Override
	public List<ViewElementInfo> getDisplayValues(HistoryView historyView) {
		// TODO Auto-generated method stub
		return null;
	}

}
