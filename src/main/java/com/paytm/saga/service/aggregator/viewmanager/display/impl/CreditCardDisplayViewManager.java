package com.paytm.saga.service.aggregator.viewmanager.display.impl;

import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.DisplayValuesManagerInterface;
import com.paytm.saga.util.DateUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.paytm.saga.common.constant.UtilityThemeTypes.*;

@Service("creditCardDisplayValuesManager")
public class CreditCardDisplayViewManager implements DisplayValuesManagerInterface {

    private static final Logger logger = LogManager.getLogger(UtilityDisplayViewManager.class);


    @Override
    public List<ViewElementInfo> getDisplayValues(HistoryView historyView) {

        List<ViewElementInfo> response = null;
        String themeType = historyView.getThemeType();
        switch (themeType) {
            case CREDIT_CARD_BILL_DUE:
            case CREDIT_CARD_BILL_DUE_TODAY_OR_TOMORROW:
            case CREDIT_CARD_BILL_OVERDUE:
            case CREDIT_CARD_NOT_PAID_ON_PAYTM:
            case CREDIT_CARD_MARKED_AS_PAID:
            case AUTOMATIC_PAYMENT:
                response = getDisplayCTA(historyView);
                break;
            default:
                break;
        }

        logger.debug("display for  card type  {} is {} ", themeType, response);

        return response;
    }

    List<ViewElementInfo> getDisplayCTA(HistoryView historyView) {
        List<ViewElementInfo> viewElementInfos = new ArrayList<ViewElementInfo>();
        if (historyView.getBillDate() != null) {

            ViewElementInfo viewElementInfo = new ViewElementInfo();

            viewElementInfo.setValue(historyView.getMinimumDue() + "");
            viewElementInfo.setKey("Minimum Due");
            viewElementInfo.setType("ds1");
            viewElementInfo.setAmount(true);
            viewElementInfos.add(viewElementInfo);
        }
        if (historyView.getDueDate() != null) {
            ViewElementInfo viewElementInfo = new ViewElementInfo();

            viewElementInfo.setValue(DateUtil.dateFormatter(
                    DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
                    DateFormats.EEE_DD_MMM));
            viewElementInfo.setKey("Due Date");
            viewElementInfo.setType("ds2");
            viewElementInfos.add(viewElementInfo);
        }
        return viewElementInfos;
    }

}
