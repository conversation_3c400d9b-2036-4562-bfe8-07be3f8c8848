package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;

@Component("electricityHeaderManager")
public class ElectricityHeaderManager extends CommonHeaderManager {
	private static final Logger logger = LogManager.getLogger(ElectricityHeaderManager.class);

	@Override
	public List<ViewElementInfo> getHeaders(HistoryView historyView) {
		logger.debug("setting headers for card type " + historyView.getThemeType());
		List<ViewElementInfo> response = null;
		String themeType = historyView.getThemeType();
		switch (themeType) {
		case CardThemeTypes.NEW_BILL_CARD_THEME:
			response = this.getNewBillHeaders(historyView);
			break;
		case CardThemeTypes.BILL_OVER_DUE_CARD_THEME:
			response = this.getBillOverDueHeaders(historyView);
			break;
		case CardThemeTypes.BILL_PARTIAL_PAID_CARD_THEME:
			response = this.getBillPartialPaidHeaders(historyView);
			break;
		case CardThemeTypes.BILL_AFTER_OVER_DUE_CARD_THEME:
			response = this.getBillNotPaidOnPaytmHeaders(historyView);
			break;
		case CardThemeTypes.ELECTRICITY_SUCCESS_RECHARGE_CARD_THEME:
			response = this.getElectricitySuccessHeaders(historyView);
			break;
		case CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME:
			response = this.getElectricityFailureHeaders(historyView);
			break;
		case CardThemeTypes.ELECTRICITY_PENDING_RECHARGE_CARD_THEME:
			response = this.getElectricityPendingHeaders(historyView);
			break;
		case CardThemeTypes.BILL_EXPIRED_CARD_THEME:
			response = this.getElectricityBillExpiredHeaders(historyView);
			break;
		case CardThemeTypes.MARKED_AS_PAID_CARD_THEME:
			response = this.getMarkedAsPaidHeaders(historyView);
			break;
		case CardThemeTypes.DROPOFF_CARD_THEME:
			response = this.getDropOffHeaders(historyView);
			break;
		default:
			break;
		}
		return response;
	}

	private List<ViewElementInfo> getMarkedAsPaidHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Bill Marked as Paid");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}
	
	private List<ViewElementInfo> getElectricityBillExpiredHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Bill not paid on Paytm");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getElectricitySuccessHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Bill Paid");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getElectricityPendingHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Bill Payment Pending");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getElectricityFailureHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Bill Payment Failed");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getBillNotPaidOnPaytmHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Bill not paid on Paytm");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getBillPartialPaidHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Remaining Bill");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getBillOverDueHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Bill Overdue");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private List<ViewElementInfo> getNewBillHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("New Bill");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}
	
	protected List<ViewElementInfo> getDropOffHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Complete your Payment");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		
		ViewElementInfo header3 = new ViewElementInfo();
		header3.setValue("You did not complete your last payment");
		header3.setType("header3");
		response.add(header3);
		
		return response;
	}

}
