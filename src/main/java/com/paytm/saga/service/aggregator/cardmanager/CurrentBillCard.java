package com.paytm.saga.service.aggregator.cardmanager;

import java.text.ParseException;
import java.util.Date;

import jakarta.validation.constraints.NotNull;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.constant.BillStateTypes;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.DateUtil;

@Component("currentBillCard")
public class CurrentBillCard implements CardManager {
	private static final Logger logger = LogManager.getLogger(CurrentBillCard.class);
	private ViewManager ctaManager;
	private ViewManager displayValuesManager;
	private ViewManager headersViewManager;

	
	@Autowired
	public CurrentBillCard(final @NotNull @Qualifier("ctaViewManager") ViewManager ctaManager,
			final @NotNull @Qualifier("displayValuesViewManager") ViewManager displayValuesManager,
			final @NotNull @Qualifier("headersViewManager") ViewManager headersViewManager) {
		this.ctaManager = ctaManager;
		this.displayValuesManager = displayValuesManager;
		this.headersViewManager = headersViewManager;
	}
	
	@Override
	public HistoryView getCardInfo(CardInfoDto cardInfoDto) {
		HistoryView historyView = new HistoryView();
		historyView.setService(cardInfoDto.getReminderHistory().getService());
		historyView.setNative(cardInfoDto.isNative());
		historyView.setOperator(cardInfoDto.getReminderHistory().getOperator());
		historyView.setAmount(cardInfoDto.getReminderHistory().getAmount());
		if (cardInfoDto.getReminderHistory().getBill_date() != null)
			historyView.setBillDate(DateUtil.dateFormatter(cardInfoDto.getReminderHistory().getBill_date(),
					DateFormats.DATE_TIME_FORMAT_2));
		if (cardInfoDto.getReminderHistory().getDue_date() != null)
			historyView.setDueDate(DateUtil.dateFormatter(cardInfoDto.getReminderHistory().getDue_date(),
					DateFormats.DATE_TIME_FORMAT_2));
		historyView.setEventType(EventTypes.BILL);
		historyView.setEventDate(new Date());
		historyView.setBillState(this.getBillState(cardInfoDto.getReminderHistory()));
		historyView.setPreviousTxnHistory(cardInfoDto.isPreviousTxnHistory());
		historyView.setThemeType(this.getThemeType(historyView));
		historyView.setHeadings(headersViewManager.getViewElements(historyView));
		historyView.setDisplayValues(displayValuesManager.getViewElements(historyView));
		historyView.setCta(ctaManager.getViewElements(historyView));
		historyView.setCreatedDate(
				DateUtil.dateFormatter(new Date(), DateFormats.DATE_TIME_FORMAT));
		
		
		try {
			if (historyView.getDueDate() != null) {
				historyView.setDueDate(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.DATE_FORMAT_2));
			}
			
			if (historyView.getBillDate() != null) {
				historyView.setBillDate(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getBillDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.DATE_FORMAT_2));
			}			
		}catch(RuntimeException e) {
			logger.error("Getting exception in parsing billdate duedate in recharge card",e);
		}
		return historyView;
	}

	private int getBillState(ReminderHistory reminderHistory) {
		Date dueDate = reminderHistory.getDue_date();
		try {
			if (dueDate != null && DateUtil.timestampToDate(new Date())
					.after(DateUtil.dateIncrDecr(DateUtil.timestampToDate(dueDate), CommonConsts.BILL_OVERDUE_DAYS))) {
				// Bill not paid - From overdue till next bill gen
				return BillStateTypes.BILL_AFTER_OVERDUE;
			} else if (dueDate != null
					&& DateUtil.timestampToDate(new Date()).after(DateUtil.timestampToDate(dueDate))) {
				// Bill Overdue
				return BillStateTypes.BILL_OVERDUE;
			} else if (dueDate != null) {
				if (reminderHistory.getIsPartial() != null && reminderHistory.getIsPartial() == 1) {
					// Partial Paid
					return BillStateTypes.BILL_PARTIAL_PAID;
				} else {
					// New Bill
					return BillStateTypes.ACTIVE_BILL;
				}

			}
		} catch (ParseException e) {
			logger.error("error in parsing date", e);
		}
		return 0;
	}

	private String getThemeType(HistoryView historyView) {
		if (historyView.getBillState() == BillStateTypes.ACTIVE_BILL) {
			return CardThemeTypes.NEW_BILL_CARD_THEME;
		} else if (historyView.getBillState() == BillStateTypes.BILL_PARTIAL_PAID) {
			return CardThemeTypes.BILL_PARTIAL_PAID_CARD_THEME;
		} else if (historyView.getBillState() == BillStateTypes.BILL_OVERDUE) {
			return CardThemeTypes.BILL_OVER_DUE_CARD_THEME;
		} else if (historyView.getBillState() == BillStateTypes.BILL_AFTER_OVERDUE) {
			return CardThemeTypes.BILL_AFTER_OVER_DUE_CARD_THEME;
		}
		return null;
	}

}
