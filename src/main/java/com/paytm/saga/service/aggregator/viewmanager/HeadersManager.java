package com.paytm.saga.service.aggregator.viewmanager;

import java.math.RoundingMode;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.DeepLinkTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.RichInfo;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.util.DateUtil;

public class HeadersManager {

	private static final Logger logger = LogManager.getLogger(HeadersManager.class);

	public static List<ViewElementInfo> getHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = null;
		String themeType = historyView.getThemeType();
		switch (themeType) {
		case CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME:
			response = getSuggestedRechargeHeaders(historyView);
			break;
		case CardThemeTypes.DATE_CHANGE_CARD_THEME:
			response = getDateChangeHeaders(historyView);
			break;
		case CardThemeTypes.MNP_CARD_THEME:
			response = getMnpHeaders(historyView);
			break;
		case CardThemeTypes.FAILURE_RECHARGE_CARD_THEME:
			response = getRechargeFailureHeaders(historyView);
			break;
		case CardThemeTypes.PENDING_RECHARGE_CARD_THEME:
			response = getRechargePendingHeaders(historyView);
			break;
		case CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME:
			response = getRechargeCancelledHeaders(historyView);
			break;
		case CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME:
			response = getRechargeSuccessHeaders(historyView);
			break;
		case CardThemeTypes.MARKED_AS_PAID_CARD_THEME:
			response = getMarkedAsPaidHeaders(historyView);
			break;
		case CardThemeTypes.CASHBACK_CARD_THEME:
			response = getScratchCardHeaders(historyView);
			break;
		default:
			break;
		}
		return response;
	}

	private static List<ViewElementInfo> getMarkedAsPaidHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Marked as Recharged");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private static List<ViewElementInfo> getRechargeCancelledHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Recharge Cancelled");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private static List<ViewElementInfo> getRechargePendingHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Recharge Pending");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private static List<ViewElementInfo> getRechargeFailureHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Recharge Failed");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private static List<ViewElementInfo> getRechargeSuccessHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Recharge Successful");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	private static List<ViewElementInfo> getMnpHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		if(historyView.getEventType() == CardTypes.OPERATOR_CHANGE_CARD) {
			header1.setValue("You changed the operator from " + historyView.getPreviousOperator()+" to "+historyView.getOperator());
		} else {
			header1.setValue("You changed the operator to " + historyView.getOperator());
		}
		response.add(header1);
		return response;
	}


	private static List<ViewElementInfo> getDateChangeHeaders(HistoryView historyView) {
		try {
			List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
			ViewElementInfo header = new ViewElementInfo();
			Date todayDate = DateUtil.timestampToDate(new Date());
			Date cardDate = DateUtil.timestampToDate(historyView.getEventDate());

			if (historyView.isLastCard()) {
				if ((todayDate.equals(cardDate))) {
					header.setValue("Today");
				} else {
					header.setValue(DateUtil.dateFormatter(cardDate, DateFormats.DATE_FORMAT));
				}
				headers.add(header);
				return headers;
			} else if (historyView.getPreviousDate() != null) {
				Date preDate = DateUtil.timestampToDate(historyView.getPreviousDate());
				if (!preDate.equals(cardDate)) {
					if ((todayDate.equals(preDate))) {
						header.setValue("Today");
					} else
						header.setValue(DateUtil.dateFormatter(preDate, DateFormats.DATE_FORMAT));
					headers.add(header);
					return headers;
				}
			}
		} catch (ParseException e) {
			logger.error("HeadersManager : getDateChangeHeaders",e.getMessage(), e);
		}

		return null;
	}

	private static List<ViewElementInfo> getSuggestedRechargeHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		int planExpiresIn = historyView.getPlanExpiryDays();
		ViewElementInfo header1 = new ViewElementInfo();
		if (historyView.isInvalidPlan()) {
			header1.setValue("Plan Discontinued");
		} else {
			header1.setValue("Suggested Recharge");
		}
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		if(!historyView.isInvalidPlan()) {
			ViewElementInfo header3 = new ViewElementInfo();
			header3.setType("header3");
			if (planExpiresIn > 0) {
				if (planExpiresIn == 1) {
					header3.setValue("Plan expires tomorrow");
				} else {
					header3.setValue(MessageFormat.format("Plan expires in {0} days", planExpiresIn));
				}

			} else if (planExpiresIn == 0) {
				header3.setValue("Plan is going to expire today");
			} else {
				header3.setValue("Plan expired");
			}
			response.add(header3);			
		}
		return response;
	}

	private static List<ViewElementInfo> getScratchCardHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		List<RichInfo> richInfo = new ArrayList<RichInfo>();
		richInfo.add(new RichInfo("scratch card", DeepLinkManager.getDeeplinkByType(DeepLinkTypes.SCRATCH_DEEP_LINK)));
		header1.setRichInfo(richInfo);
		header1.setType("header1");
		header1.setValue("You’ve won a scratch card");
		response.add(header1);
		return response;
	}

	private static String formatNumber(double number) {
		NumberFormat nf = NumberFormat.getNumberInstance();
		nf.setMaximumFractionDigits(2);
		nf.setRoundingMode(RoundingMode.HALF_UP);
		String rounded = nf.format(number);
		return rounded;
	}
}
