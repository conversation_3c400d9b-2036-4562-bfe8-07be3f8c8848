package com.paytm.saga.service.aggregator.cardmanager;

import java.util.Map;

import jakarta.validation.constraints.NotNull;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.DeepLinkTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.viewmanager.DisplayValuesManager;
import com.paytm.saga.service.aggregator.viewmanager.HeadersManager;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.DateUtil;

@Component
public class MarkedAsPaidCard {
	private static final Logger logger = LogManager.getLogger(MarkedAsPaidCard.class);

	private ViewManager displayValuesManager;
	private ViewManager headersViewManager;
	private ViewManager ctaManager;
	
	
	@Autowired
	public MarkedAsPaidCard(final @NotNull @Qualifier("ctaViewManager") ViewManager ctaManager,
			final @NotNull @Qualifier("displayValuesViewManager") ViewManager displayValuesManager,
			final @NotNull @Qualifier("headersViewManager") ViewManager headersViewManager) {
		this.ctaManager = ctaManager;
		this.displayValuesManager = displayValuesManager;
		this.headersViewManager = headersViewManager;
	}
	
	public HistoryView getMarkedAsPaidCard(ChannelHistory channelHistory) {
		logger.trace("adding marked as paid card");
		return this.convertHistoryViewEntityToDTO(channelHistory);
	}

	private HistoryView convertHistoryViewEntityToDTO(ChannelHistory channelHistory) {
		HistoryView historyView = new HistoryView();
		historyView.setService(channelHistory.getService());
		historyView.setThemeType(CardThemeTypes.MARKED_AS_PAID_CARD_THEME);
		historyView.setEventDate(channelHistory.getTransactionTime());
		historyView.setCreatedDate(
				DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT));
		historyView.setAmount(Double.parseDouble(channelHistory.getAmount()));
		historyView.setStatus(channelHistory.getStatus());
		historyView.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.MARKED_AS_PAID_DEEP_LINK));
		historyView.setPlanDetail(channelHistory.getDisplayValues());
		historyView.setEventDate(channelHistory.getTransactionTime());
		historyView.setCircle(channelHistory.getCircle());
		historyView.setOperator(channelHistory.getOperator());
		historyView.setPayType(channelHistory.getPaytype());
		historyView.setEventType(channelHistory.getEventType());
		historyView.setBillDate(this.getBillDate(channelHistory));
		historyView.setDueDate(this.getDueDate(channelHistory));
		
		// setting all header,display values based on themeTypes not on eventTypes
		historyView.setHeadings(headersViewManager.getViewElements(historyView));
		historyView.setDisplayValues(displayValuesManager.getViewElements(historyView));
		return historyView;
	}
	private String getBillDate(ChannelHistory channelHistory) {
		Map<String, String> billsObj = channelHistory.getBillsObj();
		if (billsObj != null) {
			return billsObj.get(Constants.ReminderConstants.BILL_DATE);
		}

		return null;
	}
	private String getDueDate(ChannelHistory channelHistory) {
		Map<String, String> billsObj = channelHistory.getBillsObj();
		if (billsObj != null) {
			return billsObj.get(Constants.ReminderConstants.DUE_DATE);
		}

		return null;
	}
}
