package com.paytm.saga.service.aggregator.cardmanager.impl;

import com.paytm.saga.common.constant.*;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.enums.Service;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.aggregator.cardmanager.CardManager;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.ChannelHistoryUtils;
import com.paytm.saga.util.DateUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.UtilityThemeTypes.*;

@Component("utilityReminderCard")
public class UtilityReminderCard implements CardManager {
    private static final Logger logger = LogManager.getLogger(UtilityReminderCard.class);

    private ViewManager ctaManager;
    private ViewManager displayValuesManager;
    private ViewManager headersViewManager;

    private ViewManager childCtaManager;


    @Autowired
    public UtilityReminderCard(final @NotNull @Qualifier("ctaViewManager") ViewManager ctaManager,
                               final @NotNull @Qualifier("childCtaViewManager") ViewManager childCtaManager,
                               final @NotNull @Qualifier("displayValuesViewManager") ViewManager displayValuesManager,
                               final @NotNull @Qualifier("headersViewManager") ViewManager headersViewManager) {
        this.ctaManager = ctaManager;
        this.displayValuesManager = displayValuesManager;
        this.headersViewManager = headersViewManager;
        this.childCtaManager = childCtaManager;
    }

    @Override
    public HistoryView getCardInfo(CardInfoDto cardInfoDto) {
        HistoryView historyView = new HistoryView();
        historyView.setUtility(Boolean.TRUE);
        historyView.setNative(cardInfoDto.isNative());
        logger.info("Inside utimilityremindercard.getCardInfo");
        if(Objects.nonNull(cardInfoDto.getChannelHistory()) && Objects.nonNull(cardInfoDto.getChannelHistory().getBillsObj())){
            logger.info("setting pg resp code and is retry exhausted in histry view");
            historyView.setPgRespCode(cardInfoDto.getChannelHistory().getBillsObj().get("pg_resp_code"));
            historyView.setRetryExhausted(Boolean.parseBoolean(cardInfoDto.getChannelHistory().getBillsObj().get("is_retry_exhausted")));
            logger.info("setting pg resp code and is retry exhausted in histry view,={} and {}",historyView.getPgRespCode(),historyView.isRetryExhausted());
        }
        historyView.setService(cardInfoDto.getReminderHistory().getService());
        historyView.setOperator(cardInfoDto.getReminderHistory().getOperator());
        historyView.setAmount(cardInfoDto.getReminderHistory().getAmount());
        if (cardInfoDto.getReminderHistory().getBill_date() != null)
            historyView.setBillDate(DateUtil.dateFormatter(cardInfoDto.getReminderHistory().getBill_date(),
                    DateFormats.DATE_TIME_FORMAT_2));
        if (cardInfoDto.getReminderHistory().getDue_date() != null)
            historyView.setDueDate(DateUtil.dateFormatter(cardInfoDto.getReminderHistory().getDue_date(),
                    DateFormats.DATE_TIME_FORMAT_2));
        historyView.setEventType(EventTypes.BILL);
        historyView.setAutomaticDate(cardInfoDto.getReminderHistory().getAutomaticData());
        historyView.setEventDate(new Date());
        historyView.setBillState(this.getBillState(cardInfoDto.getReminderHistory()));
        historyView.setPreviousTxnHistory(cardInfoDto.isPreviousTxnHistory());
        historyView.setMinimumDue(cardInfoDto.getReminderHistory().getCurrentMinBillAmount() != null ? cardInfoDto.getReminderHistory().getCurrentMinBillAmount().doubleValue()  : 0.0);
        historyView.setThemeType(this.getThemeType(historyView));
        ChannelHistoryUtils.updateHeadersAndFooters(historyView, headersViewManager.getViewElements(historyView));
        historyView.setDisplayValues(displayValuesManager.getViewElements(historyView));
        historyView.setCta(ctaManager.getViewElements(historyView));
        historyView.setChildCta(childCtaManager.getViewElements(historyView));
        historyView.setCreatedDate(
                DateUtil.dateFormatter(new Date(), DateFormats.DATE_TIME_FORMAT));

        try {
            if (historyView.getDueDate() != null) {
                historyView.setDueDate(DateUtil.dateFormatter(
                        DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
                        DateFormats.DATE_FORMAT_2));
            }

            if (historyView.getBillDate() != null) {
                historyView.setBillDate(DateUtil.dateFormatter(
                        DateUtil.stringToDate(historyView.getBillDate(), DateFormats.DATE_TIME_FORMAT_2),
                        DateFormats.DATE_FORMAT_2));
            }
        } catch (RuntimeException e) {
            logger.error("Getting exception in parsing billdate duedate in recharge card", e);
        }
        return historyView;
    }

    private int getBillState(ReminderHistory reminderHistory) {
        Date dueDate = reminderHistory.getDue_date();
        try {
            if (dueDate != null
                    && DateUtil.timestampToDate(new Date()).after(DateUtil.timestampToDate(dueDate))) {
                // Bill Overdue
                return BillStateTypes.BILL_OVERDUE;
            } else if (dueDate != null) {
                // New Bill
                return BillStateTypes.ACTIVE_BILL;
            }
        } catch (ParseException e) {
            logger.error("error in parsing date", e);
        }
        return 0;
    }

    private String getThemeType(HistoryView historyView) {
        logger.info("Inside getThemeType of Utilityremindercard");
        if (historyView.getAutomaticDate() != null && DateUtil.compareDateWithoutTime(historyView.getAutomaticDate(), new Date())>=0) {
            logger.info("Condition for AUTOMATIC_PAYMENT_UNDER_RETRY ={}",(!historyView.isRetryExhausted() && Constants.PG_RESP_CODES_FOR_INSUFFICIENT_BALANCE_FAILURE.contains(historyView.getPgRespCode())));
           if(!historyView.isRetryExhausted() && Constants.PG_RESP_CODES_FOR_INSUFFICIENT_BALANCE_FAILURE.contains(historyView.getPgRespCode())) {
               logger.info("returning  AUTOMATIC_PAYMENT_UNDER_RETRY themetype");
               return AUTOMATIC_PAYMENT_UNDER_RETRY;
           }
           logger.info("Returning AUTOMATIC_PAYMENT themetyoe");
            return AUTOMATIC_PAYMENT;
        } else if (historyView.getBillState() == BillStateTypes.ACTIVE_BILL) {

            if((!historyView.isNative())
                    && ((DateUtil.compareDateWithoutTime(DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2), new Date()) == 0) ||
                    DateUtil.compareDateWithoutTime(DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2), new Date (System.currentTimeMillis() + 86400000)) == 0)){

                return Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyView.getService()) ? CREDIT_CARD_BILL_DUE_TODAY_OR_TOMORROW : BILL_DUE_TODAY_OR_TOMORROW;

            }
            else return Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyView.getService()) ? CREDIT_CARD_BILL_DUE : BILL_DUE;
        } else if (historyView.getBillState() == BillStateTypes.BILL_OVERDUE) {
            return Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyView.getService()) ? CREDIT_CARD_BILL_OVERDUE : BILL_OVERDUE;
        } else if (historyView.getBillState() == BillStateTypes.BILL_AFTER_OVERDUE) {
            return Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyView.getService()) ? CREDIT_CARD_NOT_PAID_ON_PAYTM : BILL_NOT_PAID_PAYTM;
        }
        return null;
    }


}
