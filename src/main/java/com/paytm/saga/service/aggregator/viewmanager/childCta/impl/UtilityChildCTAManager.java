package com.paytm.saga.service.aggregator.viewmanager.childCta.impl;

import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.CTAManager;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ChildCTAManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.paytm.saga.common.constant.UtilityThemeTypes.*;

@Service("utilityChildCTAManager")
public class UtilityChildCTAManager implements ChildCTAManager {
    private static final Logger logger = LogManager.getLogger(UtilityChildCTAManager.class);

    @Override
    public List<ViewElementInfo> getChildCTAs(HistoryView historyView) {

        List<ViewElementInfo> response = null;
        String themeType = historyView.getThemeType();
        switch (themeType) {
            case CREDIT_CARD_BILL_DUE:
            case CREDIT_CARD_BILL_DUE_TODAY_OR_TOMORROW:
            case BILL_DUE:
            case BILL_OVERDUE:
            case CREDIT_CARD_BILL_OVERDUE:
            case BILL_DUE_TODAY_OR_TOMORROW:
            case AUTOMATIC_PAYMENT:
            case AUTOMATIC_PAYMENT_UNDER_RETRY:
                response = getReminderChildCTAs(historyView);
                break;
            default:
                break;
        }

        logger.debug("ChildCTAs for  card type  {} is {} ", themeType, response);

        return response;
    }

    private List<ViewElementInfo> getReminderChildCTAs(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo childCta1 = new ViewElementInfo();
        childCta1.setValue("Mark as Paid");
        childCta1.setType("childCta1");
        childCta1.setActionType("mark_as_paid");
        response.add(childCta1);

        ViewElementInfo childcta2 = new ViewElementInfo();
        childcta2.setValue("View Bill Details");
        childcta2.setType("childCta2");
        childcta2.setActionType("view_bill_details");
        response.add(childcta2);

        return response;
    }


}

