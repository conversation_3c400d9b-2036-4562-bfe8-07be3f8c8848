package com.paytm.saga.service.aggregator.viewmanager;

import java.util.ArrayList;
import java.util.List;

import com.paytm.saga.common.constant.Constants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;

public class CTAHandler {

	private static final Logger logger = LogManager.getLogger(CTAHandler.class);

	public static List<ViewElementInfo> getCTAs(HistoryView historyView) {
		List<ViewElementInfo> response = null;
		String eventType = historyView.getThemeType();
		logger.debug("adding CTA for cardType " + eventType);
		switch (eventType) {
		case CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME:
			response = getSuggestedRechargeCTAs(historyView.isInvalidPlan());
			break;
		case CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME:
			response = getSuccessRechargeCTAs();
			break;
		case CardThemeTypes.FAILURE_RECHARGE_CARD_THEME:
			response = getFailureRechargeCTAs();
			break;
		case CardThemeTypes.PENDING_RECHARGE_CARD_THEME:
			response = getPendingRechargeCTAs();
			break;
		case CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME:
			response = getCancelledRechargeCTAs();
			break;
		default:
			break;
		}
		return response;
	}

	private static List<ViewElementInfo> getSuccessRechargeCTAs() {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		cta.setValue("Repeat");
		cta.setType("cta1");
		cta.setActionType("repeat_recharge");
		cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME));
		response.add(cta);
		return response;
	}

	private static List<ViewElementInfo> getFailureRechargeCTAs() {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		cta.setValue("Retry");
		cta.setType("cta1");
		cta.setActionType("retry_recharge");
		cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.FAILURE_RECHARGE_CARD_THEME));
		response.add(cta);
		return response;
	}

	private static List<ViewElementInfo> getPendingRechargeCTAs() {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();

		if(Constants.CommonConstants.ENABLE_PENDING_CTA.equals(true)){
			ViewElementInfo cta = new ViewElementInfo();
			cta.setValue("Cancel");
			cta.setType("cta1");
			cta.setActionType("pos_cancel");
			cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.PENDING_RECHARGE_CARD_THEME));
			response.add(cta);
		}
		return response;
	}

	private static List<ViewElementInfo> getCancelledRechargeCTAs() {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		cta.setValue("Retry");
		cta.setType("cta1");
		cta.setActionType("retry_recharge");
		cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME));
		response.add(cta);
		return response;
	}

	private static List<ViewElementInfo> getSuggestedRechargeCTAs(boolean invalidPlan) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		if (invalidPlan) {
			ViewElementInfo cta = new ViewElementInfo();
			cta.setValue("Select another plan");
			cta.setType("cta1");
			cta.setActionType("select_another_plan");
			cta.setDeepLink(
					DeepLinkManager.getDeeplinkByType(CardThemeTypes.SUGGESTED_RECHARGE_INVALID_PLAN_CARD_THEME));
			response.add(cta);
		} else {
			ViewElementInfo cta = new ViewElementInfo();
			cta.setValue("Recharge");
			cta.setType("cta1");
			cta.setActionType("suggested_recharge");
			cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME));
			response.add(cta);
			ViewElementInfo cta2 = new ViewElementInfo();
			cta2.setValue("Recharged Already");
			cta2.setType("cta2");
			cta2.setActionType("mark_as_paid");
			cta2.setDeepLink(
					DeepLinkManager.getDeeplinkByType(CardThemeTypes.SUGGESTED_RECHARGE_RECHARGED_ALREADY_CARD_THEME));
			response.add(cta2);

		}
		return response;
	}
}
