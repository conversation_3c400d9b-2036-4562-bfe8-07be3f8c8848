package com.paytm.saga.service.aggregator.cardmanager.impl;

import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.common.constant.UtilityThemeTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.GetHistoryPageDTO;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.service.aggregator.cardmanager.CardManager;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotNull;
import java.util.Date;

@Component("staticCard")
public class StaticCard implements CardManager {
    private static final Logger logger = LogManager.getLogger(StaticCard.class);

    private ViewManager ctaManager;
    private ViewManager displayValuesManager;
    private ViewManager headersViewManager;


    @Autowired
    public StaticCard(final @NotNull @Qualifier("ctaViewManager") ViewManager ctaManager,
                      final @NotNull @Qualifier("displayValuesViewManager") ViewManager displayValuesManager,
                      final @NotNull @Qualifier("headersViewManager") ViewManager headersViewManager) {
        this.ctaManager = ctaManager;
        this.displayValuesManager = displayValuesManager;
        this.headersViewManager = headersViewManager;
    }

    @Override
    public HistoryView getCardInfo(CardInfoDto cardInfoDto) {
        HistoryView historyView = new HistoryView();
        historyView.setUtility(Boolean.TRUE);
        historyView.setNative(cardInfoDto.isNative());
        GetHistoryPageDTO historyPageRequest = cardInfoDto.getHistoryPageRequest();
        if (historyPageRequest != null) {
            historyView.setService(historyPageRequest.getService());
            historyView.setOperator(historyPageRequest.getOperator());
            historyView.setPayType(historyPageRequest.getPayType());
        }
        if (StringUtils.isNotEmpty(cardInfoDto.getEventType())) {
            historyView.setEventType(cardInfoDto.getEventType());
        } else {
            historyView.setEventType(EventTypes.RECHARGE);
        }
        if (StringUtils.isNotEmpty(cardInfoDto.getPreviousOperator())) {
            historyView.setPreviousOperator(cardInfoDto.getPreviousOperator());
        }
        if (StringUtils.isNotEmpty(cardInfoDto.getPreviousPayType())) {
            historyView.setPreviousPayType(cardInfoDto.getPreviousPayType());
        }
        historyView.setEventDate(new Date());
        historyView.setPreviousTxnHistory(cardInfoDto.isPreviousTxnHistory());
        if(cardInfoDto.getThemeType()!=null){
            historyView.setThemeType(cardInfoDto.getThemeType());
        }else {
            historyView.setThemeType(UtilityThemeTypes.SUGGESTED_RECHARGE);
        }
        historyView.setMnpType(cardInfoDto.getMnpType());
        historyView.setHeadings(headersViewManager.getViewElements(historyView));
        historyView.setCta(ctaManager.getViewElements(historyView));
        historyView.setCreatedDate(
                DateUtil.dateFormatter(new Date(), DateFormats.DATE_TIME_FORMAT));

        return historyView;
    }

}
