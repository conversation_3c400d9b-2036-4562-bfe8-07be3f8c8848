package com.paytm.saga.service.aggregator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.paytm.saga.common.constant.*;
import com.paytm.saga.util.ChannelHistoryUtils;
import com.paytm.saga.util.TTLUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.ChannelDetail;
import com.paytm.saga.dto.ChannelHistoryPage;
import com.paytm.saga.dto.FilteredEvents;
import com.paytm.saga.dto.HistoryPage;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.service.ChannelHistoryFinalizedService;
import com.paytm.saga.service.ChannelHistoryService;
import com.paytm.saga.service.DropOffService;
import com.paytm.saga.service.aggregator.cardmanager.CardManager;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.service.aggregator.cardmanager.DateCard;
import com.paytm.saga.service.aggregator.cardmanager.MarkedAsPaidCard;
import com.paytm.saga.service.aggregator.cardmanager.MnpCard;
import com.paytm.saga.service.aggregator.cardmanager.OperatorChangeCard;
import com.paytm.saga.service.aggregator.cardmanager.PlanExpiry;
import com.paytm.saga.service.aggregator.cardmanager.ScratchCard;
import com.paytm.saga.util.DateUtil;

@Component("mobileAggregatorService")
public class AggregatorService {

	private static final Logger logger = LogManager.getLogger(AggregatorService.class);

	private final ChannelHistoryFinalizedService channelHistoryFinalizedService;
	private final ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
	private final DateCard dateCardService;
	private final MnpCard mnpCardService;
	private final ScratchCard scratchCardService;
	private final CardManager rechargeCardService;
	private final CardManager dropoffCard;
	private final MarkedAsPaidCard markedAsPaidCardService;
	private final PlanExpiry planExpiry;
	private final ChannelHistoryService channelHistoryService;
	private final DropOffService dropOffService;
	private final OperatorChangeCard operatorChangeCardService;


	@Autowired
	public AggregatorService(final ChannelHistoryFinalizedService channelHistoryFinalizedService,
							 final ChannelHistoryService channelHistoryService,
							 final ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil, final DateCard dateCard,
							 final MnpCard mnpCard, final PlanExpiry planExpiry, @Qualifier("rechargeCard") final CardManager rechargeCardService,
							 @Qualifier("dropOffCard")  final CardManager dropoffCard,final ScratchCard scratchCardService
			, final MarkedAsPaidCard markedAsPaidCardService, final DropOffService dropOffService,final OperatorChangeCard operatorChangeCardService) {
		this.channelHistoryFinalizedService = channelHistoryFinalizedService;
		this.channelHistoryFinalizedUtil = channelHistoryFinalizedUtil;
		this.dateCardService = dateCard;
		this.mnpCardService = mnpCard;
		this.planExpiry = planExpiry;
		this.rechargeCardService = rechargeCardService;
		this.scratchCardService = scratchCardService;
		this.markedAsPaidCardService = markedAsPaidCardService;
		this.channelHistoryService = channelHistoryService;
		this.dropoffCard = dropoffCard;
		this.dropOffService = dropOffService;
		this.operatorChangeCardService = operatorChangeCardService;
	}

	// currentCardOperator="operatro:circle:paytype"//input operator info
	// previousCardOperator="operatro:circle:paytype"
	// previousDate=date of last card from previous page

	public HistoryPage aggregateHistoryInfo(List<ChannelHistory> channelHistories, final long customerId,
			final String rechargeNumber, final String service, String pagingState, String currentCardOperator,
			String previousCardOperator, Date previousDate, String client, String version) {
		logger.trace("aggregating history data for customerId " + customerId, " rechargeNumber " + rechargeNumber);
		return this.formatTheHistoryData(channelHistories, customerId, rechargeNumber, service, pagingState,
				previousDate, currentCardOperator, previousCardOperator,client, version);
	}

	private List<HistoryView> addDateCard(Date eventDate, Date previousDate, boolean isLastCard,
			List<HistoryView> historyViews) {
		HistoryView dateCard = dateCardService.addDateInfoCard(eventDate, previousDate, isLastCard);
		if (dateCard != null) {
			historyViews.add(dateCard);
		}
		return historyViews;
	}

	private Date getPreviousEventDate(List<HistoryView> historyViews, Date previousPageDate) {
		int count = historyViews.size();
		if (count > 0) {
			HistoryView historyView = historyViews.get(count - 1);
			return historyView.getEventDate();
		} else {
			return previousPageDate;
		}
	}
	
	private boolean isVIoperator(String operator) {
		String vodaOperators[] = { "vodafone idea", "vodafone", "idea" };
		return Arrays.asList(vodaOperators).contains(operator);
	}

	private List<HistoryView> addSuggestedRechargeCard(List<HistoryView> historyViews, String rechargeNumber,
			String requestedOperator, Date previousPageDate, List<ChannelHistory> channelHistories) {
		logger.trace("adding suggestion card for rechargeNumber " + rechargeNumber);

		try {
			HistoryView suggestion = planExpiry.getRechargeSuggestionCard(rechargeNumber, requestedOperator, channelHistories);
			boolean isLastCard = false;
			if (suggestion != null) {
				Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
				historyViews.add(suggestion);
				// add date
				historyViews = this.addDateCard(suggestion.getEventDate(), previousDate, isLastCard, historyViews);
			}
		} catch (Exception e) {
			logger.error("Getting exception in creating scratch card error msg " + e.getMessage() ,e);
		}
		return historyViews;
	}

	private List<HistoryView> addMnpCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
			String previousCardOperator, String currentCardOperator, Date previousPageDate) {
		logger.trace("adding mnp card for rechargeNumber " + channelHistory.getRechargeNumber());
		HistoryView mnpCard = mnpCardService.addMnpCard(previousCardOperator, currentCardOperator, previousPageDate);
		boolean isLastCard = false;
		if (mnpCard != null) {
			Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
			mnpCard.setEventDate(previousDate);
			// add mnp card
			historyViews.add(mnpCard);
//			// add date
//			historyViews = this.addDateCard(channelHistory.getTransactionTime(), previousDate, isLastCard,
//					historyViews);
		}
		return historyViews;
	}

	private List<HistoryView> addChangeOperatorCard(List<ChannelHistory> channelHistories, List<HistoryView> historyViews,
			String requestedOperator, Date previousPageDate) {
		
		if(historyViews.size() > 0) {
			for(HistoryView historyView: historyViews) {
				if(historyView.getEventType() == CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME) {
					return historyViews;
				}
			}
		}
		
		String previousOperator = channelHistories.get(0).getOperator();
		if(previousOperator != null && 
				(!requestedOperator.equalsIgnoreCase(previousOperator)) && 
				(!(this.isVIoperator(requestedOperator) && this.isVIoperator(previousOperator)))) {
			logger.trace("Adding change Operator for rechargeNumber " + channelHistories.get(0).getRechargeNumber());
			HistoryView operatorChangeCard = operatorChangeCardService.addOperatorChangeCard(previousOperator, requestedOperator, previousPageDate);
			if (operatorChangeCard != null) {
				Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
				operatorChangeCard.setEventDate(previousDate);
				historyViews.add(operatorChangeCard);
				return historyViews;
			}
		} else {
			return historyViews;
		}
		
		return historyViews;
	}

	private List<HistoryView> addRechargeCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
			String requestedOperator, Date previousPageDate) {
		logger.trace("adding recharge card");
		boolean hideCTA = false;
		boolean isLastCard = false;
		// hiding CTA if current(channelHistory) recharge card operator is different
		// from requested operator
		if (!requestedOperator.equalsIgnoreCase(channelHistory.getOperator())) {
			logger.trace("hiding CTA requestedOperator: " + requestedOperator + " card operator: "
					+ channelHistory.getOperator());
			hideCTA = true;
		}
		Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
		historyViews = this.addDateCard(channelHistory.getTransactionTime(), previousDate, isLastCard, historyViews);
		CardInfoDto cardInfoDto=new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(hideCTA);
		
		HistoryView rechargeCard = rechargeCardService.getCardInfo(cardInfoDto);

		if (rechargeCard.getStatus().equals("SUCCESS"))
			historyViews = this.addScratchCard(rechargeCard, historyViews, rechargeCard.getEventDate());
		historyViews.add(rechargeCard);
		return historyViews;
	}

	private List<HistoryView> addMarkedAsPaidCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
			String requestedOperator, String currentCardOperator, String previousCardOperator, Date previousPageDate) {
		logger.trace("adding marked as paid card");
		boolean isLastCard = false;
		Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
		historyViews = this.addDateCard(channelHistory.getTransactionTime(), previousDate, isLastCard, historyViews);
		HistoryView markedAsPaidCard = markedAsPaidCardService.getMarkedAsPaidCard(channelHistory);
		historyViews.add(markedAsPaidCard);
		return historyViews;
	}

	private List<HistoryView> addScratchCard(HistoryView historyView, List<HistoryView> historyViews,
			Date rechargeDate) {
		logger.trace("adding scratch card for order id " + historyView.getOrderId());
		boolean isLastCard = false;
		List<HistoryView> scratchCards = scratchCardService.getScratchCard(historyView.getOrderId());
		for (HistoryView scratchCard : scratchCards) {
			scratchCard.setEventDate(rechargeDate);
//			Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
//			historyViews = this.addDateCard(scratchCard.getEventDate(), previousDate, isLastCard, historyViews);
			historyViews.add(scratchCard);
		}

		return historyViews;
	}
	public List<HistoryView> addDropOffCard(DropOff dropOff, List<HistoryView> historyViews, String requestedOperator,
			Date previousPageDate,String client, String version) {
		logger.trace("adding drop card");
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setDropOff(dropOff);
		cardInfoDto.setClient(client);
		cardInfoDto.setVersion(version);
		HistoryView dropCardInfo = dropoffCard.getCardInfo(cardInfoDto);
		if (dropCardInfo != null) {
			Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
			historyViews.add(dropCardInfo);
			historyViews = this.addDateCard(dropCardInfo.getEventDate(), previousDate, false, historyViews);
		}
		return historyViews;
	}

	private List<HistoryView> handleNewUserSuggestionCard(List<HistoryView> historyViews){
		
		for(int historyViewIndex = 0; historyViewIndex < historyViews.size(); historyViewIndex++) {
			HistoryView historyView = historyViews.get(historyViewIndex);
			
			if(historyView.getEventType() == CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME) {
				if(historyView.isInvalidPlan() == true) {
					historyViews.remove(historyViewIndex);
					historyViewIndex = historyViewIndex - 1;
				} else {
					historyView.setPlanExpiryDate("");
					List<ViewElementInfo> ctas = historyView.getCta();
					if(ctas != null) {
						for(int ctaIndex = 0; ctaIndex < ctas.size(); ctaIndex++) {
							if(ctas.get(ctaIndex).getType() == "cta2") {
								ctas.remove(ctaIndex);
								ctaIndex = ctaIndex - 1;
							}
						}
						historyView.setCta(ctas);
					}
					List<ViewElementInfo> headers = historyView.getHeadings();
					if(headers != null) {
						for(int headerIndex = 0; headerIndex < headers.size(); headerIndex++) {
							if(headers.get(headerIndex).getType() == "header3") {
								headers.remove(headerIndex);
								headerIndex = headerIndex - 1;
							}
						}
						historyView.setHeadings(headers);
					}
				}
			}
			
		}
		
		return historyViews;
		
	}
	private HistoryPage formatTheHistoryData(List<ChannelHistory> channelHistories, final long customerId,
			final String rechargeNumber, final String service, String pagingState, Date previousPageDate,
			String requestedOperator, String previousCardOperator,String client, String version) {
		List<HistoryView> historyViews = new ArrayList<>();
		List<ChannelHistory> events = new ArrayList<>();
		List<ChannelHistory> markFinalizedEvents = null;
		List<ChannelHistory> moveToFinalizedTxns = null;
		Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
		if (pagingState == null) {
			try {
				historyViews = this.addSuggestedRechargeCard(historyViews, rechargeNumber, requestedOperator,
						previousPageDate, channelHistories);
				if(historyViews.isEmpty()==true){
					DropOff dropOff = dropOffService.getDropOff(customerId, rechargeNumber, service, requestedOperator.split(":")[0]);
					if (dropOff != null){
						this.addDropOffCard(dropOff, historyViews, requestedOperator.split(":")[0], previousPageDate,client, version);
					}
				}
			}	catch(Exception e) {
				logger.error("getting error in dropoff service",e);
			}
//			// recharge_suggession events
			// historyViews = this.addSuggestedRechargeCard(historyViews, rechargeNumber, requestedOperator,
					// previousPageDate, channelHistories);
			FilteredEvents filteredEvents = this.filterOutLatestStateOfOrders(channelHistories, finalizationDate);
			events.addAll(filteredEvents.getEvents());
			markFinalizedEvents = filteredEvents.getMarkFinaliseData();
			moveToFinalizedTxns = filteredEvents.getMoveToFinaliseData();
		}
		requestedOperator = requestedOperator.split(":")[0];// Getting only operator
		ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId,
				rechargeNumber, service, events.size(), pagingState);
		events.addAll(channelHistoryPage.getChannelHistories());
		if(events.size() < 1) {
			historyViews = this.handleNewUserSuggestionCard(historyViews);	
		}
		if(pagingState == null && events.size() > 0 && requestedOperator != null) { 
			historyViews = this.addChangeOperatorCard(events, historyViews, requestedOperator, previousPageDate);
		}
		for (ChannelHistory channelHistory : events) {
			if (channelHistory.getEventType().equals(EventTypes.MARKED_AS_PAID)) {
				historyViews = this.addMarkedAsPaidCard(channelHistory, historyViews, requestedOperator,
						channelHistory.getOperator(), previousCardOperator, previousPageDate);
			} else {
//				currentCardOperator = channelHistory.getOperator() + ":" + channelHistory.getCircle() + ":"
//						+ channelHistory.getService();
				if (previousCardOperator != null) {
					this.addMnpCard(channelHistory, historyViews, previousCardOperator, channelHistory.getOperator(),
							previousPageDate);
					
				}
				historyViews = this.addRechargeCard(channelHistory, historyViews, requestedOperator, previousPageDate);
				previousCardOperator = channelHistory.getOperator();
			}

		}
		Date previousEventDate = this.getPreviousEventDate(historyViews, previousPageDate);
		if ((!historyViews.isEmpty() || pagingState != null) && (!channelHistoryPage.isAvailableNext()))
			historyViews = this.addDateCard((previousEventDate == null ? new Date() : previousEventDate),
					previousEventDate, true, historyViews);

		markAsFinalized(moveToFinalizedTxns, markFinalizedEvents, service);
		ChannelDetail channelDetail = new ChannelDetail();
		channelDetail.setCustomerId(customerId);
		channelDetail.setOperator(requestedOperator);
		channelDetail.setRechargeNumber(rechargeNumber);
		channelDetail.setService(service);
		HistoryPage historyPage = new HistoryPage(historyViews, historyViews.size(), channelHistoryPage.getLastCardId(),
				channelHistoryPage.isAvailableNext(), "SUCCESS", 200, channelDetail);
		String dedupeDate = "";
		if (previousEventDate != null) {
			dedupeDate = DateUtil.dateFormatter(previousEventDate, DateFormats.DATE_FORMAT);
		} else {
			dedupeDate = DateUtil.dateFormatter(new Date(), DateFormats.DATE_FORMAT);
		}
		String dedupeIds[] = { dedupeDate, previousCardOperator !=null ? previousCardOperator:requestedOperator };
		historyPage.setDedupeids(dedupeIds);
		return historyPage;
	}

	public FilteredEvents filterOutLatestStateOfOrders(List<ChannelHistory> channelHistories, Date finalizationDate) {
		FilteredEvents filteredEvents = new FilteredEvents();
		Long previousRechargeOrderId = null;
		List<ChannelHistory> filteredData = new ArrayList<>();// Data to show on chat screen
		List<ChannelHistory> markFinalizeData = new ArrayList<>();// data to mark as finalized n history
																				// table
		List<ChannelHistory> moveToFinalizeData = new ArrayList<>();// data to move from history to
																					// finalized
		for (ChannelHistory channelHistory : channelHistories) {
			if (channelHistory.getEventType().equals(EventTypes.RECHARGE)) {
				Long orderId = channelHistory.getOrderId();
				if (channelHistory.getPaytype().equals("prepaid")
						&& !orderId.equals(previousRechargeOrderId)) {
					filteredData.add(channelHistory);
					if (ChannelHistoryUtils.isFinalizedState(channelHistory,finalizationDate)) {
						channelHistory.setFinalisedState(true);
						moveToFinalizeData.add(channelHistory);
						markFinalizeData.add(channelHistory);
					}
				} else {
					logger.trace("ignoreing event");
				}
				previousRechargeOrderId = orderId;
			} else if (StringUtils.equalsAnyIgnoreCase(channelHistory.getEventType(), EventTypes.MARKED_AS_PAID, Constants.CommonConstants.NOT_PAID_ON_PAYTM_MESSAGE_TYPE)) {
				filteredData.add(channelHistory);

				if (ChannelHistoryUtils.isFinalizedState(channelHistory,finalizationDate) && channelHistory.getPaytype().equals("prepaid")) {
						channelHistory.setFinalisedState(true);
						markFinalizeData.add(channelHistory);
						moveToFinalizeData.add(channelHistory);

				}
			}
			if (ChannelHistoryUtils.isFinalizedState(channelHistory,finalizationDate)) {
				channelHistory.setFinalisedState(true);
				markFinalizeData.add(channelHistory);
			}
		}
		filteredEvents.setEvents(filteredData);
		filteredEvents.setMarkFinaliseData(markFinalizeData);
		filteredEvents.setMoveToFinaliseData(moveToFinalizeData);
		logger.trace("filteredData size" + filteredData.size());
		return filteredEvents;
	}

	private void markAsFinalized(List<ChannelHistory> moveToFinalizedEvents, List<ChannelHistory> markFinalizedEvents, String service) {
		if (moveToFinalizedEvents != null && moveToFinalizedEvents.size() > 0) {
			logger.trace("marking the finalized to orders count ", moveToFinalizedEvents.size());
			List<ChannelHistoryFinalized> channelHistoryFinalized = channelHistoryFinalizedUtil
					.convertHistoryToFinalizedHistory(moveToFinalizedEvents);
			channelHistoryFinalizedService.saveAll(channelHistoryFinalized,service);
			channelHistoryService.saveAll(markFinalizedEvents,service);

		}
	}

}
