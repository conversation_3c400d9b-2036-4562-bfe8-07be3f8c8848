package com.paytm.saga.service.aggregator.viewmanager.impl;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.enums.Service;
import com.paytm.saga.service.aggregator.viewmanager.declaration.CTAManager;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ctaViewManager")
public class CTAViewManager implements ViewManager {
    @Autowired
    @Qualifier("cylinderCTAManager")
    private CTAManager cylinderCTAManager;
    @Autowired
    @Qualifier("mobileCTAManager")
    private CTAManager mobileCTAManager;
    @Autowired
    @Qualifier("electricityCTAManager")
    private CTAManager electricityCTAManager;

    @Autowired
    @Qualifier("utilityCTAManager")
    private CTAManager utilityCTAManager;

    @Autowired
    @Qualifier("creditCardCTAManager")
    private CTAManager creditCardCTAManager;

    @Override
    public List<ViewElementInfo> getViewElements(HistoryView historyView) {
        if (historyView.isUtility() && Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyView.getService())) {
            return creditCardCTAManager.getCTAs(historyView);
        } else if (historyView.isUtility()) {
            return utilityCTAManager.getCTAs(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.CYLINDER)) {
            return cylinderCTAManager.getCTAs(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.MOBILE)) {
            return mobileCTAManager.getCTAs(historyView);
        } else if (historyView.getService().equals(Constants.ServiceTypeConstants.ELECTRICITY)) {
            return electricityCTAManager.getCTAs(historyView);
        }
        return null;

    }

}
