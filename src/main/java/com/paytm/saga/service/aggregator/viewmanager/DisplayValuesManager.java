package com.paytm.saga.service.aggregator.viewmanager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;

public class DisplayValuesManager {

	private static final Logger logger = LogManager.getLogger(DisplayValuesManager.class);
	private static final Map<String, String> keysMap = new HashMap<String, String>();
	static {
		keysMap.put("data", "Data");
		keysMap.put("sms", "SMS");
		keysMap.put("talktime", "Talktime");
		keysMap.put("validity", "Validity");
	}

	public static List<ViewElementInfo> getDisplayValues(HistoryView historyView) {
		List<ViewElementInfo> response = null;
		String themeType = historyView.getThemeType();
		logger.debug("Adding display values for card type " + themeType);
		switch (themeType) {
		case CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME:
			response = getCardDisplayValues(historyView);
			break;
		case CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME:
			response = getCardDisplayValues(historyView);
			break;
		case CardThemeTypes.FAILURE_RECHARGE_CARD_THEME:
			response = getCardDisplayValues(historyView);
			break;
		case CardThemeTypes.PENDING_RECHARGE_CARD_THEME:
			response = getCardDisplayValues(historyView);
			break;
		case CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME:
			response = getCardDisplayValues(historyView);
			break;
		case CardThemeTypes.MARKED_AS_PAID_CARD_THEME:
			response = getCardDisplayValues(historyView);
			break;
		default:
			break;
		}
		return response;
	}

	private static ViewElementInfo getDataFromMap(Map<String, String> map, String key, int index) {
		if (map.get(key) != null && !map.get(key).isEmpty() && !map.get(key).equals("NA")
				&& !map.get(key).equals("N/A")) {
			if (key.equals("talktime")) {
				return addDisplayValue(getDisplayValueKeyName(key), "₹" + map.get(key), index);
			} else if (key.equals("addon_benefit1") || key.equals("addon_benefit2")) {
				return addDisplayValue("", map.get(key), index);
			} else {
				return addDisplayValue(getDisplayValueKeyName(key), map.get(key), index);
			}
		}
		return null;
	}

	private static List<ViewElementInfo> getCardDisplayValues(HistoryView historyView) {
		List<ViewElementInfo> viewElementInfos = new ArrayList<ViewElementInfo>();
		if (historyView.getPlanDetail() != null) {
			ViewElementInfo data = getDataFromMap(historyView.getPlanDetail(), "data", 1);
			if (data != null) {
				viewElementInfos.add(data);
			}

			ViewElementInfo validity = getDataFromMap(historyView.getPlanDetail(), "validity", 2);
			if (validity != null) {
				viewElementInfos.add(validity);
			}
			ViewElementInfo talktime = getDataFromMap(historyView.getPlanDetail(), "talktime", 3);
			if (talktime != null) {
				viewElementInfos.add(talktime);
			}
			ViewElementInfo sms = getDataFromMap(historyView.getPlanDetail(), "sms", 4);
			if (sms != null) {
				viewElementInfos.add(sms);
			}
			ViewElementInfo addon_benefit1 = getDataFromMap(historyView.getPlanDetail(), "addon_benefit1", 5);
			if (addon_benefit1 != null) {
				viewElementInfos.add(addon_benefit1);
			}
			ViewElementInfo addon_benefit2 = getDataFromMap(historyView.getPlanDetail(), "addon_benefit2", 6);
			if (addon_benefit2 != null) {
				viewElementInfos.add(addon_benefit2);
			}
			historyView.setPlanDescription(historyView.getPlanDetail().get("description"));
		}
		return viewElementInfos;
	}

	private static String getDisplayValueKeyName(String key) {
		if (keysMap.containsKey(key.toLowerCase())) {
			return keysMap.get(key.toLowerCase());
		} else {
			return key;
		}
	}

	private static ViewElementInfo addDisplayValue(String key, String value, int index) {
		ViewElementInfo viewElementInfo = new ViewElementInfo();
		viewElementInfo.setValue((key + " " + (value)).trim());
		viewElementInfo.setType("ds" + index);
		return viewElementInfo;
	}
}
