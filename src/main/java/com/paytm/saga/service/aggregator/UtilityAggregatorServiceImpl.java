package com.paytm.saga.service.aggregator;

import com.paytm.saga.common.constant.*;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.model.AutomaticData;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.repository.AutomaticDataRepository;
import com.paytm.saga.service.*;
import com.paytm.saga.service.aggregator.cardmanager.*;
import com.paytm.saga.util.ChannelHistoryUtils;
import com.paytm.saga.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service("UtilityAggregatorServiceImpl")
public class UtilityAggregatorServiceImpl extends CommonAggregatorService implements AggregatorServiceInterface {

    private static final Logger logger = LogManager.getLogger(UtilityAggregatorServiceImpl.class);

    private final ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
    private final ChannelHistoryService channelHistoryService;

    private final CardManager utilityReminderCard;

    private final CardManager staticCard;

    private final CardManager utilityTransactionCard;

    private final AutomaticDataRepository automaticDataRepository;
    private final AutomaticCardService automaticCardService;
    private final RecentsRepositoryWrapperService recentsRepository;

    @Autowired
    public UtilityAggregatorServiceImpl(final ChannelHistoryFinalizedService channelHistoryFinalizedService,
                                        final ChannelHistoryService channelHistoryService,
                                        final ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil, final DateCard dateCard,
                                        final MnpCard mnpCard, final PlanExpiry planExpiry,
                                        final ScratchCard scratchCardService,
                                        final MarkedAsPaidCard markedAsPaidCardService, final DropOffService dropOffService,
                                        final ReminderHistoryService reminderHistoryService,
                                        final @Qualifier("utilityReminderCard") CardManager utilityReminderCard,
                                        final @Qualifier("staticCard") CardManager staticCard,
                                        final @Qualifier("utilityTransactionCard") CardManager utilityTransactionCard,
                                        final AutomaticDataRepository automaticDataRepository,final AutomaticCardService automaticCardService, RecentsRepositoryWrapperService recentsRepository) {
        super(channelHistoryFinalizedService, channelHistoryService, channelHistoryFinalizedUtil, dateCard, mnpCard,
                planExpiry, null, null, scratchCardService, markedAsPaidCardService,
                reminderHistoryService, null, null);
        this.channelHistoryFinalizedUtil = channelHistoryFinalizedUtil;
        this.channelHistoryService = channelHistoryService;
        this.utilityReminderCard = utilityReminderCard;
        this.utilityTransactionCard = utilityTransactionCard;
        this.staticCard = staticCard;
        this.automaticDataRepository = automaticDataRepository;
        this.automaticCardService = automaticCardService;
        this.recentsRepository = recentsRepository;
    }

    @Override
    public HistoryPage aggregateHistoryInfo(long customerId, String rechargeNumber, String service, String pagingState, String currentCardOperator, String previousCardOperator, Date previousDate, String client, String version) {
        return null;
    }

    @Override
    public HistoryPage aggregateHistoryInfo(GetHistoryPageDTO historyPageRequest, String previousCardOperator, Date previousDate) {
        logger.trace("aggregating history data for customerId  {} , rechargeNumber {}", historyPageRequest.getCustomerId(), historyPageRequest.getRecharge_number());
        List<ChannelHistory> channelHistories = null;
        Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
        FilteredEvents filteredEvents = new FilteredEvents();
        List<ChannelHistory> events = new ArrayList<>();
        String rechargeNumber = historyPageRequest.getRecharge_number();
        if (com.paytm.saga.enums.Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyPageRequest.getService()))  {
            rechargeNumber = StringUtils.firstNonEmpty(historyPageRequest.getParId(), historyPageRequest.getCin());

        }
        if (historyPageRequest.getPagingState() == null) {
            channelHistories = channelHistoryService.getPageOfHistory(historyPageRequest.getCustomerId(), rechargeNumber, historyPageRequest.getService());
            filteredEvents = this.filterOutLatestStateOfOrders(channelHistories, finalizationDate);
            events.addAll(filteredEvents.getEvents());
        }
        ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(historyPageRequest.getCustomerId(),
                rechargeNumber, historyPageRequest.getService(), events.size(), historyPageRequest.getPagingState());
        channelHistoryPage.setMarkFinaliseData(filteredEvents.getMarkFinaliseData());
        channelHistoryPage.setMoveToFinaliseData(filteredEvents.getMoveToFinaliseData());

        List<ChannelHistory> finalizedEvents = channelHistoryPage.getChannelHistories();
        events.addAll(finalizedEvents);
        channelHistoryPage.setChannelHistories(events);


        HistoryPage historyPage = formatTheHistoryData(channelHistoryPage, historyPageRequest,
                previousDate, previousCardOperator);

        logger.trace("historyPage response is {}", historyPage);

        return historyPage;
    }

    @Override
    public FilteredEvents filterOutLatestStateOfOrders(List<ChannelHistory> channelHistories, Date finalizationDate) {
        FilteredEvents filteredEvents = new FilteredEvents();
        Long previousRechargeOrderId = null;
        List<ChannelHistory> filteredData = new ArrayList<>();// Data to show on chat screen
        List<ChannelHistory> markFinalizeData = new ArrayList<>();// data to mark as finalized n history
        // table
        List<ChannelHistory> moveToFinalizeData = new ArrayList<>();// data to move from history to
        // finalized
        for (ChannelHistory channelHistory : channelHistories) {
            if (channelHistory.getEventType().equals(EventTypes.RECHARGE)) {
                Long orderId = channelHistory.getOrderId();

                if (ChannelHistoryUtils.checkForLatestStateOrders(channelHistory, previousRechargeOrderId) && !ChannelHistoryUtils.checkForPennyDrop(channelHistory)) {
                    filteredData.add(channelHistory);
                    if (ChannelHistoryUtils.isFinalizedState(channelHistory, finalizationDate)) {
                        channelHistory.setFinalisedState(true);
                        moveToFinalizeData.add(channelHistory);
                        markFinalizeData.add(channelHistory);
                    }
                } else {
                    logger.trace("ignoring event service {} ", channelHistory.getService());
                }
                previousRechargeOrderId = orderId;
            } else if (StringUtils.equalsAnyIgnoreCase(channelHistory.getEventType(), EventTypes.MARKED_AS_PAID, Constants.CommonConstants.NOT_PAID_ON_PAYTM_MESSAGE_TYPE)) {
                filteredData.add(channelHistory);

                if (ChannelHistoryUtils.isFinalizedState(channelHistory, finalizationDate)) {
                    channelHistory.setFinalisedState(true);
                    markFinalizeData.add(channelHistory);
                    moveToFinalizeData.add(channelHistory);
                }
            }
            if (ChannelHistoryUtils.isFinalizedState(channelHistory,finalizationDate)){
                channelHistory.setFinalisedState(true);
                markFinalizeData.add(channelHistory);

            }
        }
        filteredEvents.setEvents(filteredData);
        filteredEvents.setMarkFinaliseData(markFinalizeData);
        filteredEvents.setMoveToFinaliseData(moveToFinalizeData);
        logger.trace("filteredData size {}", filteredData.size());
        return filteredEvents;
    }

    private HistoryPage formatTheHistoryData(ChannelHistoryPage channelHistoryPage, GetHistoryPageDTO historyPageRequest, Date previousPageDate,
                                             String previousCardOperator) {
        List<HistoryView> historyViews = new ArrayList<>();
        boolean previousTxnHistory = false;
        AutomaticCardView automaticCard = null;
        ChannelDetail channelDetail = new ChannelDetail();

        if (historyPageRequest.getPagingState() == null) {

            if (Constants.CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(historyPageRequest.getPayType())) {
                addStaticCard(historyPageRequest, historyViews, previousPageDate);
            }

            if (!Constants.CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(historyPageRequest.getPayType()))
                automaticCard = addReminderHistoryCard(historyPageRequest, historyViews, channelHistoryPage.getChannelHistories(), previousPageDate, channelDetail);
        } else {
            previousTxnHistory = true;
        }

        previousCardOperator = addCardFromChannelHistory(channelHistoryPage, historyViews, historyPageRequest.getOperator(), previousPageDate, previousTxnHistory, previousCardOperator, historyPageRequest);
        Date previousEventDate = this.getPreviousEventDate(historyViews, previousPageDate);
        if ((!historyViews.isEmpty() || historyPageRequest.getPagingState() != null) && (!channelHistoryPage.isAvailableNext()))
            historyViews = this.addDateCard((previousEventDate == null ? new Date() : previousEventDate),
                    previousEventDate, true, historyViews);

        markAsFinalized(channelHistoryPage.getMoveToFinaliseData(), channelHistoryPage.getMarkFinaliseData(), historyPageRequest.getService());

        channelDetail.setCustomerId(historyPageRequest.getCustomerId());
        channelDetail.setOperator(historyPageRequest.getOperator());
        channelDetail.setRechargeNumber(historyPageRequest.getRecharge_number());
        channelDetail.setService(historyPageRequest.getService());
        HistoryPage historyPage = new HistoryPage(historyViews, historyViews.size(), channelHistoryPage.getLastCardId(),
                channelHistoryPage.isAvailableNext(), "SUCCESS", 200, channelDetail);
        String dedupeDate = "";
        if (previousEventDate != null) {
            dedupeDate = DateUtil.dateFormatter(previousEventDate, DateFormats.DATE_FORMAT);
        } else {
            dedupeDate = DateUtil.dateFormatter(new Date(), DateFormats.DATE_FORMAT);
        }
        String previousCardPaytype=null;
        if(channelHistoryPage.getChannelHistories() !=null && !channelHistoryPage.getChannelHistories().isEmpty()){
            previousCardPaytype=channelHistoryPage.getChannelHistories().get(channelHistoryPage.getChannelHistories().size()-1).getPaytype();
        }
        String[] dedupeIds = {dedupeDate, previousCardOperator,previousCardPaytype};
        historyPage.setDedupeids(dedupeIds);
        historyPage.setAutomaticCard(automaticCard);
        return historyPage;
    }

    private List<HistoryView> addChangeOperatorCard(String oldOperator, List<HistoryView> historyViews, Date previousPageDate, GetHistoryPageDTO historyPageRequest,String newOperator,String oldPayType,String newPayType) {

        if ((oldPayType != null && newPayType!=null &&
                (!oldPayType.equalsIgnoreCase(newPayType)))
            ||(oldOperator != null &&
                (!oldOperator.equalsIgnoreCase(newOperator)) &&
                (!(this.isVIoperator(newOperator) && this.isVIoperator(oldOperator))))) {
            CardInfoDto cardInfoDto = new CardInfoDto();
            cardInfoDto.setEventType(CardTypes.OPERATOR_CHANGE_CARD);
            cardInfoDto.setThemeType(UtilityThemeTypes.MNP);
            cardInfoDto.setPreviousOperator(oldOperator);
            cardInfoDto.setPreviousPayType(oldPayType);
            if((oldPayType != null &&
                    (!oldPayType.equalsIgnoreCase(newPayType)))
                    &&(oldOperator != null &&
                    (!oldOperator.equalsIgnoreCase(newOperator)) &&
                    (!(this.isVIoperator(newOperator) && this.isVIoperator(oldOperator))))){
                cardInfoDto.setMnpType(CardTypes.OPERATOR_PAYTYPE_CHANGE_CARD);
            }else if((oldPayType != null &&
                    (!oldPayType.equalsIgnoreCase(newPayType)))){
                cardInfoDto.setMnpType(CardTypes.PAYTYPE_CHANGE_CARD);
            }else if(oldOperator != null &&
                    (!oldOperator.equalsIgnoreCase(newOperator)) &&
                    (!(this.isVIoperator(newOperator) && this.isVIoperator(oldOperator)))){
                cardInfoDto.setMnpType(CardTypes.OPERATOR_CHANGE_CARD);
            }

            historyPageRequest.setOperator(newOperator);
            historyPageRequest.setPayType(newPayType);
            cardInfoDto.setHistoryPageRequest(historyPageRequest);

            HistoryView operatorChangeCard = staticCard.getCardInfo(cardInfoDto);
            if (operatorChangeCard != null) {
                Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
                if (previousDate == null) {
                    previousDate = new Date();
                }
                operatorChangeCard.setCreatedDate(DateUtil.dateFormatter(previousDate, DateFormats.DATE_TIME_FORMAT));
                operatorChangeCard.setPreviousDate(previousDate);
                operatorChangeCard.setEventDate(previousDate);
                historyViews.add(operatorChangeCard);
                return historyViews;
            }
        }
        return historyViews;
    }

    private boolean isVIoperator(String operator) {
        return Arrays.asList(Constants.CommonConstants.vodaOperators).contains(operator);
    }

    private String addCardFromChannelHistory(ChannelHistoryPage channelHistoryPage, List<HistoryView> historyViews, String requestedOperator, Date previousPageDate, boolean previousTxnHistory, String previousCardOperator, GetHistoryPageDTO historyPageRequest) {
        String oldOperator = null;
        String newOperator = (historyPageRequest.getDedupeids() ==null) ? historyPageRequest.getOperator() : null;
        String oldPayType = null;
        String newPayType = (historyPageRequest.getDedupeids() ==null) ? historyPageRequest.getPayType() : null;
        if(historyPageRequest.getDedupeids() !=null) {
            if(historyPageRequest.getDedupeids().length>2){
                newPayType=historyPageRequest.getDedupeids()[2];
            }
            if(historyPageRequest.getDedupeids().length>=2){
                newOperator = historyPageRequest.getDedupeids()[1];

            }
        }
        for (ChannelHistory channelHistory : channelHistoryPage.getChannelHistories()) {
            oldPayType = channelHistory.getPaytype();
            oldOperator = channelHistory.getOperator();
            if(!com.paytm.saga.enums.Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyPageRequest.getService()))
                addChangeOperatorCard(oldOperator,historyViews,previousPageDate,historyPageRequest,newOperator,oldPayType,newPayType);
            if (channelHistory.getEventType().equals(Constants.CommonConstants.NOT_PAID_ON_PAYTM_MESSAGE_TYPE)) {
                this.addNotPaidOnPaytmCard(channelHistory, historyViews,
                        previousPageDate, previousTxnHistory,historyPageRequest.isNative());
            } else if (channelHistory.getEventType().equals(EventTypes.MARKED_AS_PAID)) {
                addMarkedAsPaidCard(channelHistory, historyViews, previousPageDate,historyPageRequest.isNative());
            } else if (!ChannelHistoryUtils.checkForPennyDrop(channelHistory)) {
                addTransactionCard(channelHistory, historyViews, requestedOperator, previousPageDate,historyPageRequest.isNative());
            }
            previousCardOperator = channelHistory.getOperator();
            newOperator = channelHistory.getOperator();
            newPayType = channelHistory.getPaytype();
        }
        return previousCardOperator;
    }

    public AutomaticCardView addReminderHistoryCard(GetHistoryPageDTO historyPageRequest, List<HistoryView> historyViews, List<ChannelHistory> channelHistories, Date previousPageDate, ChannelDetail channelDetail) {
        logger.debug("addReminderHistoryCard start");
        AutomaticCardView automaticCard = null;
        Integer isAutomatic = null;
        Long productId = null;
        try {

            List<ReminderHistory> reminderHistories =null;
            if(com.paytm.saga.enums.Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(historyPageRequest.getService()) && Objects.nonNull(historyPageRequest.getProductId())){
                List<ReminderHistory>   reminderHistoriesCC = reminderHistoryService
                        .findByCustomerIdAndRechargeNumberAndService(historyPageRequest.getCustomerId(), historyPageRequest.getRecharge_number(), historyPageRequest.getService());
                reminderHistories=this.filterReminderForCC(reminderHistoriesCC,historyPageRequest.getProductId());
            }
            else {
                reminderHistories = reminderHistoryService
                        .findByCustomerIdAndRechargeNumberAndServiceAndOperator(historyPageRequest.getCustomerId(), historyPageRequest.getRecharge_number(), historyPageRequest.getService(),
                                historyPageRequest.getOperator());
            }

            logger.debug("reminder size fetched {} ", reminderHistories.size());
            ReminderHistory currentBill = this.filterOutLatestedBill(reminderHistories);
            if (currentBill != null) {
                logger.debug("currentBill is  {} ", currentBill);

                if (!ChannelHistoryUtils.ignoreAutomatic(channelHistories, currentBill))
                    addAutomaticDate(currentBill, historyPageRequest, historyPageRequest.getOperator(),historyPageRequest.isNative());
                else
                    logger.debug("ignoring automatic");


                if (!ChannelHistoryUtils.ignoreBill(channelHistories, currentBill))
                    addReminderCard(currentBill, historyViews, previousPageDate, channelHistories,historyPageRequest.isNative());
                else
                    logger.debug("ignoring reminder bill card");
                productId = currentBill.getProductId();
                isAutomatic = currentBill.getIs_automatic();
            } else {
                List<Recents> recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(historyPageRequest.getCustomerId(), historyPageRequest.getService(), historyPageRequest.getRecharge_number());
                if (Objects.nonNull(recents) && !CollectionUtils.isEmpty(recents)) {
                    Recents recent = recents.get(0);
                    isAutomatic = recent.getAutomaticStatus();
                    productId = recent.getProductId();
                }else{
                    logger.info("No recent exist for historyPageRequest : {}",historyPageRequest);
                }
            }
            if(channelDetail != null && Objects.nonNull(isAutomatic) && isAutomatic == 5){
                channelDetail.setIsRenewSubscription(Boolean.TRUE);
            }
            if (Objects.nonNull(productId)) {
                automaticCard = checkIsSchedulableForAutomatic(productId,isAutomatic);
            }

        } catch (Exception e) {
            logger.error("getting exception while adding reminder card ", e);
        }
        return automaticCard;
    }

    private void addAutomaticDate(ReminderHistory currentBill, GetHistoryPageDTO historyPageRequest, String requestedOperator,boolean isNative) {
        logger.debug("addAutomaticDate start" + historyPageRequest);

        List<AutomaticData> automaticDataList = automaticDataRepository
                .findByKeyCustomerIdAndKeyRechargeNumberAndKeyServiceAndKeyOperator(historyPageRequest.getCustomerId(), historyPageRequest.getRecharge_number(), historyPageRequest.getService(),
                        requestedOperator);

        logger.debug("automaticDataList fetched size is {} ", automaticDataList.size());
        if (!CollectionUtils.isEmpty(automaticDataList)) {
            Date automaticDate = automaticDataList.get(0).getAutomaticDate();
            if (automaticDate != null)
                currentBill.setAutomaticData(automaticDate);

        }
    }


    public void addMarkedAsPaidCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
                                    Date previousPageDate,boolean isNative) {
        logger.trace("addMarkedAsPaidCard start");
        boolean isLastCard = false;
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        cardInfoDto.setThemeType( com.paytm.saga.enums.Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(channelHistory.getService()) ? UtilityThemeTypes.CREDIT_CARD_MARKED_AS_PAID : UtilityThemeTypes.BILL_MARKED_PAID);
        cardInfoDto.setNative(isNative);
        Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
        this.addDateCard(channelHistory.getTransactionTime(), previousDate, isLastCard, historyViews);
        HistoryView markedAsPaidCard = utilityTransactionCard.getCardInfo(cardInfoDto);
        historyViews.add(markedAsPaidCard);

    }


    public void addReminderCard(ReminderHistory reminderHistory, List<HistoryView> historyViews, Date previousPageDate, List<ChannelHistory> channelHistories,boolean isNative) {
        logger.trace("addReminderCard start");

        if (ChannelHistoryUtils.isPartialPendingTransaction(channelHistories, reminderHistory)) {
            logger.debug("subtracting bill amount for partial pending Transaction");
            ChannelHistory channelHistory = channelHistories.get(0);
            Double remainingAmount = reminderHistory.getAmount() - Double.parseDouble(channelHistory.getAmount());
            reminderHistory.setAmount(remainingAmount);
        }
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        cardInfoDto.setNative(isNative);
        if(!CollectionUtils.isEmpty(channelHistories)) {
            cardInfoDto.setChannelHistory(channelHistories.get(0));
        }
        HistoryView card = utilityReminderCard.getCardInfo(cardInfoDto);
        if (card != null) {
            Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
            this.addDateCard(card.getEventDate(), previousDate, false, historyViews);
            historyViews.add(card);
        } else {
            logger.debug("reminder card is null");
        }

    }

    public void addStaticCard(GetHistoryPageDTO historyPageRequest, List<HistoryView> historyViews, Date previousPageDate) {
        logger.trace("addStaticCard start");
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setHistoryPageRequest(historyPageRequest);
        HistoryView card = staticCard.getCardInfo(cardInfoDto);

        if (card != null) {
            Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
            this.addDateCard(card.getEventDate(), previousDate, false, historyViews);
            historyViews.add(card);
        } else {
            logger.debug("static card is null");
        }
        logger.debug("After adding static card size {} ", historyViews.size());
    }


    public void addTransactionCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
                                   String requestedOperator, Date previousPageDate,boolean isNative) {
        logger.trace("inside addTransactionCard");
        boolean hideCTA = false;
        boolean isLastCard = false;
        // hiding CTA if current(channelHistory) recharge card operator is different
        // from requested operator
        if (!com.paytm.saga.enums.Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(channelHistory.getService()) && !requestedOperator.equalsIgnoreCase(channelHistory.getOperator())) {
            logger.trace("hiding CTA requestedOperator: {} , card operator: {} ", requestedOperator
                    , channelHistory.getOperator());
            hideCTA = true;
        }
        Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
        this.addDateCard(channelHistory.getTransactionTime(), previousDate, isLastCard, historyViews);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        cardInfoDto.setIgnoreCTA(hideCTA);
        cardInfoDto.setNative(isNative);
        HistoryView rechargeCardView = utilityTransactionCard.getCardInfo(cardInfoDto);
        logger.debug("rechargeCardView is {}", rechargeCardView);
        rechargeCardView.setNative(isNative);
        historyViews.add(rechargeCardView);

        logger.debug("After adding transaction card size {} ", historyViews.size());


    }

    public void addNotPaidOnPaytmCard(ChannelHistory channelHistory, List<HistoryView> historyViews,
                                      Date previousPageDate, boolean previousTxnHistory,boolean isNative) {
        logger.trace("adding bill not paid card card");
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        cardInfoDto.setNative(isNative);
        cardInfoDto.setThemeType(com.paytm.saga.enums.Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(channelHistory.getService()) ? UtilityThemeTypes.CREDIT_CARD_NOT_PAID_ON_PAYTM : UtilityThemeTypes.BILL_NOT_PAID_PAYTM);
        cardInfoDto.setPreviousTxnHistory(previousTxnHistory);
        HistoryView card = utilityTransactionCard.getCardInfo(cardInfoDto);
        if (card != null) {
            Date previousDate = this.getPreviousEventDate(historyViews, previousPageDate);
            this.addDateCard(card.getEventDate(), previousDate, false, historyViews);
            historyViews.add(card);

        }

    }
    @Override
    public List<HistoryView> addDateCard(Date eventDate, Date previousDate, boolean isLastCard,
                                         List<HistoryView> historyViews) {
        // System.out.println("eventDate=" + eventDate + ",previousDate=" + previousDate
        // + ",isLastCard=" + isLastCard);
        HistoryView dateCard = dateCardService.addDateInfoCard(eventDate, previousDate, isLastCard);

        if (dateCard != null) {
            List<ViewElementInfo> headings = dateCard.getHeadings();
            if(!CollectionUtils.isEmpty(headings)) {
                for (ViewElementInfo viewElement : headings) {
                    if(!"Today".equals(viewElement.getValue())) {
                        Date date = DateUtil.stringToDate(viewElement.getValue(), DateFormats.DATE_FORMAT);
                        viewElement.setValue(DateUtil.dateFormatter(date, DateFormats.DD_MMM_YYYY));
                    }
                }
            }
            historyViews.add(dateCard);
        }
        return historyViews;
    }
    private AutomaticCardView checkIsSchedulableForAutomatic(Long productId, Integer isAutomatic){
        AutomaticCardView automaticCard = null;
        Long pid = ActiveInactivePidMapCache.getInstance().getActivePid(productId);
        if(CVRProductCache.getInstance().isInactivePID(pid)){
            return null;
        }
        else{
            ProductMin productMin = CVRProductCache.getInstance().getProductDetails(pid);
            if (Objects.nonNull(productMin.getIsSchedulable()) && productMin.getIsSchedulable() == 1) {
                if(Objects.nonNull(automaticCardService.getAutomaticCard(isAutomatic)))
                automaticCard = automaticCardService.getAutomaticCard(isAutomatic);
            }
        }
        return  automaticCard;
    }

}
