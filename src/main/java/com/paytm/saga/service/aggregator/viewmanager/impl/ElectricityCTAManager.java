package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.viewmanager.declaration.CTAManager;

@Component("electricityCTAManager")
public class ElectricityCTAManager implements CTAManager {
	private static final Logger logger = LogManager.getLogger(ElectricityCTAManager.class);

	@Override
	public List<ViewElementInfo> getCTAs(HistoryView historyView) {
		logger.debug("setting cta for card type " + historyView.getThemeType());
		List<ViewElementInfo> response = null;
		String themeType = historyView.getThemeType();
		switch (themeType) {
		case CardThemeTypes.NEW_BILL_CARD_THEME:
			response = this.getReminderCTAs(historyView);
			break;
		case CardThemeTypes.BILL_OVER_DUE_CARD_THEME:
			response = this.getReminderCTAs(historyView);
			break;
		case CardThemeTypes.BILL_PARTIAL_PAID_CARD_THEME:
			response = this.getReminderCTAs(historyView);
			break;
		case CardThemeTypes.BILL_AFTER_OVER_DUE_CARD_THEME:
			response = this.getReminderCTAs(historyView);
			break;
		case CardThemeTypes.BILL_EXPIRED_CARD_THEME:
			response = this.getBillExpiredCTAs(historyView);
			break;
		case CardThemeTypes.DROPOFF_CARD_THEME:
			response = this.getDropOffCTAs(historyView);
			break;
		case CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME:
			response = this.getFailureCTAs(historyView);
			break;
		default:
			break;
		}
		return response;
	}

	private List<ViewElementInfo> getReminderCTAs(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		cta.setValue("Pay Bill");
		cta.setType("cta1");
		cta.setActionType("retry_recharge");
		cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.DROPOFF_CARD_THEME));
		response.add(cta);

		ViewElementInfo cta2 = new ViewElementInfo();
		cta2.setValue("Mark as Paid");
		cta2.setType("cta2");
		cta2.setActionType("mark_as_paid");
		cta2.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.DROPOFF_CARD_THEME));
		response.add(cta2);
	

		return response;

	}
	private List<ViewElementInfo> getDropOffCTAs(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		cta.setValue("Continue Payment");
		cta.setType("cta1");
		cta.setActionType("retry_recharge");
		cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.DROPOFF_CARD_THEME));
		response.add(cta);
		return response;

	}
	
	private List<ViewElementInfo> getFailureCTAs(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta = new ViewElementInfo();
		cta.setValue("Retry");
		cta.setType("cta1");
		cta.setActionType("retry_recharge");
		cta.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME));
		response.add(cta);
		return response;

	}

	private List<ViewElementInfo> getBillExpiredCTAs(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo cta2 = new ViewElementInfo();
		cta2.setValue("Mark as Paid");
		cta2.setType("cta1");
		cta2.setActionType("mark_as_paid");
		cta2.setDeepLink(DeepLinkManager.getDeeplinkByType(CardThemeTypes.DROPOFF_CARD_THEME));
		response.add(cta2);
		return response;

	}

}
