package com.paytm.saga.service.aggregator.viewmanager.impl;

import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.paytm.saga.common.constant.UtilityThemeTypes.*;

@Component("utilityHeaderManager")
public class UtilityHeaderManager extends CommonHeaderManager {
    private static final Logger logger = LogManager.getLogger(UtilityHeaderManager.class);

    @Value("${utility.header.suggested.recharge.text}")
    private String sugggestedRechargeText;

    @Value("${utility.header.operatorChange.text}")
    private String operatorChangeText;

    @Override
    public List<ViewElementInfo> getHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = null;
        String themeType = historyView.getThemeType();
        switch (themeType) {
            case SUGGESTED_RECHARGE:
                response = getSuggestedRechargeHeaders(historyView);
                break;

            case RECHARGE_SUCCESS:
                response = getRechargeSuccessHeaders(historyView);
                break;

            case RECHARGE_FAILED:
                response = getRechargeFailedHeaders(historyView);
                break;

            case RECHARGE_PENDING:
                response = getRechargePendingHeaders(historyView);
                break;

            case RECHARGE_CANCELLED:
                response = getRechargeCancelledHeaders(historyView);
                break;

            case BILL_DUE:
                response = getBillDueHeaders(historyView);
                break;

            case BILL_DUE_TODAY_OR_TOMORROW:
                response = getBillDueTodayOrTomorrowHeaders(historyView);
                break;
            case BILL_OVERDUE:
                response = getBillOverDueHeaders(historyView);
                break;
            case BILL_NOT_PAID_PAYTM:
                response = getBillNotPaidOnPaytmHeaders(historyView);
                break;
            case BILL_MARKED_PAID:
                response = getBillMarkedPaidHeaders(historyView);
                break;

            case BILL_SUCCESS:
                response = getBillSuccessHeaders(historyView);
                break;
            case BILL_FAILED:
                response = getBillFailedHeaders(historyView);
                break;
            case BILL_PENDING:
                response = getBillPendingHeaders(historyView);
                break;

            case BILL_CANCELLED:
                response = getBillCancelledHeaders(historyView);
                break;

            case AUTOMATIC_PAYMENT_SUCCESS:
                response = getAutomaticSuccessHeaders(historyView);
                break;
            case AUTOMATIC_PAYMENT_FAILED:
                response = getAutomaticFailedHeaders(historyView);
                break;
            case AUTOMATIC_PAYMENT_PENDING:
                response = getAutomaticPendingHeaders(historyView);
                break;
            case AUTOMATIC_PAYMENT_CANCELLED:
                response = getAutomaticCancelledHeaders(historyView);
                break;

            case AUTOMATIC_PAYMENT:
                response = getAutomaticHeaders(historyView);
                break;

            case MNP:
                response = getMnpHeaders(historyView);
                break;

            case AUTOMATIC_PAYMENT_UNDER_RETRY:
                response = getAutomaticPaymentBillDueUnderRetryHeaders(historyView);
                break;

            default:
                break;
        }

        logger.debug("setting headers for card type {} is {}", historyView.getThemeType(), response);

        return response;
    }


    private List<ViewElementInfo> getSuggestedRechargeHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue(sugggestedRechargeText);
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getRechargeSuccessHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        if(historyView.isNative()){
            header2.setValue("Payment Successful");
        }else{
            header2.setValue("Bill Paid");
        }

        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getRechargeFailedHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Payment Failed");
        header2.setType("header2");
        response.add(header2);
        if(Constants.PG_RESP_CODES_FOR_INSUFFICIENT_BALANCE_FAILURE.contains(historyView.getPgRespCode())){
            ViewElementInfo header3 = new ViewElementInfo();
            header3.setType("header3");
            header3.setValue("Your Account balance is Low. Please Pay Manually");
            response.add(header3);
        }
        return response;
    }

    private List<ViewElementInfo> getRechargePendingHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Payment Pending");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getRechargeCancelledHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Payment Cancelled");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getBillDueHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        if(historyView.isNative()){
            ViewElementInfo header2 = new ViewElementInfo();
            header2.setValue("Payment Due");
            header2.setType("header2");
            response.add(header2);
        }
        return response;
    }

    private List<ViewElementInfo> getBillDueTodayOrTomorrowHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        if((DateUtil.compareDateWithoutTime(DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2), new Date()) == 0)){
            ViewElementInfo footer = new ViewElementInfo();
            footer.setValue("Bill Due Today");
            footer.setType("footer1");
            response.add(footer);
        }else {
            ViewElementInfo footer = new ViewElementInfo();
            footer.setValue("Bill Due Tomorrow");
            footer.setType("footer1");
            response.add(footer);
        }
        return response;
    }

    private List<ViewElementInfo> getBillOverDueHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        if(historyView.isNative()){
            ViewElementInfo header2 = new ViewElementInfo();
            header2.setValue("Payment Overdue");
            header2.setType("header2");
            response.add(header2);
        }else{
            ViewElementInfo footer = new ViewElementInfo();
            footer.setValue("Bill Overdue");
            footer.setType("footer1");
            response.add(footer);
        }

        return response;
    }


    private List<ViewElementInfo> getBillNotPaidOnPaytmHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo footer = new ViewElementInfo();
        footer.setValue("Not Paid on Paytm");
        footer.setType("footer1");
        response.add(footer);
        return response;
    }

    private List<ViewElementInfo> getBillMarkedPaidHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo footer = new ViewElementInfo();
        footer.setValue("Marked as Paid");
        footer.setType("footer1");
        response.add(footer);
        return response;
    }

    private List<ViewElementInfo> getBillSuccessHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        if(historyView.isNative()){
            header2.setValue("Payment Successful");
        }else{
            header2.setValue("Bill Paid");
        }
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getBillFailedHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Payment Failed");
        header2.setType("header2");
        response.add(header2);
        if(Constants.PG_RESP_CODES_FOR_INSUFFICIENT_BALANCE_FAILURE.contains(historyView.getPgRespCode())){
            ViewElementInfo header3 = new ViewElementInfo();
            header3.setType("header3");
            header3.setValue("Your Account balance is Low. Please Pay Manually");
            response.add(header3);
        }
        return response;
    }

    private List<ViewElementInfo> getBillPendingHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Payment Pending");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getBillCancelledHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Payment Cancelled");
        header2.setType("header2");
        response.add(header2);
        return response;
    }


    private List<ViewElementInfo> getAutomaticSuccessHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Paid Automatically");
        header2.setType("header2");
        response.add(header2);
        return response;
    }


    private List<ViewElementInfo> getAutomaticFailedHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Automatic Payment Failed");
        header2.setType("header2");
        response.add(header2);
        if(Boolean.TRUE.equals(historyView.isRetryExhausted())){
            ViewElementInfo header3 = new ViewElementInfo();
            header3.setType("header3");
            header3.setValue("Your account balance is low. Please pay manually.");
            response.add(header3);
        }
        else if(Constants.PG_RESP_CODES_FOR_INSUFFICIENT_BALANCE_FAILURE.contains(historyView.getPgRespCode())){
            ViewElementInfo header3 = new ViewElementInfo();
            header3.setType("header3");
            header3.setValue("We will retry within 24 hrs. Please Maintain Sufficient Balance.");
            response.add(header3);
        }
        
        return response;
    }


    private List<ViewElementInfo> getAutomaticPendingHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Automatic Payment Pending");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getAutomaticCancelledHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Automatic Payment Cancelled");
        header2.setType("header2");
        response.add(header2);
        return response;
    }

    private List<ViewElementInfo> getAutomaticHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        if (historyView.getAutomaticDate() != null) {
            ViewElementInfo footer1 = new ViewElementInfo();
            if(historyView.isNative()){
                footer1.setValue("Will be paid automatically on " + DateUtil.dateFormatter(historyView.getAutomaticDate(), DateFormats.DD_MMM));
            }else{
                footer1.setValue("Will be paid automatically on " + DateUtil.dateFormatter(historyView.getAutomaticDate(), DateFormats.D_MMM));
            }
            footer1.setType("footer1");
            response.add(footer1);
        }
        return response;
    }

    private List<ViewElementInfo> getMnpHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
        ViewElementInfo header1 = new ViewElementInfo();
        if (StringUtils.equalsIgnoreCase(historyView.getEventType(), CardTypes.OPERATOR_CHANGE_CARD)) {
            if(CardTypes.PAYTYPE_CHANGE_CARD.equals(historyView.getMnpType())){
                header1.setValue("We detected a change from "+capitalizeOperator(historyView.getPreviousOperator())+" "+StringUtils.capitalize(historyView.getPreviousPayType())
                        +" to "+capitalizeOperator(historyView.getOperator())+" "+StringUtils.capitalize(historyView.getPayType())+"");
            }else{
                    header1.setValue(String.format(operatorChangeText, capitalizeOperator(historyView.getPreviousOperator()),
                            StringUtils.capitalize(historyView.getPreviousPayType()), capitalizeOperator(historyView.getOperator()),StringUtils.capitalize(historyView.getPayType())));
            }

        }
        response.add(header1);
        return response;
    }

    private List<ViewElementInfo> getAutomaticPaymentBillDueUnderRetryHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Automatic Payment Enabled");
        header2.setType("header2");
        response.add(header2);
        ViewElementInfo header3 = new ViewElementInfo();
        header3.setType("header3");
        header3.setValue("We are retrying your bill payment. Please ensure sufficient balance.");
        response.add(header3);
        return response;
    }

    private List<ViewElementInfo> getPGPaymentFailedHeaders(HistoryView historyView) {
        List<ViewElementInfo> response = new ArrayList<>();
        ViewElementInfo header1 = new ViewElementInfo();
        header1.setValue(formatNumber(historyView.getAmount()));
        header1.setType("header1");
        response.add(header1);
        ViewElementInfo header2 = new ViewElementInfo();
        header2.setValue("Payment Failed!");
        header2.setType("header2");
        response.add(header2);
        ViewElementInfo header3 = new ViewElementInfo();
        header3.setType("header3");
        header3.setValue("Your Account balance is Low. Please Pay Manually.");
        response.add(header3);
        return response;
    }
    
    private String capitalizeOperator(String operator){
        int var=0;
        String operatorCapitalize="";
        for(int i=0;i<operator.length();i++){
            if(var==0) {
                operatorCapitalize +=(Character.toUpperCase(operator.charAt(i)));
                var=1;
            }
           else if(operator.charAt(i)==' ') {
               var=0;
                operatorCapitalize +=(operator.charAt(i));
            }
           else operatorCapitalize +=(operator.charAt(i));
        }
        return operatorCapitalize;
    }



}
