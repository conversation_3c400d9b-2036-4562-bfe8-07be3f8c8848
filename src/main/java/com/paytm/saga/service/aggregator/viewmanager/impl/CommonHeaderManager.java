package com.paytm.saga.service.aggregator.viewmanager.impl;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.DeepLinkTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.RichInfo;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.service.aggregator.AggregatorService;
import com.paytm.saga.service.aggregator.viewmanager.declaration.HeaderManagerInterface;
import com.paytm.saga.util.DateUtil;

abstract public class CommonHeaderManager implements HeaderManagerInterface {
	private static final Logger logger = LogManager.getLogger(CommonHeaderManager.class);
	
	protected List<ViewElementInfo> getDateChangeHeaders(HistoryView historyView) {
		try {
			List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
			ViewElementInfo header = new ViewElementInfo();
			Date todayDate = DateUtil.timestampToDate(new Date());
			Date cardDate = DateUtil.timestampToDate(historyView.getEventDate());

			if (historyView.isLastCard()) {
				if ((todayDate.equals(cardDate))) {
					header.setValue("Today");
				} else {
					header.setValue(DateUtil.dateFormatter(cardDate, DateFormats.DATE_FORMAT));
				}
				headers.add(header);
				return headers;
			} else if (historyView.getPreviousDate() != null) {
				Date preDate = DateUtil.timestampToDate(historyView.getPreviousDate());
				if (!preDate.equals(cardDate)) {
					if ((todayDate.equals(preDate))) {
						header.setValue("Today");
					} else
						header.setValue(DateUtil.dateFormatter(preDate, DateFormats.DATE_FORMAT));
					headers.add(header);
					return headers;
				}
			}
		} catch (ParseException e) {
			logger.error("CommonHeaderManager : getDateChangeHeaders",e);
		}

		return null;
	}
	protected List<ViewElementInfo> getDropOffHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		header1.setValue("Complete your Booking");
		header1.setType("header1");
		response.add(header1);
		ViewElementInfo header2 = new ViewElementInfo();
		header2.setValue("₹" + formatNumber(historyView.getAmount()));
		header2.setType("header2");
		response.add(header2);
		return response;
	}

	protected List<ViewElementInfo> getScratchCardHeaders(HistoryView historyView) {
		List<ViewElementInfo> response = new ArrayList<ViewElementInfo>();
		ViewElementInfo header1 = new ViewElementInfo();
		List<RichInfo> richInfo = new ArrayList<RichInfo>();
		richInfo.add(new RichInfo("scratch card", DeepLinkManager.getDeeplinkByType(DeepLinkTypes.SCRATCH_DEEP_LINK)));
		header1.setRichInfo(richInfo);
		header1.setType("header1");
		header1.setValue("You’ve won a scratch card");
		response.add(header1);
		return response;
	}

	private static String format(String pattern, Object value) {
		return new DecimalFormat(pattern).format(value);
	}

	public static String formatNumber(Double value) {
		if (!isAmountValid(value)) return null;
		if (value < 1000) {
			return format("###.##", value);
		} else {
			double hundreds = value % 1000;
			int other = (int) (value / 1000);
			return format(",##", other) + ',' + format("000.##", hundreds);
		}
	}

	public static boolean isAmountValid(Double amount) {
		return !Objects.isNull(amount) && amount > 0;
	}
}
