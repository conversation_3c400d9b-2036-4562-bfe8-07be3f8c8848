package com.paytm.saga.service.aggregator.cardmanager;

import java.util.HashMap;
import java.util.Map;

import jakarta.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.DeepLinkTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.OMSToRechargeStatus;

@Component("rechargeCard")
public class RechargeCard implements CardManager {

	private static final Logger logger = LogManager.getLogger(RechargeCard.class);
	private ViewManager ctaManager;
	private ViewManager displayValuesManager;
	private ViewManager headersViewManager;

	@Autowired
	public RechargeCard(final @NotNull @Qualifier("ctaViewManager") ViewManager ctaManager,
			final @NotNull @Qualifier("displayValuesViewManager") ViewManager displayValuesManager,
			final @NotNull @Qualifier("headersViewManager") ViewManager headersViewManager) {
		this.ctaManager = ctaManager;
		this.displayValuesManager = displayValuesManager;
		this.headersViewManager = headersViewManager;
	}

	private static final Map<String, String> themesMapping = new HashMap<String, String>() {
		{
			put(CardThemeTypes.SUCCESS_CYLINDER_BOOKING_CARD_THEME, "success_card");
			put(CardThemeTypes.FAILURE_CYLINDER_BOOKING_CARD_THEME, "failure_card");
			put(CardThemeTypes.PENDING_CYLINDER_BOOKING_CARD_THEME, "pending_card");
			put(CardThemeTypes.PAYMENT_PENDING_CYLINDER_BOOKING_CARD_THEME, "pending_card");
			put(CardThemeTypes.CANCELLED_CYLINDER_BOOKING_CARD_THEME, "failure_card");
			put(CardThemeTypes.REVERSAL_FAILURE_CYLINDER_BOOKING_CARD_THEME, "failure_card");
			put(CardThemeTypes.PAYMENT_FAILURE_CYLINDER_BOOKING_CARD_THEME, "failure_card");

		}
	};

	public HistoryView getCardInfo(CardInfoDto cardInfoDto) {
		return convertHistoryViewEntityToDTO(cardInfoDto.getChannelHistory(), cardInfoDto.isIgnoreCTA(),cardInfoDto.isNative());
	}

	private HistoryView convertHistoryViewEntityToDTO(ChannelHistory channelHistory, boolean hideCTA, boolean isNative) {
		HistoryView historyView = new HistoryView();
		historyView.setNative(isNative);
		historyView.setEventDate(channelHistory.getTransactionTime());
		historyView.setCreatedDate(
				DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT));
		historyView.setAmount(Double.parseDouble(channelHistory.getAmount()));
		historyView.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
				channelHistory.getInResponseCode(), channelHistory.getPaymentStatus()));
		historyView.setOrderId(channelHistory.getOrderId());
		// historyView.setPlanBucket(channelHistory.getPlanbucket());
		historyView.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK));
		historyView.setPlanDetail(channelHistory.getDisplayValues());
		historyView.setEventDate(channelHistory.getTransactionTime());
		historyView.setCircle(channelHistory.getCircle());
		historyView.setOperator(channelHistory.getOperator());
		historyView.setService(channelHistory.getService());
		historyView.setPayType(channelHistory.getPaytype());
		historyView.setEventType(channelHistory.getEventType());
		historyView.setStatusCode(channelHistory.getStatus());
		historyView.setThemeType(getRechargeThemeType(historyView));
		historyView.setBookingId(this.getCylinderBookingId(channelHistory));
		historyView.setIvrsBooking(this.isCylinderIVRSBooking(channelHistory));
		historyView.setOperatorLabel(this.getOperatorLabel(channelHistory));
		historyView.setBillDate(this.getBillDate(channelHistory));
		historyView.setDueDate(this.getDueDate(channelHistory));
		historyView.setPaymentStatus(channelHistory.getPaymentStatus());
		// setting all header, cta,display values based on themeTypes not on eventTypes
		historyView.setHeadings(headersViewManager.getViewElements(historyView));
		historyView.setDisplayValues(displayValuesManager.getViewElements(historyView));

		if (!hideCTA)
			historyView.setCta(ctaManager.getViewElements(historyView));
		historyView.setThemeType(this.getRechargeUpdatedThemeType(historyView.getThemeType()));
		
		try {
			if (historyView.getDueDate() != null) {
				historyView.setDueDate(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.DATE_FORMAT_2));
			}
			
			if (historyView.getBillDate() != null) {
				historyView.setBillDate(DateUtil.dateFormatter(
						DateUtil.stringToDate(historyView.getBillDate(), DateFormats.DATE_TIME_FORMAT_2),
						DateFormats.DATE_FORMAT_2));
			}			
		}catch(RuntimeException e) {
			logger.error("Getting exception in parsing billdate duedate in recharge card");
		}



		return historyView;
	}

	private String getCylinderBookingId(ChannelHistory channelHistory) {
		Map<String, String> billsObj = channelHistory.getBillsObj();
		if (billsObj != null) {
			return billsObj.get("cylinderBookingId");
		}

		return null;
	}

	private String getBillDate(ChannelHistory channelHistory) {
		Map<String, String> billsObj = channelHistory.getBillsObj();
		if (billsObj != null) {
			return billsObj.get(Constants.ReminderConstants.BILL_DATE);
		}

		return null;
	}

	private String getDueDate(ChannelHistory channelHistory) {
		Map<String, String> billsObj = channelHistory.getBillsObj();
		if (billsObj != null) {
			return billsObj.get(Constants.ReminderConstants.DUE_DATE);
		}

		return null;
	}

	private boolean isCylinderIVRSBooking(ChannelHistory channelHistory) {
		Map<String, String> billsObj = channelHistory.getBillsObj();
		if (billsObj != null) {
			return !((billsObj.get("isFreshBooking") != null && billsObj.get("isFreshBooking").equals("true")));
		}
		return false;
	}

	private String getOperatorLabel(ChannelHistory channelHistory) {
		String op = channelHistory.getOperator();
		if (op != null) {
			String response = "";
			String str = StringUtils.capitalize(op);
			String tokens[] = str.split(" ");
			int i = 0;
			for (String token : tokens) {
				if (i == 0 && token.length() == 2) {
					response = response + token.toUpperCase();
				} else if (token.length() == 2) {
					response = response + " " + token.toUpperCase();
				} else {
					response = (response + " " + StringUtils.capitalize(token)).trim();
				}
			}
			return response;
		} else {
			return "operator";
		}
	}

	private String getRechargeUpdatedThemeType(String themeType) {
		String updatedTheme = themesMapping.get(themeType);
		if (updatedTheme == null) {
			updatedTheme = themeType;
		}
		return updatedTheme;
	}

	private String getRechargeThemeType(HistoryView channelHistory) {
		String cardInfo = (channelHistory.getPayType() + "_" + channelHistory.getService() + "_"
				+ channelHistory.getEventType() + "_" + channelHistory.getStatus()).toLowerCase();
		logger.trace("order id" + channelHistory.getOrderId() + " event type" + channelHistory.getEventType() + "status"
				+ channelHistory.getStatus());
		String response = "";
		switch (cardInfo) {
		case "prepaid_mobile_recharge_success":
			response = CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_failure":
			response = CardThemeTypes.FAILURE_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_reversal_failure":
			response = CardThemeTypes.FAILURE_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_payment_failure":
			response = CardThemeTypes.FAILURE_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_pending":
			response = CardThemeTypes.PENDING_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_payment_pending":
			response = CardThemeTypes.PENDING_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_cancel":
			response = CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_success":
			response = CardThemeTypes.SUCCESS_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_failure":
			response = CardThemeTypes.FAILURE_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_reversal_failure":
			response = CardThemeTypes.REVERSAL_FAILURE_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_payment_failure":
			response = CardThemeTypes.PAYMENT_FAILURE_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_pending":
			response = CardThemeTypes.PENDING_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_payment_pending":
			response = CardThemeTypes.PAYMENT_PENDING_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_cancel":
			response = CardThemeTypes.CANCELLED_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "postpaid_electricity_recharge_success":
			response = CardThemeTypes.ELECTRICITY_SUCCESS_RECHARGE_CARD_THEME;
			break;
		case "postpaid_electricity_recharge_pending":
			response = CardThemeTypes.ELECTRICITY_PENDING_RECHARGE_CARD_THEME;
			break;
		case "postpaid_electricity_recharge_failure":
			response = CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME;
			break;
		case "postpaid_electricity_recharge_reversal_failure":
			response = CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME;
			break;
		case "postpaid_electricity_recharge_payment_failure":
			response = CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME;
			break;
		case "postpaid_electricity_recharge_cancel":
			response = CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME;
			break;
		case "postpaid_electricity_recharge_payment_pending":
			response = CardThemeTypes.ELECTRICITY_PENDING_RECHARGE_CARD_THEME;
			break;
		}
		logger.trace("theme type for recharge card with order id" + channelHistory.getOrderId() + ": " + response);
		return response;
	}
}
