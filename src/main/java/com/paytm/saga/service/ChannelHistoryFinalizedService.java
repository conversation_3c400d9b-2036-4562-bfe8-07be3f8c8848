package com.paytm.saga.service;

import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.List;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.util.TTLUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.cassandra.CassandraUncategorizedException;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;

import com.datastax.oss.driver.api.core.cql.PagingState;
import com.paytm.saga.dto.ResponsePage;
import com.paytm.saga.model.ChannelHistoryFinalized;

@Service
public class ChannelHistoryFinalizedService {
	private static final CustomLogger logger = CustomLogManager.getLogger(ChannelHistoryFinalizedService.class);

	private final ChannelHistoryFinalizedRepositoryWrapperService channelHistoryFinalizedRepository;

	@Autowired
	private MetricsHelper metricsHelper;

	@Autowired
	public ChannelHistoryFinalizedService(final ChannelHistoryFinalizedRepositoryWrapperService channelHistoryFinalizedRepository) {
		this.channelHistoryFinalizedRepository = channelHistoryFinalizedRepository;
	}

	public ResponsePage<ChannelHistoryFinalized> getPageOfHistory(final long customerId, final String recharge_number,
			final String service, final Integer limit, final String pagingState) {
		logger.trace("getting history from finalized table for customer id" + customerId);
		final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(limit, pagingState);

		try {
			final Slice<ChannelHistoryFinalized> historySlice = channelHistoryFinalizedRepository
					.findByCustomerIdAndRechargeNumberAndService(customerId, recharge_number, service,
							cassandraPageRequest);
			return new ResponsePage<>(historySlice);
		} catch (CassandraUncategorizedException e) {
			if (e.getMessage() != null && e.getMessage().contains("Invalid value for the paging state")) {
				logger.warn("Invalid paging state detected, falling back to first page. " +
						   "CustomerId: {}, PagingState length: {}, Error: {}",
						   customerId, pagingState != null ? pagingState.length() : 0, e.getMessage());

				// Record metrics for monitoring
				metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR,
					Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE + "_INVALID_FALLBACK");

				// Create first page request and retry
				final CassandraPageRequest firstPageRequest = CassandraPageRequest.of(PageRequest.of(0, limit), (ByteBuffer) null);
				final Slice<ChannelHistoryFinalized> historySlice = channelHistoryFinalizedRepository
						.findByCustomerIdAndRechargeNumberAndService(customerId, recharge_number, service,
								firstPageRequest);
				return new ResponsePage<>(historySlice);
			}
			// Re-throw if it's a different Cassandra error
			throw e;
		}
	}

	public ResponsePage<ChannelHistoryFinalized> getPageOfHistory(final Integer limit, final String pagingState) {
		final CassandraPageRequest cassandraPageRequest = createCassandraPageRequest(limit, pagingState);
		return getPageOfHistory(cassandraPageRequest);
	}

	public ResponsePage<ChannelHistoryFinalized> getPageOfHistory(final CassandraPageRequest cassandraPageRequest) {
		try {
			final Slice<ChannelHistoryFinalized> historySlice = channelHistoryFinalizedRepository
					.findAll(cassandraPageRequest);
			return new ResponsePage<>(historySlice);
		} catch (CassandraUncategorizedException e) {
			if (e.getMessage() != null && e.getMessage().contains("Invalid value for the paging state")) {
				logger.warn("Invalid paging state detected in findAll, falling back to first page. Error: {}", e.getMessage());

				// Record metrics for monitoring
				metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR,
					Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE + "_FINDALL_INVALID_FALLBACK");

				// Create first page request and retry
				final CassandraPageRequest firstPageRequest = CassandraPageRequest.of(
					PageRequest.of(0, cassandraPageRequest.getPageSize()), (ByteBuffer) null);
				final Slice<ChannelHistoryFinalized> historySlice = channelHistoryFinalizedRepository
						.findAll(firstPageRequest);
				return new ResponsePage<>(historySlice);
			}
			// Re-throw if it's a different Cassandra error
			throw e;
		}
	}

	private CassandraPageRequest createCassandraPageRequest(final Integer limit, final String pagingState) {
		final PageRequest pageRequest = PageRequest.of(0, limit);
		if (pagingState != null) {
			try {
				logger.info("Processing paging state: length={}", pagingState.length());

				// Try Base64 decoding first (new format)
				byte[] decodedBytes = Base64.getDecoder().decode(pagingState);
				ByteBuffer pagingStateBuffer = ByteBuffer.wrap(decodedBytes);
				metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE);
				return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
			} catch (Exception e) {
				logger.error("Failed to parse paging state - length: {}, error: {}",
						pagingState.length(), e.getMessage());
				// Try fallback to old format for backward compatibility
				try {
					logger.info("Attempting fallback to PagingState.fromString format");
					final PagingState pageState = PagingState.fromString(pagingState);
					ByteBuffer pagingStateBuffer = ByteBuffer.wrap(pageState.toBytes());
					metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE);
					return CassandraPageRequest.of(pageRequest, pagingStateBuffer);
				} catch (Exception fallbackException) {
					logger.error("Fallback parsing also failed: {}", fallbackException.getMessage());
					metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_ERROR, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE);
					return CassandraPageRequest.of(pageRequest, null);
				}
			}
		}

		metricsHelper.recordStatusCodes(Constants.MetricConstants.PAGING_STATE_PARSE_SUCCESS, Constants.MetricConstants.PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE_DEFAULT);
		return CassandraPageRequest.of(pageRequest, null);
	}

	public void saveAll(List<ChannelHistoryFinalized> channelHistoryFinalizeList, String service) {
		Integer ttl = TTLUtils.getTTL(service);
		logger.info("[ChannelHistoryFinalizedService.saveAll] CustomerId : {} , RechargeNumber : {}, TTl : {}",channelHistoryFinalizeList.get(0).getCustomerId(),channelHistoryFinalizeList.get(0).getRechargeNumber(),ttl);
		channelHistoryFinalizedRepository.saveAll(channelHistoryFinalizeList,ttl);
	}
}
