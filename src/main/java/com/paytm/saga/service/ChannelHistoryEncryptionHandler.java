package com.paytm.saga.service;

import com.google.common.base.Strings;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.util.AESUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ChannelHistoryEncryptionHandler {
	private final AESUtil aesUtil;

	@Autowired
	public ChannelHistoryEncryptionHandler(AESUtil aesUtil, MetricsHelper metricsHelper) {
		this.aesUtil = aesUtil;
	}

	public List<ChannelHistory> encryptChannelHistoryList(List<ChannelHistory> channelHistoryList)  throws AES256Exception {
		List<ChannelHistory> encryptedChannelHistoryList = new ArrayList<>();
		for(ChannelHistory channelHistory: channelHistoryList) {
			encryptedChannelHistoryList.add(encryptChannelHistory(channelHistory));
		}
		return encryptedChannelHistoryList;
	}

	public ChannelHistory encryptChannelHistory(ChannelHistory channelHistoryToEncrypt)  throws AES256Exception {
		ChannelHistory channelHistory = new ChannelHistory();
		BeanUtils.copyProperties(channelHistoryToEncrypt, channelHistory, ChannelHistory.class);
		if (channelHistoryToEncrypt.getBillsObj() != null) {
			channelHistory.setBillsObj(new HashMap<>(channelHistoryToEncrypt.getBillsObj()));
		}
		if (isEncrypted(channelHistory)) {
			return channelHistory;
		}
		if (!Strings.isNullOrEmpty(channelHistory.getAmount())) {
			channelHistory.setAmount(aesUtil.encrypt(channelHistory.getAmount()));
		}
		if (!Strings.isNullOrEmpty(channelHistory.getRechargeNumber())) {
			channelHistory.setRechargeNumber(aesUtil.encrypt(channelHistory.getRechargeNumber()));
		}
		if (!Strings.isNullOrEmpty(channelHistory.getRecharge_number_3())) {
			channelHistory.setRecharge_number_3(aesUtil.encrypt(channelHistory.getRecharge_number_3()));
		}
		if (Objects.nonNull(channelHistory.getBillsObj())) {
			Map<String, String> encryptedBillsObj = new HashMap<>();
			channelHistory.getBillsObj().forEach((key, value) -> encryptedBillsObj.put(key, aesUtil.encrypt(value)));
			channelHistory.setBillsObj(encryptedBillsObj);
		}
		channelHistory.setIsEncrypted(1);
		return channelHistory;
	}

	public Slice<ChannelHistory> decryptChannelHistorySlice(Slice<ChannelHistory> channelHistoryList)  throws AES256Exception {
		List<ChannelHistory> decryptedChannelHistoryList = new ArrayList<>();
		for(ChannelHistory channelHistory: channelHistoryList.getContent()) {
			decryptedChannelHistoryList.add(decryptChannelHistory(channelHistory));
		}
		return new SliceImpl<>(decryptedChannelHistoryList, channelHistoryList.getPageable(), channelHistoryList.hasNext());
	}

	public List<ChannelHistory> decryptChannelHistoryList(List<ChannelHistory> channelHistoryList)  throws AES256Exception {
		List<ChannelHistory> decryptedChannelHistoryList = new ArrayList<>();
		for(ChannelHistory channelHistory: channelHistoryList) {
			decryptedChannelHistoryList.add(decryptChannelHistory(channelHistory));
		}
		return decryptedChannelHistoryList;
	}

	public ChannelHistory decryptChannelHistory(ChannelHistory channelHistoryToDecrypt)  throws AES256Exception {
		if (!isEncrypted(channelHistoryToDecrypt)) {
			return channelHistoryToDecrypt;
		}
		ChannelHistory channelHistory = new ChannelHistory();
		BeanUtils.copyProperties(channelHistoryToDecrypt, channelHistory, ChannelHistory.class);
		if (channelHistoryToDecrypt.getBillsObj() != null) {
			channelHistory.setBillsObj(new HashMap<>(channelHistoryToDecrypt.getBillsObj()));
		}
		if (!Strings.isNullOrEmpty(channelHistory.getAmount())) {
			channelHistory.setAmount(aesUtil.decrypt(channelHistory.getAmount()));
		}
		if (!Strings.isNullOrEmpty(channelHistory.getRechargeNumber())) {
			channelHistory.setRechargeNumber(aesUtil.decrypt(channelHistory.getRechargeNumber()));
		}
		if (!Strings.isNullOrEmpty(channelHistory.getRecharge_number_3())) {
			channelHistory.setRecharge_number_3(aesUtil.decrypt(channelHistory.getRecharge_number_3()));
		}
		if (Objects.nonNull(channelHistory.getBillsObj())) {
			Map<String, String> decryptedBillsObj = new HashMap<>();
			channelHistory.getBillsObj().forEach((key, value) -> decryptedBillsObj.put(key, aesUtil.decrypt(value)));
			channelHistory.setBillsObj(decryptedBillsObj);
		}
		channelHistory.setIsEncrypted(0);
		return channelHistory;
	}

	private boolean isEncrypted(ChannelHistory channelHistory) {
		return Objects.nonNull(channelHistory) && Objects.nonNull(channelHistory.getIsEncrypted()) && channelHistory.getIsEncrypted() == 1;
	}
}
