package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service("com.paytm.saga.service.SmsCardsRestTemplateService")
public class SmsCardsRestTemplateService extends RestTemplateService {
	private static final CustomLogger logger = CustomLogManager.getLogger(SmsCardsRestTemplateService.class);

    @Autowired
    public SmsCardsRestTemplateService(@NonNull @Qualifier("SMSCardsRestTemplateClient") RestTemplate restTemplate) {
        super(restTemplate);
    }
}
