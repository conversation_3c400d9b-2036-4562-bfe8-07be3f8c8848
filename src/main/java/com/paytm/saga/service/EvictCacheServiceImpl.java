package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.property.EvictCacheClientConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.PrometheusConstants;
import com.paytm.saga.dto.*;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import static com.paytm.saga.common.constant.Constants.MetricConstants.PAYMODE_ORDER;

@Service
@Configuration
@EnableConfigurationProperties(EvictCacheClientConfig.class)
public class EvictCacheServiceImpl implements EvictCacheService {

    private final CustomLogger logger = CustomLogManager.getLogger(EvictCacheServiceImpl.class);
    @Autowired
    @Qualifier("monitoringClient")
    protected StatsDClient monitoringClient;
    @Autowired
    private GenericRestClient restTemplateService;

    @Autowired
    private EvictCacheClientConfig evictCacheClientConfig;

    @Override
    public void evictCache(EvictCacheRequest evictCacheRequest) throws HttpServerErrorException, RestClientException {
        ResponseEntity<EvictCacheResponse> evictCacheResponse = null;
        try {
            evictCacheResponse = restTemplateService.post(evictCacheClientConfig.getUrl(), evictCacheRequest, EvictCacheResponse.class);
        } catch (Exception e) {
            logger.info("Retrying to hit the API again");
            throw new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR);
        }
        if (Objects.nonNull(evictCacheResponse)) {
            HttpStatusCode statusCode = evictCacheResponse.getStatusCode();
            if (Objects.equals(statusCode.value(), Constants.CommonConstants.HTTP_SUCCESS_CODE)) {
                logger.info("[EvictCacheListner.hitBFFCacheCleanAPI] Response Message : {}", evictCacheResponse.getBody().getDisplayMessage());
                logger.info("[EvictCacheListner.hitBFFCacheCleanAPI] Response Code : {} ", statusCode);
            } else {
                logger.info("[EvictCacheListner.hitBFFCacheCleanAPI] Response Message : {}", evictCacheResponse.getBody().getDisplayMessage());
                logger.error("[EvictCacheListner.hitBFFCacheCleanAPI] Response Code : {} ", statusCode);
            }
        }
    }



    @Override
    public void upsertCache(UpsertCacheRequest paymodeCountCacheRequest) throws RestClientException {
        AtomicInteger retryCount = new AtomicInteger(0);
        boolean retry = true;
        while (retry && retryCount.incrementAndGet() <= evictCacheClientConfig.getRetryLimit()) {
            try {
                ResponseEntity<UpsertCacheResponse> paymodeCountCacheResponseResponse = restTemplateService.post(evictCacheClientConfig.getUpsertUrl(), paymodeCountCacheRequest, UpsertCacheResponse.class);
                if (paymodeCountCacheResponseResponse.getStatusCode().value() == Constants.CommonConstants.HTTP_SUCCESS_CODE) {
                    pushCountToDD(PAYMODE_ORDER, PrometheusConstants.STATE.ORDER_UPSERT_SUCCESS);
                    logger.info("[EvictCacheServiceImpl.upsertTxnCount] Response Message : {}", paymodeCountCacheResponseResponse.getBody().getDisplayMessage());
                    logger.info("[EvictCacheServiceImpl.upsertTxnCount] Response Code : {} ", paymodeCountCacheResponseResponse.getStatusCode());
                    retry = false;
                } else {
                    pushCountToDD(PAYMODE_ORDER, PrometheusConstants.STATE.ORDER_UPSERT_FAIL);
                    logger.error("[EvictCacheServiceImpl.upsertTxnCount] Response Code : {} ", paymodeCountCacheResponseResponse.getStatusCode());
                }
            } catch (Exception e) {
                pushCountToDD(PAYMODE_ORDER, PrometheusConstants.STATE.ORDER_UPSERT_FAIL);
                logger.error("[EvictCacheServiceImpl.upsertTxnCount], For request {} exception --> ",paymodeCountCacheRequest.getKeyName(),e);
            }
            if(StringUtils.equalsAnyIgnoreCase(paymodeCountCacheRequest.getInstruction(), AerospikeInstructionsEnum.DECREMENT.label) || StringUtils.equalsAnyIgnoreCase(paymodeCountCacheRequest.getInstruction(),AerospikeInstructionsEnum.INCREMENT.label))
                retry=false;
            if (retry) {
                try {
                    Thread.sleep(evictCacheClientConfig.getRetryInterval());
                } catch (InterruptedException e) {
                    logger.error("[EvictCacheServiceImpl.upsertTxnCount], exception --> ", e);
                }
            }
        }
    }
    protected void pushCountToDD(String metricName, String state) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.STATE + ":" + state;
        monitoringClient.incrementCounter(metricName, tags);
    }
}
