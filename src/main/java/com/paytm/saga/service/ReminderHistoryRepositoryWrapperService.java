package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.repository.ReminderHistoryRepository;
import com.paytm.saga.util.AESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static com.paytm.saga.common.constant.EncryptionConstants.*;

@Service
public class ReminderHistoryRepositoryWrapperService {
	private final CustomLogger logger = CustomLogManager.getLogger(ReminderHistoryRepositoryWrapperService.class);
	private final ReminderHistoryRepository reminderHistoryRepository;
	private final ReminderHistoryEncryptionHandler reminderHistoryEncryptionHandler;
	private final MetricsHelper metricsHelper;
	private final AESUtil aesUtil;

	@Autowired
	public ReminderHistoryRepositoryWrapperService(ReminderHistoryRepository reminderHistoryRepository, ReminderHistoryEncryptionHandler reminderHistoryEncryptionHandler,
												   MetricsHelper metricsHelper,
												   AESUtil aesUtil) {
		this.reminderHistoryRepository = reminderHistoryRepository;
		this.reminderHistoryEncryptionHandler = reminderHistoryEncryptionHandler;
		this.metricsHelper = metricsHelper;
		this.aesUtil = aesUtil;
	}

	public ReminderHistory save(ReminderHistory reminderHistory, int ttl) {
		if(Objects.nonNull(reminderHistory) && FINANCIAL_SERVICES.equalsIgnoreCase(reminderHistory.getService())) {
			try {
				boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(reminderHistory.getCustomerId());
				if(isCCEncryptionRequired) {
					ReminderHistory encReminderHistory = reminderHistoryEncryptionHandler.encryptReminderHistory(reminderHistory);
					return reminderHistoryRepository.save(encReminderHistory, ttl);				}
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerReminderHistoryRepository : save with ttl : Error in encrypting the request", e);
				metricsHelper.pushToDD(ENCRYPTION, REMINDER_HISTORY_SAVE_TO_DB_ERROR);
				throw new AES256Exception("Error in encrypting the request at the time of saving Reminder History with ttl");
			}
		}
		return reminderHistoryRepository.save(reminderHistory, ttl);
	}

	public ReminderHistory save(ReminderHistory reminderHistory) {
		if (Objects.isNull(reminderHistory)) {
			return null;
		}
		if (FINANCIAL_SERVICES.equalsIgnoreCase(reminderHistory.getService())) {
			try {
				boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(reminderHistory.getCustomerId());
				if (isCCEncryptionRequired) {
					ReminderHistory encReminderHistory = reminderHistoryEncryptionHandler.encryptReminderHistory(reminderHistory);
					return reminderHistoryRepository.save(encReminderHistory);
				}
			} catch (AES256Exception e) {
				logger.error("EncryptionHandlerReminderHistoryRepository : save : Error in encrypting the request", e);
				metricsHelper.pushToDD(ENCRYPTION, REMINDER_HISTORY_SAVE_TO_DB_ERROR);
				throw new AES256Exception("Error in encrypting the request at the time of saving Reminder History");
			}
		}
		return reminderHistoryRepository.save(reminderHistory);
	}

	public List<ReminderHistory> findByCustomerIdAndRechargeNumberAndService(final Long customerId, final String rechargeNumber, final String service) {
		if(FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if(isCCEncryptionRequired) {
				try {
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<ReminderHistory> reminderHistoryList = reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumbersList, service);
					return reminderHistoryEncryptionHandler.decryptReminderHistory(reminderHistoryList);
				} catch (ParseException e) {
					logger.error("EncryptionHandlerReminderHistoryRepository : findByCustomerIdAndRechargeNumberAndService : Error in decrypting the date in the request in EncryptionHandlerReminderHistoryRepository", e);
					metricsHelper.pushToDD(PARSING, REMINDER_HISTORY_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("Error in decrypting the date in the request in the EncryptionHandlerReminderHistoryRepository");
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerReminderHistoryRepository : findByCustomerIdAndRechargeNumberAndService : Error in decrypting the request in EncryptionHandlerReminderHistoryRepository", e);
					metricsHelper.pushToDD(DECRYPTION, REMINDER_HISTORY_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("Error in decrypting the request in findByCustomerIdAndRechargeNumberAndService in EncryptionHandlerReminderHistoryRepository");
				}
			}
		}
		return reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, service);
	}

	public List<ReminderHistory> findByCustomerIdAndRechargeNumberInAndServiceIn(final Long customerId, final List<String> rechargeNumber, final List<String> service) {
		if(service.contains(FINANCIAL_SERVICES)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if(isCCEncryptionRequired) {
				try {
					List<String> encryptedRechargeNumbersList = new ArrayList<>();
					for(String rechargeNum : rechargeNumber) {
						encryptedRechargeNumbersList.add(aesUtil.encrypt(rechargeNum));
					}
					List<String> rechargeNumbersList = new ArrayList<>(rechargeNumber);
					rechargeNumbersList.addAll(encryptedRechargeNumbersList);
					List<ReminderHistory> reminderHistoryList = reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumbersList, service);
					return reminderHistoryEncryptionHandler.decryptReminderHistory(reminderHistoryList);
				} catch (ParseException e) {
					logger.error("EncryptionHandlerReminderHistoryRepository : findByCustomerIdAndRechargeNumberAndService : Error in decrypting the date in the request in EncryptionHandlerReminderHistoryRepository", e);
					metricsHelper.pushToDD(PARSING, REMINDER_HISTORY_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("Error in decrypting the date in the request in EncryptionHandlerReminderHistoryRepository");
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerReminderHistoryRepository : findByCustomerIdAndRechargeNumberAndService : Error in decrypting the request in EncryptionHandlerReminderHistoryRepository", e);
					metricsHelper.pushToDD(DECRYPTION, REMINDER_HISTORY_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("Error in decrypting the request in EncryptionHandlerReminderHistoryRepository");
				}
			}
		}
		return reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumber, service);
	}

	public List<ReminderHistory> findByCustomerIdAndRechargeNumberAndServiceAndOperator(final Long customerId, final String rechargeNumber, final String service, final String operator) {
		if(FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
			boolean isCCEncryptionRequired = EncryptionDecision.isDataEncryptionRequired(customerId);
			if(isCCEncryptionRequired) {
				try {
					List<String> rechargeNumbersList = Arrays.asList(rechargeNumber, aesUtil.encrypt(rechargeNumber));
					List<ReminderHistory> reminderHistoryList = reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceAndOperator(customerId, rechargeNumbersList, service, operator);
					return reminderHistoryEncryptionHandler.decryptReminderHistory(reminderHistoryList);
				} catch (ParseException e) {
					logger.error("EncryptionHandlerReminderHistoryRepository : findByCustomerIdAndRechargeNumberAndServiceAndOperator : Error in decrypting the date in the request", e);
					metricsHelper.pushToDD(PARSING, REMINDER_HISTORY_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("Error in decrypting the date in the request in EncryptionHandlerReminderHistoryRepository");
				} catch (AES256Exception e) {
					logger.error("EncryptionHandlerReminderHistoryRepository : findByCustomerIdAndRechargeNumberAndServiceAndOperator : Error in decrypting the request in the EncryptionHandlerReminderHistoryRepository", e);
					metricsHelper.pushToDD(DECRYPTION, REMINDER_HISTORY_SELECT_FROM_DB_ERROR);
					throw new AES256Exception("Error in decrypting the request in findByCustomerIdAndRechargeNumberAndServiceAndOperator in EncryptionHandlerReminderHistoryRepository");
				}
			}
		}
		return reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber, service, operator);
	}
}
