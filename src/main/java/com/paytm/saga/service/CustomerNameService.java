package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.dto.CustomerNameRequest;
import com.paytm.saga.dto.CustomerNameResponse;
import com.paytm.saga.model.Recents;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.paytm.recentutils.service.NickNameService;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class CustomerNameService {

    private final CustomLogger logger = CustomLogManager.getLogger(StatsDClient.class);

    private final RecentsRepositoryWrapperService recentsRepository;



    public CustomerNameService(RecentsRepositoryWrapperService recentsRepository) {
        this.recentsRepository = recentsRepository;

    }

    public String getCustomerName(Long customer_id, String service,String recharge_number,String operator){
        String customerName = null;
        ArrayList<String> nickNames = new ArrayList<>();
        ArrayList<String> consumerNames = new ArrayList<>();
        try {
            List<Recents> recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customer_id,service,recharge_number,operator);
            if (Objects.nonNull(recents)) {
                for (Recents recent : recents) {
                    nickNames.add(getNonEmptyCustomerName(recent.getNickName()));
                    consumerNames.add(getNonEmptyCustomerName(recent.getConsumerName()));
                }
                nickNames.removeAll(Collections.singletonList(null));
                consumerNames.removeAll(Collections.singletonList(null));
                if(nickNames.isEmpty() && consumerNames.isEmpty()) return null;
                if(nickNames.isEmpty()) nickNames.add(null);
                else if (consumerNames.isEmpty()) {
                    consumerNames.add(null);
                }
                NickNameService nickNameService = new NickNameService();
                customerName = nickNameService.getNickName(nickNames.get(0),consumerNames.get(0),null);

            }
        }
        catch(RuntimeException runtimeException){
            logger.error("getCustomerName exception -- {} - customerid :{},Recharge_number:{},Service:{}",runtimeException,customer_id,recharge_number,service);
        }


        return customerName;
    }

    public String getNonEmptyCustomerName(String s){
        if(StringUtils.isBlank(s)) return null;
        return s;
    }

    public CustomerNameResponse getCustomerNameResponse(CustomerNameRequest customerNameRequest) {
        CustomerNameResponse customerNameResponse = new CustomerNameResponse();
        try {
            customerNameResponse.setCustomer_id(customerNameRequest.getCustomer_id());
            customerNameResponse.setRecharge_number(customerNameRequest.getRecharge_number());
            customerNameResponse.setService(customerNameRequest.getService());
            customerNameResponse.setCustomer_name(getCustomerName(customerNameRequest.getCustomer_id(), customerNameRequest.getService(), customerNameRequest.getRecharge_number(),customerNameRequest.getOperator()));

        } catch (RuntimeException runtimeException) {
            logger.error("getCustomerNameResponse exception", runtimeException);
        }
        return customerNameResponse;

    }
}
