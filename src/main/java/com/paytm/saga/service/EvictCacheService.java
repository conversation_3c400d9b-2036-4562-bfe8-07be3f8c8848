package com.paytm.saga.service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.EvictCacheRequest;
import com.paytm.saga.dto.UpsertCacheRequest;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;


public interface EvictCacheService {
    @Retryable(value = {HttpServerErrorException.class, RestClientException.class}, maxAttempts = Constants.CommonConstants.EVICT_CACHE_RETRY)
    public void evictCache(EvictCacheRequest evictCacheRequest) throws HttpServerErrorException, RestClientException;

    public void upsertCache(UpsertCacheRequest paymodeCountCacheRequest) throws HttpServerErrorException, RestClientException;


}
