package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.exception.RestClientException;
import com.paytm.saga.common.metrics.MetricsHelper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.MetricConstants.SAGA_EXTERNAL_API_SERVICE;

@Component("GenericRestClient")
public class GenericRestClient {

    private final CustomLogger logger = CustomLogManager.getLogger(GenericRestClient.class);

    @Autowired
    private MetricsHelper metricsHelper;


    @Autowired
    @Qualifier("GenericRestTemplateClient")
    private RestTemplate restTemplate;

    /**
     * @param baseUrl
     * @param path
     * @param queryParams
     * @param headers
     * @param responseType
     * @return
     */
    public <T, U> U get(String baseUrl, String path, Map<String, Object> queryParams,
                        MultiValueMap<String, String> headers, ParameterizedTypeReference<U> responseType) {

        ResponseEntity<U> responseEntity = null;
        String url = buildUrlWithParams(baseUrl, path, queryParams);

        try {

            HttpHeaders httpHeaders = getHttpHeaders(headers);
            HttpEntity<T> request = new HttpEntity<>(httpHeaders);

            logger.trace("Rest request for url- {}, method- GET", url);

            responseEntity = restTemplate.exchange(url, HttpMethod.GET, request, responseType);
            U response = responseEntity.getBody();

            logger.trace("Rest response for url- {}, method- GET, responsePayload- {}", url, response);

            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, responseEntity.getStatusCodeValue(), url);
            return response;

        } catch (HttpStatusCodeException e) {
            logger.error(
                    "[non_2xx_exception] method: GET, url: {}, response_status: {}, response_message: {}, exceptionMsg: {}",
                    url, e.getStatusCode(), e.getResponseBodyAsString(), e.getMessage());

            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, e.getStatusCode().value(), url);
            throw new RestClientException(e.getStatusCode(), e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("[unknown_exception] method: GET, url: {}", url, e);
            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, HttpStatus.INTERNAL_SERVER_ERROR.value(), url);
            throw new RestClientException(HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    /**
     * @param baseUrl
     * @param path
     * @param queryParams
     * @param headers
     * @param responseType
     * @return
     */
    @Retryable(maxAttempts = 3)
    public <T, U> U retryGet(String baseUrl, String path, Map<String, Object> queryParams,
                        MultiValueMap<String, String> headers, ParameterizedTypeReference<U> responseType) {

        ResponseEntity<U> responseEntity = null;
        String url = buildUrlWithParams(baseUrl, path, queryParams);

        try {

            HttpHeaders httpHeaders = getHttpHeaders(headers);
            HttpEntity<T> request = new HttpEntity<>(httpHeaders);

            logger.trace("Rest request for url- {}, method- GET", url);

            responseEntity = restTemplate.exchange(url, HttpMethod.GET, request, responseType);
            U response = responseEntity.getBody();

            logger.trace("Rest response for url- {}, method- GET, responsePayload- {}", url, response);

            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, responseEntity.getStatusCodeValue(), url);
            return response;

        } catch (HttpStatusCodeException e) {
            logger.error(
                    "[non_2xx_exception] method: GET, url: {}, response_status: {}, response_message: {}, exceptionMsg: {}",
                    url, e.getStatusCode(), e.getResponseBodyAsString(), e.getMessage());

            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, e.getStatusCode().value(), url);
            throw new RestClientException(e.getStatusCode(), e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("[unknown_exception] method: GET, url: {}", url, e);
            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, HttpStatus.INTERNAL_SERVER_ERROR.value(), url);
            throw new RestClientException(HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    private HttpHeaders getHttpHeaders(MultiValueMap<String, String> headers) {
        HttpHeaders httpHeaders = headers == null ? new HttpHeaders() : new HttpHeaders(headers);
        if (httpHeaders.getContentType() == null) {
            // default content-type as application/json
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        }

        return httpHeaders;
    }

    public String getQueryParamsString(Map<String, Object> queryParams) {
        StringBuilder queryParamsStrBuilder = new StringBuilder();

        if (queryParams == null || queryParams.isEmpty()) {
            return queryParamsStrBuilder.toString();
        }

        for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
            if (Objects.nonNull(entry.getKey()) && Objects.nonNull(entry.getValue())) {
                queryParamsStrBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }

        return queryParamsStrBuilder.substring(0, queryParamsStrBuilder.length() - 1);
    }

    public String buildUrlWithParams(String url, String path, Map<String, Object> queryParams) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(url).append("/").append(path);
        String queryParamsStr = getQueryParamsString(queryParams);

        if (!queryParamsStr.isEmpty()) {
            return stringBuilder.append("?").append(queryParamsStr).toString();
        }
        return stringBuilder.toString();
    }

    public <T, U> ResponseEntity<U> post(String url, T payload, Class<U> responseType) {
        try {

            HttpEntity<T> request = new HttpEntity<>(payload);

            logger.trace("Rest request for url- {}, method- POST, payload- {}", url, payload);

            ResponseEntity<U> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, responseType);

            logger.debug("Rest response for url- {}, method- POST, payload- {}", url, payload);

            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, responseEntity.getStatusCodeValue(), url);
            return responseEntity;
        } catch (HttpStatusCodeException e) {
            logger.error(
                    "[non_2xx_exception] method: POST, url: {}, request_payload: {}, response_status: {}, response_message: {}, exceptionMsg: {}",
                    url, payload == null ? "" : payload, e.getStatusCode(), e.getResponseBodyAsString(),
                    e.getMessage());

            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, e.getStatusCode().value(), url);
            throw new RestClientException(e.getStatusCode(), e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("[unknown_exception] method: POST, url: {}, request_payload: {} error message {}", url,
                    payload == null ? "" : payload, e.getMessage(), e);
            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, HttpStatus.INTERNAL_SERVER_ERROR.value(), url);
            throw new RestClientException(HttpStatus.FAILED_DEPENDENCY, e.getMessage());
        }
    }


    public <T, U> ResponseEntity<U> postWithHeader(String url, T payload, HttpHeaders headers, Class<U> responseType) {
        try {

            HttpEntity<T> request = new HttpEntity<>(payload, headers);

            logger.debug("Rest request for url- {}, method- POST, payload- {}, header={}", url, payload, headers);

            ResponseEntity<U> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, responseType);

            logger.debug("Rest response for url- {}, method- POST, payload- {}, header={}", url, payload, headers);

            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, responseEntity.getStatusCodeValue(), url);
            return responseEntity;
        } catch (HttpStatusCodeException e) {
            logger.error(
                    "[non_2xx_exception] method: POST, url: {}, request_payload: {}, request_header={} response_status: {}, response_message: {}, exceptionMsg: {}",
                    url, payload == null ? "" : payload, headers, e.getStatusCode(), e.getResponseBodyAsString(),
                    e.getMessage());

            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, e.getStatusCode().value(), url);
            throw new RestClientException(e.getStatusCode(), e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("[unknown_exception] method: POST, url: {}, request_payload: {}, request_header={} error message {}", url,
                    payload == null ? "" : payload, headers, e.getMessage(), e);
            metricsHelper.recordAPIStatusCode(SAGA_EXTERNAL_API_SERVICE, HttpStatus.INTERNAL_SERVER_ERROR.value(), url);
            throw new RestClientException(HttpStatus.FAILED_DEPENDENCY, e.getMessage());
        }
    }
}
