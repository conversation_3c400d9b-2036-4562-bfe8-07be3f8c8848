package com.paytm.saga.service;


import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.MethodLatencyMetricsAction;
import com.paytm.saga.common.constant.Constants;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Map;

@Service
@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
public class LocalisationManager {

    private final CustomLogger log = CustomLogManager.getLogger(LatestOrderServiceImpl.class);

    @Value("${localisation.service:}")
    private String service;
    @Value("${localisation.environment:}")
    private String environment;
    @Value("${localisation.language:}")
    private String language;
    @Value("${localisation.reloadInterval}")
    private Integer reloadInterval;

    @PostConstruct
    public void initialize() {
        com.paytm.localisation.Service.init(service, language, environment, reloadInterval);
    }

    @MethodLatencyMetricsAction(metricsName = "localization_getMessage")
    public String getMessage(String[] keyComponents, Boolean findSimilarMatch, Map<String, String> payload) {

        log.info("getMessage::keyComponents {}", keyComponents, " payload {}", payload);

        String localisedMessage = com.paytm.localisation.Service.getMessage(keyComponents, findSimilarMatch);

        if (localisedMessage.isEmpty()) {
            localisedMessage = this.getDefaultEnglishMessage(keyComponents, findSimilarMatch);
        }

        return StringSubstitutor.replace(localisedMessage, payload);
    }

    public String getDefaultEnglishMessage(String[] keyComponents, Boolean findSimilarMatch) {
        keyComponents[0] = Constants.LANGUAGE_ENGLISH;
        return com.paytm.localisation.Service.getMessage(keyComponents, findSimilarMatch);
    }


}
