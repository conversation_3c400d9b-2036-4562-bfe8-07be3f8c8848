package com.paytm.saga.service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.service.impl.ServiceConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;

@Service
public class CommonService {

    @Autowired
    private ServiceConfig serviceConfig;

    public  Integer getSmartReminderTTL(CustomerBill customerBill) {
        Date due_date = customerBill.getKey().getDue_date();
        Integer secondsDiff = Math.toIntExact(due_date.getTime()/1000 - (new Date()).getTime()/1000);

        return secondsDiff + (serviceConfig.getBillVisiblityDays(customerBill.getKey().getService()) + 1) * Constants.CommonConstants.SECONDS_IN_DAY ;

    }


}
