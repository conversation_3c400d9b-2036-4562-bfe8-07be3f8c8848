package com.paytm.saga.service;
import java.util.*;
import java.util.stream.Collectors;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.SuggestedCard;
import com.paytm.saga.dto.plans.PlansMapCache;
import org.apache.commons.lang3.time.DateUtils;

import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.LatestOrderResponseWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import com.paytm.saga.dto.LatestOrdersRequest;
import com.paytm.saga.dto.LatestOrdersResponse;
import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.enums.PayType;
import com.paytm.saga.model.Recents;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.BillStateHandler;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.EventStateHandler;
import com.paytm.saga.util.RecentUtils;
import org.springframework.util.StringUtils;

@Service
@DependsOn("keyspaceRecentConfig")
public class LatestOrderServiceImpl implements LatestOrderService{
    private final RecentsRepositoryWrapperService recentsRepository;
    private SuggestedCardService suggestedCardService;

    private MetricsHelper metricsHelper;
    private final CustomLogger logger = CustomLogManager.getLogger(LatestOrderServiceImpl.class);

    @Autowired  // This annotation is important for constructor injection
    public LatestOrderServiceImpl(RecentsRepositoryWrapperService recentsRepository, MetricsHelper metricsHelper, SuggestedCardService suggestedCardService) {
        this.recentsRepository = recentsRepository;
        this.metricsHelper = metricsHelper;
        this.suggestedCardService = suggestedCardService;
    }
    @Autowired
    protected ServiceConfig serviceConfig;

    @Override
    public List<LatestOrdersResponse> getLatestOrders(LatestOrdersRequest request) throws RechargeSagaBaseException {
        LatestOrderResponseWrapper wrapper = getLatestOrderWrapper(request);
        return wrapper.getData();
    }
    @Override
    public LatestOrderResponseWrapper getLatestOrderWrapper(LatestOrdersRequest request) throws RechargeSagaBaseException {
        logger.info("getLatestOrders starts request :: request: {}", request);
    
        if (request.getCustomerId() == null || request.getRechargeNumber() == null || request.getService() == null || request.getOperator() == null) {
            logger.error("getLatestOrderWrapper:: Missing required parameters");
            throw new RechargeSagaBaseException("Missing required parameters");
        }
    
        LatestOrderResponseWrapper wrapper = new LatestOrderResponseWrapper();
    
        try {
            List<Recents> allRecents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(request.getCustomerId(), request.getService(), request.getRechargeNumber(), request.getOperator());
            logger.info("getLatestOrderWrapper:: Retrieved {} recents from the database", allRecents.size());

            boolean statsPushed=false;
            if(allRecents.isEmpty()){
                metricsHelper.recordStatusCodes(Constants.FETCH_LAST_TWO_RECENTS_SERVICE, Constants.NO_RECENT);
                statsPushed=true;
            }
            else if(!hasAnyTxn(allRecents)){
                metricsHelper.recordStatusCodes(Constants.FETCH_LAST_TWO_RECENTS_SERVICE, Constants.RECENT_BUT_NO_TXN);
                statsPushed=true;
            }

            List<Recents> recentsSpecialRecharge = filterRecentsByPlanBucket(allRecents, Constants.SPECIAL_RECHARGE);
            List<Recents> recentsDataPack = filterRecentsByPlanBucket(allRecents, Constants.DATA_PACK);
            logger.info("getLatestOrderWrapper:: Filtered {} recents for special recharge and {} for data pack", recentsSpecialRecharge.size(), recentsDataPack.size());

            List<LatestOrdersResponse> latestOrdersResponse = prepareLatestOrdersResponse(recentsSpecialRecharge, recentsDataPack);
            wrapper.setData(latestOrdersResponse);

            List<SuggestedCard> suggestedCards = suggestedCardService.createSuggestedCardForMissingRecents(latestOrdersResponse, request);
            logger.info("getLatestOrderWrapper:: suggestedCards length {}", suggestedCards.size());
            wrapper.setSuggestedCards(suggestedCards);

            if(!statsPushed && wrapper.getData().size()==0){
                metricsHelper.recordStatusCodes(Constants.FETCH_LAST_TWO_RECENTS_SERVICE, Constants.RECENT_BUT_NO_MATCHING_PLAN_BUCKET);
            }
        } catch (Exception e) {
            logger.error("An error occurred while retrieving recents", e);
            throw new RechargeSagaBaseException("An error occurred while retrieving recents", e);
        }
    
        logger.info("getLatestOrderWrapper:: getLatestOrders completed successfully");
        return wrapper;
    }
    
    public List<LatestOrdersResponse> prepareLatestOrdersResponse(List<Recents> recentsSpecialRecharge, List<Recents> recentsDataPack) {
        List<LatestOrdersResponse> response = new ArrayList<>();
        try {
            LatestOrdersResponse latestSpecialRecharge = getLatestOrdersResponse(recentsSpecialRecharge, "Special Recharge");
            LatestOrdersResponse latestDataPack = getLatestOrdersResponse(recentsDataPack, "Data Pack");
            if (latestSpecialRecharge != null) {
                response.add(latestSpecialRecharge);
            }
    
            if (latestDataPack != null) {
                response.add(latestDataPack);
            }
        } catch (Exception e) {
            logger.error("An error occurred while preparing latest orders response", e);
        }
    
        return response;
    }
    
    public List<Recents> filterRecentsByPlanBucket(List<Recents> recents, String planBucket) {
        if (recents == null || planBucket == null) {
            return Collections.emptyList();
        }
        //
        return recents.stream()
                .filter(recent -> isValidRecent(recent, planBucket))
                .sorted(Comparator.comparing(
                        (Recents recent) -> recent.getUpdatedAt() != null ? recent.getUpdatedAt() : recent.getTxnTime(),
                        Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    private boolean hasAnyTxn(List<Recents> recents) {
        if (recents == null) {
            return false;
        }
        return recents.stream()
                .filter(recent -> (recent.getOrderId() != null)).count()>0;
    }


    private boolean isValidPlanBucket(String planBucket) {
        return Arrays.stream(Constants.VALID_PLAN_BUCKETS).anyMatch(bucket -> bucket.equalsIgnoreCase(planBucket));
    }

    public void setPlanBucket(Recents recent){
        String planBucket=recent.getKey().getPlanBucket();
        if(isValidPlanBucket(planBucket)) {
            logger.info("LatestOrderServiceImpl.setPlanBucket:: valid plan bucket: {}",planBucket);
            metricsHelper.recordSuccessRate(Constants.VALID_PLAN_BUCKET, planBucket.toLowerCase());
            return;
        }

        String key=new StringBuilder()
                .append(recent.getKey().getService())
                .append("_")
                .append(recent.getKey().getOperator())
                .append("_")
                .append(recent.getCircle())
                .append("_")
                .append((int)Math.round(recent.getTxnAmount()))
                .toString();
        String updatedPlan=PlansMapCache.getInstance().getPlanName(key);
        logger.info("LatestOrderServiceImpl.setPlanBucket:: key: {} and updated plan bucket: {}",key, updatedPlan);
        if(!StringUtils.isEmpty(updatedPlan)){
            recent.getKey().setPlanBucket(updatedPlan);
        }
    }
    
    public boolean isValidRecent(Recents recent, String planBucket) {
        logger.info("LatestOrderServiceImpl::isValidRecent: recent: {}, planBucket: {}", recent, planBucket);
        if(recent.getOrderId() != null)
            setPlanBucket(recent);
        if (recent.getOrderId() == null || !planBucket.equals(recent.getKey().getPlanBucket())) {
            if(recent.getOrderId() != null){
                metricsHelper.recordStatusCodes(Constants.FETCH_LAST_TWO_RECENTS_MISMATCHED_PLANS, recent.getKey().getPlanBucket());
            }
            return false;
        }

        Long productId = recent.getProductId();
        if (CVRProductCache.getInstance().isInactivePID(productId)) {
            logger.info("LatestOrderServiceImpl::isValidRecent: inactive productId: {}", productId);
            Long newProductId = ActiveInactivePidMapCache.getInstance().getActivePid(productId);
            if (newProductId != null && !newProductId.equals(productId) && !CVRProductCache.getInstance().isInactivePID(newProductId)) {
                logger.info("LatestOrderServiceImpl::isValidRecent: newProductId: {}", newProductId);
                recent.setProductId(newProductId);
                return true;
            } else {
                metricsHelper.recordSuccessRate(Constants.INVALID_PID, "newProductId.toString()");
                logger.info("reco invalid PID for customer id {} recharge number {} service {} operator {} PID {}",
                        recent.getKey().getCustomerId(), recent.getKey().getRechargeNumber(),
                        recent.getKey().getService(), recent.getKey().getOperator(), recent.getProductId());
                return false;
            }
        }
        return true;
    }
    
    public void updateWidgetSpecificFields(Recents recent) {
        if (!RecentUtils.isBillDue(recent)) {
            recent.setOnlyTxn(true);
            return;
        }
        if(CommonConsts.PAYTM_POSTPAID_SERVICE.equalsIgnoreCase(recent.getKey().getService())){
            return;
        }
        if (isExpiryWithinRange(recent.getPayType(), recent.getKey().getService(), recent.getDueDate()))
            return;
        recent.setOnlyTxn(true);
    }

    public boolean isExpiryWithinRange(Date expiryDate, int startDays, int endDays) {
        Date startDate = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -endDays));

        Date endDate = DateUtil.getEndOfDayDate(DateUtils.addDays(new Date(), startDays));

        logger.trace("isExpiryWithinRange  range start {}, end {}, expiryDate {}", startDate, endDate, expiryDate);

        return expiryDate.compareTo(startDate) >= 0 && expiryDate.compareTo(endDate) <= 0;
    }

    public boolean isExpiryWithinRange(String payType, String service, Date dueDate) {

        if (payType == null || dueDate == null || service == null)
            return false;


        logger.debug("service to check expiry for {}",service);
        return isExpiryWithinRange(dueDate, getBillVisiblityStartDays(service, payType), getBillVisiblityEndDays(service, payType));

    }
    public int getBillVisiblityStartDays(String service, String payType) {

        Integer allowedLength = serviceConfig.getBillVisiblityStartDaysByService(service);
        if (allowedLength == null || allowedLength.intValue() == 0) {
            if (PayType.PREPAID.value.equalsIgnoreCase(payType))
                return serviceConfig.getPrepaidStartDay();
            else
                return serviceConfig.getPostpaidStartDay();
        }

        return allowedLength;
    }

    public int getBillVisiblityEndDays(String service, String payType) {

        Integer allowedLength = serviceConfig.getBillVisiblityEndDaysByService(service);
        if (allowedLength == null || allowedLength.intValue() == 0) {
            if (PayType.PREPAID.value.equalsIgnoreCase(payType))
                return serviceConfig.getPrepaidEndDay();
            else
                return serviceConfig.getPostpaidEndDay();
        }
        return allowedLength;
    }
    
    public LatestOrdersResponse getLatestOrdersResponse(List<Recents> recents, String planBucket) {
        if (recents.isEmpty()) {
            return null;
        }
        Recents latestRecent = recents.get(0);
        LatestOrdersResponse res = new LatestOrdersResponse();
    
        try {
            res.setAmount(latestRecent.getTxnAmount());
            updateWidgetSpecificFields(latestRecent);
            res.setPid(latestRecent.getProductId() != null ? latestRecent.getProductId().toString() : null);
            res.setPlanBucket(planBucket);
            res.setEventState(EventStateHandler.getState(latestRecent));
            res.setBillState(BillStateHandler.getState(latestRecent));
            res.setPlanCircleKey(latestRecent.getRechargeNumber2());
            res.setPlanId(latestRecent.getRechargeNumber3());
            res.setTxnAmount(latestRecent.getTxnAmount());
            if (latestRecent.getTxnTime() != null) {
                res.setTxnDate(DateUtil.formatDate(latestRecent.getTxnTime(), DateFormats.DATE_TIME_FORMAT_2));
            }
            if(latestRecent.getMarkAsPaidTime() != null){
                res.setMarkAsPaidDate(DateUtil.formatDate(latestRecent.getMarkAsPaidTime(), DateFormats.DATE_TIME_FORMAT_2));
            }
            if(latestRecent.getAutomaticDate() !=null){
                res.setAutomaticDate(DateUtil.formatDate(latestRecent.getAutomaticDate(), DateFormats.DATE_TIME_FORMAT_2));
            }
            if (Boolean.TRUE.equals(isSafeDueDate(latestRecent))) {
                res.setDueDate(DateUtil.formatDate(latestRecent.getDueDate(), DateFormats.DATE_TIME_FORMAT_2));
                res.setBillDueDate(DateUtil.formatDate(latestRecent.getDueDate(), DateFormats.DATE_TIME_FORMAT_2));
            }
            logger.info("LatestOrderServiceImp:: response sent {}", res);
        } catch (Exception e) {
            logger.error("An error occurred while creating LatestOrdersResponse", e);
        }
    
        return res;
    }
    
    private Boolean isSafeDueDate(Recents recent) {
        return recent != null && recent.getDueDate() != null;
    }
    

}

