package com.paytm.saga.service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.factory.DummyRechargeNumberGeneratorFactory;
import com.paytm.saga.model.*;
import com.paytm.saga.repository.*;
import com.paytm.saga.service.external.BillsSyncService;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.RecentUtils;
import com.timgroup.statsd.StatsDClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import com.paytm.saga.common.configuration.SchedulerExecutorConfiguration;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.common.constant.FulfillmentTxnStatus;
import com.paytm.saga.common.exception.ErrorMessages;
import com.paytm.saga.common.exception.MarkAsPaidServiceException;
import com.paytm.saga.common.exception.MarkAsPaidServiceWithDisplayMessageException;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.listeners.DropOffDBHelper;
import com.paytm.saga.service.external.ReminderService;
import com.paytm.saga.util.DateUtil;

import jakarta.annotation.PostConstruct;

import static com.paytm.saga.common.constant.Constants.FASTAG_LOW_BALANCE_OPERATOR;

@Service
public class MarkAsPaidService {
	protected final ChannelHistoryRepositoryWrapperService channelHistoryRepository;
	protected final ChannelHistoryFinalizedRepositoryWrapperService channelHistoryFinalizedRepository;
	protected final ReminderService reminderService;
	protected final ReminderHistoryRepositoryWrapperService reminderHistoryRepository;
	protected final SchedulerExecutorConfiguration schedulerExecutorConfiguration;
	protected final PlanExpiryHistoryRepository planExpiryHistoryRepository;
	protected final DCATService dcatService;
	protected final DropOffDBHelper dropOffDBHelper;
	protected final RPSService rpsService;
	private StatsDClient monitoringClient;

	public RecentsRepositoryWrapperService recentsRepository;
	private KafkaProducerService kafkaProducerService;
	private RecentService recentService;
	private final ChannelHistoryService channelHistoryService;
	private BillsSyncService billsSyncService;
	final DummyRechargeNumberGeneratorFactory dummyRechargeNumberGeneratorFactory;
	final ServiceConfig serviceConfig;
	private final CustomLogger logger = CustomLogManager.getLogger(MarkAsPaidService.class);
	@Autowired
	public MarkAsPaidService(final ChannelHistoryRepositoryWrapperService channelHistoryRepository,
							 final ReminderService reminderService,
							 final SchedulerExecutorConfiguration schedulerExecutorConfiguration,
							 final PlanExpiryHistoryRepository planExpiryHistoryRepository, final DCATService dcatService,
							 final ChannelHistoryFinalizedRepositoryWrapperService channelHistoryFinalizedRepository,
							 final ReminderHistoryRepositoryWrapperService reminderHistoryRepository,
							 final DropOffDBHelper dropOffDBHelper,
							 final RPSService rpsService,
							 final RecentsRepositoryWrapperService recentsRepository,
							 final StatsDClient monitoringClient,
							 final KafkaProducerService kafkaProducerService, RecentService recentService, ChannelHistoryService channelHistoryService,final BillsSyncService billsSyncService,
							 final DummyRechargeNumberGeneratorFactory dummyRechargeNumberGeneratorFactory,
							 final ServiceConfig serviceConfig) {
		this.channelHistoryRepository = channelHistoryRepository;
		this.reminderService = reminderService;
		this.schedulerExecutorConfiguration = schedulerExecutorConfiguration;
		this.planExpiryHistoryRepository = planExpiryHistoryRepository;
		this.dcatService = dcatService;
		this.channelHistoryFinalizedRepository = channelHistoryFinalizedRepository;
		this.reminderHistoryRepository = reminderHistoryRepository;
		this.dropOffDBHelper = dropOffDBHelper;
		this.rpsService = rpsService;
		this.recentsRepository = recentsRepository;
		this.kafkaProducerService = kafkaProducerService;
		this.recentService = recentService;
		this.channelHistoryService = channelHistoryService;
		this.monitoringClient = monitoringClient;
		this.billsSyncService = billsSyncService;
		this.dummyRechargeNumberGeneratorFactory = dummyRechargeNumberGeneratorFactory;
		this.serviceConfig = serviceConfig;
	}

	private Integer getTTL(Date date) {
		Integer secondsDiff = Math.abs(Math.toIntExact(date.getTime() / 1000 - (new Date()).getTime() / 1000));
		return secondsDiff + 365 * 24 * 60 * 60;
	}

	public MarkAsPaidResponse intiateProcess(MarkAsPaidRequest markAsPaidRequest) throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {

		String displayMessage = null;

		if(markAsPaidRequest.getCircle() !=null)
			markAsPaidRequest.setCircle(markAsPaidRequest.getCircle().toLowerCase());
		markAsPaidRequest.setPaytype(markAsPaidRequest.getPaytype().toLowerCase());
		markAsPaidRequest.setService(markAsPaidRequest.getService().toLowerCase());
		markAsPaidRequest.setOperator(markAsPaidRequest.getOperator().toLowerCase());
		if(markAsPaidRequest.getService().equalsIgnoreCase("fastag recharge") && FASTAG_LOW_BALANCE_OPERATOR.equalsIgnoreCase(markAsPaidRequest.getOperator())) {
			markAsPaidRequest.setOperator(FASTAG_LOW_BALANCE_OPERATOR);
		}

		if(markAsPaidRequest.getPaytype().equals("prepaid")){
			if(!Arrays.stream(Constants.ServiceTypeConstants.SERVICE_LIST).anyMatch(x -> x.equals(markAsPaidRequest.getService().toLowerCase()))){
				if(markAsPaidRequest.getService().toLowerCase().equals(Constants.ServiceTypeConstants.MOBILE) && markAsPaidRequest.getPlanBucket() == null){
					throw new MarkAsPaidServiceException(ErrorMessages.SAGA_MARK_AS_PAID_ERROR_PLAN_BUCKET_NULL);
				} else if(markAsPaidRequest.getExpiry() == null){
					throw new MarkAsPaidServiceException(ErrorMessages.SAGA_MARK_AS_PAID_ERROR_EXPIRY_NULL);
				}
				if(markAsPaidRequest.getExpiry() != null && !isValidFormat(Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT, markAsPaidRequest.getExpiry())){
					throw new MarkAsPaidServiceException(ErrorMessages.SAGA_MARK_AS_PAID_INCORRECT_EXPIRY_FORMAT);
				}
			}

		} else {
//			if(markAsPaidRequest.getDue_date() == null){
//				throw new MarkAsPaidServiceException(ErrorMessages.SAGA_MARK_AS_PAID_ERROR_DUE_DATE_NULL);
//			}
			if(markAsPaidRequest.getDue_date() != null && !isValidFormat(Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT, markAsPaidRequest.getDue_date())){
				throw new MarkAsPaidServiceException(ErrorMessages.SAGA_MARK_AS_PAID_INCORRECT_DUE_DATE_FORMAT);
			}
			if((markAsPaidRequest.getBill_date() != null && !markAsPaidRequest.getBill_date().isEmpty()&& !isValidFormat(Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT, markAsPaidRequest.getBill_date()))){
				throw new MarkAsPaidServiceException(ErrorMessages.SAGA_MARK_AS_PAID_INCORRECT_BILL_DATE_FORMAT);

			}
			if(markAsPaidRequest.getBill_date()!=null && markAsPaidRequest.getBill_date().isEmpty()) {
				markAsPaidRequest.setBill_date(null);
			}
		}
		schedulerExecutorConfiguration.getScheduledExecutorService().submit(serviceRequestTask(markAsPaidRequest));
		String loggerService = LoggerThreadContext.getServiceName();
		CompletableFuture.runAsync(() -> {
			LoggerThreadContext.setServiceName(loggerService);
			try {
				retryTemplate.execute(context-> serviceRequestTaskFetchFromDb(markAsPaidRequest));
			} catch (Exception e) {
				logger.error("InterruptedException exception for retryTemplate.execute",e);
				Thread.currentThread().interrupt();
			} finally {
				LoggerThreadContext.clear();
			}
		}).whenComplete((task , throwable) -> {
			if(throwable != null){
				logger.error(throwable.getMessage());
			}else{
				String threadName = Thread.currentThread().getName();
				logger.info("Thread Name ={}",threadName);
			}
		});
		try {


			displayMessage = Constants.CommonConstants.DISPLAY_MESSAGE_MAPPING.get("MARK_AS_PAID_SUCCESS");
			MarkAsPaidResponse markAsPaidResponse = new MarkAsPaidResponse(HttpStatus.OK.value(), displayMessage, null,
					null, HttpStatus.OK);
			logger.info("[MarkAsPaidService] MarkAsPaidResponse::intiateProcess request {} response {}" , markAsPaidRequest
					,markAsPaidResponse);
			return markAsPaidResponse;
		}
		catch (Exception e) {
			logger.info("Execption : intiateProcess : ", e);
			throw new MarkAsPaidServiceException(e.toString());
		}
	}

	private RetryTemplate retryTemplate = new RetryTemplate();
	@PostConstruct
	public void init() {
		SimpleRetryPolicy simpleRetryPolicy = new SimpleRetryPolicy();
		simpleRetryPolicy.setMaxAttempts(4);
		FixedBackOffPolicy fixedBackOff = new FixedBackOffPolicy();
		fixedBackOff.setBackOffPeriod(50);
		retryTemplate.setBackOffPolicy(fixedBackOff);
	}

	public void filterRecentsByDueDate(List<Recents> recents) {
		if(recents == null || recents.size() == 0) {
			return;
		}

		// keep recents where due date > now
		recents.removeIf(recent -> recent.getDueDate() != null && !recent.getDueDate().after(new Date()));
	}

    public Runnable serviceRequestTaskFetchFromDb(MarkAsPaidRequest markAsPaidRequest) throws InterruptedException, RecentDataToKafkaException {
        try{
            logger.info("serviceRequestTaskFetchFromDb Retry Number : {} for MarkAsPaid Request {} ", RetrySynchronizationManager.getContext().getRetryCount(),markAsPaidRequest);
            String defaultPlanBucket="";
            List<Recents> recents = null;
            ProductMin productByRequestPid = CVRProductCache.getInstance().getProductDetails(markAsPaidRequest.getProductId());
            if (Constants.CREDIT_CARD_PAYTYPE.equals(markAsPaidRequest.getPaytype())) {
                if (Objects.nonNull(productByRequestPid)) {
                    if (Constants.CARD_NETWORK_DUMMY.equalsIgnoreCase(productByRequestPid.getCardNetwork())) {
                        recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(markAsPaidRequest.getCustomerId(),
                                markAsPaidRequest.getService(), markAsPaidRequest.getRechargeNumber(), productByRequestPid.getBankCode().toLowerCase());
                    }else{
                        recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(markAsPaidRequest.getCustomerId(),
                                markAsPaidRequest.getService(), markAsPaidRequest.getReferenceId());
                    }
                }
            } else if(markAsPaidRequest.getService().equals(Constants.FASTAG)) {
                logger.info("[MarkAsPaidService.serviceRequestTaskFetchFromDb] ::  getting recents by customerId, service and rechargeNumber");
                recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(markAsPaidRequest.getCustomerId(),
                        markAsPaidRequest.getService(), markAsPaidRequest.getRechargeNumber());
                filterRecentsByDueDate(recents);
            } else if(Constants.PREPAID_PAYTYPE.equals(markAsPaidRequest.getPaytype())) {
                recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(markAsPaidRequest.getCustomerId(),
                        markAsPaidRequest.getService(), markAsPaidRequest.getRechargeNumber(), markAsPaidRequest.getOperator(),
                        markAsPaidRequest.getPlanBucket());
            }else if(serviceConfig.getDummyRechargeNumberAllowedServices().contains(markAsPaidRequest.getService())){
                DummyRechargeNumberGenerator dummyRechargeNumberGenerator = dummyRechargeNumberGeneratorFactory.getGenerator(markAsPaidRequest.getService());
				if(dummyRechargeNumberGenerator != null) {
					String dummyRN = dummyRechargeNumberGenerator.generateDummyRN(String.valueOf(markAsPaidRequest.getCustomerId()), markAsPaidRequest.getOperator());
					recents = recentsRepository.findByCustomerIdAndServiceAndRechargeNumbersAndOperatorAndPlanBucket(markAsPaidRequest.getCustomerId(), markAsPaidRequest.getService(), Arrays.asList(markAsPaidRequest.getRechargeNumber(), dummyRN), markAsPaidRequest.getOperator(), defaultPlanBucket);
				}
			} else {
				recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(markAsPaidRequest.getCustomerId(),
					markAsPaidRequest.getService(), markAsPaidRequest.getRechargeNumber(), markAsPaidRequest.getOperator(),
					defaultPlanBucket);
            }

            if(recents != null && !recents.isEmpty()) {
                logger.info("[MarkAsPaidService.serviceRequestTaskFetchFromDb]:: recents fetched: {} ", recents.size());
                boolean isDummyRNAllowedService = serviceConfig.getDummyRechargeNumberAllowedServices().contains(markAsPaidRequest.getService());
                List<Recents> recentsToProcess = isDummyRNAllowedService ? recents : Arrays.asList(recents.get(0));
                boolean anyUpdated = false;
                for (Recents recentObj : recentsToProcess) {
                    Date existingUpdatedAt = recentObj.getUpdatedAt();
                    recentObj.setIsMarkAsPaid(true);
                    recentObj.setMarkAsPaidTime(new Date());
                    recentObj.setMarkAsPaidAmount(markAsPaidRequest.getAmount());
                    recentObj.setUpdatedAt(new Date());
                    recentObj.setMarkAsPaidSource(markAsPaidRequest.getMarkAsPaidSource());
					Boolean isUpdated = recentService.updateMarkAsPaidDetailsInRecents(recentObj, existingUpdatedAt);
                    if (Boolean.FALSE.equals(anyUpdated) && isUpdated) {
                        anyUpdated = true;
                    }
                }
                if (!anyUpdated) {
                    logger.info("[MarkAsPaidService.serviceRequestTaskFetchFromDb] :: Retrying again as no recents were updated for markAsPaidRequest: {}",
                            markAsPaidRequest);
                    throw new InterruptedException();
                }
				logger.info("MARK_AS_PAID_TIME and IS_MARK_AS_PAID updated in Recents for customerId {}",markAsPaidRequest.getCustomerId());
            }
            kafkaProducerService.sendMessage(Long.toString(markAsPaidRequest.getCustomerId()));
            pushToDD("markAsPaid_service","SUCCESS");

        }catch (Exception e) {
            logger.error("[MarkAsPaidService.intiateProcess] kafka cache clean event publish error for cusomter id {}, recharge number {}, service {}, Exception",
                    markAsPaidRequest.getCustomerId(), markAsPaidRequest.getRechargeNumber(),
                    markAsPaidRequest.getService(), e);
            pushToDD("markAsPaid_service","ERROR");
            throw e;
        }
        return null;
    }

	public Callable serviceRequestTask(MarkAsPaidRequest markAsPaidRequest) {

		return new Callable<Void>() {
			@Override
			public Void call() {
				AtomicBoolean fail = new AtomicBoolean(false);
				AtomicReference<ErrorMessages> errorCode = new AtomicReference<>();
				List<Future<Object>> futureResponseList = new ArrayList<>();
				ProductMin productByRequestPid = CVRProductCache.getInstance().getProductDetails(markAsPaidRequest.getProductId());
				if(!Arrays.stream(Constants.ServiceTypeConstants.SERVICE_LIST).anyMatch(x -> x.equals(markAsPaidRequest.getService().toLowerCase()))||!markAsPaidRequest.getPaytype().equals("prepaid")) {
					Future<Object> reminderServiceFutureResponse =null;
					Future<Object> billsSyncServiceFutureResponse = null;
					if(Objects.nonNull(FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED)) && FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED).contains(String.valueOf(markAsPaidRequest.getProductId()))){
						billsSyncServiceFutureResponse = schedulerExecutorConfiguration.getScheduledExecutorService().submit(billsSyncService.markAsPaidTask(markAsPaidRequest.getRechargeNumber(),markAsPaidRequest.getCustomerId(),markAsPaidRequest.getService(),markAsPaidRequest.getOperator()));

					}
					else {
						reminderServiceFutureResponse = schedulerExecutorConfiguration.getScheduledExecutorService()
								.submit(reminderService.markAsPaidTask(markAsPaidRequest.getRechargeNumber(),
										markAsPaidRequest.getProductId(), markAsPaidRequest.getOperator(),
										markAsPaidRequest.getPaytype(), markAsPaidRequest.getCustomerId(),
										markAsPaidRequest.getPlanBucket(), markAsPaidRequest.getReferenceId(),
										markAsPaidRequest.getService()));

					}
				


					futureResponseList.add(reminderServiceFutureResponse);
					futureResponseList.add(billsSyncServiceFutureResponse);

					Integer index = 0;

					if (Objects.nonNull(futureResponseList)) {
						futureResponseList.forEach(futureResponseObj -> {
							try {
								if(Objects.nonNull(futureResponseObj)){
								Object response = futureResponseObj.get(); //blocking operation
//								if (index == 0) {
									if (response == null) {
										fail.set(true);
										if (markAsPaidRequest.getPaytype().toLowerCase().equals("prepaid")) {
											errorCode.set(ErrorMessages.SAGA_MARK_AS_PAID_PREPAID_REMINDER_API_ERROR);
										} else {
											errorCode.set(ErrorMessages.SAGA_MARK_AS_PAID_POSTPAID_REMINDER_API_ERROR);
										}
									}
								}
							} catch (InterruptedException | ExecutionException e) {
								logger.error("[MarkAsPaidService serviceRequest] Exception occured {}", e.getMessage());
								return;
							}
						});
					}
				}

				if (new Boolean(false) == fail.get() || new Boolean(true) == fail.get()) {
					try {
						createChannelHistoryForMarkAsPaid(markAsPaidRequest);
						createDropOffForMarkAsPaid(markAsPaidRequest);
					} catch (Exception e) {
						if(markAsPaidRequest.getPaytype().toLowerCase().equals("prepaid")) {
							errorCode.set(ErrorMessages.SAGA_MARK_AS_PAID_PREPAID_CASSANDRA_ERROR);
						} else {
							errorCode.set(ErrorMessages.SAGA_MARK_AS_PAID_POSTPAID_CASSANDRA_ERROR);
						}
						logger.error("[MarkAsPaidService serviceRequest] Exception occured {}", errorCode.get());
						}
				} else {
						logger.error("[MarkAsPaidService serviceRequest external API call] Exception occured {}", errorCode.get());
				}
				return null;
			}
		};


	}
	
	public void createDropOffForMarkAsPaid(MarkAsPaidRequest markAsPaidRequest) {
		String rechargeNumber=markAsPaidRequest.getRechargeNumber();
		Map<String, String> billsObj = new HashMap<String, String>();
	    billsObj.put(Constants.CommonConstants.BILLS_OBJ_MARK_AS_PAID_DROP_OFF_FLAG, "true");
	    if(markAsPaidRequest.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
			if(markAsPaidRequest.getReferenceId() != null){
				rechargeNumber=markAsPaidRequest.getReferenceId();
			}
			billsObj.put(Constants.CommonConstants.BILLS_OBJ_MCN, markAsPaidRequest.getRechargeNumber());
			billsObj.put(Constants.CommonConstants.BILLS_OBJ_CIN, markAsPaidRequest.getReferenceId());
		}
		DropOff dropOff=new DropOff();
		//dropOff.setCategoryId(categoryId);
        dropOff.setCustomerId(markAsPaidRequest.getCustomerId());
        dropOff.setProductId(markAsPaidRequest.getProductId());
        dropOff.setCreatedTime(new Date());
        dropOff.setStatus("7");
        dropOff.setRechargeNumber(rechargeNumber);
        dropOff.setService(markAsPaidRequest.getService());
        dropOff.setOperator(markAsPaidRequest.getOperator());
        dropOff.setPaytype(markAsPaidRequest.getPaytype());
		if(markAsPaidRequest.getAmount() != null) {
			dropOff.setAmount(markAsPaidRequest.getAmount().toString());
		}
        dropOff.setCircle(markAsPaidRequest.getCircle());
        dropOff.setEventType(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE);
        dropOff.setTransactionTime(new Date());
        if(markAsPaidRequest.getPlanBucket() != null) {
            billsObj.put("plan_bucket", markAsPaidRequest.getPlanBucket());
        }
        dropOff.setBillsObj(billsObj);
        dropOffDBHelper.insertDropOff(dropOff);
        
	}
	
	public ReminderStatusResponse createDropOffForDoNotRemindme(ReminderStatusRequest reminderStatusRequest) {
		DropOff dropOff=new DropOff();
		//dropOff.setCategoryId(categoryId);
        dropOff.setCustomerId(reminderStatusRequest.getCustomerId());
        dropOff.setProductId(reminderStatusRequest.getProductId());
        dropOff.setCreatedTime(new Date());
        dropOff.setStatus(FulfillmentTxnStatus.SUCCESS);
        dropOff.setRechargeNumber(reminderStatusRequest.getRechargeNumber());
        dropOff.setService(reminderStatusRequest.getService().toLowerCase());
        dropOff.setOperator(reminderStatusRequest.getOperator().toLowerCase());
        dropOff.setPaytype(reminderStatusRequest.getPaytype().toLowerCase());
        if(reminderStatusRequest.getAmount() != null) {
            dropOff.setAmount(reminderStatusRequest.getAmount().toString());        	
        }else {
        	dropOff.setAmount("0");
        }

        dropOff.setCircle(reminderStatusRequest.getCircle());
        dropOff.setEventType(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE);
        dropOff.setTransactionTime(new Date());
        Map<String, String> billsObj = new HashMap<String, String>();
        billsObj.put(Constants.CommonConstants.DONOT_REMINDME_FLAG, "true");
        if(reminderStatusRequest.getPlanBucket() != null) {
            billsObj.put(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY, reminderStatusRequest.getPlanBucket());
        }else {
        	billsObj.put(Constants.CommonConstants.BILLS_OBJ_PLAN_BUCKET_KEY, Constants.CommonConstants.DEFAULT_PLAN_BUCKET);
        }
        if(reminderStatusRequest.getPaytype() !=null 
        		&& reminderStatusRequest.getPaytype().equalsIgnoreCase("credit card")) {
        	String cin=reminderStatusRequest.getReferenceId();
        	String mcn=reminderStatusRequest.getRechargeNumber();
        	billsObj.put("cin", cin);
        	billsObj.put("mcn", mcn);
        	dropOff.setRechargeNumber(cin);
        }
        dropOff.setBillsObj(billsObj);
        dropOffDBHelper.insertDropOff(dropOff);

        String displayMessage = Constants.CommonConstants.DISPLAY_MESSAGE_MAPPING.get("DO_NOT_REMIND_ME_SUCCESS");
        ReminderStatusResponse reminderStatusResponse = new ReminderStatusResponse(HttpStatus.OK.value(), displayMessage, null,
				null, HttpStatus.OK);
		
        return reminderStatusResponse;
	}

	public void createChannelHistoryForMarkAsPaid(MarkAsPaidRequest markAsPaidRequest) {
		if (Arrays.stream(Constants.ServiceTypeConstants.CHANNEL_HISTORY_SERVICE_LIST).anyMatch(x -> x.equals(markAsPaidRequest.getService().toLowerCase()))&& markAsPaidRequest.getPaytype().equals("prepaid")) {
			createMarkAsPaidInChannelHistoryForPrepaid(markAsPaidRequest);
		} else {
			createMarkAsPaidInChannelHistoryForPostpaid(markAsPaidRequest);
		}

	}

	private void createMarkAsPaidInChannelHistoryForPostpaid(MarkAsPaidRequest markAsPaidRequest) {
		Date now = new Date();
		String rechargeNumber=markAsPaidRequest.getRechargeNumber();
		if(markAsPaidRequest.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
			rechargeNumber=markAsPaidRequest.getReferenceId();
		}

		List<ChannelHistory> channelHistories = channelHistoryRepository
				.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(markAsPaidRequest.getCustomerId(),
						rechargeNumber,
						markAsPaidRequest.getService().toLowerCase(), false);
		boolean notPaidEventFound = false;
		if (!notPaidEventFound) {

			List<ReminderHistory> reminderHistories = reminderHistoryRepository
					.findByCustomerIdAndRechargeNumberAndServiceAndOperator(markAsPaidRequest.getCustomerId(),
							markAsPaidRequest.getRechargeNumber(),
							markAsPaidRequest.getService().toLowerCase(),
							markAsPaidRequest.getOperator().toLowerCase());
			if(markAsPaidRequest.getPaytype() !=null && markAsPaidRequest.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
				reminderHistories = filterOutByCin(reminderHistories, markAsPaidRequest.getReferenceId());				
			}
			if (reminderHistories != null && !reminderHistories.isEmpty()) {
				Map<String, String> billsObj = new HashMap<String, String>();
				if(markAsPaidRequest.getPaytype().equals(Constants.CREDIT_CARD_PAYTYPE)) {
					billsObj.put(Constants.CommonConstants.BILLS_OBJ_MCN, markAsPaidRequest.getRechargeNumber());
					billsObj.put(Constants.CommonConstants.BILLS_OBJ_CIN, markAsPaidRequest.getReferenceId());
				}
				if (markAsPaidRequest.getDue_date() != null)
					billsObj.put(Constants.ReminderConstants.DUE_DATE,
							DateUtil.dateFormatter(
									DateUtil.stringToDate(markAsPaidRequest.getDue_date(),
											Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT),
									DateFormats.DATE_TIME_FORMAT_2));
				if (markAsPaidRequest.getBill_date() != null)
					billsObj.put(Constants.ReminderConstants.BILL_DATE,
							DateUtil.dateFormatter(
									DateUtil.stringToDate(markAsPaidRequest.getBill_date(),
											Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT),
									DateFormats.DATE_TIME_FORMAT_2));
				
				if(markAsPaidRequest.getCircle() !=null) {
					markAsPaidRequest.setCircle(markAsPaidRequest.getCircle().toLowerCase());
				}
				billsObj.put("markedAsPaidTime", new Date().toString());
				ReminderHistory reminderHistory = reminderHistories.get(0);
				Double minDueAmount = reminderHistory.getCurrentMinBillAmount();
				if (minDueAmount != null) {
					billsObj.put(Constants.ReminderConstants.MIN_DUE_AMOUNT, minDueAmount.toString());
				}
				ChannelHistory channelHistory = new ChannelHistoryBuilder()
						.setCustomerId(markAsPaidRequest.getCustomerId())
						.setRechargeNumber(rechargeNumber)
						.setProductId(markAsPaidRequest.getProductId()).setOrderId(1L).setCreatedTime(now)
						.setOperator(markAsPaidRequest.getOperator().toLowerCase())
						.setPaytype(markAsPaidRequest.getPaytype().toLowerCase())
						.setService(markAsPaidRequest.getService().toLowerCase())
						.setAmount(String.valueOf(markAsPaidRequest.getAmount()))
						.setEventType(EventTypes.MARKED_AS_PAID).setTransactionTime(now).setTransactionUpdateTime(now)
						.setItemId(1L).setFinalisedState(false).setBillsObj(billsObj)
						.setCircle(markAsPaidRequest.getCircle()).build();
				channelHistory.setStatus(FulfillmentTxnStatus.SUCCESS);
				channelHistoryService.save(channelHistory, markAsPaidRequest.getService());


				reminderHistory.setStatus(15);
				reminderHistory.setUpdatedAt(new Date());
				reminderHistoryRepository.save(reminderHistory);

			}
		}

	}

	private void createMarkAsPaidInChannelHistoryForPrepaid(MarkAsPaidRequest markAsPaidRequest) {
		Date now = new Date();
		Map<String, String> billsObj = new HashMap<String, String>();
		if (markAsPaidRequest.getPlanBucket() != null) {
			billsObj.put("plan_bucket", markAsPaidRequest.getPlanBucket());
		}
		Map<String, String> displayValues = new HashMap<String, String>();

		try {
			DCATGetPlanResponse dcatGetPlanResponse = this.getPlanDetailsFromDcat(markAsPaidRequest.getService(),
					markAsPaidRequest.getOperator(), markAsPaidRequest.getAmount(), markAsPaidRequest.getCircle(),
					markAsPaidRequest.getRechargeNumber());
			if(Objects.nonNull(dcatGetPlanResponse)) {
				if (dcatGetPlanResponse.getPlan_bucket() != null)
					billsObj.put("plan_bucket", dcatGetPlanResponse.getPlan_bucket());
				if (dcatGetPlanResponse.getValidity() != null)
					displayValues.put("validity", dcatGetPlanResponse.getValidity());
				if (dcatGetPlanResponse.getSms() != null)
					displayValues.put("sms", dcatGetPlanResponse.getSms());
				if (dcatGetPlanResponse.getData() != null)
					displayValues.put("data", dcatGetPlanResponse.getData());
				if (dcatGetPlanResponse.getTalktime() != null)
					displayValues.put("talktime", dcatGetPlanResponse.getTalktime());
				if (dcatGetPlanResponse.getAddon_benefit1() != null)
					displayValues.put("addon_benefit1", dcatGetPlanResponse.getAddon_benefit1());
				if (dcatGetPlanResponse.getAddon_benefit2() != null)
					displayValues.put("addon_benefit2", dcatGetPlanResponse.getAddon_benefit2());
				if (dcatGetPlanResponse.getAddon_benefit3() != null)
					displayValues.put("addon_benefit3", dcatGetPlanResponse.getAddon_benefit3());
				if (dcatGetPlanResponse.getAddon_benefit4() != null)
					displayValues.put("addon_benefit4", dcatGetPlanResponse.getAddon_benefit4());
				if (dcatGetPlanResponse.getDescription() != null)
					displayValues.put("description", dcatGetPlanResponse.getDescription());
			}
		} catch (Exception e) {
			logger.error("error in hitting dact service,={}",e.getMessage());
		}

		ChannelHistory channelObj = new ChannelHistory(markAsPaidRequest.getCustomerId(),
				markAsPaidRequest.getRechargeNumber(), markAsPaidRequest.getProductId(), 1L, now, null,
				null, null, null, markAsPaidRequest.getOperator().toLowerCase(),
				markAsPaidRequest.getPaytype().toLowerCase(), markAsPaidRequest.getService().toLowerCase(),
				String.valueOf(markAsPaidRequest.getAmount()), billsObj, null,
				Constants.CommonConstants.MARK_AS_PAID_MESSAGE_TYPE, now, now, false, 1L, displayValues,
				Objects.nonNull(markAsPaidRequest.getCircle()) ? markAsPaidRequest.getCircle().toLowerCase() : null, null, null);
		channelObj.setStatus(FulfillmentTxnStatus.SUCCESS);
		channelHistoryService.save(channelObj,markAsPaidRequest.getService());

	}
	private List<ReminderHistory> filterOutByCin(List<ReminderHistory> reminderHistories,String cin){
		List<ReminderHistory> res =new ArrayList<ReminderHistory>();
		if (reminderHistories != null && !reminderHistories.isEmpty()) {
			for(ReminderHistory reminderHistory:reminderHistories) {
				if((reminderHistory.getReference_id()!=null && reminderHistory.getReference_id().equals(cin))
				 || (reminderHistory.getPar() !=null && reminderHistory.getPar().equals(cin))) {
					res.add(reminderHistory);
					return res;
				}
			}
		}
		return res;
	}
	private DCATGetPlanResponse getPlanDetailsFromDcat(String service, String operator, double amount, String circle,
			String recharge_number) {
		DCATGetPlanResponse dcatGetPlanResponse = rpsService.getPlanDetails(
				Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service), Constants.CommonConstants.DCAT_VERSION,
				Constants.CommonConstants.DCAT_CHANNEL, operator, "" + ((int) amount), circle,null ,recharge_number,service);
		if (dcatGetPlanResponse != null) {
			logger.info("[PlanValidityListeners.insertDataIntoCassandra] MarkAsPaid rpsPlanResponse  success  , :"
					+ recharge_number + (dcatGetPlanResponse.toString()));

			return dcatGetPlanResponse;
		} else {
			logger.error(
					"[PlanValidityListeners.insertDataIntoCassandra] rpsPlanResponse null  , :" + recharge_number);
		}
		return null;
	}

	public void createPlanValidityHistoryForMarkAsPaid(MarkAsPaidRequest markAsPaidRequest) {
		Date now = new Date();
		Map<String, String> displayValues = new HashMap<String, String>();

		PlanExpiryHistory planExpiryObj = new PlanExpiryHistory(markAsPaidRequest.getRechargeNumber(),
				markAsPaidRequest.getService().toLowerCase(), markAsPaidRequest.getCircle().toLowerCase(),
				markAsPaidRequest.getOperator().toLowerCase(), markAsPaidRequest.getPlanBucket(), now,
				markAsPaidRequest.getCustomerId(), null, null, now, displayValues, markAsPaidRequest.getAmount());
		planExpiryHistoryRepository.save(planExpiryObj);

	}

	private static boolean isValidFormat(String format, String value) {
		Date date = null;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(format);
			if (value != null) {
				date = sdf.parse(value);
				if (!value.equals(sdf.format(date))) {
					date = null;
				}
			}

		} catch (ParseException ex) {
		}
		return date != null;
	}


	private void pushToDD(String metricName, String state) {
		String[] tags = new String[1];
		tags[0] = Constants.MetricConstants.STATE + ":" + state;
		monitoringClient.incrementCounter(metricName, tags);
	}


}