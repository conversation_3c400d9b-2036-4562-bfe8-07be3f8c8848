package com.paytm.saga.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.exception.SmsCardException;
import com.paytm.saga.service.external.ReminderService;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;

@Service
public class SmsCardService {
	private static final CustomLogger logger = CustomLogManager.getLogger(SmsCardService.class);

	private ReminderService reminderService;

	@Autowired
	public SmsCardService(@NonNull ReminderService reminderService) {
		this.reminderService = reminderService;
	}

	public SavedCardResponse getSmsCards(long customerID, List<String> services) throws SmsCardException {
		SavedCardResponse savedCardResponse = new SavedCardResponse();
		List<SmsCard> smsCards = reminderService.fetchSmsCards(customerID, services);
		List<SavedCardApiResponse> savedCardApiResponses = new ArrayList<SavedCardApiResponse>();
		if (smsCards != null) {
			for (SmsCard smsCard : smsCards) {
				try {
					if(smsCard.getCustomer_other_info()!=null) {
						try {
							smsCard.setCustomerOtherInfo(JsonUtils.parseJson(smsCard.getCustomer_other_info(), CustomerOtherInfo.class));
						}catch(Exception e) {
							logger.error("[SmsCardService.getSmsCards] Exception: in parsing getCustomer_other_info", e);
						}

					}

					if(smsCard.getExtra()!=null) {
						try {
							smsCard.setExtra_info(JsonUtils.parseJson(smsCard.getExtra(), ExtraInfo.class));
						}catch(Exception e) {
							logger.error("[SmsCardService.getSmsCards] Exception: in parsing extraInfo", e);
						}

					}

					SavedCardBillStateEnum state = getBillState(smsCard);
					if (state == SavedCardBillStateEnum.NO_BILL) {
						smsCard.setDue_amount(0.0);
						smsCard.setCustomerOtherInfo(null);
						smsCard.setDue_date(null);
						smsCard.setBillDate(null);
					}

					SavedCardApiResponse savedCardApiResponse = formatResponse(smsCard, state);
					savedCardApiResponses.add(savedCardApiResponse);
				} catch (RuntimeException e) {
					logger.error("[SmsCardService.getSmsCards] Exception:", e);
				}

			}
		}
		savedCardResponse.setSavedCardApiResponses(savedCardApiResponses);
		return savedCardResponse;
	}

	private SavedCardBillsObj setBill(SmsCard smsCard, SavedCardBillStateEnum state) {
		SavedCardBillsObj savedCardBillsObj = new SavedCardBillsObj();

		if(smsCard.getDue_amount() != null) savedCardBillsObj.setAmount(smsCard.getDue_amount());
		else savedCardBillsObj.setAmount(smsCard.getAmount());

		if (smsCard.getCustomerOtherInfo() != null) {
			savedCardBillsObj.setMin_due_amount(smsCard.getCustomerOtherInfo().getCurrentMinBillAmount());
			savedCardBillsObj.setOriginal_min_due_amount(smsCard.getCustomerOtherInfo().getCurrentMinBillAmount());
		}

		savedCardBillsObj.setOriginal_due_amount(smsCard.getAmount());
		if (smsCard.getDue_date() != null)
			savedCardBillsObj
					.setDue_date(DateUtil.dateFormatter(smsCard.getDue_date(), DateFormats.DATE_TIME_FORMAT_2));
		if (smsCard.getBillDate() != null)
			savedCardBillsObj
					.setBill_date(DateUtil.dateFormatter(smsCard.getBillDate(), DateFormats.DATE_TIME_FORMAT_2));
		if(smsCard.getExtra_info()!=null){
			savedCardBillsObj.setLast_paid_amount(smsCard.getExtra_info().getLast_paid_amount());
		}
		savedCardBillsObj.setBillState(state);
		return savedCardBillsObj;
	}

	private ProductInfo setProductInfo(SmsCard smsCard) {
		ProductInfo productInfo = new ProductInfo();
		productInfo.setBankName(smsCard.getBank_name());
		if(smsCard.getPaytype().equalsIgnoreCase(Constants.CREDIT_CARD_PAYTYPE)) {
			productInfo.setCardType(Constants.CC_CARD);
		}
		productInfo.setService(smsCard.getService());
		productInfo.setCategoryId(
				Long.parseLong(Constants.CommonConstants.DCAT_GROUPING_ID_MAP.get(smsCard.getService())));
		productInfo.setOperator(smsCard.getOperator());
		productInfo.setProductId(smsCard.getProduct_id());
		return productInfo;
	}

	private SavedCardApiResponse formatResponse(SmsCard smsCard, SavedCardBillStateEnum state) {
		SavedCardApiResponse savedCardApiResponse = new SavedCardApiResponse();
		List<SavedCardBillsObj> bills = new ArrayList<SavedCardBillsObj>();
		bills.add(setBill(smsCard, state));
		savedCardApiResponse.setBills(bills);
		savedCardApiResponse.setOperator(smsCard.getOperator());
		savedCardApiResponse.setPriority(Constants.SMS_CARD_PRIORIY);
		savedCardApiResponse.setProduct(setProductInfo(smsCard));
		savedCardApiResponse.setRechargeNumber(smsCard.getRecharge_number());
		savedCardApiResponse.setRechargeNumberForDisplay(smsCard.getRecharge_number());
		savedCardApiResponse.setType(Constants.SMS_CARD);
		if(smsCard.getStatus()==14&&savedCardApiResponse.getBills().get(0).getLast_paid_amount()!=null){
			savedCardApiResponse.setPaidOutside(Constants.PAID_OUTSIDE_PAYTM);
		}
		return savedCardApiResponse;
	}

	private String formatMCN(String rechargeNumber) {
		rechargeNumber = rechargeNumber.length() > 4
				? rechargeNumber.substring(0, rechargeNumber.length() - 4) + " "
						+ rechargeNumber.substring(rechargeNumber.length() - 4, rechargeNumber.length())
				: rechargeNumber;
		return "XXXX XXXX XXXX XXXX".substring(0, (19 - rechargeNumber.length()))+rechargeNumber;
	}

	private SavedCardBillStateEnum getBillState(SmsCard smsCard) {
		SavedCardBillStateEnum state = null;

		if (smsCard.getAmount() <= 0) {
			if (smsCard.getBillDate() == null || smsCard.getDue_date() == null) {
				state = SavedCardBillStateEnum.NO_BILL;
			} else {
				state = SavedCardBillStateEnum.FULLY_PAID;
			}
		} else {
			state = SavedCardBillStateEnum.MIN_DUE;
		}
		Date dueDate = smsCard.getDue_date();

		if (dueDate != null && !state.equals(SavedCardBillStateEnum.NO_BILL)) {
			Date currentDate = new Date();
			if ((currentDate.getTime() - dueDate.getTime())
					/ (1000 * 60 * 60 * 24) > Constants.SAVED_CARD_BILL_VISIBLE_THRESHOLD_DAYS) {
				state = SavedCardBillStateEnum.NO_BILL;
			}
		}

		return state;
	}
}
