package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.dto.RecentReconResponse;
import com.paytm.saga.dto.RecentReconDataCache;
import com.paytm.saga.model.peppipostemail.MailAddress;
import com.paytm.saga.model.peppipostemail.MailAttachment;
import com.paytm.saga.model.peppipostemail.MailContent;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.MailManager;
import com.paytm.saga.util.CsvUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.*;

@Service
public class RecentReconScheduler {

    private final CustomLogger logger = CustomLogManager.getLogger(RecentReconScheduler.class);

    @Autowired
    private MailManager mailManager;
    private List<MailAddress> mailingList = new ArrayList<>();

    @Value("#{${recentRecon.mail.to}}")
    private Map<String, String> to;

    @PostConstruct
    public void onStartup() {
        for (Map.Entry<String, String> entry : to.entrySet()) {
            mailingList.add(new MailAddress(entry.getKey(), entry.getValue()));
        }

    }

    @Scheduled(cron = "0 0/2 * * * *")
    public void recentRecon() {
        logger.info("[RecentRecon]: RecentRecon starting");
        List<RecentReconResponse> reportData = RecentReconDataCache.getInstance().getReportData();
        Integer recordsProcessed = RecentReconDataCache.getInstance().getRecordsProcessed();
        if (reportData.isEmpty()) {
            logger.info("[RecentRecon]: No data to send, RecentRecon ends");
            return;
        }

        try {
            String date = DateUtil.dateFormatter(new Date(), "dd MMMM yyyy hh:mm");
            String csvString = CsvUtil.toCSV(reportData, RecentReconResponse.class);
            Base64 base64 = new Base64();
            byte[] csvByteArray = csvString.getBytes();
            MailAttachment attachment = new MailAttachment("Cassandra Recon Report " + date + ".csv", base64.encodeAsString(csvByteArray));
            MailContent content = new MailContent("html", getMailTemplateHtml(reportData, recordsProcessed));
            mailManager.sendMailWithAttachment(mailingList, "Cassandra Recon Report at " + date,
                    Collections.singletonList(content), attachment);
        } catch (Exception e) {
            logger.error("[RecentRecon]: Exception occured in RecentRecon,Exception{}", e.getMessage());
            return;
        }
        logger.info("33[RecentRecon]:Size of reportData={}", reportData.size());
        RecentReconDataCache.getInstance().clearReportData(reportData);
        RecentReconDataCache.getInstance().updateRecordsProcessed(recordsProcessed);
        logger.info("[RecentRecon]: RecentRecon ends");


    }

    public String getMailTemplateHtml(List<RecentReconResponse> reportData, Integer recordsProcessed) {
        StringBuilder templateHtml = new StringBuilder();
        templateHtml.append("<html>" +
                "<head><style>" +
                "table {" +
                "width:100%;" +
                "}" +
                "table, th, td {" +
                "border: 1px solid black;" +
                "}" +
                "th {" +
                "text-align: center;" +
                "}" +
                "td {" +
                "text-align: left;" +
                "}" +
                "</style>" +
                "<body>" +
                "<p> <b>Total Orders Processed since last error: </b>" + recordsProcessed + "<br>" +
                "<b>Total Entries with Error for last 2 minutes: </b>" + reportData.size() + "<br></p><table border=1>"
        );
        if (!reportData.isEmpty()) {
            templateHtml.append("<tr><th>Key</th>" +
                    "<th>Operation Type</th>" +
                    "<th>OrderId</th>" +
                    "<th>OMS Recharge Number</th>" +
                    "<th>Recent Recharge Number</th>" +
                    "<th>OMS CustomerId</th>" +
                    "<th>Recent CustomerId</th>" +
                    "<th>OMS MCN</th>" +
                    "<th>Recent MCN</th>" +
                    "<th>Errors</th></tr>");


            for (RecentReconResponse res : reportData) {
                logger.info("[RecentReconScheduler]: All Data: Operation Type={}", res.getOperationType());
                templateHtml.append("<tr><td>" + res.getKey() + "</td>" +
                        "<td>" + res.getOperationType() + "</td>" +
                        "<td>" + res.getOrderId() + "</td>" +
                        "<td>" + res.getOmsRechargeNumber() + "</td>" +
                        "<td>" + res.getRecentRechargeNumber() + "</td>" +
                        "<td>" + res.getOmsCustomerId() + "</td>" +
                        "<td>" + res.getRecentCustomerId() + "</td>" +
                        "<td>" + res.getOmsMcn() + "</td>" +
                        "<td>" + res.getRecentMcn() + "</td>" +
                        "<td>" + res.getErrors() + "</td></tr>");
            }
            templateHtml.append("</table></p></body></head></html>");
        }
        return templateHtml.toString();

    }

}

