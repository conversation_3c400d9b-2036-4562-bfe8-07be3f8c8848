package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.MethodLatencyMetricsAction;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.catalogue.CvrDataResponse;
import com.paytm.saga.util.CVRUtils;
import com.paytm.saga.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Service
public class CVRDataService {

    private final CustomLogger logger = CustomLogManager.getLogger(CVRDataService.class);

    private static final String CVR_DATA_KEY = "cvrDataLib";
    private static final int CVR_PAGE_SIZE = 1000;

    @Value("${ruleEngine.host}")
    private String host;

    @Value("${ruleEngine.endpoints.offloadcpuData}")
    private String offloadcpuData;

    @Autowired
    @Qualifier("GenericRestClient")
    private GenericRestClient genericRestClient;

    @Autowired
    private MetricsHelper metricsHelper;

    private int recordsCounter;
    private boolean success;
    private boolean onStartUp=true;

    public boolean syncCVRData() throws IOException {
        recordsCounter = 0;
        success = false;
        fetchCvrData();
        return success;
    }

    @PostConstruct
    public void onStartup() throws IOException {
        fetchCvrData();
        onStartUp=false;
    }

    @MethodLatencyMetricsAction(metricsName = "fetch_cvr_data")
    @Scheduled(cron = "0 0/15 * * * *")
    public void fetchCvrData() throws IOException {
        logger.info("fetchCvrData starts");
        try {
            int pageNumber = 0;
            boolean isDone = false;
            while (!isDone) {

                CvrDataResponse response = genericRestClient.retryGet(host, offloadcpuData, buildQueryParams(pageNumber),
                        null, new ParameterizedTypeReference<CvrDataResponse>() {
                        });

                if (CVRUtils.isValid(response)) {
                    CVRUtils.addToCache(response);

                    recordsCounter += response.getData().getProductData().size();

                    if (response.getData().hasNext()) {
                        pageNumber = pageNumber + 1;
                        logger.trace("fetchCvrData:: going to fetch next batch pageNumber {}", pageNumber);
                    } else {
                        logger.info("startFetchingCvrData:: total fetched merchant Data recordsCounter {}",
                                recordsCounter);
                        success = true;
                        isDone = true;
                    }
                } else {
                    String responseAsString = JsonUtils.serialiseJson(response);
                    logger.error("fetchCvrData:: error received responseCode {}, body {}", response.getStatus(),
                            responseAsString);
                    metricsHelper.pushToDD("fetch_cvr_data_error", null);
                    isDone = true;
                }
            }
            logger.info("fetchCvrData::cvr sync finished");
        } catch (Exception ex) {
            logger.error("fetchCvrData:: error while fetching CVR Data ", ex);
            metricsHelper.pushToDD("fetch_cvr_data_error", null);
            if (onStartUp)
                throw ex;
        }
        logger.info("fetchCvrData ends");
    }

    private Map<String, Object> buildQueryParams(int pageNumber) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("key", CVR_DATA_KEY);
        queryParams.put("pageSize", CVR_PAGE_SIZE);
        queryParams.put("pageNumber", pageNumber);
        return queryParams;
    }

}
