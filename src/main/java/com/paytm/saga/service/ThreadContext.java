package com.paytm.saga.service;

import org.springframework.stereotype.Service;

@Service
public class ThreadContext {
	private static final ThreadLocal<String> serviceContext = new ThreadLocal<>();
	private static final ThreadLocal<String> customerIdContext = new ThreadLocal<>();
	private static final ThreadLocal<String> rechargeNumberContext = new ThreadLocal<>();
	public static void setServiceName(String serviceName) {
		serviceContext.set(serviceName);
	}
	public static String getServiceName() {
		return serviceContext.get();
	}
	public static void setRechargeNumber(String rechargeNumber) {
		rechargeNumberContext.set(rechargeNumber);
	}
	public static void setCustomerId(String customerId) {
		customerIdContext.set(customerId);
	}
	public static String getCustomerId() {
		return customerIdContext.get();
	}

	public static String getRechargeNumber() {
		return rechargeNumberContext.get();
	}
	public static void clear() {
		serviceContext.remove();
		customerIdContext.remove();
		rechargeNumberContext.remove();
	}
}
