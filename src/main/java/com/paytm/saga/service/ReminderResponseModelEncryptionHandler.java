package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.ReminderDataResponseModel;
import com.paytm.saga.dto.ReminderResponseModel;
import com.paytm.saga.util.AESUtil;
import com.paytm.saga.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.paytm.saga.common.constant.EncryptionConstants.*;

@Service
public class ReminderResponseModelEncryptionHandler {
	private final CustomLogger logger = CustomLogManager.getLogger(ReminderResponseModelEncryptionHandler.class);

	private final AESUtil aesUtil;
	private final MetricsHelper metricsHelper;

	@Autowired
	public ReminderResponseModelEncryptionHandler(AESUtil aesUtil, MetricsHelper metricsHelper) {
		this.aesUtil = aesUtil;
		this.metricsHelper = metricsHelper;
	}

	public ReminderResponseModel decryptReminderResponseModel(ReminderResponseModel reminderResponseModel) {
		try{
			boolean isEncrypted = isEncrypted(reminderResponseModel.getData());
			if(Objects.nonNull(reminderResponseModel.getData())) {
				reminderResponseModel.setData(decryptReminderDataResponseModel(reminderResponseModel.getData(), isEncrypted));
			}
			if(Objects.nonNull(reminderResponseModel.getOld())) {
				reminderResponseModel.setOld(decryptReminderDataResponseModel(reminderResponseModel.getOld(), isEncrypted));
			}
			return reminderResponseModel;
		} catch (AES256Exception e) {
			logger.error("EncryptionHandlerReminderResponseModel : decryptReminderResponseModel : error at the time of decryption", e);
			metricsHelper.pushToDD(ENCRYPTION, ENCRYPTION_ON_REMINDER_RESPONSE_MODEL);
			throw new AES256Exception("Error in decrypting the payload of reminder response model");
		}
	}

	private ReminderDataResponseModel decryptReminderDataResponseModel(ReminderDataResponseModel reminderDataResponseModel, boolean isEncrypted) throws AES256Exception {
		if (!isEncrypted) return reminderDataResponseModel;
		if(Objects.nonNull(reminderDataResponseModel.getEncAmount())) {
			reminderDataResponseModel.setAmount(Double.valueOf(aesUtil.decrypt(reminderDataResponseModel.getEncAmount())));
		}
		reminderDataResponseModel.setDue_date(aesUtil.decrypt(reminderDataResponseModel.getEncDueDate()));
		reminderDataResponseModel.setRecharge_number(aesUtil.decrypt(reminderDataResponseModel.getRecharge_number()));
		reminderDataResponseModel.setReference_id(aesUtil.decrypt(reminderDataResponseModel.getReference_id()));
		try {
			reminderDataResponseModel.setCustomerOtherInfo(decryptCustomerOtherInfo(reminderDataResponseModel));
		} catch (Exception e) {
			logger.error("EncryptionHandlerReminderResponseModel : decryptReminderDataResponseModel : error at the time of decrypting customer other info", e);
			metricsHelper.pushToDD(DECRYPTION, PARSE_CUSTOMER_OTHER_INFO_ERROR);
		}
		try {
			reminderDataResponseModel.setExtra(decryptExtra(reminderDataResponseModel));
		} catch (Exception e) {
			logger.error("EncryptionHandlerReminderResponseModel : decryptReminderDataResponseModel : error at the time of decrypting extra", e);
			metricsHelper.pushToDD(DECRYPTION, PARSE_CUSTOMER_OTHER_INFO_ERROR);
		}
		try {
			reminderDataResponseModel.setUser_data(decryptUserData(reminderDataResponseModel));
		} catch (Exception e) {
			logger.error("EncryptionHandlerReminderResponseModel : decryptReminderDataResponseModel : error at the time of decrypting user Data", e);
			metricsHelper.pushToDD(DECRYPTION, USER_DATA);
		}
		reminderDataResponseModel.setIsEncrypted(1);
		return reminderDataResponseModel;
	}

	private String decryptCustomerOtherInfo(ReminderDataResponseModel reminderDataResponseModel) throws JsonProcessingException {
		String customerOtherInfo = reminderDataResponseModel.getCustomerOtherInfo();
		if (Objects.isNull(customerOtherInfo)) {
			return customerOtherInfo;
		}
		Map<String, Object> reminderCustomerOtherInfo = JsonUtils.parseJson(customerOtherInfo, Map.class);
		if (Objects.isNull(reminderCustomerOtherInfo)) {
			return customerOtherInfo;
		}
		Set<String> customerOtherInfoEncryptedKeys = JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject(CUSTOMER_OTHER_INFO_ENCRYPTED_KEYS));
		if (Objects.isNull(customerOtherInfoEncryptedKeys) || customerOtherInfoEncryptedKeys.isEmpty()) {
			return customerOtherInfo;
		}
		Map<String, Object> updatedReminderCustomerOtherInfo = new HashMap<>(reminderCustomerOtherInfo);
		reminderCustomerOtherInfo.forEach((key, value) -> {
			if (customerOtherInfoEncryptedKeys.contains(key)) {
				updatedReminderCustomerOtherInfo.put(key, aesUtil.decrypt((String) value));
			}
		});
		return JsonUtils.serialiseJson(updatedReminderCustomerOtherInfo);
	}

	private String decryptExtra(ReminderDataResponseModel reminderDataResponseModel) throws JsonProcessingException {
		String extra = reminderDataResponseModel.getExtra();
		if (Objects.isNull(extra)) {
			return extra;
		}
		Set<String> extraEncryptedKeys = JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS));
		if (Objects.isNull(extraEncryptedKeys) || extraEncryptedKeys.isEmpty()) return extra;
		Map<String, Object> reminderExtra = JsonUtils.parseJson(extra, Map.class);
		if (Objects.isNull(reminderExtra)) {
			return extra;
		}
		Map<String, Object> updatedRecentExtra = new HashMap<>(reminderExtra);
		reminderExtra.forEach((key, value) -> {
			if (extraEncryptedKeys.contains(key)) {
				updatedRecentExtra.put(key, aesUtil.decrypt((String) value));
			}
		});
		return JsonUtils.serialiseJson(updatedRecentExtra);
	}

	private String decryptUserData(ReminderDataResponseModel reminderDataResponseModel) throws JsonProcessingException {
		String userData = reminderDataResponseModel.getUser_data();
		if (Objects.isNull(userData)) {
			return userData;
		}
		Set<String> userDataEncryptedKeys = JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject(USER_DATA_ENCRYPTED_KEYS));
		if (Objects.isNull(userDataEncryptedKeys) || userDataEncryptedKeys.isEmpty()) return userData;
		Map<String, Object> reminderUserData = JsonUtils.parseJson(userData, Map.class);
		if (Objects.isNull(reminderUserData)) {
			return userData;
		}
		Map<String, Object> updatedUserData = new HashMap<>(reminderUserData);
		reminderUserData.forEach((key, value) -> {
			if (userDataEncryptedKeys.contains(key)) {
				updatedUserData.put(key, aesUtil.decrypt((String) value));
			}
		});
		return JsonUtils.serialiseJson(updatedUserData);
	}

	private boolean isEncrypted(ReminderDataResponseModel reminderDataResponseModel) {
		return Objects.nonNull(reminderDataResponseModel) && Objects.nonNull(reminderDataResponseModel.getIsEncrypted()) && (reminderDataResponseModel.getIsEncrypted() == 1);
	}
}
