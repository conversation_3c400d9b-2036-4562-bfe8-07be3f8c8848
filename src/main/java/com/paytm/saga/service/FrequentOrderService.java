package com.paytm.saga.service;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.FrequentOrderResponse;
import com.paytm.saga.dto.FrequentOrderResponseWrapper;

import java.util.List;

public interface FrequentOrderService {
    List<FrequentOrderResponse> getFrequentOrders(FrequentOrderRequest request) throws RechargeSagaBaseException;

    FrequentOrderResponseWrapper getFrequentOrderWrapper(FrequentOrderRequest request) throws RechargeSagaBaseException;
}
