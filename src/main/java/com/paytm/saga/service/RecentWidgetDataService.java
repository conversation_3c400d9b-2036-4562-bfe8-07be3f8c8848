package com.paytm.saga.service;


import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.RecentDao;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.builder.RecentsBuilder;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.util.CommonUtils;
import com.paytm.saga.util.CreditCardUtils;
import com.paytm.saga.util.RecentUtils;
import com.paytm.saga.util.VersionComparable;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.OLD_BILL_REMINDER_STATUS;
import static com.paytm.saga.enums.Service.ELECTRICITY;
import static com.paytm.saga.util.RecentUtils.shouldExcludeRecentBasedOnService;

@Service
@DependsOn("keyspaceRecentConfig")
public class RecentWidgetDataService extends FrequentOrderServiceImpl {

    private final CustomLogger logger = CustomLogManager.getLogger(RecentWidgetDataService.class);

    @Autowired
    private RecentDao recentDao;

    @Autowired
    private MetricsHelper metricsHelper;

    @Override
    protected List<Recents> getRecentDataFromDB(FrequentOrderRequest request) {
        logger.debug("RecentWidgetDataService :: getRecentDataFromDB starts");
        return recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout());

    }

    @Override
    protected boolean skipBasedOnCategory(Map<Long, Integer> categoryWiseCount, Long productId) {
        ProductMin product = CVRProductCache.getInstance().getProductDetails(productId);
        Long categoryId = null;
        if(product != null)
            categoryId = product.getCategoryId();
        return categoryWiseCount.getOrDefault(categoryId, 0) >= serviceConfig.getRecentCategoryLimit();
    }

    @Override
    protected void addToFavResponse(List<FrequentOrderResponse> favResponse, FrequentOrderResponse response, Map<Long, Integer> categoryWiseMap) {

        ProductMin product = CVRProductCache.getInstance().getProductDetails(Long.valueOf(response.getPid()));
        Long categoryId = null;
        if(product != null)
            categoryId = product.getCategoryId();
        categoryWiseMap.put(categoryId, categoryWiseMap.getOrDefault(categoryId, 0) + 1);
        logger.trace("addToFavResponse categoryWiseMap {}", categoryWiseMap);
        favResponse.add(response);
    }
    @Override
    protected void updateWidgetSpecificFields(Recents recent) {
        if (!RecentUtils.isBillDue(recent)) {
            if(RecentUtils.isBillDuePartial(recent) && isPartialBillExpiryWithinRange(recent.getPayType(), recent.getKey().getService(), recent.getBillDate())){
                return;
            }
            recent.setOnlyTxn(true);
            return;
        }
        if(CommonConsts.PAYTM_POSTPAID_SERVICE.equalsIgnoreCase(recent.getKey().getService())){
            return;
        }
        if (isExpiryWithinRange(recent.getPayType(), recent.getKey().getService(), recent.getDueDate()))
            return;
        recent.setOnlyTxn(true);
    }

    @Override
    protected boolean isSkippable(Recents recent, FrequentOrderRequest request) {

        if (request.getVersion() == null)
            return false;

        if (StringUtils.equalsAnyIgnoreCase(Constants.FASTAG_LOW_BALANCE_OPERATOR, recent.getOperator())) {
            return true;
        }

        if(StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.RU_SMS) &&  StringUtils.equalsAnyIgnoreCase(recent.getPayType(),"prepaid") && StringUtils.equalsAnyIgnoreCase(recent.getKey().getService(),"mobile") && !(StringUtils.equalsAnyIgnoreCase(RecentUtils.getPlanBucket(recent),"Special Recharge") )){
            return true;
        }

        if(RecentUtils.isNotSpecialRechargeSMSCard(recent)){
            return true;
        }

        if(Constants.RENT_PAYMENT.equalsIgnoreCase(recent.getKey().getService())&&(Objects.isNull(recent.getIsNewBiller())||Objects.nonNull(recent.getIsNewBiller())&& Boolean.TRUE.equals(!recent.getIsNewBiller()))){
            return true;
        }
        if(Objects.nonNull(recent.getReminderStatus()) && recent.getReminderStatus() == OLD_BILL_REMINDER_STATUS){
            logger.trace("not Skippable In Case of Old Bill flow");
            return false;
        }
        if(RecentUtils.isBillDuePartial(recent) && !isPartialBillExpiryWithinRange(recent.getPayType(), recent.getKey().getService(), recent.getBillDate())){
            return true;
        }

        VersionComparable requestedVersion = new VersionComparable(request.getVersion());
        if (Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(recent.getPayType()) && (Constants.CLIENT.WEB.equalsIgnoreCase(request.getClient()) || (StringUtils.equalsAnyIgnoreCase(request.getClient(), Constants.CLIENT.IOS, Constants.CLIENT.ANDROID) && requestedVersion.compareTo(new VersionComparable("8.0.2")) < 0))) {

            logger.debug("isSkippable skpping");

            return true;
        }
        return false;
    }

    public String getUniqueKey(Recents recent, RecentConfig config) {
       //Do I need to change here as well
        StringBuilder key = new StringBuilder();
        if (Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(recent.getPayType())) {
            if (Objects.nonNull(CVRProductCache.getInstance().getCustomOperatorForCreditCard(recent.getProductId()))) {
                key.append(recent.getMcn()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(CVRProductCache.getInstance().getCustomOperatorForCreditCard(recent.getProductId()));
            } else {
                key.append(recent.getMcn()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator());
            }
        } else if (StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF,Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION)) {
            key.append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator());
        }/*else if(recent.getKey().getService().equals("mobile")){
            key.append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator()).append(Constants.Delimiters.UNDERSCORE).append(RecentUtils.getPlanBucket(recent));

        }*/
        /*else if(recent.getKey().getService().equalsIgnoreCase(ELECTRICITY.value) && (serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase()))) {
            key.append(RecentUtils.getTrimmedRechargeNumber(recent.getKey().getRechargeNumber())).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService());

        }*/
        else if (config.getIncludeOperatorInKeyServices().contains(recent.getKey().getService())) {
            key.append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getOperator());
        } else
            key.append(recent.getKey().getRechargeNumber()).append(Constants.Delimiters.UNDERSCORE).append(recent.getKey().getService());


        return key.toString();
    }

    protected void updateSortedRecentsAndDropoff(List<Recents> recents, Map<String, DropOffResponse> dropOffMap, FrequentOrderRequest request, FrequentOrderResponseWrapper wrapper) throws RechargeSagaBaseException {
        String loggerService = LoggerThreadContext.getServiceName();
        List<Future<Object>> recentAndDropOffTaskFuture = new ArrayList<>();
        recentAndDropOffTaskFuture.add(frequentOrderRecentExecutor.submit(() -> {
            LoggerThreadContext.setServiceName(loggerService);
            try{
                return getRecentDataFromDB(request);
            } finally {
                LoggerThreadContext.clear();
            }
        }));

        recentAndDropOffTaskFuture.add(frequentOrderDropOffExecutor.submit(() -> {
            if (!Boolean.TRUE.equals(request.getExcludeDropoff()) && !serviceConfig.disableDropOffService()) {
                //return dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null);
                return dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null);
            }
            return null;
        }));
        recentAndDropOffTaskFuture.add(frequentOrderSmartRecentExecutor.submit(() -> {
            if (StringUtils.equalsIgnoreCase(Constants.METHOD_POST,request.getMethodType()) && !request.getServices().isEmpty() && serviceConfig.getSmartRecentsEnabledServices().contains(request.getServices().get(0))){
                return smartRecentsService.getSmartRecentsForFrequentOrderCLP(request);
            }
            else if(StringUtils.equalsIgnoreCase("v5",request.getApiVersion())){
                return smartRecentsService.getSmartRecents(request,serviceConfig.getSmartRecentsEnabledServices());
            }
            return null;
        }));

        try {
            List<Recents> recent = (List<Recents>) recentAndDropOffTaskFuture.get(0).get(serviceConfig.getFrequentOrderExecutorTimeOut(), TimeUnit.MILLISECONDS);
            if(recent!=null) {
                recent.stream().forEach(recentObj -> recentObj.setUpdatedAt(ObjectUtils.firstNonNull(recentObj.getUpdatedAt(),recentObj.getTxnTime())));
                recents.addAll(CommonUtils.collectionToStream(recent).filter(recentObj -> recentObj.getUpdatedAt() != null
                        && filterForSMSEnabledService(recentObj, request)
                        && !shouldExcludeRecentBasedOnService(recentObj)
                        && (!Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET.equalsIgnoreCase(recentObj.getKey().getPlanBucket()) ))
                        .collect(Collectors.toList()));
                CreditCardUtils.removeDuplicates(recents, metricsHelper, logger);
                Collections.sort(recents, Comparator.comparing(Recents::getUpdatedAt, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
            }

        } catch (TimeoutException ex) {
            logger.error("updateSortedRecentsAndDropoff recent TimeoutException is {} recents {}", ex, recents);
            metricsHelper.pushToDD(Constants.MetricConstants.RECENT_API, Constants.MetricConstants.RECENT_API_RECENT_TIMEOUT);
            //throw new RechargeSagaBaseException(ex.getMessage());
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            logger.error("updateSortedRecentsAndDropoff InterruptedException is {} recents {} ", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        } catch (ExecutionException ex) {
            logger.error("updateSortedRecentsAndDropoff  ExecutionException is {} recents {} ", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        }


        try {
            Map<String, DropOffResponse> dropOffResponse = (Map<String, DropOffResponse>) recentAndDropOffTaskFuture.get(1).get(serviceConfig.getFrequentOrderExecutorTimeOut(), TimeUnit.MILLISECONDS);
            if (dropOffResponse != null)
                dropOffMap.putAll(dropOffResponse);

        } catch (TimeoutException ex) {
            logger.error("updateSortedRecentsAndDropoff dropOff TimeoutException is {} recents {} dropOffMap {}", ex, recents, dropOffMap);
            metricsHelper.pushToDD(Constants.MetricConstants.RECENT_API, Constants.MetricConstants.RECENT_API_DROP_OFF_RECENT_TIMEOUT);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            logger.error("updateSortedRecentsAndDropoff InterruptedException is {} recents {} dropOffMap {}", ex, recents, dropOffMap);
        } catch (ExecutionException ex) {
            logger.error("updateSortedRecentsAndDropoff  ExecutionException is {} recents {} dropOffMap {}", ex, recents, dropOffMap);
        }

        try {
            List<SmartRecents> smartRecent = (List<SmartRecents>) recentAndDropOffTaskFuture.get(2).get(serviceConfig.getFrequentOrderExecutorTimeOut(), TimeUnit.MILLISECONDS);

            if(!CollectionUtils.isEmpty(smartRecent)) {
                Collections.sort(smartRecent, Comparator.comparing(SmartRecents::getUpdatedAt, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                recents.addAll(smartRecent.stream().map(RecentsBuilder.fromSmartRecents::apply).collect(Collectors.toList()));
            }

        } catch (TimeoutException ex) {
            logger.error("updateSortedRecentsAndDropoff smartRecent TimeoutException is {} recents {}", ex, recents);
            metricsHelper.pushToDD(Constants.MetricConstants.RECENT_API, Constants.MetricConstants.RECENT_API_SMART_RECENT_TIMEOUT);
            //throw new RechargeSagaBaseException(ex.getMessage());
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            logger.error("updateSortedRecentsAndDropoff InterruptedException is {} recents {} ", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        } catch (ExecutionException ex) {
            logger.error("updateSortedRecentsAndDropoff  ExecutionException is {} recents {} ", ex, recents);
            throw new RechargeSagaBaseException(ex.getMessage());
        }

        logger.trace("recents is {} and dropOffMap is {}", recents, dropOffMap);

    }
    @Override
    protected void addSmsCardToFavResponse(List<FrequentOrderResponse> favResponse, FrequentOrderResponse response) {
        favResponse.add(response);
    }

    @Override
    protected void handleFastagRechargeNumber(List<Recents> recents) {
        for (Recents recent : recents) {
            if (Constants.FASTAG.equalsIgnoreCase(recent.getKey().getService())) {
                recent.setFastagRechargeNumber(recent.getKey().getRechargeNumber());
                recent.getKey().setRechargeNumber(recent.getKey().getRechargeNumber().toUpperCase());
            }
        }
    }

    @Override
    protected void revertFastagRechargeNumber(List<Recents> recents, Map<String, Recents> filteredRecents, Map<String, Recents> txnStatus, Map<String, Recents> txnStatusForNonRU, RecentConfig config) {

        boolean isFastag = false;
        long originalCnt = 0;
        for (Recents recent : recents) {
            if (Constants.FASTAG.equalsIgnoreCase(recent.getKey().getService())) {
                recent.getKey().setRechargeNumber(recent.getFastagRechargeNumber());
                originalCnt++;
                isFastag = true;
            }
        }

        if (!isFastag) {
            return;
        }

        Map<String, Recents> revertedFilteredRecents = new LinkedHashMap<>();
        Map<String, Recents> revertedTxnStatus = new HashMap<>();
        Map<String, Recents> revertedTxnStatusForNonRU = new HashMap<>();

        long filteredCnt = 0;
        for (Map.Entry<String, Recents> recentEntry : filteredRecents.entrySet()) {
            String key = recentEntry.getKey();
            Recents recent = recentEntry.getValue();

            if (Constants.FASTAG.equalsIgnoreCase(recent.getKey().getService())) {
                filteredCnt++;
                recent.getKey().setRechargeNumber(recent.getFastagRechargeNumber());
                String newKey = getUniqueKey(recent, config);
                revertedFilteredRecents.put(newKey, recent);
            } else {
                revertedFilteredRecents.put(key, recent);
            }
        }

        long duplicateCount = originalCnt - filteredCnt;
        metricsHelper.pushCountToDD(Constants.FASTAG_RECENTS, Constants.DUPLICATE, duplicateCount);

        for (Map.Entry<String, Recents> recentEntry : txnStatus.entrySet()) {
            String key = recentEntry.getKey();
            Recents recent = recentEntry.getValue();

            if (Constants.FASTAG.equalsIgnoreCase(recent.getKey().getService())) {
                recent.getKey().setRechargeNumber(recent.getFastagRechargeNumber());
                revertedTxnStatus.put(getTxnMapKey(recent, config), recent);
            } else {
                revertedTxnStatus.put(key, recent);
            }
        }

        for (Map.Entry<String, Recents> recentEntry : txnStatusForNonRU.entrySet()) {
            String key = recentEntry.getKey();
            Recents recent = recentEntry.getValue();

            if (Constants.FASTAG.equalsIgnoreCase(recent.getKey().getService())) {
                recent.getKey().setRechargeNumber(recent.getFastagRechargeNumber());
                revertedTxnStatusForNonRU.put(getTxnMapKeyForNonRU(recent, config), recent);
            } else {
                revertedTxnStatusForNonRU.put(key, recent);
            }
        }

        filteredRecents = revertedFilteredRecents;
        txnStatus = revertedTxnStatus;
        txnStatusForNonRU = revertedTxnStatusForNonRU;

    }

}
