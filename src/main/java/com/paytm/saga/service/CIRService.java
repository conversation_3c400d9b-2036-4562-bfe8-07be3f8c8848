package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.CIRRequest;
import com.paytm.saga.dto.CIRResponse;
import com.paytm.saga.dto.DeleteCIRRequest;
import com.paytm.saga.dto.DeleteCIRResponse;
import com.paytm.saga.model.DeleteCIR;
import com.paytm.saga.repository.DeleteCIRRepository;
import com.paytm.saga.common.constant.Constants;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class CIRService {
    private final CustomLogger logger = CustomLogManager.getLogger(com.paytm.saga.service.CIRService.class);
    
    private DeleteCIRRepository deleteCIRRepository;

    @Autowired
    public void CIRService(@NonNull DeleteCIRRepository deleteCIRRepository) {
        this.deleteCIRRepository = deleteCIRRepository;
    }
    public DeleteCIRResponse deleteCIR(DeleteCIRRequest deleteCIRRequest) throws RechargeSagaBaseException {
        logger.trace("[CIRService.deleteCIR] function starting");
        DeleteCIRResponse deleteCIRResponse = new DeleteCIRResponse();
        try{
            deleteCIRRequest.setCustomerId(deleteCIRRequest.getCustomerId());
            deleteCIRRequest.setBureauName(deleteCIRRequest.getBureauName());
            deleteCIRRequest.setBankName(deleteCIRRequest.getBankName());

            this.saveDeleteCIR(deleteCIRRequest);
            deleteCIRResponse.setMessage("SUCCESS");
        }catch(Exception exception){
            logger.error("failed to insert data in deletecir table");
            deleteCIRResponse.setErrorMessage("Something went wrong. Please try again");
        }
        return deleteCIRResponse;
    }

    public CIRResponse getBankNames(CIRRequest cirRequest) throws RechargeSagaBaseException {
        logger.trace("[CIRService.getBankNames] function starting");
        CIRResponse cirResponse = new CIRResponse();
        List<DeleteCIR> bankNames;
        List<String> bankNamesList = new ArrayList<>();
        try{
            cirRequest.setCustomerId(cirRequest.getCustomerId());
            cirRequest.setBureauName(cirRequest.getBureauName());
            bankNames = this.findBankNames(cirRequest);
            for (DeleteCIR bankName:bankNames
                 ) {
                bankNamesList.add(bankName.getBankName().toLowerCase());
            }
            cirResponse.setBankNames(bankNamesList);
        }catch (Exception exception){
            logger.error("failed to fetch bankNames",exception);
            cirResponse.setBankNames(bankNamesList);
        }
        return cirResponse;
    }

    public List<DeleteCIR> findBankNames(CIRRequest cirRequest) {
        List<DeleteCIR> bankNames = null;
        try {
            bankNames = deleteCIRRepository.findBankNamesbyCustomerIdandBureauName(cirRequest.getCustomerId(), cirRequest.getBureauName());
        }catch(Exception e){
            logger.error("[CIRService.findBankNames]: error in getting bankNames , e={}",e.getMessage());
        }
        return bankNames;
    }

    public void saveDeleteCIR(DeleteCIRRequest deleteCIRRequest) {
        Date deleteAt = new Date();
        Boolean message = false;
        try{
            message= deleteCIRRepository.insertData(deleteCIRRequest.getCustomerId(), deleteCIRRequest.getBureauName(), deleteCIRRequest.getBankName(), deleteAt, Constants.CIR_TTL);
        }catch(Exception e){
            logger.error("[CIRService.saveDeleteCIR]: error in inserting data in deletecir table , e={}",e.getMessage());
        }
    }
}
