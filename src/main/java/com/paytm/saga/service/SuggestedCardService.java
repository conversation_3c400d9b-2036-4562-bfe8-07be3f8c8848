package com.paytm.saga.service;

import java.util.*;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.metrics.MetricsHelper;
import org.springframework.beans.factory.annotation.Autowired;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.LatestOrdersRequest;
import com.paytm.saga.dto.LatestOrdersResponse;
import com.paytm.saga.dto.SuggestedCard;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.JsonUtils;
import org.springframework.stereotype.Component;

import static com.paytm.saga.common.constant.Constants.ALL_CIRCLES;

@Component
public class SuggestedCardService {
    private final CustomLogger logger = CustomLogManager.getLogger(SuggestedCardService.class);

    @Autowired
    protected ServiceConfig serviceConfig;

    @Autowired
    private MetricsHelper metricsHelper;

    public SuggestedCardService() {
    }

    public List<SuggestedCard> createSuggestedCards(List<LatestOrdersResponse> latestOrdersResponse, LatestOrdersRequest request) {
        List<SuggestedCard> suggestedCards = new ArrayList<>();

        try {
            if(!serviceConfig.isSuggestedCardEnabled()){
                logger.debug("createSuggestedCardForMissingRecents:: Suggested card feature is disabled. Returning empty list");
                return suggestedCards;
            }

            ProductMin product = CVRProductCache.getInstance().getProductDetails(request.getProductId());
            String circle = null;
            Long productId = null;

            if(Objects.nonNull(product)){
                circle = product.getCircle();
                productId = product.getProductId();
           } else {
                metricsHelper.recordSuccessRateForMultipleTags(Constants.SUGGESTED_CARDS_SERVICE, Arrays.asList("ERROR:INVALID_REQUEST", "operator:" + request.getOperator(), "CAUSE:MISSING_PRODUCT_ID"));
            }

            if(Objects.isNull(circle)) {
                circle = ALL_CIRCLES;
            }

            logger.debug("createSuggestedCardForMissingRecents:: Creating suggested card for missing recents. LatestOrdersResponse: {}, LatestOrdersRequest: {}", latestOrdersResponse, request);

            List<String> planBuckets = Arrays.asList(Constants.SPECIAL_RECHARGE, Constants.DATA_PACK);

            for (String planBucket : planBuckets) {
                SuggestedCard suggestedCard = createSuggestedCard(request.getOperator(), circle, planBucket, productId);
                if (suggestedCard == null) {
                    metricsHelper.recordSuccessRateForMultipleTags(Constants.SUGGESTED_CARDS_SERVICE, Arrays.asList("STATUS:FAILURE", "operator:" + request.getOperator(), "circle:" + circle, "planBucket:" + planBucket));
                } else {
                    metricsHelper.recordSuccessRateForMultipleTags(Constants.SUGGESTED_CARDS_SERVICE, Arrays.asList("STATUS:SUCCESS", "operator:" + request.getOperator(), "circle:" + circle, "planBucket:" + planBucket));
                    suggestedCards.add(suggestedCard);
                }
            }
        } catch (Exception e) {
            logger.error("createSuggestedCardForMissingRecents:: Error in creating suggested card for missing recents. latestOrdersResponse: {} , request: {}, error: {}", latestOrdersResponse, request, e);
            metricsHelper.recordSuccessRateForMultipleTags(Constants.SUGGESTED_CARDS_SERVICE, Collections.singletonList("ERROR:INTERNAL_ERROR"));
        }

        return suggestedCards;
    }

    public SuggestedCard createSuggestedCard(String operator, String circle, String planBucket, Long productId) {
        try {
            if(Objects.isNull(operator) || Objects.isNull(circle) || Objects.isNull(planBucket)) {
                logger.debug("createSuggestedCard:: One of the input parameters is null. Returning null. Operator: {}, Circle: {}, PlanBucket: {}, ProductId: {}", operator, circle, planBucket, productId);
                metricsHelper.recordSuccessRateForMultipleTags(Constants.SUGGESTED_CARDS_SERVICE, Arrays.asList("ERROR:INVALID_REQUEST", "operator:" + operator, "circle:" + circle, "planBucket:" + planBucket));
                return null;
            }
            logger.debug("createSuggestedCard:: Creating suggested card for operator: {}, circle: {}, planBucket: {}, productId: {}", operator, circle, planBucket, productId);

            Map<String, Object> suggestedCardData = getSuggestedCardData(operator, circle, planBucket);

            if(Objects.isNull(suggestedCardData)) {
                return null;
            }

            SuggestedCard suggestedCard = new SuggestedCard();

            suggestedCard.setProductId(productId);
            suggestedCard.setPlanBucket(planBucket);
            suggestedCard.setAmount(((Integer) suggestedCardData.get("amount")).doubleValue());
            suggestedCard.setPlanId((String) suggestedCardData.getOrDefault("planId", null));
            suggestedCard.setPlanCircleKey((String) suggestedCardData.getOrDefault("circleName", null));

            logger.debug("createSuggestedCard:: Suggested card created: {}", suggestedCard);

            return suggestedCard;
        } catch (Exception ex) {
            logger.error("createSuggestedCard:: Error in creating suggested card for operator: {}, circle: {}, planBucket: {}, productId: {}, Exception: {}", operator, circle, planBucket, productId, ex);
            throw ex;
        }
    }

    public Map<String, Object> getSuggestedCardData(String operator, String circle, String planBucket){
        try {
            Map<String, Object> mobileSuggestedCardConfig = new HashMap<>();

            if(FeatureConfigCache.getInstance().getSuggestedCardConfig() != null) {
                mobileSuggestedCardConfig = FeatureConfigCache.getInstance().getSuggestedCardConfig();
            } else {
                metricsHelper.recordSuccessRateForMultipleTags(Constants.SUGGESTED_CARDS_SERVICE, Collections.singletonList("ERROR:NULL_CONFIG"));
            }

            if(mobileSuggestedCardConfig != null && mobileSuggestedCardConfig.get(operator.toLowerCase()) != null){
                Map<String, Object> operatorData = JsonUtils.convertObjectToMap(mobileSuggestedCardConfig.get(operator.toLowerCase()));
                if (operatorData != null) {
                        Map<String, Object> circleData = JsonUtils.convertObjectToMap(operatorData.get(circle.toLowerCase()));
                        if(circleData == null) {
                            circleData = JsonUtils.convertObjectToMap(operatorData.get(ALL_CIRCLES));
                        }
                        if (circleData != null) {
                            for (String key : circleData.keySet()) {
                                if (key.equalsIgnoreCase(planBucket)) {
                                    Map<String, Object> plaBucketData = JsonUtils.convertObjectToMap(circleData.get(key));
                                    return plaBucketData;
                                }
                            }
                        }
                }
            }
            logger.info("getSuggestedCardData:: Suggested card data not found for operator: {}, circle: {}, planBucket: {}", operator, circle, planBucket);
            metricsHelper.recordSuccessRateForMultipleTags(Constants.SUGGESTED_CARDS_SERVICE, Arrays.asList("ERROR:NOT_FOUND", "operator:" + operator, "circle:" + circle, "planBucket:" + planBucket));
            return null;
        } catch (Exception ex) {
            logger.error("getSuggestedCardData:: Error in getting suggested card config for operator: {}, circle: {}, planBucket: {}, Exception: {}", operator, circle, planBucket, ex);
            metricsHelper.recordSuccessRateForMultipleTags(Constants.SUGGESTED_CARDS_SERVICE, Arrays.asList("ERROR:SUGGESTED_CARD_CONFIG", "operator:" + operator, "circle:" + circle, "planBucket:" + planBucket));
            throw ex;
        }
    }
}
