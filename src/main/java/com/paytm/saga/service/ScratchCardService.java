package com.paytm.saga.service;

import java.util.List;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.paytm.saga.model.ScratchCardHistory;
import com.paytm.saga.repository.ScratchCardHistoryRepository;

@Service
public class ScratchCardService {
	private static final CustomLogger logger = CustomLogManager.getLogger(ScratchCardService.class);
	protected final ScratchCardHistoryRepository scratchCardHistoryRepository;

    @Autowired
    public ScratchCardService(final ScratchCardHistoryRepository scratchCardHistoryRepository) {
        this.scratchCardHistoryRepository = scratchCardHistoryRepository;
    }

    public List<ScratchCardHistory> getByOrderId(final long orderId) {
    	logger.trace("getting scratch cards for orderId " + orderId);
    	return scratchCardHistoryRepository.findByOrderId(orderId);
    }
}
