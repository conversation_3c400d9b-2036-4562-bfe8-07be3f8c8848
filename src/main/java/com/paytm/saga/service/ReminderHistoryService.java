package com.paytm.saga.service;

import java.util.List;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import com.paytm.saga.model.ReminderHistory;

@Service
public class ReminderHistoryService {
    private final CustomLogger logger = CustomLogManager.getLogger(com.paytm.saga.service.DropOffService.class);
    private final ReminderHistoryRepositoryWrapperService reminderHistoryRepository;

    @Autowired
    public ReminderHistoryService(
            @NonNull ReminderHistoryRepositoryWrapperService reminderHistoryRepository) {
        this.reminderHistoryRepository = reminderHistoryRepository;
    }

    public List<ReminderHistory> findByCustomerIdAndRechargeNumberAndService(final Long customerId, final String rechargeNumber, final String service){
        return reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, service);
    }

    public List<ReminderHistory> findByCustomerIdAndRechargeNumberAndServiceAndOperator(final Long customerId, final String rechargeNumber, final String service, final String operator){
        return reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber, service, operator);
    }

    public  List<ReminderHistory> findByCustomerIdAndRechargeNumberInAndServiceIn(final Long customerId, final List<String> rechargeNumber, final List<String> service){
        return reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumber, service);
    }

}
