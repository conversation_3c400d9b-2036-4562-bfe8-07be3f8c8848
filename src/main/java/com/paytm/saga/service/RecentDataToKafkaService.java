package com.paytm.saga.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.configuration.property.RecentDataDwhKafkaConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.RecentDataToKafkaModel;
import com.paytm.saga.model.Recents;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.paytm.saga.common.constant.Constants.CommonConstants.FASTAG_API;

@Service
@Data
public class RecentDataToKafkaService {

    @Autowired
    public KafkaTemplate<String, Object> recentDataKafkaTemplate;
    @Autowired
    private MetricsHelper metricsHelper;

    @Autowired
    RecentDataDwhKafkaConfig recentDataDwhKafkaConfig;

    private final CustomLogger logger = CustomLogManager.getLogger(RecentDataToKafkaService.class);

    public void setRecentConsumerSource(Recents recents, String recentConsumerSource) {
        recents.setConsumerSource(recentConsumerSource);
    }

    @Retryable(value = {RecentDataToKafkaException.class},
            maxAttempts = 4)
    public void pushRecentDataToKafka(Recents recents, String op) throws RecentDataToKafkaException {
        logger.info("[RecentDataToKafkaService.pushRecentDataToKafka] :: function starting");
        RecentDataToKafkaModel recentDataToKafkaModel = new RecentDataToKafkaModel();
        recentDataToKafkaModel.setOp(op);
        BeanUtils.copyProperties(recents,recentDataToKafkaModel);
        recentDataToKafkaModel.setCustomerId(recents.getKey().getCustomerId());
        recentDataToKafkaModel.setService(recents.getKey().getService());
        recentDataToKafkaModel.setRechargeNumber(recents.getKey().getRechargeNumber());
        recentDataToKafkaModel.setOperator(recents.getKey().getOperator());
        recentDataToKafkaModel.setPlanBucket(recents.getKey().getPlanBucket());

        ObjectMapper objectMapper = new ObjectMapper();

        String sourceName = Constants.DEFAULT_CONSUMER_SOURCE;
        if(recents.getConsumerSource() != null) {
            sourceName = recents.getConsumerSource();

            if(isRecentUpdateTimeToBeUpdate(recents)) {
                recentDataToKafkaModel.setRecentUpdatedAt(recents.getUpdatedAt());
            }
        }

        recentDataToKafkaModel.setUpdatedAt(new Date());

        try {
            String recentDataJson = objectMapper.writeValueAsString(recentDataToKafkaModel);
            logger.info("[RecentDataToKafkaService.pushRecentDataToKafka] :: Pushing into kafka for customerID {}, service: {}, rechargeNumber : {}, updated_at : {} ",recentDataToKafkaModel.getCustomerId(),recentDataToKafkaModel.getService(),recentDataToKafkaModel.getRechargeNumber(), recentDataToKafkaModel.getUpdatedAt());
            recentDataKafkaTemplate.send(recentDataDwhKafkaConfig.getTopicName(),recentDataToKafkaModel.getCustomerId().toString(),recentDataJson);
            metricsHelper.recordSuccessRate(Constants.CommonConstants.RECENT_DATA_DWH_KAFKA_SUCCESS,sourceName);
        } catch (Exception e) {
            logger.error("[RecentDataToKafkaService.pushRecentDataToKafka] :: Error while putting data into kafka ",e);
            metricsHelper.recordSuccessRate(Constants.CommonConstants.RECENT_DATA_DWH_KAFKA_FAILURE,sourceName);
            throw new RecentDataToKafkaException(e);
        }
    }

    private boolean isRecentUpdateTimeToBeUpdate(Recents recents) {
        return recents.getConsumerSource() != null && recents.getConsumerSource().equalsIgnoreCase(Constants.CommonConstants.FASTAG_API_UPDATE_INSURANCE);
    }

    @Recover
    public void recover(RecentDataToKafkaException ex) {
        metricsHelper.recordSuccessRate(Constants.CommonConstants.RECENT_DATA_TO_DWH,"FAILURE_AFTER_RETRY");
        logger.error("[RecentDataToKafkaService.pushRecentDataToKafka] All retry attempts exhausted for pushRecentDataToKafka() after all attempts. message {} error message {}",ex.getMessage(),ex.getErrorMessages());
    }


}
