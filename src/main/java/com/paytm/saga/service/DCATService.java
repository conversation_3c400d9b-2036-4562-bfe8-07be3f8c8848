package com.paytm.saga.service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.newrelic.api.agent.Trace;
import com.paytm.saga.common.configuration.property.DCATConfig;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.DCATCategoryGroupingAggAggModel;
import com.paytm.saga.dto.DCATCategoryGroupingAggModel;
import com.paytm.saga.dto.DCATCategoryGroupingsModel;
import com.paytm.saga.dto.DCATCategoryRequestModel;
import com.paytm.saga.dto.DCATCategoryResponseModel;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.model.CommonCache;

@Service
public class DCATService {
	private final DCATConfig dcatConfig;
	private final ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
	private final DcatRestTemplateService restTemplateService;
	private final CommonCacheService commonCacheService;
	private final CustomLogger logger = CustomLogManager.getLogger(DCATService.class);

	@Autowired
	public DCATService(@NonNull DcatRestTemplateService restTemplateService, @NonNull DCATConfig dcatConfig,
			@NonNull ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig,
			@NonNull CommonCacheService commonCacheService) {
		this.restTemplateService = restTemplateService;
		this.externalEndpointsPropertiesConfig = externalEndpointsPropertiesConfig;
		this.commonCacheService = commonCacheService;
		this.dcatConfig = dcatConfig;
	}

	@Retryable(value = { RuntimeException.class }, maxAttempts = 2)
	public DCATGetPlanResponse hitgetPlanAPI(String categoryId, String version, String channel, String operator,
			String amount, String circle, String service) throws RuntimeException {

		String url = externalEndpointsPropertiesConfig.getBrowsePlanUrl() + categoryId + "/getplan";
		Map<String,String> queryMap = new HashMap<>();
		queryMap.put("locale",Constants.CommonConstants.DCAT_LOCALE);
		queryMap.put("version", version);
		queryMap.put("channel", channel);
		queryMap.put("amount", amount);


		if (service != null && !service.equals("dth")) {
			if(circle !=null) {
				try {
					circle = URLEncoder.encode(circle, StandardCharsets.UTF_8.toString());
				} catch (UnsupportedEncodingException e) {
					logger.error("[DCATService.hitgetPlanAPI] circle encoding Exception:", e);
				}
			}
			queryMap.put("circle", circle);
		}
		if(operator != null) {
			try {
				operator = URLEncoder.encode(operator, StandardCharsets.UTF_8.toString());
			} catch (UnsupportedEncodingException e) {
				logger.error("[DCATService.hitgetPlanAPI] operator encoding Exception:", e);
			}
		}
		queryMap.put("operator", operator);
		

		DCATGetPlanResponse dcatGetPlanResponse;
		try {
			dcatGetPlanResponse = restTemplateService.executeGetRequest(url,queryMap,DCATGetPlanResponse.class);
			logger.info("[DCATService.hitgetPlanAPI:Response] : Endpoint: " + url +" queryMap: "+queryMap+ " Response : "
					+ dcatGetPlanResponse);
		} catch (Exception e) {
			logger.error("[DCATService.hitgetPlanAPI] Exception:", e);
			throw new RuntimeException(e);
		}
		return dcatGetPlanResponse;
	}

	@Recover
	public DCATGetPlanResponse recover(RuntimeException e) {
		DCATGetPlanResponse dcatGetPlanResponse = null;
		return dcatGetPlanResponse;
	}

	public DCATGetPlanResponse getPlanDetails(String categoryId, String version, String channel, String operator,
			String amount, String circle, String service) {

		if (categoryId == null || version == null || channel == null || operator == null || amount == null
				|| circle == null || service == null) {
			DCATGetPlanResponse dcatGetPlanResponse = null;
			return dcatGetPlanResponse;
		}

		ObjectMapper mapper = new ObjectMapper();
		String key = generateCacheKey(categoryId, version, channel, operator, amount, circle);
		service = service.toLowerCase();
		DCATGetPlanResponse dcatGetPlanResponse = null;

		// Get data from Cache.
		List<CommonCache> listCommonCache = commonCacheService.getCache(key);

		if (listCommonCache != null && listCommonCache.isEmpty()) {
			// Hit DCAT and store in cache
			try {
				logger.info("[DCATService.getPlanDetails] Hitting DCAT" + categoryId + ":" + version + ":" + channel
						+ ":" + operator + ":" + amount + ":" + circle);
				DCATCategoryResponseModel dcatCategoryResponseModel = hitCategoryApiWrapper(service);

				if (dcatCategoryResponseModel != null) {

					String actual_operator = getActualOperatorName(dcatCategoryResponseModel, operator.toLowerCase());

					String actual_circle = getActualCircleName(dcatCategoryResponseModel, circle.toLowerCase());

					if (actual_operator != null) {

						operator = actual_operator;

					}

					if (actual_circle != null) {

						circle = actual_circle;

					}

				}
				dcatGetPlanResponse = hitgetPlanAPI(categoryId, version, channel, operator, amount, circle, service);
				dcatGetPlanResponse = modifyDCATGetPlanResponse(dcatGetPlanResponse);
				String data = mapper.writeValueAsString(dcatGetPlanResponse);
				commonCacheService.setCache(key, data, Constants.CommonConstants.DCAT_CACHE_TTL);
			} catch (Exception e) {
				logger.error("[DCATService.getPlanDetails] getPlanDetails1 Exception:", e);
			}

		} else {
			// Return from cache
			logger.info("[DCATService.getPlanDetails] getPlanDetails, serving from cache: " + key);
			try {
				dcatGetPlanResponse = mapper.readValue(listCommonCache.get(0).getCacheValue(),
						DCATGetPlanResponse.class);
			} catch (IOException e) {
				logger.error("[DCATService.getPlanDetails] getPlanDetails2 Exception:", e);
			}
		}
		return dcatGetPlanResponse;

	}

	public DCATCategoryResponseModel hitCategoryGroupingApi(String service) throws RuntimeException {

		if (service == null)
			return null;

		String groupingId = Constants.CommonConstants.DCAT_GROUPING_ID_MAP.get(service);

		String[] groupsBody = Constants.CommonConstants.DCAT_GROUPING_BODY_MAP.get(service);

		String url = dcatConfig.getCategoryUrl() + groupingId + "/grouping";

		Map<String,String> queryMap = new HashMap<>();
		queryMap.put("locale",Constants.CommonConstants.DCAT_LOCALE);
		queryMap.put("version", Constants.CommonConstants.DCAT_VERSION);
		queryMap.put("channel", Constants.CommonConstants.DCAT_CHANNEL);

		DCATCategoryRequestModel dcatCategoryRequestModel = new DCATCategoryRequestModel(Arrays.asList(groupsBody));

		Map<String,String> requestHeaders = new HashMap<>();
		requestHeaders.put("Content-Type", "application/json");

		DCATCategoryResponseModel dcatCategoryResponseModel;

		try {
			dcatCategoryResponseModel = restTemplateService.executePostRequest(url, requestHeaders, queryMap,
					DCATCategoryResponseModel.class, dcatCategoryRequestModel);
			logger.info("[DCATService.hitCategoryGroupingApi:Response] : Endpoint: " + url + " Response : "
					+ dcatCategoryResponseModel);
		} catch (Exception e) {
			logger.error("[DCATService.hitCategoryGroupingApi] Exception:", e);
			throw new RuntimeException(e);
		}

		return dcatCategoryResponseModel;
	}
	
	@Trace(dispatcher=true)
    public DCATCategoryResponseModel hitGetCategoryApi(String service) throws RuntimeException {

		if (service == null)
			return null;

		String groupingId = Constants.CommonConstants.DCAT_GROUPING_ID_MAP.get(service);
		
		String url = dcatConfig.getCategoryUrl() + groupingId + "/getcategory";

		Map<String, String> queryMap = new HashMap<String, String>();
		queryMap.put("locale",Constants.CommonConstants.DCAT_LOCALE);
		queryMap.put("version", Constants.CommonConstants.DCAT_VERSION);
		queryMap.put("channel", Constants.CommonConstants.DCAT_CHANNEL);

		DCATCategoryResponseModel dcatCategoryResponseModel;

		try {
			dcatCategoryResponseModel = restTemplateService.executeGetRequest(url, queryMap,
					DCATCategoryResponseModel.class);
			logger.info("[DCATService.hitGetCategoryApi:Response] : Endpoint: " + url + " Response : "
					+ dcatCategoryResponseModel);
		} catch (Exception e) {
			logger.error("[DCATService.hitGetCategoryApi] Exception:", e);
			throw new RuntimeException(e);
		}

		return dcatCategoryResponseModel;
	}

	public String getActualOperatorName(DCATCategoryResponseModel dcatCategoryResponseModel,
			String lower_case_operator) {

		String operator = null;
		if(dcatCategoryResponseModel == null) return operator;
		DCATCategoryGroupingsModel groupings = dcatCategoryResponseModel.getGroupings();
		if(groupings == null) return operator;
		List<DCATCategoryGroupingAggModel> groupingsAgg = groupings.getAggs();
		if(groupingsAgg == null) return operator;
		for (DCATCategoryGroupingAggModel groupingAggObj : groupingsAgg) {

			if (groupingAggObj.getValue() != null
					&& groupingAggObj.getValue().toLowerCase().equals(lower_case_operator)) {

				operator = groupingAggObj.getValue();

			}

		}

		return operator;

	}

	public String getActualCircleName(DCATCategoryResponseModel dcatCategoryResponseModel, String lower_case_circle) {

		String circle = null;
		if(dcatCategoryResponseModel == null) return circle;
		DCATCategoryGroupingsModel groupings = dcatCategoryResponseModel.getGroupings();
		if(groupings == null) return circle;
		List<DCATCategoryGroupingAggModel> groupingsAgg = groupings.getAggs();
		if(groupingsAgg == null) return circle;
		for (DCATCategoryGroupingAggModel groupingAggObj : groupingsAgg) {

			List<DCATCategoryGroupingAggAggModel> dcatCategoryGroupingAggAggModel = groupingAggObj.getAggs();
			if(dcatCategoryGroupingAggAggModel == null) return circle;
			for (DCATCategoryGroupingAggAggModel obj : dcatCategoryGroupingAggAggModel) {

				if (obj.getValue() != null && obj.getValue().toLowerCase().equals(lower_case_circle)) {

					circle = obj.getValue();

				}

			}

		}

		return circle;

	}
	
	
	public DCATCategoryResponseModel hitCategoryApiWrapper(String service) {

		if (service == null) {

			DCATCategoryResponseModel dcatCategoryResponseModel = null;

			return dcatCategoryResponseModel;

		}

		service = service.toLowerCase();

		ObjectMapper mapper = new ObjectMapper();

		String key = generateCategoryCacheKey(service);

		DCATCategoryResponseModel dcatCategoryResponseModel = null;

		// Get data from Cache.

		List<CommonCache> listCommonCache = commonCacheService.getCache(key);

		if (listCommonCache != null && listCommonCache.isEmpty()) {

			// Hit DCAT and store in cache

			try {

				logger.info("[DCATService.hitCategoryApiWrapper] Hitting DCAT" + service);

				dcatCategoryResponseModel = hitCategoryGroupingApi(service);

				String data = mapper.writeValueAsString(dcatCategoryResponseModel);

				commonCacheService.setCache(key, data, Constants.CommonConstants.DCAT_CATEGORY_CACHE_TTL);

			} catch (Exception e) {

				logger.error("[DCATService.hitCategoryApiWrapper] Exception:", e);

			}

		} else {

			// Return from cache

			logger.info("[DCATService.getPlanDetails] hitCategoryApiWrapper, serving from cache: " + key);

			try {

				dcatCategoryResponseModel = mapper.readValue(listCommonCache.get(0).getCacheValue(),
						DCATCategoryResponseModel.class);

			} catch (IOException e) {

				logger.error("[DCATService.getPlanDetails] hitCategoryApiWrapper Exception1:", e);

			}
		}

		return dcatCategoryResponseModel;

	}

	private String generateCategoryCacheKey(String service) {

		String delimiter = Constants.CommonConstants.CACHE_KEY_DELIMTER;

		String key = Constants.CommonConstants.DCAT_CATEGORY_CACHE_KEY_PREFIX + delimiter + service;

		return key;

	}

	public String generateCacheKey(String categoryId, String version, String channel, String operator, String amount,
			String circle) {
		String delimiter = Constants.CommonConstants.CACHE_KEY_DELIMTER;
		if(operator!=null) operator = operator.toLowerCase();
		if(circle!=null) circle = circle.toLowerCase();
		String key = Constants.CommonConstants.DCAT_CACHE_KEY_PREFIX + delimiter + categoryId + delimiter + version
				+ delimiter + channel + delimiter + operator + delimiter + amount + delimiter + circle;
		return key;
	}

	private DCATGetPlanResponse modifyDCATGetPlanResponse(DCATGetPlanResponse dcatGetPlanResponse){
		try{
			List<String> addOnBenefits = dcatGetPlanResponse.getAddon_benefit();
			if(addOnBenefits!=null){
				for (int i = 0; i < addOnBenefits.size(); i++) {
					if (addOnBenefits.get(i) != null) {
						switch (i) {
							case 0:
								dcatGetPlanResponse.setAddon_benefit1(addOnBenefits.get(i));
								break;
							case 1:
								dcatGetPlanResponse.setAddon_benefit2(addOnBenefits.get(i));
								break;
							case 2:
								dcatGetPlanResponse.setAddon_benefit3(addOnBenefits.get(i));
								break;
							case 3:
								dcatGetPlanResponse.setAddon_benefit4(addOnBenefits.get(i));
								break;
							default:
						}
					}
				}
			}
		} catch(Exception e){
			logger.error("[DCATService.modifyDCATGetPlanResponse]  Exception:", e);
		}
		return dcatGetPlanResponse;
	}

}
