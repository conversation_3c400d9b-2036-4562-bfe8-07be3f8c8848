package com.paytm.saga.service;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.dto.WhatsappRemindLaterRequest;
import com.paytm.saga.dto.WhatsappRemindLaterResponse;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WhatsappReminderLaterService {
    private final CustomLogger log = CustomLogManager.getLogger(WhatsappReminderLaterService.class);
    @Autowired
    RecentService recentService;
    public WhatsappRemindLaterResponse setRemindLaterDate(WhatsappRemindLaterRequest whatsappRemindLaterRequest){
        Long customerId = Long.valueOf(whatsappRemindLaterRequest.getCustomerId());
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(customerId);
        recentsPrimaryKey.setService(whatsappRemindLaterRequest.getService());
        recentsPrimaryKey.setRechargeNumber(whatsappRemindLaterRequest.getRechargeNumber());
        recentsPrimaryKey.setOperator(whatsappRemindLaterRequest.getOperator());
        recents.setKey(recentsPrimaryKey);
        recents.setRemindLaterDate(whatsappRemindLaterRequest.getRemindLaterDate());
        WhatsappRemindLaterResponse whatsappRemindLaterResponse = new WhatsappRemindLaterResponse();
        try {
            log.info("[WhatsappRemindLaterService.setRemindLaterDate]:: Setting Remind Later Date for Customer Id: {}",customerId);
            recentService.updateRemindLater(recents,whatsappRemindLaterRequest.getRemindLaterOffset());
            whatsappRemindLaterResponse.setStatus("Success");
            whatsappRemindLaterResponse.setMessage("Remind Later Date Set Successfully");
        }catch (Exception e){
            whatsappRemindLaterResponse.setStatus("Failure");
            whatsappRemindLaterResponse.setMessage("Remind Later Date Set Failed");
        }
        return whatsappRemindLaterResponse;
    }
}
