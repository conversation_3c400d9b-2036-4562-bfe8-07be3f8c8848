package com.paytm.saga.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.saga.dto.ThemeDetails;
import com.paytm.saga.dto.ThemeDto;
import com.paytm.saga.dto.ThemeResponse;
import com.paytm.saga.dto.ViewDetail;
import com.paytm.saga.model.CommonCache;
import com.paytm.saga.model.Theme;
import com.paytm.saga.repository.CommonCacheRepository;
import com.paytm.saga.repository.ThemeRepository;
import com.paytm.saga.util.JsonUtils;

import static com.paytm.saga.common.constant.Constants.WIDTH_HANDLING;

@Service
public class ThemeService {
	private static final CustomLogger logger = CustomLogManager.getLogger(ThemeService.class);
	@Autowired
	private ThemeRepository themeRepository;
	@Autowired
	private CommonCacheRepository commonCacheRepository;

	public void saveTheme(Theme theme) {
		Theme hashObj = new Theme();
		hashObj.setViewItem(UUID.randomUUID().toString());
		hashObj.setThemeType("theme_hash_key");
		List<Theme> themes = new ArrayList<Theme>();
		themes.add(theme);
		themes.add(hashObj);
		themeRepository.saveAll(themes);
	}

	public ThemeResponse getThemes(String themeHashKey) {
		logger.info("Theme fetch request received for themeHashKey", themeHashKey);
		ThemeResponse themeResponse = new ThemeResponse();
			List<Theme> themeHash = themeRepository.findByThemeType("theme_hash_key");



		if (themeHash != null && themeHash.size() > 0) {
			if (themeHashKey == null || !themeHash.get(0).getThemeHash().equals(themeHashKey)) {

				List<CommonCache> commonCaches = commonCacheRepository
						.findByCacheKey("theme_details_" + themeHash.get(0).getThemeHash());

				if (commonCaches != null && !commonCaches.isEmpty()) {
					themeResponse
							.setThemes(JsonUtils.parseJsonToList(commonCaches.get(0).getCacheValue(), ThemeDto.class));
					themeResponse.setThemehash(themeHash.get(0).getThemeHash());
				} else {

					Map<String, List<Theme>> themesMap = this.createMapByThemeType(themeRepository.findAll());
					List<ThemeDto> themeDtos = new ArrayList<ThemeDto>();
					for (String key : themesMap.keySet()) {
						if (!key.equals("theme_hash_key")) {
							ThemeDto themeDto = new ThemeDto();
							themeDto.setThemeType(key);
							List<Theme> themes = themesMap.get(key);
							ThemeDetails themeDetails = new ThemeDetails();
							for (Theme theme : themes) {
								if (theme.getViewItem().contains("cta")) {
									themeDetails = this.setCta(theme, themeDetails);
								} else if (theme.getViewItem().contains("ds")) {
									themeDetails = this.setDisplayValues(theme, themeDetails);
								} else if (theme.getViewItem().contains("header")) {
									themeDetails = this.setHeadings(theme, themeDetails);
								} else if (theme.getViewItem().contains("footer")) {
									themeDetails = this.setFooters(theme, themeDetails);
								} else if (theme.getViewItem().contains("card")) {
									themeDto.setDisplayValuesAlignment(theme.getDisplayValuesAlignment());
									themeDto.setBgColor(theme.getBgColor());
									themeDto.setThemeType(theme.getThemeType());
									themeDto.setShowDisplayValue(theme.getShowDisplayValue());
									themeDto.setCardAlignment(theme.getCardAlignment());
									if(NumberUtils.isParsable(theme.getWidth()))
										themeDto.setWidth(Integer.parseInt(theme.getWidth()));
									themeDto.setBorderColor(theme.getBorderColor());
									themeDto.setValueColor(theme.getValueColor());
								}
							}
							themeDto.setThemeDetail(themeDetails);
							themeDtos.add(themeDto);
						}
					}
					themeResponse.setThemes(themeDtos);
					themeResponse.setThemehash(themeHash.get(0).getThemeHash());
					try {
						CommonCache commonCache = new CommonCache("theme_details_" + themeHash.get(0).getThemeHash(),
								JsonUtils.serialiseJson(themeDtos));
						commonCacheRepository.save(commonCache, 15 * 60);
					} catch (JsonProcessingException e) {
						logger.error(e.getMessage());
					}

				}
			}

		}
		return themeResponse;
	}

	private Map<String, List<Theme>> createMapByThemeType(List<Theme> themes) {
		Map<String, List<Theme>> map = new HashMap<String, List<Theme>>();
		for (Theme theme : themes) {
			List<Theme> themeInfo = null;
			if (map.containsKey(theme.getThemeType())) {
				themeInfo = map.get(theme.getThemeType());
				themeInfo.add(theme);
			} else {
				themeInfo = new ArrayList<Theme>();
				themeInfo.add(theme);
			}
			map.put(theme.getThemeType(), themeInfo);
		}
		return map;
	}

	private ThemeDetails setCta(Theme theme, ThemeDetails themeDetails) {
		if (theme.getViewItem().equals("cta1")) {
			themeDetails.setCta1(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("cta2")) {
			themeDetails.setCta2(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("cta3")) {
			themeDetails.setCta3(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("cta4")) {
			themeDetails.setCta4(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("cta5")) {
			themeDetails.setCta5(this.setViewDetail(theme));

		}
		return themeDetails;
	}

	private ThemeDetails setHeadings(Theme theme, ThemeDetails themeDetails) {
		if (theme.getViewItem().equals("header1")) {
			themeDetails.setHeader1(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("header2")) {
			themeDetails.setHeader2(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("header3")) {
			themeDetails.setHeader3(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("header4")) {
			themeDetails.setHeader4(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("header5")) {
			themeDetails.setHeader5(this.setViewDetail(theme));

		}
		return themeDetails;
	}

	private ThemeDetails setFooters(Theme theme, ThemeDetails themeDetails) {
		if (theme.getViewItem().equals("footer1")) {
			themeDetails.setFooter1(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("footer2")) {
			themeDetails.setFooter2(this.setViewDetail(theme));

		}
		return themeDetails;
	}

	private ThemeDetails setDisplayValues(Theme theme, ThemeDetails themeDetails) {
		if (theme.getViewItem().equals("ds1")) {
			themeDetails.setDs1(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("ds2")) {
			themeDetails.setDs2(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("ds3")) {
			themeDetails.setDs3(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("ds4")) {
			themeDetails.setDs4(this.setViewDetail(theme));

		} else if (theme.getViewItem().equals("ds5")) {
			themeDetails.setDs5(this.setViewDetail(theme));

		}
		return themeDetails;
	}

	private ViewDetail setViewDetail(Theme theme) {
		return new ViewDetail(theme.getValueColor(), theme.getKeyColor(),theme.getBgColor(), theme.getBorderColor(),
				theme.getValueFontWeight(),theme.getKeyFontWeight(), theme.getRichTextColor(), theme.getRightThumbImage(),
				theme.getLeftThumbnail(), theme.getIsAmount(), theme.getTheme());
	}
}
