package com.paytm.saga.service;

import com.paytm.recentutils.service.NickNameService;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.DBUpdateException;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.RecentDao;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import com.paytm.saga.util.*;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import com.paytm.saga.service.impl.ServiceConfig;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


import static com.paytm.saga.common.constant.Constants.CommonConstants.FASTAG_API;
import static com.paytm.saga.common.constant.Constants.CommonConstants.FASTAG_API_UPDATE_INSURANCE;
import static com.paytm.saga.common.exception.ErrorMessages.FETCH_RECENT_API_NON_ALLOWED_SERVICES_ERROR;
import static com.paytm.saga.common.exception.ErrorMessages.FETCH_RECENT_API_RECHARGE_NUMBER_MISSING;


@Service
public class RecentsServiceImpl implements RecentsService {

    private final CustomLogger logger = CustomLogManager.getLogger(RecentsServiceImpl.class);


    @Autowired
    private MetricsHelper metricsHelper;

    @Autowired
    @Qualifier("monitoringClient")
    private StatsDClient monitoringClient;
    @Autowired
    private RecentDao recentDao;
    @Autowired
    private RecentsRepositoryWrapperService recentsRepository;
    @Autowired
    private SmartRecentsRepository smartRecentsRepository;
    @Autowired
    @Qualifier("deleteCreditCardRecentCard")
    private DeleteRecentCard deleteCreditCardRecentCard;
    @Autowired
    @Qualifier("deleteNonCreditCardRecentCard")
    private DeleteRecentCard deleteNonCreditCardRecentCard;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private ServiceConfig serviceConfig;

    @Autowired
    private RecentService recentService;

    @Autowired
    private RechargeNumberMapService rechargeNumberMapService;

    @Autowired
    private RecentDataToKafkaService recentDataToKafkaService;



    @Override
    public NickNameResponse updateNickName(NickNameRequest nickNameRequest) {
        NickNameResponse nickNameResponse = new NickNameResponse();
        try {
            if(Objects.nonNull(nickNameRequest.getNickName())){
                nickNameRequest.setNickName(nickNameRequest.getNickName().trim());
            }
            if((nickNameRequest.getNickName() !=null && !StringUtils.isEmpty(nickNameRequest.getNickName())) && ( !this.isStringOnlyAlphabet(nickNameRequest.getNickName())|| nickNameRequest.getNickName().length()> RecentUtils.getNicknameLength(monitoringClient))){
                logger.info("[RecentsController.updateNickName]: invalid length of nickname {} for customerid={},recharge_number={},service={} and operator={}",
                        nickNameRequest.getNickName(),nickNameRequest.getCustomerId(), nickNameRequest.getRechargeNumber(), nickNameRequest.getService(), nickNameRequest.getOperator());
                nickNameResponse.setStatusCode(400);
                nickNameResponse.setDisplayMessage("invalid nickname length");
                metricsHelper.recordSuccessRate(Constants.NICK_NAME_UPDATE_SERVICE,Constants.NICK_NAME_UPDATE_INAVLID_LENGTH_TAG);
                return nickNameResponse;
            }

            nickNameRequest.setService(nickNameRequest.getService().toLowerCase());
            nickNameRequest.setOperator(nickNameRequest.getOperator().toLowerCase());
            Boolean exisitngRecord = null;
            try{
                 exisitngRecord = recentService.fetchAndUpdateNickNameInRecents(nickNameRequest);
            }catch (Exception e){
                logger.error("[RecentsServiceImpl.updateNickname] :: Update failed in recent even after retry for request {} and error {}",nickNameRequest,e);
            }
            if(Boolean.TRUE.equals(exisitngRecord)){
                try {
                    kafkaProducerService.sendMessage(Long.toString(nickNameRequest.getCustomerId()));
                } catch (Exception e) {
                    logger.error("[RecentsServiceImpl.nicknameupdate] kafka cache clean event publish error for customer id {}, Exception {}",
                            nickNameRequest.getCustomerId(), e);
                }
                nickNameResponse.setStatusCode(200);
                nickNameResponse.setDisplayMessage("[NickNameService.updateNickName] nickname updated");
            } else {
                nickNameResponse.setStatusCode(400);
                nickNameResponse.setDisplayMessage("[NickNameService.updateNickName] Record not found in DB");
                logger.error("[NickNameService.updateNickName]: No record found in db for customerid={},recharge_number={},service={} and operator={}",
                        nickNameRequest.getCustomerId(), nickNameRequest.getRechargeNumber(), nickNameRequest.getService(), nickNameRequest.getOperator());
                metricsHelper.recordSuccessRate(Constants.NICK_NAME_UPDATE_SERVICE,Constants.NICK_NAME_UPDATE_USER_NOT_EXISTS_TAG);
            }
        } catch (Exception exception) {
            nickNameResponse.setStatusCode(400);
            nickNameResponse.setDisplayMessage(exception.getMessage());
            metricsHelper.recordSuccessRate(Constants.NICK_NAME_UPDATE_SERVICE,Constants.NICK_NAME_UPDATE_ERROR_TAG);
            logger.error("[AutomaticDataListener.listen] Error {}", exception.getMessage());
        }
        return nickNameResponse;
    }

    private boolean isStringOnlyAlphabet(String str)
    {
        return ((!str.equals(""))
                && (str != null)
                && (str.matches("^[a-zA-Z0-9 \\-]*$")));
    }

    public List<Recents> filterFastagRecents(List<Recents> recents, DeleteRequestBody deleteRequestBody) {
        return recents.stream().filter(recent -> {
            if (recent.getKey().getService().equalsIgnoreCase(Constants.FASTAG)) {
                if (recent.getKey().getOperator() != null && recent.getKey().getOperator().equalsIgnoreCase(Constants.FASTAG_LOW_BALANCE_OPERATOR)) {
                    return recent.getKey().getRechargeNumber().equalsIgnoreCase(deleteRequestBody.getRechargeNumber());
                } else {
                    return recent.getKey().getRechargeNumber().equals(deleteRequestBody.getRechargeNumber());
                }
            }
            return false;
        }).collect(Collectors.toList());
    }

    public List<Recents> filterRecents(List<Recents> recents,DeleteRequestBody deleteRequestBody) {
        if(deleteRequestBody.getService() == null) {
            return recents;
        }
        if (deleteRequestBody.getService().equalsIgnoreCase(Constants.FASTAG)) {
            return filterFastagRecents(recents, deleteRequestBody);
        }
        return recents;
    }

    public NickNameResponse removeRecent(DeleteRequestBody deleteRequestBody) {
        int agentLimit = serviceConfig.getAgentLimit() + 1;
        List<Recents>  recents = recentsRepository.findByCustomerId(deleteRequestBody.getCustomerId(),agentLimit);
        recents = filterRecents(recents, deleteRequestBody);
        logger.info("[RecentsServiceImpl.removeRecent]:: recents to delete: {}",recents.size());
        if(Objects.nonNull(recents)
                && !recents.isEmpty()){
            for(Recents recentsObj : recents){
                try{
                    DeleteRecentCard deleteRecentCard=this.getDeleteCardInstance(recentsObj.getPayType(),deleteRequestBody.getPaytype());
                    deleteRecentCard.deleteRecentCard(recentsObj,deleteRequestBody);
                }catch(RuntimeException | RecentDataToKafkaException runtimeException){
                    metricsHelper.recordSuccessRate(Constants.DELETE_RECENT_SERVICE,Constants.DELETE_RECENT_ERROR_TAG);
                    logger.error("removeRecent exception",runtimeException);
                }
            }
            try {
                kafkaProducerService.sendMessage(Long.toString(deleteRequestBody.getCustomerId()));
            } catch (Exception e) {
                logger.error("[RecentsServiceImpl.removeRecent] kafka cache clean event publish error for data {}, Exception {}",
                        deleteRequestBody.toString(), e);
            }
        }
        return this.createSuccessResponse();
    }

    private NickNameResponse removeSmartRecent(DeleteRequestBody deleteRequestBody) {
        logger.info("[RecentsServiceImpl.removeSmartRecent]");
        
        ProductMin productMin=CVRProductCache.getInstance().getProductDetails(deleteRequestBody.getProductId());
        if(Objects.nonNull(productMin)){
            deleteRequestBody.setOperator(productMin.getOperator());
        }
        if(Objects.nonNull(deleteRequestBody.getService())
                && Objects.nonNull(deleteRequestBody.getOperator())){
            smartRecentsRepository.deleteRecentByCustomerIdAndServiceAndOperator(deleteRequestBody.getCustomerId()
                    ,deleteRequestBody.getService().toLowerCase()
                    ,deleteRequestBody.getOperator().toLowerCase());
            invalidateCache(deleteRequestBody.getCustomerId());
        }
        return this.createSuccessResponse();
    }

    private void invalidateCache(Long customerId){
        try {
            kafkaProducerService.sendMessage(Long.toString(customerId));
        } catch (Exception e) {
            logger.error("[RecentsServiceImpl.invalidateCache] kafka cache clean event publish error for data {}, Exception {}",
                    Long.toString(customerId), e);
        }
    }

    @Override
    public NickNameResponse removeRecentWrapper(DeleteRequestBody deleteRequestBody) {
        if(StringUtils.isEmpty(deleteRequestBody.getRechargeNumber())
            || "dummyRechargeNumber".equalsIgnoreCase(deleteRequestBody.getRechargeNumber())){
            return this.removeSmartRecent(deleteRequestBody);
        }else{
            return this.removeRecent(deleteRequestBody);
        }
    }

    @Override
    public String getCustomerName(GetHistoryPageDTO getHistoryPageDTO, Boolean isNameRequired){
        String customerName = null;
        ArrayList<String> nickNames = new ArrayList<>();
        ArrayList<String> consumerNames = new ArrayList<>();
        try {
            if (isNameRequired) {
                List<Recents> recents = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(getHistoryPageDTO.getCustomerId(), getHistoryPageDTO.getService(), getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getOperator());
                if (Objects.nonNull(recents)) {
                    for (Recents recent : recents){
                        nickNames.add(getNonEmptyCustomerName(recent.getNickName()));
                        consumerNames.add(getNonEmptyCustomerName(recent.getConsumerName()));
                    }
                    nickNames.removeAll(Collections.singletonList(null));
                    consumerNames.removeAll(Collections.singletonList(null));
                    if(nickNames.isEmpty() && consumerNames.isEmpty()) return null;
                    if(nickNames.isEmpty()) nickNames.add(null);
                    else if (consumerNames.isEmpty()) {
                        consumerNames.add(null);
                    }
                    NickNameService nickNameService = new NickNameService();
                    customerName = nickNameService.getNickName(nickNames.get(0),consumerNames.get(0),null);
                }
            }
        }
        catch(RuntimeException runtimeException){
            logger.error("getCustomerName exception",runtimeException);
        }

        return customerName;
    }

    @Override
    public FetchRecentsResponse fetchRecentsByRechargeNumber(FetchRecentsRequest fetchRecentsRequest) throws RechargeSagaBaseException {
        logger.trace("[RecentsServiceImpl.fetchRecentsByRechargeNumber] funciton starting");
        ValidationUtils.validateByGroup(fetchRecentsRequest, RequestValidatorV2.class);
        if(StringUtils.equalsIgnoreCase(Constants.FINANCIAL_SERVICES,fetchRecentsRequest.getService())){
            throw new RechargeSagaBaseException(FETCH_RECENT_API_NON_ALLOWED_SERVICES_ERROR);
        }

        FetchRecentsResponse fetchRecentsResponse = new FetchRecentsResponse();
        List<Long> customerIds = rechargeNumberMapService.fetchCustomerIdsFromDb(fetchRecentsRequest.getRechargeNumber(), fetchRecentsRequest.getService(), fetchRecentsRequest.getOperator());
        logger.trace("[RecentsServiceImpl.fetchRecentsByRechargeNumber]:customerIds fetched from recharge_to_customer_map table ={}",customerIds);
        if (!CollectionUtils.isEmpty(customerIds)) {
            List<Recents> recents = recentService.getRecents(fetchRecentsRequest, customerIds);

            if (!CollectionUtils.isEmpty(recents)) {
                RecentUtils.prepareRecentResponse(fetchRecentsResponse, recents);
            }
        }

        logger.trace("[RecentsServiceImpl.fetchRecentsByRechargeNumber] Response={}",fetchRecentsResponse);
        return fetchRecentsResponse;
    }

    @Override
    public FetchRecentsResponse fetchRecentsByCustomerId(FetchRecentsRequest fetchRecentsRequest) throws RechargeSagaBaseException {
        logger.trace("[RecentsServiceImpl.fetchRecentsByCustomerId] function starting");
        ValidationUtils.validateByGroup(fetchRecentsRequest, RequestValidator.class);
        if(!StringUtils.equalsIgnoreCase(Constants.FINANCIAL_SERVICES,fetchRecentsRequest.getService()) && StringUtils.isEmpty(fetchRecentsRequest.getRechargeNumber())){
            throw new RechargeSagaBaseException(FETCH_RECENT_API_RECHARGE_NUMBER_MISSING);
        }
        FetchRecentsResponse fetchRecentsResponse = new FetchRecentsResponse();
        Long customerId = fetchRecentsRequest.getCustomerId();
        String rechargeNumber = fetchRecentsRequest.getRechargeNumber();
        String operator = fetchRecentsRequest.getOperator();
        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());

        List<Recents> recents = recentDao.findByParams(customerId, services, rechargeNumber, operator, null, null);
        logger.trace("[RecentsServiceImpl.fetchRecentsByCustomerId] recents from db {}",recents);
        if (!CollectionUtils.isEmpty(recents)) {
            recents = filterRecentsFromDb(recents, fetchRecentsRequest);
            logger.trace("[RecentsServiceImpl.fetchRecentsByCustId] : Recents in response : {}", recents);
            RecentUtils.prepareRecentResponse(fetchRecentsResponse, recents);
        }

        return fetchRecentsResponse;
    }

    @Retryable(value = {DBUpdateException.class},
            maxAttemptsExpression = "${db.writes.max.attempts}")
    @Override
    public Boolean createRecentForInsurance(CreateRecentRequest request) throws DBUpdateException {
        Boolean result = false;
        try {
            Recents recents = null;
            List<Recents> oldRecentList = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(request.getCustomerId(),request.getService().toLowerCase(),request.getRechargeNumber(),request.getOperator().toLowerCase());
            if(oldRecentList.isEmpty()) {
                recents = new Recents();
                RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
                recentsPrimaryKey.setCustomerId(request.getCustomerId());
                recentsPrimaryKey.setOperator(request.getOperator().toLowerCase());
                recentsPrimaryKey.setRechargeNumber(request.getRechargeNumber());
                recentsPrimaryKey.setService(request.getService().toLowerCase());
                recentsPrimaryKey.setPlanBucket(request.getPlanBucket());
                recents.setKey(recentsPrimaryKey);
                recents.setCreatedAt(new Date());
                recents.setIsValidation(Boolean.TRUE);
                recents.setUpdatedAt(new Date());
                recents.setProductId(request.getProductId());
                recents.setPayType(request.getPaytype().toLowerCase());
                if(Objects.nonNull(request.getInsuranceCard()))
                    recents.setInsuranceCard(RecentUtils.getMapper().writeValueAsString(request.getInsuranceCard()));
                recentDataToKafkaService.setRecentConsumerSource(recents,FASTAG_API+"_CREATE");
                result =  recentsRepository.updateRecentWhenNoExistingData(recents, TTLUtils.getTTL(request.getService()));
            }
            else {
                recents = oldRecentList.get(0);
                Date oldUpdatedat = recents.getUpdatedAt();
                recents.setUpdatedAt(new Date());
                recents.setInsuranceCard(RecentUtils.getMapper().writeValueAsString(request.getInsuranceCard()));
                recentDataToKafkaService.setRecentConsumerSource(recents,FASTAG_API+"_UPDATE");
                result = recentsRepository.updateRecentWhenDataAlreadyExist(recents, oldUpdatedat,TTLUtils.getTTL(request.getService()));
            }
            if(Boolean.FALSE.equals(result)){
                throw new DBUpdateException("Update in createRecentForInsurance failed");
            }

        }catch (Exception e){
            logger.error("[RecentServiceImpl :: createRecentForInsurance ] Error while createing recent ",e);
            throw new DBUpdateException(e);
        }
        return result;
    }

    @Retryable(value = {DBUpdateException.class},
            maxAttemptsExpression = "${db.writes.max.attempts}")
    @Override
    public Boolean updateRecentForInsurance(Recents recents, String insuranceCard){
        Boolean result = false;
        try {
                List<Recents> recentsInDB = recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(recents.getKey().getCustomerId(),recents.getKey().getService(),recents.getKey().getRechargeNumber(),recents.getKey().getOperator(),recents.getKey().getPlanBucket());
                for(Recents recent : recentsInDB) {
                    recent.setInsuranceCard(insuranceCard);
                    result = recentsRepository.updateRecentWhenDataAlreadyExist(recent, recent.getUpdatedAt(), TTLUtils.getTTL(recent.getKey().getService()));
                    recentDataToKafkaService.setRecentConsumerSource(recents, FASTAG_API_UPDATE_INSURANCE);
                }

            if(Boolean.FALSE.equals(result)){
                throw new DBUpdateException("Update in card_insurance failed");
            }

        }catch (Exception e){
            logger.error("[RecentServiceImpl :: updateRecentForInsurance ] Error while updating recent ",e);
        }
        return result;
    }

    private List<Recents> filterRecentsFromDb(List<Recents> recents, FetchRecentsRequest fetchRecentsRequest) {
        List<Recents> finalRecents;
        finalRecents = CommonUtils.collectionToStream(recents)
                .filter(RecentUtils.getFetchRecentslFilterPredicate(fetchRecentsRequest)).collect(Collectors.toList());
        return finalRecents;
    }

    public String getNonEmptyCustomerName(String s){
        if(StringUtils.isBlank(s)) return null;
        return s;
    }

    private NickNameResponse createSuccessResponse(){
        NickNameResponse nickNameResponse=new NickNameResponse();
        nickNameResponse.setDisplayMessage("success");
        nickNameResponse.setStatusCode(200);
        return nickNameResponse;
    }

    private DeleteRecentCard getDeleteCardInstance(String recentPaytype,String requestPaytype){
        if("credit card".equalsIgnoreCase(requestPaytype)){
            return deleteCreditCardRecentCard;
        }else{
            return deleteNonCreditCardRecentCard;
        }
    }
}
