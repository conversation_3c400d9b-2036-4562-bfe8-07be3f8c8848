package com.paytm.saga.enums;

import java.util.HashMap;

public class AccountTypeMapper {
    private static final HashMap<Integer,String> ACCOUNT_TYPE_MAPPING = new HashMap<>();

    static {
        ACCOUNT_TYPE_MAPPING.put(1,"BANK");
        ACCOUNT_TYPE_MAPPING.put(2,"UPI");
        ACCOUNT_TYPE_MAPPING.put(3,"MOBILE");
    }

    public static String getAccountTypeValue(Integer account) {
        return ACCOUNT_TYPE_MAPPING.getOrDefault(account, null);
    }

}

