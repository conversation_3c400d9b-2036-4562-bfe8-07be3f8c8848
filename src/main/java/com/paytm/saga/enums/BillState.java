package com.paytm.saga.enums;

public enum BillState {
    NO_DATE("NO_DATE"),
    NO_DUE("NO_DUE"),
    WILL_EXPIRE("WILL_EXPIRE"),
    EXPIRES_TODAY("EXPIRES_TODAY"),
    EXPIRES_TOMORROW("EXPIRES_TOMORROW"),
    ALREADY_EXPIRED("ALREADY_EXPIRED"),
    ALREADY_EXPIRED_TODAY("ALREADY_EXPIRED_TODAY"),
    WILL_EXPIRE_AUTOMATIC("WILL_EXPIRE_AUTOMATIC"),
    EXPIRES_TODAY_AUTOMATIC("EXPIRES_TODAY_AUTOMATIC"),
    EXPIRES_TOMORROW_AUTOMATIC("EXPIRES_TOMORROW_AUTOMATIC"),
    NO_DATE_NO_AMOUNT("NO_DATE_NO_AMOUNT"),
    EXPIRED("EXPIRED"),
    EXPIRING_SOON("EXPIRING_SOON"),
    INCOMING_STOPPED("INCOMING_STOPPED"),
    NO_DUE_OUT_AMT("NO_DUE_OUT_AMT"),
    AMOUNT_NULL("AMOUNT_NULL"),
    AMOUNT_NEGATIVE("AMOUNT_NEGATIVE");

    private String eventType;

    BillState(String eventType) {
        this.eventType = eventType;
    }

}
