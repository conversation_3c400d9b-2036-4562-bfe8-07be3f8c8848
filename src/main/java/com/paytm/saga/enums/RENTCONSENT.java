package com.paytm.saga.enums;


import java.util.Objects;

public enum RENTCONSENT {
    W_YES(1),
    W_NO(2),
    W_SENT(3),
    W_NOTRECIEVED(4),
    OLD_BILLER(5),
    W_DEFAULT(0);
    private final Integer constant;
    private RENTCONSENT(Integer constant) {
        this.constant = constant;
    }
    public Integer getConstant () {
        return this.constant;
    }
    public static RENTCONSENT valueOfInteger(Integer constant) {
        if(Objects.isNull(constant)){
            return W_DEFAULT;
        }
        switch (constant) {
            case 1:
                return W_YES;
            case 2:
                return W_NO;
            case 3:
                return W_SENT;
            case 4:
                return W_NOTRECIEVED;
            case 5:
                return OLD_BILLER;
            default:
                return W_DEFAULT;
        }
    }

    public static RENTCONSENT valueOfString(String constant) {
        if(Objects.isNull(constant)){
            return W_DEFAULT;
        }
        switch (constant) {
            case "W_YES":
                return W_YES;
            case "W_NO":
                return W_NO;
            case "W_SENT":
                return W_SENT;
            case "W_NOTRECIEVED":
                return W_NOTRECIEVED;
            case "OLD_BILLER":
                return OLD_BILLER;
            default:
                return W_DEFAULT;
        }
    }
}
