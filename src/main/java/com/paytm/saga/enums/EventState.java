package com.paytm.saga.enums;

public enum EventState {
    DROP_OFF_SUCCESS("DROP_OFF_SUCCESS"),
    REC<PERSON><PERSON><PERSON>_SUCCESS("REC<PERSON>R<PERSON>_SUCCESS"),
    REC<PERSON><PERSON><PERSON>_PENDING("<PERSON><PERSON><PERSON><PERSON><PERSON>_PENDING"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>_PENDING_NO_CONSENT("R<PERSON><PERSON><PERSON><PERSON>_PENDING_NO_CONSENT"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>_PENDING_CONSENT_NOTRECEIVED("RECHARGE_PENDING_CONSENT_NOTRECEIVED"),
    RECHARGE_FAILURE_CONSENT_NOTRECEIVED("REC<PERSON>R<PERSON>_FAILURE_CONSENT_NOTRECEIVED"),
    NEW_ACCOUNT_CONSENT_NOTRECEIVED("NEW_ACCOUNT_CONSENT_NOTRECEIVED"),
    RECHARGE_PENDING_CONSENT_PENDING("REC<PERSON><PERSON><PERSON>_PENDING_CONSENT_PENDING"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>_CANCEL("R<PERSON><PERSON>RGE_CANCEL"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>_FAILURE("<PERSON><PERSON><PERSON><PERSON><PERSON>_FAILURE"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>_FAILURE_NO_CONSENT("RECHARGE_FAILURE_NO_CONSENT"),
    NEW_ACCOUNT_NO_CONSENT("NEW_ACCOUNT_NO_CONSENT"),
    RECHARGE_FAILURE_CONSENT_PENDING("RECHARGE_FAILURE_CONSENT_PENDING"),
    NEW_ACCOUNT_CONSENT_PENDING("NEW_ACCOUNT_CONSENT_PENDING"),
    RECHARGE_AUTOMATIC_SUCCESS("RECHARGE_AUTOMATIC_SUCCESS"),
    RECHARGE_AUTOMATIC_PENDING("RECHARGE_AUTOMATIC_PENDING"),
    RECHARGE_AUTOMATIC_CANCEL("RECHARGE_AUTOMATIC_CANCEL"),
    RECHARGE_AUTOMATIC_FAILURE("RECHARGE_AUTOMATIC_FAILURE"),
    MARK_AS_PAID("MARK_AS_PAID"),
    SMS_CARD("SMS_CARD"),
    UPI_CARD("UPI_CARD"),
    NEW_ACCOUNT("NEW_ACCOUNT"),
    SMART_RECENT_VALIDATION("SMART_RECENT_VALIDATION"),
    SMART_RECENT_DROPOFF("SMART_RECENT_DROPOFF"),
    NEW_ACCOUNT_BROWSE_PLAN("NEW_ACCOUNT_BROWSE_PLAN"),
    SMS_CARD_NO_AMOUNT("SMS_CARD_NO_AMOUNT"),
    MARK_AS_PAID_NO_AMOUNT("MARK_AS_PAID_NO_AMOUNT"),
    SMS_CARD_MIN_AMOUNT("SMS_CARD_MIN_AMOUNT"),
    MARK_AS_PAID_MIN_AMOUNT("MARK_AS_PAID_MIN_AMOUNT"),
    SMS_CARD_TXN("SMS_CARD_TXN"),
    PG_DELETED_AMW("PG_DELETED_AMW"),
    RENEW_AUTOMATIC("RENEW_AUTOMATIC"),
    OLD_BILL_PENALTY("OLD_BILL_PENALTY"),
    OLD_BILL_AVOID_DISCONNECTION("OLD_BILL_AVOID_DISCONNECTION"),
    LOW_BALANCE("LOW_BALANCE"),
    HEURISTIC_LOW_BALANCE("HEURISTIC_LOW_BALANCE"),
    PARTIAL_BILL("PARTIAL_BILL"),
    PREPAID_BILL_PAID("PREPAID_BILL_PAID"),
    PREPAID_BILL_GEN("PREPAID_BILL_GEN"),
    OLD_BILLER("OLD_BILLER"),
    DATA_EXHAUST_50("DATA_EXHAUST_50"),
    DATA_EXHAUST_90("DATA_EXHAUST_90"),
    DATA_EXHAUST_100("DATA_EXHAUST_100"),
    LOW_BALANCE_NO_AMOUNT("LOW_BALANCE_NO_AMOUNT");

    private String eventType;

    EventState(String eventType) {
        this.eventType = eventType;
    }

}
