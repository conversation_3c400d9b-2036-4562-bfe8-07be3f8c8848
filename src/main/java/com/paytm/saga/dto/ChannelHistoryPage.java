package com.paytm.saga.dto;

import java.util.List;

import com.paytm.saga.model.ChannelHistory;

public class ChannelHistoryPage {
	private String lastCardId;
	private boolean availableNext;
	private List<ChannelHistory> channelHistories;
	private List<ChannelHistory> markFinaliseData;
	private List<ChannelHistory> moveToFinaliseData;
	

	public String getLastCardId() {
		return lastCardId;
	}

	public void setLastCardId(String lastCardId) {
		this.lastCardId = lastCardId;
	}

	public boolean isAvailableNext() {
		return availableNext;
	}

	public void setAvailableNext(boolean availableNext) {
		this.availableNext = availableNext;
	}

	public List<ChannelHistory> getChannelHistories() {
		return channelHistories;
	}

	public void setChannelHistories(List<ChannelHistory> channelHistories) {
		this.channelHistories = channelHistories;
	}

	public List<ChannelHistory> getMarkFinaliseData() {
		return markFinaliseData;
	}

	public void setMarkFinaliseData(List<ChannelHistory> markFinaliseData) {
		this.markFinaliseData = markFinaliseData;
	}

	public List<ChannelHistory> getMoveToFinaliseData() {
		return moveToFinaliseData;
	}

	public void setMoveToFinaliseData(List<ChannelHistory> moveToFinaliseData) {
		this.moveToFinaliseData = moveToFinaliseData;
	}

}
