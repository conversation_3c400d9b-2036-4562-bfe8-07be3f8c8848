package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.util.JsonUtils;
import lombok.ToString;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

@ToString
public class FeatureConfig {

    private final Logger logger = LogManager.getLogger(FeatureConfig.class);

    @JsonProperty("id")
    private String id;
    @JsonProperty("feature_name")
    private String featureName;
    @JsonProperty("key_name")
    private String keyName;
   // private String config;
    private Integer status;
    @JsonProperty("service_name")
    private String serviceName;
    @JsonProperty("config")
    String config;
    @JsonIgnore
    Map<String, Object> serviceConfig;

    public void parseConfig() {
       serviceConfig = new HashMap<>();
        try {
            serviceConfig = JsonUtils.parseMapJson(config);
        } catch (Exception e) {
            logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }

    public Map<String, Object> getConfigMap() {
        return serviceConfig;
    }

    public String getServiceName() {
        return serviceName;
    }

}
