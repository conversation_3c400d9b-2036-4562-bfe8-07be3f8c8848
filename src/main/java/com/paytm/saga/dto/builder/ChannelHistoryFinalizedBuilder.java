package com.paytm.saga.dto.builder;

import java.util.Date;
import java.util.Map;

import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ChannelHistoryFinalized;

public class ChannelHistoryFinalizedBuilder {

	private Long customerId;
	private String rechargeNumber;
	private String service;
	private Date transactionTime;
	private Long orderId;
	private Long itemId;
	private Date transactionUpdateTime;
	private Long categoryId;
	private String recharge_number_2;
	private String recharge_number_3;
	private String recharge_number_4;
	private String operator;
	private String paytype;
	private String amount;
	private Map<String, String> billsObj;
	private Map<String, String> displayValues;
	private String status;
	private String eventType;
	private Date createdTime;
	private boolean finalisedState;
	private Long productId;
	private String circle;
	private String inResponseCode;
	private String paymentStatus;

	public ChannelHistoryFinalizedBuilder setCustomerId(Long customerId) {
		this.customerId = customerId;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setRechargeNumber(String rechargeNumber) {
		this.rechargeNumber = rechargeNumber;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setService(String service) {
		this.service = service;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setOrderId(Long orderId) {
		this.orderId = orderId;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setItemId(Long itemId) {
		this.itemId = itemId;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setTransactionUpdateTime(Date transactionUpdateTime) {
		this.transactionUpdateTime = transactionUpdateTime;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setRecharge_number_2(String recharge_number_2) {
		this.recharge_number_2 = recharge_number_2;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setRecharge_number_3(String recharge_number_3) {
		this.recharge_number_3 = recharge_number_3;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setRecharge_number_4(String recharge_number_4) {
		this.recharge_number_4 = recharge_number_4;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setOperator(String operator) {
		this.operator = operator;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setPaytype(String paytype) {
		this.paytype = paytype;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setAmount(String amount) {
		this.amount = amount;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setBillsObj(Map<String, String> billsObj) {
		this.billsObj = billsObj;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setDisplayValues(Map<String, String> displayValues) {
		this.displayValues = displayValues;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setStatus(String status) {
		this.status = status;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setEventType(String eventType) {
		this.eventType = eventType;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setFinalisedState(boolean finalisedState) {
		this.finalisedState = finalisedState;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setProductId(Long productId) {
		this.productId = productId;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setCircle(String circle) {
		this.circle = circle;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setInResponseCode(String inResponseCode) {
		this.inResponseCode = inResponseCode;
		return this;
	}

	public ChannelHistoryFinalizedBuilder setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
		return this;
	}

	public ChannelHistoryFinalized build() {
		return new ChannelHistoryFinalized(customerId, rechargeNumber, productId, orderId, createdTime, categoryId,
				recharge_number_2, recharge_number_3, recharge_number_4, operator, paytype, service, amount, billsObj,
				status, eventType, transactionTime, transactionUpdateTime, finalisedState, itemId, displayValues,
				circle, paymentStatus, inResponseCode);
	}

}
