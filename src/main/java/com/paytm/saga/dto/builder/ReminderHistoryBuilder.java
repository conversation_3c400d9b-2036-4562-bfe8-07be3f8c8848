package com.paytm.saga.dto.builder;

import java.util.Date;

import com.paytm.saga.model.ReminderHistory;

public class ReminderHistoryBuilder {
	private Long customerId;
	private String rechargeNumber;
	private String service;
	private String operator;
	private Date updatedAt;
	private String paytype;
	private String circle;
	private Integer status;
	private Double amount;
	private Long productId;
	private Date created_at;
	private Date bill_date;
	private Date due_date;
	private Integer is_automatic;
	private Integer isPartial;
	private Double originalMinBillAmount;
	private String panUniqueReference;
	private String cin;
	

	public ReminderHistoryBuilder setCustomerId(Long customerId) {
		this.customerId = customerId;
		return this;
	}

	public ReminderHistoryBuilder setRechargeNumber(String rechargeNumber) {
		this.rechargeNumber = rechargeNumber;
		return this;
	}

	public ReminderHistoryBuilder setService(String service) {
		this.service = service;
		return this;
	}

	public ReminderHistoryBuilder setOperator(String operator) {
		this.operator = operator;
		return this;
	}

	public ReminderHistoryBuilder setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
		return this;
	}

	public ReminderHistoryBuilder setPaytype(String paytype) {
		this.paytype = paytype;
		return this;
	}

	public ReminderHistoryBuilder setCircle(String circle) {
		this.circle = circle;
		return this;
	}

	public ReminderHistoryBuilder setStatus(Integer status) {
		this.status = status;
		return this;
	}

	public ReminderHistoryBuilder setAmount(Double amount) {
		this.amount = amount;
		return this;
	}

	public ReminderHistoryBuilder setProductId(Long productId) {
		this.productId = productId;
		return this;
	}

	public ReminderHistoryBuilder setCreated_at(Date created_at) {
		this.created_at = created_at;
		return this;
	}

	public ReminderHistoryBuilder setBill_date(Date bill_date) {
		this.bill_date = bill_date;
		return this;
	}

	public ReminderHistoryBuilder setDue_date(Date due_date) {
		this.due_date = due_date;
		return this;
	}

	public ReminderHistoryBuilder setIs_automatic(Integer is_automatic) {
		this.is_automatic = is_automatic;
		return this;
	}

	public ReminderHistoryBuilder setIsPartial(Integer isPartial) {
		this.isPartial = isPartial;
		return this;
	}

	public void setOriginalMinBillAmount(Double originalMinBillAmount) {
		this.originalMinBillAmount = originalMinBillAmount;
	}

	public void setPanUniqueReference(String panUniqueReference) {
		this.panUniqueReference = panUniqueReference;
	}

	public ReminderHistory build() {
		return new ReminderHistory(customerId, rechargeNumber, service, operator, updatedAt, paytype, circle, status,
				amount, productId, created_at, bill_date, due_date, is_automatic, isPartial, panUniqueReference,cin,null,null);
	}

}
