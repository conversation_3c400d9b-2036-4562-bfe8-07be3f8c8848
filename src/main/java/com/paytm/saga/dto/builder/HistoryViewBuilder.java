package com.paytm.saga.dto.builder;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;

public class HistoryViewBuilder {
	private Date eventDate;
	private Date previousDate;
	private int planExpiryDays;
	private Map<String, String> planDetail;
	private boolean invalidPlan;
	private String themeType;
	private String createdDate;
	private double amount;
	private String cardId;
	private String status;
	private long orderId;
	private String planBucket;
	private String carddeeplink;
	private List<ViewElementInfo> headings;
	private List<ViewElementInfo> displayValues;
	private List<ViewElementInfo> cta;

	private List<ViewElementInfo> childCta;
	private List<ViewElementInfo> footer;
	private String circle;
	private String operator;
	private String service;
	private String payType;
	private String eventType;
	private boolean mnp;
	private boolean lastCard;
	private String planDescription;
	private String planExpiryDate;
	private boolean ivrsBooking;
	private String operatorLabel;
	private String bookingId;
	private String billDate;
	private String dueDate;
	private String previousPayType;
	private String pgRespCode;
	private boolean isRetryExhausted;

	private double minimumDueAmount;
	public String getPreviousPayType() {
		return previousPayType;
	}

	public void setPreviousPayType(String previousPayType) {
		this.previousPayType = previousPayType;
	}


	public HistoryViewBuilder setEventDate(Date eventDate) {
		this.eventDate = eventDate;
		return this;
	}

	public HistoryViewBuilder setPreviousDate(Date previousDate) {
		this.previousDate = previousDate;
		return this;
	}

	public HistoryViewBuilder setPlanExpiryDays(int planExpiryDays) {
		this.planExpiryDays = planExpiryDays;
		return this;
	}

	public HistoryViewBuilder setPlanDetail(Map<String, String> planDetail) {
		this.planDetail = planDetail;
		return this;
	}

	public HistoryViewBuilder setInvalidPlan(boolean invalidPlan) {
		this.invalidPlan = invalidPlan;
		return this;
	}

	public HistoryViewBuilder setThemeType(String themeType) {
		this.themeType = themeType;
		return this;
	}

	public HistoryViewBuilder setCreatedDate(String createdDate) {
		this.createdDate = createdDate;
		return this;
	}

	public HistoryViewBuilder setAmount(double amount) {
		this.amount = amount;
		return this;
	}

	public HistoryViewBuilder setCardId(String cardId) {
		this.cardId = cardId;
		return this;
	}

	public HistoryViewBuilder setStatus(String status) {
		this.status = status;
		return this;
	}

	public HistoryViewBuilder setOrderId(long orderId) {
		this.orderId = orderId;
		return this;
	}

	public HistoryViewBuilder setPlanBucket(String planBucket) {
		this.planBucket = planBucket;
		return this;
	}

	public HistoryViewBuilder setCarddeeplink(String carddeeplink) {
		this.carddeeplink = carddeeplink;
		return this;
	}

	public HistoryViewBuilder setHeadings(List<ViewElementInfo> headings) {
		this.headings = headings;
		return this;
	}

	public HistoryViewBuilder setDisplayValues(List<ViewElementInfo> displayValues) {
		this.displayValues = displayValues;
		return this;
	}

	public HistoryViewBuilder setCta(List<ViewElementInfo> cta) {
		this.cta = cta;
		return this;
	}

	public HistoryViewBuilder setChildCta(List<ViewElementInfo> childCta) {
		this.childCta = childCta;
		return this;
	}

	public HistoryViewBuilder setFooter(List<ViewElementInfo> footer) {
		this.footer = footer;
		return this;
	}

	public HistoryViewBuilder setCircle(String circle) {
		this.circle = circle;
		return this;
	}

	public HistoryViewBuilder setOperator(String operator) {
		this.operator = operator;
		return this;
	}

	public HistoryViewBuilder setService(String service) {
		this.service = service;
		return this;
	}

	public HistoryViewBuilder setPayType(String payType) {
		this.payType = payType;
		return this;
	}

	public HistoryViewBuilder setEventType(String eventType) {
		this.eventType = eventType;
		return this;
	}

	public HistoryViewBuilder setMnp(boolean mnp) {
		this.mnp = mnp;
		return this;
	}

	public HistoryViewBuilder setLastCard(boolean lastCard) {
		this.lastCard = lastCard;
		return this;
	}

	public HistoryViewBuilder setPlanDescription(String planDescription) {
		this.planDescription = planDescription;
		return this;
	}

	public HistoryViewBuilder setPlanExpiryDate(String planExpiryDate) {
		this.planExpiryDate = planExpiryDate;
		return this;
	}

	public HistoryViewBuilder setIvrsBooking(boolean ivrsBooking) {
		this.ivrsBooking = ivrsBooking;
		return this;
	}

	public HistoryViewBuilder setOperatorLabel(String operatorLabel) {
		this.operatorLabel = operatorLabel;
		return this;
	}

	public HistoryViewBuilder setBookingId(String bookingId) {
		this.bookingId = bookingId;
		return this;
	}

	public HistoryView build() {
		return new HistoryView(eventDate, previousDate, planExpiryDays, planDetail, invalidPlan, themeType, createdDate,
				amount, cardId, status, orderId, planBucket, carddeeplink, headings, displayValues, cta,childCta, footer, circle,
				operator, service, payType, eventType, mnp, lastCard, planDescription, planExpiryDate, ivrsBooking,
				operatorLabel, bookingId,billDate,dueDate,previousPayType,minimumDueAmount,pgRespCode,isRetryExhausted);
	}

	public HistoryViewBuilder setBillDate(String billDate) {
		this.billDate = billDate;
		return this;
	}

	public HistoryViewBuilder setDueDate(String dueDate) {
		this.dueDate = dueDate;
		return this;
	}

	public HistoryViewBuilder setMinimumDueAmount(Double minimumDueAmount) {
		this.minimumDueAmount = minimumDueAmount;
		return this;
	}
}
