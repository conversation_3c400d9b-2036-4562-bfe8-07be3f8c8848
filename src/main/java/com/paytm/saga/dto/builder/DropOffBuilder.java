package com.paytm.saga.dto.builder;

import java.util.Date;
import java.util.Map;

import com.paytm.saga.model.DropOff;
import jnr.ffi.annotations.In;

public class DropOffBuilder {
	private Long categoryId;
	private String recharge_number_2;
	private String recharge_number_3;
	private String recharge_number_4;
	private String operator;
	private String paytype;
	private String amount;
	private String status;
	private String in_response_code;
	private String payment_status;
	private String eventType;
	private Date createdTime;
	private Long productId;
	private String circle;
	private Map<String, String> displayValues;
	private Map<String, String> billsObj;
	private Map<String, String> customerDataResponse;
	private Long orderId;
	private Long itemId;
	private Boolean dummyRecharge;
	private Long customerId;
	private String rechargeNumber;
	private String service;
	private Date transactionTime;
	private String paidOutside;
	private Boolean isAutomatic;

	public DropOffBuilder setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
		return this;
	}

	public DropOffBuilder setRecharge_number_2(String recharge_number_2) {
		this.recharge_number_2 = recharge_number_2;
		return this;
	}

	public DropOffBuilder setRecharge_number_3(String recharge_number_3) {
		this.recharge_number_3 = recharge_number_3;
		return this;
	}

	public DropOffBuilder setRecharge_number_4(String recharge_number_4) {
		this.recharge_number_4 = recharge_number_4;
		return this;
	}

	public DropOffBuilder setOperator(String operator) {
		this.operator = operator;
		return this;
	}

	public DropOffBuilder setPaytype(String paytype) {
		this.paytype = paytype;
		return this;
	}

	public DropOffBuilder setAmount(String amount) {
		this.amount = amount;
		return this;
	}

	public DropOffBuilder setStatus(String status) {
		this.status = status;
		return this;
	}

	public DropOffBuilder setIn_response_code(String in_response_code) {
		this.in_response_code = in_response_code;
		return this;
	}

	public DropOffBuilder setPayment_status(String payment_status) {
		this.payment_status = payment_status;
		return this;
	}

	public DropOffBuilder setEventType(String eventType) {
		this.eventType = eventType;
		return this;
	}

	public DropOffBuilder setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
		return this;
	}

	public DropOffBuilder setProductId(Long productId) {
		this.productId = productId;
		return this;
	}

	public DropOffBuilder setCircle(String circle) {
		this.circle = circle;
		return this;
	}

	public DropOffBuilder setDisplayValues(Map<String, String> displayValues) {
		this.displayValues = displayValues;
		return this;
	}

	public DropOffBuilder setCustomerDataResponse(Map<String, String> customerDataResponse) {
		this.customerDataResponse = customerDataResponse;
		return this;
	}

	public DropOffBuilder setOrderId(Long orderId) {
		this.orderId = orderId;
		return this;
	}

	public DropOffBuilder setItemId(Long itemId) {
		this.itemId = itemId;
		return this;
	}

	public DropOffBuilder setDummyRecharge(Boolean dummyRecharge) {
		this.dummyRecharge = dummyRecharge;
		return this;
	}

	public DropOffBuilder setCustomerId(Long customerId) {
		this.customerId = customerId;
		return this;
	}

	public DropOffBuilder setRechargeNumber(String rechargeNumber) {
		this.rechargeNumber = rechargeNumber;
		return this;
	}

	public DropOffBuilder setService(String service) {
		this.service = service;
		return this;
	}

	public DropOffBuilder setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
		return this;
	}

	public DropOffBuilder setBillsObj(Map<String, String> billsObj) {
		this.billsObj = billsObj;
		return this;
	}

	public void setPaidOutside(String paidOutside) {
		this.paidOutside = paidOutside;
	}
	public void setIsAutomatic(Boolean isAutomatic) {
		this.isAutomatic = isAutomatic;
	}


	public DropOff build() {
		return new DropOff(categoryId, recharge_number_2, recharge_number_3, recharge_number_4, operator, paytype,
				amount, status, in_response_code, payment_status, eventType, createdTime, productId, circle,
				displayValues, billsObj, customerDataResponse, orderId, itemId, dummyRecharge, customerId, rechargeNumber,
				service, transactionTime,paidOutside,isAutomatic);
	}


}