package com.paytm.saga.dto.builder;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FetchRecentsResponseObject;
import com.paytm.saga.model.Recents;
import com.paytm.saga.util.RecentUtils;

import java.util.Objects;
import java.util.function.Function;

public class RecentsResponseBuilder {

    public static final Function<Recents, FetchRecentsResponseObject> recentToRecentsResponseObject = recent -> {
        FetchRecentsResponseObject fetchRecentsResponseObject = new FetchRecentsResponseObject();
        if (Objects.nonNull(recent)) {
            fetchRecentsResponseObject.setCustomerId(recent.getKey().getCustomerId());
            fetchRecentsResponseObject.setRechargeNumber(recent.getKey().getRechargeNumber());
            fetchRecentsResponseObject.setOperator(recent.getKey().getOperator());
            fetchRecentsResponseObject.setService(recent.getKey().getService());
            fetchRecentsResponseObject.setProductId(recent.getProductId());
            fetchRecentsResponseObject.setUpdatedAt(recent.getUpdatedAt());
            if(fetchRecentsResponseObject.getService().equalsIgnoreCase(Constants.FINANCIAL_SERVICES)){
                fetchRecentsResponseObject.setRechargeNumber(recent.getMcn());
                fetchRecentsResponseObject.setCin(recent.getCin());
                fetchRecentsResponseObject.setPar(recent.getPar());
            }
        }

        return fetchRecentsResponseObject;
    };

    private RecentsResponseBuilder() {

    }
}
