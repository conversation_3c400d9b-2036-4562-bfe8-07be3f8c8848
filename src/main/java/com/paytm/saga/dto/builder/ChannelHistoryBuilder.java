package com.paytm.saga.dto.builder;

import java.util.Date;
import java.util.Map;

import com.paytm.saga.model.ChannelHistory;

public class ChannelHistoryBuilder {
	private Long customerId;
	private String rechargeNumber;
	private String service;
	private Date transactionTime;
	private Long orderId;
	private Long itemId;
	private Date transactionUpdateTime;
	private Long categoryId;
	private String recharge_number_2;
	private String recharge_number_3;
	private String recharge_number_4;
	private String operator;
	private String paytype;
	private String amount;
	private Map<String, String> billsObj;
	private Map<String, String> displayValues;
	private String status;
	private String eventType;
	private Date createdTime;
	private boolean finalisedState;
	private Long productId;
	private String circle;
	private String inResponseCode;
	private String paymentStatus;

	public ChannelHistoryBuilder setCustomerId(Long customerId) {
		this.customerId = customerId;
		return this;
	}

	public ChannelHistoryBuilder setRechargeNumber(String rechargeNumber) {
		this.rechargeNumber = rechargeNumber;
		return this;
	}

	public ChannelHistoryBuilder setService(String service) {
		this.service = service;
		return this;
	}

	public ChannelHistoryBuilder setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
		return this;
	}

	public ChannelHistoryBuilder setOrderId(Long orderId) {
		this.orderId = orderId;
		return this;
	}

	public ChannelHistoryBuilder setItemId(Long itemId) {
		this.itemId = itemId;
		return this;
	}

	public ChannelHistoryBuilder setTransactionUpdateTime(Date transactionUpdateTime) {
		this.transactionUpdateTime = transactionUpdateTime;
		return this;
	}

	public ChannelHistoryBuilder setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
		return this;
	}

	public ChannelHistoryBuilder setRecharge_number_2(String recharge_number_2) {
		this.recharge_number_2 = recharge_number_2;
		return this;
	}

	public ChannelHistoryBuilder setRecharge_number_3(String recharge_number_3) {
		this.recharge_number_3 = recharge_number_3;
		return this;
	}

	public ChannelHistoryBuilder setRecharge_number_4(String recharge_number_4) {
		this.recharge_number_4 = recharge_number_4;
		return this;
	}

	public ChannelHistoryBuilder setOperator(String operator) {
		this.operator = operator;
		return this;
	}

	public ChannelHistoryBuilder setPaytype(String paytype) {
		this.paytype = paytype;
		return this;
	}

	public ChannelHistoryBuilder setAmount(String amount) {
		this.amount = amount;
		return this;
	}

	public ChannelHistoryBuilder setBillsObj(Map<String, String> billsObj) {
		this.billsObj = billsObj;
		return this;
	}

	public ChannelHistoryBuilder setDisplayValues(Map<String, String> displayValues) {
		this.displayValues = displayValues;
		return this;
	}

	public ChannelHistoryBuilder setStatus(String status) {
		this.status = status;
		return this;
	}

	public ChannelHistoryBuilder setEventType(String eventType) {
		this.eventType = eventType;
		return this;
	}

	public ChannelHistoryBuilder setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
		return this;
	}

	public ChannelHistoryBuilder setFinalisedState(boolean finalisedState) {
		this.finalisedState = finalisedState;
		return this;
	}

	public ChannelHistoryBuilder setProductId(Long productId) {
		this.productId = productId;
		return this;
	}

	public ChannelHistoryBuilder setCircle(String circle) {
		this.circle = circle;
		return this;
	}

	public ChannelHistoryBuilder setInResponseCode(String inResponseCode) {
		this.inResponseCode = inResponseCode;
		return this;
	}

	public ChannelHistoryBuilder setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
		return this;
	}

	public ChannelHistory build() {
		return new ChannelHistory(customerId, rechargeNumber, productId, orderId, createdTime, categoryId,
				recharge_number_2, recharge_number_3, recharge_number_4, operator, paytype, service, amount, billsObj,
				status, eventType, transactionTime, transactionUpdateTime, finalisedState, itemId, displayValues,
				circle, inResponseCode,paymentStatus);
	}

}
