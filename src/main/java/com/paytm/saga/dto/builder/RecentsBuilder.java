package com.paytm.saga.dto.builder;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.PrepaidBillsListenerModel;
import com.paytm.saga.dto.cdc.RecentCDC;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.modelmapper.ModelMapper;
import org.springframework.data.util.Pair;

import java.util.Date;
import java.util.Objects;
import java.util.function.Function;

public class RecentsBuilder {

    private RecentsBuilder() {

    }


    public static final Function<RecentCDC, Recents> fromRecentCDC = recentCDC -> {

        Recents recent = new Recents();
        recent.setKey(new RecentsPrimaryKey());
        recent.getKey().setCustomerId(recentCDC.getAfter().getCustomerid().getValue());
        recent.getKey().setOperator(recentCDC.getAfter().getOperator().getValue());
        recent.getKey().setRechargeNumber(recentCDC.getAfter().getRechargeNumber().getValue());
        recent.getKey().setService(recentCDC.getAfter().getService().getValue());
        recent.getKey().setPlanBucket(recentCDC.getAfter().getPlanBucket().getValue());
        if (recentCDC.getAfter().getOrderId() != null)
            recent.setOrderId(recentCDC.getAfter().getOrderId().getValue());
        if (recentCDC.getAfter().getTxnTime() != null && recentCDC.getAfter().getTxnTime().getValue() != null)
            recent.setTxnTime(new Date(recentCDC.getAfter().getTxnTime().getValue()));

        if (recentCDC.getAfter().getMcn() != null && recentCDC.getAfter().getMcn().getValue() != null)
            recent.setMcn(recentCDC.getAfter().getMcn().getValue());

        return recent;
    };

    public static final Function<SmartRecents, Recents> fromSmartRecents = smartRecent -> {
        ModelMapper modelMapper = new ModelMapper();
        Recents recent = modelMapper.map(smartRecent,Recents.class);
        return recent;
    };

    public static final Function<Pair<PlanExpiryHistory, PrepaidBillsListenerModel>, Recents> fromPrepaidBillsListenerData = pair -> {
        PlanExpiryHistory planExpiryHistory = pair.getFirst();
        PrepaidBillsListenerModel prepaidBillsListenerModel = pair.getSecond();

        Date bill_update_time = new Date();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setRechargeNumber(planExpiryHistory.getRechargeNumber());
        recents.getKey().setService(planExpiryHistory.getService());
        recents.getKey().setOperator(planExpiryHistory.getOperator());
        recents.getKey().setCustomerId(planExpiryHistory.getCustomerid());
        recents.setDueDate(planExpiryHistory.getValidity_expiry_date());
        recents.setDueAmount(planExpiryHistory.getAmount());
        if(Objects.isNull(planExpiryHistory.getPlan_bucket()))
            recents.getKey().setPlanBucket("");
        else{
            recents.getKey().setPlanBucket(planExpiryHistory.getPlan_bucket());
        }
        recents.setBillUpdateTime(bill_update_time);
        recents.setAutomaticAmount(planExpiryHistory.getAmount());

        recents.setUpdatedAt(bill_update_time);
        recents.setPayType(Constants.CommonConstants.PREPAID_PAYTYPE);
        recents.setCircle(planExpiryHistory.getCircle());
        recents.setPlanName(planExpiryHistory.getCategory_name());
        recents.setAutomaticStatus(prepaidBillsListenerModel.getData().getIsAutomatic());
        if(Boolean.TRUE.equals(isNewBillUpdated(prepaidBillsListenerModel))){
            recents.setNewBillUpdatedAt(new Date());
        }
        recents.setMarkAsPaidTime(null);
        recents.setMarkAsPaidAmount(null);
        recents.setIsMarkAsPaid(false);
        recents.setMarkAsPaidSource(null);
        return recents;
    };

    public static Boolean isNewBillUpdated(PrepaidBillsListenerModel prepaidBillsListenerModel){
        if(prepaidBillsListenerModel.getOperationType().equals(Constants.OP_INSERT) || (prepaidBillsListenerModel.getOperationType().equals(Constants.OP_UPDATE) && Objects.nonNull(prepaidBillsListenerModel.getOld().getDueDate()))){
            if(prepaidBillsListenerModel.getData().getDueDate() != null)
                return true;
        }
        return false;
    }



}
