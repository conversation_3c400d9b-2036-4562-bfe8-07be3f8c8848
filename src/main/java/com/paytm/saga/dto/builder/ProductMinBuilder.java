package com.paytm.saga.dto.builder;

import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.dto.catalogue.ProductAttributes;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

public class ProductMinBuilder {
    public static Function<Product, ProductMin> buildProductMin = product -> {

        ProductMin productMin = new ProductMin();
        productMin.setProductId(product.getProductId());

        productMin.setService(StringUtils.lowerCase(product.getService()));
        productMin.setCategoryId(product.getCategoryId());
        productMin.setOperator(StringUtils.lowerCase(product.getOperator()));
        productMin.setPayType(StringUtils.lowerCase(product.getPayType()));
        productMin.setStatus(product.getStatus());
        productMin.setOperatorLabel(StringUtils.lowerCase(product.getOperatorLabel()));
        productMin.setCircle(StringUtils.lowerCase(product.getCircle()));
        String attributes = product.getAttributes();

        if (attributes != null) {

            ProductAttributes productAttributes = JsonUtils.parseJson(attributes, ProductAttributes.class);

            if (productAttributes != null) {

                productMin.setBankCode(productAttributes.getBankCode());

                productMin.setCardNetwork(productAttributes.getCardNetwork());

                productMin.setIsPaytmFirstCard(productAttributes.getIsPaytmFirstCard());

                productMin.setIsSchedulable(productAttributes.getSchedulable());

                productMin.setMinAmount(productAttributes.getMinAmount());

            }

        }
        return productMin;
    };
}
