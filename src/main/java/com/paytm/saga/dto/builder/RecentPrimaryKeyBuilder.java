package com.paytm.saga.dto.builder;

import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;

import java.util.function.Function;

public class RecentPrimaryKeyBuilder {
    public static Function<CustomerBill, RecentsPrimaryKey> generateRecentsPrimaryKey = (customerBill) -> {

        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setPlanBucket(customerBill.getKey().getPlanBucket());
        key.setCustomerId(customerBill.getKey().getCustomerId());
        key.setOperator(customerBill.getKey().getOperator());
        key.setService(customerBill.getKey().getService());
        key.setRechargeNumber(customerBill.getKey().getRechargeNumber());
        return key;
    };

}
