package com.paytm.saga.dto.builder;


import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FrequentOrderRequest;
import org.springframework.util.CollectionUtils;
import java.util.Map;

public class FrequentOrderRequestBuilder {

    /*
    * On Landline and broadband CLP page we are showing recent for both services(landline and broadband)
    * PID is also has same services for these categories. And UI always hit backend with landline services.
    * That's why adding this statement if we are getting landline request for without broadband, we are adding it.
    * */
    public static void addQueryParams(Map<String, String> params, FrequentOrderRequest frequentOrderRequest) {

        frequentOrderRequest.setClient(params.get("client"));
        frequentOrderRequest.setIsCoft(Boolean.valueOf(params.get("isCoft")));
        frequentOrderRequest.setVersion(params.get("version"));
        frequentOrderRequest.setExcludeDropoff(Boolean.valueOf(params.get("excludedropoff")));
        frequentOrderRequest.setOnlyReminder(Boolean.valueOf(params.get("onlyreminder")));
        frequentOrderRequest.setRecentBillType(params.get("bill_type"));
        frequentOrderRequest.setIsReNewSubscription(Boolean.valueOf(params.get("is_renew_subscription")));
        frequentOrderRequest.setApiVersion(params.get("apiVersion"));
        if(!CollectionUtils.isEmpty(frequentOrderRequest.getServices())){
            if(frequentOrderRequest.getServices().size() > 1){
                frequentOrderRequest.setRechargeNumber(null);
            }
            if(frequentOrderRequest.getServices().contains(Constants.LANDLINE)
                    && !frequentOrderRequest.getServices().contains(Constants.BROADBAND)){
                frequentOrderRequest.getServices().add(Constants.BROADBAND);
            }
        }
    }

}
