package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.ToString;

@ToString
public class ValidationResponseDisplayValues {
    @JsonProperty("label")
    private String label;

    @JsonProperty("value")
    private String value;

    public ValidationResponseDisplayValues(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public ValidationResponseDisplayValues() {

    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
