package com.paytm.saga.dto;

public class ReminderMarkAsPaidRequest {

    private String rechargeNumber;
    private String productID;
    private String operator;
    private String paytype;
    private String customerID;
    private String planBucket;
    private String referenceID;
    private String service;

    public ReminderMarkAsPaidRequest(String rechargeNumber, String productID, String operator, String paytype, String customerID, String planBucket, String referenceID, String service) {
        this.rechargeNumber = rechargeNumber;
        this.productID = productID;
        this.operator = operator;
        this.paytype = paytype;
        this.customerID = customerID;
        this.planBucket = planBucket;
        this.referenceID = referenceID;
        this.service = service;
    }

    public String getRechargeNumber() {
        return rechargeNumber;
    }

    public void setRechargeNumber(String rechargeNumber) {
        this.rechargeNumber = rechargeNumber;
    }

    public String getProductID() {
        return productID;
    }

    public void setProductID(String productID) {
        this.productID = productID;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getCustomerID() {
        return customerID;
    }

    public void setCustomerID(String customerID) {
        this.customerID = customerID;
    }

    public String getPlanBucket() {
        return planBucket;
    }

    public void setPlanBucket(String planBucket) {
        this.planBucket = planBucket;
    }

    public String getReferenceID() {
        return referenceID;
    }

    public void setReferenceID(String referenceID) {
        this.referenceID = referenceID;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    @Override
    public String toString() {
        return "ReminderMarkAsPaidRequest{" +
                "rechargeNumber='" + rechargeNumber + '\'' +
                ", productID='" + productID + '\'' +
                ", operator='" + operator + '\'' +
                ", paytype='" + paytype + '\'' +
                ", customerID='" + customerID + '\'' +
                ", planBucket='" + planBucket + '\'' +
                ", referenceID='" + referenceID + '\'' +
                ", service='" + service + '\'' +
                '}';
    }
}
