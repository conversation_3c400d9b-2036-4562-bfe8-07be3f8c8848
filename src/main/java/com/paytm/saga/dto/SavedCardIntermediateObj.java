package com.paytm.saga.dto;

import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ReminderHistory;

public class SavedCardIntermediateObj {
    private Long customerId;
    private String cin;
    private String panUniqueReference;
    private String rechargeNumber;
    private ChannelHistory channelHistoryObj;
    private ReminderHistory reminderHistoryObj;

    public SavedCardIntermediateObj() {
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCin() {
        return cin;
    }

    public void setCin(String cin) {
        this.cin = cin;
    }

    public String getRechargeNumber() {
        return rechargeNumber;
    }

    public void setRechargeNumber(String rechargeNumber) {
        this.rechargeNumber = rechargeNumber;
    }

    public ChannelHistory getChannelHistoryObj() {
        return channelHistoryObj;
    }

    public void setChannelHistoryObj(ChannelHistory channelHistoryObj) {
        this.channelHistoryObj = channelHistoryObj;
    }

    public ReminderHistory getReminderHistoryObj() {
        return reminderHistoryObj;
    }

    public void setReminderHistoryObj(ReminderHistory reminderHistoryObj) {
        this.reminderHistoryObj = reminderHistoryObj;
    }

    public String getPanUniqueReference() {
        return panUniqueReference;
    }

    public void setPanUniqueReference(String panUniqueReference) {
        this.panUniqueReference = panUniqueReference;
    }

    @Override
    public String toString() {
        return "SavedCardIntermediateObj{" +
                "customerId=" + customerId +
                ", cin='" + cin + '\'' +
                ", panUniqueReference='" + panUniqueReference + '\'' +
                ", rechargeNumber='" + rechargeNumber + '\'' +
                ", channelHistoryObj=" + channelHistoryObj +
                ", reminderHistoryObj=" + reminderHistoryObj +
                '}';
    }
}
