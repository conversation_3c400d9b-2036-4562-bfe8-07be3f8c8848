package com.paytm.saga.dto;

public class ThemeDetails {
	private ViewDetail header1;
	private ViewDetail header2;
	private ViewDetail header3;
	private ViewDetail header4;
	private ViewDetail header5;
	private ViewDetail ds1;
	private ViewDetail ds2;
	private ViewDetail ds3;
	private ViewDetail ds4;
	private ViewDetail ds5;
	private ViewDetail cta1;
	private ViewDetail cta2;
	private ViewDetail cta3;
	private ViewDetail cta4;
	private ViewDetail cta5;
	private ViewDetail footer1;
	private ViewDetail footer2;

	public ViewDetail getDs1() {
		return ds1;
	}

	public void setDs1(ViewDetail ds1) {
		this.ds1 = ds1;
	}

	public ViewDetail getDs2() {
		return ds2;
	}

	public void setDs2(ViewDetail ds2) {
		this.ds2 = ds2;
	}

	public ViewDetail getDs3() {
		return ds3;
	}

	public void setDs3(ViewDetail ds3) {
		this.ds3 = ds3;
	}

	public ViewDetail getDs4() {
		return ds4;
	}

	public void setDs4(ViewDetail ds4) {
		this.ds4 = ds4;
	}

	public ViewDetail getDs5() {
		return ds5;
	}

	public void setDs5(ViewDetail ds5) {
		this.ds5 = ds5;
	}

	public ViewDetail getCta1() {
		return cta1;
	}

	public void setCta1(ViewDetail cta1) {
		this.cta1 = cta1;
	}

	public ViewDetail getCta2() {
		return cta2;
	}

	public void setCta2(ViewDetail cta2) {
		this.cta2 = cta2;
	}

	public ViewDetail getCta3() {
		return cta3;
	}

	public void setCta3(ViewDetail cta3) {
		this.cta3 = cta3;
	}

	public ViewDetail getCta4() {
		return cta4;
	}

	public void setCta4(ViewDetail cta4) {
		this.cta4 = cta4;
	}

	public ViewDetail getCta5() {
		return cta5;
	}

	public void setCta5(ViewDetail cta5) {
		this.cta5 = cta5;
	}

	public ViewDetail getFooter1() {
		return footer1;
	}

	public void setFooter1(ViewDetail footer1) {
		this.footer1 = footer1;
	}

	public ViewDetail getFooter2() {
		return footer2;
	}

	public void setFooter2(ViewDetail footer2) {
		this.footer2 = footer2;
	}

	public ViewDetail getHeader1() {
		return header1;
	}

	public void setHeader1(ViewDetail header1) {
		this.header1 = header1;
	}

	public ViewDetail getHeader2() {
		return header2;
	}

	public void setHeader2(ViewDetail header2) {
		this.header2 = header2;
	}

	public ViewDetail getHeader3() {
		return header3;
	}

	public void setHeader3(ViewDetail header3) {
		this.header3 = header3;
	}

	public ViewDetail getHeader4() {
		return header4;
	}

	public void setHeader4(ViewDetail header4) {
		this.header4 = header4;
	}

	public ViewDetail getHeader5() {
		return header5;
	}

	public void setHeader5(ViewDetail header5) {
		this.header5 = header5;
	}
}
