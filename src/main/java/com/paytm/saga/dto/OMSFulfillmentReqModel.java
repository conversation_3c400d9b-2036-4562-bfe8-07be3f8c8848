package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class OMSFulfillmentReqModel {

	@JsonProperty("recharge_number")
	private String recharge_number;
	@JsonProperty("recharge_number_2")
	private String recharge_number_2;
	@JsonProperty("recharge_number_3")
	private String recharge_number_3;
	@JsonProperty("recharge_number_4")
	private String recharge_number_4;
	@JsonProperty("recharge_number_5")
	private String recharge_number_5;
	@JsonProperty("recharge_number_6")
	private String recharge_number_6;
	@JsonProperty("recharge_number_7")
	private String recharge_number_7;
	@JsonProperty("recharge_number_8")
	private String recharge_number_8;

}
