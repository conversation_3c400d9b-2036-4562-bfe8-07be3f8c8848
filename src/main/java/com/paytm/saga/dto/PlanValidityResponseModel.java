package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.dto.PlanValidityDataResponseModel;
import lombok.ToString;

@ToString
public class PlanValidityResponseModel {
    @JsonProperty("type")
    private String operationType;

    @JsonProperty("table")
    private String table;

    @JsonProperty("data")
    private PlanValidityDataResponseModel data;
    
    @JsonProperty("old")
    private PlanValidityDataResponseModel old;

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public PlanValidityDataResponseModel getData() {
        return data;
    }

    public void setData(PlanValidityDataResponseModel data) {
        this.data = data;
    }

	public PlanValidityDataResponseModel getOld() {
		return old;
	}

	public void setOld(PlanValidityDataResponseModel old) {
		this.old = old;
	}
}
