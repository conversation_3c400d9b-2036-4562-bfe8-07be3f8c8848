package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.dto.OMSItemProductModel;

import jakarta.validation.constraints.NotNull;

public class OMSItemModel {
    @NotNull(message = "itemId cannot be null")
    @JsonProperty("id")
    private Long itemId;

    @NotNull(message = "orderId cannot be null")
    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("status")
    private Long status;

    @NotNull(message = "verticalId cannot be null")
    @JsonProperty("vertical_id")
    private Integer verticalId;

    @JsonProperty("product_id")
    private Long productId;

    @JsonProperty("fulfillment_id")
    private Long fulfillmentId;

    @JsonProperty("fulfillment_req")
    private String fulfillmentReq;

    @JsonProperty("mrp")
    private String mrp;

    @JsonProperty("price")
    private Double price;

    @JsonProperty("product")
    private OMSItemProductModel product;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("meta_data")
    private String meta_data;


    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Integer getVerticalId() {
        return verticalId;
    }

    public void setVerticalId(Integer verticalId) {
        this.verticalId = verticalId;
    }

    public Long getFulfillmentId() {
        return fulfillmentId;
    }

    public void setFulfillmentId(Long fulfillmentId) {
        this.fulfillmentId = fulfillmentId;
    }

    public String getFulfillmentReq() {
        return fulfillmentReq;
    }

    public void setFulfillmentReq(String fulfillmentReq) {
        this.fulfillmentReq = fulfillmentReq;
    }

    public String getMrp() {
        return mrp;
    }

    public void setMrp(String mrp) {
        this.mrp = mrp;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public OMSItemProductModel getProduct() {
        return product;
    }

    public void setProduct(OMSItemProductModel product) {
        this.product = product;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "OMSItemModel{" +
                "itemId=" + itemId +
                ", orderId=" + orderId +
                ", status=" + status +
                ", verticalId=" + verticalId +
                ", productId=" + productId +
                ", fulfillmentId=" + fulfillmentId +
                ", fulfillmentReq='" + fulfillmentReq + '\'' +
                ", mrp='" + mrp + '\'' +
                ", price=" + price +
                ", product=" + product +
                ", createdAt='" + createdAt + '\'' +
                ", updatedAt='" + updatedAt + '\'' +
                ", meta_data='" + meta_data + '\'' +
                '}';
    }

    public String getMeta_data() {
        return meta_data;
    }

    public void setMeta_data(String meta_data) {
        this.meta_data = meta_data;
    }
}
