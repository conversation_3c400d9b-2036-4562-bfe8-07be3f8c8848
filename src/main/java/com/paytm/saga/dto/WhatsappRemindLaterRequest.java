package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WhatsappRemindLaterRequest {
    @JsonProperty("customer_id")
    String customerId;
    @JsonProperty("service")
    String service;

    @JsonProperty("recharge_number")
    String rechargeNumber;
    @JsonProperty("operator")
    String operator;
    @JsonProperty("productId")
    Double productId;
    @JsonProperty("plan_bucket")
    String planBucket;
    @JsonProperty("recharge_number_2")
    String rechargeNumber2;
    @JsonProperty("recharge_number_3")
    String rechargeNumber3;
    @JsonProperty("recharge_number_4")
    String rechargeNumber4;
    @JsonProperty("recharge_number_5")
    String rechargeNumber5;
    @JsonProperty("recharge_number_6")
    String rechargeNumber6;
    @JsonProperty("recharge_number_7")
    String rechargeNumber7;
    @JsonProperty("recharge_number_8")
    String rechargeNumber8;
    @JsonProperty("remindLaterOffset")
    Integer remindLaterOffset;
    @JsonProperty("remindLaterDate")
    Date remindLaterDate;
}
