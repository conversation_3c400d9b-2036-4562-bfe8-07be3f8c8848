package com.paytm.saga.dto;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Scope("singleton")
public class RecentReconDataCache {

    private static RecentReconDataCache recentReconDataCache;
    private final Logger logger = LogManager.getLogger(RecentReconDataCache.class);
    private List<RecentReconResponse> reportData;
    private AtomicInteger recordsProcessed = new AtomicInteger(0);

    private RecentReconDataCache() {
        reportData = Collections.synchronizedList(new ArrayList<>());
    }

    public static RecentReconDataCache getInstance() {
        if (recentReconDataCache == null) {
            recentReconDataCache = new RecentReconDataCache();
        }
        return recentReconDataCache;
    }

    public void pushReportData(RecentReconResponse data) {
        if (data == null) {
            logger.info("[RecentReconReportCache.pushReportData]: data value is null");
            return;
        }
        reportData.add(data);
    }

    public List<RecentReconResponse> getReportData() {
        if (reportData.isEmpty()) {
            return Collections.emptyList();
        }

        return reportData;

    }

    public void clearReportData(List<RecentReconResponse> currentData) {

        reportData.removeAll(currentData);
    }

    public void incrementRecordsProcessed() {
        recordsProcessed.incrementAndGet();
    }

    public Integer getRecordsProcessed() {
        return recordsProcessed.get();
    }

    public void updateRecordsProcessed(Integer currentValue) {
        if (recordsProcessed.get() >= currentValue) {
            recordsProcessed.set(recordsProcessed.get() - currentValue);
        }

    }
}
