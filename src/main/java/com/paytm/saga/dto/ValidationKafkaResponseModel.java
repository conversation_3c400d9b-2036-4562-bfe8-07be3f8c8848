package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.ToString;

import java.util.List;

@ToString
public class ValidationKafkaResponseModel {
    @JsonProperty("catalogProductID")
    private Long productId;

    @JsonProperty("validationSuccessful")
    private Boolean validationSuccessful;

    @JsonProperty("userData_amount")
    private String userData_amount;

    @JsonProperty("timestamps_init")
    private String timestamps_init;

    @JsonProperty("customerInfo_customer_id")
    private Long customerInfo_customer_id;

    @JsonProperty("customerInfo_channel_id")
    private String customerInfo_channel_id;

    @JsonProperty("productInfo_verticalId")
    private Integer productInfo_verticalId;

    @JsonProperty("productInfo_paytype")
    private String productInfo_paytype;

    @JsonProperty("productInfo_circle")
    private String productInfo_circle;

    @JsonProperty("productInfo_service")
    private String productInfo_service;

    @JsonProperty("productInfo_operator")
    private String productInfo_operator;

    @JsonProperty("customerDataResponse")
    private CustomerDataResponseModel customerDataResponse;

    @JsonProperty("dueAmount")
    private String dueAmount;

    @JsonProperty("metaData")
    private ValidationResponseMetaModel metaData;

    @JsonProperty("displayValues")
    private List<ValidationResponseDisplayValues> displayValues;

    @JsonProperty("userData_recharge_number")
    private String userData_recharge_number;

    @JsonProperty("userData_recharge_number_2")
    private String userData_recharge_number_2;

    @JsonProperty("userData_recharge_number_3")
    private String userData_recharge_number_3;

    @JsonProperty("userData_recharge_number_4")
    private String userData_recharge_number_4;

    @JsonProperty("productInfo_category_id")
    private Long productInfo_category_id;

    @JsonProperty("useRedisData")
    private Boolean useRedisData;

    @JsonProperty("rechargeGwResponse_operatorResponseCode")
    private String recGw_operatorResCode;

    public String getRecGw_operatorResCode() {
        return recGw_operatorResCode;
    }

    public void setRecGw_operatorResCode(String recGw_operatorResCode) {
        this.recGw_operatorResCode = recGw_operatorResCode;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Boolean getValidationSuccessful() {
        return validationSuccessful;
    }

    public void setValidationSuccessful(Boolean validationSuccessful) {
        this.validationSuccessful = validationSuccessful;
    }

    public String getTimestamps_init() {
        return timestamps_init;
    }

    public void setTimestamps_init(String timestamps_init) {
        this.timestamps_init = timestamps_init;
    }

    public Long getCustomerInfo_customer_id() {
        return customerInfo_customer_id;
    }

    public void setCustomerInfo_customer_id(Long customerInfo_customer_id) {
        this.customerInfo_customer_id = customerInfo_customer_id;
    }

    public String getCustomerInfo_channel_id() {
        return customerInfo_channel_id;
    }

    public void setCustomerInfo_channel_id(String customerInfo_channel_id) {
        this.customerInfo_channel_id = customerInfo_channel_id;
    }

    public Integer getProductInfo_verticalId() {
        return productInfo_verticalId;
    }

    public void setProductInfo_verticalId(Integer productInfo_verticalId) {
        this.productInfo_verticalId = productInfo_verticalId;
    }

    public String getProductInfo_paytype() {
        return productInfo_paytype;
    }

    public void setProductInfo_paytype(String productInfo_paytype) {
        this.productInfo_paytype = productInfo_paytype;
    }

    public String getProductInfo_circle() {
        return productInfo_circle;
    }

    public void setProductInfo_circle(String productInfo_circle) {
        this.productInfo_circle = productInfo_circle;
    }

    public String getProductInfo_service() {
        return productInfo_service;
    }

    public void setProductInfo_service(String productInfo_service) {
        this.productInfo_service = productInfo_service;
    }

    public String getProductInfo_operator() {
        return productInfo_operator;
    }

    public void setProductInfo_operator(String productInfo_operator) {
        this.productInfo_operator = productInfo_operator;
    }

    public CustomerDataResponseModel getCustomerDataResponse() {
        return customerDataResponse;
    }

    public void setCustomerDataResponse(CustomerDataResponseModel customerDataResponse) {
        this.customerDataResponse = customerDataResponse;
    }

    public String getUserData_recharge_number() {
        return userData_recharge_number;
    }

    public void setUserData_recharge_number(String userData_recharge_number) {
        this.userData_recharge_number = userData_recharge_number;
    }

    public String getUserData_recharge_number_2() {
        return userData_recharge_number_2;
    }

    public void setUserData_recharge_number_2(String userData_recharge_number_2) {
        this.userData_recharge_number_2 = userData_recharge_number_2;
    }

    public String getUserData_recharge_number_3() {
        return userData_recharge_number_3;
    }

    public void setUserData_recharge_number_3(String userData_recharge_number_3) {
        this.userData_recharge_number_3 = userData_recharge_number_3;
    }

    public String getUserData_recharge_number_4() {
        return userData_recharge_number_4;
    }

    public void setUserData_recharge_number_4(String userData_recharge_number_4) {
        this.userData_recharge_number_4 = userData_recharge_number_4;
    }

    public String getUserData_amount() {
        return userData_amount;
    }

    public void setUserData_amount(String userData_amount) {
        this.userData_amount = userData_amount;
    }

    public ValidationKafkaResponseModel(Long productId, Boolean validationSuccessful, String userData_amount, String timestamps_init, Long customerInfo_customer_id, String customerInfo_channel_id, Integer productInfo_verticalId, String productInfo_paytype, String productInfo_circle, String productInfo_service, String productInfo_operator, CustomerDataResponseModel customerDataResponse, String dueAmount, ValidationResponseMetaModel metaData, List<ValidationResponseDisplayValues> displayValues, String userData_recharge_number, String userData_recharge_number_2, String userData_recharge_number_3, String userData_recharge_number_4, Long productInfo_category_id, Boolean useRedisData) {
        this.productId = productId;
        this.validationSuccessful = validationSuccessful;
        this.userData_amount = userData_amount;
        this.timestamps_init = timestamps_init;
        this.customerInfo_customer_id = customerInfo_customer_id;
        this.customerInfo_channel_id = customerInfo_channel_id;
        this.productInfo_verticalId = productInfo_verticalId;
        this.productInfo_paytype = productInfo_paytype;
        this.productInfo_circle = productInfo_circle;
        this.productInfo_service = productInfo_service;
        this.productInfo_operator = productInfo_operator;
        this.customerDataResponse = customerDataResponse;
        this.dueAmount = dueAmount;
        this.metaData = metaData;
        this.displayValues = displayValues;
        this.userData_recharge_number = userData_recharge_number;
        this.userData_recharge_number_2 = userData_recharge_number_2;
        this.userData_recharge_number_3 = userData_recharge_number_3;
        this.userData_recharge_number_4 = userData_recharge_number_4;
        this.productInfo_category_id = productInfo_category_id;
        this.useRedisData = useRedisData;
    }

    public ValidationKafkaResponseModel(){

    }

    public ValidationResponseMetaModel getMetaData() {
        return metaData;
    }

    public void setMetaData(ValidationResponseMetaModel metaData) {
        this.metaData = metaData;
    }

    public List<ValidationResponseDisplayValues> getDisplayValues() {
        return displayValues;
    }

    public void setDisplayValues(List<ValidationResponseDisplayValues> displayValues) {
        this.displayValues = displayValues;
    }

    public Long getProductInfo_category_id() {
        return productInfo_category_id;
    }

    public void setProductInfo_category_id(Long productInfo_category_id) {
        this.productInfo_category_id = productInfo_category_id;
    }

    public String getDueAmount() {
        return dueAmount;
    }

    public void setDueAmount(String dueAmount) {
        this.dueAmount = dueAmount;
    }

    public Boolean getUseRedisData() {
        return useRedisData;
    }

    public void setUseRedisData(Boolean useRedisData) {
        this.useRedisData = useRedisData;
    }
}
