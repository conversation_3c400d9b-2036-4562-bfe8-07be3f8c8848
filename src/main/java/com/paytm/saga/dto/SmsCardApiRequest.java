package com.paytm.saga.dto;

import jakarta.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class SmsCardApiRequest {
	
	@JsonProperty("product.service")
	private String service;

	@JsonProperty("product.services")
	private List<String> services;
	
	@JsonProperty("product.paytype")
	@NotBlank(message = "paytype cannot be empty")
	private String paytype;
	
	@JsonProperty("type")
	@NotBlank(message = "cardType cannot be empty")
	private String cardType;
	
	public String getService() {
		return service;
	}
	public void setService(String service) {
		this.service = service;
	}
	public String getPaytype() {
		return paytype;
	}
	public void setPaytype(String paytype) {
		this.paytype = paytype;
	}
	public String getCardType() {
		return cardType;
	}
	public void setCardType(String cardType) {
		this.cardType = cardType;
	}
	public List<String> getServices() {
		return services;
	}

	public void setServices(List<String> services) {
		this.services = services;
	}
	
}
