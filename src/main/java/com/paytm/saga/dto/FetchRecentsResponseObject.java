package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class FetchRecentsResponseObject {
    @JsonProperty("customer_id")
    private Long customerId;

    @JsonProperty("recharge_number")
    private String rechargeNumber;

    @JsonProperty("product_id")
    private Long productId;

    @JsonProperty("service")
    private String service;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("cin")
    private String cin;

    @JsonProperty("par")
    private String par;

    @JsonProperty("updated_at")
    private Date updatedAt;

}
