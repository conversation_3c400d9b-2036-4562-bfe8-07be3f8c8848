package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OMSFulfillmentResponseModel {
	@JsonProperty("in_code")
	private String in_code;
	@JsonProperty("Fresh Booking")
	private String freshBooking;

	@JsonProperty("creditCardId")
	private String creditCardId;

	@JsonProperty("operator")
	private String operator;

	@Override
	public String toString() {
		return "OMSFulfillmentResponseModel [in_code=" + in_code + ", freshBooking=" + freshBooking + ", bookingId="
				+ bookingId + "]";
	}

	@JsonProperty("operatorRefNumber")
	private String bookingId;

	public String getIn_code() {
		return in_code;
	}

	public void setIn_code(String in_code) {
		this.in_code = in_code;
	}

	public String getFreshBooking() {
		return freshBooking;
	}

	public void setFreshBooking(String freshBooking) {
		this.freshBooking = freshBooking;
	}

	public String getBookingId() {
		return bookingId;
	}

	public void setBookingId(String bookingId) {
		this.bookingId = bookingId;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getCreditCardId() {
		return creditCardId;
	}

	public void setCreditCardId(String creditCardId) {
		this.creditCardId = creditCardId;
	}
}
