package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.ToString;

import java.util.List;

@ToString
public class ValidationResponseMetaModel {
    @JsonProperty("plan_bucket")
    private String plan_bucket;

    @JsonProperty("data")
    private String data;

    @JsonProperty("validity")
    private String validity;

    @JsonProperty("talktime")
    private String talktime;

    @JsonProperty("sms")
    private String sms;

    @JsonProperty("couponDescription")
    private String description;

    @JsonProperty("addon_benefit1")
    private String addon_benefit1;

    @JsonProperty("addon_benefit2")
    private String addon_benefit2;

    @JsonProperty("addon_benefit3")
    private String addon_benefit3;

    @JsonProperty("addon_benefit4")
    private String addon_benefit4;

    @JsonProperty("addon_benefit")
    private List<String> addon_benefit;

    @JsonProperty("plan_id")
    private String plan_id;

    @JsonProperty("userIntent")
    private Integer userIntent;

    @JsonProperty("recharge_benefits")
    private ValidationResponseMetaBenefitsModel recharge_benefits;
    
    @JsonProperty("cin")
    private String cin;
    @JsonProperty("panUniqueReference")
    private String par;
    
    @JsonProperty("cylinder_agency_name")
    private String cylinder_agency_name;

    @JsonProperty("additionalPayload")
    private ValidationResponseMetaAdditionalPayload additionalPayload;

    @JsonProperty("RMN")
    private String RMN;


    public String getPlan_bucket() {
        return plan_bucket;
    }

    public void setPlan_bucket(String plan_bucket) {
        this.plan_bucket = plan_bucket;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getValidity() {
        return validity;
    }

    public void setValidity(String validity) {
        this.validity = validity;
    }

    public String getTalktime() {
        return talktime;
    }

    public void setTalktime(String talktime) {
        this.talktime = talktime;
    }

    public String getSms() {
        return sms;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAddon_benefit1() {
        return addon_benefit1;
    }

    public void setAddon_benefit1(String addon_benefit1) {
        this.addon_benefit1 = addon_benefit1;
    }

    public String getAddon_benefit2() {
        return addon_benefit2;
    }

    public void setAddon_benefit2(String addon_benefit2) {
        this.addon_benefit2 = addon_benefit2;
    }

    public String getAddon_benefit3() {
        return addon_benefit3;
    }

    public void setAddon_benefit3(String addon_benefit3) {
        this.addon_benefit3 = addon_benefit3;
    }

    public String getAddon_benefit4() {
        return addon_benefit4;
    }

    public void setAddon_benefit4(String addon_benefit4) {
        this.addon_benefit4 = addon_benefit4;
    }

    public String getPlan_id() {
        return plan_id;
    }

    public void setPlan_id(String plan_id) {
        this.plan_id = plan_id;
    }

    public List<String> getAddon_benefit() {
        return addon_benefit;
    }

    public void setAddon_benefit(List<String> addon_benefit) {
        this.addon_benefit = addon_benefit;
    }

    public ValidationResponseMetaBenefitsModel getRecharge_benefits() {
        return recharge_benefits;
    }

    public void setRecharge_benefits(ValidationResponseMetaBenefitsModel recharge_benefits) {
        this.recharge_benefits = recharge_benefits;
    }

    public ValidationResponseMetaAdditionalPayload getAdditionalPayload() {
        return additionalPayload;
    }

    public void setAdditionalPayload(ValidationResponseMetaAdditionalPayload additionalPayload) {
        this.additionalPayload = additionalPayload;
    }

    public Integer getUserIntent() {
        return userIntent;
    }

    public void setUserIntent(Integer userIntent) {
        this.userIntent = userIntent;
    }
	public String getCin() {
		return cin;
	}

	public void setCin(String cin) {
		this.cin = cin;
	}


    public String getPar() { return par; }

    public void setPar(String par) { this.par = par; }

    public String getRMN() {
        return RMN;
    }

    public void setRMN(String RMN) {
        this.RMN = RMN;
    }

	public String getCylinder_agency_name() {
		return cylinder_agency_name;
	}

	public void setCylinder_agency_name(String cylinder_agency_name) {
		this.cylinder_agency_name = cylinder_agency_name;
	}
}
