package com.paytm.saga.dto;

import java.util.List;
import java.util.Map;

public class GetSmsCardsResponse {
	private Map<String,List<SmsCard>> data;
	private int status;
	private String error;

	public Map<String,List<SmsCard>> getData() {
		return data;
	}

	public void setData(Map<String,List<SmsCard>> data) {
		this.data = data;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getError() {
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	@Override
	public String toString() {
		return "GetSmsCardsResponse [data=" + data + ", status=" + status + ", error=" + error + "]";
	}
}
