package com.paytm.saga.dto;

import com.datastax.oss.driver.api.core.cql.PagingState;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.Slice;

import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.List;

public class ResponsePage<T> {
	private static final CustomLogger logger = CustomLogManager.getLogger(ResponsePage.class);

	private final Integer count;
	private final List<T> content;
	private final String pagingState;
	private final Boolean hasNext;

	public ResponsePage(final Slice<T> slice) {
		this.content = slice.getContent();
		this.count = content.size();
		this.hasNext = slice.hasNext();

		String tempPagingState = null;
		if (this.hasNext) {
			CassandraPageRequest pageRequest = (CassandraPageRequest) slice.nextPageable();
			ByteBuffer pagingStateBuffer = pageRequest.getPagingState();
			if (pagingStateBuffer != null) {
				try {
					// Use Base64 encoding for reliable serialization
					byte[] bytes = new byte[pagingStateBuffer.remaining()];
					pagingStateBuffer.get(bytes);
					tempPagingState = Base64.getEncoder().encodeToString(bytes);
					logger.debug("Successfully serialized paging state, length: {}", tempPagingState.length());
				} catch (Exception e) {
					logger.error("Failed to serialize paging state: {}", e.getMessage());
					tempPagingState = null;
				}
			}
		}
		this.pagingState = tempPagingState;
	}

	public ResponsePage(Integer count, List<T> content, String pagingState, Boolean hasNext) {
		this.content = content;
		this.count = count;
		this.hasNext = hasNext;
		this.pagingState = pagingState;
	}

	public List<T> getContent() {
		return content;
	}

	public String getPagingState() {
		return pagingState;
	}

	public Integer getCount() {
		return count;
	}

	public Boolean getHasNext() {
		return hasNext;
	}
}
