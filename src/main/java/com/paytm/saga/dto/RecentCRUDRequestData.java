package com.paytm.saga.dto;

import java.util.ArrayList;
import java.util.List;

public class RecentCRUDRequestData {

    private String fieldName;
    private List<RecentCRUDRequestDataFieldValue> fieldValue;


    public RecentCRUDRequestData(String fieldName, Long amount, String due_date, String bill_date, String mark_as_paid_time, String expiry, String plan_bucket) {
        this.fieldName = fieldName;
        RecentCRUDRequestDataFieldValue value = new RecentCRUDRequestDataFieldValue(amount, due_date, bill_date, mark_as_paid_time, expiry, plan_bucket);
        ArrayList<RecentCRUDRequestDataFieldValue> list=new ArrayList<RecentCRUDRequestDataFieldValue>();
        list.add(value);
        this.fieldValue = list;
    }

    @Override
    public String toString() {
        return "RecentCRUDRequestData{" +
                "fieldName='" + fieldName + '\'' +
                ", fieldValue=" + fieldValue +
                '}';
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public List<RecentCRUDRequestDataFieldValue> getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(List<RecentCRUDRequestDataFieldValue> fieldValue) {
        this.fieldValue = fieldValue;
    }
}
