package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class DCATCategoryGroupingAggModel {
	@JsonProperty("value")
	private String value;
	@JsonProperty("displayValue")
	private String displayValue;
	@JsonProperty("aggKey")
	private String aggKey;
	@JsonProperty("is_paytm_first_card")
	private String isPaytmFirstCard;
	@JsonProperty("aggs")
	private List<DCATCategoryGroupingAggAggModel> aggs;

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getDisplayValue() {
		return displayValue;
	}

	public void setDisplayValue(String displayValue) {
		this.displayValue = displayValue;
	}

	public String getAggKey() {
		return aggKey;
	}

	public void setAggKey(String aggKey) {
		this.aggKey = aggKey;
	}

	public List<DCATCategoryGroupingAggAggModel> getAggs() {
		return aggs;
	}

	public void setAggs(List<DCATCategoryGroupingAggAggModel> aggs) {
		this.aggs = aggs;
	}

	public String getIsPaytmFirstCard() {
		return isPaytmFirstCard;
	}

	public void setIsPaytmFirstCard(String isPaytmFirstCard) {
		this.isPaytmFirstCard = isPaytmFirstCard;
	}

}