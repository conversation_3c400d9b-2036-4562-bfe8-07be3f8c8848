package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ViewElementInfo {
	private String value;
	private String key;
	private String info;
//	private String heading;
	private String deepLink;
	private String type;
	private String actionType;
	private List<RichInfo> richInfo;
	@JsonProperty(value="isAmount")
	private boolean isAmount;
}
