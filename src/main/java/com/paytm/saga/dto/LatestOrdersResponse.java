package com.paytm.saga.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.enums.BillState;
import com.paytm.saga.enums.EventState;
import java.util.Date;

import lombok.Data;
@Data
public class LatestOrdersResponse{
    private Double amount;
    @JsonProperty("productId")
    private String pid;
    private String dueDate;
    @JsonProperty("plan_Bucket")
    private String planBucket;
    private EventState eventState;
    private BillState billState;
    private Double txnAmount;
    private String txnDate;
    private String billDueDate;
    private String markAsPaidDate;
    private String automaticDate;
    private String planCircleKey;
    private String planId;
}