package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.dto.OMSFulfillmentsModel;

import jakarta.validation.constraints.NotNull;
import java.util.List;


public class OMSResponseModel {
    @NotNull(message = "OrderId cannot be null")
    @JsonProperty("id")
    private Long orderId;


    @JsonProperty("customer_id")
    private Long customerId;

    @JsonProperty("payment_status")
    private Integer payment_status;

    @JsonProperty("payments")
    private List<OMSPaymentModel> payment;

    @JsonProperty("items")
    private List<OMSItemModel> items;

    @JsonProperty("fulfillments")
    private List<OMSFulfillmentsModel> fulfillments;

    @JsonProperty("channel_id")
    private String channelId;

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public void setItems(List <OMSItemModel> items) {
        this.items = items;
    }

    public List <OMSItemModel> getItems() {
        return items;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    public Long getOrderId(){return orderId;}

    @Override
    public String toString() {
        return "OMSResponseModel{" +
                "orderId=" + orderId +
                ", customerId=" + customerId +
                ", items=" + items +
                ", fulfillments=" + fulfillments +
                ", channelId=" + channelId +
                '}';
    }

    public List<OMSFulfillmentsModel> getFulfillments() {
        return fulfillments;
    }

    public void setFulfillments(List<OMSFulfillmentsModel> fulfillments) {
        this.fulfillments = fulfillments;
    }

    public Integer getPayment_status() {
        return payment_status;
    }

    public void setPayment_status(Integer payment_status) {
        this.payment_status = payment_status;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public List<OMSPaymentModel> getPayment() {
        return payment;
    }
}
