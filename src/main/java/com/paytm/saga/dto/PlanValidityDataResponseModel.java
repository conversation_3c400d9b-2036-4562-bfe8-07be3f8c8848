package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
public class PlanValidityDataResponseModel {
    @JsonProperty("recharge_number")
    private String recharge_number;

    @JsonProperty("customer_id")
    private Long customer_id;

    @JsonProperty("service")
    private String service;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("circle")
    private String circle;
    
    @JsonProperty("amount")
    private double amount;

    @JsonProperty("validity_expiry_date")
    private String validity_expiry_date;
    
    @JsonProperty("cust_mobile")
    private String cust_mobile;
    
    @JsonProperty("cust_email")
    private String cust_email;
    
    @JsonProperty("order_ids")
    private String order_ids;
    
    @JsonProperty("latest_recharge_date")
    private String latest_recharge_date;
    
    @JsonProperty("notification_status")
    private Integer notification_status;

    @JsonProperty("plan_bucket")
    private String plan_bucket;

    @JsonProperty("category_name")
    private String category_name;

    @JsonProperty("created_at")
    private String created_at;

    @JsonProperty("updated_at")
    private String updated_at;

    @JsonProperty("recon_id")
    private String reconId;

    @JsonProperty("extra")
    private String extra;
    @JsonProperty("product_id")
    private Long productId;

    public String getReconId() {
        return reconId;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getRecharge_number() {
        return recharge_number;
    }

    public void setRecharge_number(String recharge_number) {
        this.recharge_number = recharge_number;
    }

    public Long getCustomer_id() {
        return customer_id;
    }

    public void setCustomer_id(Long customer_id) {
        this.customer_id = customer_id;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getCircle() {
        return circle;
    }

    public void setCircle(String circle) {
        this.circle = circle;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getValidity_expiry_date() {
        return validity_expiry_date;
    }

    public void setValidity_expiry_date(String validity_expiry_date) {
        this.validity_expiry_date = validity_expiry_date;
    }

    public String getCategory_name() {
        return category_name;
    }

    public void setCategory_name(String category_name) {
        this.category_name = category_name;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(String updated_at) {
        this.updated_at = updated_at;
    }

    public String getPlan_bucket() {
        return plan_bucket;
    }

    public void setPlan_bucket(String plan_bucket) {
        this.plan_bucket = plan_bucket;
    }


    public String getCust_mobile() {
		return cust_mobile;
	}

	public void setCust_mobile(String cust_mobile) {
		this.cust_mobile = cust_mobile;
	}

	public String getCust_email() {
		return cust_email;
	}

	public void setCust_email(String cust_email) {
		this.cust_email = cust_email;
	}

	public String getOrder_ids() {
		return order_ids;
	}

	public void setOrder_ids(String order_ids) {
		this.order_ids = order_ids;
	}

	public String getLatest_recharge_date() {
		return latest_recharge_date;
	}

	public void setLatest_recharge_date(String latest_recharge_date) {
		this.latest_recharge_date = latest_recharge_date;
	}

	public Integer getNotification_status() {
		return notification_status;
	}

	public void setNotification_status(Integer notification_status) {
		this.notification_status = notification_status;
	}

    public String getExtras() {
        return extra;
    }

    public void setExtras(String extra) {
        this.extra = extra;
    }

    public String getRecondId() {
        return reconId;
    }

    public void setReconId(String reconId){
        this.reconId = reconId;
    }

	@Override
    public String toString() {
        return "PlanValidityDataResponseModel{" +
                "recharge_number='" + recharge_number + '\'' +
                ", customer_id=" + customer_id +
                ", service='" + service + '\'' +
                ", operator='" + operator + '\'' +
                ", circle='" + circle + '\'' +
                ", amount=" + amount +
                ", validity_expiry_date='" + validity_expiry_date + '\'' +
                ", plan_bucket='" + plan_bucket + '\'' +
                ", category_name='" + category_name + '\'' +
                ", created_at='" + created_at + '\'' +
                ", updated_at='" + updated_at + '\'' +
                '}';
    }

}

