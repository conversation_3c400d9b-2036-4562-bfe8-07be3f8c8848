package com.paytm.saga.dto.oms;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.dto.OMSFulfillmentReqModel;
import com.paytm.saga.dto.OMSItemMetaModel;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

@ToString
@Data
public class OMSObject {
    public Long id;
    public Integer merchant_id;
    public Integer vertical_id;
    public String sku;
    public Integer mrp;
    public Double price;
    public Integer qty_ordered;
    public Integer status;
    public Long product_id;
    public Long order_id;
    public Date created_at;
    public Date updated_at;
    public String name;
    public Date ship_by_date;
    public Long fulfillment_id;
    public String fulfillment_req;
    public Double selling_price;
    public Double discount;
    public Integer conv_fee;
    public Object promo_code;
    public Object promo_description;
    public Double shipping_amount;
    public Integer fulfillment_service;
    public String custom_text1;
    public String attributes;
    public String info;
    public String custom_text4;
    public String custom_text5;
    public Object custom_text3;
    public Double max_refund;
    public String meta_data;
    public Boolean isSubscription;
    public Object subscriptionInfo;
    public Integer stamped_quantity;
    public Date ack_by;
    public Order order;
    public ArrayList<Fulfillment> fulfillments;
    public Date estimated_delivery;
    public ArrayList<Date> estimated_delivery_range;
    @JsonProperty("SLAextended")
    public String vertical_name;
    public String paymentStatus;
    public String paymentStatusText;
    public String thumbnail;
    public String image;
    public Integer category_id;
    public Integer brand_id;
}
