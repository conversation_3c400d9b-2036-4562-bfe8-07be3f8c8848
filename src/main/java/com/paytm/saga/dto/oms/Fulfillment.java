package com.paytm.saga.dto.oms;

import com.paytm.saga.dto.OMSFulfillmentResponseModel;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@ToString
@Data
public class Fulfillment {
    public Long id;
    public Long order_id;
    public Integer status;
    public String tracking_number;
    public Integer merchant_id;
    public String merchant_track_id;
    public Integer fulfillment_service_id;
    public String fulfillment_response;
    public String post_actions;
    public Date created_at;
    public Date updated_at;
    public String shipped_at;
    public String delivered_at;
    public String returned_at;
}
