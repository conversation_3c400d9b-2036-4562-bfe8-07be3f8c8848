package com.paytm.saga.dto.oms;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;

@Data
@ToString
public class Order {
    public Long id;
    public String customer_firstname;
    public String customer_lastname;
    public Date created_at;
    public Integer payment_status;
    public String customer_email;
    public Object promo_code;
    public Long customer_id;
    public Object promo_description;
    public String phone;
    public String title;
    public String channel_id;
    public String remote_ip;
    public Integer info;
    public Integer customer_type;
    public Object label;
    public Date updated_at;
    public String order_info;
    public Integer site_id;
    public Object gstin;
    public Object solution_name;
    public Boolean isNewCustomer;
    public ArrayList<Payment> payments;
    public ArrayList<Object> address;
    public Integer isCOD;
    public Integer order_item_count;
    public Integer is_payment_initiated;
    public String customer_email_enc;
    public String phone_enc;
}
