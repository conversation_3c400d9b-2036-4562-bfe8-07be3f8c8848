package com.paytm.saga.dto.oms;

import lombok.Data;
import lombok.ToString;

import java.util.Date;
@ToString
@Data
public class Payment {
    public Long id;
    public Long order_id;
    public Integer pg_amount;
    public String transaction_number;
    //public Integer kind;
    //public String payment_method;
    public Integer status;
    public String bank_transaction_id;
    public String payment_bank;
    //public String gateway;
    //public String provider;
    public String mid;
    //public String pg_response_code;
    //public String transaction_response;
    //public String appkey;
    public Date created_at;
    public Date updated_at;
}
