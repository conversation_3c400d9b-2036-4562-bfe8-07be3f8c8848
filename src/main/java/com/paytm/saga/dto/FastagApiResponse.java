package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Map;
import java.util.Objects;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FastagApiResponse {
    private Long make;
    private Long model;
    private Map<String,Object> variant;
    private String vehicleColor;
    private Integer variantId;

    public void setVariant(Map<String, Object> variant) {
        if(Objects.nonNull(variant) && variant.containsKey("variantId")){
            this.setVariantId((Integer) variant.get("variantId"));
        }
    }
}
