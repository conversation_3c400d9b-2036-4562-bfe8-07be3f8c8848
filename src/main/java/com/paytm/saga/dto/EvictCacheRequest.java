package com.paytm.saga.dto;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

public class EvictCacheRequest {
    @NotEmpty
    private List<String> cacheKey;

    public EvictCacheRequest() {

    }

    public EvictCacheRequest(List<String> cacheKey) {
        this.cacheKey = cacheKey;
    }

    public List<String> getCacheKey() {
        return cacheKey;
    }

    public void setCacheKey(List<String> cacheKey) {
        this.cacheKey = cacheKey;
    }

}
