package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ReminderMarkAsPaidResponse {

    @JsonProperty("statusCode")
    private String statusCode;


    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String toString() {
        return "ReminderMarkAsPaidResponse{" +
                "statusCode='" + statusCode + '\'' +
                '}';
    }
}
