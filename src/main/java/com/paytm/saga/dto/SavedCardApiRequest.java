package com.paytm.saga.dto;

import jakarta.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SavedCardApiRequest {
	@NotBlank(message = "type cannot be empty")
	private String type;
	@NotBlank(message = "service cannot be empty")
	@JsonProperty("product.service")
	private String service;
	@NotBlank(message = "cardType cannot be empty")
	@JsonProperty("product.cardType")
	private String cardType;
	@JsonProperty("isCardSkinRequired")
	private boolean isCardSkinRequired;

	public boolean getIsCardSkinRequired() {
		return isCardSkinRequired;
	}

	public void setIsCardSkinRequired(boolean isCardSkinRequired) {
		this.isCardSkinRequired = isCardSkinRequired;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

}
