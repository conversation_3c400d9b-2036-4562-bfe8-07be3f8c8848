package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ReminderCustomerOtherInfo {
	@JsonProperty("customerId")
	private Long customerId;
	@JsonProperty("currentMinBillAmount")
	private Double currentMinBillAmount;

	@JsonProperty("earlyPaymentDate")
	private String earlyPaymentDate;

	@JsonProperty("additionalFees")
	private ReminderCustomerOtherInfoAdditionalFees additionalFees;

	@JsonProperty("consumerName")
	private String consumerName;

	@JsonProperty("current_outstanding_amount")
	private Double currentOutstandingAmount;

	public ReminderCustomerOtherInfoAdditionalFees getAdditionalFees() {
		return additionalFees;
	}

	public void setAdditionalFees(ReminderCustomerOtherInfoAdditionalFees additionalFees) {
		this.additionalFees = additionalFees;
	}

	public String getConsumerName() {
		return consumerName;
	}

	public void setConsumerName(String consumerName) {
		this.consumerName = consumerName;
	}

	public String getEarlyPaymentDate() {
		return earlyPaymentDate;
	}

	public void setEarlyPaymentDate(String earlyPaymentDate) {
		this.earlyPaymentDate = earlyPaymentDate;
	}


	public Long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}

	public Double getCurrentMinBillAmount() {
		return currentMinBillAmount;
	}

	public void setCurrentMinBillAmount(Double currentMinBillAmount) {
		this.currentMinBillAmount = currentMinBillAmount;
	}

	public Double getCurrentOutstandingAmount() {
		return currentOutstandingAmount;
	}

	public void setCurrentOutstandingAmount(Double currentOutstandingAmount) {
		this.currentOutstandingAmount = currentOutstandingAmount;
	}
}
