package com.paytm.saga.dto;

import jakarta.validation.constraints.NotNull;

import com.paytm.saga.custom.validator.CircleValidation;
import com.paytm.saga.custom.validator.MarkAsPaidRechargeNumberValidation;
import com.paytm.saga.custom.validator.ReferenceIdValidation;
import lombok.Data;
import lombok.ToString;

@CircleValidation
@MarkAsPaidRechargeNumberValidation
@Data
@ToString
public class MarkAsPaidRequest {
    private String rechargeNumber;
    private String recharge_number;

    @NotNull(message="operator can't be null")
    private String operator;

    private Long customerId;

    private String referenceId;

    @NotNull(message="paytype can't be null")
    private String paytype;

    @NotNull(message="service can't be null")
    private String service;

    @NotNull(message="productId can't be null")
    private Long productId;

    private String planBucket;


    //@NotNull(message="amount can't be null")
    private Double amount;

    private String circle;

    private String expiry;

    private String bill_date;

    private String due_date;

    private String markAsPaidSource;
}
