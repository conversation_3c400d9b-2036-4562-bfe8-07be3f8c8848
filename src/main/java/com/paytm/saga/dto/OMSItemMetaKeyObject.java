package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class OMSItemMetaKeyObject {
    @JsonProperty("subId")
    private String subId;

    public OMSItemMetaKeyObject(String subId) {
        this.subId = subId;
    }

    public OMSItemMetaKeyObject() {

    }

    @Override
    public String toString() {
        return "OMSItemMetaKeyObject{" +
                "subId='" + subId + '\'' +
                '}';
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }
}
