package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ReminderUserDataResponseModel {
    @JsonProperty("recharge_number_2")
    private String recharge_number_2;
    @JsonProperty("recharge_number_3")
    private String recharge_number_3;
    @JsonProperty("recharge_number_4")
    private String recharge_number_4;

    public String getRecharge_number_2() {
        return recharge_number_2;
    }

    public void setRecharge_number_2(String recharge_number_2) {
        this.recharge_number_2 = recharge_number_2;
    }

    public String getRecharge_number_3() {
        return recharge_number_3;
    }

    public void setRecharge_number_3(String recharge_number_3) {
        this.recharge_number_3 = recharge_number_3;
    }

    public String getRecharge_number_4() {
        return recharge_number_4;
    }

    public void setRecharge_number_4(String recharge_number_4) {
        this.recharge_number_4 = recharge_number_4;
    }

    @Override
    public String toString() {
        return "ReminderUserDataResponseModel [recharge_number_2=" + recharge_number_2 + ", recharge_number_3="+ recharge_number_3 +
                ",recharge_number_4="+ recharge_number_4 + "]";
    }
}
