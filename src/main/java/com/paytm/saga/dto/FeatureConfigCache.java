package com.paytm.saga.dto;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.util.JsonUtils;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
@Data
public class FeatureConfigCache {
    private final Logger logger = LogManager.getLogger(FeatureConfigCache.class);
    private Map<String, Object> featureConfigMap;
    private Map<String, CardDetails> allowedSkinAndVariant = new HashMap<>();
    private Map<String, Object> suggestedCardConfig = new HashMap<>();

    private ObjectMapper objectMapper = new ObjectMapper();

    private static FeatureConfigCache featureConfigCache;

    private FeatureConfigCache() {
        featureConfigMap = new ConcurrentHashMap<>();
    }

    public static FeatureConfigCache getInstance() {
        if (featureConfigCache == null) {
            featureConfigCache = new FeatureConfigCache();
        }
        return featureConfigCache;
    }

    public Boolean getBoolean(String featureName) {
        return (Boolean) featureConfigMap.get(featureName);
    }

    public String getString(String featureName) {
        return (String) featureConfigMap.get(featureName);
    }

    public Integer getInteger(String featureName) {
        try {
            return (Integer) featureConfigMap.get(featureName);
        } catch (Exception e) {
            logger.error("Exception while parsing config");
            return null;
        }
    }
    public Object getObject(String featureName) {
        try {
            return (Object) featureConfigMap.get(featureName);
        } catch (Exception e) {
            logger.error("Exception while parsing config");
            return null;
        }
    }

    public List<String> getList(String featureName){
        try {
            return (List<String>) featureConfigMap.get(featureName);
        }
        catch (Exception e){
            logger.error("FeatureConfigCache.getList]:Exception in parsing config with feature_name={} and Exception={}",featureName,e.getMessage());
            return null;
        }
    }


    public void setMobileSuggestedCardsConfig (FeatureConfig featureConfig) {
       try {
           featureConfig.parseConfig();
           suggestedCardConfig = featureConfig.getConfigMap();
           logger.info("setMobileSuggestedCardsConfig :: added config {}", featureConfig);
       } catch (Exception e) {
           logger.error("setMobileSuggestedCardsConfig:: Exception while parsing config", e);
       }
    }

    public void set(FeatureConfig featureConfig) {
        featureConfig.parseConfig();
        featureConfigMap = featureConfig.getConfigMap();
        allowedSkinAndVariant = getAllowedSkinAndVariant("allowedSkinAndVariant");
        logger.info("featureConfigMap :: add {}", featureConfigMap);
    }
    private Map<String,CardDetails> getAllowedSkinAndVariant(String featureName){
        try {
            Map<String,CardDetails> finalAllowedSkinVariantMap = new LinkedHashMap<>();
            List<Object> listOfMap = (List<Object>) featureConfigMap.get(featureName);
            for(Object map : listOfMap){
                Map<String,CardDetails> skinMap = JsonUtils.convertObjectToMap(map);
                finalAllowedSkinVariantMap.putAll(skinMap);
            }
            return finalAllowedSkinVariantMap;
        } catch (Exception e) {
            logger.error("Exception while parsing config");
            return null;
        }
    }

    public CardDetails getCardVariantSkinDetails(String cardVariant){
        CardDetails cardDetails = null;
        try {
            if(CollectionUtils.isEmpty(allowedSkinAndVariant))
                return null;
            for(String key : allowedSkinAndVariant.keySet()){
                if(cardVariant.toLowerCase().contains(key.toLowerCase())) {
                    Object mapping = allowedSkinAndVariant.get(key);
                    String mappingString =  objectMapper.writeValueAsString(mapping);
                    cardDetails = objectMapper.readValue(mappingString,new TypeReference<CardDetails>() {
                    });
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("Exception while parsing config:{}",e);
            return null;
        }
        return cardDetails;
    }
    public void setFeatureConfigMap(Map<String, Object> featureConfigMap) {
        this.featureConfigMap = featureConfigMap;
    }


    public Long getLong(String featureName) {
        try {
            return Long.valueOf((String) featureConfigMap.get(featureName));
        } catch (Exception e) {
            logger.error("Exception while parsing config");
            return null;
        }
    }
}
