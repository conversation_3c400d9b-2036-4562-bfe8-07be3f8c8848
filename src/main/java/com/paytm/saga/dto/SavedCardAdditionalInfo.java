package com.paytm.saga.dto;

public class SavedCardAdditionalInfo {
	private String cin;
	private String panUniqueReference;
	private String tin;
	private boolean isCardCoft;
	private boolean isEligibleForCoft;
	private String tokenStatus;

	public String getCin() {
		return cin;
	}

	public void setCin(String cin) {
		this.cin = cin;
	}

	public String getPanUniqueReference() {
		return panUniqueReference;
	}

	public void setPanUniqueReference(String panUniqueReference) {
		this.panUniqueReference = panUniqueReference;
	}

	public String getTin() {
		return tin;
	}

	public void setTin(String tin) {
		this.tin = tin;
	}

	public boolean isCardCoft() {
		return isCardCoft;
	}

	public void setCardCoft(boolean cardCoft) {
		isCardCoft = cardCoft;
	}

	public boolean isEligibleForCoft() {
		return isEligibleForCoft;
	}

	public void setEligibleForCoft(boolean eligibleForCoft) {
		isEligibleForCoft = eligibleForCoft;
	}

	public String getTokenStatus() {
		return tokenStatus;
	}

	public void setTokenStatus(String tokenStatus) {
		this.tokenStatus = tokenStatus;
	}

	@Override
	public String toString() {
		return "SavedCardAdditionalInfo{" +
				"cin='" + cin + '\'' +
				", panUniqueReference='" + panUniqueReference + '\'' +
				", tin='" + tin + '\'' +
				", isCardCoft='" + isCardCoft + '\'' +
				", isEligibleForCoft='" + isEligibleForCoft + '\'' +
				", tokenStatus='" + tokenStatus + '\'' +
				'}';
	}
}
