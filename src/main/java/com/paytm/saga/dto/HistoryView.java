package com.paytm.saga.dto;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@JsonIgnoreProperties(value = { "previousTxnHistory","billState","planExpiring","operatorLabel","bookingId","ivrsBooking","previousDate", "invalidPlan", "eventDate", "planExpiryDays", "planDetail", "mnp",
		"lastCard" })
@Data
public class HistoryView {
//	private String dateTime;
	private Date eventDate;
	private Date previousDate;
	private int planExpiryDays;
	private Map<String, String> planDetail;
	private boolean invalidPlan;
	private boolean planExpiring;

	public String getMnpType() {
		return mnpType;
	}

	public void setMnpType(String mnpType) {
		this.mnpType = mnpType;
	}

	private String themeType;
	private String mnpType;
	private String createdDate;
	private double amount;
	private String cardId;
	private String status;
	private long orderId;
	private String planBucket;
	private String carddeeplink;
	private List<ViewElementInfo> headings;
	private List<ViewElementInfo> displayValues;
	private List<ViewElementInfo> cta;
	private List<ViewElementInfo> childCta;
	private List<ViewElementInfo> footer;
	private String circle;
	private String operator;
	private String service;
	private String payType;
	private String eventType;
	private boolean mnp;
	private boolean lastCard;
	private boolean previousTxnHistory;
	private String planDescription;
	private String planExpiryDate;
	private boolean ivrsBooking;
	private String operatorLabel;
	private String bookingId;
	private int billState;
	private String billDate;
	private String dueDate;
	private String statusCode;
	private String paymentStatus;
	private String previousOperator;
	private boolean showInfoIcon;
	private boolean isUtility;
	private Date automaticDate;

	private String previousPayType;

	private double minimumDue;
        private boolean isNative;


	public HistoryView() {

	}

	public HistoryView(Date eventDate, Date previousDate, int planExpiryDays, Map<String, String> planDetail,
			boolean invalidPlan, String themeType, String createdDate, double amount, String cardId, String status,
			long orderId, String planBucket, String carddeeplink, List<ViewElementInfo> headings,
			List<ViewElementInfo> displayValues, List<ViewElementInfo> cta,List<ViewElementInfo> childCta, List<ViewElementInfo> footer, String circle,
			String operator, String service, String payType, String eventType, boolean mnp, boolean lastCard,
			String planDescription, String planExpiryDate, boolean ivrsBooking, String operatorLabel, String bookingId,
			String billDate, String dueDate, String previousPayType, double minimumDueAmount) {
		this.eventDate = eventDate;
		this.previousDate = previousDate;
		this.planExpiryDays = planExpiryDays;
		this.planDetail = planDetail;
		this.invalidPlan = invalidPlan;
		this.themeType = themeType;
		this.createdDate = createdDate;
		this.amount = amount;
		this.cardId = cardId;
		this.status = status;
		this.orderId = orderId;
		this.planBucket = planBucket;
		this.carddeeplink = carddeeplink;
		this.headings = headings;
		this.displayValues = displayValues;
		this.cta = cta;
		this.childCta = childCta;
		this.footer = footer;
		this.circle = circle;
		this.operator = operator;
		this.service = service;
		this.payType = payType;
		this.eventType = eventType;
		this.mnp = mnp;
		this.lastCard = lastCard;
		this.planDescription = planDescription;
		this.planExpiryDate = planExpiryDate;
		this.ivrsBooking = ivrsBooking;
		this.operatorLabel = operatorLabel;
		this.bookingId = bookingId;
		this.billDate = billDate;
		this.dueDate = dueDate;
		this.previousPayType = previousPayType;
		this.minimumDue = minimumDueAmount;
	}

}
