package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.util.RequestValidatorV2;
import com.paytm.saga.util.RequestValidator;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

@Data
public class FetchRecentsRequest {

    @JsonProperty("customerId")
    private Long customerId;

    @NotEmpty(groups = RequestValidatorV2.class, message = "Mandatory params missing")
    @JsonProperty("recharge_number")
    private String rechargeNumber;

    @NotEmpty(groups = RequestValidatorV2.class, message = "Mandatory params missing")
    @JsonProperty("operator")
    private String operator;

    @NotEmpty(groups = {RequestValidator.class, RequestValidatorV2.class},message = "Mandatory params missing")
    @JsonProperty("service")
    private String service;

    @NotEmpty(groups = RequestValidator.class, message = "Mandatory params missing")
    @JsonProperty("paytype")
    private String paytype;


}
