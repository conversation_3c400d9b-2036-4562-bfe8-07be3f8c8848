

package com.paytm.saga.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;
@ToString
@Getter
@Setter
public class CustomerNameRequest {


    @NotNull(message = "customerId cannot be null")
    private Long customer_id;

    @NotNull(message = "recharge_number cannot be null")
    private String recharge_number;

    @NotNull(message = "service cannot be null")
    private String service;

    @NotNull(message = "operator cannot be null")
    private String operator;

    private String pid;

}