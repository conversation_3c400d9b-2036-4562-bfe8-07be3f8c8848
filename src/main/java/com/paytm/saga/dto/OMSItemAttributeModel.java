package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OMSItemAttributeModel {
    @JsonProperty("service")
    private String service;

    @JsonProperty("paytype")
    private String paytype;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("circle")
    private String circle;



    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getCircle() {
        return circle;
    }

    public void setCircle(String circle) {
        this.circle = circle;
    }

    @Override
    public String toString() {
        return "OMSItemAttributeModel{" +
                "service='" + service + '\'' +
                ", paytype='" + paytype + '\'' +
                ", operator='" + operator + '\'' +
                ", circle='" + circle + '\'' +
                '}';
    }
}
