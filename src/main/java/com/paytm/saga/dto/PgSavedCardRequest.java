package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PgSavedCardRequest {
    @JsonProperty("body")
    PgSavedCardBody body;
    @JsonProperty("head")
    PgSavedCardHead head;

    public PgSavedCardBody getBody() {
        return body;
    }

    public void setBody(PgSavedCardBody body) {
        this.body = body;
    }

    public PgSavedCardHead getHead() {
        return head;
    }

    public void setHead(PgSavedCardHead head) {
        this.head = head;
    }

    @Override
    public String toString() {
        return "PgSavedCardV2Request{" +
                "body=" + body +
                ", head=" + head +
                '}';
    }
}

