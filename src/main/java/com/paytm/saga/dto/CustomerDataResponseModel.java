package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.ToString;

@ToString
public class CustomerDataResponseModel {
    @JsonProperty("subscriberNumber")
    private String subscriberNumber;

    @JsonProperty("minReloadAmount")
    private String minReloadAmount;

    @JsonProperty("currentBillAmount")
    private String currentBillAmount;

    @JsonProperty("billDueDate")
    private String billDueDate;

    @JsonProperty("billNumber")
    private String billNumber;

    @JsonProperty("incentiveAmount")
    private String incentiveAmount;

    @JsonProperty("totalAmount")
    private String totalAmount;

    @JsonProperty("incentiveDate")
    private String incentiveDate;

    @JsonProperty("nextBillFetchDate")
    private String nextBillFetchDate;

    @JsonProperty("billDate")
    private String billDate;

    @JsonProperty("ref_id")
    private String ref_id;

    @JsonProperty("pre_booking")
    private Boolean pre_booking;

    @JsonProperty("flowType")
    private String flowType;

    @JsonProperty("customerName")
    private String customerName;



    public String getSubscriberNumber() {
        return subscriberNumber;
    }

    public void setSubscriberNumber(String subscriberNumber) {
        this.subscriberNumber = subscriberNumber;
    }

    public String getMinReloadAmount() {
        return minReloadAmount;
    }

    public void setMinReloadAmount(String minReloadAmount) {
        this.minReloadAmount = minReloadAmount;
    }

    public String getCurrentBillAmount() {
        return currentBillAmount;
    }

    public void setCurrentBillAmount(String currentBillAmount) {
        this.currentBillAmount = currentBillAmount;
    }

    public String getBillDueDate() {
        return billDueDate;
    }

    public void setBillDueDate(String billDueDate) {
        this.billDueDate = billDueDate;
    }

    public String getBillNumber() {
        return billNumber;
    }

    public void setBillNumber(String billNumber) {
        this.billNumber = billNumber;
    }

    public String getIncentiveAmount() {
        return incentiveAmount;
    }

    public void setIncentiveAmount(String incentiveAmount) {
        this.incentiveAmount = incentiveAmount;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getIncentiveDate() {
        return incentiveDate;
    }

    public void setIncentiveDate(String incentiveDate) {
        this.incentiveDate = incentiveDate;
    }

    public String getNextBillFetchDate() {
        return nextBillFetchDate;
    }

    public void setNextBillFetchDate(String nextBillFetchDate) {
        this.nextBillFetchDate = nextBillFetchDate;
    }

    public String getBillDate() {
        return billDate;
    }

    public void setBillDate(String billDate) {
        this.billDate = billDate;
    }

    public String getRef_id() {
        return ref_id;
    }

    public void setRef_id(String ref_id) {
        this.ref_id = ref_id;
    }

    public Boolean getPre_booking() {
        return pre_booking;
    }

    public void setPre_booking(Boolean pre_booking) {
        this.pre_booking = pre_booking;
    }

    public String getFlowType() {
        return flowType;
    }

    public void setFlowType(String flowType) {
        this.flowType = flowType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
}



