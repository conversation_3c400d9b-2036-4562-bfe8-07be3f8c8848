package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class RecentCRUDResponseData {
    @JsonProperty("rows_updated")
    private String rows_updated;

    @JsonProperty("fields")
    private List<String> fields;

    public String getRows_updated() {
        return rows_updated;
    }

    public void setRows_updated(String rows_updated) {
        this.rows_updated = rows_updated;
    }

    public List<String> getFields() {
        return fields;
    }

    public void setFields(List<String> fields) {
        this.fields = fields;
    }

    @Override
    public String toString() {
        return "RecentCRUDResponseData{" +
                "rows_updated='" + rows_updated + '\'' +
                ", fields=" + fields +
                '}';
    }
}
