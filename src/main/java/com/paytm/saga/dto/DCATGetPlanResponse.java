package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class DCATGetPlanResponse {
    @JsonProperty("plan_bucket")
    private String plan_bucket;

    @JsonProperty("data")
    private String data;

    @JsonProperty("price")
    private String price;

    @JsonProperty("validity")
    private String validity;

    @JsonProperty("talktime")
    private String talktime;

    @JsonProperty("sms")
    private String sms;

    @JsonProperty("description")
    private String description;

    @JsonProperty("addon_benefit")
    private List<String> addon_benefit;

    @JsonProperty("addon_benefit1")
    private String addon_benefit1;

    @JsonProperty("addon_benefit2")
    private String addon_benefit2;

    @JsonProperty("addon_benefit3")
    private String addon_benefit3;

    @JsonProperty("addon_benefit4")
    private String addon_benefit4;


    @JsonProperty("productId")
    private String plan_id;

    public String getPlan_bucket() {
        return plan_bucket;
    }

    public void setPlan_bucket(String plan_bucket) {
        this.plan_bucket = plan_bucket;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getValidity() {
        return validity;
    }

    public void setValidity(String validity) {
        this.validity = validity;
    }

    public String getTalktime() {
        return talktime;
    }

    public void setTalktime(String talktime) {
        this.talktime = talktime;
    }

    public String getSms() {
        return sms;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getAddon_benefit() {
        return addon_benefit;
    }

    public void setAddon_benefit(List<String> addon_benefit) {
        this.addon_benefit = addon_benefit;
    }

    public String getAddon_benefit1() {
        return addon_benefit1;
    }

    public void setAddon_benefit1(String addon_benefit1) {
        this.addon_benefit1 = addon_benefit1;
    }

    public String getAddon_benefit2() {
        return addon_benefit2;
    }

    public void setAddon_benefit2(String addon_benefit2) {
        this.addon_benefit2 = addon_benefit2;
    }

    public String getAddon_benefit3() {
        return addon_benefit3;
    }

    public void setAddon_benefit3(String addon_benefit3) {
        this.addon_benefit3 = addon_benefit3;
    }

    public String getAddon_benefit4() {
        return addon_benefit4;
    }

    public void setAddon_benefit4(String addon_benefit4) {
        this.addon_benefit4 = addon_benefit4;
    }

    public String getPlan_id() {
        return plan_id;
    }

    public void setPlan_id(String plan_id) {
        this.plan_id = plan_id;
    }


    @Override
    public String toString() {
        return "DCATGetPlanResponse{" +
                "plan_bucket='" + plan_bucket + '\'' +
                ", data='" + data + '\'' +
                ", price='" + price + '\'' +
                ", validity='" + validity + '\'' +
                ", talktime='" + talktime + '\'' +
                ", sms='" + sms + '\'' +
                ", description='" + description + '\'' +
                ", addon_benefit=" + addon_benefit +
                ", addon_benefit1='" + addon_benefit1 + '\'' +
                ", addon_benefit2='" + addon_benefit2 + '\'' +
                ", addon_benefit3='" + addon_benefit3 + '\'' +
                ", addon_benefit4='" + addon_benefit4 + '\'' +
                ", plan_id='" + plan_id + '\'' +
                '}';
    }

    public DCATGetPlanResponse() {
    }
}
