package com.paytm.saga.dto;

public class ChannelDetail {
	private long customerId;
	private String rechargeNumber;
	private String rechargeNumber2;
	private String rechargeNumber3;
	private String rechargeNumber4;
	private String service;
	private String operator;
	private String categoryId;
	private String productId;
	private Boolean isRenewSubscription;

	public long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(long customerId) {
		this.customerId = customerId;
	}

	public String getRechargeNumber() {
		return rechargeNumber;
	}

	public void setRechargeNumber(String rechargeNumber) {
		this.rechargeNumber = rechargeNumber;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getRechargeNumber2() {
		return rechargeNumber2;
	}

	public void setRechargeNumber2(String rechargeNumber2) {
		this.rechargeNumber2 = rechargeNumber2;
	}

	public String getRechargeNumber3() {
		return rechargeNumber3;
	}

	public void setRechargeNumber3(String rechargeNumber3) {
		this.rechargeNumber3 = rechargeNumber3;
	}

	public String getRechargeNumber4() {
		return rechargeNumber4;
	}

	public void setRechargeNumber4(String rechargeNumber4) {
		this.rechargeNumber4 = rechargeNumber4;
	}

	public Boolean getIsRenewSubscription() {
		return isRenewSubscription;
	}

	public void setIsRenewSubscription(Boolean renewSubscription) {
		isRenewSubscription = renewSubscription;
	}
}
