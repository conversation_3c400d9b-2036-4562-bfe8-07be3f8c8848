package com.paytm.saga.dto;


import org.springframework.http.HttpStatus;

public class MarkAsPaidResponse {

    private int statusCode;
    private String displayMessage;
    private String errorMessage;
    private String errorCode;

    public HttpStatus getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(HttpStatus httpStatus) {
        this.httpStatus = httpStatus;
    }

    private HttpStatus httpStatus;

    public MarkAsPaidResponse(int statusCode, String displayMessage, String errorMessage, String errorCode, HttpStatus httpStatus) {
        this.statusCode = statusCode;
        this.displayMessage = displayMessage;
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getDisplayMessage() {
        return displayMessage;
    }

    public void setDisplayMessage(String displayMessage) {
        this.displayMessage = displayMessage;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    @Override
    public String toString() {
        return "MarkAsPaidResponse{" +
                "statusCode=" + statusCode +
                ", displayMessage='" + displayMessage + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", httpStatus=" + httpStatus +
                '}';
    }
}
