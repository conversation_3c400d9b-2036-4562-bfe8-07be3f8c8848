package com.paytm.saga.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SavedCardResponse {
	@JsonProperty("recents")
	private List<SavedCardApiResponse> savedCardApiResponses;

	public List<SavedCardApiResponse> getSavedCardApiResponses() {
		return savedCardApiResponses;
	}

	public void setSavedCardApiResponses(List<SavedCardApiResponse> savedCardApiResponses) {
		this.savedCardApiResponses = savedCardApiResponses;
	}
}
