package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.ToString;

import java.util.List;

@ToString
public class ValidationResponseMetaAdditionalPayload {
    @JsonProperty("Multiple_RMN")
    private Boolean multipleRMN;

    public Boolean getMultipleRMN() {
        return multipleRMN;
    }

    public void setMultipleRMN(Boolean multipleRMN) {
        this.multipleRMN = multipleRMN;
    }

    public ValidationResponseMetaAdditionalPayload(Boolean multipleRMN) {
        this.multipleRMN = multipleRMN;
    }
    public ValidationResponseMetaAdditionalPayload() {

    }

}
