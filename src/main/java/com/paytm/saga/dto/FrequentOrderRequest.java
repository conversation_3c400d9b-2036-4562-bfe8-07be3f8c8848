package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class FrequentOrderRequest {

    private Long customerId;

    @JsonProperty("recharge_number")
    private String rechargeNumber;

    private List<String> services;

    private String operator;

    private Boolean excludeDropoff;

    private Boolean onlyReminder;

    private Boolean isCoft;

    private String client;

    private String version;

    @JsonProperty("method_type")
    private String methodType;

    @JsonProperty("isCardSkinRequired")
    private Boolean isCardSkinRequired;
  
     @JsonProperty("bill_type")
    private String recentBillType;

    @JsonProperty("is_renew_subscription")
    private Boolean isReNewSubscription;

    private String apiVersion;

}
