package com.paytm.saga.dto;

import java.util.List;

import com.paytm.saga.model.ChannelHistory;

public class FilteredEvents {
	private List<ChannelHistory> events;
	private List<ChannelHistory> markFinaliseData;
	private List<ChannelHistory> moveToFinaliseData;
	public List<ChannelHistory> getEvents() {
		return events;
	}
	public void setEvents(List<ChannelHistory> events) {
		this.events = events;
	}
	public List<ChannelHistory> getMarkFinaliseData() {
		return markFinaliseData;
	}
	public void setMarkFinaliseData(List<ChannelHistory> markFinaliseData) {
		this.markFinaliseData = markFinaliseData;
	}
	public List<ChannelHistory> getMoveToFinaliseData() {
		return moveToFinaliseData;
	}
	public void setMoveToFinaliseData(List<ChannelHistory> moveToFinaliseData) {
		this.moveToFinaliseData = moveToFinaliseData;
	}

	
}
