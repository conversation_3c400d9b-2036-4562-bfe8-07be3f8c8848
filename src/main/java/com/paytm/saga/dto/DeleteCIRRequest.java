package com.paytm.saga.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class DeleteCIRRequest {
    @JsonProperty("customerid")
    @NotNull(message = "customerId can't be empty")
    private Long customerId;

    @JsonProperty("bureauname")
    @NotNull(message = "bureauName can't be empty")
    private String bureauName;

    @JsonProperty("bankname")
    @NotNull(message = "bankName can't be empty")
    private String bankName;
}
