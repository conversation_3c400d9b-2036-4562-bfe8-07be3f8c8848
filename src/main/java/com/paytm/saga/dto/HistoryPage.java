package com.paytm.saga.dto;

import java.util.List;

public class HistoryPage {
	private final Integer count;
	private String status;
	private int code;
	private final String lastCardId;
	private final boolean availableNext;
	private String[] dedupeids;
	private String customer_name;
	private final ChannelDetail channelDetails;
	private AutomaticCardView automaticCard;
	private final List<HistoryView> cards;

	private ThemeResponse themeDetails;

	public HistoryPage(List<HistoryView> cards, Integer count, String lastCardId, boolean availableNext, String status,
					   int code, ChannelDetail channelDetails) {
		this.count = count;
		this.cards = cards;
		this.status = status;
		this.code = code;
		this.lastCardId = lastCardId;
		this.availableNext = availableNext;
		this.channelDetails = channelDetails;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	public String getCustomer_name() {
		return customer_name;
	}

	public void setCustomer_name(String customer_name) {
		this.customer_name = customer_name;
	}
	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public Integer getCount() {
		return count;
	}

	public String getLastCardId() {
		return lastCardId;
	}

	public boolean getAvailableNext() {
		return availableNext;
	}

	public List<HistoryView> getCards() {
		return cards;
	}

	public ChannelDetail getChannelDetails() {
		return channelDetails;
	}

	public ThemeResponse getThemeDetails() {
		return themeDetails;
	}

	public void setThemeDetails(ThemeResponse themeDetails) {
		this.themeDetails = themeDetails;
	}

	public String[] getDedupeids() {
		return dedupeids;
	}

	public void setDedupeids(String[] dedupeids) {
		this.dedupeids = dedupeids;
	}
	public void setAutomaticCard(AutomaticCardView automaticCard){
		this.automaticCard = automaticCard;
	}
	public AutomaticCardView getAutomaticCard() {
		return automaticCard;
	}

}
