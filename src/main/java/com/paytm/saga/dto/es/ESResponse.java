package com.paytm.saga.dto.es;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.dto.oms.OMSResponse;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;

@ToString
@Getter
@Setter
@Data
public class ESResponse  {

    @JsonProperty("took")
    private Integer took;
    @JsonProperty("timed_out")
    private boolean timedOut;
    @JsonProperty("_shards")
    private  EsShards shards;
    @JsonProperty("hits")
    private EsHits hits;

}


