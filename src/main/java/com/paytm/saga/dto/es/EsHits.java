package com.paytm.saga.dto.es;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@ToString
@Getter
@Setter
public class EsHits {

    @JsonProperty("total")
    private Map<String, Object> total;

    @JsonProperty("max_score")
    private Double maxScore;

    @JsonProperty("hits")
    private List<HitsEs> hits;


}
