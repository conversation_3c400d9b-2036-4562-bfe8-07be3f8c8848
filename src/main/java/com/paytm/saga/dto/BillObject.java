package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BillObject {

    private Double billAmount;
    private String billState;
    private String billDate;
    private String expiryDate;
    private String billDueDate;
    private Double min_due_amount;
    private Double original_due_amount;
    private Double original_min_due_amount;
    private String markAsPaidDate;
    private Boolean markedAsPaid;
    private Boolean isBillDue;
    private String plan_bucket;
    private String plan;
    private String bill_source;
    @JsonProperty("current_outstanding_amount")
    private Double currentOutstandingAmount;


}
