package com.paytm.saga.dto.catalogue;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.builder.ProductMinBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import static com.paytm.saga.common.constant.Constants.CommonConstants.INACTIVE_PID_STATUS;
import static com.paytm.saga.common.constant.Constants.Delimiters.UNDERSCORE;

@Component
@Scope("singleton")
public class CVRProductCache {

    private static final Logger logger = LogManager.getLogger(CVRProductCache.class);


    private final Map<Long, ProductMin> productDataMap;
    private final Map<String, Long> bankCodeCardNetworkKeyToProductIdMap;

    private static CVRProductCache cvrProductCache;

    private CVRProductCache() {
        productDataMap = new ConcurrentHashMap<Long, ProductMin>();
        bankCodeCardNetworkKeyToProductIdMap = new ConcurrentHashMap<>();
    }

    public static CVRProductCache getInstance() {
        if (cvrProductCache == null) {
            cvrProductCache = new CVRProductCache();
        }
        return cvrProductCache;
    }

    public ProductMin getProductDetails(Long productId) {
        if (Objects.isNull(productId)) {
            return null;
        }
        return productDataMap.get(productId);
    }

    public String getBankCode(Long productId) {
        if (Objects.isNull(productId)) {
            return null;
        }
        ProductMin product = productDataMap.get(productId);
        if (product != null) {
            return product.getBankCode();
        }
        return null;
    }

    public Long getProductId(String bankCode, String service, String payType) {
        String rupayKey = bankCode + UNDERSCORE + Constants.CardNetworkType.RUPAY + UNDERSCORE + service + UNDERSCORE + payType + UNDERSCORE + 1;
        String viabbpsKey = bankCode + UNDERSCORE + Constants.CardNetworkType.VIABBPS + UNDERSCORE + service + UNDERSCORE + payType + UNDERSCORE + 1;
        if (bankCodeCardNetworkKeyToProductIdMap.containsKey(rupayKey)) {
            return bankCodeCardNetworkKeyToProductIdMap.get(rupayKey);
        } else return bankCodeCardNetworkKeyToProductIdMap.getOrDefault(viabbpsKey, null);
    }

    private String makeBankCardNetworkKey(ProductMin productMin){
        return productMin.getBankCode()
                + UNDERSCORE + productMin.getCardNetwork()
                + UNDERSCORE + productMin.getService()
                + UNDERSCORE + productMin.getPayType()
                + UNDERSCORE + productMin.getStatus();
    }

    public void addProductDetails(Product product) {
        ProductMin minProduct = ProductMinBuilder.buildProductMin.apply(product);
        if (productDataMap.get(product.getProductId()) != null) {
            if (!productDataMap.get(product.getProductId()).equals(minProduct)) {
                productDataMap.put(product.getProductId(), minProduct);
                bankCodeCardNetworkKeyToProductIdMap.put(makeBankCardNetworkKey(minProduct), product.getProductId());
            }
        } else {
            productDataMap.put(product.getProductId(), minProduct);
            bankCodeCardNetworkKeyToProductIdMap.put(makeBankCardNetworkKey(minProduct), product.getProductId());
        }

    }

    public String getCustomOperatorForCreditCard(Long productId) {
        if (Objects.isNull(productId)) {
            return null;
        }
        ProductMin productMin = productDataMap.get(productId);
        if (productMin != null && Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(productMin.getPayType()) && productMin.getBankCode()!=null) {
            return (productMin.getBankCode() + UNDERSCORE
                    + (StringUtils.isEmpty(productMin.getIsPaytmFirstCard()) ? "0" : productMin.getIsPaytmFirstCard())).toLowerCase();
        }
        return null;
    }

    public boolean isInactivePID(Long productId) {
        ProductMin product = CVRProductCache.getInstance().getProductDetails(productId);

        if (product == null) {
            logger.error("isInactivePID : Product is null");
            return true;
        }

        if (INACTIVE_PID_STATUS.contains(product.getStatus())) {
            logger.error("isInactivePID : Product is inactive pid is {} and status is {}", product.getProductId(), product.getStatus());
            return true;
        }
        return false;
    }

    public boolean isInactivePID(String productId) {
        if (productId == null) {
            logger.error("isInactivePID : ProductId is null");
            return true;
        }
        return isInactivePID(Long.valueOf(productId));

    }

}
