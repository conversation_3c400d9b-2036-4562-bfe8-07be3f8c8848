package com.paytm.saga.dto.catalogue;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductAttributes {
    @JsonProperty("bank_code")
    private String bankCode;
    @JsonProperty("card_network")
    private String cardNetwork;
    @JsonProperty("is_paytm_first_card")
    private String isPaytmFirstCard;
    @JsonProperty("schedulable")
    private Integer schedulable;
    @JsonProperty("min_amount")
    private Double minAmount;
}
