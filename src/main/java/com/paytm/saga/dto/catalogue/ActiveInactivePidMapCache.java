package com.paytm.saga.dto.catalogue;


import com.paytm.saga.model.ActiveInactiveMap;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Scope("singleton")
public class ActiveInactivePidMapCache {
    private Map<String, Long> pidMap;

    private static ActiveInactivePidMapCache activeInactivePidMapCache;

    private ActiveInactivePidMapCache() {
        pidMap = new HashMap<>();
    }

    public static ActiveInactivePidMapCache getInstance() {
        if (activeInactivePidMapCache == null) {
            activeInactivePidMapCache = new ActiveInactivePidMapCache();
        }
        return activeInactivePidMapCache;
    }

    public void storePidMapByApi(Map<String, Long> apiMap) {
        if (apiMap == null) {
            pidMap = null;
            return;
        }
        pidMap = apiMap;
    }

    public void storePidMapByDbHit(List<ActiveInactiveMap> activeInactiveMapList) {
        for (ActiveInactiveMap element : activeInactiveMapList) {
            if (element.getStatus() == 1) {
                pidMap.put(element.getOldPid().toString(), element.getNewPid());
            } else {
                pidMap.remove(element.getOldPid().toString());
            }
        }
    }

    public Map<String, Long> getPidMap() {
        return pidMap;
    }

    public Long getActiveInactivePid(String inactivePid) {
        return pidMap.get(inactivePid);
    }

    public Long getActivePid(Long pid) {
        if (pid == null)
            return null;
        Long activePID = getActiveInactivePid(String.valueOf(pid));
        return activePID != null ? activePID : Long.valueOf(pid);
    }

    public String getActivePid(String pid) {
        if (pid == null)
            return null;
        Long activePID = getActiveInactivePid(pid);
        return activePID != null ? String.valueOf(activePID) : pid;
    }

}
