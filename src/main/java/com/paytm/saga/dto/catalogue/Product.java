package com.paytm.saga.dto.catalogue;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Product {

    @JsonProperty("service")
    private String service;
    @JsonProperty("paytype")
    private String payType;
    @JsonProperty("operator")
    private String operator;
    @JsonProperty("circle")
    private String circle;
    @JsonProperty("producttype")
    private String productType;
    @JsonProperty("paytype_label")
    private String payTypeLabel;
    @JsonProperty("operator_label")
    private String operatorLabel;
    @JsonProperty("vertical_id")
    private Long verticalId;
    @JsonProperty("status")
    private Integer status;
    @JsonProperty("category_id")
    private Long categoryId;
    @JsonProperty("brand")
    private String brand;
    @JsonProperty("merchant_id")
    private Long merchantId;
    @JsonProperty("attributes")
    private String attributes;
    @JsonProperty("product_id")
    private Long productId;
    @JsonProperty("display_name")
    private String displayName;
    @JsonProperty("fulfillment_service")
    private Long fulfillmentService;
    @JsonProperty("validate")
    private Integer validate;
    @JsonProperty("price")
    private Double price;
    @JsonProperty("thumbnail")
    private String thumbnail;
}
