package com.paytm.saga.dto.catalogue;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Objects;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductMin {

    @JsonProperty("service")
    private String service;

    @JsonProperty("paytype")
    private String payType;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("product_id")
    private Long productId;

    @JsonProperty("card_network")
    private String cardNetwork;

    @JsonProperty("bank_code")
    private String bankCode;

    @JsonProperty("category_id")
    private Long categoryId;

    @JsonProperty("is_paytm_first_card")
    private String isPaytmFirstCard;
    @JsonProperty("status")
    private Integer status;
    @JsonProperty("schedulable")
    private Integer isSchedulable;

    @JsonProperty("operator_label")
    private String operatorLabel;

    @JsonProperty("min_amount")
    private Double minAmount;

    @JsonProperty("circle")
    private String circle;

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ProductMin other = (ProductMin) obj;
        return Objects.equals(bankCode, other.bankCode) && Objects.equals(cardNetwork, other.cardNetwork)
                && Objects.equals(isPaytmFirstCard, other.isPaytmFirstCard) && Objects.equals(operator, other.operator)
                && Objects.equals(payType, other.payType) && Objects.equals(productId, other.productId)
                && Objects.equals(service, other.service) && Objects.equals(status, other.status) && Objects.equals(categoryId, other.categoryId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bankCode, cardNetwork, isPaytmFirstCard, operator, payType, productId, service, status);
    }

}
