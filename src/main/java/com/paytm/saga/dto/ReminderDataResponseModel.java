package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ReminderDataResponseModel {
    @JsonProperty("user_data")
    private String user_data;
    @JsonProperty("recharge_number")
    private String recharge_number;

    @JsonProperty("customer_id")
    private Long customer_id;

    @JsonProperty("product_id")
    private Long product_id;

    @JsonProperty("service")
    private String service;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("circle")
    private String circle;

    @JsonProperty("amount")
    private Double amount;

    @JsonProperty("bill_date")
    private String bill_date;

    @JsonProperty("due_date")
    private String due_date;

    @JsonProperty("paytype")
    private String paytype;

    @JsonProperty("created_at")
    private String created_at;

    @JsonProperty("updated_at")
    private String updated_at;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("is_automatic")
    private Integer is_automatic;

    @JsonProperty("subscriptionIsAutomatic")
    private Integer subscriptionIsAutomatic;

    @JsonProperty("currentMinBillAmount")
    private Double currentMinBillAmount;

    @JsonProperty("current_outstanding_amount")
    private Double currentOutstandingAmount;

    @JsonProperty("par_id")
    private String par;
    
    @JsonProperty("tin")
    private String tin;

    @JsonProperty("reference_id")
    private String reference_id;

    @JsonProperty("customerOtherInfo")
    private String customerOtherInfo;

    @JsonProperty("extra")
    private String extra;

    @JsonProperty("last_paid_amount")
    private Double lastPaidAmount;

    @JsonProperty("payment_date")
    private String paymentDate;
    @JsonProperty("notification_status")
    private Integer notificationStatus;
    @JsonProperty("data_source")
    private String dataSource;
    @JsonProperty("early_payment_date")
    private String earlyPaymentDate;
    @JsonProperty("early_payment_amount")
    private Double earlyPaymentAmount;

    @JsonProperty("recon_id")
    private String recondId;
    @JsonProperty("next_bill_fetch_date")
    private String nextBillFetchDate;

    @JsonProperty("bill_fetch_date")
    private String billFetchDate;

    @JsonProperty("old_bill_fetch_date")
    private String oldBillFetchDate;
    @JsonProperty("card_skin")
    private String cardSkin;
    @JsonProperty("card_variant")
    private String cardVariant;
    @JsonProperty("remind_later_date")
    private String remindLaterDate;
    @JsonProperty("enc_amount")
    private String encAmount;
    @JsonProperty("enc_due_date")
    private String encDueDate;
    @JsonProperty("is_encrypted")
    private Integer isEncrypted;

    @JsonProperty("consent_valid_till")
    private String consentValidTill;
    @JsonProperty("consumer_name")
    private String consumerName;
}
