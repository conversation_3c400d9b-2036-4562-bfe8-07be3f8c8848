package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
@Data
public class RPSApiRequest {

    @JsonProperty
    private List<RpsFiltersRequest> filters;
    @JsonProperty
    private List<RpsFiltersRequest> searchFilters;
    @JsonProperty("rechargeNumber")
    private String rechargeNumber;
    @JsonProperty("productId")
    private String productId;
    boolean userPlansActive;

}
