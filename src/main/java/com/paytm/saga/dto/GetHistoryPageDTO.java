package com.paytm.saga.dto;

import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

@Data
public class GetHistoryPageDTO {
	public static final Integer DEFAULT_LIMIT = 10;

	private Integer limit = DEFAULT_LIMIT;
	private String pagingState;

	private Long customerId;
	@NotNull(message = "recharge_number cannot be null")
	private String recharge_number;
	private String recharge_number_2;
	private String recharge_number_3;
	private String recharge_number_4;
	private Long productId;
	@NotNull(message = "operator cannot be null")
	private String operator;
	//@NotNull(message = "circle cannot be null")
	private String circle;
	@NotNull(message = "service cannot be null")
	private String service;
	private String[] dedupeids;
	private String themehash;
	private String payType;
	private String cin;
	private String parId;
	private boolean isNative;
}
