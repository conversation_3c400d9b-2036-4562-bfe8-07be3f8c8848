package com.paytm.saga.dto;

import java.util.Date;
import java.util.List;

public class DropOffResponse {
    private String recharge_number;
    private String recharge_number_2;
    private String recharge_number_3;
    private String recharge_number_4;
    private String recharge_number_5;
    private String recharge_number_6;
    private String recharge_number_7;
    private String recharge_number_8;
    private String category_id;
    private String service;
    private String paytype;
    private String product_id;
    private List<DropOffBillsObject> bills;
    private String type;
    private Date timestamp;
    private String consumerName;
    private DropOffResponseAdditionalData additionalData;
    private String channel;
    private String paidOutside;
    private Boolean isAutomaticActive;

    public DropOffResponse(String recharge_number, String recharge_number_2, String recharge_number_3, String recharge_number_4, String category_id, String service, String paytype, String product_id, List<DropOffBillsObject> bills, String type, Date timestamp, String consumerName, DropOffResponseAdditionalData additionalData, String channel, String paidOutside, Boolean isAutomaticActive) {
        this(recharge_number, recharge_number_2, recharge_number_3, recharge_number_4, null, null, null, null, category_id, service, paytype, product_id, bills, type, timestamp, consumerName, additionalData, channel, paidOutside, isAutomaticActive);
    }

    public DropOffResponse(String recharge_number, String recharge_number_2, String recharge_number_3, String recharge_number_4, String recharge_number_5, String recharge_number_6, String recharge_number_7, String recharge_number_8, String category_id, String service, String paytype, String product_id, List<DropOffBillsObject> bills, String type, Date timestamp, String consumerName, DropOffResponseAdditionalData additionalData, String channel, String paidOutside, Boolean isAutomaticActive) {
        this.recharge_number = recharge_number;
        this.recharge_number_2 = recharge_number_2;
        this.recharge_number_3 = recharge_number_3;
        this.recharge_number_4 = recharge_number_4;
        this.recharge_number_5 = recharge_number_5;
        this.recharge_number_6 = recharge_number_6;
        this.recharge_number_7 = recharge_number_7;
        this.recharge_number_8 = recharge_number_8;
        this.category_id = category_id;
        this.service = service;
        this.paytype = paytype;
        this.product_id = product_id;
        this.bills = bills;
        this.type = type;
        this.timestamp = timestamp;
        this.consumerName = consumerName;
        this.additionalData = additionalData;
        this.channel = channel;
        this.paidOutside=paidOutside;
        this.isAutomaticActive=isAutomaticActive;
    }

    public  DropOffResponse(){

    }

    @Override
    public String toString() {
        return "DropOffResponse{" +
                "recharge_number='" + recharge_number + '\'' +
                ", recharge_number_2='" + recharge_number_2 + '\'' +
                ", recharge_number_3='" + recharge_number_3 + '\'' +
                ", recharge_number_4='" + recharge_number_4 + '\'' +
                ", recharge_number_5='" + recharge_number_5 + '\'' +
                ", recharge_number_6='" + recharge_number_6 + '\'' +
                ", recharge_number_7='" + recharge_number_7 + '\'' +
                ", recharge_number_8='" + recharge_number_8 + '\'' +
                ", category_id='" + category_id + '\'' +
                ", service='" + service + '\'' +
                ", paytype='" + paytype + '\'' +
                ", product_id='" + product_id + '\'' +
                ", bills=" + bills +
                ", type='" + type + '\'' +
                ", timestamp=" + timestamp +
                ", consumerName='" + consumerName + '\'' +
                ", additionalData=" + additionalData +
                ", channel='" + channel + '\'' +
                ", isAutomaticActive='" + isAutomaticActive + '\'' +
                ", paidOutside='" + paidOutside + '\'' +
                '}';
    }

    public String getRecharge_number() {
        return recharge_number;
    }

    public void setRecharge_number(String recharge_number) {
        this.recharge_number = recharge_number;
    }

    public String getRecharge_number_2() {
        return recharge_number_2;
    }

    public void setRecharge_number_2(String recharge_number_2) {
        this.recharge_number_2 = recharge_number_2;
    }

    public String getRecharge_number_3() {
        return recharge_number_3;
    }

    public void setRecharge_number_3(String recharge_number_3) {
        this.recharge_number_3 = recharge_number_3;
    }

    public String getRecharge_number_4() {
        return recharge_number_4;
    }

    public void setRecharge_number_4(String recharge_number_4) {
        this.recharge_number_4 = recharge_number_4;
    }

    public String getRecharge_number_5() {
        return recharge_number_5;
    }

    public void setRecharge_number_5(String recharge_number_5) {
        this.recharge_number_5 = recharge_number_5;
    }

    public String getRecharge_number_6() {
        return recharge_number_6;
    }

    public void setRecharge_number_6(String recharge_number_6) {
        this.recharge_number_6 = recharge_number_6;
    }

    public String getRecharge_number_7() {
        return recharge_number_7;
    }

    public void setRecharge_number_7(String recharge_number_7) {
        this.recharge_number_7 = recharge_number_7;
    }

    public String getRecharge_number_8() {
        return recharge_number_8;
    }

    public void setRecharge_number_8(String recharge_number_8) {
        this.recharge_number_8 = recharge_number_8;
    }

    public String getCategory_id() {
        return category_id;
    }

    public void setCategory_id(String category_id) {
        this.category_id = category_id;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public List<DropOffBillsObject> getBills() {
        return bills;
    }

    public void setBills(List<DropOffBillsObject> bills) {
        this.bills = bills;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getConsumerName() {
        return consumerName;
    }

    public void setConsumerName(String consumerName) {
        this.consumerName = consumerName;
    }

    public DropOffResponseAdditionalData getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(DropOffResponseAdditionalData additionalData) {
        this.additionalData = additionalData;
    }

    public String getChannel() {
        return channel;
    }

    public Boolean getIsAutomaticActive() {
        return isAutomaticActive;
    }

    public void setIsAutomaticActive(Boolean isAutomaticActive) {
        this.isAutomaticActive = isAutomaticActive;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getPaidOutside() {
        return paidOutside;
    }

    public void setPaidOutside(String paidOutside) {
        this.paidOutside = paidOutside;
    }
}
