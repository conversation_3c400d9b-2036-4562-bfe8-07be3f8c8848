package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.validators.CreditCardValidate;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
@ToString
public class DeleteRequestBody {
    @NotNull
    @JsonProperty("customer_id")
    Long customerId;

    @JsonProperty("recharge_number")
    @NotNull
    String rechargeNumber;

    String paytype;

    String par;

    String cin;

    @JsonProperty("txn_amount")
    Double txnAmount;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("service")
    private String service;

    @JsonProperty("product_id")
    private Long productId;

    @JsonProperty("billerAccountId")
    Integer billerAccountId;

}
