package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class PgSavedCardBodyResponse {
    @JsonProperty("savedCardDetails")
    private List<SavedCardDetails> savedCardDetails;

    @JsonProperty("resultInfo")
    private PgResultInfoResponse resultInfo;

    public List<SavedCardDetails> getSavedCardDetails() {
        return savedCardDetails;
    }

    public void setSavedCardDetails(List<SavedCardDetails> savedCardDetails) {
        this.savedCardDetails = savedCardDetails;
    }

    public PgResultInfoResponse getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(PgResultInfoResponse resultInfo) {
        this.resultInfo = resultInfo;
    }

    @Override
    public String toString() {
        return "PgSavedCardBodyResponse{" +
                "savedCardDetails=" + savedCardDetails +
                ", resultInfo=" + resultInfo +
                '}';
    }
}
