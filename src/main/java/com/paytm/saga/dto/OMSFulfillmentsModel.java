package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OMSFulfillmentsModel {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("fulfillment_response")
    private String fulfillment_response;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFulfillment_response() {
        return fulfillment_response;
    }

    public void setFulfillment_response(String fulfillment_response) {
        this.fulfillment_response = fulfillment_response;
    }

    @Override
    public String toString() {
        return "OMSFulfillmentsModel{" +
                "id=" + id +
                ", fulfillment_response='" + fulfillment_response + '\'' +
                '}';
    }
}
