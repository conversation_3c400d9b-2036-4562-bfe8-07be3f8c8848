package com.paytm.saga.dto.plans;

import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.model.ActiveInactiveMap;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Scope("singleton")
public class PlansMapCache {
    private Map<String, String> plansMap;

    private static PlansMapCache plansMapCache;

    private PlansMapCache() {
        plansMap = new HashMap<>();
    }

    public static PlansMapCache getInstance() {
        if (plansMapCache == null) {
            plansMapCache = new PlansMapCache();
        }
        return plansMapCache;
    }
    public void addPlansDetail(String key,String value) {
        plansMap.put(key,value);
    }

    public String getPlanName(String plansKey) {
        return plansMap.get(plansKey);
    }

    public void clearPlansMapCache() {
        plansMap.clear();
    }

}
