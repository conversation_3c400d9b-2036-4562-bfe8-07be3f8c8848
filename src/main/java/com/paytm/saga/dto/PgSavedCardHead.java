package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PgSavedCardHead {
    @JsonProperty("tokenType")
    private String tokenType;
    @JsonProperty("token")
    private String token;
    @JsonProperty("clientId")
    private String clientId;

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    @Override
    public String toString() {
        return "PgSavedCardV2Head{" +
                "tokenType='" + tokenType + '\'' +
                ", token='" + token + '\'' +
                ", clientId='" + clientId + '\'' +
                '}';
    }
}

