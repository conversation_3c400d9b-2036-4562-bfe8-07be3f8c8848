package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class RecentCRUDResponse {
    @JsonProperty("status_code")
    private String status_code;

    @JsonProperty("data")
    private RecentCRUDResponseData data;

    public String getStatus_code() {
        return status_code;
    }

    public void setStatus_code(String status_code) {
        this.status_code = status_code;
    }

    public RecentCRUDResponseData getData() {
        return data;
    }

    public void setData(RecentCRUDResponseData data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "RecentCRUDResponse{" +
                "status_code='" + status_code + '\'' +
                ", data=" + data +
                '}';
    }
}
