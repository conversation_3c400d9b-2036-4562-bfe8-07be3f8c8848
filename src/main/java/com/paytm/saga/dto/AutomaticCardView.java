package com.paytm.saga.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(value = { "previousTxnHistory","billState","planExpiring","operatorLabel","bookingId","ivrsBooking","previousDate", "invalidPlan", "eventDate", "planExpiryDays", "planDetail", "mnp",
        "lastCard" })
public class AutomaticCardView {
    //	private String dateTime;
    private String themeType;
    private List<ViewElementInfo> headings;
    private List<ViewElementInfo> cta;


    public AutomaticCardView() {

    }
    public AutomaticCardView(String themeType, List<ViewElementInfo> headings, List<ViewElementInfo> cta) {
        this.themeType = themeType;
        this.headings = headings;
        this.cta = cta;
    }

    public String getThemeType() {
        return themeType;
    }

    public void setThemeType(String themeType) {
        this.themeType = themeType;
    }

    public List<ViewElementInfo> getHeadings() {
        return headings;
    }

    public void setHeadings(List<ViewElementInfo> headings) {
        this.headings = headings;
    }

    public List<ViewElementInfo> getCta() {
        return cta;
    }

    public void setCta(List<ViewElementInfo> cta) {
        this.cta = cta;
    }
}