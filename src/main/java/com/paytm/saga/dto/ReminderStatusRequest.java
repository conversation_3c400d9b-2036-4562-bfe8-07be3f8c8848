package com.paytm.saga.dto;

import jakarta.validation.constraints.NotNull;

import com.paytm.saga.custom.validator.CircleValidationReminder;

@CircleValidationReminder
public class ReminderStatusRequest{
    @NotNull(message="recharge number can't be null")
    private String rechargeNumber;

    @NotNull(message="operator can't be null")
    private String operator;

    @NotNull(message="customerId can't be null")
    private Long customerId;

    private String referenceId;

    @NotNull(message="paytype can't be null")
    private String paytype;

    @NotNull(message="service can't be null")
    private String service;

    @NotNull(message="service can't be null")
    private Long productId;

    private String planBucket;

    private Double amount;

    private String circle;

    private String expiry;

    private String bill_date;

    private String due_date;


    public String getRechargeNumber() {
        return rechargeNumber;
    }

    public void setRechargeNumber(String rechargeNumber) {
        this.rechargeNumber = rechargeNumber;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getPlanBucket() {
        return planBucket;
    }

    public void setPlanBucket(String planBucket) {
        this.planBucket = planBucket;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getCircle() {
        return circle;
    }

    public void setCircle(String circle) {
        this.circle = circle;
    }

    public String getExpiry() {
        return expiry;
    }

    public void setExpiry(String expiry) {
        this.expiry = expiry;
    }

    public String getBill_date() {
        return bill_date;
    }

    public void setBill_date(String bill_date) {
        this.bill_date = bill_date;
    }

    public String getDue_date() {
        return due_date;
    }

    public void setDue_date(String due_date) {
        this.due_date = due_date;
    }

    @Override
    public String toString() {
        return "MarkAsPaidRequest{" +
                "rechargeNumber='" + rechargeNumber + '\'' +
                ", operator='" + operator + '\'' +
                ", customerId=" + customerId +
                ", referenceId='" + referenceId + '\'' +
                ", paytype='" + paytype + '\'' +
                ", service='" + service + '\'' +
                ", productId=" + productId +
                ", planBucket='" + planBucket + '\'' +
                ", amount=" + amount +
                ", circle='" + circle + '\'' +
                ", expiry='" + expiry + '\'' +
                ", bill_date='" + bill_date + '\'' +
                ", due_date='" + due_date + '\'' +
                '}';
    }
}
