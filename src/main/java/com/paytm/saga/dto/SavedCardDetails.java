package com.paytm.saga.dto;

import java.util.ArrayList;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SavedCardDetails {
    @JsonProperty("savedCardId")
    private String savedCardId;
    @JsonProperty("cardScheme")
    private String cardScheme;
    @JsonProperty("expiryDate")
    private String expiryDate;
    @JsonProperty("issuingBankName")
    private String issuingBankName;
    @JsonProperty("issuingBankCardVariant")
    private String issuingBankCardVariant;
    @JsonProperty("cardSuffix")
    private String cardSuffix;
    @JsonProperty("cardType")
    private String cardType;
    @JsonProperty("displayName")
    private String displayName;
    @JsonProperty("isCardCoft")
    private boolean isCardCoft;
    @JsonProperty("panUniqueReference")
    private String panUniqueReference;
    @JsonProperty("tokenBin")
    private String tokenBin;
    @JsonProperty("tokenStatus")
    private String tokenStatus;
    @JsonProperty("cardFirstSixDigits")
    private String cardFirstSixDigits;
    @JsonProperty("issuerCode")
    private String issuerCode;
    @JsonProperty("isEligibleForCoft")
    private boolean isEligibleForCoft;

    @JsonProperty("issuerMetadata")
    private Map<Object, Object> issuerMetadata;
    @JsonProperty("colors")
    private ArrayList<Object> colors;
    @JsonProperty("mediaAssets")
    private ArrayList<Object> mediaAssets;

    public ArrayList<Object> getColors() {
        return colors;
    }

    public void setColors(ArrayList<Object> colors) {
        this.colors = colors;
    }

    public ArrayList<Object> getMediaAssets() {
        return mediaAssets;
    }

    public void setMediaAssets(ArrayList<Object> mediaAssets) {
        this.mediaAssets = mediaAssets;
    }

    public String getSavedCardId() {
        return savedCardId;
    }

    public void setSavedCardId(String savedCardId) {
        this.savedCardId = savedCardId;
    }

    public String getCardScheme() {
        return cardScheme;
    }

    public void setCardScheme(String cardScheme) {
        this.cardScheme = cardScheme;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getIssuingBankName() {
        return issuingBankName;
    }

    public void setIssuingBankName(String issuingBankName) {
        this.issuingBankName = issuingBankName;
    }

    public String getIssuingBankCardVariant() {
        return issuingBankCardVariant;
    }

    public void setIssuingBankCardVariant(String issuingBankCardVariant) {
        this.issuingBankCardVariant = issuingBankCardVariant;
    }

    public String getCardSuffix() {
        return cardSuffix;
    }

    public void setCardSuffix(String cardSuffix) {
        this.cardSuffix = cardSuffix;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public boolean isCardCoft() {
        return isCardCoft;
    }

    public void setCardCoft(boolean cardCoft) {
        isCardCoft = cardCoft;
    }

    public String getPanUniqueReference() {
        return panUniqueReference;
    }

    public void setPanUniqueReference(String panUniqueReference) {
        this.panUniqueReference = panUniqueReference;
    }

    public String getTokenBin() {
        return tokenBin;
    }

    public void setTokenBin(String tokenBin) {
        this.tokenBin = tokenBin;
    }

    public String getTokenStatus() {
        return tokenStatus;
    }

    public void setTokenStatus(String tokenStatus) {
        this.tokenStatus = tokenStatus;
    }

    public String getCardFirstSixDigits() {
        return cardFirstSixDigits;
    }

    public void setCardFirstSixDigits(String cardFirstSixDigits) {
        this.cardFirstSixDigits = cardFirstSixDigits;
    }

    public String getIssuerCode() {
        return issuerCode;
    }

    public void setIssuerCode(String issuerCode) {
        this.issuerCode = issuerCode;
    }

    public boolean isEligibleForCoft() {
        return isEligibleForCoft;
    }

    public void setEligibleForCoft(boolean eligibleForCoft) {
        isEligibleForCoft = eligibleForCoft;
    }

    @Override
    public String toString() {
        return "SavedCardV2Details{" +
                "savedCardId='" + savedCardId + '\'' +
                ", cardScheme='" + cardScheme + '\'' +
                ", expiryDate='" + expiryDate + '\'' +
                ", issuingBankName='" + issuingBankName + '\'' +
                ", issuingBankCardVariant='" + issuingBankCardVariant + '\'' +
                ", cardSuffix='" + cardSuffix + '\'' +
                ", cardType='" + cardType + '\'' +
                ", displayName='" + displayName + '\'' +
                ", isCardCoft=" + isCardCoft +
                ", panUniqueReference='" + panUniqueReference + '\'' +
                ", tokenBin='" + tokenBin + '\'' +
                ", tokenStatus='" + tokenStatus + '\'' +
                ", cardFirstSixDigits='" + cardFirstSixDigits + '\'' +
                ", issuerCode='" + issuerCode + '\'' +
                ", isEligibleForCoft='" + isEligibleForCoft + '\'' +
                '}';
    }
}
