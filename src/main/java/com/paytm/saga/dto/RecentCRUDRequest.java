package com.paytm.saga.dto;

import java.util.ArrayList;
import java.util.List;

public class RecentCRUDRequest {

    private String recharge_number;
    private String operator;
    private Long customer_id;
    private String reference_id;
    private String paytype;
    private String service;
    private List<RecentCRUDRequestData> data;


    public RecentCRUDRequest(String recharge_number, String operator, Long customer_id, String reference_id, String paytype, String service, String fieldName, Long amount, String due_date, String bill_date, String mark_as_paid_time, String expiry, String plan_bucket) {
        this.recharge_number = recharge_number;
        this.operator = operator;
        this.customer_id = customer_id;
        this.reference_id = reference_id;
        this.paytype = paytype;
        this.service = service;
        RecentCRUDRequestData value = new RecentCRUDRequestData(fieldName, amount, due_date, bill_date, mark_as_paid_time, expiry, plan_bucket);
        ArrayList<RecentCRUDRequestData> list=new ArrayList<RecentCRUDRequestData>();
        list.add(value);
        this.data = list;
    }

    public String getRecharge_number() {
        return recharge_number;
    }

    public void setRecharge_number(String recharge_number) {
        this.recharge_number = recharge_number;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getCustomer_id() {
        return customer_id;
    }

    public void setCustomer_id(Long customer_id) {
        this.customer_id = customer_id;
    }

    public String getReference_id() {
        return reference_id;
    }

    public void setReference_id(String reference_id) {
        this.reference_id = reference_id;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public List<RecentCRUDRequestData> getData() {
        return data;
    }

    public void setData(List<RecentCRUDRequestData> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "RecentCRUDRequest{" +
                "recharge_number='" + recharge_number + '\'' +
                ", operator='" + operator + '\'' +
                ", customer_id=" + customer_id +
                ", reference_id='" + reference_id + '\'' +
                ", paytype='" + paytype + '\'' +
                ", service='" + service + '\'' +
                ", data=" + data +
                '}';
    }
}
