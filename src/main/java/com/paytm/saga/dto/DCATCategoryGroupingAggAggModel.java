package com.paytm.saga.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DCATCategoryGroupingAggAggModel {
	@JsonProperty("value")
	private String value;
	@JsonProperty("displayValue")
	private String displayValue;
	@JsonProperty("productList")
	private List<DCATCategoryGroupingAggAggProduct> productList;

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getDisplayValue() {
		return displayValue;
	}

	public void setDisplayValue(String displayValue) {
		this.displayValue = displayValue;
	}

	public List<DCATCategoryGroupingAggAggProduct> getProductList() {
		return productList;
	}

	public void setProductList(List<DCATCategoryGroupingAggAggProduct> productList) {
		this.productList = productList;
	}

}
