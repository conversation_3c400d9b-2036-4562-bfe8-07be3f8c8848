package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PgSavedCardBody {
    @JsonProperty("userId")
    private String userId;
    @JsonProperty("filterTokenCards")
    private Boolean filterTokenCards;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

//    public Boolean getIsCardIndexNumberRequired() {
//        return filterTokenCards;
//    }
//
//    public void setIsCardIndexNumberRequired(Boolean filterTokenCards) {
//        this.filterTokenCards = filterTokenCards;
//    }


	public Boolean getFilterTokenCards() {
		return filterTokenCards;
	}

	public void setFilterTokenCards(Boolean filterTokenCards) {
		this.filterTokenCards = filterTokenCards;
	}

	@Override
    public String toString() {
        return "PgSavedCardV2Body{" +
                "userId='" + userId + '\'' +
                ", filterTokenCards=" + filterTokenCards +
                '}';
    }
}
