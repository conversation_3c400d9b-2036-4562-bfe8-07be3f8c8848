package com.paytm.saga.dto;

public class SavedCardBillsObj {
    private String expiry;
    private SavedCardBillStateEnum billState;
    private String bill_date;
    private String due_date;
    private Double amount;
    private Double original_due_amount;
    private Double min_due_amount;
    private Double original_min_due_amount;
    private Long order_id;
    private String order_date;
    private Double last_paid_amount;
    private String mark_as_paid_time;

    public SavedCardBillsObj() {
    }

    public String getExpiry() {
        return expiry;
    }

    public void setExpiry(String expiry) {
        this.expiry = expiry;
    }

    public SavedCardBillStateEnum getBillState() {
        return billState;
    }

    public void setBillState(SavedCardBillStateEnum billState) {
        this.billState = billState;
    }

    public String getBill_date() {
        return bill_date;
    }

    public void setBill_date(String bill_date) {
        this.bill_date = bill_date;
    }

    public String getDue_date() {
        return due_date;
    }

    public void setDue_date(String due_date) {
        this.due_date = due_date;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getOriginal_due_amount() {
        return original_due_amount;
    }

    public void setOriginal_due_amount(Double original_due_amount) {
        this.original_due_amount = original_due_amount;
    }

    public Double getMin_due_amount() {
        return min_due_amount;
    }

    public void setMin_due_amount(Double min_due_amount) {
        this.min_due_amount = min_due_amount;
    }

    public Double getOriginal_min_due_amount() {
        return original_min_due_amount;
    }

    public void setOriginal_min_due_amount(Double original_min_due_amount) {
        this.original_min_due_amount = original_min_due_amount;
    }

    public Long getOrder_id() {
        return order_id;
    }

    public void setOrder_id(Long order_id) {
        this.order_id = order_id;
    }

    public String getOrder_date() {
        return order_date;
    }

    public void setOrder_date(String order_date) {
        this.order_date = order_date;
    }

    public Double getLast_paid_amount() {
        return last_paid_amount;
    }

    public void setLast_paid_amount(Double last_paid_amount) {
        this.last_paid_amount = last_paid_amount;
    }

    public String getMark_as_paid_time() {
        return mark_as_paid_time;
    }

    public void setMark_as_paid_time(String mark_as_paid_time) {
        this.mark_as_paid_time = mark_as_paid_time;
    }

    public SavedCardBillsObj(String expiry, SavedCardBillStateEnum billState, String bill_date, String due_date, Double amount, Double original_due_amount, Double min_due_amount, Double original_min_due_amount, Long order_id, String order_date, Double last_paid_amount, String mark_as_paid_time) {
        this.expiry = expiry;
        this.billState = billState;
        this.bill_date = bill_date;
        this.due_date = due_date;
        this.amount = amount;
        this.original_due_amount = original_due_amount;
        this.min_due_amount = min_due_amount;
        this.original_min_due_amount = original_min_due_amount;
        this.order_id = order_id;
        this.order_date = order_date;
        this.last_paid_amount = last_paid_amount;
        this.mark_as_paid_time = mark_as_paid_time;
    }
}

