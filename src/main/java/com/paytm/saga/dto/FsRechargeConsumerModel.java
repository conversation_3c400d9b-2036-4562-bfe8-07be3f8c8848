package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class FsRechargeConsumerModel {

    @JsonProperty("customerInfo_customer_id")
    private Long customerId;

    @JsonProperty("userData_recharge_number")
    private String recharge_number;

    @JsonProperty("userData_recharge_number_2")
    private String recharge_number_2;

    @JsonProperty("userData_recharge_number_3")
    private String recharge_number_3;

    @JsonProperty("userData_recharge_number_4")
    private String recharge_number_4;

    @JsonProperty("userData_amount")
    private String amount;

    @JsonProperty("productInfo_service")
    private String service;

    @JsonProperty("productInfo_paytype")
    private String paytype;

    @JsonProperty("productInfo_operator")
    private String operator;

    @JsonProperty("productInfo_circle")
    private String circle;

    @JsonProperty("productInfo_categoryId")
    private Long categoryId;

    @JsonProperty("productInfo_verticalId")
    private Integer verticalId;

    @JsonProperty("orderInfo_order_id")
    private Long orderId;

    @JsonProperty("orderInfo_item_id")
    private Long itemId;

    @JsonProperty("orderInfo_merchant_id")
    private Long merchantId;

    @JsonProperty("inStatusMap_responseCode")
    private String inResponseCode;

    @JsonProperty("recentData")
    private Map<String, Object> recentData;

    @JsonProperty("metaData")
    private String metaData;

    @JsonProperty("catalogProductID")
    private Long catalogProductID;

    @JsonProperty("originalPid")
    private Long originalPid;

    private String planBucket;

    @JsonProperty(value = "timestamps_init")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private Date txnInit;

}
