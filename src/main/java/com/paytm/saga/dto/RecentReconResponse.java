package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class RecentReconResponse {

    @JsonProperty("key")
    private String key;

    @JsonProperty("operation_type")
    private String operationType;

    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("oms_recharge_number")
    private String omsRechargeNumber;

    @JsonProperty("recent_recharge_number")
    private String recentRechargeNumber;

    @JsonProperty("oms_customer_id")
    private String omsCustomerId;

    @JsonProperty("recent_customer_is")
    private String recentCustomerId;

    @JsonProperty("oms_mcn")
    private String omsMcn;

    @JsonProperty("recent_mcn")
    private String recentMcn;

    @JsonProperty("recent_operator")
    private String recentOperator;

    @JsonProperty("oms_operator")
    private String omsOperator;

    @JsonProperty("errors")
    private List<String> errors;

    public RecentReconResponse() {

    }

    public RecentReconResponse(String key, String operationType, Long orderId, String omsRechargeNumber, String recentRechargeNumber, String omsCustomerId, String recentCustomerId) {
        this.key = key;
        this.operationType = operationType;
        this.orderId = orderId;
        this.omsRechargeNumber = omsRechargeNumber;
        this.recentRechargeNumber = recentRechargeNumber;
        this.omsCustomerId = omsCustomerId;
        this.recentCustomerId = recentCustomerId;

    }
}
