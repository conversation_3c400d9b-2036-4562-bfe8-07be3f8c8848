package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class PrepaidBillsListenerDataModel {
    @JsonProperty("RECHARGE_NUMBER")
    private String recharge_number;

    @JsonProperty("CUSTOMER_ID")
    private Long customer_id;

    @JsonProperty("SERVICE")
    private String service;

    @JsonProperty("PAYTYPE")
    private String paytype;

    @JsonProperty("CUSTOMER_MOBILE")
    private String customerMobile;

    @JsonProperty("CUSTOMER_EMAIL")
    private String customerEmail;

    @JsonProperty("OPERATOR")
    private String operator;

    @JsonProperty("CIRCLE")
    private String circle;

    @JsonProperty("AMOUNT")
    private Double amount;

    @JsonProperty("PRODUCT_ID")
    private Long productId;

    @JsonProperty("BILL_DATE")
    private String billDate;

    @JsonProperty("DUE_DATE")
    private String dueDate;

    @JsonProperty("BILL_FETCH_DATE")
    private String billFetchDate;

    @JsonProperty("NEXT_BILL_FETCH_DATE")
    private String nextBillFetchDate;

    @JsonProperty("STATUS")
    private Integer status;

    @JsonProperty("IS_AUTOMATIC")
    private Integer isAutomatic;

    @JsonProperty("META_DATA")
    private Map<String,Object> metaData;

    @JsonProperty("CREATED_AT")
    private String created_at;

    @JsonProperty("UPDATED_AT")
    private String updated_at;


}
