package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

public class OMSItemMetaModel {
    @JsonProperty("plan_bucket")
    private String plan_bucket;

    @JsonProperty("data")
    private String data;

    @JsonProperty("validity")
    private String validity;

    @JsonProperty("talktime")
    private String talktime;

    @JsonProperty("sms")
    private String sms;

    @JsonProperty("couponDescription")
    private String description;

    @JsonProperty("addon_benefit1")
    private String addon_benefit1;

    @JsonProperty("addon_benefit2")
    private String addon_benefit2;

    @JsonProperty("addon_benefit3")
    private String addon_benefit3;

    @JsonProperty("addon_benefit4")
    private String addon_benefit4;

    @JsonProperty("addon_benefit")
    private List<String> addon_benefit;

    @JsonProperty("plan_id")
    private String plan_id;

    @JsonProperty("additionalData")
    private OMSItemMetaAdditionalData additionalData;

    @JsonProperty("recharge_benefits")
    private OMSItemMetaBenefitsModel recharge_benefits;
    
    @JsonProperty("cin")
    private String cin;

    @JsonProperty("panUniqueReference")
    private String par;
    
    @JsonProperty("cylinder_agency_name")
    private String cylinder_agency_name;

    @JsonProperty("RMN")
    private String RMN;

    @JsonProperty("key")
    private List<OMSItemMetaKeyObject> key;

    @JsonProperty("isRetryExhausted")
    private boolean isRetryExhausted;

    @Override
    public String toString() {
        return "OMSItemMetaModel{" +
                "plan_bucket='" + plan_bucket + '\'' +
                ", data='" + data + '\'' +
                ", validity='" + validity + '\'' +
                ", talktime='" + talktime + '\'' +
                ", sms='" + sms + '\'' +
                ", description='" + description + '\'' +
                ", addon_benefit1='" + addon_benefit1 + '\'' +
                ", addon_benefit2='" + addon_benefit2 + '\'' +
                ", addon_benefit3='" + addon_benefit3 + '\'' +
                ", addon_benefit4='" + addon_benefit4 + '\'' +
                ", addon_benefit=" + addon_benefit +
                ", plan_id='" + plan_id + '\'' +
                ", additionalData=" + additionalData +
                ", recharge_benefits=" + recharge_benefits +
                ", RMN='" + RMN + '\'' +
                ", key=" + key +
                '}';
    }

    public String getPlan_bucket() {
        return plan_bucket;
    }

    public void setPlan_bucket(String plan_bucket) {
        this.plan_bucket = plan_bucket;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getValidity() {
        return validity;
    }

    public void setValidity(String validity) {
        this.validity = validity;
    }

    public String getTalktime() {
        return talktime;
    }

    public void setTalktime(String talktime) {
        this.talktime = talktime;
    }

    public String getSms() {
        return sms;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAddon_benefit1() {
        return addon_benefit1;
    }

    public void setAddon_benefit1(String addon_benefit1) {
        this.addon_benefit1 = addon_benefit1;
    }

    public String getAddon_benefit2() {
        return addon_benefit2;
    }

    public void setAddon_benefit2(String addon_benefit2) {
        this.addon_benefit2 = addon_benefit2;
    }

    public String getAddon_benefit3() {
        return addon_benefit3;
    }

    public void setAddon_benefit3(String addon_benefit3) {
        this.addon_benefit3 = addon_benefit3;
    }

    public String getAddon_benefit4() {
        return addon_benefit4;
    }

    public void setAddon_benefit4(String addon_benefit4) {
        this.addon_benefit4 = addon_benefit4;
    }

    public String getPlan_id() {
        return plan_id;
    }

    public void setPlan_id(String plan_id) {
        this.plan_id = plan_id;
    }

    public OMSItemMetaBenefitsModel getRecharge_benefits() {
        return recharge_benefits;
    }

    public void setRecharge_benefits(OMSItemMetaBenefitsModel recharge_benefits) {
        this.recharge_benefits = recharge_benefits;
    }

    public List<String> getAddon_benefit() {
        return addon_benefit;
    }

    public void setAddon_benefit(List<String> addon_benefit) {
        this.addon_benefit = addon_benefit;
    }

    public OMSItemMetaAdditionalData getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(OMSItemMetaAdditionalData additionalData) {
        this.additionalData = additionalData;
    }

	public String getCin() {
		return cin;
	}

	public void setCin(String cin) {
		this.cin = cin;
	}

    public String getPar() { return par; }

    public void setPar(String par) { this.par = par; }

    public String getRMN() {
        return RMN;
    }

    public void setRMN(String RMN) {
        this.RMN = RMN;
    }

    public List<OMSItemMetaKeyObject> getKey() {
        return key;
    }

    public void setKey(List<OMSItemMetaKeyObject> key) {
        this.key = key;
    }

    public String getCylinder_agency_name() {
		return cylinder_agency_name;
	}

	public void setCylinder_agency_name(String cylinder_agency_name) {
		this.cylinder_agency_name = cylinder_agency_name;
	}

    public boolean getIsRetryExhausted() {
        return isRetryExhausted;
    }

    public void setIsRetryExhausted(boolean retryExhausted) {
        this.isRetryExhausted = retryExhausted;
    }

	public OMSItemMetaModel() {

    }

    public OMSItemMetaModel(String plan_bucket, String data, String validity, String talktime, String sms, String description, String addon_benefit1, String addon_benefit2, String addon_benefit3, String addon_benefit4, List<String> addon_benefit, String plan_id, OMSItemMetaAdditionalData additionalData, OMSItemMetaBenefitsModel recharge_benefits, String RMN, ArrayList<OMSItemMetaKeyObject> key1) {
        this.plan_bucket = plan_bucket;
        this.data = data;
        this.validity = validity;
        this.talktime = talktime;
        this.sms = sms;
        this.description = description;
        this.addon_benefit1 = addon_benefit1;
        this.addon_benefit2 = addon_benefit2;
        this.addon_benefit3 = addon_benefit3;
        this.addon_benefit4 = addon_benefit4;
        this.addon_benefit = addon_benefit;
        this.plan_id = plan_id;
        this.additionalData = additionalData;
        this.recharge_benefits = recharge_benefits;
        this.RMN = RMN;
        this.key = key;
    }
}
