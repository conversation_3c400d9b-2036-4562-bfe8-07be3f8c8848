package com.paytm.saga.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SmsCard {
	private long customer_id;
	private String recharge_number;
	private long product_id;
	private String operator;
	private double amount;

	private Double due_amount;

	private Date due_date;
	private Date bill_fetch_date;
	private String paytype;
	private String service;
	private String circle;
	private String customer_mobile;
	private String customer_email;
	private int status;
	private Date createdAt;
	private Date updatedAt;
	@JsonProperty("bill_date")
	private Date billDate;
	private int notification_status;
	private String card_network;
	private String bank_name;
	private String customer_other_info;
	private CustomerOtherInfo customerOtherInfo;
	private String extra;
	private ExtraInfo extra_info;

	public String getExtra() {
		return extra;
	}

	public void setExtra(String extra) {
		this.extra = extra;
	}

	public ExtraInfo getExtra_info() {
		return extra_info;
	}

	public void setExtra_info(ExtraInfo extra_info) {
		this.extra_info = extra_info;
	}

	public long getCustomer_id() {
		return customer_id;
	}

	public void setCustomer_id(long customer_id) {
		this.customer_id = customer_id;
	}

	public String getRecharge_number() {
		return recharge_number;
	}

	public void setRecharge_number(String recharge_number) {
		this.recharge_number = recharge_number;
	}

	public long getProduct_id() {
		return product_id;
	}

	public void setProduct_id(long product_id) {
		this.product_id = product_id;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public double getAmount() {
		return amount;
	}

	public void setAmount(double amount) {
		this.amount = amount;
	}

	public Double getDue_amount() {
		return due_amount;
	}

	public void setDue_amount(Double due_amount) {
		this.due_amount = due_amount;
	}

	public Date getDue_date() {
		return due_date;
	}

	public void setDue_date(Date due_date) {
		this.due_date = due_date;
	}

	public Date getBill_fetch_date() {
		return bill_fetch_date;
	}

	public void setBill_fetch_date(Date bill_fetch_date) {
		this.bill_fetch_date = bill_fetch_date;
	}

	public String getPaytype() {
		return paytype;
	}

	public void setPaytype(String paytype) {
		this.paytype = paytype;
	}

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getCircle() {
		return circle;
	}

	public void setCircle(String circle) {
		this.circle = circle;
	}

	public String getCustomer_mobile() {
		return customer_mobile;
	}

	public void setCustomer_mobile(String customer_mobile) {
		this.customer_mobile = customer_mobile;
	}

	public String getCustomer_email() {
		return customer_email;
	}

	public void setCustomer_email(String customer_email) {
		this.customer_email = customer_email;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Date getBillDate() {
		return billDate;
	}

	public void setBillDate(Date billDate) {
		this.billDate = billDate;
	}

	public int getNotification_status() {
		return notification_status;
	}

	public void setNotification_status(int notification_status) {
		this.notification_status = notification_status;
	}

	public String getCard_network() {
		return card_network;
	}

	public void setCard_network(String card_network) {
		this.card_network = card_network;
	}

	public String getBank_name() {
		return bank_name;
	}

	public void setBank_name(String bank_name) {
		this.bank_name = bank_name;
	}
	
	public CustomerOtherInfo getCustomerOtherInfo() {
		return customerOtherInfo;
	}

	public void setCustomerOtherInfo(CustomerOtherInfo customerOtherInfo) {
		this.customerOtherInfo = customerOtherInfo;
	}

	public String getCustomer_other_info() {
		return customer_other_info;
	}

	public void setCustomer_other_info(String customer_other_info) {
		this.customer_other_info = customer_other_info;
	}

	@Override
	public String toString() {
		return "SmsCard{" +
				"customer_id=" + customer_id +
				", recharge_number='" + recharge_number + '\'' +
				", product_id=" + product_id +
				", operator='" + operator + '\'' +
				", amount=" + amount +
				", due_amount=" + due_amount +
				", due_date=" + due_date +
				", bill_fetch_date=" + bill_fetch_date +
				", paytype='" + paytype + '\'' +
				", service='" + service + '\'' +
				", circle='" + circle + '\'' +
				", customer_mobile='" + customer_mobile + '\'' +
				", customer_email='" + customer_email + '\'' +
				", status=" + status +
				", createdAt=" + createdAt +
				", updatedAt=" + updatedAt +
				", billDate=" + billDate +
				", notification_status=" + notification_status +
				", card_network='" + card_network + '\'' +
				", bank_name='" + bank_name + '\'' +
				", customer_other_info='" + customer_other_info + '\'' +
				", extra='" + extra + '\'' +
				", customerOtherInfo=" + customerOtherInfo +
				", extra_info=" + extra_info +
				'}';
	}
}
