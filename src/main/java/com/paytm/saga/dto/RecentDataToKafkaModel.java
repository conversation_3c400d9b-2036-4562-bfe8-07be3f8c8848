package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
@Data
@ToString
public class RecentDataToKafkaModel {
    @JsonProperty("mcn")
    private String mcn;

    @JsonProperty("is_tokenized_transaction")
    private Boolean isTokenizedTransaction;

    @JsonProperty("bill_date")
    private Date billDate;

    @JsonProperty("due_date")
    private Date dueDate;

    @JsonProperty("due_amount")
    private Double dueAmount;

    @JsonProperty("min_due_amount")
    private Double minDueAmount;

    @JsonProperty("original_due_amount")
    private Double originalDueAmount;

    @JsonProperty("original_min_due_amount")
    private Double originalMinDueAmount;

    @JsonProperty("current_outstanding_amount")
    private Double currentOutstandingAmount;

    @JsonProperty("nick_name_v2")
    private String nickName;

    @JsonProperty("channel_id")
    private String channelId;

    @JsonProperty("consumername_v2")
    private String consumerName;

    @JsonProperty("cylinder_agency_name_v2")
    private String cylinderAgencyName;

    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("product_id")
    private Long productId;

    @JsonProperty("txn_amount")
    private Double txnAmount;

    @JsonProperty("txn_time")
    private Date txnTime;

    @JsonProperty("txn_status")
    private String txnStatus;

    @JsonProperty("automatic_date")
    private Date automaticDate;

    @JsonProperty("automatic_status")
    private Integer automaticStatus;

    @JsonProperty("recharge_number_2")
    private String rechargeNumber2;

    @JsonProperty("recharge_number_3")
    private String rechargeNumber3;

    @JsonProperty("recharge_number_4")
    private String rechargeNumber4;

    @JsonProperty("recharge_number_5")
    private String rechargeNumber5;

    @JsonProperty("recharge_number_6")
    private String rechargeNumber6;

    @JsonProperty("recharge_number_7")
    private String rechargeNumber7;

    @JsonProperty("recharge_number_8")
    private String rechargeNumber8;

    @JsonProperty("bill_update_time")
    private Date billUpdateTime;

    @JsonProperty("is_mark_as_paid")
    private Boolean isMarkAsPaid;

    @JsonProperty("mark_as_paid_time")
    private Date markAsPaidTime;

    private String cin;

    @JsonProperty
    private String par;

    @JsonProperty
    private String tin;

    @JsonProperty("extra_info")
    private String extra;

    @JsonProperty("updated_at")
    private Date updatedAt;

    @JsonProperty("paytype")
    private String payType;

    @JsonProperty("circle")
    private String circle;

    @JsonProperty("recon_id")
    private String reconId;

    @JsonProperty("not_paid_on_paytm")
    private Integer notPaidOnPaytm;

    @JsonProperty("notification_status")
    private Integer notificationStatus;

    @JsonProperty("plan_name")
    private String planName;

    @JsonProperty("mark_as_paid_amount")
    private Double markAsPaidAmount;

    @JsonProperty("new_bill_updated_at")
    private Date newBillUpdatedAt;

    @JsonProperty("is_saved_card")
    private Boolean isSavedCard;

    @JsonProperty("is_validation")
    private Boolean isValidation;

    @JsonProperty("is_transaction")
    private Boolean isTransaction;

    @JsonProperty("event_source")
    private String eventSource;

    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("is_sms_parsed")
    private Boolean isSmsParsed;

    @JsonProperty("is_new_biller")
    private Boolean isNewBiller;

    @JsonProperty("rent_tf_data")
    private String rentTFData;

    @JsonProperty("early_payment_amount")
    private Double earlyPaymentAmount;

    @JsonProperty("early_payment_date")
    private Date earlyPaymentDate;

    @JsonProperty("customerid")
    private Long customerId;

    @JsonProperty("service")
    private String service;

    @JsonProperty("recharge_number")
    private String rechargeNumber;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("plan_bucket")
    private String planBucket;

    @JsonProperty("card_variant")
    private String cardVariant;

    @JsonProperty("card_skin")
    private String cardSkin;

    @JsonProperty("card_insurance")
    private String insuranceCard;

    @JsonProperty("recent_updated_at")
    private Date recentUpdatedAt;
    @JsonProperty("remind_later_date")
    private Date remindLaterDate;

    @JsonProperty("mark_as_paid_source")
    private String markAsPaidSource;

    @JsonProperty("enc_due_amount")
    private String encDueAmount;

    @JsonProperty("enc_min_due_amount")
    private String encMinDueAmount;

    @JsonProperty("enc_due_date")
    private String encDueDate;

    @JsonProperty("enc_original_due_amount")
    private String encOriginalDueAmount;

    @JsonProperty("enc_original_min_due_amount")
    private String encOriginalMinDueAmount;

    @JsonProperty("enc_current_outstanding_amount")
    private String encCurrentOutstandingAmount;

    @JsonProperty("is_encrypted")
    private Integer isEncrypted;

    @JsonProperty("consent_valid_till_date")
    private Date consentValidTill;

    @JsonProperty("op")
    private String op;

}
