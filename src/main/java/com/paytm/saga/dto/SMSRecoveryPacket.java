package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@ToString
@Data
@Getter
@Setter
public class SMSRecoveryPacket {
    private Long customerId;
    private String rechargeNumber;
    private Long productId;
    private String operator;
    private Double amount;
    @JsonProperty("bill_fetch_date")
    private String billFetchDate;
    private String payType;
    private String service;
    private String circle;
    @JsonProperty("customer_mobile")
    private String customerMobile;
    @JsonProperty("customer_email")
    private String customerEmail;
    private Integer status;
    private String userData;
    private String createdAt;
    @JsonProperty("updateAt")
    private String updatedAt;
    private String billDate;
    private String extra;
    private Integer notificationStatus;
    private String dueDate;
    private String customerOtherInfo;
    private String cardNetwork;
    private String dbEvent;
    private String debugKey;
    private String additionalInfo;
    private String bankName;
    private String cdcEventType;
    private String paymentDate;
    private String remindLaterDate;
    private Boolean remindLaterFlow;
    private String oldBillFetchDate;
}
