package com.paytm.saga.dto;

public class ViewDetail {
	private String valueColor;
	private String keyColor;
	private String bgColor;
	private String borderColor;
	private String valueFontWeight;
	private String keyFontWeight;
	private String rightThumbImage;
	private String leftThumbImage;
	private String richTextColor;
	private String theme;
	private Boolean isAmount;

	public ViewDetail(String valueColor,String keyColor, String bgColor, String borderColor, String valueFontWeight,
			String keyFontWeight,
			String richTextColor, String rightThumbImage, String leftThumbImage, Boolean isAmount, String theme) {
		this.valueColor = valueColor;
		this.keyColor = keyColor;
		this.bgColor = bgColor;
		this.borderColor = borderColor;
		this.valueFontWeight = valueFontWeight;
		this.keyFontWeight = keyFontWeight;
		this.rightThumbImage = rightThumbImage;
		this.leftThumbImage = leftThumbImage;
		this.richTextColor = richTextColor;
		
//		if (isAmount == null) {
//			isAmount = false;
//		}
		this.isAmount = isAmount;
		this.theme = theme;
	}
	public ViewDetail() {
		
	}

	public String getValueColor() {
		return valueColor;
	}

	public void setValueColor(String valueColor) {
		this.valueColor = valueColor;
	}

	public String getBgColor() {
		return bgColor;
	}

	public void setBgColor(String bgColor) {
		this.bgColor = bgColor;
	}

	public String getBorderColor() {
		return borderColor;
	}

	public void setBorderColor(String borderColor) {
		this.borderColor = borderColor;
	}

	public String getValueFontWeight() {
		return valueFontWeight;
	}

	public void setValueFontWeight(String valueFontWeight) {
		this.valueFontWeight = valueFontWeight;
	}

	public String getRightThumbImage() {
		return rightThumbImage;
	}

	public void setRightThumbImage(String rightThumbImage) {
		this.rightThumbImage = rightThumbImage;
	}

	public String getLeftThumbImage() {
		return leftThumbImage;
	}

	public void setLeftThumbImage(String leftThumbImage) {
		this.leftThumbImage = leftThumbImage;
	}

	public String getRichTextColor() {
		return richTextColor;
	}

	public void setRichTextColor(String richTextColor) {
		this.richTextColor = richTextColor;
	}

	public Boolean getIsAmount() {
		return isAmount;
	}

	public void setIsAmount(Boolean isAmount) {
		this.isAmount = isAmount;
	}
	public String getKeyColor() {
		return keyColor;
	}
	public void setKeyColor(String keyColor) {
		this.keyColor = keyColor;
	}
	public String getKeyFontWeight() {
		return keyFontWeight;
	}
	public void setKeyFontWeight(String keyFontWeight) {
		this.keyFontWeight = keyFontWeight;
	}

	public String getTheme() {
		return theme;
	}

	public void setTheme(String theme) {
		this.theme = theme;
	}
}
