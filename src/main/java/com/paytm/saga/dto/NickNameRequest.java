package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.validators.CreditCardValidate;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

@CreditCardValidate
@Data
@ToString
public class NickNameRequest {

    @JsonProperty("customer_id")
    @NotNull(message = "customer_id is missing")
    private Long customerId;
    @NotEmpty(message = "operator is missing")
    private String operator;

    private String panUniqueReference;

    @JsonProperty("reference_id")
    private String referenceId;
    @NotEmpty(message = "paytype is missing")
    private String paytype;
    @NotEmpty(message = "service is missing")
    private String service;

    @JsonProperty("recharge_number")
    @NotEmpty(message = "recharge_number is missing")
    private String rechargeNumber;

    @JsonProperty("nickname")
    private String nickName;

}