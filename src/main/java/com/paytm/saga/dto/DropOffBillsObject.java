package com.paytm.saga.dto;

import lombok.Data;

@Data
public class DropOffBillsObject {
    private String expiry;
    private String plan_bucket;
    private Double amount;
    private Long orderId;
    private String state;
    private String bill_date;
    private String due_date;
    private String cin;
    private String panUniqueReference;
    private String tin;
    private Double min_due_amount;
    private Double original_due_amount;
    private Double original_min_due_amount;
    private Double reminder_amount;
    private String previousOrderId;
    private String channel;
    

    public DropOffBillsObject(String expiry, String plan_bucket, Double amount, Long orderId, String state, String bill_date, String due_date,String cin) {
        this.expiry = expiry;
        this.plan_bucket = plan_bucket;
        this.amount = amount;
        this.orderId = orderId;
        this.state = state;
        this.bill_date = bill_date;
        this.due_date = due_date;
        this.cin = cin;
    }
    
    public DropOffBillsObject(String expiry, String plan_bucket, Double amount, Long orderId, String state, String bill_date, String due_date) {
        this.expiry = expiry;
        this.plan_bucket = plan_bucket;
        this.amount = amount;
        this.orderId = orderId;
        this.state = state;
        this.bill_date = bill_date;
        this.due_date = due_date;
    }

    public DropOffBillsObject(){

    }

    @Override
    public String toString() {
        return "DropOffBillsObject{" +
                "expiry='" + expiry + '\'' +
                ", plan_bucket='" + plan_bucket + '\'' +
                ", amount=" + amount +
                ", orderId=" + orderId +
                ", state='" + state + '\'' +
                ", bill_date='" + bill_date + '\'' +
                ", due_date='" + due_date + '\'' +
                '}';
    }
}
