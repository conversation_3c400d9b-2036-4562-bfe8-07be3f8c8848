package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class OMSItemMetaBenefitsModel {
    @JsonProperty("data")
    private String data;

    @JsonProperty("validity")
    private String validity;

    @JsonProperty("talktime")
    private String talktime;

    @JsonProperty("sms")
    private String sms;

    @JsonProperty("couponDescription")
    private String description;

    @JsonProperty("addon_benefit")
    private List<String> addon_benefit;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getValidity() {
        return validity;
    }

    public void setValidity(String validity) {
        this.validity = validity;
    }

    public String getTalktime() {
        return talktime;
    }

    public void setTalktime(String talktime) {
        this.talktime = talktime;
    }

    public String getSms() {
        return sms;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public List<String> getAddon_benefit() {
        return addon_benefit;
    }

    public void setAddon_benefit(List<String> addon_benefit) {
        this.addon_benefit = addon_benefit;
    }

    @Override
    public String toString() {
        return "OMSItemMetaBenefitsModel{" +
                "data='" + data + '\'' +
                ", validity='" + validity + '\'' +
                ", talktime='" + talktime + '\'' +
                ", sms='" + sms + '\'' +
                ", description='" + description + '\'' +
                ", addon_benefit=" + addon_benefit +
                '}';
    }
}
