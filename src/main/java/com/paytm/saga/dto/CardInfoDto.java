package com.paytm.saga.dto;

import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.ReminderHistory;

public class CardInfoDto {
	private ChannelHistory channelHistory;
	private ReminderHistory reminderHistory;
	private DropOff dropOff;
	private boolean ignoreCTA;
	private boolean previousTxnHistory;
	private String client;
	private String version;
	private GetHistoryPageDTO historyPageRequest;
	private String themeType;

	private String eventType;
	private String mnpType;

	public boolean isNative() {
		return isNative;
	}

	public void setNative(boolean aNative) {
		isNative = aNative;
	}

	private boolean isNative;

	public ChannelHistory getChannelHistory() {
		return channelHistory;
	}

	public void setChannelHistory(ChannelHistory channelHistory) {
		this.channelHistory = channelHistory;
	}

	public boolean isIgnoreCTA() {
		return ignoreCTA;
	}

	public void setIgnoreCTA(boolean ignoreCTA) {
		this.ignoreCTA = ignoreCTA;
	}

	public DropOff getDropOff() {
		return dropOff;
	}

	public void setDropOff(DropOff dropOff) {
		this.dropOff = dropOff;
	}

	public ReminderHistory getReminderHistory() {
		return reminderHistory;
	}

	public void setReminderHistory(ReminderHistory reminderHistory) {
		this.reminderHistory = reminderHistory;
	}

	public boolean isPreviousTxnHistory() {
		return previousTxnHistory;
	}

	public void setPreviousTxnHistory(boolean previousTxnHistory) {
		this.previousTxnHistory = previousTxnHistory;
	}

	public String getClient() {
		return client;
	}

	public void setClient(String client) {
		this.client = client;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public GetHistoryPageDTO getHistoryPageRequest() {
		return historyPageRequest;
	}

	public void setHistoryPageRequest(GetHistoryPageDTO historyPageRequest) {
		this.historyPageRequest = historyPageRequest;
	}

	public String getThemeType() {
		return themeType;
	}

	public void setThemeType(String themeType) {
		this.themeType = themeType;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	private String previousOperator;

	public String getPreviousOperator() {
		return previousOperator;
	}

	public void setPreviousOperator(String previousOperator) {
		this.previousOperator = previousOperator;
	}

	private String previousPayType;

	public String getPreviousPayType() {
		return previousPayType;
	}

	public void setPreviousPayType(String previousPayType) {
		this.previousPayType = previousPayType;
	}

	public String getMnpType() {
		return mnpType;
	}

	public void setMnpType(String mnpType) {
		this.mnpType = mnpType;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((channelHistory == null) ? 0 : channelHistory.hashCode());
		result = prime * result + ((dropOff == null) ? 0 : dropOff.hashCode());
		result = prime * result + (ignoreCTA ? 1231 : 1237);
		result = prime * result + (previousTxnHistory ? 1231 : 1237);
		result = prime * result + ((reminderHistory == null) ? 0 : reminderHistory.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CardInfoDto other = (CardInfoDto) obj;
		if (channelHistory == null) {
			if (other.channelHistory != null)
				return false;
		} else if (!channelHistory.equals(other.channelHistory))
			return false;
		if (dropOff == null) {
			if (other.dropOff != null)
				return false;
		} else if (!dropOff.equals(other.dropOff))
			return false;
		if (ignoreCTA != other.ignoreCTA)
			return false;
		if (previousTxnHistory != other.previousTxnHistory)
			return false;
		if (reminderHistory == null) {
			if (other.reminderHistory != null)
				return false;
		} else if (!reminderHistory.equals(other.reminderHistory))
			return false;
		return true;
	}
	

}
