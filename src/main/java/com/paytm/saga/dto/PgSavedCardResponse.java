package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class PgSavedCardResponse {
    @JsonProperty("responseStatus")
    private String responseStatus;
    @JsonProperty("httpCode")
    private String httpCode;
    @JsonProperty("httpSubCode")
    private String httpSubCode;
    @JsonProperty("codeDetail")
    private String codeDetail;
    @JsonProperty("response")
    private List<SavedCardDetails> response;

    public String getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(String responseStatus) {
        this.responseStatus = responseStatus;
    }

    public String getHttpCode() {
        return httpCode;
    }

    public void setHttpCode(String httpCode) {
        this.httpCode = httpCode;
    }

    public String getHttpSubCode() {
        return httpSubCode;
    }

    public void setHttpSubCode(String httpSubCode) {
        this.httpSubCode = httpSubCode;
    }

    public String getCodeDetail() {
        return codeDetail;
    }

    public void setCodeDetail(String codeDetail) {
        this.codeDetail = codeDetail;
    }

    public List<SavedCardDetails> getResponse() {
        return response;
    }

    public void setResponse(List<SavedCardDetails> response) {
        this.response = response;
    }

    @Override
    public String toString() {
        return "PgSavedCardV2Response{" +
                "responseStatus='" + responseStatus + '\'' +
                ", httpCode='" + httpCode + '\'' +
                ", httpSubCode='" + httpSubCode + '\'' +
                ", codeDetail='" + codeDetail + '\'' +
                ", response=" + response +
                '}';
    }
}
