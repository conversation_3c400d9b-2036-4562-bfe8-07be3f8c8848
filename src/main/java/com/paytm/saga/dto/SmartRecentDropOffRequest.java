package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
@ToString
public class SmartRecentDropOffRequest {
    private Long customerId;

    @JsonProperty("rechargeNumber")
    private String rechargeNumber;

    @NotEmpty(message = "operator params missing")
    private String operator;

    @NotEmpty(message = "service params missing")
    private String service;

    @NotNull(message = "productId params missing")
    private Long productId;

    @NotEmpty(message = "source cannot be empty")
    private String source;

}
