package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ScratchCardResponseModel {
    @JsonProperty("scratchCardId")
    private Long scratchCardId;

    @JsonProperty("userId")
    private String userId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("txnId")
    private String orderId;

    @JsonProperty("createdAt")
    private Long createdAt;

    @JsonProperty("updatedAt")
    private Long updatedAt;

    @JsonProperty("deleted")
    private boolean deleted;

    @JsonProperty("txnSource")
    private String txnSource;

    @JsonProperty("txnInfo")
    private ScratchCardHistoryTxnInfoModel txnInfo;

    public Long getScratchCardId() {
        return scratchCardId;
    }

    public void setScratchCardId(Long scratchCardId) {
        this.scratchCardId = scratchCardId;
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderId() {
        return orderId;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean isDeleted() {
        return deleted;
    }

    @Override
    public String toString() {
        return "ScratchCardResponseModel{" +
                "scratchCardId=" + scratchCardId +
                ", userId='" + userId + '\'' +
                ", status='" + status + '\'' +
                ", orderId='" + orderId + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", deleted=" + deleted +
                ", txnSource='" + txnSource + '\'' +
                ", txnInfo=" + txnInfo +
                '}';
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTxnSource() {
        return txnSource;
    }

    public void setTxnSource(String txnSource) {
        this.txnSource = txnSource;
    }

    public ScratchCardHistoryTxnInfoModel getTxnInfo() {
        return txnInfo;
    }

    public void setTxnInfo(ScratchCardHistoryTxnInfoModel txnInfo) {
        this.txnInfo = txnInfo;
    }
}
