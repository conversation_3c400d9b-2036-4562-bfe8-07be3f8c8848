package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class DCATCategoryGroupingsModel {
    @JsonProperty("aggKey")
    private String aggKey;
    @JsonProperty("aggs")
    private List<DCATCategoryGroupingAggModel> aggs;

    public String getAggKey() {
        return aggKey;
    }

    public void setAggKey(String aggKey) {
        this.aggKey = aggKey;
    }

    public List<DCATCategoryGroupingAggModel> getAggs() {
        return aggs;
    }

    public void setAggs(List<DCATCategoryGroupingAggModel> aggs) {
        this.aggs = aggs;
    }

}