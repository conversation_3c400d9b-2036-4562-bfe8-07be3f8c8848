package com.paytm.saga.dto;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SavedCardApiResponse {
	private ProductInfo product;
	@JsonProperty("additional_info")
	private SavedCardAdditionalInfo additionalInfo;
	@JsonProperty("recharge_number")
	private String rechargeNumber;
	@JsonProperty("recharge_number_for_display")
	private String rechargeNumberForDisplay;
	private String operator;
	private String type;
	private int priority;
	private List<SavedCardCTAResponse> cta;
	private List<SavedCardBillsObj> bills;
	private String paidOutside;
	@JsonProperty("colors")
    private ArrayList<Object> colors;
	@JsonProperty("mediaAssets")
    private ArrayList<Object> mediaAssets;
	@JsonProperty("issuingBankCardVariant")
	private String issuingBankCardVariant;

	public ArrayList<Object> getColors() {
		return colors;
	}
	public void setColors(ArrayList<Object> colors) {
		this.colors = colors;
	}
	public ArrayList<Object> getMediaAssets() {
		return mediaAssets;
	}
	public void setMediaAssets(ArrayList<Object> mediaAssets) {
		this.mediaAssets = mediaAssets;
	}
	public ProductInfo getProduct() {
		return product;
	}
	public void setProduct(ProductInfo product) {
		this.product = product;
	}
	public SavedCardAdditionalInfo getAdditionalInfo() {
		return additionalInfo;
	}
	public void setAdditionalInfo(SavedCardAdditionalInfo additionalInfo) {
		this.additionalInfo = additionalInfo;
	}
	public String getRechargeNumber() {
		return rechargeNumber;
	}
	public void setRechargeNumber(String rechargeNumber) {
		this.rechargeNumber = rechargeNumber;
	}
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public int getPriority() {
		return priority;
	}
	public void setPriority(int priority) {
		this.priority = priority;
	}
	public List<SavedCardCTAResponse> getCta() {
		return cta;
	}
	public void setCta(List<SavedCardCTAResponse> cta) {
		this.cta = cta;
	}
	public String getRechargeNumberForDisplay() {
		return rechargeNumberForDisplay;
	}
	public void setRechargeNumberForDisplay(String rechargeNumberForDisplay) {
		this.rechargeNumberForDisplay = rechargeNumberForDisplay;
	}

	public List<SavedCardBillsObj> getBills() {
		return bills;
	}

	public void setBills(List<SavedCardBillsObj> bills) {
		this.bills = bills;
	}

	public String getPaidOutside() {
		return paidOutside;
	}

	public void setPaidOutside(String paidOutside) {
		this.paidOutside = paidOutside;
	}

	public String getIssuingBankCardVariant() {
		return issuingBankCardVariant;
	}

	public void setIssuingBankCardVariant(String issuingBankCardVariant) {
		this.issuingBankCardVariant = issuingBankCardVariant;
	}

	@Override
	public String toString() {
		return "SavedCardApiResponse{" +
				"product=" + product +
				", additionalInfo=" + additionalInfo +
				", rechargeNumber='" + rechargeNumber + '\'' +
				", rechargeNumberForDisplay='" + rechargeNumberForDisplay + '\'' +
				", operator='" + operator + '\'' +
				", type='" + type + '\'' +
				", priority=" + priority +
				", cta=" + cta +
				", bills=" + bills +
				", paidOutside='" + paidOutside + '\'' +
				'}';
	}
}
