package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DCATCategoryGroupingAggAggProduct {
	@JsonProperty("productId")
	private Long productId;
	@JsonProperty("card_network")
	private String cardNetwork;
	@JsonProperty("service")
	private String service;
	@JsonProperty("bank_code")
	private String bankCode;
	@JsonProperty("operator")
	private String operator;
	

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getCardNetwork() {
		return cardNetwork;
	}

	public void setCardNetwork(String cardNetwork) {
		this.cardNetwork = cardNetwork;
	}

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

}
