package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.ServiceNameConstants;
import com.paytm.saga.enums.EventState;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SuggestedCard {
    private Double amount;

    private String planCircleKey;

    private String planId;

    private Long productId;
    
    @JsonProperty("plan_Bucket")
    private String planBucket;

    private final String eventState = Constants.SUGGESTED_CARD;
}