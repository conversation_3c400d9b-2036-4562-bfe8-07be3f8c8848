package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class DCATCategoryRequestModel {
    @JsonProperty("groups")
    private List<String> groups;

    public DCATCategoryRequestModel(List<String> groups) {
        this.groups = groups;
    }

    public List<String> getGroups() {
        return groups;
    }

    public void setGroups(List<String> groups) {
        this.groups = groups;
    }

    @Override
    public String toString() {
        return "DCATCategoryRequestModel{" +
                "groups=" + groups +
                '}';
    }
}