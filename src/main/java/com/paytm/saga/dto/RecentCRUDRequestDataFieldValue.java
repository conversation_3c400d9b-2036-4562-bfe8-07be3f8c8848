package com.paytm.saga.dto;

public class RecentCRUDRequestDataFieldValue {

    private Long amount;
    private String due_date;
    private String bill_date;
    private String mark_as_paid_time;
    private String expiry;
    private String plan_bucket;


    public RecentCRUDRequestDataFieldValue(Long amount, String due_date, String bill_date, String mark_as_paid_time, String expiry, String plan_bucket) {
        this.amount = amount;
        this.due_date = due_date;
        this.bill_date = bill_date;
        this.mark_as_paid_time = mark_as_paid_time;
        this.expiry = expiry;
        this.plan_bucket = plan_bucket;
    }

    @Override
    public String toString() {
        return "RecentCRUDRequestDataFieldValue{" +
                "amount=" + amount +
                ", due_date='" + due_date + '\'' +
                ", bill_date='" + bill_date + '\'' +
                ", mark_as_paid_time='" + mark_as_paid_time + '\'' +
                ", expiry='" + expiry + '\'' +
                ", plan_bucket='" + plan_bucket + '\'' +
                '}';
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getDue_date() {
        return due_date;
    }

    public void setDue_date(String due_date) {
        this.due_date = due_date;
    }

    public String getBill_date() {
        return bill_date;
    }

    public void setBill_date(String bill_date) {
        this.bill_date = bill_date;
    }

    public String getMark_as_paid_time() {
        return mark_as_paid_time;
    }

    public void setMark_as_paid_time(String mark_as_paid_time) {
        this.mark_as_paid_time = mark_as_paid_time;
    }

    public String getExpiry() {
        return expiry;
    }

    public void setExpiry(String expiry) {
        this.expiry = expiry;
    }

    public String getPlan_bucket() {
        return plan_bucket;
    }

    public void setPlan_bucket(String plan_bucket) {
        this.plan_bucket = plan_bucket;
    }
}
