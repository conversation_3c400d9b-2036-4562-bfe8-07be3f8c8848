package com.paytm.saga.dto;

import java.util.HashMap;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.paytm.saga.util.JsonUtils;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
public class DropOffConfigByService {
	private final Logger logger = LogManager.getLogger(DropOffConfigByService.class);
	private final Map<String, DropOffConfig> dropOffConfigMap = new HashMap<String, DropOffConfig>();

	public DropOffConfig getDropOffConfig(String category) {
		return dropOffConfigMap.get(category);
	}

	public void updateConfigConfigMap(String service, String category, String config) {
		try {
			if (category != null && config != null) {
				DropOffConfig dropOffConfig = JsonUtils.parseJson(config, DropOffConfig.class);
				dropOffConfigMap.put(category, dropOffConfig);
			}
		} catch (Exception e) {
			logger.error(
					"DropOffConfigByService :: updateConfigConfigMap, getting exception in loading config for category-"
							+ category,
					e);
		}
	}
}
