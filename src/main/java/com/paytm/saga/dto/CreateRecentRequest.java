package com.paytm.saga.dto;


import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Date;


@Data
public class CreateRecentRequest {
    @NotNull(message = "customerId can't be null")
    private Long customerId;
    @NotEmpty(message = "service can't be empty")
    private String service;
    @NotEmpty(message = "rechargeNumber can't be empty")
    private String rechargeNumber;
    @NotEmpty(message = "operator can't be empty")
    private String operator;
    private String planBucket;
    @NotNull(message = "productId can't be null")
    private Long productId;
    @NotEmpty(message = "paytype can't be empty")
    private String paytype;
    private Double dueAmount;
    private Date dueDate;
    private Object insuranceCard;
}
