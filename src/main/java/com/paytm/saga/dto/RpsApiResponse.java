package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
@Data
public class RpsApiResponse {
    @JsonProperty("addon_benefit")
    private List<String> addon_benefit;
    @JsonProperty("data")
    private String data;

    @JsonProperty("price")
    private String price;

    @JsonProperty("validity")
    private String validity;

    @JsonProperty("talktime")
    private String talktime;

    @JsonProperty("sms")
    private String sms;

    @JsonProperty("description")
    private String description;
    @JsonProperty("productId")
    private String plan_id;
    @JsonProperty("plan_bucket")
    private String plan_bucket;

}
