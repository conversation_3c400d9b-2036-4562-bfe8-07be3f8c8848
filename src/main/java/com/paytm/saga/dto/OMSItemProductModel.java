package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OMSItemProductModel {

    @JsonProperty("id")
    private Long productId;

    @JsonProperty("attributes")
    private OMSItemAttributeModel attributes;

    @JsonProperty("category_id")
    private Long categoryId;

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public OMSItemAttributeModel getAttributes() {
        return attributes;
    }

    public void setAttributes(OMSItemAttributeModel attributes) {
        this.attributes = attributes;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public String toString() {
        return "OMSItemProductModel{" +
                "productId=" + productId +
                ", attributes=" + attributes.toString() +
                ", categoryId=" + categoryId +
                '}';
    }
}


