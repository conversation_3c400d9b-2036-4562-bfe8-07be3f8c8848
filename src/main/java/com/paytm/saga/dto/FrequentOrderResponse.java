package com.paytm.saga.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.common.constant.BillStateTypes;
import com.paytm.saga.enums.BillState;
import com.paytm.saga.enums.BillType;
import com.paytm.saga.enums.EventState;
import com.paytm.saga.enums.EventType;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

@Data
public class FrequentOrderResponse {

    private Boolean isDefaultAmount;
    public void setIsDefaultAmount(Boolean isDefaultAmount) {
        this.isDefaultAmount = isDefaultAmount;
    }

    private String date;

    private String channel;

    private Integer automaticState;

    private String automaticDate;

    private String markAsPaidDate;

    private String txnDate;
    private Date operatorValidatedAt;

    private String oprBillGenDate;

    private String pid;

    private String consumerName;

    private String cylinderAgencyName;

    private String nickName;

    private boolean hasPaymentHistory;

    private Double amount;

    private Integer billVisibilityDays;

    @JsonProperty("recharge_number_1")
    private String rechargeNumber1;

    @JsonProperty("recharge_number_2")
    private String rechargeNumber2;

    @JsonProperty("recharge_number_3")
    private String rechargeNumber3;

    @JsonProperty("recharge_number_4")
    private String rechargeNumber4;

    @JsonProperty("recharge_number_5")
    private String rechargeNumber5;

    @JsonProperty("recharge_number_6")
    private String rechargeNumber6;

    @JsonProperty("recharge_number_7")
    private String rechargeNumber7;

    @JsonProperty("recharge_number_8")
    private String rechargeNumber8;

    private Map<String, Object> ambiguous;

    private EventType eventType;

    private EventState eventState;

    private BillState billState;

    private Map<String, String> additionalInfo;

    private Map<String, String> operatorData;

    private Map<String, Object> operatorRecentData;

    private Map<String, Object> fastagExtraData;

    private Long orderId;

    private Object subscriberDetails;

    private BillObject bill;

    private Boolean isTxnAutomatic;

    private boolean notPaidOnPaytm;

    private boolean reminderNotificationEnabled;

    private Boolean groupDisplayEnabled;

    private Boolean amountEditable;

    private Double txnAmount;

    private String createdAt;

    private Double originalDueAmount;

    private Integer automaticSubscriptionId;

    private Map<String, Object> rentTfData;
    private boolean earlyPayment;

    private Map<String, Object> apiParams;

    private Boolean showBBPSFlag;

    @JsonProperty("issuingBankCardVariant")
    private String issuingBankCardVariant;

    @JsonProperty("mediaAssets")
    private ArrayList<Object> mediaAssets;

    private String planBucket;
    private BillType billType;
    private Double automaticAmount;
    private String remindLaterDate;
    private Boolean remindLater;
    @JsonProperty("isPaytmVPA")
    private Integer isPaytmVPA;
    private Boolean isPrepaid;
    private String rentConsent;
    private Integer hasConsent;
    private String consentValidTill;

}
