package com.paytm.saga.dto;

public class DropOffConfig {
	private int newUserTimeDropOffTime;// in minutes
	private int recurringUserDropOffTime;// in minutes

	public DropOffConfig() {

	}

	public DropOffConfig(int newUserTimeDropOffTime, int recurringUserDropOffTime) {
		super();
		this.newUserTimeDropOffTime = newUserTimeDropOffTime;
		this.recurringUserDropOffTime = recurringUserDropOffTime;
	}

	public int getNewUserTimeDropOffTime() {
		return newUserTimeDropOffTime;
	}

	public void setNewUserTimeDropOffTime(int newUserTimeDropOffTime) {
		this.newUserTimeDropOffTime = newUserTimeDropOffTime;
	}

	public int getRecurringUserDropOffTime() {
		return recurringUserDropOffTime;
	}

	public void setRecurringUserDropOffTime(int recurringUserDropOffTime) {
		this.recurringUserDropOffTime = recurringUserDropOffTime;
	}

	@Override
	public String toString() {
		return "DropOffConfig [newUserTimeDropOffTime=" + newUserTimeDropOffTime + ", recurringUserDropOffTime="
				+ recurringUserDropOffTime + "]";
	}
}
