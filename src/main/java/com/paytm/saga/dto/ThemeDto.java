package com.paytm.saga.dto;

public class ThemeDto {
	private String themeType;
	private String cardAlignment;
	private String displayValuesAlignment;
	private String borderColor;
	private String bgColor;
	private Integer width;
	private String showDisplayValue;
	private ThemeDetails themeDetail;
	private String valueColor;

	public String getValueColor() {
		return valueColor;
	}

	public void setValueColor(String valueColor) {
		this.valueColor = valueColor;
	}

	public String getThemeType() {
		return themeType;
	}

	public void setThemeType(String themeType) {
		this.themeType = themeType;
	}

	public String getDisplayValuesAlignment() {
		return displayValuesAlignment;
	}

	public void setDisplayValuesAlignment(String displayValuesAlignment) {
		this.displayValuesAlignment = displayValuesAlignment;
	}

	public String getBgColor() {
		return bgColor;
	}

	public void setBgColor(String bgColor) {
		this.bgColor = bgColor;
	}

	public ThemeDetails getThemeDetail() {
		return themeDetail;
	}

	public void setThemeDetail(ThemeDetails themeDetail) {
		this.themeDetail = themeDetail;
	}

	public String getShowDisplayValue() {
		return showDisplayValue;
	}

	public void setShowDisplayValue(String showDisplayValue) {
		this.showDisplayValue = showDisplayValue;
	}

	public String getCardAlignment() {
		return cardAlignment;
	}

	public void setCardAlignment(String cardAlignment) {
		this.cardAlignment = cardAlignment;
	}

	public String getBorderColor() {
		return borderColor;
	}

	public void setBorderColor(String borderColor) {
		this.borderColor = borderColor;
	}

	public Integer getWidth() {
		return width;
	}

	public void setWidth(Integer width) {
		this.width = width;
	}
}
