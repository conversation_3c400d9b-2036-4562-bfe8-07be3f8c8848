package com.paytm.saga.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
@Data
public class CIRRequest {
    @JsonProperty("customerid")
    @NotNull(message = "customerId can't be empty")
    private Long customerId;

    @JsonProperty("bureauname")
    @NotNull(message = "bureauName can't be empty")
    private String bureauName;
}
