package com.paytm.saga.dto.cdc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
@Data
public class NonPaytmAfter {
    @NotNull(message = "customerId can't be empty")
    @JsonProperty("customer_id")
    private LongEntity customerId;

    @NotNull(message = "service can't be empty")
    @JsonProperty("service")
    private StringEntity service;

    @NotNull(message = "rechargeNumber can't be empty")
    @JsonProperty("recharge_number")
    private StringEntity rechargeNumber;

    @NotNull(message = "operator can't be empty")
    private StringEntity operator;

    @JsonProperty("bill_date")
    private StringEntity billDate;

    @JsonProperty("payment_date")
    private StringEntity paymentDate;

    @JsonProperty("circle")
    private StringEntity circle;

    @JsonProperty("event_source")
    private StringEntity eventSource;

    @JsonProperty("due_amount")
    private DoubleEntity dueAmount;

    @JsonProperty("due_date")
    private StringEntity dueDate;

    @JsonProperty("amount")
    private DoubleEntity amount;

    @JsonProperty("transaction_id")
    private LongEntity orderId;

    @JsonProperty("transaction_amount")
    private StringEntity txnAmount;

    @JsonProperty("paytype")
    private StringEntity paytype;

    @JsonProperty("product_id")
    private LongEntity productId;

    @JsonProperty("status")
    private IntegerEntity status;

    @JsonProperty("notification_status")
    private IntegerEntity notificationStatus;

    @JsonProperty("user_data")
    private StringEntity userData;

    @JsonProperty("additional_info")
    private StringEntity additionalInfo;

    @JsonProperty("updated_at")
    private LongEntity updatedAt;

    @JsonProperty("created_at")
    private LongEntity createdAt;

    @JsonProperty("customer_other_info")
    private StringEntity customerOtherInfo;

    @JsonProperty("extra")
    private StringEntity extra;

    @JsonProperty("update_at")
    private LongEntity updateAt;

    @JsonProperty("create_at")
    private LongEntity createAt;
    @JsonProperty("billFetchDate")
    private LongEntity billFetchDate;
    @JsonProperty("card_network")
    private StringEntity cardNetwork;
    @JsonProperty("bank_name")
    private StringEntity bankName;
    @JsonProperty("remindLaterDate")
    private LongEntity remindLaterDate;
    @JsonProperty("remindLaterFlow")
    private BooleanEntity remindLaterFlow;
    @JsonProperty("oldBillFetchDate")
    private StringEntity oldBillFetchDate;
}
