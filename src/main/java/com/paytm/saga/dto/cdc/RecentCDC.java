package com.paytm.saga.dto.cdc;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.RecentDataToKafkaModel;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
@ToString
public class RecentCDC {
    private Integer tsMs;
    @NotEmpty(message = "Op can't be empty")
    private String op;
    private Source source;
    @NotNull(message = "After can't be null")
    @Valid
    private After after;

    public RecentCDC() {
    }
    public RecentCDC(RecentDataToKafkaModel recentDataToKafkaModel) {
        this.tsMs = (int) (System.currentTimeMillis() / 1000);
        this.op = recentDataToKafkaModel.getOp() == null ? Constants.CDC.UPDATE_OP : recentDataToKafkaModel.getOp();
        this.source = new Source();
        this.after = new After(recentDataToKafkaModel);
    }
}
