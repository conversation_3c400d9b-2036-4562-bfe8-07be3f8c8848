package com.paytm.saga.dto.cdc;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.dto.RecentDataToKafkaModel;
import jnr.ffi.annotations.In;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

@Data
@ToString
public class After {
    @NotNull(message = "customerId can't be empty")
    private LongEntity customerid;

    @NotNull(message = "service can't be empty")
    private StringEntity service;

    @NotNull(message = "rechargeNumber can't be empty")
    @JsonProperty("recharge_number")
    private StringEntity rechargeNumber;

    @NotNull(message = "operator can't be empty")
    private StringEntity operator;

    @NotNull(message = "planBucket can't be empty")
    @JsonProperty("plan_bucket")
    private StringEntity planBucket;

    @JsonProperty("automatic_date")
    private LongEntity automaticDate;

    @JsonProperty("automatic_status")
    private IntegerEntity automaticStatus;

    @JsonProperty("bill_date")
    private LongEntity billDate;

    @JsonProperty("bill_update_time")
    private LongEntity billUpdateTime;

    @JsonProperty("channel_id")
    private StringEntity channelId;

    @JsonProperty("cin")
    private StringEntity cin;

    @JsonProperty("circle")
    private StringEntity circle;

    @JsonProperty("consumername_v2")
    private StringEntity consumername;

    @JsonProperty("cylinder_agency_name_v2")
    private StringEntity cylinderAgencyName;

    @JsonProperty("dismiss_action_time")
    private LongEntity dismissActionTime;

    @JsonProperty("due_amount")
    private DoubleEntity dueAmount;

    @JsonProperty("due_date")
    private LongEntity dueDate;

    @JsonProperty("is_mark_as_paid")
    private StringEntity isMarkAsPaid;

    @JsonProperty("is_tokenized_transaction")
    private StringEntity isTokenizedTransaction;

    @JsonProperty("mark_as_paid_time")
    private LongEntity markAsPaidTime;

    @JsonProperty("mcn")
    private StringEntity mcn;

    @JsonProperty("min_due_amount")
    private IntegerEntity minDueAmount;

    @JsonProperty("nick_name_v2")
    private StringEntity nickName;

    @JsonProperty("order_id")
    private LongEntity orderId;

    @JsonProperty("original_due_amount")
    private IntegerEntity originalDueAmount;

    @JsonProperty("original_min_due_amount")
    private IntegerEntity originalMinDueAmount;

    @JsonProperty("par")
    private StringEntity par;

    @JsonProperty("paytype")
    private StringEntity paytype;

    @JsonProperty("product_id")
    private IntegerEntity productId;

    @JsonProperty("recharge_number_2")
    private StringEntity rechargeNumber2;

    @JsonProperty("recharge_number_3")
    private StringEntity rechargeNumber3;

    @JsonProperty("recharge_number_4")
    private StringEntity rechargeNumber4;

    @JsonProperty("recharge_number_5")
    private StringEntity rechargeNumber5;

    @JsonProperty("recharge_number_6")
    private StringEntity rechargeNumber6;

    @JsonProperty("recharge_number_7")
    private StringEntity rechargeNumber7;

    @JsonProperty("recharge_number_8")
    private StringEntity rechargeNumber8;

    @JsonProperty("tin")
    private StringEntity tin;

    @JsonProperty("txn_amount")
    private IntegerEntity txnAmount;

    @JsonProperty("txn_status")
    private StringEntity txnStatus;

    @JsonProperty("txn_time")
    private LongEntity txnTime;

    @JsonProperty("updated_at")
    private LongEntity updatedAt;

    public After() {

    }

    public After(RecentDataToKafkaModel recentDataToKafkaModel) {
        this.customerid = new LongEntity(recentDataToKafkaModel.getCustomerId());
        this.service = new StringEntity(recentDataToKafkaModel.getService());
        this.rechargeNumber = new StringEntity(recentDataToKafkaModel.getRechargeNumber());
        this.operator = new StringEntity(recentDataToKafkaModel.getOperator());
        this.planBucket = new StringEntity(recentDataToKafkaModel.getPlanBucket());
        this.automaticDate = recentDataToKafkaModel.getAutomaticDate() != null ? new LongEntity(recentDataToKafkaModel.getAutomaticDate().getTime()) : null;
        this.automaticStatus = new IntegerEntity(recentDataToKafkaModel.getAutomaticStatus());
        this.billDate = recentDataToKafkaModel.getBillDate() != null ? new LongEntity(recentDataToKafkaModel.getBillDate().getTime()) : null;
        this.billUpdateTime = recentDataToKafkaModel.getBillUpdateTime() != null ? new LongEntity(recentDataToKafkaModel.getBillUpdateTime().getTime()) : null;
        this.channelId = new StringEntity(recentDataToKafkaModel.getChannelId());
        this.cin = new StringEntity(recentDataToKafkaModel.getCin());
        this.circle = new StringEntity(recentDataToKafkaModel.getCircle());
        this.consumername = new StringEntity(recentDataToKafkaModel.getConsumerName());
        this.cylinderAgencyName = new StringEntity(recentDataToKafkaModel.getCylinderAgencyName());
        //this.dismissActionTime = new LongEntity(recentDataToKafkaModel.getDismissActionTime());
        this.dueAmount = new DoubleEntity(recentDataToKafkaModel.getDueAmount());
        this.dueDate = recentDataToKafkaModel.getDueDate() != null ? new LongEntity(recentDataToKafkaModel.getDueDate().getTime()) : null;
        this.isMarkAsPaid = recentDataToKafkaModel.getIsMarkAsPaid() != null ? new StringEntity(recentDataToKafkaModel.getIsMarkAsPaid().toString()) : null;
        this.isTokenizedTransaction = recentDataToKafkaModel.getIsTokenizedTransaction() != null ? new StringEntity(recentDataToKafkaModel.getIsTokenizedTransaction().toString()) : null;
        this.markAsPaidTime = recentDataToKafkaModel.getMarkAsPaidTime() != null ? new LongEntity(recentDataToKafkaModel.getMarkAsPaidTime().getTime()) : null;
        this.mcn = new StringEntity(recentDataToKafkaModel.getMcn());
        this.minDueAmount = recentDataToKafkaModel.getMinDueAmount() != null ? new IntegerEntity(recentDataToKafkaModel.getMinDueAmount().intValue()) : null;
        this.nickName = new StringEntity(recentDataToKafkaModel.getNickName());
        this.orderId = new LongEntity(recentDataToKafkaModel.getOrderId());
        this.originalDueAmount = recentDataToKafkaModel.getOriginalDueAmount() != null ? new IntegerEntity(recentDataToKafkaModel.getOriginalDueAmount().intValue()) : null;
        this.originalMinDueAmount = recentDataToKafkaModel.getOriginalMinDueAmount() != null ? new IntegerEntity(recentDataToKafkaModel.getOriginalMinDueAmount().intValue()) : null;
        this.par = new StringEntity(recentDataToKafkaModel.getPar());
        this.paytype = new StringEntity(recentDataToKafkaModel.getPayType());
        this.productId = recentDataToKafkaModel.getProductId() != null ? new IntegerEntity(recentDataToKafkaModel.getProductId().intValue()) : null;
        this.rechargeNumber2 = new StringEntity(recentDataToKafkaModel.getRechargeNumber2());
        this.rechargeNumber3 = new StringEntity(recentDataToKafkaModel.getRechargeNumber3());
        this.rechargeNumber4 = new StringEntity(recentDataToKafkaModel.getRechargeNumber4());
        this.rechargeNumber5 = new StringEntity(recentDataToKafkaModel.getRechargeNumber5());
        this.rechargeNumber6 = new StringEntity(recentDataToKafkaModel.getRechargeNumber6());
        this.rechargeNumber7 = new StringEntity(recentDataToKafkaModel.getRechargeNumber7());
        this.rechargeNumber8 = new StringEntity(recentDataToKafkaModel.getRechargeNumber8());
        this.tin = new StringEntity(recentDataToKafkaModel.getTin());
        this.txnAmount = recentDataToKafkaModel.getTxnAmount() != null ? new IntegerEntity(recentDataToKafkaModel.getTxnAmount().intValue()) : null;
        this.txnStatus = new StringEntity(recentDataToKafkaModel.getTxnStatus());
        this.txnTime = recentDataToKafkaModel.getTxnTime() != null ? new LongEntity(recentDataToKafkaModel.getTxnTime().getTime()) : null;
        this.updatedAt = recentDataToKafkaModel.getUpdatedAt() != null ? new LongEntity(recentDataToKafkaModel.getUpdatedAt().getTime()) : null;
    }
}
