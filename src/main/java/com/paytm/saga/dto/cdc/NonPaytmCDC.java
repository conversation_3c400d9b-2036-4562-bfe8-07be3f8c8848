package com.paytm.saga.dto.cdc;

import lombok.Data;
import lombok.ToString;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
@Data
@ToString
public class NonPaytmCDC {
    private Integer tsMs;
    @NotEmpty(message = "Op can't be empty")
    private String op;
    //private Source source;
    @NotNull(message = "After can't be null")
    @Valid
    private NonPaytmAfter after;
}
