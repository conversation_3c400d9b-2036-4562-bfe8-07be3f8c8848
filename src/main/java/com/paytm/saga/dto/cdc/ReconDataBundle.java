package com.paytm.saga.dto.cdc;

import com.paytm.saga.dto.oms.OMSObject;
import com.paytm.saga.dto.oms.OMSResponse;
import com.paytm.saga.model.Recents;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.Map;

@ToString
@Data
public class ReconDataBundle {
    private Recents recordFromDB;
    private OMSObject omsObject;
    private Recents recordFromKafka;
    private Map<String,Object> esData;
    private boolean compareWithES;
    private String dbOpType;

}
