package com.paytm.saga.dto;

import jakarta.validation.constraints.NotBlank;

public class ProductInfo {
	private Long productId;
	private String operator;
	private Long categoryId;
	@NotBlank(message = "service cannot be empty")
	private String service;
	@NotBlank(message = "cardType cannot be empty")
	private String cardType;
	private String bankName;
	private String cardNetwork;

	public ProductInfo() {}
	public ProductInfo(Long productId, String operator, Long categoryId, String service, String cardType,
			String bankName,String cardNetwork) {
		super();
		this.productId = productId;
		this.operator = operator;
		this.categoryId = categoryId;
		this.service = service;
		this.cardType = cardType;
		this.bankName = bankName;
		this.cardNetwork = cardNetwork;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getCardNetwork() {
		return cardNetwork;
	}
	public void setCardNetwork(String cardNetwork) {
		this.cardNetwork = cardNetwork;
	}
	@Override
	public String toString() {
		return "ProductInfo [productId=" + productId + ", operator=" + operator + ", categoryId=" + categoryId
				+ ", service=" + service + ", cardType=" + cardType + ", bankName=" + bankName + "]";
	}
}
