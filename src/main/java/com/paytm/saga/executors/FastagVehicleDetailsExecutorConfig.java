package com.paytm.saga.executors;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Data
@Component
@ConfigurationProperties(prefix = "fastag.vehicle-details.config.http")
public class FastagVehicleDetailsExecutorConfig {

    private int minPoolSize;

    private int maxPoolSize;

    private int queueSize;

    private int keepAlive;

    private int timeout;

    @Bean
    public ExecutorService fastagVehicleDetailsExecutor(final MeterRegistry registry) {
        ExecutorService executorService = new ThreadPoolExecutor(minPoolSize, maxPoolSize, keepAlive, TimeUnit.SECONDS, new LinkedBlockingQueue<>(queueSize));
        return ExecutorServiceMetrics.monitor(registry, executorService, "fastagVehicleDetailsExecutor", Tags.of("key", "value"));
    }
}
