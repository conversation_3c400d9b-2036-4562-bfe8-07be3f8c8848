package com.paytm.saga.executors;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Data
@Component
@ConfigurationProperties(prefix = "frequent-order.cc.reco.config.http")
public class FrequentOrderRecoEncryptionExecutorConfig {
	private int minPoolSize;

	private int maxPoolSize;

	private int queueSize;

	private int keepAlive;

	@Bean("frequentOrderCCRecoExecutor")
	public ExecutorService frequentOrderCCRecoExecutor(final MeterRegistry registry) {
		ExecutorService executorService = new ThreadPoolExecutor(minPoolSize, maxPoolSize, keepAlive, TimeUnit.SECONDS, new LinkedBlockingQueue<>(queueSize));
		return ExecutorServiceMetrics.monitor(registry, executorService, "frequentOrderCCRecoExecutor", Tags.of("key", "value"));
	}
}
