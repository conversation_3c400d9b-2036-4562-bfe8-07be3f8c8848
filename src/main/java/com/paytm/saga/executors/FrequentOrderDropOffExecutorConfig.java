package com.paytm.saga.executors;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Data
@Configuration
@ConfigurationProperties(prefix = "frequent-order.drop-off.config.http")
public class FrequentOrderDropOffExecutorConfig {

    private int minPoolSize;

    private int maxPoolSize;

    private int queueSize;

    private int keepAlive;

    @Bean
    public ExecutorService frequentOrderDropOffExecutor(final MeterRegistry registry) {
        ExecutorService executorService = new ThreadPoolExecutor(minPoolSize, maxPoolSize, keepAlive, TimeUnit.SECONDS, new LinkedBlockingQueue<>(queueSize));
        return ExecutorServiceMetrics.monitor(registry, executorService, "frequentOrderDropOffExecutor", Tags.of("key", "value"));
    }
}
