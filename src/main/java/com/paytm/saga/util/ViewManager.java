package com.paytm.saga.util;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;

public class ViewManager {
	private static final Logger logger = LogManager.getLogger(ViewManager.class);
}
