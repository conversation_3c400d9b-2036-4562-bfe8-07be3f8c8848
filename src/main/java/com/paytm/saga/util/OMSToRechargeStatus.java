package com.paytm.saga.util;

import java.util.Arrays;

public class OMSToRechargeStatus {
	public static String getRechargeStatusByOMSStatus(String omsStatus, String inResponseCode, String paymentStatus) {
		String[] cancelledStausCodes = { "10", "17", "2000", "3000" };
		if (omsStatus != null) {
			if (omsStatus.equals("7") || omsStatus.equals("210")) {
				return "SUCCESS";
			} else if (omsStatus.equals("6") || omsStatus.equals("18") || omsStatus.equals("34")
					|| (paymentStatus != null && paymentStatus.equals("4"))
					|| (paymentStatus != null && paymentStatus.equals("8"))) {
				if (inResponseCode != null && Arrays.stream(cancelledStausCodes).anyMatch(inResponseCode::equals)) {
					return "CANCEL";
				} else if (inResponseCode != null && inResponseCode.equals("11")) {
					return "REVERSAL_FAILURE";
				} else if ((paymentStatus != null && paymentStatus.equals("4"))
						|| (paymentStatus != null && paymentStatus.equals("8"))) {
					return "PAYMENT_FAILURE";
				} else {
					return "FAILURE";
				}

			} else {
				if (omsStatus.equals("1") && paymentStatus != null && paymentStatus.equals("1")) {
					return "PAYMENT_PENDING";
				} else {
					return "PENDING";
				}

			}
		} else {
			return "PENDING";
		}
	}
}
