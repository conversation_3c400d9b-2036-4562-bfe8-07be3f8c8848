package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import org.apache.maven.artifact.versioning.ComparableVersion;

import java.util.Arrays;

public class VersionComparison {
    public static boolean isDropOffWhitetlisted(String client, String version) {
        if(client==null || version == null ) return false;
        ComparableVersion currentVersion = new ComparableVersion(version);

        if(Arrays.asList(Constants.CommonConstants.ANDROID_APP_CLIENT_NAMES).contains(client)){
            ComparableVersion dropOffThresholdVersion = new ComparableVersion(Constants.CommonConstants.ANDROID_APP_DROPOFF_VERSION);
            if(currentVersion.compareTo(dropOffThresholdVersion)>=0) return true;
            return false;
        } else if (Arrays.asList(Constants.CommonConstants.IOS_APP_CLIENT_NAMES).contains(client)){
            ComparableVersion dropOffThresholdVersion = new ComparableVersion(Constants.CommonConstants.IOS_APP_DROPOFF_VERSION);
            if(currentVersion.compareTo(dropOffThresholdVersion)>=0) return true;
            return false;
        } else {
            return false;
        }
    }
}
