package com.paytm.saga.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.paytm.saga.common.constant.DateFormats;

public class DateUtil extends DateUtils {

	private static final Logger logger = LogManager.getLogger(DateUtil.class);

	public static Date dateIncrDecr(Date date, int dayOffset) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int daysToDecrement = dayOffset;
		cal.add(Calendar.DATE, daysToDecrement);
		date = cal.getTime();
		return date;
	}
	public static Date max(Date d1, Date d2) {
		if (d1 == null && d2 == null) return null;
		if (d1 == null) return d2;
		if (d2 == null) return d1;
		return (d1.after(d2)) ? d1 : d2;
	}
	public static Date hoursIncrDecr(Date date, int hoursOffset) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.HOUR_OF_DAY, hoursOffset);
		date = cal.getTime();
		return date;
	}
	public static Date minutesIncrDecr(Date date, int minutesOffset) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int daysToDecrement = minutesOffset;
		cal.add(Calendar.MINUTE, daysToDecrement);
		date = cal.getTime();
		return date;
	}
	public static Date secondsIncrDecr(Date date, int secondsOffset) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int daysToDecrement = secondsOffset;
		cal.add(Calendar.SECOND, daysToDecrement);
		date = cal.getTime();
		return date;
	}

	public static Date timestampToDate(Date date) throws ParseException {
		try {
			DateFormat formatter = new SimpleDateFormat(DateFormats.DATE_FORMAT);
			Date todayWithZeroTime = formatter.parse(formatter.format(date));
			return todayWithZeroTime;
		} catch (Exception e) {
			logger.error("DateUtil: timestampToDate", e);
			throw e;
		}

	}
	public static int compareDateWithoutTime(Date date1, Date date2) {
		Date firstDate = getZeroTimeDate(date1);
		Date secondDate = getZeroTimeDate(date2);
		return firstDate.compareTo(secondDate);
	}

	public static Date getZeroTimeDate(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		date = calendar.getTime();
		return date;
	}

	public static Date getEndOfDayDate(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		date = calendar.getTime();
		return date;
	}

	public static String dateFormatter(Date date, String format) {
		DateFormat formatter = new SimpleDateFormat(format);
		return formatter.format(date);
	}

	public static Date stringToDate(String str, String format) {
		try {
			Date date = new SimpleDateFormat(format).parse(str);
			return date;
		} catch (Exception e) {
			logger.error("DateUtil: stringToDate",e);
		}
		return null;
	}

	// TODO remove dateFormatter usage
	public static String formatDate(Date date, String format) {
		if (date != null) {
			DateFormat formatter = new SimpleDateFormat(format);
			return formatter.format(date);
		}
		return null;
	}


	public static int compareDate(Date date1, Date date2) {
		if (date1 == null || date2 == null)
			return 2;
		return date1.compareTo(date2);
	}

	public static long getDayDiffBetweenDates(LocalDateTime date1, LocalDateTime date2){
		return  ChronoUnit.DAYS.between(date1.toLocalDate(),date2.toLocalDate());
	}

	public static LocalDateTime convertToLocalDateTime(Date dateToConvert) {
		return dateToConvert.toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDateTime();
	}

	public static Date convertToDateViaInstant(LocalDateTime dateToConvert) {
		return java.util.Date
				.from(dateToConvert.atZone(ZoneId.systemDefault())
						.toInstant());
	}
	public static Date maxDate(Date d1, Date d2) {
		if(Objects.isNull(d1) && Objects.isNull(d2))
			return null;
		if(Objects.isNull(d1))
			return d2;
		if(Objects.isNull(d2))
			return d1;
		return d1.after(d2)?d1:d2;
	}

	public static Date maxDateAmongAll(Date... dates) {
		if (dates == null || dates.length == 0) {
			return null;
		}

		Date maxDate = null;
		for (Date date : dates) {
			if (date != null) {
				if (maxDate == null || date.after(maxDate)) {
					maxDate = date;
				}
			}
		}
		return maxDate;
	}

	public static long getTimeUntilEndOfMonth() {
		// Get current date and time
		LocalDateTime now = LocalDateTime.now();
		// Get last day of current month
		LocalDateTime endOfMonth = now.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
		// Calculate duration between now and end of month
		Duration duration = Duration.between(now, endOfMonth);
		// Return duration in seconds
		return duration.getSeconds();
	}

	public static Boolean isDateOlderThanXDays(Date date, int xDays) {
		if (date == null) {
			return false;
		}
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, -xDays);
		Date comparisonDate = getZeroTimeDate(cal.getTime());
		Date zeroTimeDate = getZeroTimeDate(date);
		return zeroTimeDate.before(comparisonDate);
	}

	public static Date getLatestDate(Date date1, Date date2) {
		if (date1 == null && date2 == null) {
			return null;
		}
		if (date1 == null) {
			return date2;
		}
		if (date2 == null) {
			return date1;
		}
		return date1.after(date2) ? date1 : date2;
	}
}
