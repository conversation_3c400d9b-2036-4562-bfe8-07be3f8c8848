package com.paytm.saga.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.*;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.builder.RecentsResponseBuilder;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.enums.*;
import com.paytm.saga.model.BillerAccountKafkaModel;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.service.EncryptionDecision;
import com.paytm.saga.service.impl.ServiceConfig;
import com.timgroup.statsd.StatsDClient;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.util.CollectionUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.*;
import static com.paytm.saga.common.constant.EncryptionConstants.*;
import static com.paytm.saga.common.constant.Constants.CommonConstants.PARTIAL_DUE;
import static com.paytm.saga.common.constant.Constants.CommonConstants.TOTAL_DUE;
import static com.paytm.saga.common.constant.Constants.MetricConstants.FREQUENT_ORDER;
import static com.paytm.saga.common.constant.ServiceNameConstants.EXCLUDE_RECENT_SERVICES;
import static com.paytm.saga.common.constant.ServiceNameConstants.UPMS;
import static com.paytm.saga.common.constant.ServiceNameConstants.*;

public class RecentUtils {
    private static final Logger logger = LogManager.getLogger(RecentUtils.class);

    @Getter
    private static final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private ServiceConfig serviceConfig;
    private RecentUtils() {

    }


    public static boolean isMarkAsPaid(Recents recent) {
        if (recent.getMarkAsPaidTime() == null)
            return Boolean.FALSE;
        Date txnTime = recent.getTxnTime();
        Date billUpdateTime = recent.getBillUpdateTime();
        Date date = recent.getTxnTime();
        if(Objects.isNull(txnTime) && Objects.isNull(billUpdateTime)) {
            return Boolean.TRUE;
        }else if(Objects.isNull(txnTime) && Objects.nonNull(billUpdateTime)) {
            date = billUpdateTime;
        }else if(Objects.nonNull(txnTime) && Objects.isNull(billUpdateTime)) {
            date = txnTime;
        }else if(recent.getBillUpdateTime().compareTo(recent.getTxnTime())>0) {
            date = recent.getBillUpdateTime();
        }
        if(date.compareTo(recent.getMarkAsPaidTime()) > 0)
            return Boolean.FALSE;
        return Boolean.TRUE;
    }

    public static Map getExtras(Recents recent) {
        Map extrasRecent = null;
        if (recent != null && !StringUtils.isEmpty(recent.getExtra())) {
            try {
                extrasRecent = JsonUtils.parseJson(recent.getExtra(), Map.class);
            } catch (Exception e) {
                logger.error("[RecentUtils.getExtras] Recents getExtras case error occurred while parsing: ", e);
                return null;
            }
        }
        return extrasRecent;
    }
    

    public static String getPartialBill(Map extrasRecent){
        if(Objects.isNull(extrasRecent))return null;

        if(extrasRecent.containsKey(Constants.PARTIAL_BILL_EVENT_KEY)){
            String partialBillState = (String) extrasRecent.get(Constants.PARTIAL_BILL_EVENT_KEY);

            if(Objects.nonNull(partialBillState) && PARTIAL_BILL_STATE_CONSTANTS.VALID_PARTIAL_BILL_STATE.contains(partialBillState)) {
                return partialBillState;
            }
        }
        return null;
    }

    public static  boolean checkIfPrepaidCase(Recents recent) {
        Map extras = RecentUtils.getExtras(recent);
        if(checkIfPrepaidFlagIsSet(extras)) {
            return true;
        }

        List<String> prepaidAllowedServicesConfig = FeatureConfigCache.getInstance().getList(PREPAID_ALLOWED_SERVICES);
        List<String> prepaidAllowedOperatorsConfig = FeatureConfigCache.getInstance().getList(PREPAID_ALLOWED_OPERATORS);

        return recent != null &&
                ((prepaidAllowedServicesConfig != null && prepaidAllowedServicesConfig.stream().anyMatch(service -> service.equalsIgnoreCase(recent.getKey().getService()))) ||
                        (prepaidAllowedOperatorsConfig != null && prepaidAllowedOperatorsConfig.stream().anyMatch(operator -> operator.equalsIgnoreCase(recent.getKey().getOperator()))));
    }

    public static boolean checkIfPrepaidFlagIsSet(Map extrasRecent) {
        try {
            return Objects.nonNull(extrasRecent) && Objects.nonNull(extrasRecent.get(Constants.IS_PREPAID_KEY)) && Boolean.parseBoolean(extrasRecent.get(IS_PREPAID_KEY).toString());
        }catch (Exception ex){
            logger.error("[RecentUtils.checkIfPrepaidFlagIsSet] error occurred while parsing:", ex);
        }
        return false;
    }

    public static boolean isBillDue(Recents recent) {
        if (recent != null && (recent.getDueDate() != null || (checkDueAmount(recent) && PayType.CREDIT_CARD.value.equalsIgnoreCase(recent.getPayType()))) && !isMarkAsPaid(recent) && ((StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.RU_SMS,EVENT_SOURCE.PG_DELETED_AMW, EVENT_SOURCE.CSV) && (recent.getDueAmount() == null)) || checkDueAmount(recent)))
            return Boolean.TRUE;
        return Boolean.FALSE;
    }
    public static String getFastagDefaultRechargeNumber(long customerid){
      return new StringBuilder().append("default_").append(customerid).toString();
    }

    public static boolean isBillDuePartial(Recents recent){
        if(recent != null && StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.RU_SMS, EVENT_SOURCE.PG_DELETED_AMW, EVENT_SOURCE.CSV) && !isMarkAsPaid(recent) && recent.getBillDate() != null && (recent.getDueDate() == null || recent.getDueAmount() == null))
            return Boolean.TRUE;
        return Boolean.FALSE;
    }

    public static boolean isBillDueRUPartial(Recents recent, List<String> ruPartialBillRecoServices, List<String> updatedSourceList){
        if(recent == null) {
            return false;
        }
        
        // Check if event source matches validation_sync
        boolean isValidEventSource = StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), VALIDATION_SYNC_SOURCE_KEY);
        
        // Check if updated_data_source is in the configured list
        if (!isValidEventSource && recent.getExtra() != null) {
            try {
                Map<String, String> extraMap = JsonUtils.parseJson(recent.getExtra(), Map.class);
                if (extraMap != null && extraMap.containsKey(Constants.UPDATED_DATA_SOURCE)) {
                    String updatedDataSource = extraMap.get(Constants.UPDATED_DATA_SOURCE);
                    isValidEventSource = updatedSourceList.contains(updatedDataSource);
                }
            } catch (Exception e) {
                logger.error("[RecentUtils.isBillDueRUPartial] Error parsing extra JSON: {}", e.getMessage());
            }
        }
        
        return isValidEventSource && !isMarkAsPaid(recent) && recent.getBillDate() != null && 
               isRUPartialBill(recent.getKey().getService(), recent.getDueDate(), ruPartialBillRecoServices);
    }

    private static boolean checkDueAmount(Recents recent){
        return recent.getDueAmount() != null && recent.getDueAmount()>0;
    }

    public static String getBillState(Recents recent) {
        if (!isBillDue(recent)) return null;

        if (!PayType.CREDIT_CARD.value.equalsIgnoreCase(recent.getPayType()) || (StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.RU_SMS, EVENT_SOURCE.PG_DELETED_AMW, EVENT_SOURCE.CSV) && (recent.getDueAmount() == null || recent.getOriginalDueAmount() == null)) || recent.getDueAmount().equals(recent.getOriginalDueAmount()))
            return TOTAL_DUE;

        return PARTIAL_DUE;


    }

    public static Map<String, String> getAddionalInfo(Recents recent) {
        Map<String, String> additionalInfo = new HashMap<>();
        if (!StringUtils.isAllEmpty(recent.getPar(), recent.getCin(), recent.getTin())) {
            additionalInfo.put(Constants.CommonConstants.PAN_UNIQUE_REFERENCE, recent.getPar());
            additionalInfo.put(Constants.CommonConstants.BILLS_OBJ_CIN, recent.getCin());
            additionalInfo.put(Constants.CommonConstants.TIN, recent.getTin());
        }
        if (Objects.nonNull(recent.getIsNewBillIdentified())) {
            additionalInfo.put(IS_NEW_BILL_IDENTIFIED, String.valueOf(recent.getIsNewBillIdentified()));

        }
        if(DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET.equalsIgnoreCase(recent.getKey().getPlanBucket())){
            Map<String,String > extra = JsonUtils.parseJson(recent.getExtra(),Map.class);
            additionalInfo.put("data_exhaust_value",extra.get("data_exhaust_value"));
        }

        if(Objects.nonNull(recent.getReminderStatus()) && recent.getReminderStatus() == OLD_BILL_REMINDER_STATUS && Objects.nonNull(recent.getOldBillFetchDate()) && Objects.nonNull(recent.getDueDate()) && Objects.nonNull(recent.getDueAmount())){
            additionalInfo.put(PENALTY_AMOUNT,RecentUtils.getPenaltyAmount(recent));
        }

        if (!CollectionUtils.isEmpty(recent.getTxnTimes())) {
            additionalInfo.put(RecentConstants.TXN_TIMES_COUNT, String.valueOf(recent.getTxnTimes().size()));
        }

        additionalInfo.putAll(RecentUtils.getDisplayValueMap(recent));

        return additionalInfo;

    }

    public static String getPenaltyAmount(Recents recent){
        Map<String, List<String>> penaltyPercentageOperator = new HashMap<>();
        Map<String, List<String>> penaltyPercentageService = new HashMap<>();

        if(FeatureConfigCache.getInstance().getObject(ServiceNameConstants.OLD_BILL_PENALTY_PERCENTAGE_OPERATOR_WISE) != null) {
            penaltyPercentageOperator = JsonUtils.convertObjectToMap(FeatureConfigCache.getInstance().getObject(ServiceNameConstants.OLD_BILL_PENALTY_PERCENTAGE_OPERATOR_WISE));
        }
        if(FeatureConfigCache.getInstance().getObject(ServiceNameConstants.OLD_BILL_PENALTY_PERCENTAGE_SERVICE_WISE) != null) {
            penaltyPercentageService = JsonUtils.convertObjectToMap(FeatureConfigCache.getInstance().getObject(ServiceNameConstants.OLD_BILL_PENALTY_PERCENTAGE_SERVICE_WISE));
        }

        Double penaltyAmount = 0.0;
        Double tempAmount = recent.getDueAmount();
        Date tempDate = recent.getDueDate();

        //Operator wise penalty, Simple interest
        if(penaltyPercentageOperator != null && penaltyPercentageOperator.get(recent.getOperator().toLowerCase()) != null){
            List<String> monthWisePenaltyPercentage = penaltyPercentageOperator.get(recent.getOperator().toLowerCase());
            if (monthWisePenaltyPercentage != null) {
                for (String penaltyPercentage : monthWisePenaltyPercentage) {
                    penaltyAmount += tempAmount * (Double.parseDouble(penaltyPercentage) / 100);
                    tempDate = DateUtil.addMonths(tempDate, 1);
                    if (DateUtil.compareDate(tempDate, new Date()) >= 0)
                        break;
                }
            }
            //Service wise penalty, compound interest
        } else if(penaltyPercentageService != null && penaltyPercentageService.get(recent.getKey().getService()) != null) {
            List<String> monthWisePenaltyPercentage = penaltyPercentageService.get(recent.getKey().getService().toLowerCase());
            if (monthWisePenaltyPercentage != null) {
                for (String penaltyPercentage : monthWisePenaltyPercentage) {
                    penaltyAmount = tempAmount * (Double.parseDouble(penaltyPercentage) / 100);
                    tempAmount += penaltyAmount;
                    tempDate = DateUtil.addMonths(tempDate, 1);
                    if (DateUtil.compareDate(tempDate, new Date()) >= 0)
                        break;
                }
            }
        }

        return String.format("%.2f", penaltyAmount);
    }

    public static boolean isOldBillPenaltyState(Recents recent){
        Integer oldBillPenaltyDays = FeatureConfigCache.getInstance().getInteger(ServiceNameConstants.OLD_BILL_PENALTY_DAYS);
        if(oldBillPenaltyDays == null)
            oldBillPenaltyDays = 0;
        Date penaltyLastDate = DateUtil.getZeroTimeDate(DateUtils.addDays(recent.getOldBillFetchDate(), oldBillPenaltyDays));

        return DateUtil.compareDate(new Date(), penaltyLastDate) <= 0;
    }

    public static boolean isOldBillAvoidDisconnectionState(Recents recent){
        List<String> oldBillEnabledServices = new ArrayList<>();
        if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.OLD_BILL_ENABLED_SERVICES) != null) {
            oldBillEnabledServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.OLD_BILL_ENABLED_SERVICES);
        }

        Integer oldBillVisibilityDays = 0;
        String service = recent.getKey().getService();
        if(!oldBillEnabledServices.isEmpty() && oldBillEnabledServices.contains(service)) {
            if (FeatureConfigCache.getInstance().getObject(ServiceNameConstants.SMART_RECO_END_DAYS) != null) {
                Map<String, Integer> serviceWiseVisibleEndDays = (Map<String, Integer>) FeatureConfigCache.getInstance().getObject(ServiceNameConstants.SMART_RECO_END_DAYS);
                oldBillVisibilityDays = serviceWiseVisibleEndDays.getOrDefault(service, 0);
            }
        }

        Date disconnectionLastDate = DateUtil.getZeroTimeDate(DateUtils.addDays(recent.getDueDate(), oldBillVisibilityDays));

        return DateUtil.compareDate(new Date(), disconnectionLastDate) <= 0;
    }

    public static String getDynamicQuery(Long customerId, List<RecentsPrimaryKey> recentsPrimaryKeys) {
        StringBuilder sb = new StringBuilder();
        sb.append("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS + " from recents where customerid = ");
        sb.append(customerId);
        sb.append(" and (service, recharge_number, operator) in ");
        sb.append("(");
        recentsPrimaryKeys.stream().forEach(primaryKey -> {
            sb.append("(");
            sb.append(getAttributeAsString(primaryKey.getService()));
            sb.append(",");
            sb.append(getAttributeAsString(primaryKey.getRechargeNumber()));
            sb.append(",");
            sb.append(getAttributeAsString(primaryKey.getOperator()));
            sb.append("),");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");

        logger.trace("fetching from recents using query : {}", sb);

        return sb.toString();
    }

    public static String getDynamicNicknameUpdateQuery(Long customerid, List<Recents> recentPrimaryKeys, String nickname, Date updatedAt ,Integer TTL) {
        StringBuilder sb = new StringBuilder();
        sb.append("update recents using TTL " + TTL + " set nick_name_v2=");
        sb.append(getAttributeAsString(nickname));
        sb.append(", updated_at=");
        sb.append(getAttributeAsString(DateUtil.formatDate(updatedAt,Constants.CommonConstants.RECENT_CRUD_NOW_DATE_FORMAT)));
        sb.append(" where customerid=");
        sb.append(customerid);
        sb.append(" and (service, recharge_number, operator, plan_bucket) in ");
        sb.append("(");
        recentPrimaryKeys.stream().forEach(primaryKey -> {
            sb.append("(");
            sb.append(getAttributeAsString(primaryKey.getKey().getService()));
            sb.append(",");
            sb.append(getAttributeAsString(primaryKey.getKey().getRechargeNumber()));
            sb.append(",");
            sb.append(getAttributeAsString(primaryKey.getKey().getOperator()));
            sb.append(",");
            sb.append(getAttributeAsString(primaryKey.getKey().getPlanBucket()));
            sb.append("),");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");

        logger.info("update nickname query : {}", sb);

        return sb.toString();
    }

    public static String getAttributeAsString(String attribute) {
        StringBuilder sb = new StringBuilder();
        sb.append("'");
        sb.append(attribute);
        sb.append("'");
        return sb.toString();
    }

    public static boolean skipIfMCNMismatch(FrequentOrderRequest request, Recents recent) {
        return request.getRechargeNumber() != null && Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(recent.getPayType()) && !maskMCN(request.getRechargeNumber()).equalsIgnoreCase(maskMCN(recent.getMcn()));
    }


    public static String maskMCN(String mcn) {
        if (StringUtils.length(mcn) > 7) {
            return Constants.MCN_MASKED_PREFIX + mcn.substring(7);
        }
        return mcn;
    }

    public static Predicate<CustomerBill> getCustomerBillFilterPredicate(FrequentOrderRequest request) {
        Set<String> excludedRecoServices = JsonUtils.convertObjectToSetString(FeatureConfigCache.getInstance().getObject(EXCLUDED_RECO_SERVICES));
        if(!EncryptionDecision.isDataEncryptionRequired(request.getCustomerId())){
            excludedRecoServices = Collections.emptySet();
        }
        Set<String> finalExcludedRecoServices = Objects.nonNull(excludedRecoServices)? excludedRecoServices: Collections.emptySet();
        Predicate<CustomerBill> service = customerBill -> (CollectionUtils.isEmpty(request.getServices()) || CommonUtils.containIgnoreCase(request.getServices(), customerBill.getKey().getService())) && !finalExcludedRecoServices.contains(customerBill.getKey().getService());

        Predicate<CustomerBill> rechargeNumber = customerBill -> request.getRechargeNumber() == null || Service.FINANCIAL_SERVICES.value.equalsIgnoreCase(customerBill.getKey().getService()) || StringUtils.equalsIgnoreCase(customerBill.getKey().getRechargeNumber(), request.getRechargeNumber());

        Predicate<CustomerBill> operator = customerBill -> request.getOperator() == null || StringUtils.equalsIgnoreCase(customerBill.getKey().getOperator(), request.getOperator());

        return service.and(rechargeNumber).and(operator);
    }

    public static Predicate<Recents> getFetchRecentslFilterPredicate(FetchRecentsRequest request) {
        Predicate<Recents> paytype = recents ->
                StringUtils.isEmpty(request.getPaytype()) || StringUtils.equalsIgnoreCase(request.getPaytype(), recents.getPayType());

        Predicate<Recents> service = recents ->
                StringUtils.isEmpty(request.getRechargeNumber()) ||
                        !StringUtils.equalsIgnoreCase(request.getService(), Constants.FINANCIAL_SERVICES) ||
                        StringUtils.equalsIgnoreCase(maskMCN(request.getRechargeNumber()), maskMCN(recents.getMcn()));

        return paytype.and(service);
    }

    
    public static String getRecentDataStringValue(Map<String, Object> map) {
        ObjectMapper mapperObj = new ObjectMapper();
        try {
            return mapperObj.writeValueAsString(map);
        } catch (Exception e) {
            logger.info("[RecentUtils]:exception in converting recentData to string");
            return null;
        }
    }


    public static boolean isAutomaticValid(Recents recent) {
        return recent!= null && recent.getAutomaticStatus() != null && 1 == recent.getAutomaticStatus().intValue();
    }

    public static boolean isTxnAutomatic(Recents recent) {
        return Constants.ReminderConstants.AUTOMATIC_CHANNEL.equalsIgnoreCase(recent.getChannelId());
    }

    public static Map<String, Object> getRecentData(Recents recent) {
        Map<String, Object> recentDataMap = new HashMap<>();
        if (recent != null && recent.getRecentData() != null) {

            Map<String, Object> recentDataAsMap = JsonUtils.parseMapJson(recent.getRecentData());

            for (Map.Entry<String, Object> entry : recentDataAsMap.entrySet()) {
                if (Service.DTH.value.equalsIgnoreCase(recent.getKey().getService()) && entry.getKey().equalsIgnoreCase(RecentConstants.DISPLAY_VALUES) && entry.getValue() instanceof List) {
                    List<Map<String, String>> displayValues = (List<Map<String, String>>) entry.getValue();
                    displayValues = CommonUtils.collectionToStream(displayValues).filter(displayValue -> displayValue.get(RecentConstants.CUSTOM_CODE) == null).collect(Collectors.toList());
                    recentDataMap.put(entry.getKey(), displayValues);
                } else
                    recentDataMap.put(entry.getKey(), entry.getValue());
            }
        }

        return recentDataMap;
    }


    public static String getRentTFData(BillerAccountKafkaModel billerAccountResponseModel) {
        ObjectMapper mapperObj = new ObjectMapper();
        if (billerAccountResponseModel != null) {

            Map<String, Object> billerAccountDataMap = new HashMap<>();
            if (Objects.nonNull(billerAccountResponseModel.getBankAccountName())) {
                billerAccountDataMap.put("bankAccountName", billerAccountResponseModel.getBankAccountName());
            }
            if (Objects.nonNull(billerAccountResponseModel.getBankName())) {
                billerAccountDataMap.put("bankName", billerAccountResponseModel.getBankName());
            }
            if (Objects.nonNull(billerAccountResponseModel.getAccountType())) {
                billerAccountDataMap.put("accountType", billerAccountResponseModel.getAccountType());
            }

            if (Objects.nonNull(billerAccountResponseModel.getSelfTransfer())) {
                billerAccountDataMap.put("selfTransfer", billerAccountResponseModel.getSelfTransfer());
            }

            billerAccountDataMap.put("metaData", billerAccountResponseModel.getMetaData());

            billerAccountDataMap.put("tenantDetails", billerAccountResponseModel.getTenantDetailsObj());

            try {
                return mapperObj.writeValueAsString(billerAccountDataMap);
            } catch (Exception e) {
                logger.info("[RecentUtils]:exception in converting recentData to string");
                return null;
            }


        }

        return null;
    }


    public static Map<String, String> getDisplayValueMap(Recents recent) {

        Map<String, String> displayValue = new HashMap<>();
        try{
            if (recent != null && Service.DTH.value.equalsIgnoreCase(recent.getKey().getService()) && recent.getRecentData() != null) {
                Map<String, Object> recentDataAsMap = JsonUtils.parseMapJson(recent.getRecentData());
                Object displayValueObject = recentDataAsMap.get(RecentConstants.DISPLAY_VALUES);
                if (displayValueObject instanceof List) {
                    List<Map<String, Object>> displayValues = (List<Map<String, Object>>) displayValueObject;
                    Map<String, Object> displayValueMap = new HashMap<>();
                    CommonUtils.collectionToStream(displayValues).forEach(displayValueMap::putAll);
                    for (Map.Entry<String, Object> displayValueEntry : displayValueMap.entrySet()) {
                        if (RecentConstants.ADDITIONALINFO_KEYS_MAPPER.get(displayValueEntry.getKey()) != null)
                            displayValue.put(RecentConstants.ADDITIONALINFO_KEYS_MAPPER.get(displayValueEntry.getKey()), displayValueEntry.getValue().toString());
                    }

                }
            }
        }catch(Exception e){
            logger.error("issue while returning display values display values {} for rechargenumber {}",displayValue,recent.getKey().getRechargeNumber());
            throw e;
        }

        return displayValue;
    }

    public static Map<String, String> getOperatorData(Recents recent) {

        Map<String, String> operatorData = new HashMap<>();
        if (Constants.CREDIT_CARD_PAYTYPE.equalsIgnoreCase(recent.getPayType())) {
            if (!StringUtils.isEmpty(recent.getPar())) {
                operatorData.put(Constants.CREDIT_CARD.PAN_UNIQUE_REFERENCE, recent.getPar());
                operatorData.put(Constants.CREDIT_CARD.TIN, recent.getRechargeNumber4());
            } else {
                operatorData.put(Constants.CREDIT_CARD.CREDIT_CARD_ID, recent.getCin());
            }
        }
        return operatorData;
    }

    private static void pushCountToDD(String metricName, String state, StatsDClient monitoringClient) {
        String[] tags = new String[1];
        tags[0] = Constants.MetricConstants.STATE + ":" + state;
        monitoringClient.incrementCounter(metricName, tags);
    }


    public static int getNicknameLength(StatsDClient monitoringClient) {
        Integer allowedLength = FeatureConfigCache.getInstance().getInteger("nicknamelength");
        if (allowedLength == null || allowedLength == 0) {
            allowedLength = 30;
            pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG, monitoringClient);
        }
        return allowedLength;

    }

    public static boolean isNewChatH5ThemeEnabled(StatsDClient monitoringClient) {
        Boolean isNewChatH5ThemeEnabled = FeatureConfigCache.getInstance().getBoolean("enableNewChatH5Theme");
        if (Objects.nonNull(isNewChatH5ThemeEnabled)) {
            return isNewChatH5ThemeEnabled;
        }
        pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG, monitoringClient);
        return false;

    }

    public static String getPlanName(Recents recent, String payType) {

        if (!PayType.PREPAID.value.equalsIgnoreCase(payType))
            return  null;

        if (recent == null || StringUtils.equalsAnyIgnoreCase(NULL_STRING, StringUtils.trim(recent.getPlanName())))
            return Constants.Recents.DEFAULT_PLAN;

        return StringUtils.firstNonEmpty(recent.getPlanName(), Constants.Recents.DEFAULT_PLAN);
    }

    public static void prepareRecentResponse(FetchRecentsResponse fetchRecentsResponse, List<Recents> recents) {
        fetchRecentsResponse.setFetchRecentsResponseObjectList(new HashSet<>());
        Set<FetchRecentsResponseObject> fetchRecentsResponseObjects = recents.stream().map(recent -> RecentsResponseBuilder.recentToRecentsResponseObject.apply(recent)).collect(Collectors.toSet());
        fetchRecentsResponse.setFetchRecentsResponseObjectList(fetchRecentsResponseObjects);

    }
    public static Date getLastTxnTime(Recents recent){
        Date lastPendingTxn=null;
        Date lastFailureTxn=null;
        Date lastSuccessTxn=recent.getTxnTime();
        if (Objects.nonNull(recent.getLastFailureTxn())) {
            JSONObject failedTxnDetails = new JSONObject(recent.getLastFailureTxn());
            lastFailureTxn=getTxnTime(failedTxnDetails);
        }
        if (Objects.nonNull(recent.getLastPendingTxn())) {
            JSONObject pendingTxnDetails = new JSONObject(recent.getLastPendingTxn());
            lastPendingTxn=getTxnTime(pendingTxnDetails);
        }
        return DateUtil.maxDate(lastPendingTxn, DateUtil.maxDate(lastFailureTxn, lastSuccessTxn));
    }

    public static Boolean compareRecents(Recents mapRecent, Recents recent) {
        Date d1 = DateUtil.maxDate(mapRecent.getMarkAsPaidTime(), getLastTxnTime(mapRecent));
        Date d2 = DateUtil.maxDate(recent.getMarkAsPaidTime(), getLastTxnTime(recent));

        if (Objects.nonNull(d1) && Objects.nonNull(d2))
            return d1.compareTo(d2) < 0;
        else if (Objects.isNull(d1)) {
            return Boolean.TRUE;
        } else
            return Boolean.FALSE;
    }
    public static String getDecryptedRechargeNumber(String encryptedNumber) throws Exception {
        String iv = null;
        String secretKey = null;
        Object keyObj = FeatureConfigCache.getInstance().getObject("smsParsedDecryptionKey");
        ObjectMapper mapper = new ObjectMapper();
        if (Objects.nonNull(keyObj)) {
            Map<String, String> ttlmap = mapper.convertValue(keyObj, Map.class);
            for (Map.Entry<String, String> records : ttlmap.entrySet()) {
                if(records.getKey().equalsIgnoreCase("iv"))
                    iv = records.getValue();
                if(records.getKey().equalsIgnoreCase("secretKey"))
                    secretKey = records.getValue();
            }
        }
        return decrypt(encryptedNumber, secretKey, iv);
    }
    public static String decrypt(String encryptedNumber, String secretKey, String iv) throws Exception {
        byte[] encryptedBytes = hexStringToByteArray(encryptedNumber);
        byte[] keyBytes = secretKey.getBytes("UTF-8");
        byte[] ivBytes = iv.getBytes("UTF-8");
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, "UTF-8");
    }
    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }

    public static Map<String, Object> getRentTFDataFE(Recents recent) {

        Map<String, Object> rentTfData = new HashMap<>();
        if (recent != null && recent.getRentTFData() != null) {

            Map<String, Object> rentTFDataAsMap = JsonUtils.parseMapJson(recent.getRentTFData());
            if (Service.RENT_PAYMENT.value.equalsIgnoreCase(recent.getKey().getService()) || Service.BUSINESS_PAYMENT.value.equalsIgnoreCase(recent.getKey().getService()) || Service.TUITION_FEES.value.equalsIgnoreCase(recent.getKey().getService())) {
                rentTfData.put("additionalInfo",rentTFDataAsMap.get("metaData"));
                rentTfData.put("maskedPan",maskingRechargeNumber(recent.getRechargeNumber5()));
                if(Objects.nonNull(rentTFDataAsMap.get("accountType"))) {
                    rentTfData.put("accountType", AccountTypeMapper.getAccountTypeValue(Integer.parseInt(String.valueOf(rentTFDataAsMap.get("accountType")))));
                }
                if(Objects.nonNull(rentTFDataAsMap.get("metaData"))){
                    Map metaMap=new ObjectMapper().convertValue(rentTFDataAsMap.get("metaData"),Map.class);
                    rentTfData.put("address",metaMap.get("address"));

                }
                if(Objects.nonNull(rentTFDataAsMap.get("tenantDetails"))){
                    Map tenantDetailsMap=new ObjectMapper().convertValue(rentTFDataAsMap.get("tenantDetails"),Map.class);
                    if(tenantDetailsMap.containsKey("name"))rentTfData.put("name",tenantDetailsMap.get("name"));
                    if(tenantDetailsMap.containsKey("tenantName"))rentTfData.put("tenantName",tenantDetailsMap.get("tenantName"));
                }

                if(Objects.nonNull(rentTFDataAsMap.get("bankName"))) {
                    rentTfData.put("bankName",rentTFDataAsMap.get("bankName"));
                }

                if(Objects.nonNull(rentTFDataAsMap.get("bankAccountName"))) {
                    rentTfData.put("bankAccountName",rentTFDataAsMap.get("bankAccountName"));
                }
                if(Objects.nonNull(rentTFDataAsMap.get("selfTransfer"))) {
                    rentTfData.put("selfTransfer",rentTFDataAsMap.get("selfTransfer"));
                }

            }
        }
        return rentTfData;

    }

    private static String maskingRechargeNumber(String recNum){
        if(Objects.nonNull(recNum) && recNum.length()>4){
            return recNum.substring(0, Math.min(4, recNum.length())) + "XXXXX" + recNum.charAt(recNum.length() - 1);
        }
        return null;
    }

    private static boolean isRentConsentRejected(String rentConsent){
        return Objects.nonNull(rentConsent) && rentConsent.endsWith("_NO");
    }

    private static boolean isRentConsentGiven(String rentConsent){
        return Objects.nonNull(rentConsent) && rentConsent.endsWith("_YES");
    }

    public static EventState getFinalState(Recents recent, Map extras) {
        if (recent.getLastPendingTxn()!=null || recent.getLastFailureTxn() !=null) {

            Date failedTxnTime = null;
            Date pendingTxnTime = null;
            Date reminderIsAutomaticUpdateTime = null;
            Integer reminderIsAutomaticStatus = null;
            JSONObject failedTxnDetails = null;
            JSONObject pendingTxnDetails = null;


            if (Objects.nonNull(recent.getLastFailureTxn())) {
                failedTxnDetails = new JSONObject(recent.getLastFailureTxn());
                failedTxnTime = getTxnTime(failedTxnDetails);
            }
            if (Objects.nonNull(recent.getLastPendingTxn())) {
                pendingTxnDetails = new JSONObject(recent.getLastPendingTxn());
                pendingTxnTime = getTxnTime(pendingTxnDetails);
            }
            if(Objects.nonNull(extras) && extras.containsKey(SUBSCRIPTION_UPDATED_AT) && extras.containsKey(SUBSCRIPTION_UPDATED_AT)){
                reminderIsAutomaticStatus = (Integer) extras.get(SUBSCRIPTION_IS_AUTOMATIC);
                reminderIsAutomaticUpdateTime = DateUtil.stringToDate((String) extras.get(SUBSCRIPTION_UPDATED_AT), DateFormats.RECENTS_DATE_FORMAT);
            }
            Date finalTxnTime = DateUtil.maxDateAmongAll(pendingTxnTime,failedTxnTime,recent.getTxnTime(),reminderIsAutomaticUpdateTime);
            if(Objects.nonNull(finalTxnTime) && Objects.nonNull(reminderIsAutomaticUpdateTime) && reminderIsAutomaticUpdateTime.equals(finalTxnTime) && PDN_FAILURE_REMINDER_IS_AUTOMATIC_STATUS.equals(reminderIsAutomaticStatus)){
                return EventState.RECHARGE_AUTOMATIC_FAILURE;
            }
            if ((Objects.nonNull(finalTxnTime) && Objects.nonNull(failedTxnTime) && finalTxnTime.equals(failedTxnTime)) && (!isBillDue(recent) || ((recent.isOnlyTxn() || (Objects.nonNull(recent.getNewBillUpdatedAt()) && failedTxnTime.after(recent.getNewBillUpdatedAt()))) ||  recent.getNewBillUpdatedAt()==null))) {
                if(!failedTxnDetails.isNull(CHANNEL_ID) && failedTxnDetails.getString(CHANNEL_ID).equalsIgnoreCase(ReminderConstants.AUTOMATIC_CHANNEL))
                    return EventState.RECHARGE_AUTOMATIC_FAILURE;
                if(!recent.isRecoEvent()) {
                    if (RENT_PAYMENT.equalsIgnoreCase(recent.getKey().getService()) && !isRentConsentGiven(RENTCONSENT.valueOfInteger(recent.getRentConsent()).toString())) {
                        return resolveRentFailureState(recent);
                    }
                    return EventState.RECHARGE_FAILURE;
                }
            }
            if (((Objects.nonNull(finalTxnTime) && Objects.nonNull(pendingTxnTime) && finalTxnTime.equals(pendingTxnTime)) && (Objects.isNull(recent.getTxnTime()) || (Objects.nonNull(recent.getTxnTime()) &&!finalTxnTime.equals(recent.getTxnTime())))) && (Math.toIntExact((new Date().getTime() - pendingTxnTime.getTime()) / 1000) <= PENDING_TXN_VISIBILITY_DAYS) && (!isBillDue(recent) || ((recent.isOnlyTxn() || (Objects.nonNull(recent.getNewBillUpdatedAt()) && pendingTxnTime.after(recent.getNewBillUpdatedAt()))) || recent.getNewBillUpdatedAt()==null))) {
                if(!pendingTxnDetails.isNull(CHANNEL_ID) && pendingTxnDetails.getString(CHANNEL_ID).equalsIgnoreCase(ReminderConstants.AUTOMATIC_CHANNEL))
                    return EventState.RECHARGE_AUTOMATIC_PENDING;
                if(!recent.isRecoEvent()) {
                    if (RENT_PAYMENT.equalsIgnoreCase(recent.getKey().getService()) && !isRentConsentGiven(RENTCONSENT.valueOfInteger(recent.getRentConsent()).toString())) {
                        return resolveRentPendingState(recent);
                    }
                    return EventState.RECHARGE_PENDING;
                }
            }
        }  else if (!StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), EVENT_SOURCE.SMS, EVENT_SOURCE.CSV) && !Boolean.TRUE.equals(recent.getIsSavedCard())&& (recent.getOrderId()==null && recent.getLastPendingTxn()==null && recent.getLastFailureTxn() ==null) &&
                ((CommonConstants.PREPAID_PAYTYPE.equalsIgnoreCase(recent.getPayType())
                        && ((recent.getOrderId()==null && recent.getLastPendingTxn()==null && recent.getLastFailureTxn() ==null))) || !recent.isRecoEvent())) {
            if((StringUtils.equalsAnyIgnoreCase(recent.getPayType(),CommonConstants.PREPAID_PAYTYPE)
                    &&StringUtils.equalsAnyIgnoreCase(recent.getKey().getService(),SERVICE_MOBILE))
                    &&(Objects.isNull(recent.getDueAmount())||recent.getDueAmount()==0)){
                return EventState.NEW_ACCOUNT_BROWSE_PLAN;
            }
            else if (isRentPaymentService(recent)) {
                return resolveRentNewAccountState(recent);
            } else
                return  EventState.NEW_ACCOUNT;
        }
        else if(Boolean.TRUE.equals(recent.getIsNewBiller())
                && (recent.getOrderId()==null && recent.getLastPendingTxn()==null && recent.getLastFailureTxn() ==null)){
            if (isRentPaymentService(recent)) {
                return resolveRentNewAccountState(recent);
            } else
                return  EventState.NEW_ACCOUNT;
        }

        return EventState.RECHARGE_SUCCESS;
    }
    private static boolean isRentPaymentService(Recents recent) {
        return StringUtils.equalsAnyIgnoreCase(recent.getKey().getService(), RENT_PAYMENT);
    }

    private static EventState resolveRentNewAccountState(Recents recent) {
        RENTCONSENT rentConsent = RENTCONSENT.valueOfInteger(recent.getRentConsent());

        switch (rentConsent) {
            case W_NO: return EventState.NEW_ACCOUNT_NO_CONSENT;
            case W_NOTRECIEVED: return EventState.NEW_ACCOUNT_CONSENT_NOTRECEIVED;
            case W_SENT:return EventState.NEW_ACCOUNT_CONSENT_PENDING;
            case OLD_BILLER:return EventState.OLD_BILLER;
            case W_YES:
            default: return EventState.NEW_ACCOUNT;
        }
    }

    private static EventState resolveRentFailureState(Recents recent) {
        RENTCONSENT rentConsent = RENTCONSENT.valueOfInteger(recent.getRentConsent());

        switch (rentConsent) {
            case W_YES: return EventState.RECHARGE_FAILURE;
            case W_NO: return EventState.RECHARGE_FAILURE_NO_CONSENT;
            case W_NOTRECIEVED: return EventState.RECHARGE_FAILURE_CONSENT_NOTRECEIVED;
            case W_SENT:
            default: return EventState.RECHARGE_FAILURE_CONSENT_PENDING;
        }
    }

    private static EventState resolveRentPendingState(Recents recent) {
        RENTCONSENT rentConsent = RENTCONSENT.valueOfInteger(recent.getRentConsent());
        switch (rentConsent) {
            case W_YES: return EventState.RECHARGE_PENDING;
            case W_NO: return EventState.RECHARGE_PENDING_NO_CONSENT;
            case W_NOTRECIEVED: return EventState.RECHARGE_PENDING_CONSENT_NOTRECEIVED;
            case W_SENT :
            default: return EventState.RECHARGE_PENDING_CONSENT_PENDING;
        }
    }

    public static EventState deduceFailureState(JSONObject failedTxnDetails, Recents recent){
        Map extras = RecentUtils.getExtras(recent);
        String pgRespCode = "";
        boolean isRetryExhausted = false;
        if(extras!=null){
            pgRespCode = String.valueOf(extras.get(PG_RESP_CODE));
            isRetryExhausted = Boolean.parseBoolean(String.valueOf(extras.get(IS_RETRY_EXHAUST)));
        }
        String channelId = failedTxnDetails.isNull(CHANNEL_ID) ? null : failedTxnDetails.getString(CHANNEL_ID);
        boolean isAutomaticChannel = ReminderConstants.AUTOMATIC_CHANNEL.equalsIgnoreCase(channelId);
        boolean isInsufficientBalanceFailure = PG_RESP_CODES_FOR_INSUFFICIENT_BALANCE_FAILURE.contains(pgRespCode);
        if (isAutomaticChannel && isInsufficientBalanceFailure) {
            return isRetryExhausted ? EventState.RECHARGE_AUTOMATIC_PG_FAILURE : EventState.RECHARGE_AUTOMATIC_PG_FAILURE_RETRY;
        }
        if (isAutomaticChannel) {
            return EventState.RECHARGE_AUTOMATIC_FAILURE;
        }
        if (isInsufficientBalanceFailure) {
            return EventState.RECHARGE_PG_FAILURE;
        }
        if(RENT_PAYMENT.equalsIgnoreCase(recent.getKey().getService()) && !isRentConsentGiven(RENTCONSENT.valueOfInteger(recent.getRentConsent()).toString())){
            return resolveRentFailureState(recent);
        }
        return EventState.RECHARGE_FAILURE;
    }

    /*
    * This function will return the stringified JSON txn details of failed or pending eventStates
    * Return null of event state is not pending or failure
    * @param currentState The current event state.
    * @param recent The Recents object containing transaction history.
    * @return Stringified transaction details JSON or null if no matching transaction is found.
    * */
    private static String getTransactionDetailsForState(EventState currentState, Recents recent) {
        // Define set of failure states
        Set<EventState> failureStates = EnumSet.of(
                EventState.RECHARGE_FAILURE,
                EventState.RECHARGE_FAILURE_CONSENT_PENDING,
                EventState.RECHARGE_FAILURE_NO_CONSENT,
                EventState.RECHARGE_FAILURE_CONSENT_NOTRECEIVED
        );

        // Define set of pending states
        Set<EventState> pendingStates = EnumSet.of(
                EventState.RECHARGE_PENDING,
                EventState.RECHARGE_PENDING_CONSENT_PENDING,
                EventState.RECHARGE_PENDING_NO_CONSENT,
                EventState.RECHARGE_PENDING_CONSENT_NOTRECEIVED
        );
        if (failureStates.contains(currentState)) {
            return recent.getLastFailureTxn();
        } else if (pendingStates.contains(currentState)) {
            return recent.getLastPendingTxn();
        }
        return null;
    }
    /*
     * @param response final response obj in which we are setting the txnDetails.
     * @param recent The Recents object containing transaction history.
     * */
    public static void getFinalTxnDetails(FrequentOrderResponse response, Recents recent) {
        EventState currentState = response.getEventState();
        String txnDetails = getTransactionDetailsForState(currentState, recent);

        // Update response with transaction details if available
        updateResponseWithTxnDetails(response, txnDetails);
    }

    /*
     * @param response final response obj in which we are setting the txnDetails.
     * @param txnDetails stringified JSON,which contain the txn info like txn_id,txn_amt, channel id
     * */
    private static void updateResponseWithTxnDetails(FrequentOrderResponse response, String txnDetails) {
        if (!StringUtils.isEmpty(txnDetails)) {
            try {
                JSONObject details = new JSONObject(txnDetails);
                Date txnTime = getTxnTime(details);

                response.setTxnDate(DateUtil.formatDate(txnTime, DateFormats.DATE_TIME_FORMAT_2));
                response.setOrderId(details.getLong(TXN_ID));
                response.setTxnAmount(details.getDouble(TXN_AMOUNT));
                response.setChannel(details.getString(CHANNEL_ID));
            }catch (JSONException e) {
                throw new IllegalArgumentException("updateResponseWithTxnDetails Invalid transaction details JSON: " + txnDetails, e);
            }
        }

        response.setHasPaymentHistory(Objects.nonNull(response.getOrderId()));
    }

    public static Date getTxnTime(JSONObject txnDetails){
        Date txnTime = null;
        if (!txnDetails.isNull(TXN_TIME)) {
            try {
                txnTime = new SimpleDateFormat(Constants.DATE_FORMAT).parse(txnDetails.getString(TXN_TIME));
            } catch (ParseException e) {
                logger.error("[RecentUtils.getTxnTime()] :: Unable to parse the txn time");
            }
        }
        return txnTime;
    }
    public static Long getFailedOrderId(Recents recent){
        JSONObject failedTxnDetails;
        if (Objects.nonNull(recent.getLastFailureTxn())) {
            failedTxnDetails = new JSONObject(recent.getLastFailureTxn());
            return failedTxnDetails.getLong(TXN_ID);
        }
        return null;
    }
    public static Long getPendingOrderId(Recents recent){
        JSONObject pendingTxnDetails;
        if (Objects.nonNull(recent.getLastPendingTxn())) {
            pendingTxnDetails = new JSONObject(recent.getLastPendingTxn());
            return pendingTxnDetails.getLong(TXN_ID);
        }
        return null;
    }
    public static Boolean isValidPendingTxn(Recents recent){
        JSONObject pendingTxnDetails;
        if (Objects.nonNull(recent.getLastPendingTxn())) {
            pendingTxnDetails = new JSONObject(recent.getLastPendingTxn());
            Date pendingTxnTime = getTxnTime(pendingTxnDetails);
            if(pendingTxnTime!=null && Math.toIntExact((new Date().getTime() - pendingTxnTime.getTime()) / 1000) <= PENDING_TXN_VISIBILITY_DAYS)
                return true;
        }
        return false;
    }
    public static Boolean isPGDeltedCard(ReminderResponseModel reminderResponseModel){

        Map<String,String> extraInfo=null;
        if(Objects.nonNull(reminderResponseModel.getData()) && Objects.nonNull(reminderResponseModel.getData().getExtra()) && !StringUtils.isEmpty(reminderResponseModel.getData().getExtra())) {
            extraInfo =  JsonUtils.parseJson(reminderResponseModel.getData().getExtra(), Map.class);
        }

        if (!CollectionUtils.isEmpty(extraInfo) && Objects.nonNull(extraInfo.get("type")) && "PG_Deleted_AMW".equals(extraInfo.get("type")) && reminderResponseModel.getData().getReference_id() != null) {
            return true;
        }

        return false;
    }
   
     public static ArrayList<Object> createCardSkin(String skin){
        if(StringUtils.isEmpty(skin))
            return new ArrayList<>();
        Map<String, String> profilesMap = new HashMap<>();
        ArrayList<Object> mediaAssetsList = new ArrayList<>();
        JSONObject cardSkin = new JSONObject(skin);
        if(cardSkin.toMap().containsKey(SKIN_URL_KEY) && cardSkin.toMap().containsKey(SKIN_URL_SOURCE)) {
            profilesMap.put(SKIN_HDPI, cardSkin.get(SKIN_URL_KEY).toString());
            Map<String, Object> profiles = new HashMap<>();
            profiles.put(SKIN_PROFILES, profilesMap);

            Map<String, Object> mediaAsset = new HashMap<>(profiles);
            mediaAsset.put(SKIN_URL_SOURCE, cardSkin.get(SKIN_URL_SOURCE).toString());
            mediaAsset.put(SKIN_ASSETS, COMBINED_CARD_ART);

            mediaAssetsList.add(mediaAsset);
        }
        return mediaAssetsList;
    }


    public static String getPlanBucketForCDCPayload(ReminderCDC reminderCDC) {
        Map<String,String> extras = null;
        if (reminderCDC.getAfter().getExtra() != null && reminderCDC.getAfter().getExtra().getValue() != null) {
            extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
            if (Objects.nonNull(extras) && extras.containsKey("plan_bucket") && !StringUtils.isEmpty(extras.get("plan_bucket"))) {
                return extras.get("plan_bucket");
            }
        }
        return "";
    }

    public static String getPlanBucket(Recents recents) {
        Map<String,String> extras = null;
        if(!StringUtils.equals("",recents.getKey().getPlanBucket())){
            return recents.getKey().getPlanBucket();
        }

        try{
            if (recents.getExtra() != null &&  !StringUtils.isEmpty(recents.getExtra())) {
                extras = JsonUtils.parseJson(recents.getExtra(), Map.class);
                if (Objects.nonNull(extras) && extras.containsKey("plan_bucket") && !StringUtils.isEmpty(extras.get("plan_bucket"))) {
                    return extras.get("plan_bucket");
                }
            }

        }
        catch(Exception ex){
            logger.info("getPlanBucket : exception in getting Plan Bucket occurred {}", ex);

        }

        return "";
    }

    public static boolean isAutomaticRenewState(Recents recents) {
        return NumberConstants.SUBSCRIPTION_RENEW_STATE.equals(recents.getAutomaticStatus());
    }

    public static Long getOrderId(Recents recent) {
        if (recent.getLastPendingTxn() != null || recent.getLastFailureTxn() != null || recent.getTxnTime() != null) {
            Date failedTxnTime = Objects.nonNull(recent.getLastFailureTxn())?getTxnTime(new JSONObject(recent.getLastFailureTxn())):null;
            Date pendingTxnTime = Objects.nonNull(recent.getLastPendingTxn())?getTxnTime(new JSONObject(recent.getLastPendingTxn())):null;
            Date successTxnTime = recent.getTxnTime();

            Date maxDate = DateUtil.maxDate(failedTxnTime, DateUtil.maxDate(pendingTxnTime, successTxnTime));
            if (maxDate.equals(pendingTxnTime)) {
                long timeDifference = (new Date().getTime() - pendingTxnTime.getTime()) / 1000;
                if (timeDifference <= PENDING_TXN_VISIBILITY_DAYS) {
                    return RecentUtils.getPendingOrderId(recent);
                } else if (Objects.nonNull(failedTxnTime) && failedTxnTime.equals(DateUtil.maxDate(failedTxnTime, successTxnTime))) {
                    return RecentUtils.getFailedOrderId(recent);
                } else {
                    return recent.getOrderId();
                }
            } else if (maxDate.equals(failedTxnTime)) {
                return RecentUtils.getFailedOrderId(recent);
            } else {
                return recent.getOrderId();
            }
        }
        return null;
    }

    public static boolean isWhitelistedCustId(Long customerId, ServiceConfig serviceConfig) {
        List<String> whitelistedCustIds=serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer();
        if(Objects.isNull(whitelistedCustIds) || Objects.isNull(customerId)){
            return false;
        }
        return whitelistedCustIds.isEmpty() || whitelistedCustIds.contains(String.valueOf(customerId)) || isLiveForTraffic(customerId,serviceConfig);
    }

    private  static boolean isLiveForTraffic(Long customerId, ServiceConfig serviceConfig){
        Integer percentForLiveTraffic=serviceConfig.getPercentOfLiveTraffic();
        if(Objects.isNull(percentForLiveTraffic) || percentForLiveTraffic <=0 || percentForLiveTraffic >100 ){
            return false;
        }
        int inputPercent=(int) (customerId%100);
        return inputPercent < percentForLiveTraffic;
    }

    public static Map<String, Object> getGenericExtraForReminder(ReminderCDC reminderCDC) {
        Map<String, Object> extras = null;
        try {
            if (reminderCDC.getAfter().getExtra() != null && reminderCDC.getAfter().getExtra().getValue() != null) {
                extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
            }
        } catch (Exception e) {
            logger.error("[RecentUtils]:getExtraForReminder error in parsing extra: ", e);
        }
        return extras;
    }

    public static Map<String, String> getExtraForReminder(ReminderCDC reminderCDC) {
        Map<String, String> extras = null;
        try {
            if (reminderCDC.getAfter().getExtra() != null && reminderCDC.getAfter().getExtra().getValue() != null) {
                extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
            }
        } catch (Exception e) {
            logger.error("[RecentUtils]:getExtraForReminder error in parsing extra: ", e);
        }
        return extras;
    }

    public static Map<String, String> getExtraForRecents(Recents recents) {
        Map<String, String> extras = null;
        try {
            if (recents != null && recents.getExtra() != null) {
                logger.info("[RecentUtils]:getExtraForRecents parsing extra: ", recents.getExtra());
                extras = JsonUtils.parseJson(recents.getExtra(), Map.class);
            }
        } catch (Exception e) {
            logger.error("[RecentUtils]:getExtraForRecents error in parsing extra: ", e);
        }
        return extras;
    }

    public static boolean isFastagLowBalance(ReminderCDC reminderCDC) {
        Map<String, String> extras = getExtraForReminder(reminderCDC);
        if (Objects.nonNull(extras) && extras.containsKey(Constants.LOW_BALANCE_IDENTIFIER_KEY) && StringUtils.equalsIgnoreCase(extras.get(Constants.LOW_BALANCE_IDENTIFIER_KEY), Constants.FASTAG_LOW_BALANCE_OPERATOR)) {
            return true;
        }
        return false;
    }
    public static boolean isFastaglowBalanceSkippble(ReminderCDC reminderCDC){
        if (RecentUtils.isFastagLowBalance(reminderCDC)) {
            Map<String, String> extras = RecentUtils.getExtraForReminder(reminderCDC);

            if (Objects.nonNull(extras) && extras.containsKey(Constants.LOW_BALANCE_DATE_KEY) && extras.get(Constants.LOW_BALANCE_DATE_KEY) != null) {
                Date date = DateUtil.stringToDate(extras.get(Constants.LOW_BALANCE_DATE_KEY), "yyyy-MM-dd HH:mm:ss");
                if (date != null && date.before(DateUtil.hoursIncrDecr(new Date(), -24))) {
                    logger.info("Skipping fastag low balance old Date: {}", date);
                    return true;
                }
            }
            return isOlderSMS(reminderCDC);
        }
        return true; // not low balance skip it

    }

    private static boolean isOlderSMS(ReminderCDC reminderCDC) {
        Map<String, Object> extras = RecentUtils.getGenericExtraForReminder(reminderCDC);

        if (Objects.nonNull(extras) && extras.containsKey(Constants.SMS_DATE_TIME) && extras.get(Constants.SMS_DATE_TIME) != null) {
            try {
                long epochTime = Long.parseLong(extras.get(Constants.SMS_DATE_TIME).toString());
                Date smsDate = DateUtil.getZeroTimeDate(new Date(epochTime)); // Truncate time from smsDateTime
                Date currentDate = DateUtil.getZeroTimeDate(new Date()); // Truncate time from current date

                if (smsDate.before(currentDate)) {
                    logger.info("Skipping fastag low balance old Date: {}", smsDate);
                    return true;
                }
            } catch (NumberFormatException e) {
                logger.error("Invalid epoch format for SMS_DATE_TIME: {}", extras.get(Constants.SMS_DATE_TIME), e);
            }
        }
        return  false;
    }

    public static boolean isFastagService(ReminderCDC reminderCDC) {

        Boolean isFastag = false;
        try {
            if (reminderCDC.getAfter().getService() != null && reminderCDC.getAfter().getService().getValue() != null) {
                isFastag = reminderCDC.getAfter().getService().getValue().equalsIgnoreCase(FASTAG);
            }
        } catch (Exception e) {
            logger.error("[RecentUtils]:getExtraForReminder error in parsing extra: ", e);
        }
        return isFastag;
    }

    public static boolean checkExhaustEvent(ReminderCDC reminderCDC) {
        if (isFastagLowBalance(reminderCDC)) {
            return true;
        }
        return false;
    }
    
    public static boolean isFastagLowBalanceForRecents(Recents recents) {
        Map<String, String> extras = getExtraForRecents(recents);
        
        if (Objects.nonNull(extras) && extras.containsKey(Constants.LOW_BALANCE_IDENTIFIER_KEY) && StringUtils.equalsIgnoreCase(extras.get(Constants.LOW_BALANCE_IDENTIFIER_KEY), Constants.FASTAG_LOW_BALANCE_OPERATOR)) {
            return true;
        }
        return false;
    }
    
    public static Boolean checkIfPartialBillStateIsPresentInRecents(Recents recents) {
            if (recents.getExtra() != null) {
                JsonNode extraNode = JsonUtils.fromStringToJsonNode(recents.getExtra());
                return (extraNode != null && extraNode.get(Constants.PARTIAL_BILL_STATE) != null);
            }
            return false;
    }
    public static boolean isWhitelistedCustIdForMobileNonRUPersist(Long customerId, ServiceConfig serviceConfig) {
        List<String> whitelistedCustIds=serviceConfig.getWhitelistedCustIdsForMobileNonRUPersist();
        if(Objects.isNull(whitelistedCustIds) || Objects.isNull(customerId)){
            return false;
        }
        return whitelistedCustIds.isEmpty() || whitelistedCustIds.contains(String.valueOf(customerId)) || isLiveForTrafficForMobileNonRUPersist(customerId,serviceConfig);
    }

    private  static boolean isLiveForTrafficForMobileNonRUPersist(Long customerId, ServiceConfig serviceConfig) {
        Integer percentForLiveTraffic = serviceConfig.getPercentOfLiveTrafficForMobileNonRUPersist();
        if(Objects.isNull(percentForLiveTraffic) || percentForLiveTraffic <=0 || percentForLiveTraffic >100 ){
            return false;
        }
        int inputPercent=(int) (customerId%100);
        return inputPercent < percentForLiveTraffic;
    }

    public static boolean isWhitelistedCustIdForMobileNonRU(Long customerId, ServiceConfig serviceConfig) {
        List<String> whitelistedCustIds=serviceConfig.getWhitelistedCustIdsForMobilePrepaidNonRu();
        if(Objects.isNull(whitelistedCustIds) || Objects.isNull(customerId)){
            return false;
        }
        return whitelistedCustIds.isEmpty() || whitelistedCustIds.contains(String.valueOf(customerId)) || isLiveForTrafficForMobileNonRU(customerId,serviceConfig);
    }

    private  static boolean isLiveForTrafficForMobileNonRU(Long customerId, ServiceConfig serviceConfig){
        Integer percentForLiveTraffic=serviceConfig.getPercentOfLiveTrafficForNonRu();
        if(Objects.isNull(percentForLiveTraffic) || percentForLiveTraffic <=0 || percentForLiveTraffic >100 ){
            return false;
        }
        int inputPercent=(int) (customerId%100);
        return inputPercent < percentForLiveTraffic;
    }

    public static Double getFrequentOrderResponseAmount(Recents recents){
        if(EventStateHandler.getState(recents).equals(EventState.MARK_AS_PAID))
            return recents.getMarkAsPaidAmount();
        if (StringUtils.equalsIgnoreCase(recents.getPayType(),Constants.PREPAID_PAYTYPE) && BillStateTypes.AUTOMATIC_BILL_STATES.contains(BillStateHandler.getState(recents)) && Objects.nonNull(recents.getAutomaticAmount()))
            return recents.getAutomaticAmount();
        if(Objects.nonNull(recents.getDueAmount()) && !checkIfPrepaidCase(recents))
            return recents.getDueAmount() <= 0 ? 0.0 : recents.getDueAmount();
        return recents.getDueAmount();
    }
    public static Boolean isNotTxnRecord(Recents recents){
        if(Objects.isNull(recents.getOrderId())){
            if((Objects.isNull(recents.getLastFailureTxn())) && (Objects.isNull(recents.getLastFailureTxn()))){
                return true;
            }else if(Objects.isNull(getOrderIdFromPendingAndFailureTxn(recents.getLastFailureTxn())) &&
                    Objects.isNull(getOrderIdFromPendingAndFailureTxn(recents.getLastPendingTxn()))){
                return true;
            }
        }
        return false;
    }

    public static Long getOrderIdFromPendingAndFailureTxn(String lastTxn){
        Long orderId = null;
        if(Objects.nonNull(lastTxn)){
            Map<String,Object> lastTxnDetails = JsonUtils.parseJson(lastTxn,Map.class);
            orderId = (Long) lastTxnDetails.get(Constants.TXN_ID);
        }
        return orderId;

    }
    public static String getCreatedSourceFromExtra(Recents recents) {
        Map<String,String> extras = null;

        try{
            if (recents.getExtra() != null &&  !StringUtils.isEmpty(recents.getExtra())) {
                extras = JsonUtils.parseJson(recents.getExtra(), Map.class);
                if (Objects.nonNull(extras) && extras.containsKey("created_source") && !StringUtils.isEmpty(extras.get("created_source"))) {
                    return extras.get("created_source");
                }
            }

        }
        catch(Exception ex){
            logger.info("getCreatedSourceFromExtra : exception in getting Plan Bucket occurred  {}", ex);

        }

        return null;
    }

    public static Boolean checkForDataExhaustPlanInReminderCDC(ReminderCDC reminderCDC) {
        try {
            if (reminderCDC.getAfter().getExtra() != null && reminderCDC.getAfter().getExtra().getValue() != null) {
                Map<String, String> extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
                if (extras != null && extras.containsKey(DATA_EXHAUST.EXHAUSTED_DATE)) {
                    Date exhaustedDate = DateUtil.stringToDate(extras.get(DATA_EXHAUST.EXHAUSTED_DATE), Constants.DATA_EXHAUST.EXHAUSTED_DATE_FORMAT);
                    return (exhaustedDate != null && !exhaustedDate.after(DateUtil.getEndOfDayDate(new Date())));
                }
            }
        } catch(Exception ex){
            logger.info("checkForDataExhaustPlanInReminderCDC, exception occurred, reminderCDC -  {}", reminderCDC);
        }
        return false;
    }


    public static boolean isNotSpecialRechargeSMSCard(Recents recent) {
        return isNotTxnRecord(recent) &&
                (Constants.EVENTSOURCE_SMS.equalsIgnoreCase(recent.getEventSource()) || Constants.EVENTSOURCE_SMS.equalsIgnoreCase(RecentUtils.getCreatedSourceFromExtra(recent)))
                && StringUtils.equalsAnyIgnoreCase(recent.getPayType(), "prepaid")
                && StringUtils.equalsAnyIgnoreCase(recent.getKey().getService(), "mobile")
                && !(StringUtils.equalsAnyIgnoreCase(getPlanBucket(recent), "Special Recharge"));

    }

    public static boolean shouldExcludeRecentBasedOnService(Recents recents) {
        if(recents == null || recents.getKey() == null || recents.getKey().getService() == null) {
            return false;
        }

        List<String> excludeServices = FeatureConfigCache.getInstance().getList(EXCLUDE_RECENT_SERVICES);
        return Objects.nonNull(excludeServices) && excludeServices.contains(recents.getKey().getService().toLowerCase());
    }

    public static boolean checkForLowBalanceForElectricityPrepaid(Recents recent) {
        Integer prepaidElectricityLowBalanceThreshold = (Integer) FeatureConfigCache.getInstance().getObject(ServiceNameConstants.PREPAID_ELECTRICITY_LOW_BALANCE_THRESHOLD);
        if (Objects.nonNull(prepaidElectricityLowBalanceThreshold)) {
            return recent.getDueAmount() != null && recent.getDueAmount() > 0 && recent.getDueAmount() < prepaidElectricityLowBalanceThreshold;
        } else {
            return recent.getDueAmount() != null && recent.getDueAmount() > 0 && recent.getDueAmount() < NumberConstants.PREPAID_ELECTRICITY_LOW_BALANCE_THRESHOLD;
        }
    }

    public static boolean checkForBillDateAfterTxnDate(Recents recent) {
        return Objects.nonNull(recent.getBillDate()) && Objects.nonNull(recent.getTxnTime()) && recent.getBillDate().after(recent.getTxnTime());
    }

    public static String getTrimmedRechargeNumber(String rechargeNumber) {
        return rechargeNumber == null ? "" : rechargeNumber.replaceFirst("^0+(?!$)", "");
    }

    public static String generateDummyRNForLoan(String customerId, String operator) {
        return customerId + "_" + operator.toLowerCase().replaceAll(" ","_");
    }

    public static boolean isRUPartialBill(String service, Date dueDate, List<String> ruPartialBillRecoServices) {
        Date minDate = DateUtil.stringToDate(MIN_RECENT_DUE_DATE_THRESHOLD, DateFormats.DATE_FORMAT_2);
        Date maxDate = DateUtil.stringToDate(MAX_RECENT_DUE_DATE_THRESHOLD, DateFormats.DATE_FORMAT_2);
        if(minDate == null || maxDate == null) {
            return false;
        }
        return ruPartialBillRecoServices.contains(service) && (dueDate == null || dueDate.before(minDate) || dueDate.after(maxDate));
    }

    public static boolean isServicePrepaid(String service) {
        List<String> prepaidServices = FeatureConfigCache.getInstance().getList(ServiceNameConstants.PREPAID_SERVICES);
        if (CollectionUtils.isEmpty(prepaidServices)) {
            return false;
        }
        return prepaidServices.stream().anyMatch(s -> StringUtils.equalsIgnoreCase(s, service));
    }

    public static boolean isOperatorPrepaid(String operator) {
        List<String> prepaidOperators = FeatureConfigCache.getInstance().getList(ServiceNameConstants.PREPAID_OPERATORS);
        if (CollectionUtils.isEmpty(prepaidOperators)) {
            return false;
        }
        return prepaidOperators.stream().anyMatch(o -> StringUtils.equalsIgnoreCase(o, operator));
    }

    public static boolean isPrepaidQualified(String service, String operator, Map<String,String> extra) {
        Boolean isPrepaidFlowEnabled = FeatureConfigCache.getInstance().getBoolean(IS_PREPAID_ENABLED);
        if (isPrepaidFlowEnabled == null || !isPrepaidFlowEnabled) {
            return false;
        }
        if(checkIfRequestContainsPrepaidFlag(extra)){
            return true;
        }
        return (RecentUtils.isServicePrepaid(service) || RecentUtils.isOperatorPrepaid(operator));
    }

    public static boolean checkIfRequestContainsPrepaidFlag(Map<String, String> extra) {
        return (extra != null && extra.containsKey(IS_PREPAID_KEY) && NumberConstants.PREPAID_VALUE.equals(extra.get(IS_PREPAID_KEY)));
    }

}
