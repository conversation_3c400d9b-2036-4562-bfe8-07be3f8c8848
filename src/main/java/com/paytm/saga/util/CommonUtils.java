package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Stream;

public class CommonUtils {

    public static <T> Stream<T> collectionToParallelStream(Collection<T> collection) {
        return Optional.ofNullable(collection)
                .map(Collection::parallelStream)
                .orElseGet(Stream::empty);
    }

    public static <T> Stream<T> collectionToStream(Collection<T> collection) {
        return Optional.ofNullable(collection)
                .map(Collection::stream)
                .orElseGet(Stream::empty);
    }

    public static boolean containIgnoreCase(List<String> services, String service) {
        if (!CollectionUtils.isEmpty(services)) {
            for (String serviceName : services) {
                if(StringUtils.equalsIgnoreCase(serviceName, service))
                    return true;
            }
        }
        return false;
    }

       public static HistoryPage getDefaultHistoryResponse(GetHistoryPageDTO getHistoryPageDTO) {
        ChannelDetail channelDetails = new ChannelDetail();
        channelDetails.setCustomerId(getHistoryPageDTO.getCustomerId());
        channelDetails.setRechargeNumber(getHistoryPageDTO.getRecharge_number());
        channelDetails.setOperator(getHistoryPageDTO.getOperator());
        channelDetails.setService(getHistoryPageDTO.getService());
        HistoryPage historyPage = new HistoryPage(new ArrayList<>(), 0, null, false, "SUCCESS",
                200, channelDetails);
        historyPage.setCode(200);
        historyPage.setStatus("SUCCESS");
        historyPage.setThemeDetails(new ThemeResponse());
        return historyPage;
    }

    public static Map<String, DropOffResponse> getDefaultDropOffResponse() {
        return new HashMap<>();
    }

    public static String getRechargeNumberBasedOnRules(String recharge_number_1, String operator) {
        if (StringUtils.equalsIgnoreCase(Constants.AIRTEL_LANDLINE, operator) && recharge_number_1 != null) {
            if (recharge_number_1.startsWith(Constants.NUMBER_CONSTANTS.PLUS_NINTY_ONE))
                recharge_number_1 = StringUtils.substringAfter(recharge_number_1, Constants.NUMBER_CONSTANTS.PLUS_NINTY_ONE);
            if (!recharge_number_1.startsWith(Constants.NUMBER_CONSTANTS.ZERO))
                return Constants.NUMBER_CONSTANTS.ZERO + recharge_number_1;
        }
        return recharge_number_1;
    }

    public static  Set<String> convertToSetCaseInsensitive(List<String> elements) {
        Set<String> elementSet = new HashSet<>();
        for (String element : elements) {
            elementSet.add(StringUtils.lowerCase(element));
        }
        return elementSet;
    }

}
