package com.paytm.saga.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FeatureConfigCache;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.paytm.saga.util.DateUtil.getEndOfDayDate;

public class TTLUtils {
    private static final Integer MIN_TTL = 365 * 24 * 60 * 60;

    private TTLUtils() {
    }

    private static Logger logger = LogManager.getLogger(TTLUtils.class);

    public static Integer getTTL(String service) {
        Integer ttl = getTTLFromConfig(service);
        if (ttl < MIN_TTL)
            ttl = MIN_TTL;                                        //ttl can not be lower than 1 year
        logger.info("TTLUtils.getTTL : TTL for service {} is {}", service, ttl);
        return ttl;
    }

    private static Integer getTTLFromConfig(String service){
        Integer ttl = null;
        try{
            Object ttlObj = FeatureConfigCache.getInstance().getObject("categoryWiseTtl");
            ObjectMapper mapper = new ObjectMapper();
            if (Objects.nonNull(ttlObj)) {
                Map<String, Integer> ttlmap = mapper.convertValue(ttlObj, Map.class);
                for (Map.Entry<String, Integer> records : ttlmap.entrySet()) {
                    String category = records.getKey();
                    Integer categoryTtl = records.getValue();
                    if (service.equalsIgnoreCase(category)) {
                        ttl = categoryTtl;
                        break;
                    }
                }
            }
        }catch(Exception e){
            logger.error("TTLUtils:: getTTLFromConfig Getting exception while fetching feature config for ttl error",e);
        }

        if (ttl == null) {
            ttl = Constants.DEFAULT_TTL;                                         //ttl 18 months will be used as fallback
        }
        return ttl;
    }

    private static Integer getTTLFromConfigForDataExhaust(){
        Integer ttl = null;
        try{
            Object ttlObj = FeatureConfigCache.getInstance().getObject("categoryWiseTtl");
            ObjectMapper mapper = new ObjectMapper();
            if (Objects.nonNull(ttlObj)) {
                Map<String, Integer> ttlmap = mapper.convertValue(ttlObj, Map.class);
                for (Map.Entry<String, Integer> records : ttlmap.entrySet()) {
                    String category = records.getKey();
                    Integer categoryTtl = records.getValue();
                    if ("data_exhaust_minutes".equalsIgnoreCase(category)) {
                        ttl = categoryTtl;
                        break;
                    }
                }
            }
        }catch(Exception e){
            logger.error("TTLUtils:: getTTLFromConfig Getting exception while fetching feature config for ttl error",e);
        }
        return ttl;
    }
    /*
     * BASE_TTL=service based ttl if exists otherwise Constants.DEFAULT_TTL
     * CASE 1: when txnTime and createdAtTime are null return BASE_TTL
     * CASE 2: when txnTime is null return BASE_TTL- difference between current time and createdAtTime
     * CASE 3: when createdAtTime is null return BASE_TTL- difference between current time and txn time
     * */
    public static Integer getTTL(String service, Date txnTime,Date createdAtTime) {
        Integer ttl=getTTLFromConfig(service);
        if(Objects.nonNull(txnTime)){
            ttl= ttl+Math.toIntExact((txnTime.getTime()-new Date().getTime()) / 1000);
        }else if(Objects.nonNull(createdAtTime)){
            ttl= ttl+Math.toIntExact((createdAtTime.getTime()-new Date().getTime()) / 1000);
        }
        logger.info("TTLUtils.getTTL : TTL for service {} is {}", service, ttl);
        return ttl;
    }

    public static Integer getMidnightTTL() {
        Date date = new Date();
        try {
            Date endOfDayDate = DateUtil.getEndOfDayDate(date);
            Integer ttl = getTTLFromConfigForDataExhaust();
            if(Objects.nonNull(ttl) && ttl>0){
                return Math.toIntExact((DateUtil.addMinutes(date,ttl).getTime() / 1000 - date.getTime() / 1000));
            }
            return Math.toIntExact((endOfDayDate.getTime() / 1000 - date.getTime() / 1000));
        }catch (Exception ex){
            logger.error("TTLUtils, getMidnightTTL : exception occurred", ex);
            return 0;
        }
    }


    public static Integer getPlanValidityTTL(Date validity_expiry_date){
        if(validity_expiry_date == null){
            return Constants.CommonConstants.PLAN_VALIDITY_DEFAULT_TTL;
        }
        Integer secondsDiff = Math.abs(Math.toIntExact(validity_expiry_date.getTime()/1000 - (new Date()).getTime()/1000));
        return secondsDiff + 7*24*60*60;
    }
}
