package com.paytm.saga.util;

import com.paytm.saga.exception.ConsentException;
import com.paytm.saga.model.ConsentValidityModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Component
@Slf4j
public class ConsentUtil {

    private final AESUtil aesUtil;
    private static final Logger logger = LogManager.getLogger(ConsentUtil.class);

    @Autowired
    public ConsentUtil(AESUtil aesUtil) {
        this.aesUtil = aesUtil;

    }

    public static ConsentValidityModel parseCsv(String message) {
        try {
            String[] parts = message.split(",", -1);

            return new ConsentValidityModel(
                    Long.parseLong(parts[0]),
                    parts[1],
                    parts[2].isEmpty() ? null : parts[2],
                    parts[3].isEmpty() ? null : parts[3],
                    parts[4]
            );
        } catch (Exception e) {
            throw new ConsentException("Error parsing CSV message: " + message, "CSV_PARSING_ERROR");
        }
    }

    public static Date getConsentValidTill(String initTimestamp) {
        try {
            Instant instant;
            if (initTimestamp.contains("T") && initTimestamp.endsWith("Z")) {
                // Handle ISO_INSTANT format
                instant = Instant.parse(initTimestamp);
            } else {
                // Handle custom format
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                LocalDateTime localDateTime = LocalDateTime.parse(initTimestamp, formatter);
                instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
            }
            return Date.from(instant);
        } catch (Exception e) {
            log.error("Error parsing initTimestamp: {}", e.getMessage());
            throw new ConsentException("Error parsing initTimestamp: " + initTimestamp, "TIMESTAMP_PARSING_ERROR");
        }
    }
}
