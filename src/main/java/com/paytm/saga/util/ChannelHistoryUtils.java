package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ReminderHistory;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.paytm.saga.common.constant.Constants.CommonConstants.OMS_PENNYDROP_STATUS;

public class ChannelHistoryUtils {

    private static final Logger logger = LogManager.getLogger(ChannelHistoryUtils.class);

    public static String getBillDate(ChannelHistory channelHistory) {
        Map<String, String> billsObj = channelHistory.getBillsObj();
        if (billsObj != null) {
            return billsObj.get(Constants.ReminderConstants.BILL_DATE);
        }

        return null;
    }

    public static String getDueDate(ChannelHistory channelHistory) {
        Map<String, String> billsObj = channelHistory.getBillsObj();
        if (billsObj != null) {
            return billsObj.get(Constants.ReminderConstants.DUE_DATE);
        }

        return null;
    }

    public static Boolean isAutomaticTransaction(ChannelHistory channelHistory) {
        Map<String, String> billsObj = channelHistory.getBillsObj();
        if (billsObj != null) {
            return Constants.ReminderConstants.AUTOMATIC_CHANNEL.equalsIgnoreCase(billsObj.get(Constants.CommonConstants.CHANNEL));
        }

        return Boolean.FALSE;
    }

    public static String getOperatorLabel(ChannelHistory channelHistory) {
        String op = channelHistory.getOperator();
        if (op != null) {
            String response = "";
            String str = StringUtils.capitalize(op);
            String tokens[] = str.split(" ");
            int i = 0;
            for (String token : tokens) {
                if (i == 0 && token.length() == 2) {
                    response = response + token.toUpperCase();
                } else if (token.length() == 2) {
                    response = response + " " + token.toUpperCase();
                } else {
                    response = (response + " " + StringUtils.capitalize(token)).trim();
                }
            }
            return response;
        } else {
            return "operator";
        }
    }

    public static boolean checkForLatestStateOrders(ChannelHistory channelHistory, Long previousRechargeOrderId) {
        if (previousRechargeOrderId == null || !previousRechargeOrderId.equals(channelHistory.getOrderId())) {
            return Boolean.TRUE;
        } else
            return Boolean.FALSE;
    }

    public static boolean isPartialPendingTransaction(List<ChannelHistory> channelHistories, ReminderHistory currentBill) {
        if (!CollectionUtils.isEmpty(channelHistories)) {
            ChannelHistory channelHistory = channelHistories.get(0);
            String txnStatus = OMSToRechargeStatus.getRechargeStatusByOMSStatus(
                    channelHistory.getStatus(), channelHistory.getInResponseCode(),
                    channelHistory.getPaymentStatus());

            if (channelHistory.getEventType().equals(EventTypes.RECHARGE)
                    && (txnStatus != null && (txnStatus.equals("PENDING") || txnStatus.equals("PAYMENT_PENDING")))
                    && currentBill.getAmount().compareTo(Double.parseDouble(channelHistory.getAmount())) > 0) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public static boolean ignoreBill(List<ChannelHistory> channelHistories, ReminderHistory currentBill) {

        if (!CollectionUtils.isEmpty(channelHistories)) {
            ChannelHistory channelHistory = channelHistories.get(0);
            String txnStatus = OMSToRechargeStatus.getRechargeStatusByOMSStatus(
                    channelHistory.getStatus(), channelHistory.getInResponseCode(),
                    channelHistory.getPaymentStatus());

            if ((channelHistory.getEventType().equals(EventTypes.RECHARGE)
                    && (txnStatus != null && (txnStatus.equals("PENDING") || txnStatus.equals("PAYMENT_PENDING")))
                    && currentBill.getAmount().compareTo(Double.parseDouble(channelHistory.getAmount())) <= 0)
                    || EventTypes.MARKED_AS_PAID.equalsIgnoreCase(channelHistory.getEventType())) {

                Map<String, String> billsObj = channelHistory.getBillsObj();
                if (billsObj != null) {
                    String dueDate = billsObj.get(Constants.OMSConstants.DUE_DATE);
                    Date date = DateUtil.stringToDate(dueDate, DateFormats.DATE_TIME_FORMAT_2);
                    if (DateUtil.compareDateWithoutTime(currentBill.getDue_date(), date) == 0) {
                        return Boolean.TRUE;
                    }
                }

            }
        }

        return Boolean.FALSE;
    }

    public static boolean isFinalizedState(ChannelHistory channelHistory, Date finalizationDate) {

        if (!channelHistory.isFinalisedState() && channelHistory.getTransactionTime().before(finalizationDate)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public static boolean ignoreAutomatic(List<ChannelHistory> channelHistories, ReminderHistory currentBill) {

        if (!CollectionUtils.isEmpty(channelHistories)) {
            ChannelHistory channelHistory = channelHistories.get(0);
            String dueDate = getDueDate(channelHistory);
            if (dueDate != null) {
                Date date = DateUtil.stringToDate(dueDate, DateFormats.DATE_TIME_FORMAT_2);
                logger.debug("ignoreAutomatic date is {}", date);
                if (isAutomaticTransaction(channelHistory) && DateUtil.compareDateWithoutTime(date, currentBill.getDue_date()) == 0) {
                    return Boolean.TRUE;
                }
            }
        }

        if (1 != currentBill.getIs_automatic()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public static void updateHeadersAndFooters(HistoryView historyView, List<ViewElementInfo> viewElements) {
        List<ViewElementInfo> headers = viewElements.stream().filter(viewElement -> viewElement.getType().contains("header")).collect(Collectors.toList());
        List<ViewElementInfo> footers = viewElements.stream().filter(viewElement -> viewElement.getType().contains("footer")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(headers))
            historyView.setHeadings(headers);
        if (!CollectionUtils.isEmpty(footers))
            historyView.setFooter(footers);
    }

    public static boolean checkForPennyDrop(ChannelHistory channelHistory) {
        List<String> pennyDropStatus = Arrays.asList(OMS_PENNYDROP_STATUS);
        if (pennyDropStatus.contains(channelHistory.getStatus()))
            return true;
        else
            return false;
    }

    public static double getMinDueAmount(ChannelHistory channelHistory) {
        Map<String, String> billsObj = channelHistory.getBillsObj();
        if (billsObj != null && billsObj.containsKey(Constants.ReminderConstants.MIN_DUE_AMOUNT)) {
            return Double.valueOf(billsObj.get(Constants.ReminderConstants.MIN_DUE_AMOUNT));
        }

        return 0;
    }
}
