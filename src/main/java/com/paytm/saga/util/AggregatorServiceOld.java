package com.paytm.saga.util;

//@Component
public class AggregatorServiceOld {
//	@Autowired
//	private final ChannelHistoryFinalizedPaginationService channelHistoryFinalizedPaginationService;
//	@Autowired
//	private final ScratchCardService scratchCardService;
//	@Autowired
//	private final MNPHistoryService mnpHistoryService;
//
//	@Autowired
//	private final EntityToDTOConverter entityToDTOConverter;
//
//	@Autowired
//	public AggregatorServiceOld(final ChannelHistoryFinalizedPaginationService channelHistoryFinalizedPaginationService,
//			EntityToDTOConverter entityToDTOConverter, ScratchCardService scratchCardService,
//			MNPHistoryService mnpHistoryService) {
//		this.channelHistoryFinalizedPaginationService = channelHistoryFinalizedPaginationService;
//		this.entityToDTOConverter = entityToDTOConverter;
//		this.scratchCardService = scratchCardService;
//		this.mnpHistoryService = mnpHistoryService;
//	}
//
//	public HistoryPage aggregateHistoryInfo(List<ChannelHistory> channelHistories, String pagingState) {
//		return this.convertToDto(channelHistories, pagingState);
//		// channelHistories.stream().map(this::convertToDto).collect(Collectors.toList());
//	}
//
//	private HistoryPage convertToDto(List<ChannelHistory> channelHistories, String pagingState) {
//		EventsList eventsList = this.filterOutLatestStateOfOrders(channelHistories);
//		List<ChannelHistory> rechargeEvents = eventsList.getRechargeEvents();
//		List<HistoryView> historyViews = entityToDTOConverter.formatHistoryViewToDTO(rechargeEvents);
//
//		ResponsePage<ChannelHistoryFinalized> channelHistoryFinalizPage = channelHistoryFinalizedPaginationService
//				.getPageOfHistory(10 - rechargeEvents.size(), pagingState);
//		if (channelHistoryFinalizPage.getCount() > 0) {
//			historyViews.addAll(
//					entityToDTOConverter.formatFinalizeHistoryViewToDTO(channelHistoryFinalizPage.getContent()));
//		}
//
//		List<ChannelHistory> PlanValidityEvents = eventsList.getPlanValidityEvents();
//		historyViews = this.convertRechargeCardToRecommendedCard(this.filterExpiringPlans(PlanValidityEvents),
//				historyViews);
//		historyViews = this.addCashBackInfo(historyViews);
//		historyViews = this.addMnpCards(historyViews, "customerId", "recharge_number");
//		historyViews = this.addDateInfoCard(historyViews);
//		ChannelDetail channelDetail=new ChannelDetail();
//		HistoryPage historyPage = new HistoryPage(historyViews,historyViews.size(),
//				channelHistoryFinalizPage.getPagingState(), channelHistoryFinalizPage.getHasNext(),"SUCCESS",200,channelDetail);
//
//		return historyPage;
//	}
//
//	private List<HistoryView> addDateInfoCard(List<HistoryView> historyViews) {
//		List<HistoryView> finalResponse = new ArrayList<HistoryView>();
//		try {
//			for (HistoryView channelHistory : historyViews) {
//				Date todayDate = DateUtil.timestampToDate(new Date());
//				Date cardDate = DateUtil.timestampToDate(DateUtil.stringToDate(channelHistory.getCreatedDate(), "yyyy-MM-dd HH:mm:ss"));
//				Date previousDate = null;
//				if (DateUtil.stringToDate(channelHistory.getCreatedDate(), "yyyy-MM-dd HH:mm:ss").after(DateUtil.timestampToDate(new Date())) && previousDate == null) {
//					// add today card
//
//				} else if (previousDate == null || !(DateUtil.timestampToDate(previousDate)
//						.equals(DateUtil.stringToDate(channelHistory.getCreatedDate(), "yyyy-MM-dd HH:mm:ss")))) {
//					// add date card
//
//				}
//				finalResponse.add(channelHistory);
//				previousDate = DateUtil.stringToDate(channelHistory.getCreatedDate(), "yyyy-MM-dd HH:mm:ss");
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//		return finalResponse;
//	}
//
//	private List<ScratchCardHistory> getActiveCashBackInfo(List<ScratchCardHistory> scratchCardHistories) {
//		List<ScratchCardHistory> finalResponse = new ArrayList<ScratchCardHistory>();
//		BigInteger previousScratchCardId = null;
//		for (ScratchCardHistory scratchCardHistory : scratchCardHistories) {
//			BigInteger scratchCardId = scratchCardHistory.getKey().getScratchcardid();
//			if ((previousScratchCardId != scratchCardId)) {
//				previousScratchCardId = scratchCardId;
//				if (scratchCardHistory.getStatus() == "UNSCRATCHED"
//						|| scratchCardHistory.getStatus() == "INITIALIZED") {
//					finalResponse.add(scratchCardHistory);
//				}
//			}
//
//		}
//		return finalResponse;
//	}
//
//	private List<HistoryView> addCashBackInfo(List<HistoryView> historyViews) {
//		List<HistoryView> finalResponse = new ArrayList<HistoryView>();
//		for (HistoryView historyView : historyViews) {
//			finalResponse.add(historyView);
//			String orderId = historyView.getOrderId();
//			List<ScratchCardHistory> cashbackInfos = scratchCardService.getOrderById(orderId);
//			List<ScratchCardHistory> filteredResponse = this.getActiveCashBackInfo(cashbackInfos);
//			// convert ScratchCardHistory into ChannelHistory and add it in finalResponse
//			List<HistoryView> formattedCashbacknfo = null;
//			finalResponse.addAll(formattedCashbacknfo);
//		}
//		return finalResponse;
//	}
//
//	private Map<String, Boolean> convertMnpInfoToMap(List<MNPHistory> mnpHistories) {
//		Map<String, Boolean> mnpMap = new HashMap<String, Boolean>();
//		for (MNPHistory mnpHistory : mnpHistories) {
//			String key = mnpHistory.getMnpHistoryPrimaryKey().getCircle() + ":"
//					+ mnpHistory.getMnpHistoryPrimaryKey().getOperator() + ":"
//					+ mnpHistory.getMnpHistoryPrimaryKey().getPayType() + ":"
//					+ mnpHistory.getMnpHistoryPrimaryKey().getUpdatedDate();
//			mnpMap.put(key, true);
//		}
//		return mnpMap;
//	}
//
//	// any special handling needed for vodafonev2
//	private List<HistoryView> addMnpCards(List<HistoryView> historyViews, String customerId, String rechargeNumber) {
//		List<MNPHistory> mnpHistories = mnpHistoryService.getMnpInfoByCustomerAndRecharge(rechargeNumber);
//		List<HistoryView> finalResponse = new ArrayList<HistoryView>();
//		Map<String, Boolean> mnpMap = this.convertMnpInfoToMap(mnpHistories);
//		HistoryView currentInfo = historyViews.get(0);
//		for (HistoryView historyView : historyViews) {
//			String circle = historyView.getCircle();
//			String payType = historyView.getPayType();
//			String operator = historyView.getOperator();
//			String key = circle + ":" + operator + ":" + payType + ":" + historyView.getCreatedDate();
//
//			if (mnpMap.containsKey(key)) { 
//				if ((currentInfo.getCircle() == circle) && (currentInfo.getOperator() == operator)
//						&& (currentInfo.getPayType() == payType)) {
//					//no need to ignore displayvalues and CTA
//				}else {
//					
//				}
//
//				// add mnp card
//			}
//			finalResponse.add(historyView);
//		}
//		return finalResponse;
//	}
//
//	private Map<String, Date> filterExpiringPlans(List<ChannelHistory> planExpiryList) {
//		// sort planExpiryList in asc by date order
//		//
//		planExpiryList.sort(Comparator.comparing(ChannelHistory::getTransactionTime));
//		Map<String, Date> map = new HashMap<String, Date>();
//		for (ChannelHistory channelHistory : planExpiryList) {
//			String key = channelHistory.getOperator() + ":" + channelHistory.getCircle() + ":"
//					+ channelHistory.getPlanbucket();
//			if (map.containsKey(key)) {
//				if (channelHistory.getTransactionTime().after(DateUtil.dateIncrDecr(new Date(), -3))
//						&& channelHistory.getTransactionTime().before(DateUtil.dateIncrDecr(new Date(), 3))) {
//					map.put(key, channelHistory.getTransactionTime());// setting new plan expiry
//				} else {
//					map.remove(key);
//				}
//			} else if (channelHistory.getTransactionTime().after(DateUtil.dateIncrDecr(new Date(), -3))
//					&& channelHistory.getTransactionTime().before(DateUtil.dateIncrDecr(new Date(), 3))) {
//				map.put(key, channelHistory.getTransactionTime());// setting new plan expiry
//			}
//		}
//		return map;
//	}
//
//	private List<HistoryView> convertRechargeCardToRecommendedCard(Map<String, Date> expiringPlans,
//			List<HistoryView> historyViews) {
//		Set<String> keys = expiringPlans.keySet();
//		for (String key : keys) {
//			String planInfo[] = key.split(":");
//			String circle = planInfo[0];
//			String operator = planInfo[1];
//			String planBucket = planInfo[2];
//			for (HistoryView historyView : historyViews) {
//				if (historyView.getStatus() == "SUCCESS") {
//					if (circle == historyView.getCircle() && operator == historyView.getOperator()
//							&& planBucket == historyView.getPlanBucket()) {
//						// convert recharge card to recommendation card
//					}
//				}
//
//			}
//		}
//		return historyViews;
//	}
//
//	private EventsList filterOutLatestStateOfOrders(List<ChannelHistory> channelHistories) {
//		EventsList eventsList = new EventsList();
//		String previousRechargeOrderId = null;
//		List<ChannelHistory> rechargeHistoriesFilteredData = new ArrayList<ChannelHistory>();
//		List<ChannelHistory> planExipryHistoriesFilteredData = new ArrayList<ChannelHistory>();
//		for (ChannelHistory channelHistory : channelHistories) {
//			if (channelHistory.getEventType() == "RECHARGE") {
//				String orderId = channelHistory.getKey().getOrderId();
//				if (previousRechargeOrderId == null || orderId != previousRechargeOrderId) {
//					rechargeHistoriesFilteredData.add(channelHistory);
//				}
//				previousRechargeOrderId = orderId;
//			} else if (channelHistory.getEventType() == "MARK_AS_PAID") {
//				if (channelHistory.getTransactionTime().after(DateUtil.dateIncrDecr(new Date(), -4))) {
//					planExipryHistoriesFilteredData.add(channelHistory);
//				}
//			}
//		}
//		eventsList.setPlanValidityEvents(planExipryHistoriesFilteredData);
//		eventsList.setRechargeEvents(rechargeHistoriesFilteredData);
//		return eventsList;
//	}
//
//	
//
}
