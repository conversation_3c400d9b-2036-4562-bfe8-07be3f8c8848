package com.paytm.saga.util;

import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

public class CsvUtil {
    private static final Logger logger = LogManager.getLogger(CsvUtil.class);

    public static <T> String toCSV(List<T> list, Class<T> c) {
        String csv;
        CsvMapper csvMapper = new CsvMapper();
        try {
            csvMapper.disable(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY);
            CsvSchema schema = csvMapper.schemaFor(c)
                    .withHeader()
                    .withColumnSeparator(',')
                    .withLineSeparator("\n");

            csv = csvMapper.writer(schema)
                    .writeValueAsString(list);
        } catch (Exception e) {
            logger.info("[CsvUtils]: Exception occures in converting to CSV, Exception={}", e.getMessage());
            return null;
        }
        return csv;


    }


}
