package com.paytm.saga.util;

//@Component
public class EntityToDTOConverter {
//
//	public List<HistoryView> formatHistoryViewToDTO(List<ChannelHistory> channelHistories) {
//		List<HistoryView> historyViews = new ArrayList<HistoryView>();
//		for (ChannelHistory channelHistory : channelHistories) {
//			HistoryView historyView = this.convertHistoryViewEntityToDTO(channelHistory);
//			historyViews.add(historyView);
//		}
//		return historyViews;
//	}
//
//	public List<ChannelHistory> formatFinalizeHistoryViewToDTO(List<ChannelHistoryFinalized> channelHistories) {
//		List<HistoryView> historyViews = new ArrayList<HistoryView>();
//		for (ChannelHistoryFinalized channelHistory : channelHistories) {
//			HistoryView historyView = this.convertFinalizedHistoryViewEntityToDTO(channelHistory);
//			historyViews.add(historyView);
//		}
//		return null;
//	}
//
//	private HistoryView convertHistoryViewEntityToDTO(ChannelHistory channelHistory) {
//		HistoryView historyView = new HistoryView();
//		historyView.setThemeType(this.getThemeType(channelHistory.getEventType(), channelHistory.getPaytype(),
//				channelHistory.getStatus()));
//		historyView.setCreatedDate(DateUtil.dateFormatter(channelHistory.getTransactionTime(), "yyyy-MM-dd HH:mm:ss"));
//		historyView.setAmount(channelHistory.getAmount());
//		historyView.setCardId(channelHistory.getMsgId().toString());
//		historyView.setStatus(channelHistory.getStatus());
//		historyView.setOrderId(channelHistory.getKey().getOrderId());
//		historyView.setPlanBucket(channelHistory.getPlanbucket());
//		historyView.setCarddeeplink("");
//		historyView.setCircle(channelHistory.getCircle());
//		historyView.setOperator(channelHistory.getOperator());
//		historyView.setPayType(channelHistory.getPaytype());
//
//		return historyView;
//	}
//
//	private HistoryView convertFinalizedHistoryViewEntityToDTO(ChannelHistoryFinalized channelHistory) {
//		HistoryView historyView = new HistoryView();
//		historyView.setThemeType(this.getThemeType(channelHistory.getEventType(), channelHistory.getPaytype(),
//				channelHistory.getStatus()));
//		historyView.setCreatedDate(DateUtil.dateFormatter(channelHistory.getTransactionTime(), "yyyy-MM-dd HH:mm:ss"));
//		historyView.setAmount(channelHistory.getAmount());
//		historyView.setCardId(channelHistory.getMsgId().toString());
//		historyView.setStatus(channelHistory.getStatus());
//		historyView.setOrderId(channelHistory.getKey().getOrderId());
//		historyView.setPlanBucket(channelHistory.getPlanBucket());
//		historyView.setCarddeeplink("");
//		historyView.setCircle(channelHistory.getCircle());
//		historyView.setOperator(channelHistory.getOperator());
//		historyView.setPayType(channelHistory.getPaytype());
//		return historyView;
//	}
//
//	private String getThemeType(String eventType, String paytype, String status) {
//		return null;
//	}
}
