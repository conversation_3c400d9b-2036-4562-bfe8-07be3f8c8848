package com.paytm.saga.util;

import com.paytm.recharges.custom_logger.service.ServiceWrapper;
import com.paytm.saga.dto.OMSItemModel;
import com.paytm.saga.dto.OMSResponseModel;
import com.paytm.saga.dto.ReminderResponseModel;
import com.paytm.saga.dto.ValidationKafkaResponseModel;
import com.paytm.saga.dto.cdc.NonPaytmCDC;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.ReminderHistory;

import java.util.Objects;

public class ServiceWrapperUtil {
	private ServiceWrapperUtil() {}
	public static ServiceWrapper findServiceWrapper(Object object) {
		String service = null;
		if(Objects.nonNull(object)) {
			if(object instanceof  String) {
				service = (String) object;
			} else if(object instanceof Recents) {
				service = findServiceRecents((Recents) object);
			} else if(object instanceof ReminderResponseModel) {
				service = findServiceReminderResponseModel((ReminderResponseModel) object);
			} else if(object instanceof ReminderCDC) {
				service = findServiceReminderCDC((ReminderCDC) object);
			} else if(object instanceof OMSItemModel) {
				service = findServiceOMSItemModel((OMSItemModel) object);
			} else if(object instanceof OMSResponseModel) {
				service = findServiceOMSResponseModel((OMSResponseModel) object);
			} else if(object instanceof NonPaytmCDC) {
				service = findServiceNonPaytmCDC((NonPaytmCDC) object);
			} else if(object instanceof ReminderHistory) {
				service = findServiceReminderHistory((ReminderHistory) object);
			} else if(object instanceof ValidationKafkaResponseModel) {
				service = findServiceValidationKafkaResponseModel((ValidationKafkaResponseModel) object);
			}
		}
		return new ServiceWrapper(service);
	}
	private static String  findServiceRecents(Recents recents) {
		String service = null;
		if(Objects.nonNull(recents) && Objects.nonNull(recents.getKey()) && Objects.nonNull(recents.getKey().getService()) ) {
			service = recents.getKey().getService();
		}
		return service;
	}

	private static String findServiceReminderResponseModel(ReminderResponseModel reminderResponseModel) {
		String service = null;
		if(Objects.nonNull(reminderResponseModel) && Objects.nonNull(reminderResponseModel.getData()) && Objects.nonNull(reminderResponseModel.getData().getService())) {
			service = reminderResponseModel.getData().getService();
		}
		return service;
	}

	private static String findServiceReminderCDC(ReminderCDC reminderCDC) {
		String service = null;
		if(Objects.nonNull(reminderCDC) && Objects.nonNull(reminderCDC.getAfter()) && Objects.nonNull(reminderCDC.getAfter().getService())) {
			service = reminderCDC.getAfter().getService().getValue();
		}
		return service;
	}

	private static String findServiceOMSResponseModel(OMSResponseModel omsResponseModel) {
		String service = null;
		if(Objects.nonNull(omsResponseModel) && Objects.nonNull(omsResponseModel.getItems()) && !omsResponseModel.getItems().isEmpty() && Objects.nonNull(omsResponseModel.getItems().get(0))) {
			service = findServiceOMSItemModel(omsResponseModel.getItems().get(0));
		}
		return service;
	}
	private static String findServiceOMSItemModel(OMSItemModel omsItemModel) {
		String service = null;
		if(Objects.nonNull(omsItemModel) && Objects.nonNull(omsItemModel.getProduct()) && Objects.nonNull(omsItemModel.getProduct().getAttributes()) && Objects.nonNull(omsItemModel.getProduct().getAttributes().getService()) ) {
			service = omsItemModel.getProduct().getAttributes().getService();
		}
		return service;
	}

	private static String findServiceNonPaytmCDC(NonPaytmCDC nonPaytmCDC) {
		String service = null;
		if(Objects.nonNull(nonPaytmCDC) && Objects.nonNull(nonPaytmCDC.getAfter()) && Objects.nonNull(nonPaytmCDC.getAfter().getService())) {
			service = nonPaytmCDC.getAfter().getService().getValue();
		}
		return service;
	}

	private static String findServiceReminderHistory(ReminderHistory reminderHistory) {
		String service = null;
		if(Objects.nonNull(reminderHistory) && Objects.nonNull(reminderHistory.getService())) {
			service = reminderHistory.getService();
		}
		return service;
	}

	private static String findServiceValidationKafkaResponseModel(ValidationKafkaResponseModel validationKafkaResponseModel) {
		String service = null;
		if(Objects.nonNull(validationKafkaResponseModel) && Objects.nonNull(validationKafkaResponseModel.getProductInfo_service())) {
			service = validationKafkaResponseModel.getProductInfo_service();
		}
		return service;
	}

}
