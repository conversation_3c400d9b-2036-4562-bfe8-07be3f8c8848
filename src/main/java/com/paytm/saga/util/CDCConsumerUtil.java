package com.paytm.saga.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.SMSRecoveryPacket;
import com.paytm.saga.dto.cdc.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.IS_PREPAID_KEY;
import static com.paytm.saga.common.constant.Constants.PARTIAL_BILL_DATE;

@Service
public class CDCConsumerUtil {

    private static Logger logger = LogManager.getLogger(CDCConsumerUtil.class);
    private static MetricsHelper metricsHelper;

    @Autowired
    public CDCConsumerUtil(@NonNull MetricsHelper metricsHelper) {
        this.metricsHelper = metricsHelper;
    }

    public static ReminderCDC convertSmsPayloadToReminderCdc(NonPaytmCDC nonPaytmCDC) {
        ReminderCDC reminderCDC = new ReminderCDC();
        ReminderAfter reminderAfter = new ReminderAfter();
        reminderCDC.setAfter(reminderAfter);
        boolean isPartialBill = false;
        try {
            BeanUtils.copyProperties(nonPaytmCDC, reminderCDC);
            BeanUtils.copyProperties(nonPaytmCDC.getAfter(), reminderCDC.getAfter());
            DateFormat inputFormat1 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
            DateFormat inputFormat2 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            DateFormat inputFormat3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            inputFormat1.setLenient(false);
            inputFormat2.setLenient(false);
            inputFormat3.setLenient(false);
            Map<String, String> extras = null;
            if (Objects.nonNull(nonPaytmCDC.getAfter().getExtra()) && Objects.nonNull(nonPaytmCDC.getAfter().getExtra().getValue())) {
                extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
                if (extras != null) {
                    if (extras.containsKey("recon_id"))
                        reminderCDC.getAfter().setReconId(extras.get("recon_id"));
                }
            }
            updateDueDate(nonPaytmCDC, extras, inputFormat1, inputFormat2, inputFormat3, reminderCDC);

            if (nonPaytmCDC.getAfter().getBillDate() != null && nonPaytmCDC.getAfter().getBillDate().getValue() != null) {
                Date billDate = dateFormatter(inputFormat1, inputFormat2, inputFormat3, nonPaytmCDC.getAfter().getBillDate().getValue());
                LongEntity billDateEntity = new LongEntity();
                if (billDate != null)
                    billDateEntity.setValue(billDate.getTime());
                else billDateEntity.setValue(null);
                billDateEntity.setSet(nonPaytmCDC.getAfter().getBillDate().getSet());
                billDateEntity.setDeletionTs(nonPaytmCDC.getAfter().getBillDate().getDeletionTs());
                reminderCDC.getAfter().setBillDate(billDateEntity);
            }
            if (nonPaytmCDC.getAfter().getPaymentDate() != null && nonPaytmCDC.getAfter().getPaymentDate().getValue() != null) {
                Date paymentDate = dateFormatter(inputFormat1, inputFormat2, inputFormat3, nonPaytmCDC.getAfter().getPaymentDate().getValue());
                LongEntity paymentDateEntity = new LongEntity();
                if (paymentDate != null)
                    paymentDateEntity.setValue(paymentDate.getTime());
                else paymentDateEntity.setValue(null);
                paymentDateEntity.setSet(nonPaytmCDC.getAfter().getPaymentDate().getSet());
                paymentDateEntity.setDeletionTs(nonPaytmCDC.getAfter().getPaymentDate().getDeletionTs());
                reminderCDC.getAfter().setPaymentDate(paymentDateEntity);
            }
            StringEntity eventSourceEntity = new StringEntity();
            eventSourceEntity.setValue(getEventSource(reminderCDC));
            eventSourceEntity.setSet(true);
            eventSourceEntity.setDeletionTs(null);
            reminderCDC.getAfter().setEventSource(eventSourceEntity);
            updatePrepaidFlag(reminderCDC);
        } catch (Exception e) {
            logger.error("[CDCReminderListener.convertSmsPayloadToReminderCdc] :: Error while parsing sms data to reminder cdc data, Exception : {} ", e);
        }
        return reminderCDC;
    }

    private static void updateDueDate(NonPaytmCDC nonPaytmCDC, Map<String, String> extras, DateFormat inputFormat1, DateFormat inputFormat2, DateFormat inputFormat3, ReminderCDC reminderCDC) {
        // in case of partial bills, partialBillDate is considered as due date
        Date dueDate = null;
        if (extras != null && extras.containsKey(Constants.PARTIAL_BILL_STATE) && extras.get(Constants.PARTIAL_BILL_STATE) != null && nonPaytmCDC.getAfter().getService() != null && nonPaytmCDC.getAfter().getService().getValue().equalsIgnoreCase(Constants.SERVICE_MOBILE)) {
            if (!StringUtils.isEmpty(extras.get(PARTIAL_BILL_DATE))) {
                metricsHelper.pushToDD("valid_due_date_for_partial_bill", "recharge_saga");
                dueDate = dateFormatter(inputFormat1, inputFormat2, inputFormat3, extras.get(PARTIAL_BILL_DATE));
            } else {
                metricsHelper.pushToDD("empty_due_date_for_partial_bill", "recharge_saga");
                dueDate = new Date();
                logger.warn("updateDueDate, due date is not present while parsing sms data to reminder cdc data, extras - {}", extras);
            }
        } else if (nonPaytmCDC.getAfter().getDueDate() != null && nonPaytmCDC.getAfter().getDueDate().getValue() != null)
            dueDate = dateFormatter(inputFormat1, inputFormat2, inputFormat3, nonPaytmCDC.getAfter().getDueDate().getValue());
        LongEntity dueDateEntity = new LongEntity();
        if (dueDate != null)
            dueDateEntity.setValue(dueDate.getTime());
        else
            dueDateEntity.setValue(null);
        dueDateEntity.setSet(nonPaytmCDC.getAfter().getDueDate() == null ? null : nonPaytmCDC.getAfter().getDueDate().getSet());
        dueDateEntity.setDeletionTs(nonPaytmCDC.getAfter().getDueDate() == null ? null : nonPaytmCDC.getAfter().getDueDate().getDeletionTs());
        reminderCDC.getAfter().setDueDate(dueDateEntity);
    }

    public static Date dateFormatter(DateFormat inputFormat1, DateFormat inputFormat2, DateFormat inputFormat3, String date) {
        Date newDate = null;
        try {
            newDate = inputFormat1.parse(date);
        } catch (Exception e) {
            //logger.debug("[CDCReminderListener.dateFormatter] : Unable to parse the date {} through formatter {}",date,inputFormat1);
            try {
                newDate = inputFormat2.parse(date);
            } catch (Exception e1) {
                //logger.debug("[CDCReminderListener.dateFormatter] : Unable to parse the date {} through formatter {}",date,inputFormat2);
                try {
                    newDate = inputFormat3.parse(date);
                } catch (Exception e2) {
                    //logger.debug("[CDCReminderListener.dateFormatter] : Unable to parse the date {} through formatter {}",date,inputFormat3);
                }
            }
        }
        if (newDate != null) {
            return newDate;
        }
        logger.error("[CDCReminderListener.dateFormatter] : Unable to parse the date {}", date);
        return null;
    }

    public static String getEventSource(ReminderCDC reminderCDC) {
        Map<String, String> extras = null;
        Map<String, Object> customerOtherInfo = null;
        if (Objects.nonNull(reminderCDC.getAfter().getExtra()) && Objects.nonNull(reminderCDC.getAfter().getExtra().getValue())) {
            try {
                extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
            } catch (Exception e) {
                logger.error("[CDCReminderListener.getEventSource] Unable to parse extras due to, error ", e);
            }
            try {
                if (reminderCDC.getAfter().getCustomerOtherInfo() != null) {
                    customerOtherInfo = JsonUtils.parseJson(reminderCDC.getAfter().getCustomerOtherInfo().getValue(), Map.class);
                    if (Objects.nonNull(extras) && Objects.nonNull(customerOtherInfo) && customerOtherInfo.containsKey("sms_date_time")) {
                        extras.put("sms_date_time", customerOtherInfo.get("sms_date_time").toString());
                        reminderCDC.getAfter().getExtra().setValue(JsonUtils.serialiseJson(extras));
                    }
                }
            } catch (Exception e) {
                logger.error("[CDCReminderListener.getEventSource] Unable to parse customerOtherInfo due to, error ", e);
            }
            try {
                if (Objects.nonNull(extras) && reminderCDC.getAfter().getBillFetchDate() != null && reminderCDC.getAfter().getBillFetchDate().getValue() != null) {
                    extras.put("billFetchDate", reminderCDC.getAfter().getBillFetchDate().getValue().toString());
                    reminderCDC.getAfter().getExtra().setValue(JsonUtils.serialiseJson(extras));
                }
            } catch (Exception e) {
                logger.error("[CDCReminderListener.getEventSource] Unable to parse customerOtherInfo due to, error ", e);
            }
            if (Objects.nonNull(extras) && extras.containsKey(Constants.CREATED_SOURCE)) {
                if (extras.get(Constants.CREATED_SOURCE).equalsIgnoreCase(Constants.VALIDATION_SYNC))
                    return Constants.EVENTSOURCE_VALIDATION;
                else if (extras.get(Constants.CREATED_SOURCE).equalsIgnoreCase(Constants.EVENT_SOURCE.CSV))
                    return Constants.EVENT_SOURCE.CSV;

            }
        }
        return Constants.EVENTSOURCE_SMS;
    }

    public static void updatePrepaidFlag(ReminderCDC reminderCDC) {
        Map<String, Object> extras = null;
        if (Objects.nonNull(reminderCDC.getAfter().getExtra()) && Objects.nonNull(reminderCDC.getAfter().getExtra().getValue())) {
            try {
                extras = JsonUtils.parseJson(reminderCDC.getAfter().getExtra().getValue(), Map.class);
            } catch (Exception e) {
                logger.error("[CDCReminderListener.updatePrepaidFlag] Unable to parse extras due to, error ", e);
            }
            try {
                if (extras != null && extras.containsKey(IS_PREPAID_KEY)) {
                    String isPrepaid = (String) extras.get(IS_PREPAID_KEY);
                    if ("1".equalsIgnoreCase(isPrepaid)) {
                        extras.put(IS_PREPAID_KEY, Boolean.TRUE);
                    } else if ("0".equalsIgnoreCase(isPrepaid)) {
                        extras.put(IS_PREPAID_KEY, Boolean.FALSE);
                    }
                    reminderCDC.getAfter().setExtra(new StringEntity(JsonUtils.serialiseJson(extras)));
                }
            } catch (JsonProcessingException e) {
                logger.error("[CDCReminderListener.updatePrepaidFlag] Unable to parse extras due to, error ", e);
                throw new RuntimeException(e);
            }
        }
    }

    public static NonPaytmCDC convertRecoveryCodeToCDCFormat(SMSRecoveryPacket smsRecoveryPacket) {
        NonPaytmCDC nonPaytmCDC = new NonPaytmCDC();
        nonPaytmCDC.setOp(smsRecoveryPacket.getCdcEventType());
        NonPaytmAfter nonPaytmAfter = new NonPaytmAfter();
        nonPaytmCDC.setAfter(nonPaytmAfter);
        nonPaytmAfter.setCircle(convertStringToStringEntity(smsRecoveryPacket.getCircle()));
        nonPaytmAfter.setBillDate(convertStringToStringEntity(smsRecoveryPacket.getBillDate()));
        nonPaytmAfter.setOperator(convertStringToStringEntity(smsRecoveryPacket.getOperator()));
        nonPaytmAfter.setAmount(convertDoubleToDoubleEntity(smsRecoveryPacket.getAmount()));
        nonPaytmAfter.setCustomerId(convertLongToLongEntity(smsRecoveryPacket.getCustomerId()));
        nonPaytmAfter.setService(convertStringToStringEntity(smsRecoveryPacket.getService()));
        nonPaytmAfter.setDueDate(convertStringToStringEntity(smsRecoveryPacket.getDueDate()));
        nonPaytmAfter.setRechargeNumber(convertStringToStringEntity(smsRecoveryPacket.getRechargeNumber()));
        nonPaytmAfter.setAdditionalInfo(convertStringToStringEntity(smsRecoveryPacket.getAdditionalInfo()));
        nonPaytmAfter.setBankName(convertStringToStringEntity(smsRecoveryPacket.getBankName()));
        nonPaytmAfter.setCardNetwork(convertStringToStringEntity(smsRecoveryPacket.getCardNetwork()));
        nonPaytmAfter.setExtra(convertStringToStringEntity(smsRecoveryPacket.getExtra()));
        nonPaytmAfter.setCustomerOtherInfo(convertStringToStringEntity(smsRecoveryPacket.getCustomerOtherInfo()));
        nonPaytmAfter.setNotificationStatus(convertIntegerToIntegerEntity(smsRecoveryPacket.getNotificationStatus()));
        nonPaytmAfter.setPaytype(convertStringToStringEntity(smsRecoveryPacket.getPayType()));
        nonPaytmAfter.setProductId(convertLongToLongEntity(smsRecoveryPacket.getProductId()));
        nonPaytmAfter.setStatus(convertIntegerToIntegerEntity(smsRecoveryPacket.getStatus()));
        nonPaytmAfter.setPaymentDate(convertStringToStringEntity(smsRecoveryPacket.getPaymentDate()));
        if (Objects.nonNull(smsRecoveryPacket.getRemindLaterDate()))
            nonPaytmAfter.setRemindLaterDate(convertLongToLongEntity(DateUtil.stringToDate(smsRecoveryPacket.getRemindLaterDate(), "yyyy-MM-dd HH:mm:ss").getTime()));
//        nonPaytmAfter.setCreateAt();
//        nonPaytmAfter.setUpdateAt();
        if (Objects.nonNull(smsRecoveryPacket.getCreatedAt()))
            nonPaytmAfter.setCreatedAt(convertLongToLongEntity(DateUtil.stringToDate(smsRecoveryPacket.getCreatedAt(), "yyyy-MM-dd HH:mm:ss").getTime()));
        if (Objects.nonNull(smsRecoveryPacket.getUpdatedAt()))
            nonPaytmAfter.setUpdatedAt(convertLongToLongEntity(DateUtil.stringToDate(smsRecoveryPacket.getUpdatedAt(), "yyyy-MM-dd HH:mm:ss").getTime()));
        nonPaytmAfter.setUserData(convertStringToStringEntity(smsRecoveryPacket.getUserData()));
        nonPaytmAfter.setOldBillFetchDate(convertStringToStringEntity(smsRecoveryPacket.getOldBillFetchDate()));
        return nonPaytmCDC;
    }

    private static StringEntity convertStringToStringEntity(String str) {
        if (Objects.nonNull(str)) {
            StringEntity stringEntity = new StringEntity();
            stringEntity.setValue(str);
            stringEntity.setSet(true);
            return stringEntity;
        }
        return null;
    }

    private static DoubleEntity convertDoubleToDoubleEntity(Double number) {
        if (Objects.nonNull(number)) {
            DoubleEntity doubleEntity = new DoubleEntity();
            doubleEntity.setValue(number);
            doubleEntity.setSet(true);
            return doubleEntity;
        }
        return null;
    }

    private static LongEntity convertLongToLongEntity(Long number) {
        if (Objects.nonNull(number)) {
            LongEntity longEntity = new LongEntity();
            longEntity.setValue(number);
            longEntity.setSet(true);
            return longEntity;
        }
        return null;
    }

    private static IntegerEntity convertIntegerToIntegerEntity(Integer number) {
        if (Objects.nonNull(number)) {
            IntegerEntity integerEntity = new IntegerEntity();
            integerEntity.setValue(number);
            integerEntity.setSet(true);
            return integerEntity;
        }
        return null;
    }

}
