package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.DropOffBillsObject;
import com.paytm.saga.dto.DropOffResponse;
import com.paytm.saga.enums.BillState;
import com.paytm.saga.enums.PayType;
import com.paytm.saga.model.Recents;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static com.paytm.saga.common.constant.Constants.OLD_BILL_REMINDER_STATUS;

public class BillStateHandler {

    public static BillState getState(Recents recent) {

        String eventType = "";
        Double dueAmount = null;
        Double minDueAmount = null;
        Date date = null;
        if(Objects.nonNull(recent)) {
            eventType = recent.getEventSource();
            dueAmount = recent.getDueAmount();
            minDueAmount = recent.getMinDueAmount();
            date = recent.getDueDate();
        }

        if(RecentUtils.checkIfPrepaidCase(recent)){
            if(dueAmount == null)
                return BillState.AMOUNT_NULL;
            else if (dueAmount < 0)
                return BillState.AMOUNT_NEGATIVE;
        }

        if(Objects.nonNull(recent) && Objects.nonNull(recent.getReminderStatus())  && recent.getReminderStatus() == OLD_BILL_REMINDER_STATUS && Objects.nonNull(recent.getOldBillFetchDate()) && recent.getDueAmount() != null && recent.getDueAmount() > 0 && Objects.nonNull(recent.getDueDate()) &&  RecentUtils.isOldBillAvoidDisconnectionState(recent)){
            if(recent.getIsMarkAsPaid() != null && recent.getIsMarkAsPaid())
                return BillState.NO_DUE;

            return BillState.ALREADY_EXPIRED;
        }

        if(StringUtils.equalsAnyIgnoreCase(eventType, Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.RU_SMS,Constants.EVENT_SOURCE.PG_DELETED_AMW,Constants.EVENT_SOURCE.CSV) && (date == null)) {
            if(RecentUtils.isMarkAsPaid(recent)) {
                return BillState.NO_DUE;
            } else if ((Objects.nonNull(dueAmount) && (dueAmount > 0.0)) || (Objects.nonNull(minDueAmount) && (minDueAmount > 0.0))){
                return BillState.NO_DATE;
            } else {
                return BillState.NO_DATE_NO_AMOUNT;
            }
        }

        if (recent == null || recent.isOnlyTxn() || !RecentUtils.isBillDue(recent)) {
            if(Objects.nonNull(recent) && Objects.nonNull(recent.getKey()) && FINANCIAL_SERVICES.equalsIgnoreCase(recent.getKey().getService()) && Objects.nonNull(recent.getCurrentOutstandingAmount()) && recent.getCurrentOutstandingAmount() > 0) {
                return BillState.NO_DUE_OUT_AMT;
            } else {
                return BillState.NO_DUE;
            }
        }

        return getState(recent.getDueDate(),recent.getAutomaticDate(), recent.getPayType(), RecentUtils.isAutomaticValid(recent));

    }

    /*
    public static BillState getState(DropOffResponse dropOff) {
        Date dueDate = null;
        if (!CollectionUtils.isEmpty(dropOff.getBills())) {
            DropOffBillsObject dropOffBillsObject = dropOff.getBills().get(0);
            dueDate = DateUtil.stringToDate(StringUtils.firstNonEmpty(dropOffBillsObject.getDue_date(), dropOffBillsObject.getExpiry()), DateFormats.DATE_TIME_FORMAT_2);
        }


        getState(dueDate, dropOff.getPaytype(), DropOffUtils.isTxnAutomatic());


    }
    */

    private static BillState getState(Date dueDate, Date automaticDate, String payType, boolean isAutomatic) {

        if (dueDate == null)
            return BillState.NO_DUE;

        LocalDateTime dueDateLocal = DateUtil.convertToLocalDateTime(dueDate);

        if (!PayType.PREPAID.value.equalsIgnoreCase(payType)) {
            dueDateLocal = dueDateLocal.toLocalDate().atTime(LocalTime.MAX);
        }

        long dayDiffDueDate = DateUtil.getDayDiffBetweenDates(LocalDateTime.now(), dueDateLocal);
        if (isAutomatic) {
            return getAutomaticRelatedBillState(automaticDate,dayDiffDueDate);
        }
        if (dayDiffDueDate < 0) {

            return BillState.ALREADY_EXPIRED;

        } else if (dayDiffDueDate == 0) {

            if (Constants.PREPAID_PAYTYPE.equalsIgnoreCase(payType) && dueDateLocal.isBefore(LocalDateTime.now())) {
                return BillState.ALREADY_EXPIRED_TODAY;
            }

            return BillState.EXPIRES_TODAY;

        } else if (dayDiffDueDate >= 2) {
            return BillState.WILL_EXPIRE;
        } else if (dayDiffDueDate == 1) {

            return BillState.EXPIRES_TOMORROW;
        }

        return BillState.NO_DUE;
    }

    public static BillState getState(DropOffResponse dropOff, boolean isAutomatic) {
        DropOffBillsObject dropOffBillObject = dropOff.getBills().get(0);

        String expiryDays = StringUtils.firstNonEmpty(dropOffBillObject.getDue_date(), dropOffBillObject.getExpiry());

        return getState(DateUtil.stringToDate(expiryDays, DateFormats.DATE_TIME_FORMAT_2),null, dropOff.getPaytype(), isAutomatic);
    }

    public static BillState getPartialBillState(Recents recent) {
        Map extras = RecentUtils.getExtras(recent);
        if(extras!= null){
            String partialBillState = RecentUtils.getPartialBill(extras);
            if(Objects.equals(partialBillState, Constants.PARTIAL_BILL_STATE_CONSTANTS.EXPIRED)){
                return BillState.EXPIRED;
            }
            else if (Objects.equals(partialBillState, Constants.PARTIAL_BILL_STATE_CONSTANTS.EXPIRING_SOON)){
                return BillState.EXPIRING_SOON;
            }
            else if(Objects.equals(partialBillState, Constants.PARTIAL_BILL_STATE_CONSTANTS.INCOMING_STOPPED)){
                return BillState.INCOMING_STOPPED;
            }
        }
        return null;
    }

    public static BillState getAutomaticRelatedBillState(Date automaticDate, long dayDiffDueDate){
        if(Objects.nonNull(automaticDate)) {
            LocalDateTime automaticDateLocal = DateUtil.convertToLocalDateTime(automaticDate);
            long dayDiffAutomaticDate = DateUtil.getDayDiffBetweenDates(LocalDateTime.now(), automaticDateLocal);
            if (dayDiffAutomaticDate == 0) {
                return BillState.EXPIRES_TODAY_AUTOMATIC;
            }
            if (dayDiffAutomaticDate >= 2) {
                return BillState.WILL_EXPIRE_AUTOMATIC;
            }
            if(dayDiffAutomaticDate == 1){
                return BillState.EXPIRES_TOMORROW_AUTOMATIC;
            }
        }
        else{
            if(dayDiffDueDate<0){
                return BillState.ALREADY_EXPIRED;
            }

        }
        return BillState.WILL_EXPIRE;
    }
}
