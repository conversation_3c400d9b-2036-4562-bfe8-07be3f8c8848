package com.paytm.saga.util;



import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.util.Base64;
import java.util.Objects;
import javax.crypto.Cipher;

import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.service.RecentsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.crypto.generators.OpenSSLPBEParametersGenerator;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;


public class DecryptionUtil {
    private static final Logger logger = LogManager.getLogger(DecryptionUtil.class);



    public static String getEncryptedText(String decryptedText)  {

        //String SECRET_KEY = "hJWIJVgX+BddLbbLsUznOfE1FSvpS3ZhMaQdHVHPdNw=";
        String SECRET_KEY = (String) FeatureConfigCache.getInstance().getObject("billerDecryptionKey");
        byte[] encryptedBytes=null;
        try{
            byte[] decryptedBytes = decryptedText.getBytes("UTF-8");
            OpenSSLPBEParametersGenerator pbeGenerator = new OpenSSLPBEParametersGenerator();
            pbeGenerator.init(SECRET_KEY.getBytes(StandardCharsets.UTF_8), new byte[0]);
            ParametersWithIV parameters = (ParametersWithIV) pbeGenerator.generateDerivedParameters(256, 0);
            KeyParameter keyParam = (KeyParameter)parameters.getParameters();
            SecretKeySpec secretKey = new SecretKeySpec(keyParam.getKey(), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            encryptedBytes= cipher.doFinal(decryptedBytes);

        }
        catch(RuntimeException|UnsupportedEncodingException| NoSuchAlgorithmException|NoSuchPaddingException|IllegalBlockSizeException|BadPaddingException|InvalidKeyException ex ){
          logger.error("DecryptionUtil.getEncryptedText :: Exception while encrypting::",ex);
        }

        if(Objects.nonNull(encryptedBytes))
            return Hex.encodeHexString(encryptedBytes).toUpperCase();
        return null;

    }




    public static String getDecryptedText(String encryptedText)  {

        if(StringUtils.isEmpty(encryptedText)){
            return encryptedText;
        }


        String SECRET_KEY = (String) FeatureConfigCache.getInstance().getObject("billerDecryptionKey");
        byte[] encryptedBytes=null;
        try{
            byte[] decryptedBytes = Hex.decodeHex(encryptedText.toCharArray());
            OpenSSLPBEParametersGenerator pbeGenerator = new OpenSSLPBEParametersGenerator();
            pbeGenerator.init(SECRET_KEY.getBytes(StandardCharsets.UTF_8), new byte[0]);
            ParametersWithIV parameters = (ParametersWithIV) pbeGenerator.generateDerivedParameters(256, 0);
            KeyParameter keyParam = (KeyParameter)parameters.getParameters();
            SecretKeySpec secretKey = new SecretKeySpec(keyParam.getKey(), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            encryptedBytes = cipher.doFinal(decryptedBytes);

        }
        catch(RuntimeException | NoSuchAlgorithmException | NoSuchPaddingException | IllegalBlockSizeException | BadPaddingException | InvalidKeyException |
              DecoderException ex ){
            logger.error("DecryptionUtil.getDecryptedText :: Exception while decrypting::",ex);
        }
        if(Objects.nonNull(encryptedBytes))
            return new String(encryptedBytes, StandardCharsets.UTF_8);
        return null;



    }







}

