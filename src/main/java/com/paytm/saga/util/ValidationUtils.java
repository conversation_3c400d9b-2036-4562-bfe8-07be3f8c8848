package com.paytm.saga.util;

import java.util.Set;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

import org.apache.commons.collections4.CollectionUtils;

public class ValidationUtils {

	public static <T> void validate(T objectToValidate) {

		ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
		Validator validator = factory.getValidator();
		Set<ConstraintViolation<T>> violations = validator.validate(objectToValidate);
		StringBuilder sb = new StringBuilder("");
		if (!CollectionUtils.isEmpty(violations)) {
			violations.forEach(constraint -> sb.append(constraint.getMessage()));
			throw new ConstraintViolationException(sb.toString(), violations);
		}

	}
	public static <T> void validateByGroup(T objectToValidate,Class group) {

		ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
		Validator validator = factory.getValidator();
		Set<ConstraintViolation<T>> violations = validator.validate(objectToValidate,group);
		StringBuilder sb = new StringBuilder("");
		if (!CollectionUtils.isEmpty(violations)) {
			violations.forEach(constraint -> sb.append(constraint.getMessage()));
			throw new ConstraintViolationException(sb.toString(), violations);
		}

	}

}
