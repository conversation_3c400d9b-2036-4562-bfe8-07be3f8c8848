package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.LocalDate;

public class ESServiceUtil {
    private static final Logger logger = LogManager.getLogger(ESServiceUtil.class);


    public static String getEndpointWithCurrentMonthIndex(String elasticSearchUrl) {
        LocalDate currentDate = LocalDate.now();
        int year = currentDate.getYear();
        int month = currentDate.getMonthValue();
        String monthYear = String.format("%02d_%04d", month, year);
        String url = elasticSearchUrl.replace("recharge_mis", "recharge_mis".concat(Constants.Delimiters.UNDERSCORE).concat(monthYear));
        return url;

    }
}
