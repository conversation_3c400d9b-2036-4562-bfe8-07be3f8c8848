package com.paytm.saga.util;

import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.model.Recents;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static io.netty.util.internal.StringUtil.EMPTY_STRING;

public class CreditCardUtils {

    private CreditCardUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static String returnMaskedMcn(String mcn) {
        if (mcn == null) {
            return null;
        }
        Integer digits = CommonConsts.VISIBLE_DIGITS_IN_MASKED_MCN;
        if (mcn.length() < digits) {
            return null;
        }
        StringBuilder newMcn = new StringBuilder();

        for(int i = 0; i < mcn.length(); i++) {
            newMcn.append(mcn.charAt(i));
        }
        Integer iterator = newMcn.length() - 1;
        while (iterator >= 0) {
            if (digits > 0 && newMcn.charAt(iterator) != ' ') {
                digits--;
            }
            else if (newMcn.charAt(iterator) != ' '){
                newMcn.setCharAt(iterator, 'X');
            }
            iterator--;
        }
        return newMcn.toString();
    }

    public static boolean inFormat(String recharge_number2) {
        String[] afterSplit = recharge_number2.split("_");
        return afterSplit.length == 2 && (!afterSplit[1].equals(" ")) &&
                (afterSplit[0].equals(CommonConsts.RECHARGE_NUMBER_2_FORMAT_IDENTIFIER) ? true : false);
    }

    public static String formatAndReturn(String recharge_number2) {
        String[] afterSplit = recharge_number2.split("_");
        return afterSplit[1];
    }

    public static String getUniqueIdForCC(Map<String, String> billsObj) {
        String uniqueId = null;

        if (billsObj.containsKey("par")) {
            uniqueId = billsObj.get("par");
        }
        else if (billsObj.containsKey("cin")) {
            uniqueId = billsObj.get("cin");
        }
        else if (billsObj.containsKey("bbpsRefId")) {
            uniqueId = billsObj.get("bbpsRefId");
        }
        else if(billsObj.containsKey("pgCardId")){
            uniqueId = billsObj.get("pgCardId");
        }

        return uniqueId;
    }

    public static String getLast4Digits(String mcn){
        return mcn.substring(mcn.length() - 4);
    }

    public static String getLast2Digits(String mcn){
        return mcn.substring(mcn.length() - 2);
    }

    public static void removeDuplicates(List<Recents> recentsList, MetricsHelper metricsHelper, CustomLogger logger){

        Map<String, Recents> recentsMap = new HashMap<>();
        List<Recents> discards = new ArrayList<>();
        try{
            for (Recents recents : recentsList) {
                if (StringUtils.equalsAnyIgnoreCase(recents.getKey().getService(), FINANCIAL_SERVICES)) {
                    if (recentsMap.containsKey(makeDedupKey(recents))) {
                        Recents existingRecents = recentsMap.get(makeDedupKey(recents));
                        if (isDuplicate(recents.getMcn(), existingRecents.getMcn())) {
                            discardAndUpdateMap(recents, existingRecents, discards, recentsMap);
                        }
                    } else {
                        recentsMap.put(makeDedupKey(recents), recents);
                    }
                }
            }
            metricsHelper.pushCountToDD("RECENTS", "deduplication", discards.size());
            recentsList.removeAll(discards);
        } catch (Exception e){
            logger.error("Error in deduplication of recents", e);
            metricsHelper.pushToDD( "deduplication", "error");
        }
    }

    private static void discardAndUpdateMap(Recents r1, Recents r2, List<Recents> discards, Map<String, Recents> recentsMap) {
        //keep 4 digit masked number rather than 2 digit masked number
        //if (r1.getMcn().replace("X", "").length() > r2.getMcn().replace("X", "").length()) {
        if (StringUtils.equals(r2.getEventSource(), Constants.EVENT_SOURCE.UPI_CREDIT_CARD)) {
            discards.add(r2);
            recentsMap.put(makeDedupKey(r1), r1);
        } else if (StringUtils.equals(r1.getEventSource(), Constants.EVENT_SOURCE.UPI_CREDIT_CARD)) {
            discards.add(r1);
            recentsMap.put(makeDedupKey(r2), r2);
        }
    }

    public static String makeDedupKey(Recents recents){
        final String separator = "_";
        String mcn = StringUtils.isNotBlank(recents.getMcn()) ? recents.getMcn() : EMPTY_STRING;
        String bankCode = CVRProductCache.getInstance().getBankCode(recents.getProductId());
        int len = Math.max(mcn.length(), 2);
        return recents.getKey().getService() + separator + bankCode + separator + mcn.substring(len - 2);
    }

    public static boolean isDuplicate(String mcn1, String mcn2){
        // XX21 should match with 4421, ie visible digits must perfectly match and X can match any digit
        if(mcn1 == null || mcn2 == null) {
            return false;
        }
        if (mcn1.length() == mcn2.length()){
            for (int i = 0; i < mcn1.length(); i++){
                if (mcn1.charAt(i) != mcn2.charAt(i) && mcn1.charAt(i) != 'X' && mcn2.charAt(i) != 'X'){
                    return false;
                }
            }
            return true;
        } else return false;
    }
}
