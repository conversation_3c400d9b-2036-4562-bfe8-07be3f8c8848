package com.paytm.saga.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.dto.FeatureConfigCache;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

public class TTLExhaustEventUtil {
    private static final Integer DEFAULT_TTL = 10 * 24 * 60 * 60;
    private static final Logger logger = LogManager.getLogger(TTLExhaustEventUtil.class);

    private static Integer getTTLFromConfig(String service) {
        Integer ttl = null;
        try {
            Object ttlObj = FeatureConfigCache.getInstance().getObject("categoryWiseExhaustEventTtl");
            ObjectMapper mapper = new ObjectMapper();
            if (Objects.nonNull(ttlObj)) {
                Map<String, Integer> ttlmap = mapper.convertValue(ttlObj, Map.class);
                for (Map.Entry<String, Integer> records : ttlmap.entrySet()) {
                    String category = records.getKey();
                    Integer categoryTtl = records.getValue();
                    if (service.equalsIgnoreCase(category)) {
                        ttl = categoryTtl;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("TTLExhaustEventUtil:: getTTLFromConfig Getting exception while fetching feature config for ttl error", e);
        }

        if (ttl == null) {
            ttl = DEFAULT_TTL;     //ttl 10 days will be used as fallback
        }
        return ttl;
    }

    public static Integer getTTL(String service, Date createdAtTime) {
        Integer ttl = getTTLFromConfig(service);
        if (Objects.nonNull(createdAtTime)) {
            ttl = ttl + Math.toIntExact((createdAtTime.getTime() - new Date().getTime()) / 1000);
        }
        logger.info("TTLExhaustEventUtil.getTTL : TTL for service {} is {}", service, ttl);
        return ttl;
    }
}
