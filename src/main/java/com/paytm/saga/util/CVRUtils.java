package com.paytm.saga.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.dto.catalogue.CvrDataResponse;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.util.Objects;

public class CVRUtils {

    public static Boolean isValid(CvrDataResponse response) {
        return response.getStatus() == HttpStatus.OK.value() && Objects.nonNull(response.getData())
                && Objects.nonNull(response.getData().getProductData());
    }

    public static void addToCache(CvrDataResponse response) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        for (Product productData : response.getData().getProductData()) {
            CVRProductCache.getInstance().addProductDetails(productData);
        }
    }
}
