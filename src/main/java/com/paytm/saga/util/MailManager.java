package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RestClientException;
import com.paytm.saga.model.peppipostemail.*;

import com.paytm.saga.service.GenericRestClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class MailManager {

    @Value("${recentRecon.mail.from.email}")
    private String fromEmail;

    @Value("${recentRecon.mail.from.name}")
    private String fromName;

    @Value("${recentRecon.mail.replyTo}")
    private String replyTo;

    @Value("${peppipostApi.url}")
    private String apiUrl;

    @Value("${peppipostApi.key}")
    private String apiKey;

    @Autowired
    @Qualifier("GenericRestClient")
    private GenericRestClient genericRestClient;

    @Autowired
    public MailManager(GenericRestClient genericRestClient) {
        this.genericRestClient = genericRestClient;
    }

    private final Logger logger = LogManager.getLogger(MailManager.class);

    public void sendMail(List<MailAddress> mailingList, String subject, List<MailContent> content) {
        MailRequestBody requestBody = createRequestBody(mailingList, subject, content);
        hitPeppipostEmailApi(requestBody);
    }

    public void sendMailWithAttachment(List<MailAddress> mailingList, String subject, List<MailContent> content, MailAttachment attachment) {
        MailRequestBody requestBody = createRequestBody(mailingList, subject, content);
        requestBody.setAttachments(Collections.singletonList(attachment));
        hitPeppipostEmailApi(requestBody);
    }

    public MailRequestBody createRequestBody(List<MailAddress> mailingList, String subject, List<MailContent> content) {
        return new MailRequestBody(new MailAddress(fromEmail, fromName), replyTo, subject, content,
                Collections.singletonList(new MailPersonalizations(mailingList)));
    }

    public void hitPeppipostEmailApi(MailRequestBody requestBody) {
        Map<String, Object> response;
        HttpHeaders headers = new HttpHeaders();
        headers.add("api_key", apiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);


       try {

            ResponseEntity<Map> res = genericRestClient.postWithHeader(apiUrl, requestBody, headers, Map.class);

            response = res.getBody();

            if (response != null && response.containsKey("status") && Constants.SUCCESS.equalsIgnoreCase(response.get("status").toString())) {
                logger.info("[MailManager.hitPeppipostEmailApi]: Email Sent, Response={}", response);
            } else {
                logger.info("[MailManager.hitPeppipostEmailApi]: Email could not be sent, Response={}", response);
            }
        } catch (RestClientException e) {
            logger.error("[MailManager.hitPeppipostEmailApi]: Exception occured in Peppipost Email Api , Exception ={}", e.getMessage());
            throw new RestClientException(e.getHttpStatus(), e.getMessage());
        }
    }
}

