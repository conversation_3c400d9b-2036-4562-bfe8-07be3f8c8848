package com.paytm.saga.util;

import com.paytm.saga.common.configuration.property.AES256Configurations;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

import static com.paytm.saga.common.constant.EncryptionConstants.*;


@Component
@DependsOn("vaultConfiguration")
public class AESUtil {
	static Logger logger = LoggerFactory.getLogger(AESUtil.class);
	private static final String ALGORITHM = "AES/CTR/NoPadding";
	private final AES256Configurations aes256Configurations;
	private final MetricsHelper metricsHelper;

	@Autowired
	public AESUtil(AES256Configurations aes256Configurations, MetricsHelper metricsHelper) {
		this.aes256Configurations = aes256Configurations;
		this.metricsHelper = metricsHelper;
	}

	public String encrypt(String text) throws AES256Exception {
		if(1==1) return text;
		if(Objects.isNull(text)) return null;
		try {
			return encrypt(text, aes256Configurations.getEncryptionPassword(), aes256Configurations.getEncryptionIvHex());
		} catch (AES256Exception e) {
			logger.error("Error in encryption of the text {}", text);
			logger.error("Error in encrypting the request", e);
			metricsHelper.pushToDD(AES256_ERROR, ENCRYPTION);
			throw new AES256Exception("Error in encrypting the request");
		}
	}

	public String decrypt(String encryptedData) throws AES256Exception{
		if(Objects.isNull(encryptedData)) return null;
		try {
			return decrypt(encryptedData, aes256Configurations.getEncryptionPassword(), aes256Configurations.getEncryptionIvHex());
		} catch (AES256Exception e) {
			logger.error("Error in decryption of the text {}", encryptedData);
			logger.error("Error in decryption the request", e);
			metricsHelper.pushToDD(AES256_ERROR, DECRYPTION);
			throw new AES256Exception("Error in decrypting the request");
		}
	}

	public static String encrypt(String text, String key, String ivHex) {
		try {
			SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
			IvParameterSpec iv = new IvParameterSpec(hexStringToByteArray(ivHex));

			Cipher cipher = Cipher.getInstance(ALGORITHM);
			cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

			byte[] encryptedBytes = cipher.doFinal(text.getBytes(StandardCharsets.UTF_8));
			return Base64.getEncoder().encodeToString(encryptedBytes);
		} catch (Exception e) {
			logger.error("Error in encrypting the text {} with error", text, e);
			throw new AES256Exception("Encryption failed");
		}
	}

	public static String decrypt(String encryptedText, String key, String ivHex) {
		try {
			SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
			IvParameterSpec iv = new IvParameterSpec(hexStringToByteArray(ivHex));

			Cipher cipher = Cipher.getInstance(ALGORITHM);
			cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

			byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);
			byte[] originalBytes = cipher.doFinal(decodedBytes);
			return new String(originalBytes, StandardCharsets.UTF_8);
		} catch (Exception e) {
			logger.error("Error in decrypting the request", e);
			throw new AES256Exception("Decryption failed");
		}
	}

	private static byte[] hexStringToByteArray(String s) {
		int len = s.length();
		byte[] data = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
				+ Character.digit(s.charAt(i + 1), 16));
		}
		return data;
	}
}