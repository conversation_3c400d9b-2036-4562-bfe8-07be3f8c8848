package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.ServiceNameConstants;
import com.paytm.saga.dto.DropOffResponse;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.enums.EventState;
import com.paytm.saga.enums.Service;
import com.paytm.saga.model.Recents;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.*;
import static com.paytm.saga.common.constant.ServiceNameConstants.UPMS;

public class EventStateHandler {

    private static final Logger logger = LogManager.getLogger(EventStateHandler.class);

    private static Map<String, EventState> eventStateMap = new HashMap<>();
    private static Map<String, EventState> eventStateMapAutomatic = new HashMap<>();

    static {
        eventStateMap.put(Constants.DropOffConstants.FAILURE, EventState.RECHARGE_FAILURE);
        eventStateMap.put(Constants.DropOffConstants.PAYMENT_FAILURE, EventState.RECHARGE_FAILURE);
        eventStateMap.put(Constants.DropOffConstants.REVERSAL_FAILURE, EventState.RECHARGE_FAILURE);
        eventStateMap.put(Constants.DropOffConstants.PENDING, EventState.RECHARGE_PENDING);
        eventStateMap.put(Constants.DropOffConstants.PAYMENT_PENDING, EventState.RECHARGE_PENDING);
        eventStateMap.put(Constants.DropOffConstants.CANCEL, EventState.RECHARGE_CANCEL);

        eventStateMapAutomatic.put(Constants.DropOffConstants.FAILURE, EventState.RECHARGE_AUTOMATIC_FAILURE);
        eventStateMapAutomatic.put(Constants.DropOffConstants.PAYMENT_FAILURE, EventState.RECHARGE_AUTOMATIC_FAILURE);
        eventStateMapAutomatic.put(Constants.DropOffConstants.REVERSAL_FAILURE, EventState.RECHARGE_AUTOMATIC_FAILURE);
        eventStateMapAutomatic.put(Constants.DropOffConstants.PENDING, EventState.RECHARGE_AUTOMATIC_PENDING);
        eventStateMapAutomatic.put(Constants.DropOffConstants.PAYMENT_PENDING, EventState.RECHARGE_AUTOMATIC_PENDING);
        eventStateMapAutomatic.put(Constants.DropOffConstants.CANCEL, EventState.RECHARGE_AUTOMATIC_CANCEL);
    }

    private static EventState getDataExhaustState(String dataExhaustValue){
        if("90%".equalsIgnoreCase(dataExhaustValue)){
            return EventState.DATA_EXHAUST_90;
        } else if ("100%".equalsIgnoreCase(dataExhaustValue)) {
            return EventState.DATA_EXHAUST_100;
        }else {
            return EventState.DATA_EXHAUST_50;
        }
    }

    public static EventState getState(Recents recent) {
        
        Map extras = RecentUtils.getExtras(recent);
        logger.debug("getState extras is - {}", extras);

        if(extras != null && RecentUtils.getPartialBill(extras) != null){
            return EventState.PARTIAL_BILL;
        }

        if(recent.getKey() != null && recent.getKey().getService() != null && recent.getKey().getService().equalsIgnoreCase(String.valueOf(Service.ELECTRICITY)) && RecentUtils.checkIfPrepaidCase(recent)){
            if (extras.get(UPDATED_DATA_SOURCE) != null && extras.get(UPDATED_DATA_SOURCE).equals(FFR_SKIP)) {
                return EventState.HEURISTIC_LOW_BALANCE;
            } else if (recent.getReminderStatus().equals(REMINDER_BILL_PAID_STATUS)) {
                return EventState.PREPAID_BILL_PAID;
            } else if (RecentUtils.checkForBillDateAfterTxnDate(recent)) {
                if (RecentUtils.checkForLowBalanceForElectricityPrepaid(recent)) {
                    return EventState.LOW_BALANCE;
                } else if(extras.containsKey(PARTIAL_BILL_EVENT_KEY) && extras.get(PARTIAL_BILL_EVENT_KEY).toString().equals(PARTIAL_BILL_STATE_CONSTANTS.LOW_BALANCE_PARTIAL_RECORD)){
                    return EventState.LOW_BALANCE_NO_AMOUNT;
                }
            } else if (FeatureConfigCache.getInstance().getList(ServiceNameConstants.REMINDER_BILL_GEN_STATUS_LIST).contains(recent.getReminderStatus().toString())) {
                return EventState.PREPAID_BILL_GEN;
            }
        }

        if(Constants.FASTAG_LOW_BALANCE_OPERATOR.equalsIgnoreCase(recent.getOperator())){
            return EventState.LOW_BALANCE;
        }

        if(Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF.equalsIgnoreCase(recent.getEventSource())){
            return EventState.SMART_RECENT_DROPOFF;
        }

        if(Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION.equalsIgnoreCase(recent.getEventSource())){
            return EventState.SMART_RECENT_VALIDATION;
        }
        if(Objects.nonNull(recent.getKey()) && Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET.equalsIgnoreCase(recent.getKey().getPlanBucket())){
            Map<String,String> extra = JsonUtils.parseJson(recent.getExtra(),Map.class);
            return getDataExhaustState(extra.get("data_exhaust_value"));
        }

        if (RecentUtils.isMarkAsPaid(recent)) {
            boolean updatedDataSourceUPMS = (recent.getMarkAsPaidSource() != null && recent.getMarkAsPaidSource().equalsIgnoreCase(UPMS));
            if(StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.RU_SMS,Constants.EVENT_SOURCE.CSV) && (recent.getDueAmount() == null) && (recent.getMinDueAmount() != null)) {
                return updatedDataSourceUPMS ? EventState.UPMS_MARK_AS_PAID_MIN_AMOUNT : EventState.MARK_AS_PAID_MIN_AMOUNT;
            }
            if ((recent.getDueAmount() == null)) {
                return updatedDataSourceUPMS ? EventState.UPMS_MARK_AS_PAID_NO_AMOUNT : EventState.MARK_AS_PAID_NO_AMOUNT;
            }
            return updatedDataSourceUPMS ? EventState.UPMS_MARK_AS_PAID : EventState.MARK_AS_PAID;
        }

        if(Objects.nonNull(recent.getReminderStatus()) && (recent.getReminderStatus() == OLD_BILL_REMINDER_STATUS) &&  Objects.nonNull(recent.getOldBillFetchDate()) && recent.getDueAmount() != null && recent.getDueAmount() > 0 && Objects.nonNull(recent.getDueDate())){
            if(RecentUtils.isOldBillAvoidDisconnectionState(recent)) {
                if (RecentUtils.isOldBillPenaltyState(recent)) {
                    return EventState.OLD_BILL_PENALTY;
                } else {
                    return EventState.OLD_BILL_AVOID_DISCONNECTION;
                }
            }
        }

        if (StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.RU_SMS,Constants.EVENT_SOURCE.PG_DELETED_AMW) && (recent.getDueAmount() == null) && (recent.getMinDueAmount() != null) && ((StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMS,Constants.EVENT_SOURCE.CSV) && recent.getOrderId()==null) || Constants.EVENT_SOURCE.RU_SMS.equalsIgnoreCase(recent.getEventSource()) || Constants.EVENT_SOURCE.PG_DELETED_AMW.equalsIgnoreCase(recent.getEventSource()) )){
            return EventState.SMS_CARD_MIN_AMOUNT;
        }

        if (StringUtils.equalsAnyIgnoreCase(recent.getEventSource(), Constants.EVENT_SOURCE.SMS, Constants.EVENT_SOURCE.RU_SMS,Constants.EVENT_SOURCE.PG_DELETED_AMW,Constants.EVENT_SOURCE.CSV) && ((StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMS,Constants.EVENT_SOURCE.CSV) && recent.getOrderId()==null) || Constants.EVENT_SOURCE.RU_SMS.equalsIgnoreCase(recent.getEventSource()) || Constants.EVENT_SOURCE.PG_DELETED_AMW.equalsIgnoreCase(recent.getEventSource()))) {
            if(!StringUtils.isEmpty(recent.getExtra())){
                Map<String,String> extraInfo = JsonUtils.parseJson(recent.getExtra(),Map.class);
                if(Objects.nonNull(extraInfo) && StringUtils.equalsIgnoreCase(extraInfo.get(Constants.SMS_CARD_EVENT_STATE),Constants.SMS_CARD_TXN)){
                    return EventState.SMS_CARD_TXN;
                }
            }
            if((recent.getDueAmount() == null) && (recent.getMinDueAmount() != null))
                return EventState.SMS_CARD_MIN_AMOUNT;
            else if (recent.getDueAmount() == null)
                return EventState.SMS_CARD_NO_AMOUNT;
            else if (recent.getMinDueAmount() == null)
                return EventState.SMS_CARD;
            else if(StringUtils.equalsAnyIgnoreCase(recent.getEventSource(),Constants.EVENT_SOURCE.SMS,Constants.EVENT_SOURCE.PG_DELETED_AMW,Constants.EVENT_SOURCE.CSV))
                return EventState.SMS_CARD;
        }
        if (StringUtils.equalsIgnoreCase(recent.getEventSource(), EVENT_SOURCE.UPI_CREDIT_CARD)) {
            return EventState.UPI_CARD;
        }

        if (RecentUtils.isTxnAutomatic(recent)) {
            return EventState.RECHARGE_AUTOMATIC_SUCCESS;
        }


        if(Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean(ServiceNameConstants.DISABLE_DROPOFF_SERVICE)) || FeatureConfigCache.getInstance().getList(ServiceNameConstants.NEW_ACCOUNT_SERVICES).contains(recent.getKey().getService()) || StringUtils.equalsIgnoreCase(recent.getKey().getService(),SERVICE_MOBILE))
            return RecentUtils.getFinalState(recent,extras);

        return EventState.RECHARGE_SUCCESS;
    }
    public static EventState getState(DropOffResponse dropOffResponse) {

        String state = dropOffResponse.getBills().get(0).getState();
        logger.debug("getState state is   {}", state);

        if (DropOffUtils.isTxnAutomatic(dropOffResponse)) {
            return eventStateMapAutomatic.getOrDefault(state, EventState.DROP_OFF_SUCCESS);
        }

        return eventStateMap.getOrDefault(state, EventState.DROP_OFF_SUCCESS);
    }
}
