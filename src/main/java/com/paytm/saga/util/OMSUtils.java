package com.paytm.saga.util;

import com.paytm.saga.common.constant.ServiceNameConstants;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.OMSFulfillmentReqModel;
import com.paytm.saga.dto.OMSFulfillmentResponseModel;
import com.paytm.saga.dto.OMSItemMetaModel;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

import static com.paytm.saga.common.constant.ServiceNameConstants.FAILED_IN_RESPONSE_STATES_SERVICE;

public class OMSUtils {

    private static final Logger logger = LogManager.getLogger(OMSUtils.class);

    public static OMSItemMetaModel getOMSSiteMetaModel(String meta_data) {
        OMSItemMetaModel omsItemMetaModel = new OMSItemMetaModel();
        if (meta_data != null) {
            try {
                omsItemMetaModel = JsonUtils.parseJson(meta_data, OMSItemMetaModel.class);
            } catch (Exception e) {
                logger.error("[OMSListeners.getMetaData] json parse exception", e);
            }
        }

        return omsItemMetaModel;
    }

    public static OMSFulfillmentResponseModel getOMSFulfillmentResponse(String fulfillment_response) {
        OMSFulfillmentResponseModel omsFulfillmentResponseModel = new OMSFulfillmentResponseModel();
        if (fulfillment_response != null) {
            try {
                omsFulfillmentResponseModel = JsonUtils.parseJson(fulfillment_response, OMSFulfillmentResponseModel.class);

            } catch (Exception e) {
                logger.error("getOMSFulfillmentResponse prsing to OMSFulfillmentResponseModel error:", e);

            }
        }

        return omsFulfillmentResponseModel;
    }

    public static OMSFulfillmentReqModel getOMSFillmentRequest(String fulfillment_req) {
        OMSFulfillmentReqModel omsFulfillmentReqModel = new OMSFulfillmentReqModel();
        if (fulfillment_req != null) {
            try {
                omsFulfillmentReqModel = JsonUtils.parseJson(fulfillment_req, OMSFulfillmentReqModel.class);

            } catch (Exception e) {
                logger.error("getOMSFillmentRequest prsing to OMSFulfillmentReqModel error:", e);

            }
        }

        return omsFulfillmentReqModel;
    }


    public static Boolean isPaymodeCountSupportEnabled(Long category){
        ArrayList<String> list = (ArrayList<String>) FeatureConfigCache.getInstance().getList(ServiceNameConstants.PAYMODE_COUNT_SUPPORT_SERVICE);
        if (list!= null&& !list.isEmpty())
            return list.contains(String.valueOf(category));
        return false;
    }

    public static ArrayList<String> getFailedInResponseStates(String serviceName){
        ArrayList<String> list = (ArrayList<String>) FeatureConfigCache.getInstance().getList(serviceName);
        if (list!= null&& !list.isEmpty())
            return list;
        return new ArrayList<>();
    }

}
