package com.paytm.saga.util;

import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.FrequentOrderResponse;
import com.paytm.saga.enums.EventType;

public class FrequentOrderUtil {
    @Deprecated
    public static boolean isValid(FrequentOrderResponse response, FrequentOrderRequest request) {
        if (response != null) {
            if (Boolean.TRUE.equals(request.getOnlyReminder())) {
                if (!EventType.RECENT.equals(response.getEventType()))
                    return Boolean.TRUE;
                if (Boolean.TRUE.equals(response.getBill().getIsBillDue()))
                    return Boolean.TRUE;
            } else
                return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}

