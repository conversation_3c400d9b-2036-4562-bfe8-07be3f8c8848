package com.paytm.saga;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.cassandra.CassandraDataAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableRetry
@EnableAsync
@SpringBootApplication
@ComponentScan(basePackages = {"com.paytm.saga.*"})
@EnableAutoConfiguration(exclude={CassandraDataAutoConfiguration.class})
@PropertySource("classpath:/config/${ENV}/config.properties")
public class RechargeSagaApplication {
	private static final Logger logger = LogManager.getLogger(RechargeSagaApplication.class);

	public static void main(String[] args) {
		try {
			SpringApplication.run(RechargeSagaApplication.class, args);
		} catch (Exception e) {
			System.out.println(e);
		}
		logger.info("**Service started now**");
	}

}
