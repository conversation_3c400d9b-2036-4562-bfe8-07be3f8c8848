package com.paytm.saga.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.model.NewTheme;
import com.paytm.saga.model.NewThemePrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Repository;


@Repository("newThemeRepository")
public interface NewThemeRepository extends CassandraRepository<NewTheme, NewThemePrimaryKey> {

    @Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
    Slice<NewTheme> findByThemeKeyAndThemeCategory(String themeKey, String themeCategory, PageRequest pageRequest);
}
