package com.paytm.saga.repository;

import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.ApplicationConfig;
import com.paytm.saga.model.ApplicationConfigPrimaryKey;

@Repository
public interface ApplicationConfigRepository
		extends CassandraRepository<ApplicationConfig, ApplicationConfigPrimaryKey> {

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	List<ApplicationConfig> findByService(String service);

}
