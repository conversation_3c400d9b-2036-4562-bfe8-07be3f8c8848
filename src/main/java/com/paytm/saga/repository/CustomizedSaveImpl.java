package com.paytm.saga.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.repository.CustomizedSave;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.cassandra.core.CassandraOperations;
import org.springframework.data.cassandra.core.InsertOptions;
import org.springframework.data.cassandra.repository.Consistency;

public class CustomizedSaveImpl<T> implements CustomizedSave<T> {

    @Autowired
    @Qualifier("sagaCassandraTemplate")
    private CassandraOperations operations;

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Override
    public <S extends T> S save(S entity, int ttl) {
        InsertOptions insertOptions = org.springframework.data.cassandra.core.InsertOptions.builder().ttl(ttl).build();
        operations.insert(entity, insertOptions);
        return entity;
    }
}
