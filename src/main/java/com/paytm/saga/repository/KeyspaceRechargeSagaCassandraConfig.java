package com.paytm.saga.repository;

import com.datastax.oss.driver.api.core.CqlSession;
import com.datastax.oss.driver.api.core.config.DriverConfigLoader;
import com.datastax.oss.driver.api.core.config.DefaultDriverOption;
import com.paytm.saga.common.configuration.DateToTimestampCodec;
import com.paytm.saga.common.configuration.property.CassandraProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.data.cassandra.config.CassandraEntityClassScanner;
import org.springframework.data.cassandra.config.CqlSessionFactoryBean;
import org.springframework.data.cassandra.core.CassandraTemplate;
import org.springframework.data.cassandra.core.convert.CassandraConverter;
import org.springframework.data.cassandra.core.convert.MappingCassandraConverter;
import org.springframework.data.cassandra.core.mapping.CassandraMappingContext;
import org.springframework.data.cassandra.repository.config.EnableCassandraRepositories;
import org.springframework.data.cassandra.config.SessionBuilderConfigurer;

@Configuration
@EnableCassandraRepositories(cassandraTemplateRef = "sagaCassandraTemplate",basePackages = "com.paytm.saga.repository")
@DependsOn("vaultConfiguration")
public class KeyspaceRechargeSagaCassandraConfig {

    private final String entityBasePackage = "com.paytm.saga.repository";

    @Autowired
    private CassandraProperties cassandraProperties;
    @Value("${saga.cassandra.contact-points}")
    private String contactPoints;

    @Value("${cassandra.port}")
    private int port;
    @Value("${cassandra.keyspace-name.saga}")
    private String keyspace;
    @Value("${cassandra.username}")
    private String username;

    @Value("${cassandra.dc.saga}")
    private String recharge_saga_dc;

    @Value("${datastax-java-driver.basic.request.timeout:10s}")
    private String requestTimeout;

    @Value("${datastax-java-driver.advanced.prepared-statements.reprepare-timeout:10s}")
    private String reprepareTimeout;

    protected String getContactPoints() {
        return contactPoints;
    }

    protected int getPort() {
        return port;
    }

    public String getKeyspaceName() {
        return keyspace;
    }

    @Bean("sagaCassandraSession")
    @Primary
    public CqlSessionFactoryBean sagaSession() {
        String password = cassandraProperties.getPassword();
        CqlSessionFactoryBean session = new CqlSessionFactoryBean();
        session.setContactPoints(contactPoints);
        session.setPort(port);
        session.setKeyspaceName(keyspace);
        session.setLocalDatacenter(recharge_saga_dc);
        if (username != null && !username.isBlank() &&
                password != null && !password.isBlank()) {
            session.setUsername(username);
            session.setPassword(password);
        }

        // Add codec registry configuration and disable automatic preparation
        session.setSessionBuilderConfigurer(sessionBuilder -> {
            // Add codec
            sessionBuilder.addTypeCodecs(new DateToTimestampCodec());

            // Create configuration to disable automatic preparation and set timeouts
            DriverConfigLoader configLoader = DriverConfigLoader.programmaticBuilder()
                    .withDuration(DefaultDriverOption.REQUEST_TIMEOUT,
                            java.time.Duration.parse("PT" + requestTimeout.toUpperCase()))
                    .withDuration(DefaultDriverOption.REPREPARE_TIMEOUT,
                            java.time.Duration.parse("PT" + reprepareTimeout.toUpperCase()))
                    .withBoolean(DefaultDriverOption.PREPARE_ON_ALL_NODES, false)
                    .build();

            return sessionBuilder.withConfigLoader(configLoader);
        });
        return session;
    }

    @Bean(name = "sagaConverter")
    public CassandraConverter sagaConverter(
            @Qualifier("sagaMappingContext") CassandraMappingContext mappingContext) {
        return new MappingCassandraConverter(mappingContext);
    }

    @Bean(name = "sagaMappingContext")
    public CassandraMappingContext sagaMappingContext() {
        CassandraMappingContext context = new CassandraMappingContext();
        try {
            context.setInitialEntitySet(CassandraEntityClassScanner.scan(entityBasePackage));
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Failed to scan Cassandra recharge_saga entities", e);
        }

        return context;
    }

    public String[] getEntityBasePackages() {
        return new String[] {entityBasePackage};
    }

    protected String getLocalDataCenter() {
        return recharge_saga_dc; // This sets the DC for load balancing
    }

    @Bean(name = "sagaCassandraTemplate")
    public CassandraTemplate sagaTemplate(
            @Qualifier("sagaCassandraSession") CqlSession session,
            @Qualifier("sagaConverter") CassandraConverter converter) {
        return new CassandraTemplate(session, converter);
    }
}
