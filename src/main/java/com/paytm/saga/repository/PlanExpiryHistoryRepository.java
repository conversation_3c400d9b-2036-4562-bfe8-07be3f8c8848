package com.paytm.saga.repository;

import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.model.DropOff;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.model.PlanExpiryHistoryPrimaryKey;

@Repository
public interface PlanExpiryHistoryRepository
		extends CassandraRepository<PlanExpiryHistory, PlanExpiryHistoryPrimaryKey>, CustomizedSave<PlanExpiryHistory> {
	// @Query(value = "select * from recharge_saga.plan_expiry_history where
	// rechargeNumber=:rechargeNumber AND operator=:operator AND circle=:circle AND
	// payType=:payType;")
	// recharge_number,service),circle,operator
	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	List<PlanExpiryHistory> findByRechargeNumberAndServiceAndCircleAndOperator(final String rechargeNumber,
			final String service, final String circle, final String operator);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	List<PlanExpiryHistory> findByRechargeNumberInAndServiceIn(final List<String> rechargeNumber,
																			   final List<String> service);

}



