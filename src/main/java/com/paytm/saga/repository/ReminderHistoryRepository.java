package com.paytm.saga.repository;

import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.model.ReminderHistoryPrimaryKey;

@Repository
public interface ReminderHistoryRepository extends CassandraRepository<ReminderHistory, ReminderHistoryPrimaryKey>, CustomizedSave<ReminderHistory> {

    @Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
    List<ReminderHistory> findByCustomerIdAndRechargeNumberAndService(final Long customerId, final String rechargeNumber, final String service);
    @Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
    List<ReminderHistory> findByCustomerIdAndRechargeNumberInAndService(final Long customerId, final List<String> rechargeNumber, final String service);
    @Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
    List<ReminderHistory> findByCustomerIdAndRechargeNumberInAndServiceIn(final Long customerId, final List<String> rechargeNumber, final List<String> service);
    @Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
    List<ReminderHistory> findByCustomerIdAndRechargeNumberAndServiceAndOperator(final Long customerId, final String rechargeNumber, final String service, final String operator);
    @Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
    List<ReminderHistory> findByCustomerIdAndRechargeNumberInAndServiceAndOperator(final Long customerId, final List<String> rechargeNumber, final String service, final String operator);
}
