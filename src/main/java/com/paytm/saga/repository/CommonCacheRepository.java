package com.paytm.saga.repository;

import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.CommonCache;
import com.paytm.saga.model.CommonCachePrimaryKey;

@Repository
public interface CommonCacheRepository extends CassandraRepository<CommonCache, CommonCachePrimaryKey>, CustomizedSave<CommonCache> {
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    List<CommonCache> findByCacheKey(final String cacheKey);
}

