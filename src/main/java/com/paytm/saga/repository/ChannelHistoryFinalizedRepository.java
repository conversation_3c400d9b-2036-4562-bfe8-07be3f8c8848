package com.paytm.saga.repository;

import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.model.ChannelHistoryPrimaryKey;

@Repository
public interface ChannelHistoryFinalizedRepository
		extends CassandraRepository<ChannelHistoryFinalized, ChannelHistoryPrimaryKey>, CustomizedSaveAll<ChannelHistoryFinalized> {


	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	Slice<ChannelHistoryFinalized> findByCustomerIdAndRechargeNumberAndService(Long customerId, String rechargeNumber,
			String service, PageRequest pageRequest);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	Slice<ChannelHistoryFinalized> findByCustomerIdAndRechargeNumberInAndService(Long customerId, List<String> rechargeNumber,
																			   String service, PageRequest pageRequest);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistoryFinalized> findByCustomerIdAndRechargeNumberAndService(Long customerId, String rechargeNumber,
			String service);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistoryFinalized> findByCustomerIdAndRechargeNumberInAndService(Long customerId, List<String> rechargeNumber,
																			  String service);
}
