package com.paytm.saga.repository;

import java.util.Date;
import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ChannelHistoryPrimaryKey;

@Repository
public interface ChannelHistoryRepository extends CassandraRepository<ChannelHistory, ChannelHistoryPrimaryKey>,CustomizedSave<ChannelHistory>,CustomizedSaveAll<ChannelHistory> {

	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	Slice<ChannelHistory> findByCustomerIdAndRechargeNumberAndService(Long customerId, String rechargeNumber,
			String service, PageRequest pageRequest);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	Slice<ChannelHistory> findByCustomerIdAndRechargeNumberInAndService(Long customerId, List<String> rechargeNumber,
																	  String service, PageRequest pageRequest);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistory> findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(Long customerId, String rechargeNumber,
			String service,boolean finalisedState);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistory> findByCustomerIdAndRechargeNumberAndService(Long customerId, String rechargeNumber,
			String service);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistory> findByCustomerIdAndRechargeNumberInAndService(Long customerId, List<String> rechargeNumber,
																	 String service);

	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistory> findByCustomerIdAndRechargeNumberAndServiceAndTransactionTimeAndOrderIdAndItemIdAndTransactionUpdateTime(Long customerId, String recharge_number_1,
																																  String service, Date transactionTime , Long orderId, Long itemId, Date transactionUpdateTime);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistory> findByCustomerIdAndRechargeNumberInAndServiceAndTransactionTimeAndOrderIdAndItemIdAndTransactionUpdateTime(Long customerId, List<String> recharge_number_1,
																																  String service, Date transactionTime , Long orderId, Long itemId, Date transactionUpdateTime);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistory> findByCustomerIdAndRechargeNumberAndServiceAndFinalisedStateAndOperator(Long customerId, String rechargeNumber,
																					  String service,boolean finalisedState, String operator);
	@Consistency(DefaultConsistencyLevel.LOCAL_QUORUM)
	List<ChannelHistory> findByCustomerIdAndRechargeNumberInAndServiceAndFinalisedStateAndOperator(Long customerId, List<String> rechargeNumber,
																								 String service,boolean finalisedState, String operator);

}
