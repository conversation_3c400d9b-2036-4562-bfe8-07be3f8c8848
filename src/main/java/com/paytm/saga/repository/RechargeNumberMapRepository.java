package com.paytm.saga.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.model.RechargeNumberToCustIdMap;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RechargeNumberMapRepository extends CassandraRepository<RechargeNumberToCustIdMap, RechargeNumberToCustIdMap> {
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    List<RechargeNumberToCustIdMap> findByRechargeNumberAndService(final String rechargeNumber, final String service);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    List<RechargeNumberToCustIdMap> findByRechargeNumberAndServiceAndOperator(final String rechargeNumber, final String service, final String operator);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("select customerid from recharge_to_customer_map where recharge_number_1=:recharge_number_1 and service=:service and operator=:operator LIMIT :limit")
    public List<Long> getCustomerIdsByRechargeNumberAndServiceAndOperator(@Param("recharge_number_1") String recharge_number_1, @Param("service") String service, @Param("operator") String operator, @Param("limit") Integer limit);
}


