package com.paytm.saga.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.common.constant.CommonConsts;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.DeleteCIR;
import com.paytm.saga.model.DeleteCIRPrimaryKey;

import java.sql.Date;
import java.util.List;

@Repository
public interface DeleteCIRRepository extends CassandraRepository<DeleteCIR, DeleteCIRPrimaryKey>, CustomizedSave<DeleteCIR>{
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("select " + CommonConsts.Query.BANK_NAMES  +
            " from deletecir where customerid=:customerid and bureauname=:bureauname")
    List<DeleteCIR> findBankNamesbyCustomerIdandBureauName(@Param("customerid") final Long customerId, @Param("bureauname") final String bureauName);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("Insert into deletecir(customerid, bureauname, bankname, deleteat) values (:#{#customerid},:#{#bureauname},:#{#bankname},:#{#deleteat}) if not exists USING TTL :#{#ttl};")
	public Boolean insertData(@Param("customerid") final Long customerId, @Param("bureauname") final String bureauName, @Param("bankname") final String bankName, @Param("deleteat") final java.util.Date deleteAt, @Param("ttl") Integer TTL);
}
