package com.paytm.saga.repository;

import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.Theme;
import com.paytm.saga.model.ThemePrimaryKey;

@Repository
public interface ThemeRepository extends CassandraRepository<Theme, ThemePrimaryKey> {
	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	public List<Theme> findByThemeType(String themeType);
}
