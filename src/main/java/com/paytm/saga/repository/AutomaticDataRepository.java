package com.paytm.saga.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.model.ReminderHistory;
import org.springframework.data.cassandra.repository.CassandraRepository;

import com.paytm.saga.model.AutomaticData;
import com.paytm.saga.model.ReminderHistoryPrimaryKey;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("automaticDataRepository")
public interface AutomaticDataRepository extends CassandraRepository<AutomaticData, ReminderHistoryPrimaryKey>, CustomizedSave<AutomaticData> {
    
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    List<AutomaticData> findByKeyCustomerIdAndKeyRechargeNumberAndKeyServiceAndKeyOperator(final Long customerId, final String rechargeNumber, final String service, final String operator);
}
