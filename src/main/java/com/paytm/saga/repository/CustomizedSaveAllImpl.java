package com.paytm.saga.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.repository.CustomizedSave;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.cassandra.core.CassandraOperations;
import org.springframework.data.cassandra.core.InsertOptions;
import org.springframework.data.cassandra.repository.Consistency;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class CustomizedSaveAllImpl<T> implements CustomizedSaveAll<T> {

    @Autowired
    @Qualifier("sagaCassandraTemplate")
    private CassandraOperations operations;

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Override
    public <S extends T> List<S> saveAll(Iterable<S> entities, int ttl) {
        InsertOptions insertOptions = org.springframework.data.cassandra.core.InsertOptions.builder().ttl(ttl).build();
        List<S> result = new ArrayList<>();

        for (S entity : entities) {
            result.add(operations.insert(entity,insertOptions).getEntity());
        }
        return result;
    }
}
