package com.paytm.saga.repository;

import java.util.List;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.stereotype.Repository;

import com.paytm.saga.model.ScratchCardHistory;
import com.paytm.saga.model.ScratchCardHistoryPrimaryKey;

@Repository
public interface ScratchCardHistoryRepository
		extends CassandraRepository<ScratchCardHistory, ScratchCardHistoryPrimaryKey> {

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	List<ScratchCardHistory> findByOrderId(final long orderId);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("delete from scratch_card_history  where orderid = ?0 and scratchcardid = ?1")
	public void deleteScratchCardsBasedOnOrderIdScratchCardId(Long orderId, Long ScratchCardId);

}