package com.paytm.saga.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.model.CustomerToRechargeNumberMap;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerMapRepository extends CassandraRepository<CustomerToRechargeNumberMap, CustomerToRechargeNumberMap> {
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    List<CustomerToRechargeNumberMap> findByCustomerIdAndService(final Long customerId, final String service);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    List<CustomerToRechargeNumberMap> findByCustomerIdAndServiceAndOperator(final Long customerId, final String service, final String operator);
}



