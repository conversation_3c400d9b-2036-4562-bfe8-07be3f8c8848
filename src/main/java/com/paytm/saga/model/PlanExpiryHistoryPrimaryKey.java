package com.paytm.saga.model;

import java.util.Date;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

public class PlanExpiryHistoryPrimaryKey {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String recharge_number;
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String service;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String circle;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String operator;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String plan_bucket;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Date updated_at;
}
