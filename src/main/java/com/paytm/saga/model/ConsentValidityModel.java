package com.paytm.saga.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)

public class ConsentValidityModel {

    @JsonProperty("customer_id")
    private Long customerId;

    @JsonProperty("recharge_number")
    private String rechargeNumber;

    @JsonProperty("service")
    private String service;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("consent_valid_till")
    private String consentValidTill;

}