package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

import com.fasterxml.jackson.annotation.JsonProperty;

@Table("common_cache")
public class CommonCache {

	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED,value = "cachekey")
	@JsonProperty("cacheKey")
	private String cacheKey;

	@Column(value = "cachevalue")
	@JsonProperty("cacheValue")
	private String cacheValue;

	public CommonCache(String cacheKey, String cacheValue) {
		this.cacheKey = cacheKey;
		this.cacheValue = cacheValue;
	}
	public CommonCache() {}

	public String getCacheKey() {
		return cacheKey;
	}

	public void setCacheKey(String cacheKey) {
		this.cacheKey = cacheKey;
	}

	public String getCacheValue() {
		return cacheValue;
	}

	public void setCacheValue(String cacheValue) {
		this.cacheValue = cacheValue;
	}

	@Override
	public String toString() {
		return "CommonCache{" + "cacheKey='" + cacheKey + '\'' + ", cacheValue='" + cacheValue + '\'' + '}';
	}
}
