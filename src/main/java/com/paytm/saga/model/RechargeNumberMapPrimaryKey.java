package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

import java.util.Date;

public class RechargeNumberMapPrimaryKey {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "recharge_number_1")
    private String rechargeNumber;
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private String service;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String operator;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private Long customerId;
    @Column
    private Date updatedAt;

}
