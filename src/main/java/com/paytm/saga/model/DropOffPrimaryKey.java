package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

import java.util.Date;

public class DropOffPrimaryKey {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private Long customerId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "recharge_number_1")
    private String rechargeNumber;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String service;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private Date transactionTime;
}
