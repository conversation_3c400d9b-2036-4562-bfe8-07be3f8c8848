package com.paytm.saga.model;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

import com.paytm.saga.common.constant.Constants;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class AutomaticResponseModel {

	@JsonProperty("subscriber_id")
	private Integer subscriberId;

	@JsonProperty("customerId")
	@NotNull(message = "customerId can't be empty")
	private Long customerId;

	@JsonProperty("productId")
	private Integer productId;

	@JsonProperty("is_automatic")
	private Integer isAutomatic;

	@JsonProperty("rechargeNumber")
	@NotEmpty(message = "rechargeNumber can't be empty")
	private String rechargeNumber;

	@JsonProperty("operatorName")
	@NotEmpty(message = "operatorName can't be empty")
	private String operatorName;

	@JsonProperty("service")
	@NotEmpty(message = "service can't be empty")
	private String service;

	private Date automaticDate;

	private Double automaticAmount;

	// Flag to track if automaticDate was set to null due to special "0000-00-00 00:00:00"(null fulfillment time) case
	private boolean isFulfillmentNullDate = false;

	@JsonProperty("planBucket")
	private String planBucket;

	@JsonProperty("paytype")
	private String payType;

	@SuppressWarnings("unchecked")
    @JsonProperty("updatedData")
    private void unpackNested(Map<String,Object> updatedData) throws ParseException {
		if (updatedData != null) { 
			Map<String,String> fulfillmentTime = (Map<String,String>)updatedData.get("fulfillmentTime");
			if (fulfillmentTime != null && fulfillmentTime.get("new") != null) { 
				String dateString = fulfillmentTime.get("new");
				
				// Handle special case for "0000-00-00 00:00:00" - set automaticDate to null
				if (Constants.NULL_FUFILLMENT_TIME_STRING.equals(dateString)) {
					this.automaticDate = null;
					this.isFulfillmentNullDate = true;
				} else {
					DateFormat gmtFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					//gmtFormat.setTimeZone(TimeZone.getTimeZone("GMT")); uncomment if  in GMT
					this.automaticDate = gmtFormat.parse(dateString);
				}

				Object amountObj = updatedData.get("amount");
				if (amountObj instanceof Double) {
					this.automaticAmount = (Double) amountObj;
				} else if (amountObj instanceof String) {
					try {
						this.automaticAmount = Double.parseDouble((String) amountObj);
					} catch (NumberFormatException e) {
						// You can choose to ignore the error or handle it as per your requirement
					}
				}
			}
		}
    }
	
	
}
