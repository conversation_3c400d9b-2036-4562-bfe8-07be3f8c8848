package com.paytm.saga.model;

import com.paytm.saga.common.exception.ErrorMessages;
import org.springframework.http.HttpStatus;

public class RechargeSagaGenericErrorResponse {
    private  String errorMessage;
    private  String errorCode;
    private HttpStatus statusCode;

    public RechargeSagaGenericErrorResponse(ErrorMessages em) {
        this.errorMessage = em.getErrorMessage();
        this.errorCode = em.getErrorCode();
        this.statusCode = em.getStatusCode();
    }

    public RechargeSagaGenericErrorResponse(String errorMessage, String errorCode, HttpStatus statusCode) {
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.statusCode = statusCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public HttpStatus getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(HttpStatus statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String toString() {
        return "RechargeSagaGenericErrorResponse{" +
                "errorMessage='" + errorMessage + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", statusCode=" + statusCode +
                '}';
    }
}
