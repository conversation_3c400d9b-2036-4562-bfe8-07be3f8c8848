package com.paytm.saga.model;

import java.util.Date;
import java.util.Map;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

@Table("channel_history_finalised")
public class ChannelHistoryFinalized {

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((amount == null) ? 0 : amount.hashCode());
		result = prime * result + ((categoryId == null) ? 0 : categoryId.hashCode());
		result = prime * result + ((circle == null) ? 0 : circle.hashCode());
		result = prime * result + ((customerId == null) ? 0 : customerId.hashCode());
		result = prime * result + ((eventType == null) ? 0 : eventType.hashCode());
		result = prime * result + ((inResponseCode == null) ? 0 : inResponseCode.hashCode());
		result = prime * result + ((itemId == null) ? 0 : itemId.hashCode());
		result = prime * result + ((operator == null) ? 0 : operator.hashCode());
		result = prime * result + ((orderId == null) ? 0 : orderId.hashCode());
		result = prime * result + ((paymentStatus == null) ? 0 : paymentStatus.hashCode());
		result = prime * result + ((paytype == null) ? 0 : paytype.hashCode());
		result = prime * result + ((productId == null) ? 0 : productId.hashCode());
		result = prime * result + ((rechargeNumber == null) ? 0 : rechargeNumber.hashCode());
		result = prime * result + ((service == null) ? 0 : service.hashCode());
		result = prime * result + ((status == null) ? 0 : status.hashCode());
		result = prime * result + ((transactionTime == null) ? 0 : transactionTime.hashCode());
		result = prime * result + ((transactionUpdateTime == null) ? 0 : transactionUpdateTime.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ChannelHistoryFinalized other = (ChannelHistoryFinalized) obj;
		if (amount == null) {
			if (other.amount != null)
				return false;
		} else if (!amount.equals(other.amount))
			return false;
		if (categoryId == null) {
			if (other.categoryId != null)
				return false;
		} else if (!categoryId.equals(other.categoryId))
			return false;
		if (circle == null) {
			if (other.circle != null)
				return false;
		} else if (!circle.equals(other.circle))
			return false;
		if (customerId == null) {
			if (other.customerId != null)
				return false;
		} else if (!customerId.equals(other.customerId))
			return false;
		if (eventType == null) {
			if (other.eventType != null)
				return false;
		} else if (!eventType.equals(other.eventType))
			return false;
		if (inResponseCode == null) {
			if (other.inResponseCode != null)
				return false;
		} else if (!inResponseCode.equals(other.inResponseCode))
			return false;
		if (itemId == null) {
			if (other.itemId != null)
				return false;
		} else if (!itemId.equals(other.itemId))
			return false;
		if (operator == null) {
			if (other.operator != null)
				return false;
		} else if (!operator.equals(other.operator))
			return false;
		if (orderId == null) {
			if (other.orderId != null)
				return false;
		} else if (!orderId.equals(other.orderId))
			return false;
		if (paymentStatus == null) {
			if (other.paymentStatus != null)
				return false;
		} else if (!paymentStatus.equals(other.paymentStatus))
			return false;
		if (paytype == null) {
			if (other.paytype != null)
				return false;
		} else if (!paytype.equals(other.paytype))
			return false;
		if (productId == null) {
			if (other.productId != null)
				return false;
		} else if (!productId.equals(other.productId))
			return false;
		if (rechargeNumber == null) {
			if (other.rechargeNumber != null)
				return false;
		} else if (!rechargeNumber.equals(other.rechargeNumber))
			return false;
		if (service == null) {
			if (other.service != null)
				return false;
		} else if (!service.equals(other.service))
			return false;
		if (status == null) {
			if (other.status != null)
				return false;
		} else if (!status.equals(other.status))
			return false;
		if (transactionTime == null) {
			if (other.transactionTime != null)
				return false;
		} else if (!transactionTime.equals(other.transactionTime))
			return false;
		if (transactionUpdateTime == null) {
			if (other.transactionUpdateTime != null)
				return false;
		} else if (!transactionUpdateTime.equals(other.transactionUpdateTime))
			return false;
		return true;
	}

	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private Long customerId;
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "recharge_number_1")
	private String rechargeNumber;
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String service;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Date transactionTime;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Long orderId;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Long itemId;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Date transactionUpdateTime;

	@Column
	private Long categoryId;
	@Column
	private String recharge_number_2;
	@Column
	private String recharge_number_3;
	@Column
	private String recharge_number_4;
	@Column
	private String operator;
	@Column
	private String paytype;
	@Column
	private String amount;
	@Column
	private Map<String, String> billsObj;
	@Column
	private Map<String, String> displayValues;
	@Column
	private String status;
	@Column
	private String eventType;
	@Column
	private Date createdTime;
	@Column
	private boolean finalisedState;
	@Column
	private Long productId;
	@Column
	private String circle;
	@Column(value = "in_response_code")
	private String inResponseCode;
	@Column(value = "payment_status")
	private String paymentStatus;
	@Column(value = "is_encrypted")
	private Integer isEncrypted;

	public Integer getIsEncrypted() {
		return isEncrypted;
	}

	public void setIsEncrypted(Integer isEncrypted) {
		this.isEncrypted = isEncrypted;
	}

	public Long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}

	public String getRechargeNumber() {
		return rechargeNumber;
	}

	public void setRechargeNumber(String rechargeNumber) {
		this.rechargeNumber = rechargeNumber;
	}

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public Date getTransactionTime() {
		return transactionTime;
	}

	public void setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public Date getTransactionUpdateTime() {
		return transactionUpdateTime;
	}

	public void setTransactionUpdateTime(Date transactionUpdateTime) {
		this.transactionUpdateTime = transactionUpdateTime;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getRecharge_number_2() {
		return recharge_number_2;
	}

	public void setRecharge_number_2(String recharge_number_2) {
		this.recharge_number_2 = recharge_number_2;
	}

	public String getRecharge_number_3() {
		return recharge_number_3;
	}

	public void setRecharge_number_3(String recharge_number_3) {
		this.recharge_number_3 = recharge_number_3;
	}

	public String getRecharge_number_4() {
		return recharge_number_4;
	}

	public void setRecharge_number_4(String recharge_number_4) {
		this.recharge_number_4 = recharge_number_4;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getPaytype() {
		return paytype;
	}

	public void setPaytype(String paytype) {
		this.paytype = paytype;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public Map<String, String> getBillsObj() {
		return billsObj;
	}

	public void setBillsObj(Map<String, String> billsObj) {
		this.billsObj = billsObj;
	}

	public Map<String, String> getDisplayValues() {
		return displayValues;
	}

	public void setDisplayValues(Map<String, String> displayValues) {
		this.displayValues = displayValues;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public Date getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

	public boolean isFinalisedState() {
		return finalisedState;
	}

	public void setFinalisedState(boolean finalisedState) {
		this.finalisedState = finalisedState;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getCircle() {
		return circle;
	}

	public void setCircle(String circle) {
		this.circle = circle;
	}

	public String getInResponseCode() {
		return inResponseCode;
	}

	public void setInResponseCode(String inResponseCode) {
		this.inResponseCode = inResponseCode;
	}

	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public ChannelHistoryFinalized() {

	}

	public ChannelHistoryFinalized(Long customerId, String recharge_number_1, Long productId, Long orderId,
			Date createdTime, Long categoryId, String recharge_number_2, String recharge_number_3,
			String recharge_number_4, String operator, String paytype, String service, String amount,
			Map<String, String> billsObj, String status, String eventType, Date transactionTime,
			Date transactionUpdateTime, boolean finalisedState, Long itemId, Map<String, String> displayValues,
			String circle, String paymentStatus, String inResponseCode) {
		this.customerId = customerId;
		this.rechargeNumber = recharge_number_1;
		this.productId = productId;
		this.orderId = orderId;
		this.createdTime = createdTime;
		this.categoryId = categoryId;
		this.recharge_number_2 = recharge_number_2;
		this.recharge_number_3 = recharge_number_3;
		this.recharge_number_4 = recharge_number_4;
		this.operator = operator;
		this.service = service;
		this.paytype = paytype;
		this.amount = amount;
		this.billsObj = billsObj;
		this.status = status;
		this.eventType = eventType;
		this.transactionTime = transactionTime;
		this.transactionUpdateTime = transactionUpdateTime;
		this.finalisedState = finalisedState;
		this.itemId = itemId;
		this.circle = circle;
		this.displayValues = displayValues;
		this.paymentStatus = paymentStatus;
		this.inResponseCode = inResponseCode;
	}
}