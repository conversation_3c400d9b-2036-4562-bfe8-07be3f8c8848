package com.paytm.saga.model;

import java.util.Date;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyClass;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

import lombok.Data;

@PrimaryKeyClass
@Data
public class ReminderHistoryPrimaryKey {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private Long customerId;
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "recharge_number")
    private String rechargeNumber;
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private String service;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String operator;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "updated_at")
    private Date updatedAt;

}
