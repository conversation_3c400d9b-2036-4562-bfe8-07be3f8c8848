package com.paytm.saga.model;


import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

import java.util.Date;

@Table("recharge_to_customer_map")
public class RechargeNumberToCustIdMap {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "recharge_number_1")
    private String rechargeNumber;
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private String service;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String operator;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private Long customerId;
    @Column
    private Date updatedAt;

    public RechargeNumberToCustIdMap(String rechargeNumber, String service, String operator, Long customerId, Date updatedAt) {
        this.rechargeNumber = rechargeNumber;
        this.service = service;
        this.operator = operator;
        this.customerId = customerId;
        this.updatedAt = updatedAt;
    }

    public RechargeNumberToCustIdMap(){

    }

    @Override
    public String toString() {
        return "RechargeNumberMap{" +
                "rechargeNumber='" + rechargeNumber + '\'' +
                ", service='" + service + '\'' +
                ", operator='" + operator + '\'' +
                ", customerId=" + customerId +
                ", updatedAt=" + updatedAt +
                '}';
    }

    public String getRechargeNumber() {
        return rechargeNumber;
    }

    public void setRechargeNumber(String rechargeNumber) {
        this.rechargeNumber = rechargeNumber;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}



