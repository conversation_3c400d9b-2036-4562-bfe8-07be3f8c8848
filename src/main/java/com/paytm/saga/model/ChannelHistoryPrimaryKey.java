package com.paytm.saga.model;

import java.util.Date;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

public class ChannelHistoryPrimaryKey {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private Long customerId;
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "recharge_number_1")
	private String rechargeNumber;
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String service;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Date transactionTime;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Long orderId;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Long itemId;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Date transactionUpdateTime;
}
