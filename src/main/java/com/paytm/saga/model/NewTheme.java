package com.paytm.saga.model;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

@ToString
@Data
@Table(value = "newtheme_details")
public class NewTheme {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private String themeKey;
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private String themeCategory;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String themeType;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String viewItem;
    @Column
    private String valueColor;
    @Column
    private String keyColor;
    @Column
    private String rightThumbImage;
    @Column
    private String valueFontWeight;
    @Column
    private String keyFontWeight;
    @Column
    private String leftThumbnail;
    @Column
    private String headingColor;
    @Column
    private String bgColor;
    @Column
    private String borderColor;
    @Column
    private String richTextColor;
    @Column
    private String displayValuesAlignment;
    @Column
    private String showDisplayValue;
    @Column
    private Boolean isAmount;
    @Column
    private String cardAlignment;
    @Column
    private String width;
    @Column
    private String theme;

    private String themeHash;

}
