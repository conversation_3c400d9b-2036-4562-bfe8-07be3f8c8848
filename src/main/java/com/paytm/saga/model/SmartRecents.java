package com.paytm.saga.model;

import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKey;
import org.springframework.data.cassandra.core.mapping.Table;
import java.util.Date;

@Table("smart_recents")
@Data
@ToString
public class SmartRecents {
    @PrimaryKey
    private SmartRecentsPrimaryKey key;
    @Column("rechargenumber")
    private String rechargeNumber;
    @Column("circle")
    private String circle;
    @Column("created_at")
    private Date createdAt;
    @Column("event_source")
    private String eventSource;
    @Column("paytype")
    private String payType;
    @Column("product_id")
    private Long productId;
    @Column("updated_at")
    private Date updatedAt;
}
