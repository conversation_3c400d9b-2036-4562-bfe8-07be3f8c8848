package com.paytm.saga.model;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.dto.OperatorValidationDto;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import jnr.ffi.types.int8_t;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.Transient;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKey;
import org.springframework.data.cassandra.core.mapping.Table;

import java.util.Date;
import java.util.Map;
import java.util.Set;

@Table("recents")
@Data
@ToString
public class Recents {

    @Column("mcn")
    private String mcn;

    @Column("is_tokenized_transaction")
    private Boolean isTokenizedTransaction;

    @Column("bill_date")
    private Date billDate;

    @Column("due_date")
    private Date dueDate;

    @Column("due_amount")
    private Double dueAmount;

    @Column("min_due_amount")
    private Double minDueAmount;

    @Column("current_outstanding_amount")
    private Double currentOutstandingAmount;

    @Column("original_due_amount")
    private Double originalDueAmount;

    @Column("original_min_due_amount")
    private Double originalMinDueAmount;

    @Column("nick_name_v2")
    private String nickName;

    @Column("channel_id")
    private String channelId;

    @Column("consumername_v2")
    private String consumerName;

    @Column("cylinder_agency_name_v2")
    private String cylinderAgencyName;

    @Column("order_id")
    private Long orderId;

    @Column("product_id")
    private Long productId;

    @Column("txn_amount")
    private Double txnAmount;

    @Column("txn_time")
    private Date txnTime;

    @Column("txn_status")
    private String txnStatus;

    @Column("automatic_date")
    private Date automaticDate;

    @Column("automatic_status")
    private Integer automaticStatus;

    @JsonProperty("recharge_number_2")
    @Column("recharge_number_2")
    private String rechargeNumber2;

    @JsonProperty("recharge_number_3")
    @Column("recharge_number_3")
    private String rechargeNumber3;

    @JsonProperty("recharge_number_4")
    @Column("recharge_number_4")
    private String rechargeNumber4;

    @JsonProperty("recharge_number_5")
    @Column("recharge_number_5")
    private String rechargeNumber5;

    @JsonProperty("recharge_number_6")
    @Column("recharge_number_6")
    private String rechargeNumber6;

    @JsonProperty("recharge_number_7")
    @Column("recharge_number_7")
    private String rechargeNumber7;

    @JsonProperty("recharge_number_8")
    @Column("recharge_number_8")
    private String rechargeNumber8;

    @Column("bill_update_time")
    private Date billUpdateTime;

    @Column("dismiss_action_time")
    private Date dismissActionTime;

    @Column("is_mark_as_paid")
    private Boolean isMarkAsPaid;

    @Column("mark_as_paid_time")
    private Date markAsPaidTime;

    @Column("last_pending_txn")
    private String lastPendingTxn;

    @Column("last_failure_txn")
    private String lastFailureTxn;

    @Column("cin")
    private String cin;

    @Column
    private String par;

    @Column
    private String tin;

    @Column("updated_at")
    private Date updatedAt;

    @Column("paytype")
    private String payType;

    @Column("circle")
    private String circle;

    @Column("recentdata")
    private String recentData;

    @Column("not_paid_on_paytm")
    private Integer notPaidOnPaytm;

    @Column("notification_status")
    private Integer notificationStatus;

    @Column("plan_name")
    private String planName;

    @Column("mark_as_paid_amount")
    private Double markAsPaidAmount;

    @Column("new_bill_updated_at")
    private Date newBillUpdatedAt;

    @Column("is_saved_card")
    private Boolean isSavedCard;

    @Column("is_validation")
    private Boolean isValidation;

    @Column("is_transaction")
    private Boolean isTransaction;

    @Column("event_source")
    private String eventSource;

    @PrimaryKey
    private RecentsPrimaryKey key;

    @Transient
    private boolean onlyTxn;
    @Transient
    private boolean recoEvent;

    @Column("additional_info")
    private String additionalInfo;

    @Column("tag")
    private String tag;

    @Column("extra_info")
    private String extra;

    @Column("user_data")
    private String userData;

    @Column("created_at")
    private Date createdAt;

    @Column("order_id_p2p")
    private String orderIdP2p;

    @Column("is_sms_parsed")
    private Boolean isSmsParsed;

    @Column("is_new_biller")
    private Boolean isNewBiller;

    @Column("rent_tf_data")
    private String rentTFData;

    @Column("early_payment_amount")
    private Double earlyPaymentAmount;

    @Column("early_payment_date")
    private Date earlyPaymentDate;

    @Column("recon_id")
    private String reconId;

    @Column("operator")
    private String operator;

    @Column("bbps_ref_id")
    private String bbpsRefId;

    @Column("pg_card_id")
    private String pgCardId;

    @Column("txn_updated_at")
    private Date txnUpdatedAt;

    @Column("automatic_subscription_id")
    private Integer automaticSubscriptionId;

    @Column("next_bill_fetch_date_flag")
    private Boolean nextBillFetchDateFlag;

    @Column("card_variant")
    private String cardVariant;

    @Column("card_skin")
    private String cardSkin;

    @Column("automatic_amount")
    private Double automaticAmount;
    @Column("reminder_status")
    private Integer reminderStatus;

    @Column("old_bill_fetch_date")
    private Date oldBillFetchDate;

    @Column("card_insurance")
    private String insuranceCard;

    @Column("is_new_bill_identified")
    private Boolean isNewBillIdentified;

    @Column("remind_later_date")
    private Date remindLaterDate;

    @Column("mark_as_paid_source")
    private String markAsPaidSource;

    @Column("enc_due_amount")
    private String encDueAmount;

    @Column("enc_min_due_amount")
    private String encMinDueAmount;

    @Column("enc_due_date")
    private String encDueDate;

    @Column("enc_original_due_amount")
    private String encOriginalDueAmount;

    @Column("enc_original_min_due_amount")
    private String encOriginalMinDueAmount;

    @Column("enc_current_outstanding_amount")
    private String encCurrentOutstandingAmount;

    @Column("is_encrypted")
    private Integer isEncrypted;

    @Column("rent_consent_int")
    private Integer rentConsent;

    @Column("consent_valid_till_date")
    private Date consentValidTill;

    @Transient
    private String consumerSource;

    @Transient
    private String fastagRechargeNumber;
    @Transient
    private OperatorValidationDto operatorValidationDto;

    @Column("txn_times")
    private Set<Date> txnTimes;
}
