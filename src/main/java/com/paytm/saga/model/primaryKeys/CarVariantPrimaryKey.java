package com.paytm.saga.model.primaryKeys;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyClass;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

@PrimaryKeyClass
@Data
@ToString
public class CarVariantPrimaryKey {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "variant_id")
	private Long variantId;
}
