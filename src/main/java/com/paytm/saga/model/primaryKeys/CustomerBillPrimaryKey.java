package com.paytm.saga.model.primaryKeys;

import lombok.Data;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyClass;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

import java.util.Date;

@PrimaryKeyClass
@Data
public class CustomerBillPrimaryKey {

    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED,name = "customerid")
    private Long customerId;

    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private Date due_date;

    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String service;

    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "recharge_number")
    private String rechargeNumber;

    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String operator;

    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "plan_bucket")
    private String planBucket;

}
