package com.paytm.saga.model.primaryKeys;

import lombok.Data;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyClass;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

import java.util.Date;

@PrimaryKeyClass
@Data
public class CardVariantSkinPrimaryKey {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED,name = "variant_raw")
    private String variantRaw;

}
