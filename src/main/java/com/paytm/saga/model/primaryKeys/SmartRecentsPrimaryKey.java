package com.paytm.saga.model.primaryKeys;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyClass;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
@PrimaryKeyClass
@Data
@ToString
public class SmartRecentsPrimaryKey {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private Long customerId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String service;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String operator;
}
