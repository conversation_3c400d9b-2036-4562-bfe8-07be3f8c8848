package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

@Table("application_config")
public class ApplicationConfig {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String service;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String category;
	@Column
	private String config;

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getConfig() {
		return config;
	}

	public void setConfig(String config) {
		this.config = config;
	}

	@Override
	public String toString() {
		return "ApplicationConfig [service=" + service + ", category=" + category + ", config=" + config + "]";
	}
}
