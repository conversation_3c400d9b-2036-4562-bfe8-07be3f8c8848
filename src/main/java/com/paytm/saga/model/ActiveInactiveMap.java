package com.paytm.saga.model;

import java.sql.Timestamp;


import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import lombok.ToString;

@Data
@ToString
public class ActiveInactiveMap {
	@JsonProperty("id")
	private Integer id;
	
	@JsonProperty("status")
	private Integer status;
	
	@JsonProperty("old_pid")
	private Long oldPid;
	
	@JsonProperty("new_pid")
	private Long newPid;

	@JsonProperty("created_at")
	private Timestamp createdAt;

	@JsonProperty("updated_at")
	private Timestamp updatedAt;

	@JsonProperty("updated_by")
	private String updatedBy;


}
