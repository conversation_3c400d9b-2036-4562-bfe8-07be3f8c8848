package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

import java.util.Date;

@Table("scratch_card_history")
public class ScratchCardHistory {
    @Override
    public String toString() {
        return "ScratchCardHistory{" +
                "orderId='" + orderId + '\'' +
                ", status='" + status + '\'' +
                ", scratchCardId=" + scratchCardId +
                ", userId='" + userId + '\'' +
                ", promoCreatedAt=" + promoCreatedAt +
                ", promoUpdatedAt=" + promoUpdatedAt +
                ", createdAt=" + createdAt +
                ", deleted=" + deleted +
                '}';
    }

    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "orderid")
    private long orderId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "scratchcardid")
    private Long scratchCardId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "promoupdatedat")
    private Date promoUpdatedAt;
    private String status;
    @Column(value = "userid")
    private String userId;
    @Column(value = "promocreatedat")
    private Date promoCreatedAt;
    @Column
    private Date createdAt;
    @Column
    private boolean deleted;

    public ScratchCardHistory(){

    }

    public ScratchCardHistory(Long orderId, String status, Long scratchCardId, String  userId, Date createdAt, Date promoCreatedAt, Date promoUpdatedAt, boolean deleted){
        this.orderId = orderId;
        this.status = status;
        this.scratchCardId = scratchCardId;
        this.userId = userId;
        this.createdAt = createdAt;
        this.promoCreatedAt = promoCreatedAt;
        this.promoUpdatedAt = promoUpdatedAt;
        this.deleted = deleted;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getScratchCardId() {
        return scratchCardId;
    }

    public void setScratchCardId(Long scratchCardId) {
        this.scratchCardId = scratchCardId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getPromoCreatedAt() {
        return promoCreatedAt;
    }

    public void setPromoCreatedAt(Date promoCreatedAt) {
        this.promoCreatedAt = promoCreatedAt;
    }

    public Date getPromoUpdatedAt() {
        return promoUpdatedAt;
    }

    public void setPromoUpdatedAt(Date promoUpdatedAt) {
        this.promoUpdatedAt = promoUpdatedAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }
}
