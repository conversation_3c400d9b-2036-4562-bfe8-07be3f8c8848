package com.paytm.saga.model;

import com.paytm.saga.model.primaryKeys.CardVariantSkinPrimaryKey;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKey;
import org.springframework.data.cassandra.core.mapping.Table;

import java.util.Date;

@Table("card_variant_skin_map")
@Data
@ToString
public class CardVariantSkinMapping {
    @PrimaryKey
    private CardVariantSkinPrimaryKey key;

    @Column("skin_raw")
    private String skinRaw;

    @Column("variant_display_value")
    private String variantDisplayValue;

    @Column("skin_display_value")
    private String skinDisplayValue;

    @Column("dl_last_updated")
    private Date updatedAt;
}
