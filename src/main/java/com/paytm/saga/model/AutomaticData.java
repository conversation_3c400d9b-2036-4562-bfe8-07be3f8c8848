package com.paytm.saga.model;

import java.util.Date;

import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKey;
import org.springframework.data.cassandra.core.mapping.Table;

import lombok.Data;

@Data
@Table("automatic_data")
public class AutomaticData {

	@PrimaryKey
	private ReminderHistoryPrimaryKey key;

	@Column("automatic_date")
	private Date automaticDate;
}
