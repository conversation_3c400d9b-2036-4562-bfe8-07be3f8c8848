package com.paytm.saga.model;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKey;
import org.springframework.data.cassandra.core.mapping.Table;

@Table("car_detail")
@Data
@ToString
public class CarDetail {

    @PrimaryKey
    private CarDetailPrimaryKey key;

    @Column("display_name")
    private String displayName;

    @Column("variant")
    private String variant;

}
