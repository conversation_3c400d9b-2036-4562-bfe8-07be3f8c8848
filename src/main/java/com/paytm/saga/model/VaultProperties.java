package com.paytm.saga.model;

import com.paytm.saga.common.configuration.property.AES256Configurations;
import com.paytm.saga.common.configuration.property.CassandraProperties;
import com.paytm.saga.common.configuration.property.PGProperties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
public class VaultProperties {
    @Autowired
    private PGProperties pgProperties;
    @Autowired
    private CassandraProperties cassandraProperties;
    @Autowired
    private AES256Configurations aes256Configurations;

}
