package com.paytm.saga.model;

import java.util.Date;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

public class ScratchCardHistoryPrimaryKey {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "orderid")
    private long orderId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "scratchcardid")
    private Long scratchCardId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "promoupdatedat")
    private Date promoUpdatedAt;
}
