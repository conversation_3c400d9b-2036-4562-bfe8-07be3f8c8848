package com.paytm.saga.model;

import lombok.Data;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyClass;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

@PrimaryKeyClass
@Data
public class UserAgentPrimaryKey {

    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private Long customerId;

    public UserAgentPrimaryKey(Long customerId) {
        this.customerId = customerId;
    }

}
