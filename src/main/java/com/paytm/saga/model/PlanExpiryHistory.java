package com.paytm.saga.model;

import java.util.Date;
import java.util.Map;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

import lombok.Data;
import lombok.ToString;

@Table("plan_validity_history")
@Data
@ToString
public class PlanExpiryHistory {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "recharge_number")
	private String rechargeNumber;
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String service;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String circle;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String operator;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String plan_bucket;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private Date updated_at;
	@Column
	private Long customerid;
	@Column
	private String category_name;
	@Column
	private Date validity_expiry_date;
	@Column
	private Date created_at;
	@Column
	private Double amount;
	@Column
	private Map<String, String> display_values;

	

	public PlanExpiryHistory(String recharge_number, String service, String circle,
			   String operator, String plan_bucket, Date updated_at,
			   Long customerid, String  category_name, Date validity_expiry_date,
			   Date created_at, Map<String , String> display_values, Double amount){
		this.rechargeNumber = recharge_number;
		this.service = service;
		this.circle = circle;
		this.operator = operator;
		this.plan_bucket = plan_bucket;
		this.updated_at = updated_at;
		this.customerid = customerid;
		this.category_name = category_name;
		this.validity_expiry_date = validity_expiry_date;
		this.created_at = created_at;
		this.display_values = display_values;
		this.amount = amount;

	}


	public PlanExpiryHistory(){

	}

}
