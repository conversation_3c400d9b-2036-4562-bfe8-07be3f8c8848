package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

@Table(value = "theme_details")
public class Theme {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String themeType;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String viewItem;
	@Column
	private String valueColor;
	@Column
	private String keyColor;
	@Column
	private String rightThumbImage;
	@Column
	private String valueFontWeight;
	@Column
	private String keyFontWeight;
	@Column
	private String leftThumbnail;
	@Column
	private String headingColor;
	@Column
	private String bgColor;
	@Column
	private String borderColor;
	@Column
	private String richTextColor;
	@Column
	private String displayValuesAlignment;
	@Column
	private String showDisplayValue;
	@Column
	private Boolean isAmount;
	@Column
	private String cardAlignment;
	@Column
	private String width;
	@Column
	private String theme;

	private String themeHash;

	public String getThemeType() {
		return themeType;
	}

	public void setThemeType(String themeType) {
		this.themeType = themeType;
	}

	public String getViewItem() {
		return viewItem;
	}

	public void setViewItem(String viewItem) {
		this.viewItem = viewItem;
	}

	public String getValueColor() {
		return valueColor;
	}

	public void setValueColor(String valueColor) {
		this.valueColor = valueColor;
	}

	public String getRightThumbImage() {
		return rightThumbImage;
	}

	public void setRightThumbImage(String rightThumbImage) {
		this.rightThumbImage = rightThumbImage;
	}

	public String getValueFontWeight() {
		return valueFontWeight;
	}

	public void setValueFontWeight(String valueFontWeight) {
		this.valueFontWeight = valueFontWeight;
	}

	public String getLeftThumbnail() {
		return leftThumbnail;
	}

	public void setLeftThumbnail(String leftThumbnail) {
		this.leftThumbnail = leftThumbnail;
	}

	public String getHeadingColor() {
		return headingColor;
	}

	public void setHeadingColor(String headingColor) {
		this.headingColor = headingColor;
	}

	public String getBgColor() {
		return bgColor;
	}

	public void setBgColor(String bgColor) {
		this.bgColor = bgColor;
	}

	public String getBorderColor() {
		return borderColor;
	}

	public void setBorderColor(String borderColor) {
		this.borderColor = borderColor;
	}

	public String getDisplayValuesAlignment() {
		return displayValuesAlignment;
	}

	public void setDisplayValuesAlignment(String displayValuesAlignment) {
		this.displayValuesAlignment = displayValuesAlignment;
	}

	public String getRichTextColor() {
		return richTextColor;
	}

	public void setRichTextColor(String richTextColor) {
		this.richTextColor = richTextColor;
	}

	public String getShowDisplayValue() {
		return showDisplayValue;
	}

	public void setShowDisplayValue(String showDisplayValue) {
		this.showDisplayValue = showDisplayValue;
	}

	public Boolean getIsAmount() {
		return isAmount;
	}

	public void setIsAmount(Boolean isAmount) {
		this.isAmount = isAmount;
	}

	public String getThemeHash() {
		return themeHash;
	}

	public void setThemeHash(String themeHash) {
		this.themeHash = themeHash;
	}

	public String getKeyColor() {
		return keyColor;
	}

	public void setKeyColor(String keyColor) {
		this.keyColor = keyColor;
	}

	public String getKeyFontWeight() {
		return keyFontWeight;
	}

	public void setKeyFontWeight(String keyFontWeight) {
		this.keyFontWeight = keyFontWeight;
	}

	public String getCardAlignment() {
		return cardAlignment;
	}

	public void setCardAlignment(String cardAlignment) {
		this.cardAlignment = cardAlignment;
	}

	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	public String getTheme() {
		return theme;
	}

	public void setTheme(String theme) {
		this.theme = theme;
	}
}
