package com.paytm.saga.model;
import lombok.Data;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyClass;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
@PrimaryKeyClass
@Data
public class DeleteCIRPrimaryKey {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private Long customerId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String bureauName;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String bankName;
}
