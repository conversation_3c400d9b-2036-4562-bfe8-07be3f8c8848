package com.paytm.saga.model;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyClass;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

@PrimaryKeyClass
@Data
@ToString
public class CarDetailColourPrimaryKey {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "make")
	private String make;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "model")
	private String model;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "colour")
	private String colour;
}
