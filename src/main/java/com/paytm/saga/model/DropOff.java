package com.paytm.saga.model;

import org.springframework.data.annotation.Transient;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;



import java.util.Date;
import java.util.Map;

@Table("drop_off")
public class DropOff {
    @Column
    private Long categoryId;
    @Column
    private String recharge_number_2;
    @Column
    private String recharge_number_3;
    @Column
    private String recharge_number_4;
    @Column
    private String recharge_number_5;
    @Column
    private String recharge_number_6;
    @Column
    private String recharge_number_7;
    @Column
    private String recharge_number_8;
    @Column
    private String operator;
    @Column
    private String paytype;
    @Column
    private String amount;

    @Transient
    private Boolean isAutomatic;
    @Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((amount == null) ? 0 : amount.hashCode());
		result = prime * result + ((categoryId == null) ? 0 : categoryId.hashCode());
		result = prime * result + ((circle == null) ? 0 : circle.hashCode());
		result = prime * result + ((createdTime == null) ? 0 : createdTime.hashCode());
		result = prime * result + ((customerId == null) ? 0 : customerId.hashCode());
		result = prime * result + ((eventType == null) ? 0 : eventType.hashCode());
		result = prime * result + ((in_response_code == null) ? 0 : in_response_code.hashCode());
		result = prime * result + ((operator == null) ? 0 : operator.hashCode());
		result = prime * result + ((paytype == null) ? 0 : paytype.hashCode());
		result = prime * result + ((productId == null) ? 0 : productId.hashCode());
		result = prime * result + ((rechargeNumber == null) ? 0 : rechargeNumber.hashCode());
		result = prime * result + ((service == null) ? 0 : service.hashCode());
		result = prime * result + ((transactionTime == null) ? 0 : transactionTime.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DropOff other = (DropOff) obj;
		if (amount == null) {
			if (other.amount != null)
				return false;
		} else if (!amount.equals(other.amount))
			return false;
		if (categoryId == null) {
			if (other.categoryId != null)
				return false;
		} else if (!categoryId.equals(other.categoryId))
			return false;
		if (circle == null) {
			if (other.circle != null)
				return false;
		} else if (!circle.equals(other.circle))
			return false;
		if (createdTime == null) {
			if (other.createdTime != null)
				return false;
		} else if (!createdTime.equals(other.createdTime))
			return false;
		if (customerId == null) {
			if (other.customerId != null)
				return false;
		} else if (!customerId.equals(other.customerId))
			return false;
		if (eventType == null) {
			if (other.eventType != null)
				return false;
		} else if (!eventType.equals(other.eventType))
			return false;
		if (in_response_code == null) {
			if (other.in_response_code != null)
				return false;
		} else if (!in_response_code.equals(other.in_response_code))
			return false;
		if (operator == null) {
			if (other.operator != null)
				return false;
		} else if (!operator.equals(other.operator))
			return false;
		if (paytype == null) {
			if (other.paytype != null)
				return false;
		} else if (!paytype.equals(other.paytype))
			return false;
		if (productId == null) {
			if (other.productId != null)
				return false;
		} else if (!productId.equals(other.productId))
			return false;
		if (rechargeNumber == null) {
			if (other.rechargeNumber != null)
				return false;
		} else if (!rechargeNumber.equals(other.rechargeNumber))
			return false;
		if (service == null) {
			if (other.service != null)
				return false;
		} else if (!service.equals(other.service))
			return false;
		if (transactionTime == null) {
			if (other.transactionTime != null)
				return false;
		} else if (!transactionTime.equals(other.transactionTime))
			return false;
		return true;
	}

	@Column
    private String status;
    @Column
    private String in_response_code;
    @Column
    private String payment_status;
    @Column
    private String eventType;
    @Column
    private Date createdTime;
    @Column
    private Long productId;
    @Column
    private String circle;
    @Column
    private Map<String, String> displayValues;
    @Column
    private Map<String, String> billsObj;
    @Column
    private Map<String, String> customerDataResponse;
    @Column
    private Long orderId;
    @Column
    private Long itemId;
    @Column
    private Boolean dummyRecharge;
    @Transient
    private String paidOutside;


    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private Long customerId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "recharge_number_1")
    private String rechargeNumber;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String service;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private Date transactionTime;

    public DropOff() {
    }

    public DropOff(Long categoryId, String recharge_number_2, String recharge_number_3, String recharge_number_4, String operator, String paytype, String amount, String status, String in_response_code, String payment_status, String eventType, Date createdTime, Long productId, String circle, Map<String, String> displayValues, Map<String, String> billsObj, Map<String, String> customerDataResponse, Long orderId, Long itemId, Boolean dummyRecharge, Long customerId, String rechargeNumber, String service, Date transactionTime, String paidOutside, Boolean isAutomatic) {
        this(categoryId, recharge_number_2, recharge_number_3, recharge_number_4, null, null, null, null, operator, paytype, amount, status, in_response_code, payment_status, eventType, createdTime, productId, circle, displayValues, billsObj, customerDataResponse, orderId, itemId, dummyRecharge, customerId, rechargeNumber, service, transactionTime, paidOutside, isAutomatic);
    }

    public DropOff(Long categoryId, String recharge_number_2, String recharge_number_3, String recharge_number_4, String recharge_number_5, String recharge_number_6, String recharge_number_7, String recharge_number_8, String operator, String paytype, String amount, String status, String in_response_code, String payment_status, String eventType, Date createdTime, Long productId, String circle, Map<String, String> displayValues, Map<String, String> billsObj, Map<String, String> customerDataResponse, Long orderId, Long itemId, Boolean dummyRecharge, Long customerId, String rechargeNumber, String service, Date transactionTime, String paidOutside, Boolean isAutomatic) {
        this.categoryId = categoryId;
        this.recharge_number_2 = recharge_number_2;
        this.recharge_number_3 = recharge_number_3;
        this.recharge_number_4 = recharge_number_4;
        this.recharge_number_5 = recharge_number_5;
        this.recharge_number_6 = recharge_number_6;
        this.recharge_number_7 = recharge_number_7;
        this.recharge_number_8 = recharge_number_8;
        this.operator = operator;
        this.paytype = paytype;
        this.amount = amount;
        this.status = status;
        this.in_response_code = in_response_code;
        this.payment_status = payment_status;
        this.eventType = eventType;
        this.createdTime = createdTime;
        this.productId = productId;
        this.circle = circle;
        this.displayValues = displayValues;
        this.billsObj = billsObj;
        this.customerDataResponse = customerDataResponse;
        this.orderId = orderId;
        this.itemId = itemId;
        this.dummyRecharge = dummyRecharge;
        this.customerId = customerId;
        this.rechargeNumber = rechargeNumber;
        this.service = service;
        this.transactionTime = transactionTime;
        this.paidOutside=paidOutside;
        this.isAutomatic=isAutomatic;
    }

    @Override
    public String toString() {
        return "DropOff{" +
                "categoryId=" + categoryId +
                ", recharge_number_2='" + recharge_number_2 + '\'' +
                ", recharge_number_3='" + recharge_number_3 + '\'' +
                ", recharge_number_4='" + recharge_number_4 + '\'' +
                ", recharge_number_5='" + recharge_number_5 + '\'' +
                ", recharge_number_6='" + recharge_number_6 + '\'' +
                ", recharge_number_7='" + recharge_number_7 + '\'' +
                ", recharge_number_8='" + recharge_number_8 + '\'' +
                ", operator='" + operator + '\'' +
                ", paytype='" + paytype + '\'' +
                ", amount='" + amount + '\'' +
                ", status='" + status + '\'' +
                ", in_response_code='" + in_response_code + '\'' +
                ", payment_status='" + payment_status + '\'' +
                ", eventType='" + eventType + '\'' +
                ", createdTime=" + createdTime +
                ", productId=" + productId +
                ", circle='" + circle + '\'' +
                ", displayValues=" + displayValues +
                ", billsObj=" + billsObj +
                ", customerDataResponse=" + customerDataResponse +
                ", orderId=" + orderId +
                ", itemId=" + itemId +
                ", dummyRecharge=" + dummyRecharge +
                ", paidOutside='" + paidOutside + '\'' +
                ", customerId=" + customerId +
                ", rechargeNumber='" + rechargeNumber + '\'' +
                ", service='" + service + '\'' +
                ", isAutomatic='" + isAutomatic + '\'' +
                ", transactionTime=" + transactionTime +
                '}';
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getRecharge_number_2() {
        return recharge_number_2;
    }

    public void setRecharge_number_2(String recharge_number_2) {
        this.recharge_number_2 = recharge_number_2;
    }

    public String getRecharge_number_3() {
        return recharge_number_3;
    }

    public void setRecharge_number_3(String recharge_number_3) {
        this.recharge_number_3 = recharge_number_3;
    }

    public String getRecharge_number_4() {
        return recharge_number_4;
    }

    public void setRecharge_number_4(String recharge_number_4) {
        this.recharge_number_4 = recharge_number_4;
    }

    public String getRecharge_number_5() {
        return recharge_number_5;
    }

    public void setRecharge_number_5(String recharge_number_5) {
        this.recharge_number_5 = recharge_number_5;
    }

    public String getRecharge_number_6() {
        return recharge_number_6;
    }

    public void setRecharge_number_6(String recharge_number_6) {
        this.recharge_number_6 = recharge_number_6;
    }

    public String getRecharge_number_7() {
        return recharge_number_7;
    }

    public void setRecharge_number_7(String recharge_number_7) {
        this.recharge_number_7 = recharge_number_7;
    }

    public String getRecharge_number_8() {
        return recharge_number_8;
    }

    public void setRecharge_number_8(String recharge_number_8) {
        this.recharge_number_8 = recharge_number_8;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public Map<String, String> getCustomerDataResponse() {
        return customerDataResponse;
    }

    public void setCustomerDataResponse(Map<String, String> customerDataResponse) {
        this.customerDataResponse = customerDataResponse;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getCircle() {
        return circle;
    }

    public void setCircle(String circle) {
        this.circle = circle;
    }

    public Map<String, String> getDisplayValues() {
        return displayValues;
    }

    public void setDisplayValues(Map<String, String> displayValues) {
        this.displayValues = displayValues;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getRechargeNumber() {
        return rechargeNumber;
    }

    public void setRechargeNumber(String rechargeNumber) {
        this.rechargeNumber = rechargeNumber;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public Date getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(Date transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getIn_response_code() {
        return in_response_code;
    }

    public void setIn_response_code(String in_response_code) {
        this.in_response_code = in_response_code;
    }

    public String getPayment_status() {
        return payment_status;
    }

    public void setPayment_status(String payment_status) {
        this.payment_status = payment_status;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Boolean getDummyRecharge() {
        return dummyRecharge;
    }

    public void setDummyRecharge(Boolean dummyRecharge) {
        this.dummyRecharge = dummyRecharge;
    }

    public Map<String, String> getBillsObj() {
        return billsObj;
    }

    public void setBillsObj(Map<String, String> billsObj) {
        this.billsObj = billsObj;
    }

    public String getPaidOutside() {
        return paidOutside;
    }

    public void setPaidOutside(String paidOutside) {
        this.paidOutside = paidOutside;
    }

    public void setIsAutomatic(Boolean isAutomatic) { this.isAutomatic = isAutomatic; }
    public Boolean getIsAutomatic() { return isAutomatic; }
}
