package com.paytm.saga.model;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

@Data
@ToString
public class BillerAccountKafkaModel {

    @JsonProperty("name")
    private String name;

    @JsonProperty("productId")
    @NotNull(message = "productId can't be empty")
    private Long productId;

    @JsonProperty("customerId")
    @NotNull(message = "customerId can't be empty")
    private Long customerId;


    @JsonProperty("maskAccountId")
    @NotNull(message = "maskAccountId can't be empty")
    private String maskAccountId;

    @JsonProperty("accountId")
    @NotNull(message = "accountId can't be empty")
    private String accountId;

    @JsonProperty("ifscCode")
    @NotNull(message = "ifscCode can't be empty")
    private String ifscCode;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("accountStatus")
    @NotNull(message = "accountStatus can't be empty")
    private Integer accountStatus;

    @JsonProperty("bankAccountName")
    private String bankAccountName;

    @JsonProperty("bankName")
    private String bankName;

    @JsonProperty("accountType")
    private Integer accountType;

    @JsonProperty("metadata")
    private Map<String, Object> metaData;

    @JsonProperty("tenantDetails")
    private Map<String, Object> tenantDetailsObj;


    @JsonProperty("categoryId")
    @NotNull(message = "categoryId can't be empty")
    private Long categoryId;



    @JsonProperty("id")
    @NotNull(message = "id can't be empty")
    private Long id;

    @JsonProperty("phoneNumber")
    private Long phoneNumber;

    @JsonProperty("panNo")
    private String panNumber;

    @JsonProperty("consent")
    private String consent;


    @JsonProperty("self_transfer")
    private Integer selfTransfer;



}
