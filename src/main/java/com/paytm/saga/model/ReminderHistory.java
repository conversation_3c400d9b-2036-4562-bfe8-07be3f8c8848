package com.paytm.saga.model;

import org.springframework.data.annotation.Transient;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.Objects;

@Table("reminder_history")
@Data
@ToString
public class ReminderHistory {

    @Column
    private Double originalAmount;
    @Column
    private String reference_id;
	@Column
	private String par;
	@Column
	private String tin;
	@Column
    private String userData;
    @Column
    private Double currentMinBillAmount;
    @Column
    private Double originalMinBillAmount;	
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private Long customerId;
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED, name = "recharge_number")
	private String rechargeNumber;
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String service;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String operator;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "updated_at")
	private Date updatedAt;

	@Column
	private String paytype;
	@Column
	private String circle;
	@Column
	private Integer status;
	@Column
	private Double amount;
	@Column
	private Long productId;
	@Column
	private Date created_at;
	@Column
	private Date bill_date;
	@Column
	private Date due_date;
	@Column
	private Integer is_automatic;
	@Column
	private Integer isPartial;
	@Column
	private Double lastPaidAmount;
	@Transient
	private Date automaticData;
	@Column("earlypaymentamount")
	private Double earlyPaymentAmount;
	@Column("earlypaymentdate")
	private Date earlyPaymentDate;
	@Column("enc_amount")
	private String encAmount;
	@Column("enc_currentminbillamount")
	private String encCurrentMinBillAmount;
	@Column("enc_due_date")
	private String encDueDate;
	@Column("enc_originalamount")
	private String encOriginalAmount;
	@Column("enc_originalminbillamount")
	private String encOriginalMinBillAmount;
	@Column("is_encrypted")
	private Integer isEncrypted;

	public ReminderHistory() {
	}


	public ReminderHistory(Long customerId, String rechargeNumber, String service, String operator, Date updatedAt,
			String paytype, String circle, Integer status, Double amount, Long productId, Date created_at,
			Date bill_date, Date due_date, Integer is_automatic, Integer isPartial, String par,String tin,Date earlyPaymentDate,Double earlyPaymentAmount) {
		this.customerId = customerId;
		this.rechargeNumber = rechargeNumber;
		this.service = service;
		this.operator = operator;
		this.updatedAt = updatedAt;
		this.paytype = paytype;
		this.circle = circle;
		this.status = status;
		this.amount = amount;
		this.productId = productId;
		this.created_at = created_at;
		this.bill_date = bill_date;
		this.due_date = due_date;
		this.is_automatic = is_automatic;
		this.isPartial = isPartial;
		this.par = par;
		this.tin = tin;
		this.earlyPaymentDate = earlyPaymentDate;
		this.earlyPaymentAmount = earlyPaymentAmount;
		//this.lastPaidAmount=lastPaidAmount;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		ReminderHistory that = (ReminderHistory) o;
		return Objects.equals(originalAmount, that.originalAmount) &&
				Objects.equals(reference_id, that.reference_id) &&
				Objects.equals(par, that.par) &&
				Objects.equals(currentMinBillAmount, that.currentMinBillAmount) &&
				Objects.equals(originalMinBillAmount, that.originalMinBillAmount) &&
				Objects.equals(customerId, that.customerId) &&
				Objects.equals(rechargeNumber, that.rechargeNumber) &&
				Objects.equals(service, that.service) &&
				Objects.equals(operator, that.operator) &&
				Objects.equals(updatedAt, that.updatedAt) &&
				Objects.equals(paytype, that.paytype) &&
				Objects.equals(circle, that.circle) &&
				Objects.equals(status, that.status) &&
				Objects.equals(amount, that.amount) &&
				Objects.equals(productId, that.productId) &&
				Objects.equals(created_at, that.created_at) &&
				Objects.equals(bill_date, that.bill_date) &&
				Objects.equals(due_date, that.due_date) &&
				Objects.equals(is_automatic, that.is_automatic) &&
				Objects.equals(isPartial, that.isPartial)&&
				Objects.equals(lastPaidAmount, that.lastPaidAmount) &&
				Objects.equals(earlyPaymentAmount, that.earlyPaymentAmount) &&
				Objects.equals(earlyPaymentDate, that.earlyPaymentDate);
	}

	@Override
	public int hashCode() {
		return Objects.hash(originalAmount, reference_id, par, currentMinBillAmount, originalMinBillAmount, customerId, rechargeNumber, service, operator, updatedAt, paytype, circle, status, amount, productId, created_at, bill_date, due_date, is_automatic, isPartial,lastPaidAmount,earlyPaymentAmount,earlyPaymentDate);
	}
}
