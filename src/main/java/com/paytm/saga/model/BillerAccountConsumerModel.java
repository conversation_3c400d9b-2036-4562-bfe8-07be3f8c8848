package com.paytm.saga.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.saga.dto.ReminderDataResponseModel;
import lombok.Data;
import lombok.ToString;


@Data
@ToString
public class BillerAccountConsumerModel {
    @JsonProperty("type")
    private String operationType;

    @JsonProperty("database")
    private String database;

    @JsonProperty("table")
    private String table;

    @JsonProperty("data")
    private BillerAccountKafkaModel data;

    @JsonProperty("old")
    private BillerAccountKafkaModel old;

}
