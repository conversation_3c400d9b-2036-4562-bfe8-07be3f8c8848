package com.paytm.saga.model;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

import lombok.Data;

import java.util.Date;

@Table("deletecir")
@Data
public class DeleteCIR {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private Long customerId;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String bureauName;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String bankName;
    @Column
    private Date deleteAt;
}
