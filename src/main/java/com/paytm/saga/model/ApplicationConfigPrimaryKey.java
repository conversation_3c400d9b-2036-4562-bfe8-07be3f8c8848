package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

public class ApplicationConfigPrimaryKey {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String service;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String category;

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

}
