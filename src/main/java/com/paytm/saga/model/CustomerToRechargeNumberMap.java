package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;
import org.springframework.data.cassandra.core.mapping.Table;

import java.util.Date;


@Table("customer_to_recharge_map")
public class CustomerToRechargeNumberMap {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private Long customerId;
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private String service;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String operator;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED, name = "recharge_number_1")
    private String rechargeNumber;
    @Column
    private Date updatedAt;


    public CustomerToRechargeNumberMap(Long customerId, String service, String operator, String rechargeNumber, Date updatedAt) {
        this.customerId = customerId;
        this.service = service;
        this.operator = operator;
        this.rechargeNumber = rechargeNumber;
        this.updatedAt = updatedAt;
    }

    public CustomerToRechargeNumberMap(){

    }

    @Override
    public String toString() {
        return "CustomerMap{" +
                "customerId=" + customerId +
                ", service='" + service + '\'' +
                ", operator='" + operator + '\'' +
                ", rechargeNumber='" + rechargeNumber + '\'' +
                ", updatedAt=" + updatedAt +
                '}';
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRechargeNumber() {
        return rechargeNumber;
    }

    public void setRechargeNumber(String rechargeNumber) {
        this.rechargeNumber = rechargeNumber;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
