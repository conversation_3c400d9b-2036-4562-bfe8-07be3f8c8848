package com.paytm.saga.model;

import lombok.Data;
import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

@Data
public class NewThemePrimaryKey {
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private String themeKey;
    @PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
    private String themeCategory;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String themeType;
    @PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
    private String viewItem;
}
