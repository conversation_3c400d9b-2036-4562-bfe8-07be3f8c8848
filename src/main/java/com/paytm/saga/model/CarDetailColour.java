package com.paytm.saga.model;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKey;
import org.springframework.data.cassandra.core.mapping.Table;

@Table("car_detail_colour")
@Data
@ToString
public class CarDetailColour {

    @PrimaryKey
    private CarDetailColourPrimaryKey key;

    @Column("image_url")
    private String imageURL;

}
