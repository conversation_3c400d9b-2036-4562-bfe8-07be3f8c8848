
package com.paytm.saga.model;

import com.paytm.saga.model.primaryKeys.CarVariantPrimaryKey;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.cassandra.core.mapping.Column;
import org.springframework.data.cassandra.core.mapping.PrimaryKey;
import org.springframework.data.cassandra.core.mapping.Table;

@Table("car_variant_detail")
@Data
@ToString
public class CarVariantDetail {

    @PrimaryKey
    private CarVariantPrimaryKey key;

    @Column("make")
    private String make;

    @Column("model")
    private String model;

    @Column("display_name")
    private String displayName;

    @Column("variant")
    private String variant;
}
