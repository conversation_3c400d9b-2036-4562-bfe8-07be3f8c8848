package com.paytm.saga.model;

import org.springframework.data.cassandra.core.cql.PrimaryKeyType;
import org.springframework.data.cassandra.core.mapping.PrimaryKeyColumn;

public class ThemePrimaryKey {
	@PrimaryKeyColumn(type = PrimaryKeyType.PARTITIONED)
	private String themeType;
	@PrimaryKeyColumn(type = PrimaryKeyType.CLUSTERED)
	private String viewItem;
	public String getThemeType() {
		return themeType;
	}
	public void setThemeType(String themeType) {
		this.themeType = themeType;
	}
	public String getViewItem() {
		return viewItem;
	}
	public void setViewItem(String viewItem) {
		this.viewItem = viewItem;
	}

}
