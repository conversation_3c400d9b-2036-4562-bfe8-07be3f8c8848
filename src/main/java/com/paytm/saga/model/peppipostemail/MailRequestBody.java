package com.paytm.saga.model.peppipostemail;

import lombok.Data;

import java.util.List;
@Data
public class MailRequestBody {

    private MailAddress from;

    private String reply_to;

    private String subject;

    private List<MailContent> content;

    private List<MailAttachment> attachments;

    private List<MailPersonalizations> personalizations;

    public MailRequestBody(MailAddress from, String reply_to, String subject, List<MailContent> content,
                           List<MailPersonalizations> personalizations ){
        this.from = from;
        this.reply_to = reply_to;
        this.subject = subject;
        this.content = content;
        this.personalizations = personalizations;
    }




}