package com.paytm.saga.recent.repository;

import com.datastax.oss.driver.api.core.CqlSession;
import com.paytm.saga.common.configuration.property.CassandraProperties;
import com.paytm.saga.common.configuration.DateToTimestampCodec;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.cassandra.config.CassandraEntityClassScanner;
import org.springframework.data.cassandra.config.CqlSessionFactoryBean;
import org.springframework.data.cassandra.core.CassandraTemplate;
import org.springframework.data.cassandra.core.convert.CassandraConverter;
import org.springframework.data.cassandra.core.convert.MappingCassandraConverter;
import org.springframework.data.cassandra.core.mapping.CassandraMappingContext;
import org.springframework.data.cassandra.repository.config.EnableCassandraRepositories;

@Configuration("keyspaceRecentConfig")
@EnableCassandraRepositories(cassandraTemplateRef = "keyspaceRecentCassandraTemplate",basePackages = "com.paytm.saga.recent.repository")
@DependsOn("vaultConfiguration")
public class KeyspaceRecentsCassandraConfig {

    private final Logger logger = LogManager.getLogger(KeyspaceRecentsCassandraConfig.class);
    private final String entityBasePackage = "com.paytm.saga.recent.repository";

    @Autowired
    private CassandraProperties cassandraProperties;
    @Value("${saga.cassandra.contact-points}")
    private String contactPoints;

    @Value("${cassandra.port}")
    private int port;

    @Value("${cassandra.dc.recent}")
    private String recharge_recent_dc;

    protected String getContactPoints() {
        return contactPoints;
    }

    protected int getPort() {
        return port;
    }

    @Value("${cassandra.keyspace-name.recent}")
    private String keyspace;
    @Value("${cassandra.username}")
    private String username;

    public String getKeyspaceName() {
        return keyspace;
    }

    protected String getLocalDataCenter() {
        return recharge_recent_dc; // This sets the DC for load balancing
    }

    @Bean("keyspaceRecentSession")
    public CqlSessionFactoryBean recentSession() {
        String password = cassandraProperties.getPassword();
        CqlSessionFactoryBean session = new CqlSessionFactoryBean();
        session.setContactPoints(contactPoints);
        session.setPort(port);
        session.setKeyspaceName(keyspace);
        session.setLocalDatacenter(recharge_recent_dc);
        if (username != null && !username.isBlank() &&
                password != null && !password.isBlank()) {
            session.setUsername(username);
            session.setPassword(password);
        }

        // Add codec registry configuration
        session.setSessionBuilderConfigurer(builder ->
                builder.addTypeCodecs(new DateToTimestampCodec())
        );
        return session;
    }

    @Bean(name = "recentConverter")
    public CassandraConverter recentConverter(
            @Qualifier("recentMappingContext") CassandraMappingContext mappingContext) {
        return new MappingCassandraConverter(mappingContext);
    }

    @Bean(name = "recentMappingContext")
    public CassandraMappingContext recentMappingContext() {
        CassandraMappingContext context = new CassandraMappingContext();
        try {
            context.setInitialEntitySet(CassandraEntityClassScanner.scan(entityBasePackage));
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Failed to scan Cassandra recents entities", e);
        }
        return context;
    }

    @Bean(name = "keyspaceRecentCassandraTemplate")
    public CassandraTemplate recentTemplate(
            @Qualifier("keyspaceRecentSession") CqlSession session,
            @Qualifier("recentConverter") CassandraConverter converter) {
        return new CassandraTemplate(session, converter);
    }

    public String[] getEntityBasePackages() {
        return new String[] {entityBasePackage};
    }
}
