package com.paytm.saga.recent.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.dto.NickNameRequest;
import com.paytm.saga.model.Recents;
import java.util.Date;
import java.util.List;
import java.util.Set;

import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


/**
 * IMP : For all the new insert/update/delete query methods.
 * Please make sure to add the method name into RecentsRepositoryAspect.java for afterSave/afterUpdate/afterDelete use cases
 * So that the data will be pushed to kafka for syncing it to DWH
 */
@Repository("recentsRepository")
public interface RecentsRepository extends CassandraRepository<Recents, RecentsPrimaryKey>, RecentCustomizedSave<Recents> {
	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:customerid and service=:service and recharge_number=:recharge_number")
	List<Recents> findBycustomerIdAndserviceAndrechargeNumber(@Param("customerid") final Long customerId,
															  @Param("service") final String service,
															  @Param("recharge_number") final String rechargeNumber);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:customerid and service=:service and recharge_number IN :recharge_number")
	List<Recents> findBycustomerIdAndserviceAndrechargeNumberIN(@Param("customerid") final Long customerId,
															  @Param("service") final String service,
															  @Param("recharge_number") final List<String> rechargeNumber);
	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:customerid and service=:service and recharge_number=:recharge_number and operator=:operator")
	List<Recents> findBycustomerIdAndserviceAndrechargeNumberAndoperator(@Param("customerid") final Long customerId,
																		 @Param("service") final String service,
																		 @Param("recharge_number") final String rechargeNumber,
																		 @Param("operator") final String operator);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:customerid and service=:service and recharge_number IN :recharge_number and operator=:operator")
	List<Recents> findBycustomerIdAndserviceAndrechargeNumberINAndoperator(@Param("customerid") final Long customerId,
																		   @Param("service") final String service,
																		   @Param("recharge_number") final List<String> rechargeNumber,
																		   @Param("operator") final String operator);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select "+ CommonConsts.Query.RECENT_SELECT_COLUMNS + " from recents where customerid=:customerid limit :agentLimit")
	List<Recents> findByCustomerId(@Param("customerid") final Long customerId,@Param("agentLimit") final int agentLimit);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select "+ CommonConsts.Query.RECENT_SELECT_COLUMNS +"from recents where customerid=:customerid and service=:service")
	List<Recents> findByCustomerIdAndService(@Param("customerid") final Long customerId,@Param("service") final String service);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("DELETE FROM recents where customerid=:customerid and service=:service and recharge_number=:recharge_number and operator=:operator and plan_bucket=:plan_bucket")
	void deleteRecentByCustomerIdAndServiceAndRecharge(Recents recents, @Param("customerid") final Long customerId,
													   @Param("service") final String service,
													   @Param("recharge_number") final String rechargeNumber,
													   @Param("operator") final String operator,
													   @Param("plan_bucket") final String planBucket);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("DELETE FROM recents where customerid=:customerid and service=:service and recharge_number IN :recharge_number and operator=:operator and plan_bucket=:plan_bucket")
	void deleteRecentByCustomerIdAndServiceAndRechargeIN(Recents recents, @Param("customerid") final Long customerId,
														 @Param("service") final String service,
														 @Param("recharge_number") final List<String> rechargeNumber,
														 @Param("operator") final String operator,
														 @Param("plan_bucket") final String planBucket);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:customerid and service=:service and recharge_number=:recharge_number and operator=:operator and plan_bucket=:plan_bucket")
	List<Recents> findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(@Param("customerid") final Long customerId,
																					  @Param("service") final String service,
																					  @Param("recharge_number") final String rechargeNumber,
																					  @Param("operator") final String operator,
																					  @Param("plan_bucket") final String plan_bucket);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:customerid and service=:service and recharge_number IN :recharge_number and operator=:operator and plan_bucket=:plan_bucket")
	List<Recents> findBycustomerIdAndserviceAndrechargeNumberINAndoperatorAndplanBucket(@Param("customerid") final Long customerId,
																						@Param("service") final String service,
																						@Param("recharge_number") final List<String> rechargeNumber,
																						@Param("operator") final String operator,
																						@Param("plan_bucket") final String plan_bucket);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select order_id from recents where customerid=:customerid  and recharge_number=:recharge_number and service=:service")
	public List<Recents> findBycustomerIdAndrechargeNumberAndservice(@Param("customerid") final Long customerId,
																	 @Param("recharge_number") final String rechargeNumber,
																	 @Param("service") final String service);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select order_id from recents where customerid=:customerid  and recharge_number IN :recharge_number and service=:service")
	List<Recents> findBycustomerIdAndrechargeNumberINAndservice(@Param("customerid") final Long customerId,
																@Param("recharge_number") final List<String> rechargeNumber,
																@Param("service") final String service);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:#{#nickNameRequest.getCustomerId()} and recharge_number=:#{#nickNameRequest.getRechargeNumber()} and service=:#{#nickNameRequest.getService()} and operator=:#{#nickNameRequest.getOperator()}")
	public List<Recents> selectNonCreditRecents(@Param("nickNameRequest") NickNameRequest nickNameRequest);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:#{#nickNameRequest.getCustomerId()} and recharge_number=:#{#nickNameRequest.getRechargeNumber()} and service=:#{#nickNameRequest.getService()}")
	public List<Recents> selectCreditCardRecents(@Param("nickNameRequest") NickNameRequest nickNameRequest);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS  +
			" from recents where customerid=:#{#nickNameRequest.getCustomerId()} and recharge_number IN :recharge_number and service=:#{#nickNameRequest.getService()}")
	List<Recents> selectCreditCardRecentsRechargeNumberIN(@Param("nickNameRequest") NickNameRequest nickNameRequest, @Param("recharge_number") final List<String> rechargeNumber);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select nick_name_v2 from recents Where customerid=:customerid AND recharge_number=:recharge_number AND service=:service AND operator=:operator AND plan_bucket=:plan_bucket;")
	public List<Recents> selectNickNameFromRecents(@Param("customerid") Long customerid,
												   @Param("recharge_number") String recharge_number,
												   @Param("service") String service, @Param("operator") String operator,
												   @Param("plan_bucket") String plan_bucket);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS +
			" from recents Where customerid=:customerid AND recharge_number=:recharge_number AND service=:service AND operator=:operator;")
	public List<Recents> selectPrepaidRecentsWithoutPlanBucket(@Param("customerid") Long customerid,
															   @Param("recharge_number") String recharge_number,
															   @Param("service") String service, @Param("operator") String operator);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS +
			" from recents Where customerid=:customerid AND recharge_number IN :recharge_number AND service=:service AND operator=:operator;")
	List<Recents> selectPrepaidRecentsWithoutPlanBucketRechargeNumberIN(@Param("customerid") Long customerid,
																		@Param("recharge_number") List<String> recharge_number,
																		@Param("service") String service, @Param("operator") String operator);

	@Query(value = "select recharge_number,service "  +
			" from recents where customerid=:customerid  limit :limit" )
	public Set<Recents> findCountByCustomerId(@Param("customerid") Long customerid, @Param("limit") Integer limit);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS +
			" from recents where customerid IN :customerid and service=:service and recharge_number=:recharge_number and operator=:operator")
	List<Recents> findByCustomerIdINAndServiceAndRechargeNumberAndOperator(@Param("customerid") final List<Long> customerid,
																		   @Param("service") final String service,
																		   @Param("recharge_number") final String rechargeNumber, @Param("operator") final String operator);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select " + CommonConsts.Query.RECENT_SELECT_COLUMNS +
			" from recents where customerid IN :customerid and service=:service and recharge_number IN :recharge_number and operator=:operator")
	List<Recents> findByCustomerIdINAndServiceAndRechargeNumberINAndOperator(@Param("customerid") final List<Long> customerid,
																			 @Param("service") final String service,
																			 @Param("recharge_number") final List<String> rechargeNumber, @Param("operator") final String operator);



	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("Update recents USING TTL :ttl Set mcn=:#{#recents.getMcn()},is_tokenized_transaction=:#{#recents.getIsTokenizedTransaction()},order_id=:#{#recents.getOrderId()},product_id=:#{#recents.getProductId()},txn_amount=:#{#recents.getTxnAmount()},txn_time=:#{#recents.getTxnTime()}, txn_status=:#{#recents.getTxnStatus()},recharge_number_2=:#{#recents.getRechargeNumber2()},recharge_number_3=:#{#recents.getRechargeNumber3()},recharge_number_4=:#{#recents.getRechargeNumber4()},recharge_number_5=:#{#recents.getRechargeNumber5()},recharge_number_6=:#{#recents.getRechargeNumber6()},recharge_number_7=:#{#recents.getRechargeNumber7()},recharge_number_8=:#{#recents.getRechargeNumber8()}, cin=:#{#recents.getCin()},par=:#{#recents.getPar()},tin=:#{#recents.getTin()}, updated_at=:#{#recents.getUpdatedAt()} ,circle=:#{#recents.getCircle()},paytype=:#{#recents.getPayType()},consumername_v2=:#{#recents.getConsumerName()},cylinder_agency_name_v2=:#{#recents.getCylinderAgencyName()},last_failure_txn=:#{#recents.getLastFailureTxn()},last_pending_txn=:#{#recents.getLastPendingTxn()},channel_id=:#{#recents.getChannelId()},not_paid_on_paytm=:#{#recents.getNotPaidOnPaytm()},mark_as_paid_amount=:#{#recents.getMarkAsPaidAmount()},plan_name=:#{#recents.getPlanName()},notification_status=:#{#recents.getNotificationStatus()},recentdata=:#{#recents.getRecentData()},mark_as_paid_time=:#{#recents.getMarkAsPaidTime()},is_mark_as_paid=:#{#recents.getIsMarkAsPaid()},bill_update_time=:#{#recents.getBillUpdateTime()},automatic_status=:#{#recents.getAutomaticStatus()},nick_name_v2=:#{#recents.getNickName()},original_min_due_amount=:#{#recents.getOriginalMinDueAmount()},original_due_amount=:#{#recents.getOriginalDueAmount()},min_due_amount=:#{#recents.getMinDueAmount()},due_amount=:#{#recents.getDueAmount()},automatic_amount=:#{#recents.getAutomaticAmount()},due_date=:#{#recents.getDueDate()},bill_date=:#{#recents.getBillDate()},automatic_date=:#{#recents.getAutomaticDate()},dismiss_action_time=:#{#recents.getDismissActionTime()},created_at=:#{#recents.getCreatedAt()},event_source=:#{#recents.getEventSource()},tag=:#{#recents.getTag()},extra_info=:#{#recents.getExtra()},user_data=:#{#recents.getUserData()},additional_info=:#{#recents.getAdditionalInfo()},order_id_p2p=:#{#recents.getOrderIdP2p()},is_sms_parsed=:#{#recents.getIsSmsParsed()},is_validation=:#{#recents.getIsValidation()},is_transaction=:#{#recents.getIsTransaction()},is_saved_card=:#{#recents.getIsSavedCard()},new_bill_updated_at=:#{#recents.getNewBillUpdatedAt()},is_new_biller=:#{#recents.getIsNewBiller()},rent_tf_data=:#{#recents.getRentTFData()}, early_payment_amount=:#{#recents.getEarlyPaymentAmount()}, early_payment_date=:#{#recents.getEarlyPaymentDate()}, pg_card_id=:#{#recents.getPgCardId()},bbps_ref_id=:#{#recents.getBbpsRefId()}, recon_id=:#{#recents.getReconId()}, automatic_subscription_id=:#{#recents.getAutomaticSubscriptionId()}, next_bill_fetch_date_flag=:#{#recents.getNextBillFetchDateFlag()}, card_variant=:#{#recents.getCardVariant()}, card_skin=:#{#recents.getCardSkin()}, txn_updated_at=:#{#recents.getTxnUpdatedAt()}, reminder_status=:#{#recents.getReminderStatus()}, old_bill_fetch_date=:#{#recents.getOldBillFetchDate()}, card_insurance=:#{#recents.getInsuranceCard()}, is_new_bill_identified=:#{#recents.getIsNewBillIdentified()}, remind_later_date=:#{#recents.getRemindLaterDate()}, mark_as_paid_source=:#{#recents.getMarkAsPaidSource()}, current_outstanding_amount=:#{#recents.getCurrentOutstandingAmount()}, enc_due_amount=:#{#recents.getEncDueAmount()}, enc_min_due_amount=:#{#recents.getEncMinDueAmount()}, enc_due_date=:#{#recents.getEncDueDate()}, enc_original_due_amount=:#{#recents.getEncOriginalDueAmount()}, enc_original_min_due_amount=:#{#recents.getEncOriginalMinDueAmount()}, enc_current_outstanding_amount=:#{#recents.getEncCurrentOutstandingAmount()}, is_encrypted=:#{#recents.getIsEncrypted()}, consent_valid_till_date=:#{#recents.getConsentValidTill()}, rent_consent_int=:#{#recents.getRentConsent()} Where customerid=:#{#recents.getKey().getCustomerId()} AND recharge_number=:#{#recents.getKey().getRechargeNumber()} AND service=:#{#recents.getKey().getService()} AND operator=:#{#recents.getKey().getOperator()} AND plan_bucket=:#{#recents.getKey().getPlanBucket()} if updated_at=:old_updated_at ;")
	public Boolean updateRecentWhenDataAlreadyExist(@Param("recents") Recents recents, @Param("old_updated_at") Date old_updated_at, @Param("ttl") Integer ttl);


	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("Insert into recents(customerid, service, recharge_number, operator, plan_bucket, additional_info, automatic_date, automatic_status, bill_date,bill_update_time, channel_id, cin, circle, consumername_v2, created_at, cylinder_agency_name_v2, dismiss_action_time, due_amount, automatic_amount, due_date, event_source, extra_info, is_mark_as_paid, is_tokenized_transaction, is_sms_parsed, is_validation, is_transaction,last_failure_txn,last_pending_txn, is_saved_card, mark_as_paid_time, mark_as_paid_amount, mcn, min_due_amount, new_bill_updated_at, nick_name_v2, not_paid_on_paytm, notification_status, order_id,  order_id_p2p, original_due_amount, original_min_due_amount, par, paytype, plan_name, product_id, recentdata, recharge_number_2, recharge_number_3,recharge_number_4, recharge_number_5, recharge_number_6, recharge_number_7, recharge_number_8, tag, txn_status, tin, txn_amount, txn_time, user_data, updated_at,is_new_biller,rent_tf_data, early_payment_amount, early_payment_date, pg_card_id,bbps_ref_id,recon_id,automatic_subscription_id, next_bill_fetch_date_flag, txn_updated_at,card_variant, card_skin,reminder_status, old_bill_fetch_date,card_insurance,is_new_bill_identified,remind_later_date,mark_as_paid_source,current_outstanding_amount,enc_due_amount,enc_min_due_amount,enc_due_date,enc_original_due_amount,enc_original_min_due_amount,enc_current_outstanding_amount,is_encrypted,consent_valid_till_date,rent_consent_int) values (:#{#recents.getKey().getCustomerId()},:#{#recents.getKey().getService()},:#{#recents.getKey().getRechargeNumber()},:#{#recents.getKey().getOperator()},:#{#recents.getKey().getPlanBucket()},:#{#recents.getAdditionalInfo()},:#{#recents.getAutomaticDate()},:#{#recents.getAutomaticStatus()},:#{#recents.getBillDate()},:#{#recents.getBillUpdateTime()},:#{#recents.getChannelId()},:#{#recents.getCin()},:#{#recents.getCircle()},:#{#recents.getConsumerName()},:#{#recents.getCreatedAt()},:#{#recents.getCylinderAgencyName()},:#{#recents.getDismissActionTime()},:#{#recents.getDueAmount()},:#{#recents.getAutomaticAmount()},:#{#recents.getDueDate()},:#{#recents.getEventSource()},:#{#recents.getExtra()},:#{#recents.getIsMarkAsPaid()},:#{#recents.getIsTokenizedTransaction()},:#{#recents.getIsSmsParsed()},:#{#recents.getIsValidation()},:#{#recents.getIsTransaction()},:#{#recents.getLastFailureTxn()},:#{#recents.getLastPendingTxn()},:#{#recents.getIsSavedCard()},:#{#recents.getMarkAsPaidTime()},:#{#recents.getMarkAsPaidAmount()},:#{#recents.getMcn()},:#{#recents.getMinDueAmount()},:#{#recents.getNewBillUpdatedAt()},:#{#recents.getNickName()},:#{#recents.getNotPaidOnPaytm()},:#{#recents.getNotificationStatus()},:#{#recents.getOrderId()},:#{#recents.getOrderIdP2p()},:#{#recents.getOriginalDueAmount()},:#{#recents.getOriginalMinDueAmount()},:#{#recents.getPar()},:#{#recents.getPayType()},:#{#recents.getPlanName()},:#{#recents.getProductId()},:#{#recents.getRecentData()},:#{#recents.getRechargeNumber2()},:#{#recents.getRechargeNumber3()},:#{#recents.getRechargeNumber4()},:#{#recents.getRechargeNumber5()},:#{#recents.getRechargeNumber6()},:#{#recents.getRechargeNumber7()},:#{#recents.getRechargeNumber8()},:#{#recents.getTag()},:#{#recents.getTxnStatus()},:#{#recents.getTin()},:#{#recents.getTxnAmount()},:#{#recents.getTxnTime()},:#{#recents.getUserData()},:#{#recents.getUpdatedAt()},:#{#recents.getIsNewBiller()},:#{#recents.getRentTFData()}, :#{#recents.getEarlyPaymentAmount()}, :#{#recents.getEarlyPaymentDate()}, :#{#recents.getPgCardId()}, :#{#recents.getBbpsRefId()}, :#{#recents.getReconId()}, :#{#recents.getAutomaticSubscriptionId()}, :#{#recents.getNextBillFetchDateFlag()}, :#{#recents.getTxnUpdatedAt()}, :#{#recents.getCardVariant()}, :#{#recents.getCardSkin()},:#{#recents.getReminderStatus()}, :#{#recents.getOldBillFetchDate()}, :#{#recents.getInsuranceCard()},:#{#recents.getIsNewBillIdentified()},:#{#recents.getRemindLaterDate()},:#{#recents.getMarkAsPaidSource()}, :#{#recents.getCurrentOutstandingAmount()}, :#{#recents.getEncDueAmount()}, :#{#recents.getEncMinDueAmount()}, :#{#recents.getEncDueDate()}, :#{#recents.getEncOriginalDueAmount()}, :#{#recents.getEncOriginalMinDueAmount()}, :#{#recents.getEncCurrentOutstandingAmount()}, :#{#recents.getIsEncrypted()}, :#{#recents.getConsentValidTill()}, :#{#recents.getRentConsent()}) if not exists USING TTL :#{#ttl};")
	public Boolean updateRecentWhenNoExistingData(@Param("recents") Recents recents, @Param("ttl") Integer ttl);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("UPDATE recents USING TTL :ttl SET mcn=:#{#recents.getMcn()}, product_id=:#{#recents.getProductId()}, recharge_number_2=:#{#recents.getRechargeNumber2()}, recharge_number_3=:#{#recents.getRechargeNumber3()}, recharge_number_4=:#{#recents.getRechargeNumber4()}, recharge_number_5=:#{#recents.getRechargeNumber5()}, recharge_number_6=:#{#recents.getRechargeNumber6()}, recharge_number_7=:#{#recents.getRechargeNumber7()}, recharge_number_8=:#{#recents.getRechargeNumber8()}, updated_at=:#{#recents.getUpdatedAt()}, paytype=:#{#recents.getPayType()}, channel_id=:#{#recents.getChannelId()}, mark_as_paid_amount=:#{#recents.getMarkAsPaidAmount()}, plan_name=:#{#recents.getPlanName()}, notification_status=:#{#recents.getNotificationStatus()}, recentdata=:#{#recents.getRecentData()}, mark_as_paid_time=:#{#recents.getMarkAsPaidTime()}, is_mark_as_paid=:#{#recents.getIsMarkAsPaid()}, bill_update_time=:#{#recents.getBillUpdateTime()}, automatic_status=:#{#recents.getAutomaticStatus()}, nick_name_v2=:#{#recents.getNickName()}, original_min_due_amount=:#{#recents.getOriginalMinDueAmount()}, original_due_amount=:#{#recents.getOriginalDueAmount()}, min_due_amount=:#{#recents.getMinDueAmount()}, due_amount=:#{#recents.getDueAmount()}, due_date=:#{#recents.getDueDate()}, bill_date=:#{#recents.getBillDate()}, automatic_date=:#{#recents.getAutomaticDate()}, dismiss_action_time=:#{#recents.getDismissActionTime()}, created_at=:#{#recents.getCreatedAt()}, event_source=:#{#recents.getEventSource()}, tag=:#{#recents.getTag()}, extra_info=:#{#recents.getExtra()}, user_data=:#{#recents.getUserData()}, additional_info=:#{#recents.getAdditionalInfo()}, order_id_p2p=:#{#recents.getOrderIdP2p()}, is_sms_parsed=:#{#recents.getIsSmsParsed()}, is_validation=:#{#recents.getIsValidation()}, is_saved_card=:#{#recents.getIsSavedCard()}, new_bill_updated_at=:#{#recents.getNewBillUpdatedAt()}, is_new_biller=:#{#recents.getIsNewBiller()}, rent_tf_data=:#{#recents.getRentTFData()}, pg_card_id=:#{#recents.getPgCardId()}, recon_id=:#{#recents.getReconId()}, next_bill_fetch_date_flag=:#{#recents.getNextBillFetchDateFlag()}, txn_updated_at=:#{#recents.getTxnUpdatedAt()}, card_variant=:#{#recents.getCardVariant()}, card_skin=:#{#recents.getCardSkin()}, reminder_status=:#{#recents.getReminderStatus()}, old_bill_fetch_date=:#{#recents.getOldBillFetchDate()}, is_new_bill_identified=:#{#recents.getIsNewBillIdentified()}, remind_later_date=:#{#recents.getRemindLaterDate()}, mark_as_paid_source=:#{#recents.getMarkAsPaidSource()}, current_outstanding_amount=:#{#recents.getCurrentOutstandingAmount()}, enc_due_amount=:#{#recents.getEncDueAmount()}, enc_min_due_amount=:#{#recents.getEncMinDueAmount()}, enc_due_date=:#{#recents.getEncDueDate()}, enc_original_due_amount=:#{#recents.getEncOriginalDueAmount()}, enc_original_min_due_amount=:#{#recents.getEncOriginalMinDueAmount()}, enc_current_outstanding_amount=:#{#recents.getEncCurrentOutstandingAmount()}, is_encrypted=:#{#recents.getIsEncrypted()}, consent_valid_till_date=:#{#recents.getConsentValidTill()} WHERE customerid=:#{#recents.getKey().getCustomerId()} AND recharge_number=:#{#recents.getKey().getRechargeNumber()} AND service=:#{#recents.getKey().getService()} AND operator=:#{#recents.getKey().getOperator()} AND plan_bucket=:#{#recents.getKey().getPlanBucket()}")
	public void updateRecentWhenDataAlreadyExistInInsert(@Param("recents") Recents recents, @Param("ttl") Integer ttl);


	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("UPDATE recents USING TTL :ttl SET txn_times=:#{#recents.getTxnTimes()} WHERE customerid=:#{#recents.getKey().getCustomerId()} AND recharge_number=:#{#recents.getKey().getRechargeNumber()} AND service=:#{#recents.getKey().getService()} AND operator=:#{#recents.getKey().getOperator()} AND plan_bucket=:#{#recents.getKey().getPlanBucket()}")
	public Boolean updateRecentsTxnTimes(@Param("recents") Recents recents, @Param("ttl") Integer ttl);

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("DELETE FROM recents where customerid=:customerid and service=:service and recharge_number=:recharge_number and operator=:operator and plan_bucket=:plan_bucket")
	Boolean deleteRecentByCustomerIdAndServiceAndRechargeRetrunStatus(Recents recents, @Param("customerid") final Long customerId,
																	  @Param("service") final String service,
																	  @Param("recharge_number") final String rechargeNumber,
																	  @Param("operator") final String operator,
																	  @Param("plan_bucket") final String planBucket);
}

/**
 * IMP : For all the new insert/update/delete query methods.
 * Please make sure to add the method name into RecentsRepositoryAspect.java for afterSave/afterUpdate/afterDelete use cases
 * So that the data will be pushed to kafka for syncing it to DWH
 */
