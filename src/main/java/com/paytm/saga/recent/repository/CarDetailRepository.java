package com.paytm.saga.recent.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.model.CarDetail;
import com.paytm.saga.model.CarDetailPrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface CarDetailRepository extends CassandraRepository<CarDetail, CarDetailPrimaryKey> {

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select "+ CommonConsts.Query.CAR_DETAIL_SELECT_COLUMNS + " from car_detail where make=:makeName")
	Slice<CarDetail> findByMake(@Param("makeName") final String makeName, PageRequest pageRequest);

}
