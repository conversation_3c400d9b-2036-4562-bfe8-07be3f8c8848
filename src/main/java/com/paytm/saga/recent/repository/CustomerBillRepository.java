package com.paytm.saga.recent.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.primaryKeys.CustomerBillPrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("customerBillRepository")
public interface CustomerBillRepository extends CassandraRepository<CustomerBill, CustomerBillPrimaryKey>, RecentCustomizedSave<CustomerBill> {
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("select " + CommonConsts.Query.CUSTOMER_BILLS_SELECT_COLUMNS + " from customer_bills where customerid=?0 and due_date>=?1 order by due_date desc limit ?2")
    List<CustomerBill> findByCustomerIdAndDueDate(final Long customerId, final Date billDueDateStartRange, final Integer limit);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("DELETE FROM customer_bills where customerid=?0 and due_date=?1 and service=?2 AND recharge_number=?3 AND operator=?4 AND plan_bucket=?5")
    List<CustomerBill> deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(final Long customerId, final Date billDueDateStartRange, final String service, final String rechargeNumber, String operator, String planBucket);

    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("select " + CommonConsts.Query.CUSTOMER_BILLS_SELECT_COLUMNS + " from customer_bills where customerid=?0 and due_date>=?1 and due_date<=?2 order by due_date desc limit ?3")
    List<CustomerBill> findByCustomerIdAndDueDate(final Long customerId, final Date billDueDateStartRange, final Date billDueDateEndRange, final Integer limit);
}
