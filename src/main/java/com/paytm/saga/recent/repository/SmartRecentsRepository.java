package com.paytm.saga.recent.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("smartRecentsRepository")
public interface SmartRecentsRepository extends CassandraRepository<SmartRecents, SmartRecentsPrimaryKey>, RecentCustomizedSave<SmartRecents> {
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("Select "+ CommonConsts.Query.SMART_RECENTS_SELECT_COLUMNS+" from smart_recents where customerid=:customerid and service=:service LIMIT 10")
    List<SmartRecents> findByCustomerIdAndService(@Param("customerid") final Long customerId,@Param("service") final String service);

    @Query("SELECT " + CommonConsts.Query.SMART_RECENTS_SELECT_COLUMNS +
            " FROM smart_recents WHERE customerid=:customerid AND service IN :services LIMIT 10")
    List<SmartRecents> findByCustomerIdAndServices(@Param("customerid") final Long customerId,
                                                 @Param("services") final  List<String> services);


    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("DELETE FROM smart_recents where customerid=:customerid and service=:service and operator=:operator")
    void deleteRecentByCustomerIdAndServiceAndOperator(@Param("customerid") final Long customerId,
                                                       @Param("service") final String service,
                                                       @Param("operator") final String operator);
}
