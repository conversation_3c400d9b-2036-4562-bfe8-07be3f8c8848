package com.paytm.saga.recent.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.model.CarDetailColour;
import com.paytm.saga.model.CarDetailColourPrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CarDetailColourRepository extends CassandraRepository<CarDetailColour, CarDetailColourPrimaryKey> {

	@Consistency(DefaultConsistencyLevel.LOCAL_ONE)
	@Query("select "+ CommonConsts.Query.CAR_DETAIL_COLOUR_SELECT_COLUMNS + " from car_detail_colour where make=:makeName")
	Slice<CarDetailColour> findByMake(@Param("makeName") final String makeName, PageRequest pageRequest);

}
