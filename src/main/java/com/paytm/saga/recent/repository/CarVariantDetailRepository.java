

package com.paytm.saga.recent.repository;

import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.model.CarVariantDetail;
import com.paytm.saga.model.primaryKeys.CarVariantPrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.stereotype.Repository;


@Repository
public interface CarVariantDetailRepository extends CassandraRepository<CarVariantDetail, CarVariantPrimaryKey> {

	@Query("select "+ CommonConsts.Query.CAR_DETAIL_SELECT_COLUMNS + " from car_variant_detail where variant_id=:variantId")
	CarVariantDetail findByVariantId(Long variantId);

}
