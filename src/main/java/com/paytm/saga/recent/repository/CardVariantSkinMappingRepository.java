package com.paytm.saga.recent.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.model.CardVariantSkinMapping;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.primaryKeys.CardVariantSkinPrimaryKey;
import com.paytm.saga.model.primaryKeys.CustomerBillPrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("cardVariantSkinMappingRepository")
public interface CardVariantSkinMappingRepository extends CassandraRepository<CardVariantSkinMapping, CardVariantSkinPrimaryKey>, RecentCustomizedSave<CardVariantSkinMapping>{
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("select " + CommonConsts.Query.CARD_SKIN_VARIANT_SELECT_COLUMNS + " from card_variant_skin_map where variant_raw=?0")
    CardVariantSkinMapping findByVariantRaw(final String variantRaw);
}
