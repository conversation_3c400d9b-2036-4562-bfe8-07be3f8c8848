package com.paytm.saga.recent.repository;

import com.datastax.oss.driver.api.core.DefaultConsistencyLevel;
import com.paytm.saga.model.UserAgent;
import com.paytm.saga.model.UserAgentPrimaryKey;
import org.springframework.data.cassandra.repository.CassandraRepository;
import org.springframework.data.cassandra.repository.Consistency;
import org.springframework.data.cassandra.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface UserAgentRepository extends CassandraRepository<UserAgent, UserAgentPrimaryKey>, RecentCustomizedSave<UserAgent> {
    @Consistency(DefaultConsistencyLevel.LOCAL_ONE)
    @Query("select customerId from user_agent where customerId=?0")
    public Optional<UserAgent> findById(Long customerId);
}
