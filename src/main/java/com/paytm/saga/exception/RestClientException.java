package com.paytm.saga.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
public class RestClientException extends RuntimeException {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    private String message;
    private HttpStatus httpStatus;

    public RestClientException(HttpStatus httpStatus, String message) {
        super(message);
        this.message = message;
        this.httpStatus = httpStatus;
    }

}
