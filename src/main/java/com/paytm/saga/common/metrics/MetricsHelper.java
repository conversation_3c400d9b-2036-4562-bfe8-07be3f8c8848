package com.paytm.saga.common.metrics;

import com.timgroup.statsd.StatsDClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;


import static com.paytm.saga.common.constant.Constants.MetricConstants.*;

import jakarta.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.paytm.saga.common.constant.Constants.MetricConstants.*;

@Component
public class MetricsHelper {
    private final Logger logger = LogManager.getLogger(MetricsHelper.class);

    private StatsDClient monitoringClient;

    @Autowired
    public MetricsHelper(@NotNull @Qualifier("monitoringClient") StatsDClient monitoringClient) {
        this.monitoringClient = monitoringClient;
    }

    public void recordErrorRate(String api) {
        String metricName = "recharge_saga.api.markAsPaid.failureCount";
        String[] tags = new String[2];
        tags[0] = SERVICE_NAME_KEY + ":" + SERVICE;
        tags[1] = API_KEY + ":" + api;

        monitoringClient.incrementCounter(metricName, tags);
    }

    public void recordSuccessRate(String api) {
        String metricName = "recharge_saga.api.markAsPaid.successCount";
        String[] tags = new String[2];
        tags[0] = SERVICE_NAME_KEY + ":" + SERVICE;
        tags[1] = API_KEY + ":" + api;

        monitoringClient.incrementCounter(metricName, tags);
    }

    public void recordStatusCodes(String serviceIdentifier, String statusCode) {
        String metricName = "recharge_saga.api";
        if (serviceIdentifier != null) {
            metricName = metricName + "." + serviceIdentifier;
            String[] tags = new String[2];
            tags[0] = SERVICE_NAME_KEY + ":" + SERVICE;
            tags[1] = STATUS_CODE + ":" + statusCode;
            monitoringClient.incrementCounter(metricName, tags);
        } else {
            logger.error("MetricsHelper :: recordStatusCodes", "serviceIdentifier cannot be null");
        }

    }

    public void recordSuccessRate(String service, String tag) {
        try {
            String metricName = "recharge_saga.api." + service;
            String[] tags = new String[2];
            tags[0] = SERVICE_NAME_KEY + ":" + SERVICE;
            tags[1] = API_KEY + ":" + tag;
            monitoringClient.incrementCounter(metricName, tags);
        } catch (Exception e) {
            logger.error("getting exception in pushing stats", e);
        }
    }

    public void recordSuccessRateForMultipleTags(String service, List<String> tagList) {
        try {
            if(tagList == null) {
                tagList = new ArrayList<>();
            }
            int tagLen = tagList.size()+1;
            String metricName = "recharge_saga.api." + service;
            String[] tags = new String[tagLen];
            tags[0] = SERVICE_NAME_KEY + ":" + SERVICE;
            //each tag item should have format as key:value , e.g. service:electiricy
            for(int i = 0; i < tagList.size(); i++) {
                tags[i+1] = tagList.get(i);
            }
            monitoringClient.incrementCounter(metricName, tags);
        } catch (Exception e) {
            logger.error("getting exception in pushing stats", e);
        }
    }


    public void recordAPIStatusCode(String serviceIdentifier, Integer statusCode, String path) {
        String metricName = "recharge_saga.api";
        if (serviceIdentifier != null) {
            metricName = metricName + "." + serviceIdentifier;
            String[] tags = new String[3];
            tags[0] = SERVICE_NAME_KEY + ":" + SERVICE;
            tags[1] = STATUS_CODE + ":" + statusCode;
            tags[2] = URI + ":" + path;
            monitoringClient.incrementCounter(metricName, tags);
        } else {
            logger.error("MetricsHelper :: recordStatusCodes", "serviceIdentifier cannot be null");
        }

    }

    public void recordExecutionTimeOfEvent(String eventName, long timeTaken) {
        String metricName = SAGA_METHOD_LATENCY_METRIC;
        String[] tags = new String[1];
        tags[0] = DB_METHOD_KEY + ":" + eventName;

        monitoringClient.gauge(metricName, Double.valueOf(timeTaken), 1.0, tags);
        monitoringClient.histogram(metricName, Double.valueOf(timeTaken), 1.0, tags);
    }

    public void pushToDD(String metricName, String serviceName) {
        String[] tags = new String[1];
        tags[0] = SERVICE_NAME_KEY + ":" + serviceName;
        monitoringClient.incrementCounter(metricName, tags);
    }

    public void pushCountToDD(String service, String tag, long count) {
        try {
            String metricName = "recharge_saga.api." + service;
            String[] tags = new String[2];
            tags[0] = SERVICE_NAME_KEY + ":" + SERVICE;
            tags[1] = API_KEY + ":" + tag;
            monitoringClient.count(metricName, count, tags);
        } catch (Exception e) {
            logger.error("pushCountToDD getting exception in pushing stats", e);
        }
    }

    public void incrementCount(String service, String... tags) {
        int len = tags == null ? 0 : tags.length;
        String metricName = "recharge_saga.api." + service;
        String[] internalTags = new String[len+1];
        internalTags[0] = SERVICE_NAME_KEY + ":" + SERVICE;
        for(int i =0 ;i < tags.length; i++){
            internalTags[i+1]=tags[i];
        }
        monitoringClient.incrementCounter(metricName, internalTags);
    }
}
