package com.paytm.saga.common;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.DeepLinkTypes;

public class DeepLinkManager {
	public static String getDeeplinkByType(String type) {
		String response=null;
		switch(type) {
		case CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME:
			response=null;
			break;
		case CardThemeTypes.FAILURE_RECHARGE_CARD_THEME:
			response=null;
			break;
		case CardThemeTypes.PENDING_RECHARGE_CARD_THEME:
			response=null;
			break;
		case CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME:
			response=null;
			break;
		case CardThemeTypes.SUGGESTED_RECHARGE_INVALID_PLAN_CARD_THEME:
			response=null;
			break;
		case CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME:
			response=null;
			break;
		case CardThemeTypes.SUGGESTED_RECHARGE_RECHARGED_ALREADY_CARD_THEME:
			response=null;
			break;
		case DeepLinkTypes.SCRATCH_DEEP_LINK:
			response="paytmmp://cash_wallet?featuretype=vip&screen=homescreen";
			break;
//		case CardThemeTypes.CASHBACK_CARD_THEME:
//			response="http://cashback_temp_url.com";
//			break;
//			DeepLinkTypes.MARKED_AS_PAID_DEEP_LINK
//			DeepLinkTypes.RECHARGE_CARD_DEEP_LINK
//			CardThemeTypes.CASHBACK_CARD_THEME
//			DeepLinkTypes.SCRATCH_DEEP_LINK
			
		}
		return response;
	}
}
