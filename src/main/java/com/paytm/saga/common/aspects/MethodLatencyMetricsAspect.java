package com.paytm.saga.common.aspects;

import com.paytm.saga.common.MethodLatencyMetricsAction;
import com.paytm.saga.common.metrics.MetricsHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class MethodLatencyMetricsAspect {

    private static final Logger log = LoggerFactory.getLogger(MethodLatencyMetricsAspect.class);

    @Autowired
    private MetricsHelper metricsHelper;

    @Around(value = "@annotation(MethodLatencyMetricsAction)", argNames = "joinPoint, MethodLatencyMetricsAction")
    public Object methodExceution(ProceedingJoinPoint joinPoint, MethodLatencyMetricsAction actions) throws Throwable {
        String metricsName = actions.metricsName();
        long startTime = System.currentTimeMillis();
        Object res = joinPoint.proceed();

        long timeElapsed = System.currentTimeMillis() - startTime;
        metricsHelper.recordExecutionTimeOfEvent(metricsName, timeElapsed);
        log.debug("method {} , executed and took {} ms", metricsName, timeElapsed);
        return res;
    }
}