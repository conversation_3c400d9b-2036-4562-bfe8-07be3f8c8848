package com.paytm.saga.common.aspects;

import com.paytm.saga.common.constant.Constants;
import com.timgroup.statsd.StatsDClient;
import org.apache.catalina.connector.RequestFacade;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;

import jakarta.validation.constraints.NotNull;
import java.util.*;

/**
 * <AUTHOR> on 11/02/2021
 */

@Aspect
@Configuration
public class LoggingAspect {
    private final Logger logger = LogManager.getLogger(LoggingAspect.class);
    private StatsDClient monitoringClient;

    @Autowired
    public LoggingAspect(@NotNull @Qualifier("monitoringClient") StatsDClient monitoringClient) {
        this.monitoringClient = monitoringClient;
    }

    @Around("execution(* com.paytm.saga.controller.*.*" +
            "" +
            "(..))")
    public Object pushApiStatsToDatadog(ProceedingJoinPoint joinPoint) throws Throwable {
        Object retVal = null;
        Boolean pushToDatadog = true;
        Boolean isOutboundHit = false;
        retVal = logPacket(joinPoint, pushToDatadog, isOutboundHit);
        return retVal;
    }

    //@Pointcut("within(@org.springframework.stereotype.Repository *)")
    public void dbRepositoryPointCut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }
    //@Around("dbRepositoryPointCut()")
    public Object pushDBStatsToDatadog(ProceedingJoinPoint joinPoint) throws Throwable {
        Object retVal = null;
        retVal = logDbPacket(joinPoint);
        return retVal;
    }

    /*@Around("execution(* com.paytm.transportation.iocl.common.configuration.GlobalExceptionHandler.*(..)) ")
    public Object pushBadRequestStatsToDatadog(ProceedingJoinPoint joinPoint) throws Throwable {
        Object retVal = null;
        Boolean pushToDatadog = true;
        Boolean isOutboundHit = false;
        retVal = logPacket(joinPoint, pushToDatadog, isOutboundHit);
        return retVal;
    }*/

    @Around("execution(* com.paytm.saga.service.RestTemplateService.*(..))")
    public Object pushOutboundApiStatsToDatadog(ProceedingJoinPoint joinPoint) throws Throwable {
        Object retVal = null;
        Boolean pushToDatadog = true;
        Boolean isOutboundHit = true;
        retVal = logPacket(joinPoint, pushToDatadog, isOutboundHit);
        return retVal;
    }

    /*@AfterThrowing(value = "execution(* com.paytm.transportation.iocl.service.RestTemplateService.*(..))", throwing= "e")
    public void pushOutboundApiStatsToDatadog1(JoinPoint jointPoint, RestTemplateServiceException e){
        if(Objects.nonNull(e.getStatusCode()))
            pushStatusMetricToDatadog(true, getJoinPointMethod(jointPoint), jointPoint.getArgs(), null, null, e.getStatusCode().value());
        else
            pushStatusMetricToDatadog(true, getJoinPointMethod(jointPoint), jointPoint.getArgs(), null, null, 516);   //ErrorCode for ConnectionTimeout and ReadTimout Cases
    }*/


    private Object logPacket(ProceedingJoinPoint joinPoint, Boolean pushToDatadog, Boolean isOutboundHit) throws Throwable {
        String className = getJoinPointClass(joinPoint);
        String methodName = getJoinPointMethod(joinPoint);
        final Logger logger = LogManager.getLogger(joinPoint.getTarget().getClass());
        StringBuffer startMessageStringBuffer = new StringBuffer();
        CodeSignature codeSignature = (CodeSignature) joinPoint.getSignature();
        Object retVal = null;
        try {
            logger.trace("Entered in method {}.{} ", className,methodName);

            Object[] args = joinPoint.getArgs();
            int i = 0;
            for (Object arg : args) {
                logger.trace("Parameter Name {} has value {}", codeSignature.getParameterNames()[i++], arg);
            }
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            retVal = joinPoint.proceed();

            stopWatch.stop();
            //Method Exit Log
            logger.trace("Exit from method {}.{} Execution time: {} ms",className, methodName,stopWatch.getTotalTimeMillis());
            if (pushToDatadog) {
                pushHealthMetricToDatadog(isOutboundHit, methodName, args, stopWatch);
                pushStatusMetricToDatadog(isOutboundHit, methodName, args, stopWatch, retVal, Constants.CommonConstants.HTTP_SUCCESS_CODE);
            }

        } catch (Throwable ex) {
            StringBuffer errorMessageStringBuffer = new StringBuffer();
            // Create error message
            logger.error(errorMessageStringBuffer.toString(), ex);
            throw ex;
        }
        return retVal;
    }

    /*
    * This function extracts class name from CodeSignature, this is specific to repositoru only
    * */
    private String extractClassNamefromString(String codeSignatureShortString) {
        List<String> splitStrings = Arrays.asList(codeSignatureShortString.split("\\."));
        return splitStrings.get(0);
    }
    private Object logDbPacket(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = getJoinPointMethod(joinPoint);
        final Logger logger = LogManager.getLogger(joinPoint.getTarget().getClass());
        StringBuffer startMessageStringBuffer = new StringBuffer();
        CodeSignature codeSignature = (CodeSignature) joinPoint.getSignature();
        String className = extractClassNamefromString(codeSignature.toShortString());
        Object retVal = null;
        String dbLoggingString = "DB logs";
        try {
            logger.trace(dbLoggingString + " : Entered in method {}.{} ", className,methodName);

            Object[] args = joinPoint.getArgs();
            int i = 0;
            Map<String, String> argsMap = new HashMap<>();
            for (Object arg : args) {
                argsMap.put(codeSignature.getParameterNames()[i++], arg.toString());
            }
            logger.trace(dbLoggingString + " : Parameters for DB function : {}", argsMap.toString());
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            retVal = joinPoint.proceed();

            stopWatch.stop();
            //Method Exit Log
            logger.trace(dbLoggingString + " : Exit from DB method {}.{}, Execution time: {} ms",className, methodName,stopWatch.getTotalTimeMillis());
            pushDbMetricToDatadog(className, methodName, args, stopWatch);

        } catch (Throwable ex) {
            StringBuffer errorMessageStringBuffer = new StringBuffer();
            // Create error message
            logger.error(errorMessageStringBuffer.toString(), ex);
            throw ex;
        }
        return retVal;
    }

    private void pushHealthMetricToDatadog(Boolean isOutboundHit, String methodName, Object[] args, StopWatch stopWatch) {
        String metricName = "recharge_saga.healthcheck.latency";
        String[] tags = new String[2];
        tags[0] = "service_name:recharge_saga";
        if (isOutboundHit) {
            String httpRequestUrl = (String) args[0];
            tags[1] = "api:" + httpRequestUrl;
        } else {
            tags[1] = "api:/" + methodName;
        }
        monitoringClient.gauge(metricName, (double) stopWatch.getTotalTimeMillis(), 1.0, tags);
        monitoringClient.histogram(metricName, (double) stopWatch.getTotalTimeMillis(), 1.0, tags);
    }

    private void pushStatusMetricToDatadog(Boolean isOutboundHit, String methodName, Object[] args, StopWatch stopWatch, Object retval, int statusCode) {
        String metricName = "recharge_saga.healthcheck.count";
        String[] tags = new String[5];
        tags[0] = "service_name:recharge_saga";
        if (isOutboundHit) {
            String httpRequestUrl = (String) args[0];
            tags[1] = "api:" + httpRequestUrl;
            tags[2] = "httpStatusCode:" + statusCode;
        } else {
            ResponseEntity<?> responseEntity = (ResponseEntity<?>) retval;

            if(Objects.nonNull(args) && Objects.nonNull(args[0]) && args[0].getClass().equals(RequestFacade.class)){
                tags[1]="api:"+ ((RequestFacade) args[0]).getRequestURI();
            } else{
                tags[1] = "api:/" + methodName;
            }

            tags[2] = "httpStatusCode:" + responseEntity.getStatusCodeValue();
        }
        monitoringClient.incrementCounter(metricName, tags);
    }

    private void pushDbMetricToDatadog(String className, String methodName, Object[] args, StopWatch stopWatch) {
        String metricName = Constants.MetricConstants.DB_LATENCY_METRIC;
        String[] tags = new String[3];
        tags[0] = Constants.MetricConstants.SERVICE_NAME_KEY + ":" + Constants.MetricConstants.SERVICE;
        tags[1] = Constants.MetricConstants.DB_CLASS_KEY + ":" + className;
        tags[2] = Constants.MetricConstants.DB_METHOD_KEY + ":" + methodName;

        monitoringClient.gauge(metricName, (double) stopWatch.getTotalTimeMillis(), 1.0, tags);
        monitoringClient.histogram(metricName, (double) stopWatch.getTotalTimeMillis(), 1.0, tags);
        monitoringClient.incrementCounter(metricName, tags);
    }

    private String getJoinPointClass(ProceedingJoinPoint joinPoint) {
        return joinPoint.getTarget().getClass().getSimpleName();
    }

    private String getJoinPointMethod(JoinPoint joinPoint) {
        return joinPoint.getSignature().getName();
    }
}

