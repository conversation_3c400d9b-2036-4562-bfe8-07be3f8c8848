package com.paytm.saga.common.aspects;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.model.Recents;
import com.paytm.saga.service.RecentDataToKafkaService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class RecentsRepositoryAspect {

    private final Logger logger = LogManager.getLogger(RecentsRepositoryAspect.class);

    @Autowired
    private RecentDataToKafkaService recentDataToKafkaService;

    @AfterReturning(pointcut = "execution(* com.paytm.saga.recent.repository.RecentsRepository.updateRecentWhenNoExistingData(..))", returning = "result")
    public void afterSave(JoinPoint joinPoint, Object result) {
        callPushRecentDataToKafka(joinPoint, result, Constants.CDC.INSERT_OP);
    }

    @AfterReturning(pointcut = "execution(* com.paytm.saga.recent.repository.RecentsRepository.updateRecentWhenDataAlreadyExist(..))" +
            " || execution(* com.paytm.saga.recent.repository.RecentsRepository.updateRecentWhenDataAlreadyExistInInsert(..))" +
            " || execution(* com.paytm.saga.recent.repository.RecentsRepository.updateRecentWhenDataAlreadyExistIfTxnTimesIsNull(..))" +
            " || execution(* com.paytm.saga.recent.repository.RecentsRepository.updateRecentsTxnTimes(..))"
            , returning = "result")
    public void afterUpdate(JoinPoint joinPoint, Object result) {
        callPushRecentDataToKafka(joinPoint, result, Constants.CDC.UPDATE_OP);
    }

    @AfterReturning(pointcut = "execution(* com.paytm.saga.recent.repository.RecentsRepository.deleteRecentByCustomerIdAndServiceAndRecharge(..))", returning = "result")
    public void afterDelete(JoinPoint joinPoint, Object result) {
        callPushRecentDataToKafka(joinPoint, result, Constants.CDC.DELETE_OP);
    }

    private void callPushRecentDataToKafka(JoinPoint joinPoint, Object result, String op) {
        logger.info("[RecentsRepositoryAspect.callPushRecentDataToKafka] :: calling callPushRecentDataToKafka method");
        Object[] args = joinPoint.getArgs();
        if(args != null) {
            if(args.length > 0) {
                try {
                    if(args[0] != null) {
                        Recents recent = (Recents) args[0];
                        recentDataToKafkaService.pushRecentDataToKafka(recent, op);
                    } else {
                        logger.warn("[RecentsRepositoryAspect.callPushRecentDataToKafka] args[0] found null");
                    }
                } catch (Exception e) {
                    logger.error("[RecentsRepositoryAspect.callPushRecentDataToKafka] :: Error while pushing recents data into dwh kafka ", e);
                }
            } else {
                logger.error("[RecentsRepositoryAspect.callPushRecentDataToKafka] args[] found empty ");
            }
        } else {
            logger.error("[RecentsRepositoryAspect.callPushRecentDataToKafka] args[] found null ");
        }
    }
}
