package com.paytm.saga.common.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

public class RestTemplateServiceException extends Exception {

	private final HttpStatusCode statusCode;
	private final Integer rawStatusCode;
	private final String statusText;
	private final String responseBody;
	private final String detailMessage;

	public RestTemplateServiceException(HttpStatusCode statusCode, Integer rawStatusCode, String statusText,
			String responseBody, String detailMessage) {
		super(detailMessage);
		this.statusCode = statusCode;
		this.rawStatusCode = rawStatusCode;
		this.statusText = statusText;
		this.responseBody = responseBody;
		this.detailMessage = detailMessage;
	}

	public RestTemplateServiceException(Throwable throwable, HttpStatus statusCode, Integer rawStatusCode,
			String statusText, String responseBody, String detailMessage) {
		super(detailMessage, throwable);
		this.statusCode = statusCode;
		this.rawStatusCode = rawStatusCode;
		this.statusText = statusText;
		this.responseBody = responseBody;
		this.detailMessage = detailMessage;
	}

	public RestTemplateServiceException(Throwable e, String message) {
		super(message, e);
		this.detailMessage = message;
		this.rawStatusCode = null;
		this.responseBody = null;
		this.statusCode = null;
		this.statusText = null;
	}

	public String getDetailMessage() {
		return detailMessage;
	}

	public HttpStatusCode getStatusCode() {
		return statusCode;
	}

	public Integer getRawStatusCode() {
		return rawStatusCode;
	}

	public String getStatusText() {
		return statusText;
	}

	public String getResponseBody() {
		return responseBody;
	}

}
