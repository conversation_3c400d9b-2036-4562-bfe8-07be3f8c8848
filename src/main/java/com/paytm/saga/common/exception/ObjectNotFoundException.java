package com.paytm.saga.common.exception;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.paytm.saga.controller.error.ErrorCode;

public class ObjectNotFoundException extends RuntimeException implements ErrorCode {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private static final Logger logger = LogManager.getLogger(ObjectNotFoundException.class);

	public ObjectNotFoundException(final String message) {
		super(message);
	}

	@Override
	public String getErrorCode() {
		return "NE-001";
	}
}
