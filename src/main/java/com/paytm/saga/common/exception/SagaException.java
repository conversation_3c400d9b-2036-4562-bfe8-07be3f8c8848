package com.paytm.saga.common.exception;

import org.springframework.http.HttpStatus;

public class SagaException extends BaseException{
    /**
     +     *
     +     */
    private static final long serialVersionUID = 1L;

    public SagaException(String message) {
        super(HttpStatus.INTERNAL_SERVER_ERROR, message, String.format("%d", HttpStatus.INTERNAL_SERVER_ERROR.value()), null);
    }

    public SagaException(HttpStatus httpStatus, String message) {
        super(httpStatus, message, String.format("%d", httpStatus.value()), null);
    }

    public SagaException(HttpStatus httpStatus, String message, String errorCode, String title) {
        super(httpStatus, message, errorCode, title);
    }
}
