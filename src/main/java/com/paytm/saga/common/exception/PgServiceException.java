package com.paytm.saga.common.exception;

public class PgServiceException extends RechargeSagaBaseException {
    //------------------------------------------------------------------------------------------------------------------

    protected ErrorMessages errorMessages;

    /**
     * Creates a new instance of InitException
     */
    public PgServiceException() {
        super();
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Just provide a message String
     *
     * @param p_message
     */
    public PgServiceException(String p_message) {
        super(p_message);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide a message String and a root cause Exception which will chain the
     * two together.
     *
     * @param p_message
     * @param p_cause
     */
    public PgServiceException(String p_message, Throwable p_cause) {
        super(p_message, p_cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide no message String just a root cause Exception which will chain
     * the two together.
     *
     * @param p_cause
     */
    public PgServiceException(Throwable p_cause) {
        super(p_cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    public PgServiceException(ErrorMessages errorMessages) {
        super(errorMessages.getErrorMessage());
        this.errorMessages = errorMessages;
    }



    public ErrorMessages getErrorMessages() {
        return errorMessages;
    }
}
