package com.paytm.saga.common.exception;

public class OMSListenerException extends RechargeSagaBaseException {
    //------------------------------------------------------------------------------------------------------------------

    protected ErrorMessages errorMessages;

    private String kafkaPacket;

    /**
     * Creates a new instance of InitException
     */
    public OMSListenerException() {
        super();
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Just provide a message String
     *
     * @param p_message
     */
    public OMSListenerException(String p_message) {
        super(p_message);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide a message String and a root cause Exception which will chain the
     * two together.
     *
     * @param p_message
     * @param p_cause
     */
    public OMSListenerException(String p_message, Throwable p_cause) {
        super(p_message, p_cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide no message String just a root cause Exception which will chain
     * the two together.
     *
     * @param p_cause
     */
    public OMSListenerException(Throwable p_cause) {
        super(p_cause);
    }

    public String getKafkaPacket() {
        return kafkaPacket;
    }
    public void setKafkaPacket(String kafkaPacket) {
        this.kafkaPacket = kafkaPacket;
    }
//------------------------------------------------------------------------------------------------------------------

    public OMSListenerException(ErrorMessages errorMessages) {
        super(errorMessages.getErrorMessage());
        this.errorMessages = errorMessages;
    }



    public ErrorMessages getErrorMessages() {
        return errorMessages;
    }
}
