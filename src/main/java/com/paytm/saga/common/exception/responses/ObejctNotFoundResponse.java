package com.paytm.saga.common.exception.responses;

public class ObejctNotFoundResponse {
    private static final String DEFAULT_MESSAGE = "Object with id: '%s' could not be found. Make sure you used the correct id.";

    private String invalidId;
    private String message;

    public ObejctNotFoundResponse(final String id) {
        this(id, String.format(DEFAULT_MESSAGE, id));
    }

    public ObejctNotFoundResponse(final String id, final String message) {
        this.invalidId = id;
        this.message = message;
    }

    public String getInvalidId() {
        return this.invalidId;
    }

    public String getMessage() {
        return this.message;
    }
}
