package com.paytm.saga.common.exception;

public class SmsCardException extends RechargeSagaBaseException {
    //------------------------------------------------------------------------------------------------------------------

    protected ErrorMessages errorMessages;

    /**
     * Creates a new instance of InitException
     */
    public SmsCardException() {
        super();
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Just provide a message String
     *
     * @param p_message
     */
    public SmsCardException(String p_message) {
        super(p_message);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide a message String and a root cause Exception which will chain the
     * two together.
     *
     * @param p_message
     * @param p_cause
     */
    public SmsCardException(String p_message, Throwable p_cause) {
        super(p_message, p_cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide no message String just a root cause Exception which will chain
     * the two together.
     *
     * @param p_cause
     */
    public SmsCardException(Throwable p_cause) {
        super(p_cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    public SmsCardException(ErrorMessages errorMessages) {
        super(errorMessages.getErrorMessage());
        this.errorMessages = errorMessages;
    }



    public ErrorMessages getErrorMessages() {
        return errorMessages;
    }
}
