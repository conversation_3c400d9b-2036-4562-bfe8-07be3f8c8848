package com.paytm.saga.common.exception;

/**
 * Base exception for recharge saga.User of this class are advised to extend this base exception to write further meaningful
 * exception in the system.
 */
public class RechargeSagaBaseException extends Exception {
    //------------------------------------------------------------------------------------------------------------------

    protected ErrorMessages errorMessages;

    /**
     * Creates a new instance of InitException
     */
    public RechargeSagaBaseException() {
        super();
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Just provide a message String
     *
     * @param p_message
     */
    public RechargeSagaBaseException(String p_message) {
        super(p_message);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide a message String and a root cause Exception which will chain the
     * two together.
     *
     * @param p_message
     * @param p_cause
     */
    public RechargeSagaBaseException(String p_message, Throwable p_cause) {
        super(p_message, p_cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide no message String just a root cause Exception which will chain
     * the two together.
     *
     * @param p_cause
     */
    public RechargeSagaBaseException(Throwable p_cause) {
        super(p_cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    public RechargeSagaBaseException(ErrorMessages errorMessages) {
        super(errorMessages.getErrorMessage());
        this.errorMessages = errorMessages;
    }



    public ErrorMessages getErrorMessages() {
        return errorMessages;
    }
}
