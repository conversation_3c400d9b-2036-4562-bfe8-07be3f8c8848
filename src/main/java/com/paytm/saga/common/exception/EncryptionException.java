package com.paytm.saga.common.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class EncryptionException extends BaseException{
    private static final long serialVersionUID = 1L;

    public EncryptionException(String message) {
        super(HttpStatus.INTERNAL_SERVER_ERROR, message, String.format("%d", HttpStatus.INTERNAL_SERVER_ERROR.value()), null);
    }
}
