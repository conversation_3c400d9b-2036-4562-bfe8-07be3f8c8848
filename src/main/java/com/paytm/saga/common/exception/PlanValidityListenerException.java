package com.paytm.saga.common.exception;

public class PlanValidityListenerException extends RechargeSagaBaseException {
    //------------------------------------------------------------------------------------------------------------------

    /**
     * Creates a new instance of InitException
     */
    public PlanValidityListenerException() {
        super();
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Just provide a message String
     *
     * @param message
     */
    public PlanValidityListenerException(String message) {
        super(message);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide a message String and a root cause Exception which will chain the
     * two together.
     *
     * @param message
     * @param cause
     */
    public PlanValidityListenerException(String message, Throwable cause) {
        super(message, cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide no message String just a root cause Exception which will chain
     * the two together.
     *
     * @param cause
     */
    public PlanValidityListenerException(Throwable cause) {
        super(cause);
    }

    //------------------------------------------------------------------------------------------------------------------

    public PlanValidityListenerException(ErrorMessages errorMessages) {
        super(errorMessages.getErrorMessage());
        this.errorMessages = errorMessages;
    }



    @Override
    public ErrorMessages getErrorMessages() {
        return errorMessages;
    }
}
