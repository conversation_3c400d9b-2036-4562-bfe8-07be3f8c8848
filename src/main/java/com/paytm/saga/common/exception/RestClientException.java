package com.paytm.saga.common.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

@Getter
@Setter
public class RestClientException extends RuntimeException {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private String message;
    private HttpStatusCode httpStatus;

    public RestClientException(HttpStatusCode httpStatus, String message) {
        super(message);
        this.message = message;
        this.httpStatus = httpStatus;
    }

}
