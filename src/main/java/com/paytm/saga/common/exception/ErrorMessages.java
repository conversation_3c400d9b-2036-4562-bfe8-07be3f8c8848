package com.paytm.saga.common.exception;

import com.paytm.saga.common.constant.Constants;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

/***
 * Class contains all error messages that has to be sent outside
 * <NAME_EMAIL> on 2021/03/02
 */

public enum ErrorMessages {

    SAGA_SUCCESS("Success", null,HttpStatus.OK),
    SAGA_GENERIC_API_FAILURE("Something went wrong. Please try again later", "ERR_1000",HttpStatus.INTERNAL_SERVER_ERROR),
    SAGA_API_VALIDATION_FAILURE("One of the Parameter does not meet with the validation requirement","ERR_VL_001",HttpStatus.BAD_REQUEST),
    SAGA_API_AES256_FAILURE("DECRYPTION/ENCRYPTION FAILED INTERNALLY","ERR_1001",HttpStatus.INTERNAL_SERVER_ERROR),
    SAGA_API_BODY_PARSING_FAILURE("JSON parsing exception in API request body","ERR_VL_002",HttpStatus.BAD_REQUEST),

    //SAGA MARK AS PAID ERROR
    SAGA_MARK_AS_PAID_ERROR_PLAN_BUCKET_NULL("planBucket can't be null","ERR_VL_001",HttpStatus.BAD_REQUEST),
    SAGA_MARK_AS_PAID_ERROR_EXPIRY_NULL("expiry can't be null","ERR_VL_001",HttpStatus.BAD_REQUEST),
    SAGA_MARK_AS_PAID_INCORRECT_EXPIRY_FORMAT("Provide correct date format for field: expiry : " + Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT,"ERR_VL_001",HttpStatus.BAD_REQUEST),
    SAGA_MARK_AS_PAID_ERROR_DUE_DATE_NULL("due_date can't be null","ERR_VL_001",HttpStatus.BAD_REQUEST),
    SAGA_MARK_AS_PAID_ERROR_BILL_DATE_NULL("bill_date can't be null","ERR_VL_001",HttpStatus.BAD_REQUEST),
    SAGA_MARK_AS_PAID_INCORRECT_DUE_DATE_FORMAT("Provide correct date format for field: due_date : " + Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT,"ERR_VL_001",HttpStatus.BAD_REQUEST),
    SAGA_MARK_AS_PAID_INCORRECT_BILL_DATE_FORMAT("Provide correct date format for field: bill_date : " + Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT,"ERR_VL_001",HttpStatus.BAD_REQUEST),
    SAGA_MARK_AS_PAID_PREPAID_REMINDER_API_ERROR(Constants.CommonConstants.DISPLAY_MESSAGE_MAPPING.get("MARK_AS_PAID_PREPAID_FAILURE"),"ERR_RM_001",HttpStatus.CONFLICT),
    SAGA_MARK_AS_PAID_POSTPAID_REMINDER_API_ERROR(Constants.CommonConstants.DISPLAY_MESSAGE_MAPPING.get("MARK_AS_PAID_POSTPAID_FAILURE"),"ERR_RM_001",HttpStatus.CONFLICT),
    SAGA_MARK_AS_PAID_PREPAID_RECENT_API_ERROR(Constants.CommonConstants.DISPLAY_MESSAGE_MAPPING.get("MARK_AS_PAID_PREPAID_FAILURE"),"ERR_RC_001",HttpStatus.CONFLICT),
    SAGA_MARK_AS_PAID_POSTPAID_RECENT_API_ERROR(Constants.CommonConstants.DISPLAY_MESSAGE_MAPPING.get("MARK_AS_PAID_POSTPAID_FAILURE"),"ERR_RC_001",HttpStatus.CONFLICT),
    SAGA_MARK_AS_PAID_PREPAID_CASSANDRA_ERROR(Constants.CommonConstants.DISPLAY_MESSAGE_MAPPING.get("MARK_AS_PAID_PREPAID_FAILURE"),"ERR_CS_001",HttpStatus.CONFLICT),
    SAGA_MARK_AS_PAID_POSTPAID_CASSANDRA_ERROR(Constants.CommonConstants.DISPLAY_MESSAGE_MAPPING.get("MARK_AS_PAID_POSTPAID_FAILURE"),"ERR_CS_001",HttpStatus.CONFLICT),

    PG_SAVED_CARDS_MALFORMED_RESPONSE_ERROR("Got Malformed Response from PG Savedcard details API","ERR_CS_001",HttpStatus.CONFLICT),
	SMS_CARDS_MALFORMED_RESPONSE_ERROR("Got Malformed Response from PG Savedcard details API","ERR_CS_001",HttpStatus.CONFLICT),
    SMS_CARDS_INVALID_REQUEST_BODY_ERROR("Invalid Request Body","ERR_VL_001",HttpStatus.BAD_REQUEST),
    OMS_ITEMV2_MALFORMED_RESPONSE_ERROR("Got Malformed Response from OMS itemv2 API","ERR_CS_001",HttpStatus.CONFLICT),
    FETCH_RECENT_API_NON_ALLOWED_SERVICES_ERROR("Service is not allowed for this API","ERR_VL_003",HttpStatus.BAD_REQUEST),
    FETCH_RECENT_API_RECHARGE_NUMBER_MISSING("Mandatory params for non cc paytype missing i.e rechargeNumber is missing","ERR_VL_001",HttpStatus.BAD_REQUEST);


    private  String errorMessage;
    private  String errorCode;
    private HttpStatus statusCode;

    ErrorMessages(String errorMessage, String errorCode, HttpStatus statusCode) {
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.statusCode = statusCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public HttpStatus getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(HttpStatus statusCode) {
        this.statusCode = statusCode;
    }
}