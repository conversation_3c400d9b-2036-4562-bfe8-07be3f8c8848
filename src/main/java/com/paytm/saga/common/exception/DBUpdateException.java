package com.paytm.saga.common.exception;

public class DBUpdateException extends RechargeSagaBaseException {
    //------------------------------------------------------------------------------------------------------------------
    /**
     * Creates a new instance of InitException
     */
    public DBUpdateException() {
        super();
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Just provide a message String
     *
     * @param pMessage
     */
    public DBUpdateException(String pMessage) {
        super(pMessage);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide a message String and a root cause Exception which will chain the
     * two together.
     *
     * @param pMessage
     * @param pCause
     */
    public DBUpdateException(String pMessage, Throwable pCause) {
        super(pMessage, pCause);
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Provide no message String just a root cause Exception which will chain
     * the two together.
     *
     * @param pCause
     */
    public DBUpdateException(Throwable pCause) {
        super(pCause);
    }

    //------------------------------------------------------------------------------------------------------------------

    public DBUpdateException(ErrorMessages errorMessages) {
        super(errorMessages.getErrorMessage());
        this.errorMessages = errorMessages;
    }


}
