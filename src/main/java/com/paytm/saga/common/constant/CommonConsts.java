package com.paytm.saga.common.constant;

public class CommonConsts {
	public final static int BILL_OVERDUE_DAYS =3;
	public final static String PAYTM_POSTPAID_SERVICE ="paytm postpaid";
	public final static int NUMBER_OF_FINALIZE_DAYS =45;
	public final static int PLAN_EXPIRY_DAYS =4;
	public final static int VISIBLE_DIGITS_IN_MASKED_MCN = 4;
	public final static String RECHARGE_NUMBER_2_FORMAT_IDENTIFIER = "CIN";
	public final static int DEFAULT_PARTIAL_BILL_EXPIRY_DAYS =30;

	public class Query {

		public final static String RECENT_SELECT_COLUMNS=" customerId,service,recharge_number,operator,plan_bucket,mcn,is_tokenized_transaction,bill_date,due_date,due_amount,min_due_amount,original_due_amount,original_min_due_amount,nick_name_v2,channel_id,consumername_v2,cylinder_agency_name_v2,order_id,product_id,txn_amount,txn_time,txn_status,automatic_date,automatic_status,automatic_amount,recharge_number_2,recharge_number_3,recharge_number_4,recharge_number_5,recharge_number_6,recharge_number_7,recharge_number_8,bill_update_time,dismiss_action_time,is_mark_as_paid,mark_as_paid_time,mark_as_paid_amount,cin,par,tin,updated_at,paytype,circle,created_at,recentdata,not_paid_on_paytm,notification_status,plan_name,event_source,extra_info,additional_info,tag,user_data,is_transaction,is_validation,is_saved_card,new_bill_updated_at,last_failure_txn,last_pending_txn,is_new_biller,rent_tf_data,early_payment_date,early_payment_amount,pg_card_id,bbps_ref_id,recon_id,automatic_subscription_id,next_bill_fetch_date_flag,card_variant,card_skin,reminder_status,old_bill_fetch_date,txn_updated_at,card_insurance,is_new_bill_identified,current_outstanding_amount,enc_due_amount,enc_min_due_amount,enc_due_date,enc_original_due_amount,enc_original_min_due_amount,enc_current_outstanding_amount,is_encrypted,consent_valid_till_date,rent_consent_int ";

		public final static String NICKNAME_AND_CONSUMERNAME="nick_name_v2,consumername_v2";
		public final static String BANK_NAMES="bankname";
		public final static String CUSTOMER_BILLS_SELECT_COLUMNS=" customerid,due_date,service,recharge_number,operator,plan_bucket ";
		public final static String CARD_SKIN_VARIANT_SELECT_COLUMNS="variant_raw,skin_raw,variant_display_value,skin_display_value";

		public final static String RECENT_SELECT_PRIMARY_KEY_COLUMNS = " customerid,service,recharge_number,operator,plan_bucket";
		public static final String SMART_RECENTS_SELECT_COLUMNS = "customerid,service,operator,circle,created_at,event_source,paytype,updated_at,rechargenumber,product_id";
		public static final String CAR_DETAIL_SELECT_COLUMNS="make,model,variant_id,variant,display_name";
		public static final String CAR_DETAIL_COLOUR_SELECT_COLUMNS="make,model,colour,image_url";

	}
}
