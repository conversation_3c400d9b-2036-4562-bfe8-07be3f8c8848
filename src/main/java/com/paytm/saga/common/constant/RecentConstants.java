package com.paytm.saga.common.constant;

import java.util.HashMap;
import java.util.Map;

public class RecentConstants {

    public final static String DISPLAY_VALUES = "displayValues";
    public final static String CUSTOM_CODE = "customCode";

    public final static String RECON_ID = "reconId";

    public final static String BILL_TYPE = "bill_type";

    public final static String TXN_TIMES_COUNT = "txnTimesCount";

    public final static Map<String, String> ADDITIONALINFO_KEYS_MAPPER =  new HashMap<String, String>() {{
        put("Mobile No.", "mobile_number");
        put("mobileNumber", "mobile_number");
        put("Subscriber ID", "subscription_id");
        put("customCode", "customCode");
    }};
}
