package com.paytm.saga.common.constant;

import com.paytm.saga.enums.BillState;

import java.util.Arrays;
import java.util.List;

public final class BillStateTypes {
	public static final int ACTIVE_BILL = 1;
	public static final int BILL_OVERDUE = 2;
	public static final int BILL_AFTER_OVERDUE = 3;
	public static final int NEW_BILL_GENERATED = 4;
	public static final int BILL_PARTIAL_PAID = 5;
	public static final List<BillState> AUTOMATIC_BILL_STATES = Arrays.asList(BillState.EXPIRES_TODAY_AUTOMATIC,BillState.EXPIRES_TOMORROW_AUTOMATIC,BillState.WILL_EXPIRE_AUTOMATIC);
}
