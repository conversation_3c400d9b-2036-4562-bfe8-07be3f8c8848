package com.paytm.saga.common.constant;

public class EncryptionConstants {
	public static final String AES256_ERROR = "AES256_ERROR";
	public static final String AES256_ERROR_FROM_API = "AES256_ERROR_FROM_API";
	public static final String ENCRYPTION = "ENCRYPTION";
	public static final String DECRYPTION = "DECRYPTION";
	public static final String PARSING = "PARSING";
	public static final String ENCRYPTION_ON_REMINDER_RESPONSE_MODEL = "encryption_on_reminder_response_model";
	public static final String ENCRYPTION_ON_CUSTOMERIDS = "encryption_on_customerIds";
	public static final String ENABLE_DATA_ENCRYPTION_ON_CUSTOMER_FLAG = "enable_data_encryption_on_customer_flag";
	public static final String ENABLE_DATA_ENCRYPTION_FLAG = "enable_data_encryption_flag";
	public static final String ALLOWED_ROLLOUT_PERCENTAGE = "allowed_rollout_percentage";
	public static final String EXCLUDED_RECO_SERVICES = "excluded_reco_services";
	public static final String CUSTOMER_OTHER_INFO_ENCRYPTED_KEYS = "customer_other_info_encrypted_keys";
	public static final String REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS = "reminder_response_extra_encrypted_keys";
	public static final String USER_DATA_ENCRYPTED_KEYS = "user_data_encrypted_keys";
	public static final String USER_DATA = "USER_DATA";
	public static final String DECRYPT_RECENT = "DECRYPT_RECENT";
	public static final String ENCRYPT_RECENT = "ENCRYPT_RECENT";
	public static final String DUE_DATE_PARSE_ERROR = "DUE_DATE_PARSE_ERROR";
	public static final String RECENT_INSERT_TO_DB_ERROR = "RECENT_INSERT_TO_DB_ERROR";
	public static final String RECENT_UPDATE_TO_DB_ERROR = "RECENT_UPDATE_TO_DB_ERROR";
	public static final String RECENT_DELETE_FROM_DB_ERROR = "RECENT_DELETE_FROM_DB_ERROR";
	public static final String RECENT_SELECT_FROM_DB_ERROR = "RECENT_SELECT_FROM_DB_ERROR";
	public static final String REMINDER_HISTORY_SAVE_TO_DB_ERROR = "REMINDER_HISTORY_SAVE_ERROR";
	public static final String REMINDER_HISTORY_SELECT_FROM_DB_ERROR = "REMINDER_HISTORY_SELECT_FROM_DB_ERROR";
	public static final String CHANNEL_HISTORY_SELECT_FROM_DB_ERROR = "CHANNEL_HISTORY_SELECT_FROM_DB_ERROR";
	public static final String CHANNEL_HISTORY_SAVE_ERROR = "CHANNEL_HISTORY_SELECT_FROM_DB_ERROR";
	public static final String CHANNEL_HISTORY_FINALIZED_SELECT_FROM_DB_ERROR = "CHANNEL_HISTORY_SELECT_FROM_DB_ERROR";
	public static final String CHANNEL_HISTORY_FINALIZED_SAVE_ERROR = "CHANNEL_HISTORY_SELECT_FROM_DB_ERROR";
	public static final String PARSE_EXTRA_ERROR = "PARSE_EXTRA_ERROR";
	public static final String PARSE_CUSTOMER_OTHER_INFO_ERROR = "PARSE_CUSTOMER_OTHER_INFO_ERROR";
	public static final String ENCRYPTION_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
}
