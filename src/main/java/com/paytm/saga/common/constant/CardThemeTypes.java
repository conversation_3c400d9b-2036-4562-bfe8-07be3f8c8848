package com.paytm.saga.common.constant;

public class CardThemeTypes {
	public final static String SUGGESTED_RECHARGE_CARD_THEME = "suggested_recharge";
	public final static String SUCCESS_RECHARGE_CARD_THEME = "prepaid_recharge_success";
	public final static String FAILURE_RECHARGE_CARD_THEME = "prepaid_recharge_failure";
	public final static String PENDING_RECHARGE_CARD_THEME = "prepaid_recharge_pending";
	public final static String CANCELLED_RECHARGE_CARD_THEME = "prepaid_recharge_cancelled";
	public final static String MARKED_AS_PAID_CARD_THEME = "recharge_marked_as_paid";
	public final static String DATE_CHANGE_CARD_THEME = "date_change";
	public final static String MNP_CARD_THEME = "prepaid_mnp";
	public final static String CASHBACK_CARD_THEME = "prepaid_recharge_scratch_card";
	public final static String SUGGESTED_RECHARGE_RECHARGED_ALREADY_CARD_THEME = "suggested_recharge_already_recharged";
	public final static String SUGGESTED_RECHARGE_INVALID_PLAN_CARD_THEME = "suggested_recharge_invalid_plan";
	public final static String SUCCESS_CYLINDER_BOOKING_CARD_THEME = "prepaid_cylinder_booking_success";
	public final static String FAILURE_CYLINDER_BOOKING_CARD_THEME = "prepaid_cylinder_booking_failure";
	public final static String PENDING_CYLINDER_BOOKING_CARD_THEME = "prepaid_cylinder_booking_pending";
	public final static String PAYMENT_PENDING_CYLINDER_BOOKING_CARD_THEME = "prepaid_cylinder_booking_payment_pending";
	public final static String CANCELLED_CYLINDER_BOOKING_CARD_THEME = "prepaid_cylinder_booking_cancelled";
	public final static String DROPOFF_CARD_THEME = "dropoff_card";
	public final static String REVERSAL_FAILURE_CYLINDER_BOOKING_CARD_THEME = "prepaid_cylinder_booking_reversal_failure";
	public final static String PAYMENT_FAILURE_CYLINDER_BOOKING_CARD_THEME = "prepaid_cylinder_booking_payment_failure";
	public final static String NEW_BILL_CARD_THEME = "new_bill";
	public final static String BILL_OVER_DUE_CARD_THEME = "bill_overdue";
	public final static String BILL_PARTIAL_PAID_CARD_THEME = "bill_partial_paid";
	public final static String BILL_AFTER_OVER_DUE_CARD_THEME = "bill_after_overdue";
	public final static String ELECTRICITY_SUCCESS_RECHARGE_CARD_THEME = "electricity_recharge_success";
	public final static String ELECTRICITY_FAILURE_RECHARGE_CARD_THEME = "electricity_recharge_failure";
	public final static String ELECTRICITY_PENDING_RECHARGE_CARD_THEME = "electricity_recharge_pending";
	public final static String BILL_EXPIRED_CARD_THEME = "electricity_bill_expired";
}
