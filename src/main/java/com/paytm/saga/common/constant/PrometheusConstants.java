package com.paytm.saga.common.constant;

public class PrometheusConstants {

    public static final class METRIC_NAME {
        public static final String TXN_TIME_MIGRATION_PRODUCER = "TXN_TIME_MIGRATION_PRODUCER";
        public static final String TXN_TIME_DB_UPDATE = "TXN_TIME_DB_UPDATE";
    }

    public static final class TAG_KEY {
        public static final String STATUS = "STATUS";
        public static final String REASON = "REASON";
    }

    public static final class TAG_VALUE {

        public static final String SUCCESS = "SUCESS";
        public static final String SKIP = "SKIP";
        public static final String NOT_FOUND = "NOT_FOUND";
        public static final String CATEGORY_NOT_ENABLED = "CATEGORY_NOT_ENABLED";

        public static final String AUTOMATIC_ENABLED = "AUTOMATIC_ENABLED";

        public static final String TXN_TIMES_THRESHOLD_REACHED = "TXN_TIMES_THRESHOLD_REACHED";

    }

    public static final class STATE {
        public static final String RECENT_SIZE = "RECENT_SIZE";
        public static final String DROPOFF_SIZE = "DROPOFF_SIZE";
        public static final String RECENTS_TO_FILTERED_RECENT_DIFF = "RECENTS_TO_FILTERED_RECENT_DIFF";
        public static final String RECENTS_TO_FINAL_RECENT_DIFF = "RECENTS_TO_FINAL_RECENT_DIFF";
        public static final String ORDER_UPSERT_SUCCESS = "ORDER_UPSERT_SUCCESS";
        public static final String ORDER_UPSERT_FAIL = "ORDER_UPSERT_FAIL";
    }

    public static final class FASTAG {
        public static final String LOW_BALANCE_SKIPPABLE = "LOW_BALANCE_SKIPPABLE";
    }
}
