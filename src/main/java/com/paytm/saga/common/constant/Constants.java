package com.paytm.saga.common.constant;

import com.paytm.saga.enums.BillState;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class Constants {
	public static final String LANGUAGE_ENGLISH = "en-IN";
	public static final String CARD_NETWORK_DUMMY = "dummyNetwork";
	public static final String BROADBAND = "broadband";
	public static final String LANDLINE = "landline";
	public static final Integer DEFAULT_TTL = 548 * 24 * 60 * 60;
	public static final String X_USER_ID_HEADER = "X-USER-ID";
	public static final String NATIVE_THEME_HASH = "1234243453233431";
	public static final int SAVED_CARD_PRIORIY = 3;
	public static final String SMS_CARD = "smsCard";
	public static final String BILL_PAID_IDENTIFIER_KEY = "eventState";
	public static final String BILL_PAID_IDENTIFIER_VALUE = "bill_paid";
	public static final String FASTAG_RECENTS = "fastag_recents";
	public static final String DUPLICATE = "DUPLICATE";
	public static final String LOW_BALANCE_IDENTIFIER_KEY = "type";
	public static final String LOW_BALANCE_DATE_KEY = "low_balance_date";
	public static final String SMS_DATE_TIME = "smsDateTime";

	public static final String FASTAG_LOW_BALANCE_OPERATOR = "LOW_BALANCE";
	public static final Double FASTAG_LOW_BALANCE_MIN_AMOUNT = 2.0;
	public static final Double FASTAG_LOW_BALANCE_DEFAULT_AMOUNT = 2.0;
	public static final String FASTAG = "fastag recharge";
	public static final String LOAN = "loan";
	public static final String DATA_PACK = "Data Pack";
	public static final String SPECIAL_RECHARGE = "Special Recharge";
	public static final String SUGGESTED_CARD = "SUGGESTED_CARD";
	public static final String ALL_CIRCLES = "all circles";
	public static final String NULL_FUFILLMENT_TIME_STRING = "0000-00-00 00:00:00";
        public static final Integer CIR_TTL =6 * 30 * 24 * 60 * 60;
	public static final String CC_CARD = "CC";
	public static final int SMS_CARD_PRIORIY = 4;
	public static final String DROP_OFF_WRITE = "write";
	public static final String SMS_API_CUSTOMER_ID_REQUEST_PARAM = "customerId";
	public static final String SMS_API_SERVICE_REQUEST_PARAM = "service";
	public static final String DROP_OFF_READ = "read";
	public static final String NEW_USER_IDENTIFIER = "newUser";
	public static final String SUNTV = "suntv";
	public static final List<String> REMINDER_PLAN_BUCKET_OPERATOR = Arrays.asList("dishtv", "d2h (formerly videocon d2h)", "suntv");
	public static final String TXN_ID = "txn_id";
	public static final String[] DTH_GROUP =  {"dishtv", "d2h (formerly videocon d2h)"};
	public static final String OPERATOR_VALIDATION ="operator_validation";
	public static final String UPDATED_SOURCE_KEY ="updated_data_source";
	public static final String TRANSACTION_SOURCE_KEY ="transaction";
	public static final String VALIDATION_SYNC_SOURCE_KEY ="validationSync";
	public static final String VALIDITY_EXPIRED ="isValidityExpired";
	public static final String FFR_SOURCE_KEY ="ffr";
	public static final String PAYMENT_DATE_TIME_KEY="payment_date";
	public static final String BILL_FETCH_DATE_TIME_KEY="billFetchDate";
	public static final String SMS_DATE_TIME_KEY ="sms_date_time";
	public static final String[] SMS_PARSING_SOURCES =  {"SMS_PARSING_DWH_REALTIME", "SMS_PARSING_DWH_MANUAL","SMS_PARSING_DWH","SMS_PARSING_REALTIME"};
	public static final String[] DROPOFF_SERVICES_IGNORE_OPERATOR =  {"mobile"};
	public static final String DTH_PLAN_BUCKET= "dthDefaultBucket";
	public static final String CYLINDER_AGENCY_NAME="cylinder_agency_name";
	public static final String PLAN_VALIDITY_DUMMY_CATEGORY = "Recharge";
	public static final String ERROR_TAG = "ERROR";
	public static final String IGNORE_TAG = "IGNORE_TAG";
	public static final String PREPAID_BILLS_DELETE_EVENT_ERROR_TAG = "DELETE_EVENT_ERROR";
	public static final String DCAT_API_ERROR = "DCAT_API_ERROR";
	public static final String PLAN_EXPIRY_HISTORY_SAVE_ERROR = "PLAN_EXPIRY_HISTORY_SAVE_ERROR";
	public static final String RECENT_UPDATE_ERROR = "RECENT_UPDATE_ERROR";
	public static final String DELETE_RECENT_ERROR_TAG = "DELETE_RECENT_ERROR_TAG";
	public static final String DELETE_RECENT_SERVICE = "DELETE_RECENT";
	public static final String NICK_NAME_UPDATE_SERVICE = "NICK_NAME_UPDATE";
	public static final String NICK_NAME_UPDATE_ERROR_TAG = "NICK_NAME_UPDATE_ERROR_TAG";
	public static final String NICK_NAME_UPDATE_USER_NOT_EXISTS_TAG = "NICK_NAME_UPDATE_USER_NOT_EXISTS_TAG";
	public static final String NICK_NAME_UPDATE_INAVLID_LENGTH_TAG = "NICK_NAME_UPDATE_INVALID_LENGTH_TAG";
	public static final String DROPOFF_INSERTION_TAG = "DROPOFF_INSERTION_TAG";
	public static final String DROP_OFF_INSERTION_ERROR = "DROPOFF_INSERT_ERROR";
	public static final String FETCH_RECENTS_SERVICE = "FETCH_RECENTS";
	public static final String INVALID_PID = "INVALID_PID";
	public static final String IS_PAYTM_VPA = "isPaytmVPA";
	public static final String IS_DUMMY_RN = "isDummyRN";
	public static final String SMS_RN = "smsRechargeNumber";
	public static final String IS_PAYMODE_CHANGED = "isPaymodeChanged";
	public static final String FETCH_LAST_TWO_RECENTS_SERVICE = "FETCH_LAST_TWO_RECENTS_SERVICE";
	public static final String SUGGESTED_CARDS_SERVICE = "SUGGESTED_CARDS_SERVICE";
	public static final String OLD_CREATED_AT = "oldCreatedAt";


	public static final String FETCH_LAST_TWO_RECENTS_MISMATCHED_PLANS = "FETCH_LAST_TWO_RECENTS_MISMATCHED_PLANS";
	public static final String PLAN_BUCKET_STAMPING = "PLAN_BUCKET_STAMPING";

	public static final String NO_RECENT = "NO_RECENT";
	public static final String RECENT_BUT_NO_TXN = "RECENT_BUT_NO_TXN";

	public static final String RECENT_BUT_NO_MATCHING_PLAN_BUCKET = "RECENT_BUT_NO_MATCHING_PLAN_BUCKET";
	public static final String FETCH_RECENTS_SUCCESS = "FETCH_RECENTS_SUCCESS";
	public static final String FETCH_RECENTS_ERROR = "FETCH_RECENTS_ERROR";
	public static final String FETCH_RECENTS_BY_RECHARGE_NUMBER = "FETCH_RECENTS_ERROR";
	public static final String FETCH_RECENTS_BY_CUSTOMER_ID = "FETCH_RECENTS_ERROR";

	public static final String NEW_USER_KEY = "newUser";
	public static final String FINANCIAL_SERVICES = "financial services";
	public static final String RENT_PAYMENT = "rent payment";
	public static final String BUSINESS_PAYMENT = "business payment";

	public static final Integer FINANCIAL_SERVICES_VALIDATION_DROP_OFF_THRESHOLD_IN_MINUTES_FOR_NEW = 60 * 24 * 60;// in minutes
	public static final Integer FINANCIAL_SERVICES_RECHARGE_DROP_OFF_THRESHOLD_IN_MINUTES_FOR_NEW = 60 * 24 * 60;// in minutes
	public static final Integer DEFAULT_VALIDATION_DROP_OFF_THRESHOLD_IN_MINUTES_FOR_NEW = 30 * 24 * 60;// in minutes
	public static final Integer DEFAULT_RECHARGE_DROP_OFF_THRESHOLD_IN_MINUTES_FOR_NEW = 30 * 24 * 60;// in minutes
	public static final String FINANCIAL_SERVICE = "financial services";
	public static final String[] SAVED_CARDS_OMS_STATES = {"SUCCESS", "CANCEL", "REVERSAL_FAILURE", "PAYMENT_FAILURE", "FAILURE", "PAYMENT_PENDING", "PENDING"};
	public static final Integer SAVED_CARD_BILL_VISIBLE_THRESHOLD_DAYS = 3;
	public static final String[] VALID_PLAN_BUCKETS = {"Special Recharge", "Data Pack", "Top Up", "Roaming"};


	public static final String CREDIT_CARD_PAYTYPE = "credit card";
	public static final String PREPAID_PAYTYPE = "prepaid";
	public static final String SAVED_CARDS_BILLS_APPEND_FLAG = "appendBills";
	public static final String SAVED_CARDS_BILLS_APPEND_FLAG_TRUE = "true";
	public static final String SAVED_CARDS_BILLS_APPEND_FLAG_FALSE = "false";
	public static final String SAVED_CARDS_COFT_FLAG = "isCoft";
	public static final String NAME_REQUIRED = "isNameRequired";
	public static final String WIDTH_HANDLING = "px";
	public static final String PARTIAL_BILL_STATE = "partialBillState";
	public static final String PARTIAL_BILL_DATE = "partialBillDate";
	public static final String UPI_CARD_MCN_PREFIX = "XXXX XXXX XXXX XX";
	public static final String ENABLE_UPI_CARD_WRITES = "enableUpiCardWrites";
	public static final String INVALID_LAST_2_CARD_DIGITS = "invalidLast2CardDigits";

	public static String PAID_OUTSIDE_PAYTM = "PAID_OUTSIDE_PAYTM";
	public static final String SUCCESS = "SUCCESS";

	public static final String ONLY_ACTIVE_BILL_FLAG = "onlyActiveBills";

	public static final String INVALID_CONFIG = "INVALID_CONFIG";
	public static final String DROPOFF_DEFAULT_AMOUNT = "1";
	public static final String RECHARGE_SUCCESS_STATUS = "7";
	public static final String SUCCESS_IN_CODE = "00";
	public static final String TRUE_STRING = "true";
	public static final String OP_UPDATE = "update";
	public static final String OP_INSERT = "insert";
	public static final String OP_DELETE = "delete";
	public static final String NULL_STRING="null";
	public static final String UPDATED_SOURCE="updated_source";
	public static final String CREATED_SOURCE="created_source";
	public static final String UPDATED_DATA_SOURCE="updated_data_source";
	public static final String CA_IDENTIFY_SUFFIX = "CA_IDENTIFY";
	public static final String BILL_TYPE_SAVED_CARD="savedCard";
	public static final String BILL_TYPE_TRANSACTION="transaction";
	public static final String BILL_TYPE_VALIDATION="validationSync";
	public static final String BILL_TYPE_SMS="sms";
	public static final String BILL_AMOUNT_NOT_PRESENT="bill_Amount_Not_Present";
	public static final String CDC_P2P_CONSUMER="CDC_P2P_CONSUMER";
	public static final String CDC_SMS_CONSUMER="CDC_SMS_CONSUMER";
	public static final String METHOD_POST = "POST";
	public static final List<String> UPPCL_NEW_OPERATORS = Arrays.asList("kanpur electricity supply company", "purvanchal vidyut vitaran limited (puvvnl)", "paschimanchal vidyut vitran nigam limited (pvvnl)", "madhyanchal vidyut vitran nigam limited (mvvnl)", "dakshinanchal vidyut vitran nigam limited (dvvnl)", "kanpur electricity supply company (kesco)");
	public static final String SMS_CARD_EVENT_STATE = "eventState";
	public static final String PARTIAL_BILL_EVENT_KEY = "partialBillState";
	public static final String IS_PREPAID_KEY = "isPrepaid";
	public static final String IS_PREPAID_ENABLED = "isPrepaidEnabled";
	public static final String SUBSCRIPTION_UPDATED_AT = "subscriptionUpdatedAt";
	public static final String SUBSCRIPTION_IS_AUTOMATIC = "subscriptionIsAutomatic";
	public static final String TRANSACTION_UPDATE_TIME = "transactionUpdateTime";
	public static final Integer PDN_FAILURE_REMINDER_IS_AUTOMATIC_STATUS = 3;


	public static final String SMS_CARD_TXN = "bill_paid";

	public static final Integer OLD_BILL_REMINDER_STATUS = 5;
	public static final Integer PAYMENT_DONE_REMINDER_STATUS = 11;
	public static final String PENALTY_AMOUNT = "penaltyAmount";
	public static final String AIRTEL_PREPAID_BILL_FETCH = "AIRTEL_PREPAID_BILL_FETCH";
	public static final String AIRTEL_PREPAID_BILL_FETCH_RECO_DISMISS = "AIRTEL_PREPAID_BILL_FETCH_RECO_DISMISS";

	public static final String[] SUBSCRIPTION_SERVICES = {"electricity","insurance","landline","broadband","water","gas"};

	private Constants() {
	}

	public static final class CommonConstants {
		public static final int EVICT_CACHE_RETRY = 3;
		public static final int ES_RETRY = 3;
		public static final String DEFAULT_PLAN_BUCKET = "NA";
		public static final String DONOT_REMINDME_FLAG = "donotRemindMeDropOffFlag";
		public static final String BILLS_OBJ_PLAN_BUCKET_KEY = "plan_bucket";
		public static final Map<String, String> DCAT_CATEGORY_MAP = new HashMap<String, String>() {
			/**
			 *
			 */
			private static final long serialVersionUID = 1L;

			{
				put("mobile", "7166");
				put("Mobile", "7166");
				put("dth", "7167");
				put("datacard", "7171");
			}
		};
		public static final Map<String, String> DCAT_GROUPING_ID_MAP = new HashMap<String, String>() {{

			put("mobile", "17");

			put("dth", "18");

			put("datacard", "19");

			put("financial services", "156705");

		}};


		public static final Map<String, String[]> DCAT_GROUPING_BODY_MAP = new HashMap<String, String[]>() {{

			put("mobile", new String[]{"operator", "circle"});

			put("dth", new String[]{"operator_label"});

			put("datacard", new String[]{"operator", "circle"});

		}};

		public static final Map<String, Integer> VALIDATION_DROP_OFF_THRESHOLD_IN_MINUTES = new HashMap<String, Integer>() {{
			put("mobile", 30 * 24 * 60);
			put("electricity", 30 * 24 * 60);
			put("financial services", 30 * 24 * 60);
			put("FASTag Recharge", 30 * 24 * 60);
		}};

		public static final Map<String, Integer> RECHARGE_DROP_OFF_THRESHOLD_IN_MINUTES = new HashMap<String, Integer>() {{
			put("mobile", 30 * 24 * 60);
			put("electricity", 30 * 24 * 60);
			put("financial services", 30 * 24 * 60);
			put("FASTag Recharge", 30 * 24 * 60);
		}};

		public static final Integer VALIDATION_DROP_OFF_DEFAULT_THRESHOLD_IN_MINUTES = 30 * 24 * 60;
		public static final Integer RECHARGE_DROP_OFF_DEFAULT_THRESHOLD_IN_MINUTES = 30 * 24 * 60;
		public static final Integer READ_TIMEOUT = 1000;
		public static final String DCAT_CATEGORY_CACHE_KEY_PREFIX = "DCAT_CATEGORY";
		public static final Integer DCAT_CATEGORY_CACHE_TTL = 24 * 60 * 60;

		public static final Map<String, String> ERROR_CODE_MAPPING = new HashMap<String, String>() {{
			put("REMINDER_ERROR_001", "ERR_RM_001");
			put("RECENT_ERROR_001", "ERR_RC_001");
			put("CASSANDRA_ERROR_001", "ERR_CS_001");
		}};


		public static final Map<String, String> DISPLAY_MESSAGE_MAPPING = new HashMap<String, String>() {{
			put("MARK_AS_PAID_SUCCESS", "Success");
			put("DO_NOT_REMIND_ME_SUCCESS", "Success");
			put("MARK_AS_PAID_FAILURE", "Something went wrong. Please try again  after some time");
			put("MARK_AS_PAID_PREPAID_FAILURE", "Unable to mark as recharged right now. Please try again after some time");
			put("MARK_AS_PAID_POSTPAID_FAILURE", "Unable to mark as paid right now. Please try again after some time");
		}};

		public static final Integer CONNECTION_TIMEOUT = 1000;
		public static final Integer CONNECTION_REQUEST_TIMEOUT = 5 * 1000;
		public static final Integer HTTP_SUCCESS_CODE = 200;
		public static final Integer TOTAL_MAX_CONNECTION_COUNT = 2500;
		public static final Integer MAX_CONNECTION_ROUTE_COUNT = 1000;
		public static final Integer STALE_CONNECTION_CHECK_TIME = 200;
		public static final String DCAT_LOCALE = "en-IN";
		public static final String DCAT_VERSION = "12.12.12";
		public static final String DCAT_CHANNEL = "web";
		public static final String DCAT_CACHE_KEY_PREFIX = "DCAT_PLAN";
		public static final String CACHE_KEY_DELIMTER = "_";
		public static final Integer DCAT_CACHE_TTL = 5 * 60;
		public static final Integer PLAN_VALIDITY_DEFAULT_TTL = 90 * 24 * 60 * 60;
		public static final String BROWSE_PLAN_URL = "https://inmock.paytm.com/dcat/v1/browseplans/";
		public static final String RECHARGE_MESSAGE_TYPE = "RECHARGE";
		public static final String MARK_AS_PAID_MESSAGE_TYPE = "MARK_AS_PAID";
		public static final String[] ACCEPTABLE_RECENT_ERROR_CODES = {"00", "ERR08"};
		public static final Integer THREAD_POOL_SIZE = 500;
		public static final String RECENT_CRUD_DATE_FORMAT = "yyyy-MM-dd";
		public static final String RECENT_CRUD_NOW_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
		public static final String TRANSACTION_UPDATED_DATE_FORMAT = "EEE MMM dd HH:mm:ss zzz yyyy";
		public static final String RECENT_CRUD_STATIC_TIME = " 23:59:59";
		public static final String[] PLAN_SERVICES = {"mobile", "dth", "datacard"};
		public static final String BILLS_OBJ_MARK_AS_PAID_DROP_OFF_FLAG = "markAsPaidDropOffFlag";
		public static final String BILLS_OBJ_CIN = "cin";
		public static final String BILLS_OBJ_MCN = "mcn";
		public static final String[] WITHOUT_CIRCLE_SERVICES = {"dth"};
		public static final String[] WITHOUT_PLAN_BUCKET_SERVICES = {"dth"};
		public static final Integer DEFAULT_AMOUNT_REQUEST_VALUE = 1;
		public static final String PAYTM_FIRST_CARD_DISPLAY_NAME = "paytm";
		public static final String IS_COFT_FLAG_ENABLED = "true";
		public static final String IS_COFT_FLAG_DISABLED = "false";

		public static final String MARK_AS_PAID_ENDPOINT = "/markAsPaid";
		public static final String REMINDER_SERVICE_MARK_AS_PAID = "ReminderService";
		public static final String RECENT_CRUD_UPDATE = "RecentsCRUDUpdate";
		public static final String BILLS_SYNC_MARK_AS_PAID = "BillsSyncServiceMarkAsPaid";


		public static final Boolean ENABLE_PENDING_CTA = false;

		public static final Integer[] FS_VERTICAL_IDS = { 4, 17, 56, 71, 76, 83, 84, 86, 90, 105, 115, 155, 187,94};

		public static final String[] SKIP_PAYTYPES = {};
		public static final String VALIDATION_MESSAGE_TYPE = "VALIDATION";
		public static final String NOT_PAID_ON_PAYTM_MESSAGE_TYPE = "NOT_PAID_ON_PAYTM";
		public static final Long[] DROP_OFF_ENABLED_CATEGORY_IDS = {17L, 26L, 21L, 166690L, 156705L, 18L, 75505L, 47464L, 92791L, 68869L, 78640L, 132063L, 216852L, 222633L, 64739L, 208100L, 37217L, 198239L, 206334L, 104154L, 6239L, 46007L, 46006L, 190833L, 193483L, 199289L, 101950L, 123988L, 206896L, 107730L, 262072L, 289829L, 207075L, 274462L};
		public static final Long[] DROP_OFF_SKIP_VALIDATION_CATEGORY_IDS = {262072L, 289829L};
		public static final String[] DROP_OFF_CURRENT_BILL_AMOUNT_SERVICES = {"cylinder booking"};
		public static final String[] VALIDATION_UNSUCCESSFULL_ERROR_CODES = {"BFR001"};
		public static final String[] VALIDATION_UNSUCCESSFULL_SERVICE = {"electricity", "FASTag Recharge"};

		public static final String KAFKA_POOL_SIZE = "500";
		public static final String COMMON_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSX";
		public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
		public static final Integer VALIDATION_CONSUMER_LAG_TIME = 300;  //in seconds
		public static final Long[] OMS_SUCCESS_STATUS = {7L};
		public static final String[] OMS_PENNYDROP_STATUS = {"210"};
		public static final Long[] OMS_FAILED_STATUS = {6L, 18L, 34L};
		public static final Integer[] OMS_PAYMENT_FAILED_STATUS = {4, 8};


		public static final Integer FINALISED_THRESHOLD_DATE = 45;

		public static final Integer RECHARGENUMBER_TO_CUSTOMER_LIST_THRESHOLD = 5000;

		public static final Integer RN_TO_CUSTID_SIZE_LOGTHRESHOLD = 100;

		public static final String[] SCRATCH_CARD_SKIP_STATUS = {"INITIALIZED"};
		public static final String[] SCRATCH_CARD_TRIGGER_DELETE_STATUS = {"REDEEMED", "CANCELLED", "EXPIRED"};
		public static final String[] SCRATCH_CARD_ALLOWED_SOURCE = {"RECHARGES"};

		public static final String[] DROP_OFF_SKIP_CHANNEL_ID = {"digital-reminder", "SUBS 1", "subs 1","digital-ebps"};
		public static final Long[] DROP_OFF_SKIP_CUSTOMER_ID = {0L};

		public static final Integer[] REMINDER_SKIP_STATUS = {1, 13, 15};

		public static final Integer[] REMINDER_CDC_RECOVERY_SKIP_STATUS = {1};
		public static final String[] REMINDER_SKIP_TABLES = {};
		public static final List<Integer> REMINDER_VALID_AIRTEL_BILL_FETCH_STATUS = Arrays.asList(4);
		public static final List<Integer> REMINDER_INVALID_AIRTEL_BILL_FETCH_STATUS = Arrays.asList(130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 14);
		public static final Integer REMINDER_TTL_ADD_SECONDS = 30 * 24 * 60 * 60;
		public static final Integer REMINDER_OLD_DUE_DATE_THRESHOLD = 35 * 24 * 60 * 60;
		public static final Integer SECONDS_IN_DAY = 24 * 60 * 60;
		public static final String ANDROID_APP_DROPOFF_VERSION = "9.6.0";
		public static final String IOS_APP_DROPOFF_VERSION = "9.6.0";
		public static final String[] IOS_APP_CLIENT_NAMES = {"iosapp"};
		public static final String[] ANDROID_APP_CLIENT_NAMES = {"androidapp"};


		public static final String OMS_PENDING_STATE = "PENDING";
		public static final String OMS_SUCCESS_STATE = "SUCCESS";
		public static final String OMS_FAILURE_STATE = "FAILURE";

		public static final String[] OMS_FAILURE_STATES = {"FAILURE","CANCEL","REVERSAL_FAILURE","PAYMENT_FAILURE"};

		public static final String[] OMS_PEDING_STATES = {"PAYMENT_PENDING","PENDING"};



		public static final String CYLINDER_SERVICE = "cylinder booking";
		public static final String DTH_SERVICE = "dth";
		public static final String PREPAID_PAYTYPE = "prepaid";
		public static final String POSTPAID_PAYTYPE = "postpaid";
		public static final String[] POSTPAID_PAYTYPES = {"postpaid", "loan", "insurance", "fee payment", "new registration"};

		//This port is for Prometheus
		public static final Integer MONITORING_CLIENT_PORT = 8130;

		public static final String TOKEN_TYPE_JWT = "JWT";
		public static final String PG_SAVED_CARD_V2_CLIENT_ID = "CC_BILL_PAYMENT";
		public static final String PG_SAVED_CARD_V2_SUCCESS_HTTP_CODE = "200";
		public static final String PG_SAVED_CARD_V2_SUCCESS_HTTP_SUB_CODE = "200";
		public static final String PG_SAVED_CARD_V2_NO_CARDS_HTTP_SUB_CODE = "204";

		public static final String CHANNEL = "channel";
		public static final List<Integer> INACTIVE_PID_STATUS = Arrays.asList(0, 2);
		public static final List<String> UTILITY_PAY_TYPES = Arrays.asList("postpaid", "loan", "insurance");
		public static final String PAN_UNIQUE_REFERENCE = "panUniqueReference";
		public static final String TIN = "tin";
		public static final String TOTAL_DUE = "total_due";
		public static final String PARTIAL_DUE = "partial_due";

		public static final String[] vodaOperators = {"vodafone idea", "vodafone", "idea"};

		public static final String UPDATE_LITERAL = "Update";
		public static final String INSERT_LITERAL = "Insert";
		public static final String CC_DEFAULT_OPERATOR = "ccbp";
		public static final Integer FETCH_RECENT_DEFAULT_LIMIT = 100;
		public static final String RECENT_DATA_ORDER_ID = "orderId";
		public static final String PLAN_VALIDITY_CONSUMER = "PLAN_VALIDITY_CONSUMER";
		public static final String RECO_REFRESH_ERROR_EVENT = "RECO_REFRESH_ERROR_EVENT";
		public static final String OMS_CONSUMER = "OMS_CONSUMER";
		public static final String REMINDER_CONSUMER = "REMINDER_CONSUMER";
		public static final String DELETE_EVENT_UPI_CC = "DELETE_EVENT_UPI_CC";
		public static final String FASTAG_API = "FASTAG_API";

		public static final String FASTAG_API_UPDATE_INSURANCE = "FASTAG_API_UPDATE_INSURANCE";
		public static final String RECENT_DATA_DWH_KAFKA_SUCCESS = "RECENT_DATA_DWH_KAFKA_SUCCESS";
		public static final String RECENT_DATA_DWH_KAFKA_FAILURE = "RECENT_DATA_DWH_KAFKA_FAILURE";

		public static final String RECENT_DATA_TO_DWH = "RECENT_DATA_TO_DWH";
		public static final String AUTOMATIC_CONSUMER = "AUTOMATIC_CONSUMER";
		public static final String DELETE_CC_RECENT = "DELETE_CC_RECENT";
		public static final String DELETE_NON_CC_RECENT = "DELETE_NON_CC_RECENT";


		public static final String NICKNAME_SERVICE = "NICKNAME_SERVICE";
		public static final String REMIND_LATER_SERVICE = "REMIND_LATER_SERVICE";
		public static final String RECENT_UPDATE_ERROR = "RECENT_UPDATE_ERROR";
		public static final String RECENT_UPDATE_OMS_REATTEMPT = "RECENT_UPDATE_OMS_REATTEMPT";
		public static final String RECENT_UPDATE_OMS_FAIL = "RECENT_UPDATE_OMS_FAIL";
		public static final String RECENT_UPDATE_REMINDER_REATTEMPT = "RECENT_UPDATE_REMINDER_REATTEMPT";
		public static final String RECENT_UPDATE_REMINDER_FAIL = "RECENT_UPDATE_REMINDER_FAIL";
		public static final String RECENT_UPDATE_NICKNAME_SERVICE_REATTEMPT = "RECENT_UPDATE_NICKNAME_SERVICE_REATTEMPT";
		public static final String RECENT_UPDATE_NICKNAME_SERVICE_FAIL = "RECENT_UPDATE_NICKNAME_SERVICE_FAIL";
		public static final String RECENT_UPDATE_REMINDLATER_SERVICE_FAIL = "RECENT_UPDATE_REMINDLATER_SERVICE_FAIL";
		public static final String RECENT_UPDATE_AUTOMATIC_REATTEMPT = "RECENT_UPDATE_AUTOMATIC_REATTEMPT";
		public static final String RECENT_UPDATE_AUTOMATIC_FAIL = "RECENT_UPDATE_AUTOMATIC_FAIL";
		public static final String INSERT_EVENT_CC_FOR_EXISTING_CARD = "INSERT_EVENT_CC_FOR_EXISTING_CARD";
	}

	public static final class EnvVariableConstants {
		public static final String OMS_CONSUMER_ENABLE = "OMS_CONSUMER_ENABLE";
		public static final String SCRATCH_CARD_CONSUMER_ENABLE = "SCRATCH_CARD_CONSUMER_ENABLE"; // TODO change it.
		public static final String PLAN_VALIDITY_CONSUMER_ENABLE = "PLAN_VALIDITY_CONSUMER_ENABLE"; // TODO change it.
		public static final String VALIDATION_CONSUMER_ENABLE = "VALIDATION_CONSUMER_ENABLE"; // TODO change it.
		public static final String REMINDER_CONSUMER_ENABLE = "REMINDER_CONSUMER_ENABLE"; // TODO change it.
		public static final String AUTOMATIC_CONSUMER_ENABLE = "AUTOMATIC_CONSUMER_ENABLE"; // TODO change it.
		public static final String EVICTCACHE_CONSUMER_ENABLE = "EVICTCACHE_CONSUMER_ENABLE"; //TODO add it.
		public static final String RECON_CONSUMER_ENABLE = "RECON_CONSUMER_ENABLE"; // TODO change it.
		public static final String FS_RECHARGE_CONSUMER_ENABLE = "FS_RECHARGE_CONSUMER_ENABLE";
		public static final String BILLER_ACCOUNT_CONSUMER_ENABLE = "BILLER_ACCOUNT_CONSUMER_ENABLE"; // TODO change it.
		public static final String CDC_REMINDER_CONSUMER_ENABLE = "CDC_REMINDER_CONSUMER_ENABLE";
		public static final String CDC_RECOVERY_CONSUMER_ENABLE = "CDC_RECOVERY_CONSUMER_ENABLE";
		public static final String PREPAID_BILLS_CONSUMER_ENABLE  = "PREPAID_BILLS_CONSUMER_ENABLE";
		public static final String UPI_CREDIT_CARD_KAFKA_ENABLE = "UPI_CREDIT_CARD_KAFKA_ENABLE";

	}

	public static final class EsConstatnts {
		public static final String PRETTY = "pretty";
		public static final String CONTENT_TYPE = "Content-Type";
		public static final String APPLICATION_JSON = "application/json";
		public static final String ORDER_ID = "orderInfo_order_id";
		public static final String ES_CUTOMER_ID = "customerInfo_customer_id";
		public static final String ES_CATEGORY_ID = "productInfo_categoryId";
		public static final String ES_RECHARGE_NUMBER = "userData_recharge_number";
		public static final String ES_RECHARGE_NUMBER_2 = "userData_recharge_number_2";
		public static final String ES_RECHARGE_NUMBER_3 = "userData_recharge_number_3";
		public static final String CATEGORY_ID = "category_id";
		public static final String RECHARGE_NUMBER = "recharge_number";
		public static final String RECHARGE_NUMBER_2 = "recharge_number_2";
		public static final String RECHARGE_NUMBER_3 = "recharge_number_3";
		public static final String OPERATOR = "operator";
		public static final String ES_OPERATOR = "productInfo_operator";
		public static final String ES_SERVICE = "productInfo_service";
		public static final String ES_META_DATA = "metaData";

	}

	public static final class ServiceTypeConstants {
		public static final String ELECTRICITY = "electricity";
		public static final String CYLINDER = "cylinder booking";
		public static final String MOBILE = "mobile";
		public static final String DTH = "dth";
		public static final String LOAN = "loan";
		public static final String[] SERVICE_LIST = {"landline", "broadband", "gas"};
		public static final String[] CHANNEL_HISTORY_SERVICE_LIST = {"landline", "broadband", "mobile", "gas","dth"};
	}

	public static final class ReminderConstants {
		public static final String DUE_DATE = "due_date";
		public static final String BILL_DATE = "bill_date";
		public static final String MIN_DUE_AMOUNT = "min_due_amount";
		public static final String AUTOMATIC_CHANNEL = "SUBS 1";
		public static final List<Integer> NOT_PAID_ON_PAYTM_ALLOWED_STATUS_LIST = Arrays.asList(6, 14);
		public static final String SYNC_BILL_AUTOMATIC_EVENT = "SYNC_BILL_AUTOMATIC";
		public static final String PRODUCT_ID_NULL = "PRODUCT_ID_NULL";
		public static final String CVR_AMOUNT_NULL = "CVR_AMOUNT_NULL";

	}

	public static final class BillType {
		public static final String DUE_BILLS = "due";
		public static final String ALL_BILLS = "all";

	}
	public static final class OMSConstants {
		public static final String DUE_DATE = "due_date";
		public static final String BILL_DATE = "bill_date";
	}

	public static final class FsRechargeConsumerConstants {
		public static final String FS_RECHARGE_CONSUMER_ALLOWED_IN_RESPONSE_CODE = "00";
		public static final String FS_RECHARGE_METRICS_SERVICE_NAME = "FS_RECHARGE_CONSUMER";
		public static final String NEW_EVENT = "NEW_EVENT";
		public static final String CDC_ROLLBACK_EVENT="CDC_ROLLBACK_EVENT";
		public static final String NOT_PROCESSING_WHITELISTED="NOT_PROCESSING_WHITELISTED";
		public static final String IGNORE_EVENT = "IGNORE";
		public static final String RECENT_DATA_UPDATE_REATTEMPT = "RECENT_DATA_UPDATE_REATTEMPT";
		public static final String RECENT_DATA_UPDATE_OMS_FAIL = "RECENT_DATA_UPDATE_OMS_FAIL";
		public static final String ERROR_EVENT = "ERROR";
		public static final String RECENT_DATA_FOR_NOT_ALLOWED_SERVICES = "RECENT_DATA_FOR_NOT_ALLOWED_SERVICES";
		public static final String NON_WHITELISTED_CUSTID="NON_WHITELISTED_CUSTID";

		public static final String STATUS_ONE="STATUS_1";
		public static final String WHITELISTED_CUSTID="WHITELISTED_CUSTID";
		public static final String OUTDATED_EVENT = "OUTDATED";
		public static final String DEFAULT_UPDATED = "DEFAULT_UPDATED";
		public static final String UPDATED="UPDATED";
		public static final String TOTAL_ELIGIBLE_RECORDS_FOR_CDC_RECOVERY_UPDATE = "TOTAL_ELIGIBLE_RECORDS_FOR_CDC_RECOVERY_UPDATE";
		public static final String BILL_PAID_EVENT="BILL_PAID_EVENT";
		public static final String DELETE_EVENT = "DELETE";
		public static final String SAVE_TO_CUSTOMER_BILLS="SAVE_TO_CUSTOMER_BILLS";
		public static final String DATA_EXHAUST_EVENT="DATA_EXHAUST_EVENT";
		public static final String UNSET_BILL="UNSET_BILL";
		public static final String ALREADY_NON_EMPTY_PLAN_BUCKET = "ALREADY_NON_EMPTY_PLAN_BUCKET";
		public static final String EMPTY_PLAN_BUCKET="EMPTY_PLAN_BUCKET";
		public static final String NON_EXISTING_RECORD = "NON_EXISTING_RECORD";
		public static final String NON_EMPTY_PLAN_BUCKET_RECORDS_FOUND="NON_EMPTY_PLAN_BUCKET_RECORDS_FOUND";
		public static final String DELETING_EMPTY_PLAN_BUCKET_RECORD="DELETING_EMPTY_PLAN_BUCKET_RECORD";
		public static final String NEW_RECORD_WITH_NON_EMPTY_PLAN_BUCKET="NEW_RECORD_WITH_NON_EMPTY_PLAN_BUCKET";
		public static final String PARTIAL_BILL = "PARTIAL_BILL";
		public static final String MOBILE_PREPAID_RECORD = "MOBILE_PREPAID_RECORD";
		public static final String VALIDATION_BLOCK_FOR_CDC="VALIDATION_BLOCK_FOR_CDC";

		public static final String LOAN_DUMMY_RN_DELETED="LOAN_DUMMY_RN_DELETED";


	}

	public static final class PlanValidityConsumerConstants {
		private PlanValidityConsumerConstants() {
		}

		public static final String PLAN_DATA_UPDATE_REATTEMPT = "PLAN_DATA_UPDATE_REATTEMPT";
		public static final String PLAN_DATA_UPDATE_FAIL = "PLAN_DATA_UPDATE_FAIL";
		public static final String PLAN_VALIDITY_CONSUMER = "PLAN_VALIDITY_CONSUMER";
	}

	public static final class DropOffConstants {
		public static final String DUE_DATE = "due_date";
		public static final String BILL_DATE = "bill_date";
		public static final String SUCCESS = "SUCCESS";
		public static final String CANCEL = "CANCEL";
		public static final String FAILURE = "FAILURE";
		public static final String PAYMENT_FAILURE = "PAYMENT_FAILURE";
		public static final String REVERSAL_FAILURE = "REVERSAL_FAILURE";
		public static final String PAYMENT_PENDING = "PAYMENT_PENDING";
		public static final String PENDING = "PENDING";

	}


	public static final class MessageConstants {
		public static final String ERROR_PARSING_REQUEST = "Exception in parsing request";
	}

	public static final class PgConstants {
		public static final String PG_RESULT_INFO_SUCCESS = "S";
		public static final String PG_CLAIMS_USERID = "userId";
		public static final String PG_CLAIMS_TOKEN_TYPE = "tokenType";
		public static final String PG_CLAIMS_TOKEN_TYPE_VALUE = "JWT";
		public static final String PG_JWT_ISSUER = "ts";
	}

	public static final class MetricConstants {
		public static final String DB_LATENCY_METRIC = "recharge_saga.db.latency";
		public static final String CONSUMER_EVENTS_SUCCESS_METRIC = "recharge_saga.consumer.events.success";
		public static final String CONSUMER_EVENTS_INVALID_METRIC = "recharge_saga.consumer.events.invalid";
		public static final String CONSUMER_EVENTS_ERROR_METRIC = "recharge_saga.consumer.events.error";
		public static final String SERVICE = "recharge_saga";
		public static final String SERVICE_NAME_KEY = "service_name";
		public static final String STATUS_CODE = "STATUS_CODE";
		public static final String DB_METHOD_KEY = "methodName";
		public static final String API_KEY = "api";
		public static final String SERVICE_KEY = "service";
		public static final String DB_CLASS_KEY = "className";
		public static final String SAGA_METHOD_LATENCY_METRIC = "recharge_saga.method.latency";
		public static final String URI = "URI";
		public static final String SAGA_EXTERNAL_API_SERVICE = "external";
		public static final String STATE = "state";
		public static final String RECON_LISTENER = "recharge_saga.recon";
		public static final String FREQUENT_ORDER = "recharge_saga.frequentOrder";
		public static final String PAYMODE_ORDER = "recharge_saga.paymodeOrder";
		public static final String FETCH_RECENTS = "recharge_saga.fetchRecents";
		public static final String DUPLICATE_FINALIZED_EVENTS_METRIC = "recharge_saga.api.history";
		public static final String DUPLICATE_FINALIZED_EVENTS_SERVICE = "DUPLICATE_EVENTS_IN_FINALIZED";
		public static final String RPS_API = "recharge_saga.rpsApi";
		public static final String MESSAGE_SKIPPED = "MESSAGE_SKIPPED";
		public static final String MESSAGE_SKIPPED_TYPE = "NONRU";
		public static final String CATEGORY_METRIC_KEY = "category";
		public static final String PAYTYPE_METRIC_KEY = "paytype";
		public static final String FASTAG_INSURANCE = "fastag_insurance";
		public static final String OLD_DUE_DATE_NULL_NEW_DUE_DATE_NON_NULL= "old_due_date_null_new_due_date_non_null";
		public static final String SUCCESS = "success";
		public static final String DUPLICATE_PAYLOAD = "duplicate_payload";
		public static final String INVALID_PAYLOAD = "invalid_payload";
		public static final String REMINDER_CDC_CIRCLE_UPDATED = "reminder_cdc_circle_updated";
		public static final String RECENT_CC = "recent_cc";
		public static final String RECENT_CC_TIMEOUT = "recent_cc_timeout";
		public static final String RECENT_API = "recent_api";
		public static final String RECENT_API_RECENT_TIMEOUT = "recent_api_recent_timeout";
		public static final String RECENT_API_SMART_RECENT_TIMEOUT = "recent_api_smart_recent_timeout";
		public static final String RECENT_API_DROP_OFF_RECENT_TIMEOUT = "recent_api_drop_off_recent_timeout";
		public static final String PAGING_STATE_PARSE_SUCCESS = "PAGING_STATE_PARSE_SUCCESS";
		public static final String PAGING_STATE_PARSE_ERROR = "PAGING_STATE_PARSE_ERROR";
		public static final String PAGING_STATE_CAR_DETAIL_SERVICE = "PAGING_STATE_CAR_DETAIL_SERVICE";
		public static final String PAGING_STATE_CAR_DETAIL_SERVICE_DEFAULT = "PAGING_STATE_CAR_DETAIL_SERVICE_DEFAULT";
		public static final String PAGING_STATE_CHANNEL_HISTORY_SERVICE = "PAGING_STATE_CHANNEL_HISTORY_SERVICE";
		public static final String PAGING_STATE_CHANNEL_HISTORY_SERVICE_DEFAULT = "PAGING_STATE_CHANNEL_HISTORY_SERVICE_DEFAULT";
		public static final String PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE = "PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE";
		public static final String PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE_DEFAULT = "PAGING_STATE_CHANNEL_HISTORY_FIN_SERVICE_DEFAULT";
		public static final String PAGING_STATE_NEW_THEME_SERVICE = "PAGING_STATE_NEW_THEME_SERVICE";
		public static final String PAGING_STATE_NEW_THEME_SERVICE_DEFAULT = "PAGING_STATE_NEW_THEME_SERVICE_DEFAULT";

		// Cassandra error messages
		public static final String INVALID_PAGING_STATE_ERROR_MESSAGE = "Invalid value for the paging state";
	}

	public static final class CDC {
		public static final String INSERT_OP = "i";
		public static final String UPDATE_OP = "u";
		public static final String DELETE_OP = "d";

	}

	public static final class RECON {
		public static final Integer TXN_CUTOFF_MONTH = 9;
		public static final Integer TXN_CUTOFF_MONTH_ES = 12;

	}

	public static final class Delimiters {
		public static final String UNDERSCORE = "_";
	}

	public static final class Recents {
		public static final String BILL_VISIBLITY_DAYS = "15";
		public static final String DEFAULT_PLAN = "Recharge Plan";
	}

	public static final String VISA_PREFIX = "4";
	public static final String MASTERCARD_PREFIX = "51,52,53,54,55,";
	public static final String DISCOVER_PREFIX = "6";
	public static final String AMEX_PREFIX = "34,37,";
	public static final String DINERS_PREFIX = "30,36,38,39,";
	public static final String VISA = "VISA";
	public static final String MASTERCARD = "MASTERCARD";
	public static final String AMEX = "AMEX";
	public static final String DISCOVER = "DISCOVER";
	public static final String DINERS = "DINERS";
	public static final String NONE = "NONE";

	public static final String API_VERISON_V1 = "v1";
	public static final String API_VERISON_V2 = "v2";
	public static final String MCN_MASKED_PREFIX = "XXXX XX";

	public static final String AIRTEL_LANDLINE = "airtel landline";
	public static final String REMINDER_NEW_BILL = "reminder_new_bill";
	public static final String PLANVALIDITY_NEW_EXPIRY = "new_planExpiry_date";

	public static final class NUMBER_CONSTANTS {
		public static final String ZERO = "0";
		public static final String PLUS_NINTY_ONE = "+91";
	}

	public static final class SERVICE_CONFIG_CONSTANTS {
		public static final String RECENT_DATA_UPDATE_ALLOWED_SERVICES_FEATURE_NAME = "recentDataUpdateAllowedServices";
		public static final String FETCH_RECENT_LIMIT = "fetchRecentLimit";
		public static final String PREPAID_BILLS_CONSMUER_PIDS_ENABLED= "prepaidBillsConsumerPidsEnabled";
		public static final String NEW_BILL_IDENTIFIED_UPDATE_FOR_ALL_USER = "newBillIdentifiedUpdateForAllUser";
		public static final String NEW_BILL_IDENTIFIED_UPDATE_USER_LIST = "newBIllIdentifiedUpdateUserList";
	}

	public static final String MANAGE_ACTION_TYPE = "automaticManaged";
	public static final String SETUP_ACTION_TYPE = "automaticSetup";
	public static final String AUTOMATIC_ENABLED = "Automatic Payment Active";
	public static final String AUTOMATIC_RENEW = "Automatic Payment Deactivated";
	public static final String AUTOMATIC_DISABLED = "Automatic Payment Inactive";

	public static final class CLIENT {
		public static final String WEB = "web";
		public static final String IOS = "iosapp";
		public static final String ANDROID = "androidapp";
	}

	public static final class CREDIT_CARD {
		public static final String CREDIT_CARD_ID = "creditCardId";
		public static final String PAN_UNIQUE_REFERENCE = "panUniqueReference";
		public static final String TIN = "tin";
	}

	public static final class EVENT_SOURCE {
		public static final String SMS = "sms";
		public static final String CSV = "csv";

		public static final String  PG_DELETED_AMW= "PG_DELETED_AMW";
		public static final String RU_SMS = "ru_sms";
		public static final String SMART_RECENT_VALIDATION = "SMART_RECENT_VALIDATION";
		public static final String SMART_RECENT_DROPOFF = "SMART_RECENT_DROPOFF";
		public static final String UPI_CREDIT_CARD = "upiCreditCard";
	}

	public static final class CardNetworkType {
		public static final String RUPAY = "Rupay";
		public static final String VIABBPS = "viabbps";
	}


	public static final class billerAccountListenerConstants {
		public static final String NEW_EVENT = "NEW_BILLER_ACCOUNT_KAFKA_EVENT";
		public static final String CONSUMER_EVENTS_SUCCESS_METRIC = "recharge_saga.billerAccount.consumer.events.success";
		public static final String CONSUMER_EVENTS_INVALID_METRIC = "recharge_saga.billerAccount.consumer.events.invalid";
		public static final String CONSUMER_EVENTS_ERROR_METRIC = "recharge_saga.billerAccount.consumer.events.error";
		public static final String INVALID_INPUT_ERROR_METRIC = "recharge_saga.billerAccount.input.events.error";
		public static final String DELETE_EVENT_METRIC = "recharge_saga.billerAccount.input.events.delete";
		public static final String BILLER_ACCOUNT = "billerAccount";
		public static final int BILLER_DELETED_ACCOUNT = 2;
		public final static String RECENTS_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
		public final static String RECENT_DELETE_FAIL = "RECENT_DELETE_FAIL";
		public final static String RECENT_DELETE_SUCCESS = "RECENT_DELETE_SUCCESS";
		public static final String BILLER_ACCOUNT_CONSUMER = "BILLER_ACCOUNT_CONSUMER";
		public static final String RECENT_UPDATE_BILLER_FAIL = "RECENT_UPDATE_BILLER_FAIL";
		public static final String NEGATIVE_TTL = "NEGATIVE_TTL";
		public static final String INVALID_CONSENT_VALUE = "INVALID_CONSENT_VALUE";
		public static final String BILLER="biller";

	}

	public static final class PARTIAL_BILL_STATE_CONSTANTS {
		public static final String EXPIRED = BillState.EXPIRED.name();
		public static final String EXPIRING_SOON = BillState.EXPIRING_SOON.name();
		public static final String INCOMING_STOPPED = BillState.INCOMING_STOPPED.name();
		public static final String LOW_BALANCE_PARTIAL_RECORD = "LOW_BALANCE_PARTIAL_RECORD";
		public static final List<String> VALID_PARTIAL_BILL_STATE = Arrays.asList(EXPIRED, EXPIRING_SOON, INCOMING_STOPPED);
	}

	public static final String TAG = "tag";
	public static final String NICKNAME = "nickname";
	public static final String IS_DEFAULT_AMOUNT = "isDefaultAmount";
	public static final String CARD_NETWORK = "card_network";
	public static final String BANK_NAME = "bank_name";
	public static final String EVENTSOURCE_P2P = "p2p";
	public static final String EVENTSOURCE_SMS = "sms";

	public static final String EVENTSOURCE_VALIDATION = "validation";

	public static final String VALIDATION_SYNC = "validationsync";

	public static final String RECHARGE_NUMBER_2 = "recharge_number_2";
	public static final String RECHARGE_NUMBER_3 = "recharge_number_3";
	public static final String RECHARGE_NUMBER_4 = "recharge_number_4";
	public static final String RECHARGE_NUMBER_5 = "recharge_number_5";
	public static final String RECHARGE_NUMBER_6 = "recharge_number_6";
	public static final String CURRENT_MIN_BILL_AMOUNT = "currentMinBillAmount";
	public static final String CDC_REMINDER_CONSUMER = "cdc_reminder_cosumer";
	public static final String CDC_RECOVERY_CONSUMER = "cdc_recovery_consumer";
	public static final String CDC_CONSUMER_STATS = "cdc_consumer_stats";
	public static final String CDC_EVICT_CACHE_ERROR = "CDC_EVICT_CACHE_ERROR";
	public static final String DATA_SOURCE_CA_IDENTIFY = "DATA_SOURCE_CA_IDENTIFY";
	public static final String DATA_SOURCE_CA_IDENTIFY_PARTIAL = "DATA_SOURCE_CA_IDENTIFY_PARTIAL";
	public static final String PREPAID_BILLS_CONSUMER = "prepaid_bills_consumer";
	public static final String NEW_KAFKA_EVENT = "NEW_KAFKA_EVENT";
	public static final String SERVICE_MOBILE = "mobile";
	public static final String RECENT_UPDATE_CDC_REATTEMPT = "RECENT_UPDATE_CDC_REATTEMPT";
	public static final String RECENT_UPDATE_CDC_FAIL = "RECENT_UPDATE_CDC_FAIL";
	public static final String TXN_TIME = "txn_time";
	public static final String TXN_AMOUNT = "txn_amount";
	public static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSX";
	public static final Integer PENDING_TXN_VISIBILITY_DAYS = 30*24*60*60;
	public static final String CHANNEL_ID = "channel_id";
	public static final String CONSUMER_DEPLOYMENT_DATE = "2023-08-03 00:00:00";
	public static final List<Long> CUSTOMER_ID_LIST = Arrays.asList(243003266L, 147360742L, 109199840L,1152424150L,592268042L,1766333594L,13142060L,40458956L,1763650058L,1021030051L,3973717L,4607082L);
	public static final String TUITION_FEES="tuition fees";

	public static final String AUTOMATIC_DUMMY_DUE_DATE = "2024-07-16 00:00:00";

	public static final String SKIN_URL_KEY = "url";
	public static final String SKIN_URL_SOURCE = "source";
	public static final String SKIN_HDPI = "hdpi";
	public static final String SKIN_PROFILES = "profiles";
	public static final String SKIN_ASSETS = "assetType";
	public static final String COMBINED_CARD_ART = "COMBINED_CARD_ART";
	public static final Integer TIME_DIFF_THRESHOLD_BETWEEN_RECENTS_AND_CDC_IN_MINUTE = 60;
	public static final Integer CAR_DETAIL_MAX_PAGE_SIZE = 100;
	public static final String IS_NEW_BILL_IDENTIFIED = "isNewBillIdentified";

	public static final Integer PARTIAL_BILL_STATE_TTL = 3 * 24 * 60 * 60;

	public static final class DATA_EXHAUST{
		public static final String DATA_EXHAUST_PLAN_BUCKET= "data exhaust";
		public static final String EXHAUSTED_DATE = "data_exhaust_date";
		public final static String EXHAUSTED_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
	}
	public static final String AMBIGUOUS_MULTI_PID_DEMERGER = "ambiguous";
	public static final String CONSUMER_NAME="consumerName";
	public static final String SUBSCRIBER_DETAILS="subscriberDetails";
	public static final String DEFAULT_CONSUMER_SOURCE = "DEFAULT";
	public static final String OPERATOR_AIRTEL = "airtel";
	public static final String UPI_CREDIT_CARD_CONSUMER = "upiCreditCard";
	public static final String SKIPPED_EVENT = "skippedEvent";
	public static final String UPI_CC_BULK_CONSUMER = "upiBulkCc";
	public static final String UPI_CREDIT_CARD_BULK_KAFKA_ENABLE = "upiCreditCardBulkKafkaEnable";
	public static final String UPI_CREDIT_CARD_ROLLOUT_PERCENTAGE = "upiCreditCardRolloutPercentage";
	public static final String WHITE_LISTED_CUST_IDS_UPI =  "upiCardWhiteListedCustIds";
	public static final String ALL = "ALL";
	public static final String BANK_MAPPING_NOT_FOUND = "BANK_MAPPING_NOT_FOUND";
	public static final String CUSTOMER_NOT_ENABLED = "CUSTOMER_NOT_ENABLED";
	public static final String PRODUCT_MAPPING_NOT_FOUND = "PRODUCT_MAPPING_NOT_FOUND";
	public static final String ERROR_EVENT = "ERROR";
	public static final String SUCCESS_EVENT = "SUCCESS";
	public static final String DUPLICATE_EVENT = "DUPLICATE";
    public static final String FFR_SKIP = "ffrSkip";
	public static final Integer REMINDER_BILL_PAID_STATUS = 14;
	public static final List<Integer> REMINDER_BILL_GEN_STATUS = Arrays.asList(4,5);

	public static final String  RU_PARTIAL_BILL_RECO_SERVICES = "ru_partial_bill_reco_services";
	public static final String  RU_PARTIAL_BILL_CUSTOMER_BILL_DUE_DATE_OFFSET = "ru_partial_bill_customer_bill_due_date_offset";
	public static final Integer  RU_PARTIAL_BILL_CUSTOMER_BILL_DUE_DATE_OFFSET_DEFAULT = 2;
	public static final String RU_PARTIAL_BILL_UPDATED_SOURCE_LIST = "ru_partial_bill_updated_source_list";
	public static final int MAX_PARTITION_FETCH_BYTES_8MB = 8 * 1024 * 1024;

	public static final String MIN_RECENT_DUE_DATE_THRESHOLD = "2021-01-01";
	public static final String MAX_RECENT_DUE_DATE_THRESHOLD = "2050-01-01";


}
