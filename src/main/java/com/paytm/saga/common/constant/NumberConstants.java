package com.paytm.saga.common.constant;

public class NumberConstants {
    public static final Integer AGENT_LIMIT = 300;
    public static final Integer RECHARGE_NUMBER_TO_CUSTOMER_ID_LIMIT = 100;
    public static final Integer BILL_VISIBLITY_DAY = 3;
    public static final Integer BILL_END_RANGE_CURRENT_DATE = 31;
    public static final Integer PREPAID_ELECTRICITY_BILL_VISIBILITY_OFFSET = 5;
    public static final Integer HEURISTIC_PREPAID_ELECTRICITY_BILL_VISIBILITY_OFFSET = 3;
    public static final Long RECENT_DB_READ_TIMEOUT = 250L;
    public static final Long FREQUENT_ORDER_EXECUTOR_TIMEOUT = 500L;
    public static final Long PREPAID_END_DAYS = 3L;
    public static final Long PREPAID_START_DAYS = 5L;
    public static final Long POSTPAID_END_DAYS = 3L;

    public static final Integer SUBSCRIPTION_RENEW_STATE = 5;
    public static final Integer PREPAID_ELECTRICITY_LOW_BALANCE_THRESHOLD = 2000;
    public static final String PREPAID_VALUE = "1";


    private NumberConstants() {

    }
}
