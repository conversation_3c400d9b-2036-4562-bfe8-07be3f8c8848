package com.paytm.saga.common.configuration;

import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import com.paytm.saga.common.configuration.property.EvictCacheKafkaConfig;

@Configuration
public class KafkaProducerConfig {
	@Autowired
    private EvictCacheKafkaConfig evictCacheKafkaConfig;
    @Bean
    public Map<String, Object> evictCachePropertiesConfig() {
        Map<String, Object> Props = new HashMap<>();
        Props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, evictCacheKafkaConfig.getBootstrapServers());
        Props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        Props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        Props.put(ProducerConfig.CLIENT_ID_CONFIG, evictCacheKafkaConfig.getClientId());
        return Props;
    }
    @Bean
    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(evictCachePropertiesConfig());
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
}
