package com.paytm.saga.common.configuration.RestTemplate;

//import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;

/**
 * <AUTHOR> on 15/07/2021
 */

//public class HttpDefaultKeepAliveStrategy extends DefaultConnectionKeepAliveStrategy {
//    private long keepAliveTime;
//
//    public HttpDefaultKeepAliveStrategy() {
//        keepAliveTime = 60 * 1000; //60 secs
//    }
//
//    public HttpDefaultKeepAliveStrategy(long keepAliveTime) {
//        this.keepAliveTime = keepAliveTime;
//    }
//
//    @Override
//    public long getKeepAliveDuration(org.apache.http.HttpResponse response, org.apache.http.protocol.HttpContext context) {
//        long serverKeepAliveTime = super.getKeepAliveDuration(response, context);
//        if (keepAliveTime != -1) {
//            return keepAliveTime;
//        } else {
//            return serverKeepAliveTime;
//        }
//    }
//
//    public long getKeepAliveTime() {
//        return keepAliveTime;
//    }
//}

