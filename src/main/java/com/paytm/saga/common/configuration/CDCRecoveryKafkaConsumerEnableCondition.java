package com.paytm.saga.common.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.util.RecentUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Map;
import java.util.Objects;

public class CDCRecoveryKafkaConsumerEnableCondition implements Condition {
    private static final Logger logger = LogManager.getLogger(CDCRecoveryKafkaConsumerEnableCondition.class);
    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        boolean matches=false;
        if ((Objects.nonNull(System.getenv(Constants.EnvVariableConstants.CDC_RECOVERY_CONSUMER_ENABLE)) &&
                (System.getenv(Constants.EnvVariableConstants.CDC_RECOVERY_CONSUMER_ENABLE).equalsIgnoreCase("true")))) {
            matches = true;
        }
        return matches;
    }
}
