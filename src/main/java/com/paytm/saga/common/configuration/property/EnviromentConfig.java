package com.paytm.saga.common.configuration.property;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;

@EnableRetry
@ConfigurationProperties("application")
@Configuration("com.paytm.saga.common.configuration.property.EnvironmentConfig")
public class EnviromentConfig {
    private String environment;

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }
}
