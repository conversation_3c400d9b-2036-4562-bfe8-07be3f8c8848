package com.paytm.saga.common.configuration.property;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration("DCATPropertiesConfig")
@ConfigurationProperties("dcat")
public class DCATConfig {
    private String browsePlanUrl;
    private String categoryUrl;

    public String getBrowsePlanUrl() {
        return browsePlanUrl;
    }

    public void setBrowsePlanUrl(String browsePlanUrl) {
        this.browsePlanUrl = browsePlanUrl;
    }

    public String getCategoryUrl() {
        return categoryUrl;
    }
    
    public void setCategoryUrl(String categoryUrl) {
        this.categoryUrl = categoryUrl;
    }
}