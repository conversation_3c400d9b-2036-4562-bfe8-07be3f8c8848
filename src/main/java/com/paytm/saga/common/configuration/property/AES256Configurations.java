package com.paytm.saga.common.configuration.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties("aes256")
@Configuration
@Setter
@Getter
public class AES256Configurations {
	private String encryptionPassword;
	private String encryptionIvHex;
}
