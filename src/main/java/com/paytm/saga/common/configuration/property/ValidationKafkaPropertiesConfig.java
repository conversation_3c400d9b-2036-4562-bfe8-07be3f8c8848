package com.paytm.saga.common.configuration.property;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Configuration("ValidationKafkaPropertiesConfig")
@ConfigurationProperties("validation.kafka")
public class ValidationKafkaPropertiesConfig {
    private String bootstrapServers;
    private String topicName_DEFAULT_VALIDATION;
    private String topicName_RECHARGE_VALIDATION;
    private String topicName_UTILITY_ELECTRICITY_VALIDATION;
    private String topicName_BFSI_INSURANCE_VALIDATION;
    private String consumerGroup;

    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }

    public String getConsumerGroup() {
        return consumerGroup;
    }

    public void setConsumerGroup(String consumerGroup) {
        this.consumerGroup = consumerGroup;
    }

    public String getTopicName_DEFAULT_VALIDATION() {
        return topicName_DEFAULT_VALIDATION;
    }

    public void setTopicName_DEFAULT_VALIDATION(String topicName_DEFAULT_VALIDATION) {
        this.topicName_DEFAULT_VALIDATION = topicName_DEFAULT_VALIDATION;
    }

    public String getTopicName_RECHARGE_VALIDATION() {
        return topicName_RECHARGE_VALIDATION;
    }

    public void setTopicName_RECHARGE_VALIDATION(String topicName_RECHARGE_VALIDATION) {
        this.topicName_RECHARGE_VALIDATION = topicName_RECHARGE_VALIDATION;
    }

    public String getTopicName_UTILITY_ELECTRICITY_VALIDATION() {
        return topicName_UTILITY_ELECTRICITY_VALIDATION;
    }

    public void setTopicName_UTILITY_ELECTRICITY_VALIDATION(String topicName_UTILITY_ELECTRICITY_VALIDATION) {
        this.topicName_UTILITY_ELECTRICITY_VALIDATION = topicName_UTILITY_ELECTRICITY_VALIDATION;
    }

    public String getTopicName_BFSI_INSURANCE_VALIDATION() {
		return topicName_BFSI_INSURANCE_VALIDATION;
	}

	public void setTopicName_BFSI_INSURANCE_VALIDATION(String topicName_BFSI_INSURANCE_VALIDATION) {
		this.topicName_BFSI_INSURANCE_VALIDATION = topicName_BFSI_INSURANCE_VALIDATION;
	}

	@Override
    public String toString() {
        return "ValidationKafkaPropertiesConfig{" +
                "bootstrapServers='" + bootstrapServers + '\'' +
                ", topicName_DEFAULT_VALIDATION='" + topicName_DEFAULT_VALIDATION + '\'' +
                ", topicName_RECHARGE_VALIDATION='" + topicName_RECHARGE_VALIDATION + '\'' +
                ", topicName_UTILITY_ELECTRICITY_VALIDATION='" + topicName_UTILITY_ELECTRICITY_VALIDATION + '\'' +
                ", consumerGroup='" + consumerGroup + '\'' +
                '}';
    }
}
