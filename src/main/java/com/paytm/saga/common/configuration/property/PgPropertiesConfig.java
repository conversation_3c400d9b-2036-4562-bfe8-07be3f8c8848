package com.paytm.saga.common.configuration.property;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration("PgPropertiesConfig")
@ConfigurationProperties("pg")
public class PgPropertiesConfig {
    private String secretKey;

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    @Override
    public String toString() {
        return "PgPropertiesConfig{" +
                "secretKey='" + secretKey + '\'' +
                '}';
    }
}
