package com.paytm.saga.common.configuration;

import com.paytm.saga.common.constant.Constants;
import com.timgroup.statsd.NonBlockingStatsDClient;
import com.timgroup.statsd.StatsDClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> on 11/02/2021
 */

@Configuration
public class MonitoringConfigurations {
    private final Logger logger = LogManager.getLogger(StatsDClient.class);

    @Bean("monitoringClient")
    public StatsDClient createMonitoringClient() {
        StatsDClient monitoringClient = new NonBlockingStatsDClient("paytm", "localhost", 8130, new String[]{"application:paytm-datadog"});
        logger.info("Datadog StatsDClient initialized for hostname : " + "localhost" + " & Port : " + 8130);
        return monitoringClient;
    }
}
