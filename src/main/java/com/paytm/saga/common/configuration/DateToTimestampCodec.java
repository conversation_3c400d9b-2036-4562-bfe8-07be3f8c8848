package com.paytm.saga.common.configuration;

import com.datastax.oss.driver.api.core.type.codec.MappingCodec;
import com.datastax.oss.driver.api.core.type.codec.TypeCodecs;
import com.datastax.oss.driver.api.core.type.reflect.GenericType;

import java.time.Instant;
import java.util.Date;

public class DateToTimestampCodec extends MappingCodec<Instant, Date> {

    public DateToTimestampCodec() {
        super(TypeCodecs.TIMESTAMP, GenericType.of(Date.class));
    }

    @Override
    protected Date innerToOuter(java.time.Instant value) {
        return (value == null) ? null : Date.from(value);
    }

    @Override
    protected java.time.Instant outerToInner(Date value) {
        return (value == null) ? null : value.toInstant();
    }
}

