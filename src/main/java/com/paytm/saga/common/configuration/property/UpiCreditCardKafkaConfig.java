package com.paytm.saga.common.configuration.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration("upiCreditCardKafkaConfig")
@ConfigurationProperties("upi.cc.kafka")
public class UpiCreditCardKafkaConfig {
    private String bootstrapServers;
    private String topicName;
    private String consumerGroup;
    private int maxPollInterval;
    private int sessionTimeout;
    private boolean sshEnabled;
}
