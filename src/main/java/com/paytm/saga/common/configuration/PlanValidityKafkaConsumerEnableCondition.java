package com.paytm.saga.common.configuration;

import com.paytm.saga.common.constant.Constants;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Objects;

public class PlanValidityKafkaConsumerEnableCondition implements Condition {
    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        if (Objects.nonNull(System.getenv(Constants.EnvVariableConstants.PLAN_VALIDITY_CONSUMER_ENABLE)) &&
                (System.getenv(Constants.EnvVariableConstants.PLAN_VALIDITY_CONSUMER_ENABLE).equalsIgnoreCase("true"))) {
            return true;
        }
        else {
            return false;
        }
    }
}
