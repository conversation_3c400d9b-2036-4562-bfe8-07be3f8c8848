package com.paytm.saga.common.configuration;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Objects;

import static com.paytm.saga.common.constant.Constants.BULK_CONSENT_VALIDITY_CONSUMER_ENABLE;

public class BulkConsentValidityConsumerEnableCondition implements Condition {
    @Override
    public boolean matches(ConditionContext conditionContext,AnnotatedTypeMetadata annotatedTypeMetadata) {
//        return Objects.nonNull(System.getenv(BULK_CONSENT_VALIDITY_CONSUMER_ENABLE)) &&
//                (System.getenv(BULK_CONSENT_VALIDITY_CONSUMER_ENABLE).equalsIgnoreCase("true"));
        return true;
    }
}