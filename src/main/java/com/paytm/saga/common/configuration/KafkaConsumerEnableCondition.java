package com.paytm.saga.common.configuration;

import com.paytm.saga.common.constant.Constants;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Objects;

public class KafkaConsumerEnableCondition implements Condition {
    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        if (Objects.nonNull(getEnvironmentVariable(Constants.EnvVariableConstants.OMS_CONSUMER_ENABLE)) &&
                (getEnvironmentVariable(Constants.EnvVariableConstants.OMS_CONSUMER_ENABLE).equalsIgnoreCase("true"))) {
            return true;
        }
        else {
            return false;
        }
    }

    protected String getEnvironmentVariable(String name) {
        return System.getenv(name);
    }
}
