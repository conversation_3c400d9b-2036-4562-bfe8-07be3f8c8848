package com.paytm.saga.common.configuration;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FeatureConfigCache;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Objects;

public class UpiCreditCardBulkKafkaEnableCondition implements Condition {
    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        return Objects.nonNull(System.getenv(Constants.EnvVariableConstants.UPI_CREDIT_CARD_KAFKA_ENABLE))
                && (System.getenv(Constants.EnvVariableConstants.UPI_CREDIT_CARD_KAFKA_ENABLE).equalsIgnoreCase(Constants.TRUE_STRING))
                && Boolean.TRUE.equals(FeatureConfigCache.getInstance().getBoolean(Constants.UPI_CREDIT_CARD_BULK_KAFKA_ENABLE));
    }
}
