package com.paytm.saga.common.configuration.property;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;

@Configuration
@EnableRetry
@ConfigurationProperties("external.endpoints")
@Data
@ToString
public class ExternalEndpointsPropertiesConfig {
    private String reminderMarkAsPaid;
    private String recentsCRUDUpdate;
    private String browsePlanUrl;
    private String pgSavedCards;
    private String smsCards;
    private String itemV2Url;
    private String plansUrl;
    private String elasticSearch;
    private String billsSyncMarkAsPaid;

}
