package com.paytm.saga.common.configuration;

import com.paytm.saga.common.constant.Constants;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Objects;

public class FsRechargeKafkaConsumerEnableCondition implements Condition {
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
       return (Objects.nonNull(System.getenv(Constants.EnvVariableConstants.FS_RECHARGE_CONSUMER_ENABLE)) &&
                System.getenv(Constants.EnvVariableConstants.FS_RECHARGE_CONSUMER_ENABLE).equalsIgnoreCase("true"));

    }
}
