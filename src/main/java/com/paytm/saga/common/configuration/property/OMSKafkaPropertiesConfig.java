package com.paytm.saga.common.configuration.property;


import com.paytm.saga.common.constant.Constants;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;


@Configuration("OMSKafkaPropertiesConfig")
@ConfigurationProperties("oms.kafka")
public class OMSKafkaPropertiesConfig {
    private static final Logger log = LoggerFactory.getLogger(OMSKafkaPropertiesConfig.class);
    private String bootstrapServers;
    private String topicName;
    private String consumerGroup;

    @Bean("OMSListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> containerFactory() throws IOException, InterruptedException {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.setBatchListener(true);
        factory.setConcurrency(3);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }

    public Map<String, Object> consumerConfigs() throws IOException, InterruptedException {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, getBootstrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, getConsumerGroup());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576 * 8);
       // props.put(ConsumerConfig.CLIENT_RACK_CONFIG, getAvailabilityZone());
        return props;
    }
    private String getAvailabilityZone() throws IOException {
        try {
            URL tokenUrl = new URL("http://169.254.169.254/latest/api/token");
            HttpURLConnection tokenConnection = (HttpURLConnection) tokenUrl.openConnection();
            tokenConnection.setRequestMethod("PUT");
            tokenConnection.setRequestProperty("X-aws-ec2-metadata-token-ttl-seconds", "21600");

            // Check token API response
            int tokenResponseCode = tokenConnection.getResponseCode();
            if (tokenResponseCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("Failed to get token. Response code: " + tokenResponseCode);
            }

            BufferedReader metadataReader = getBufferedReader(tokenConnection);
            String availabilityZone = metadataReader.readLine();
            metadataReader.close();

            if (availabilityZone == null || availabilityZone.trim().isEmpty()) {
                throw new IOException("Retrieved availability zone is empty or null");
            }

            log.info("Availability Zone: {}", availabilityZone);
            return availabilityZone;

        } catch (Exception e) {
            log.error("Error retrieving availability zone", e);
            throw new IOException("Failed to retrieve availability zone", e);
        }
    }

    private static BufferedReader getBufferedReader(HttpURLConnection tokenConnection) throws IOException {
        BufferedReader tokenReader = new BufferedReader(new InputStreamReader(tokenConnection.getInputStream()));
        String token = tokenReader.readLine();
        tokenReader.close();

        // Use the token to retrieve metadata
        URL metadataUrl = new URL("http://169.254.169.254/latest/meta-data/placement/availability-zone");
        HttpURLConnection metadataConnection = (HttpURLConnection) metadataUrl.openConnection();
        metadataConnection.setRequestMethod("GET");
        metadataConnection.setRequestProperty("X-aws-ec2-metadata-token", token);

        BufferedReader metadataReader = new BufferedReader(new InputStreamReader(metadataConnection.getInputStream()));
        return metadataReader;
    }

    public ConsumerFactory<String, String> consumerFactory() throws IOException, InterruptedException {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs());
    }

    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public String getConsumerGroup() {
        return consumerGroup;
    }

    public void setConsumerGroup(String consumerGroup) {
        this.consumerGroup = consumerGroup;
    }

    @Override
    public String toString() {
        return "KafkaPropertiesConfig{" +
                "bootstrapServers='" + bootstrapServers + '\'' +
                ", topicName='" + topicName + '\'' +
                ", consumerGroup='" + consumerGroup + '\'' +
                '}';
    }
}
