package com.paytm.saga.common.configuration.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration("PrepaidBillsKafkaPropertiesConfig")
@ConfigurationProperties("prepaid.bills.kafka")
public class PrepaidBillsKafkaPropertiesConfig {
    private String bootstrapServers;
    private String topicName;
    private String consumerGroup;

}
