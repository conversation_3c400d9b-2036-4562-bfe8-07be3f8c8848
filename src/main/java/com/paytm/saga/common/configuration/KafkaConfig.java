package com.paytm.saga.common.configuration;


import com.paytm.saga.common.configuration.property.*;
import com.paytm.saga.common.constant.Constants;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.security.auth.SecurityProtocol;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.lang.NonNull;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConfig {
    private final OMSKafkaPropertiesConfig oMSKafkaPropertiesConfig;
    private final ScratchCardKafkaPropertiesConfig scratchCardKafkaPropertiesConfig;
    private final PlanValidityKafkaPropertiesConfig planValidityKafkaPropertiesConfig;
    private final ValidationKafkaPropertiesConfig validationKafkaPropertiesConfig;
    private final ReminderKafkaPropertiesConfig reminderKafkaPropertiesConfig;
    private final UppclMigrationKafkaPropertiesConfig uppclMigrationKafkaPropertiesConfig;
    private final PrepaidBillsKafkaPropertiesConfig prepaidBillsKafkaPropertiesConfig;
    private final UpiCreditCardKafkaConfig upiCreditCardKafkaConfig;
    private final BulkConsentValidityUpdateKafkaConfig bulkConsentValidityUpdate;
    private final UpiCreditCardBulkKafkaConfig upiCreditCardBulkKafkaConfig;

    @Autowired
    public KafkaConfig(@NonNull OMSKafkaPropertiesConfig oMSKafkaPropertiesConfig,
                       @NonNull ScratchCardKafkaPropertiesConfig scratchCardKafkaPropertiesConfig,
                       @NonNull PlanValidityKafkaPropertiesConfig planValidityKafkaPropertiesConfig,
                       @NonNull ValidationKafkaPropertiesConfig validationKafkaPropertiesConfig,
                       @NonNull ReminderKafkaPropertiesConfig reminderKafkaPropertiesConfig,
                       @NonNull UppclMigrationKafkaPropertiesConfig uppclMigrationKafkaPropertiesConfig,
                       @NonNull PrepaidBillsKafkaPropertiesConfig prepaidBillsKafkaPropertiesConfig,
                       @NonNull UpiCreditCardKafkaConfig upiCreditCardKafkaConfig,
                       @NonNull UpiCreditCardBulkKafkaConfig upiCreditCardBulkKafkaConfig,
                       @NonNull BulkConsentValidityUpdateKafkaConfig bulkConsentValidityUpdate) {
        this.oMSKafkaPropertiesConfig = oMSKafkaPropertiesConfig;
        this.scratchCardKafkaPropertiesConfig = scratchCardKafkaPropertiesConfig;
        this.planValidityKafkaPropertiesConfig = planValidityKafkaPropertiesConfig;
        this.validationKafkaPropertiesConfig = validationKafkaPropertiesConfig;
        this.reminderKafkaPropertiesConfig = reminderKafkaPropertiesConfig;
        this.uppclMigrationKafkaPropertiesConfig = uppclMigrationKafkaPropertiesConfig;
        this.prepaidBillsKafkaPropertiesConfig = prepaidBillsKafkaPropertiesConfig;
        this.upiCreditCardKafkaConfig = upiCreditCardKafkaConfig;
        this.bulkConsentValidityUpdate = bulkConsentValidityUpdate;
        this.upiCreditCardBulkKafkaConfig = upiCreditCardBulkKafkaConfig;
    }

    @Bean
    public Map<String, Object> OMSConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, oMSKafkaPropertiesConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, oMSKafkaPropertiesConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576*8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }

    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(OMSConsumerConfigs());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }

    @Bean
    public Map<String, Object> ScratchCardConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, scratchCardKafkaPropertiesConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, scratchCardKafkaPropertiesConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576*8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }

    @Bean
    public ConsumerFactory<String, String> ScratchCardConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(ScratchCardConsumerConfigs());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> ScratchCardkafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(ScratchCardConsumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }


    @Bean
    public Map<String, Object> PlanValidityConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, planValidityKafkaPropertiesConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, planValidityKafkaPropertiesConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576*8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }

    @Bean
    public ConsumerFactory<String, String> PlanValidityConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(PlanValidityConsumerConfigs());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> PlanValiditykafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(PlanValidityConsumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(2);
        return factory;
    }

    @Bean
    public Map<String, Object> ValidationConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, validationKafkaPropertiesConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, validationKafkaPropertiesConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576*8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }

    @Bean
    public ConsumerFactory<String, String> ValidationConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(ValidationConsumerConfigs());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> ValidationkafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(ValidationConsumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(4);
        return factory;
    }

    @Bean
    public Map<String, Object> ReminderConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, reminderKafkaPropertiesConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, reminderKafkaPropertiesConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576*8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }

    @Bean
    public ConsumerFactory<String, String> ReminderConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(ReminderConsumerConfigs());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> ReminderkafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(ReminderConsumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }
    @Bean
    public Map<String, Object> UppclMigrationConsumerConfig() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, uppclMigrationKafkaPropertiesConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, uppclMigrationKafkaPropertiesConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576*8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }

    @Bean
    public ConsumerFactory<String, String> UppclMigrationConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(PlanValidityConsumerConfigs());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> UppclMigrationkafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(UppclMigrationConsumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }

    @Bean
    public Map<String, Object> PrepaidBillsConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, prepaidBillsKafkaPropertiesConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, prepaidBillsKafkaPropertiesConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576*8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }

    @Bean
    public ConsumerFactory<String, String> PrepaidBillsConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(PrepaidBillsConsumerConfigs());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> PrepaidBillsKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(PrepaidBillsConsumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(2);
        return factory;
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> upiCreditCardKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(upiCreditCardConsumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }

    @Bean
    public ConsumerFactory<String, String> upiCreditCardConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(upiCreditCardConsumerConfigs());
    }

    private Map<String, Object> upiCreditCardConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, upiCreditCardKafkaConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, upiCreditCardKafkaConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576*8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, upiCreditCardKafkaConfig.getMaxPollInterval());
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, upiCreditCardKafkaConfig.getSessionTimeout());
        // use SSH only for PMS TPAP kafka connection and not for other kafka connections like 41.200 (RU internal kafka)
        // why use ssh at all? this was requested by PMS team which is the kafka producer, for extra security and a precursor to using kafka ACLs
        if (upiCreditCardKafkaConfig.isSshEnabled()) {
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, SecurityProtocol.SSL.name);
        }
        return props;
    }

    private Map<String, Object> bulkConsentValidityUpdateProperties() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bulkConsentValidityUpdate.getBootstrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, bulkConsentValidityUpdate.getConsumerGroup());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576 * 8);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }


    private ConsumerFactory<String, String> bulkConsentValidityUpdateFactory() {
        return new DefaultKafkaConsumerFactory<>(bulkConsentValidityUpdateProperties());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> bulkConsentValidityUpdateContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(bulkConsentValidityUpdateFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }
    private Map<String, Object> upiCreditCardBulkConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, upiCreditCardBulkKafkaConfig.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, upiCreditCardBulkKafkaConfig.getConsumerGroup());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Constants.CommonConstants.KAFKA_POOL_SIZE);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, Constants.MAX_PARTITION_FETCH_BYTES_8MB);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return props;
    }

    public ConsumerFactory<String, String> upiCreditCardBulkConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(upiCreditCardBulkConsumerConfigs());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> upiCreditCardBulkKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(upiCreditCardBulkConsumerFactory());
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }
}
