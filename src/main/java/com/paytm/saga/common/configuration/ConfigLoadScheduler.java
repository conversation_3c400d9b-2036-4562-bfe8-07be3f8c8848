package com.paytm.saga.common.configuration;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.paytm.saga.dto.DropOffConfigByService;
import com.paytm.saga.model.ApplicationConfig;
import com.paytm.saga.repository.ApplicationConfigRepository;

@Configuration
@EnableScheduling
public class ConfigLoadScheduler {
	private final Logger logger = LogManager.getLogger(ConfigLoadScheduler.class);

	@Autowired
	private ApplicationConfigRepository applicationConfigRepository;
	@Autowired
	private DropOffConfigByService dropOffConfigByService;

	@Scheduled(fixedDelay = 43200 * 1000)
	public void loadConfig() {
		List<ApplicationConfig> applicationConfigList = applicationConfigRepository.findByService("dropoff");
		if (applicationConfigList != null && !applicationConfigList.isEmpty()) {
			for (ApplicationConfig applicationConfig : applicationConfigList) {
				String category = applicationConfig.getCategory();
				dropOffConfigByService.updateConfigConfigMap(applicationConfig.getService(), category,
						applicationConfig.getConfig());
			}
		}
		logger.info("ConfigLoadScheduler :: loadConfig config loaded successfully");
	}
}
