package com.paytm.saga.common.configuration.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration("upiCreditCardBulkKafkaConfig")
@ConfigurationProperties("upi.cc.bulk.kafka")
public class UpiCreditCardBulkKafkaConfig {
    private String bootstrapServers;
    private String topicName;
    private String consumerGroup;
}