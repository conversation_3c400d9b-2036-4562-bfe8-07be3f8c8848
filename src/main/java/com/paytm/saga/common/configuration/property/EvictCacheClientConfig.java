package com.paytm.saga.common.configuration.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties("http.evictcache")
@Data
public class EvictCacheClientConfig {

    private String url;
    private String upsertUrl;
    private Long retryLimit;
    private Long retryInterval;

}
