package com.paytm.saga.common.configuration.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties("cassandra")
@Getter
@Setter
public class CassandraProperties {
    @Qualifier("password")
    private String password;
}
