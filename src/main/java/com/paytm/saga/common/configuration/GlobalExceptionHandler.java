package com.paytm.saga.common.configuration;

import com.fasterxml.jackson.core.json.UTF8StreamJsonParser;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.EncryptionConstants;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.exception.ErrorMessages;
import com.paytm.saga.common.exception.MarkAsPaidServiceWithDisplayMessageException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.MarkAsPaidResponse;
import com.paytm.saga.model.RechargeSagaGenericErrorResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.Objects;
import java.util.Optional;

/**
 * <NAME_EMAIL> on 2021/03/02
 */

@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LogManager.getLogger(GlobalExceptionHandler.class);
    private ObjectError objectError;
    @Autowired
    private MetricsHelper metricsHelper;

    @ExceptionHandler(RechargeSagaBaseException.class)
    public ResponseEntity<RechargeSagaGenericErrorResponse> handleException(HttpServletRequest request, RechargeSagaBaseException ex) {
        logger.info("[GlobalExceptionHandler.GlobalExceptionHandler] {}", ex);
        ErrorMessages errorMessages = ex.getErrorMessages();
        RechargeSagaGenericErrorResponse res = null;
        if(Objects.nonNull(errorMessages)) {
            res  = new
                    RechargeSagaGenericErrorResponse(errorMessages);

            return new ResponseEntity<>(res, errorMessages.getStatusCode());
        } else {
            res = new
                    RechargeSagaGenericErrorResponse(ErrorMessages.SAGA_GENERIC_API_FAILURE);
            return new ResponseEntity<>(res, ErrorMessages.SAGA_GENERIC_API_FAILURE.getStatusCode());
        }
    }

    @ExceptionHandler(MarkAsPaidServiceWithDisplayMessageException.class)
    public ResponseEntity<MarkAsPaidResponse> handleMarkAsPaidWithDisplayMessageException(HttpServletRequest request, MarkAsPaidServiceWithDisplayMessageException ex) {
        logger.info("[GlobalExceptionHandler.handleMarkAsPaidWithDisplayMessageException] {}", ex);
        ErrorMessages errorMessages = ex.getErrorMessages();
        MarkAsPaidResponse res = null;
        if(Objects.nonNull(errorMessages)) {
            res  = new MarkAsPaidResponse(errorMessages.getStatusCode().value(),
                    errorMessages.getErrorMessage(),
                    errorMessages.getErrorMessage(),
                    errorMessages.getErrorCode(),
                    errorMessages.getStatusCode());
            return new ResponseEntity<>(res, errorMessages.getStatusCode());
        } else {
            res  = new MarkAsPaidResponse(ErrorMessages.SAGA_GENERIC_API_FAILURE.getStatusCode().value(),
                    ErrorMessages.SAGA_GENERIC_API_FAILURE.getErrorMessage(),
                    ErrorMessages.SAGA_GENERIC_API_FAILURE.getErrorMessage(),
                    ErrorMessages.SAGA_GENERIC_API_FAILURE.getErrorCode(),
                    ErrorMessages.SAGA_GENERIC_API_FAILURE.getStatusCode());
            return new ResponseEntity<>(res, ErrorMessages.SAGA_GENERIC_API_FAILURE.getStatusCode());
        }
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<RechargeSagaGenericErrorResponse> handlevalidationException
            (HttpServletRequest request, HttpServletResponse response, MethodArgumentNotValidException ex) {
        logger.info("[GlobalExceptionHandler.handlevalidationException] {}", ex);
        BindingResult bindingResult = ex.getBindingResult();

        RechargeSagaGenericErrorResponse res = new
                RechargeSagaGenericErrorResponse(bindingResult.getFieldError().getDefaultMessage(),
                ErrorMessages.SAGA_API_VALIDATION_FAILURE.getErrorCode(),
                HttpStatus.BAD_REQUEST);
        return new ResponseEntity<>(res, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({ConstraintViolationException.class})
    public ResponseEntity<RechargeSagaGenericErrorResponse> handleParamsValidationException
            (HttpServletRequest request, HttpServletResponse response, ConstraintViolationException ex) {
        logger.info("[GlobalExceptionHandler.handleParamsValidationException] {}", ex);
        String message = "One of the Parameter does not meet with the validation requirement";
        Optional<ConstraintViolation<?>> constraintViolation = ex.getConstraintViolations().stream().findFirst();
        if (constraintViolation.isPresent()) {
            message = constraintViolation.get().getMessage();
        }
        RechargeSagaGenericErrorResponse res = new
                RechargeSagaGenericErrorResponse(message,
                ErrorMessages.SAGA_API_VALIDATION_FAILURE.getErrorCode(),
                HttpStatus.BAD_REQUEST);
        return new ResponseEntity(res, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({MissingServletRequestParameterException.class,})
    public ResponseEntity<RechargeSagaGenericErrorResponse> handleMissingParams(HttpServletRequest request, MissingServletRequestParameterException ex) {
        logger.info("[GlobalExceptionHandler.handleMissingParams] {}", ex);
        String message = ex.getParameterName() + " is not present in params";
        RechargeSagaGenericErrorResponse res = new
                RechargeSagaGenericErrorResponse(message,
                ErrorMessages.SAGA_API_VALIDATION_FAILURE.getErrorCode(),
                HttpStatus.BAD_REQUEST);
        return new ResponseEntity(res, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({AES256Exception.class})
    public ResponseEntity<RechargeSagaGenericErrorResponse> handleAES256Exception(HttpServletRequest request, AES256Exception e){
        logger.error("[GlobalExceptionHandler.AES256Exception] : ", e);
        metricsHelper.pushToDD(EncryptionConstants.AES256_ERROR, EncryptionConstants.AES256_ERROR_FROM_API);
        String message = ErrorMessages.SAGA_API_AES256_FAILURE.getErrorMessage();
        RechargeSagaGenericErrorResponse res = new
            RechargeSagaGenericErrorResponse(message,
            ErrorMessages.SAGA_API_AES256_FAILURE.getErrorCode(),
            HttpStatus.BAD_REQUEST);
        return new ResponseEntity<>(res, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public  ResponseEntity<RechargeSagaGenericErrorResponse> handleHttpMessageNotReadableException(HttpServletRequest request, HttpMessageNotReadableException e){
        logger.error("[GlobalExceptionHandler.HttpMessageNotReadableException] {}", e);
        String message = Constants.MessageConstants.ERROR_PARSING_REQUEST;

        //Logging the info to debug this exception
        if(e.getCause() != null && e.getCause() instanceof JsonMappingException){
            //accessing description of path that lead to the problem that triggered this exception
            logger.error("JsonMappingException's PathReference = "+((JsonMappingException) e.getCause()).getPathReference());

            Object processor = ((JsonMappingException) e.getCause()).getProcessor();
            if(processor != null && processor instanceof UTF8StreamJsonParser){
                logger.error("UTF8StreamJsonParser's parsingContext = " + ((UTF8StreamJsonParser)processor).getParsingContext());
            }
        }

        RechargeSagaGenericErrorResponse res = new
                RechargeSagaGenericErrorResponse(message,
                ErrorMessages.SAGA_API_BODY_PARSING_FAILURE.getErrorCode(),
                HttpStatus.BAD_REQUEST);
        return new ResponseEntity(res, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({Exception.class})
    public  ResponseEntity<RechargeSagaGenericErrorResponse> defaultException(Exception e) { //to handle runTime exceptions
        logger.info("[GlobalExceptionHandler.defaultException] {}", e);
        RechargeSagaGenericErrorResponse res = new
                RechargeSagaGenericErrorResponse(ErrorMessages.SAGA_GENERIC_API_FAILURE.getErrorMessage(),
                ErrorMessages.SAGA_GENERIC_API_FAILURE.getErrorCode(),
                HttpStatus.BAD_REQUEST);
        return new ResponseEntity(res, HttpStatus.BAD_REQUEST);
    }
}

