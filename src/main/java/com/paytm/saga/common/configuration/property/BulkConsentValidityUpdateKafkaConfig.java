package com.paytm.saga.common.configuration.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration("bulkConsentValidityUpdateKafkaConfig")
@ConfigurationProperties(prefix = "kafka.bulk-consent-validity-update")
public class BulkConsentValidityUpdateKafkaConfig {
    private String bootstrapServers;
    private List<String> topicNames;
    private String consumerGroup;

    @Override
    public String toString() {
        return "BulkConsentValidityUpdate{" +
                "bootstrapServers='" + bootstrapServers + '\'' +
                ", topicNames=" + topicNames +
                ", consumerGroup='" + consumerGroup + '\'' +
                '}';
    }
}