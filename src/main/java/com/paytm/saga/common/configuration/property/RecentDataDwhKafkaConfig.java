package com.paytm.saga.common.configuration.property;

import lombok.Data;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration("RecentDataDwhKafkaConfig")
@ConfigurationProperties("recent.dwh.kafka")
@Data
public class RecentDataDwhKafkaConfig {
    private String bootstrapServers;
    private String topicName;
    private String consumerGroup;
    private String clientId;
    @Bean
    public Map<String, Object> recentDataPropertiesConfig() {
        Map<String, Object> Props = new HashMap<>();
        Props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        Props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        Props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        Props.put(ProducerConfig.CLIENT_ID_CONFIG, clientId);
        return Props;
    }
    @Bean
    public ProducerFactory<String, Object> recentDataProducerFactory() {
        return new DefaultKafkaProducerFactory<>(recentDataPropertiesConfig());
    }

    @Bean
    public KafkaTemplate<String, Object> recentDataKafkaTemplate() {
        return new KafkaTemplate<>(recentDataProducerFactory());
    }
}
