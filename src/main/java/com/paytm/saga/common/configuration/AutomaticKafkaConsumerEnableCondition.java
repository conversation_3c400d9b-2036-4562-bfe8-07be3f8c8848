package com.paytm.saga.common.configuration;

import java.util.Objects;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import com.paytm.saga.common.constant.Constants;

public class AutomaticKafkaConsumerEnableCondition implements Condition {
	@Override
	public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
		if (Objects.nonNull(System.getenv(Constants.EnvVariableConstants.AUTOMATIC_CONSUMER_ENABLE))
				&& (System.getenv(Constants.EnvVariableConstants.AUTOMATIC_CONSUMER_ENABLE).equalsIgnoreCase("true"))) {
			return true;
		} else {
			return false;
		}
	}
}
