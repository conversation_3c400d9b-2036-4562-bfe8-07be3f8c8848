package com.paytm.saga.common.configuration.property;


import com.paytm.saga.common.constant.Constants;
import lombok.Data;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.Map;

@Configuration("CDCRollbackKafkaConfig")
@ConfigurationProperties("cdcrollback.kafka")
@Data
public class CDCRollbackKafkaConfig {

    private String bootstrapServers;
    private String topicName;
    private String consumerGroup;
    private String clientId;

    @Bean
    public Map<String, Object> cdcRollbackPropertiesConfig() {
        Map<String, Object> Props = new HashMap<>();
        Props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, getBootstrapServers());
        Props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        Props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        Props.put(ProducerConfig.CLIENT_ID_CONFIG, clientId);
        return Props;
    }
    @Bean
    public ProducerFactory<String, String> cdcRollbackProducerFactory() {
        return new DefaultKafkaProducerFactory<>(cdcRollbackPropertiesConfig());
    }

    @Bean
    public KafkaTemplate<String, String> cdcRollbackKafkaTemplate() {
        return new KafkaTemplate<>(cdcRollbackProducerFactory());
    }
    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public String getConsumerGroup() {
        return consumerGroup;
    }

    public void setConsumerGroup(String consumerGroup) {
        this.consumerGroup = consumerGroup;
    }

}
