package com.paytm.saga.common.configuration.RestTemplate;

import com.paytm.saga.common.configuration.property.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> on 15/07/2021
 */

@Configuration
public class RestTemplateBeanFactory extends RestTemplateConfiguration{
    private final PgRestClientConfig pgRestClientConfig;
    private final RecentRestClientConfig recentRestClientConfig;
    private final ReminderRestClientConfig reminderRestClientConfig;
    private final SMSCardsRestClientConfig smsCardsRestClientConfig;
    private final DcatRestClientConfig dcatRestClientConfig;

    private final OmsRestClientConfig omsRestClientConfig;

    private final EsRestClientConfig esRestClientConfig;
    private final GenericRestClientConfig genericRestClientConfig;
    private final RpsRestClientConfig rpsRestClientConfig;
    private final InsuranceRestClientConfig insuranceRestClientConfig;
    @Autowired
    public RestTemplateBeanFactory(@NonNull final PgRestClientConfig pgRestClientConfig,
                                   @NonNull final RecentRestClientConfig recentRestClientConfig,
                                   @NonNull final ReminderRestClientConfig reminderRestClientConfig,
                                   @NonNull final DcatRestClientConfig dcatRestClientConfig,
                                   @NonNull final SMSCardsRestClientConfig smsCardsRestClientConfig,
                                   @NonNull final OmsRestClientConfig omsRestClientConfig,
                                   @NonNull final EsRestClientConfig esRestClientConfig,
                                   @NonNull final RpsRestClientConfig rpsRestClientConfig,
                                   @NonNull final GenericRestClientConfig genericRestClientConfig, InsuranceRestClientConfig insuranceRestClientConfig){
        this.pgRestClientConfig = pgRestClientConfig;
        this.recentRestClientConfig = recentRestClientConfig;
        this.reminderRestClientConfig= reminderRestClientConfig;
        this.dcatRestClientConfig = dcatRestClientConfig;
        this.smsCardsRestClientConfig=smsCardsRestClientConfig;
        this.omsRestClientConfig=omsRestClientConfig;
        this.esRestClientConfig = esRestClientConfig;
        this.genericRestClientConfig=genericRestClientConfig;
        this.rpsRestClientConfig=rpsRestClientConfig;
        this.insuranceRestClientConfig = insuranceRestClientConfig;
    }

    @Bean("insuranceRestTemplateClient")
    public RestTemplate createInsuranceRestTemplateClient() {
        return new RestTemplate(createRequestFactory(insuranceRestClientConfig));
    }

    @Bean("PgRestTemplateClient")
    public RestTemplate createPgRestTemplateClient() {
        return new RestTemplate(createRequestFactory(pgRestClientConfig));
    }

    @Bean("EsRestTemplateClient")
    public RestTemplate createEsRestTemplateClient() {
        return new RestTemplate(createRequestFactory(esRestClientConfig));
    }

    @Bean("OmsRestTemplateClient")
    public RestTemplate createOmsRestTemplateClient() {
        return new RestTemplate(createRequestFactory(omsRestClientConfig));
    }

    @Bean("RecentRestTemplateClient")
    public RestTemplate createRecentRestTemplateClient() {
        return new RestTemplate(createRequestFactory(recentRestClientConfig));
    }

    @Bean("ReminderRestTemplateClient")
    public RestTemplate createReminderRestTemplateClient() {
        return new RestTemplate(createRequestFactory(reminderRestClientConfig));
    }
    @Bean("SMSCardsRestTemplateClient")
    public RestTemplate createSMSCardsRestTemplateClient() {
        return new RestTemplate(createRequestFactory(smsCardsRestClientConfig));
    }

    @Bean("DcatRestTemplateClient")
    public RestTemplate createDcatRestTemplateClient() {
        return new RestTemplate(createRequestFactory(dcatRestClientConfig));
    }

    @Bean("GenericRestTemplateClient")
    public RestTemplate createGenericRestTemplateClient() {
        return new RestTemplate(createRequestFactory(genericRestClientConfig));
    }
    @Bean("RpsRestTemplateClient")
    public RestTemplate createRpsRestTemplateClient() {
        return new RestTemplate(createRequestFactory(rpsRestClientConfig));
    }
}
