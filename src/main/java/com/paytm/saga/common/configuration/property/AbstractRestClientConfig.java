package com.paytm.saga.common.configuration.property;

/**
 * <AUTHOR> on 15/07/2021
 */
public abstract class AbstractRestClientConfig {
    protected int requestTimeout;
    protected int readTimeout;
    protected int connectTimeout;
    protected int maxConnectionPerRoute;

    public int getRequestTimeout() {
        return requestTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public int getMaxConnectionPerRoute() { return maxConnectionPerRoute; }

    public void setRequestTimeout(int requestTimeout) {
        this.requestTimeout = requestTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setMaxConnectionPerRoute(int maxConnectionPerRoute) { this.maxConnectionPerRoute = maxConnectionPerRoute; }

    @Override
    public String toString() {
        return "AbstractRestClientConfig{" +
                "requestTimeout=" + requestTimeout +
                ", readTimeout=" + readTimeout +
                ", connectTimeout=" + connectTimeout +
                ", maxConnectionPerRoute=" + maxConnectionPerRoute +
                '}';
    }
}
