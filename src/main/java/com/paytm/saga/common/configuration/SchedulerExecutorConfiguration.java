package com.paytm.saga.common.configuration;

import com.paytm.saga.common.constant.Constants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

@Configuration
public class SchedulerExecutorConfiguration {

    ScheduledExecutorService scheduledExecutorService;

    @Bean
    public ScheduledExecutorService getScheduledExecutorService(){
        scheduledExecutorService=Executors.newScheduledThreadPool(Constants.CommonConstants.THREAD_POOL_SIZE);

        return scheduledExecutorService;
    }
}
