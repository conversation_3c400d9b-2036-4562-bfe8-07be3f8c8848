package com.paytm.saga.common.configuration;

import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

@Configuration
public class LogContextFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String transactionId = UUID.randomUUID().toString();
        ThreadContext.put("logid", transactionId);
        try {
            doFilter(request, response, filterChain);
        } finally {
            ThreadContext.clearMap();
            LoggerThreadContext.clear();
        }
    }
}