server.port=${PORT:8083}
env_name=${ENV}
spring.jackson.default-property-inclusion=NON_NULL
spring.application.name=recharge-saga
spring.main.allow-bean-definition-overriding=true
management.endpoints.web.expose=*
management.endpoint.health.enabled=true
management.endpoint.metrics.enabled=true
management.endpoints.web.exposure.include=*
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.tags.application=recharges-saga
db.writes.max.attempts=8

logging.level.org.springframework.data.cassandra.core.cql.CqlTemplate=DEBUG
spring.main.allow-circular-references=true