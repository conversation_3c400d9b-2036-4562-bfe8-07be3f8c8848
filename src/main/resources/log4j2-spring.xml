<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
<Properties>
    <Property name="springAppName">${bundle:application:spring.application.name}</Property>
    <Property name="sleuthInfo">${springAppName},%X{X-B3-TraceId}</Property>
</Properties>

<Appenders>
    <File name="RollingFile"
          fileName="/Users/<USER>/Work/logs/recharge-saga/recharge-saga.log">
        <PatternLayout>
            <!--             <pattern>%d %p %C{1.} [%t] %m%n</pattern> -->
            <pattern> %style{%d{ISO8601}}{bright,red} %highlight{%-5level }[${sleuthInfo}][%style{%t}{bright,blue}] %style{%logger{1.}}{bright,red}: %msg%n%throwable</pattern>
            <disableAnsi>false</disableAnsi>
        </PatternLayout>
    </File>
</Appenders>



<Loggers>
    <!-- LOG everything at INFO level -->
    <Root level="info">
        <AppenderRef ref="RollingFile" />
    </Root>

    <!-- LOG "com.paytm.saga*" at TRACE level -->
    <Logger name="com.paytm.saga" level="debug"></Logger>
    <Logger name="com.paytm.dogstatsd" level="error"></Logger>
    <Logger name="org.springframework.data.cassandra" level="DEBUG"/>
</Loggers>

</Configuration>