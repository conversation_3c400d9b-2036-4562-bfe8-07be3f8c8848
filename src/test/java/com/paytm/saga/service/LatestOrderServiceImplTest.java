package com.paytm.saga.service;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.dto.plans.PlansMapCache;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.LatestOrdersRequest;
import com.paytm.saga.dto.LatestOrdersResponse;
import com.paytm.saga.dto.catalogue.ActiveInactivePidMapCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.util.BillStateHandler;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.EventStateHandler;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.logging.log4j.Logger;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LatestOrderServiceImplTest {
    @InjectMocks
    private LatestOrderServiceImpl latestOrderService;
    @Mock
    private RecentsRepositoryWrapperService recentsRepository;

    @Mock
    private MetricsHelper metricsHelper;
    @Mock 
    DateUtil dateUtil;
    @Mock
    EventStateHandler eventStateHandler;
    @Mock
    BillStateHandler billStateHandler;
    @Mock
    CVRProductCache cvrProductCache;
    @Mock
    ActiveInactivePidMapCache activeInactivePidMapCache;
    @Mock
    SuggestedCardService suggestedCardService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        latestOrderService = new LatestOrderServiceImpl(recentsRepository,metricsHelper,suggestedCardService);
        PlansMapCache.getInstance().clearPlansMapCache();
    }
    @Mock
    private Logger logger;

    @Test
    public void testIsValidRecentWhenInactivePIDIsFalse() {
        Recents recent = new Recents();
        String planBucket = "Special Recharge";
        Long productId = 12345L;
        recent.setProductId(productId);
        recent.setOrderId(122333L);
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("XXXX XXXX XXXX 1234");
        recentsPrimaryKey.setOperator("hdcf");
        recentsPrimaryKey.setPlanBucket(planBucket);
        recent.setKey(recentsPrimaryKey);
        Product product=new Product();
        product.setProductId(productId);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        boolean isValid = latestOrderService.isValidRecent(recent, planBucket);
        assertEquals(true, isValid);
    }


    @Test
    public void testGetLatestOrderWrapperWithNullValues() {
        LatestOrdersRequest request = new LatestOrdersRequest();
        try {
            latestOrderService.getLatestOrderWrapper(request);
        } catch (RechargeSagaBaseException e) {
            assertEquals("Missing required parameters", e.getMessage());
            verify(recentsRepository, never()).findBycustomerIdAndserviceAndrechargeNumberAndoperator(anyLong(), anyString(), anyString(), anyString());

        }
    }
    @Test
    public void testIsExpiryWithinRange_ValidRange() {
        // Create a Date that is within the specified range
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -5); // 5 days ago
        Date expiryDate = calendar.getTime();

        int startDays = 10;
        int endDays = 1;

        boolean result = latestOrderService.isExpiryWithinRange(expiryDate, startDays, endDays);

        assertEquals(false, result);
    }

    @Test
    public void testIsExpiryWithinRange_InvalidRange() {
        // Create a Date that is outside the specified range
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -15); // 15 days ago
        Date expiryDate = calendar.getTime();

        int startDays = 10;
        int endDays = 5;

        boolean result = latestOrderService.isExpiryWithinRange(expiryDate, startDays, endDays);

        assertEquals(false, result);
    }

    @Test
    public void testGetLatestOrderWrapperNoNullValues() throws RechargeSagaBaseException {
        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setCustomerId(12345L);
        request.setRechargeNumber("1234567890");
        request.setService("financial service");
        request.setOperator("airtel");
        request.setProductId(1L);
        List<Recents> mockRecentsList = new ArrayList<>();
        Recents recents1 = createMockRecents("Special Recharge", "credit card", 100.0);
        mockRecentsList.add(recents1);
        Recents recents2 = createMockRecents("Data Pack", "debit card", 50.0);
        mockRecentsList.add(recents2);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(
                request.getCustomerId(),
                request.getService(),
                request.getRechargeNumber(),
                request.getOperator()
        )).thenReturn(mockRecentsList);
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);
        when(latestOrderServiceSpy.filterRecentsByPlanBucket(anyList(), anyString())).thenReturn(mockRecentsList);
        when(latestOrderServiceSpy.prepareLatestOrdersResponse(anyList(), anyList())).thenReturn(Collections.singletonList(new LatestOrdersResponse()));
        latestOrderServiceSpy.getLatestOrderWrapper(request);
        verify(recentsRepository, times(1)).findBycustomerIdAndserviceAndrechargeNumberAndoperator(
                request.getCustomerId(),
                request.getService(),
                request.getRechargeNumber(),
                request.getOperator()
        );
        verify(latestOrderServiceSpy, times(2)).filterRecentsByPlanBucket(eq(mockRecentsList), anyString());
        verify(latestOrderServiceSpy, times(1)).prepareLatestOrdersResponse(eq(mockRecentsList), anyList());
    }

    @Test
    public void testGetLatestOrderWrapperNullOrderId() throws RechargeSagaBaseException {
        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setCustomerId(12345L);
        request.setRechargeNumber("1234567890");
        request.setService("financial service");
        request.setProductId(1L);
        request.setOperator("airtel");
        List<Recents> mockRecentsList = new ArrayList<>();
        Recents recents1 = createMockRecents("Special Recharge", "credit card", 100.0);
        recents1.setOrderId(null);
        mockRecentsList.add(recents1);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(
                request.getCustomerId(),
                request.getService(),
                request.getRechargeNumber(),
                request.getOperator()
        )).thenReturn(mockRecentsList);
        assertEquals(0,latestOrderService.getLatestOrderWrapper(request).getData().size());

    }

    @Test
    public void testGetLatestOrderWrapper_RepoError() throws RechargeSagaBaseException {
        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setCustomerId(12345L);
        request.setRechargeNumber("1234567890");
        request.setService("financial service");
        request.setOperator("airtel");
        request.setProductId(1L);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(
                request.getCustomerId(),
                request.getService(),
                request.getRechargeNumber(),
                request.getOperator()
        )).thenThrow(new RuntimeException("Repository error"));
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);
        try {
            latestOrderServiceSpy.getLatestOrderWrapper(request);
            fail("Expected RechargeSagaBaseException was not thrown");
        } catch (RechargeSagaBaseException e) {
            verify(latestOrderServiceSpy, never()).prepareLatestOrdersResponse(anyList(), anyList());
            verify(latestOrderServiceSpy, never()).filterRecentsByPlanBucket(anyList(), anyString());
        }
    }   
    @Test
    public void testGetLatestOrderWrapper_FilterError() throws RechargeSagaBaseException {
        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setCustomerId(12345L);
        request.setRechargeNumber("1234567890");
        request.setService("financial service");
        request.setOperator("airtel");
        request.setProductId(1L);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(
                request.getCustomerId(),
                request.getService(),
                request.getRechargeNumber(),
                request.getOperator()
        )).thenReturn(new ArrayList<>());
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);
        when(latestOrderServiceSpy.filterRecentsByPlanBucket(anyList(), anyString())).thenThrow(new RuntimeException("Filter error"));
        try {
            latestOrderServiceSpy.getLatestOrderWrapper(request);
            fail("Expected RechargeSagaBaseException was not thrown");
        } catch (RechargeSagaBaseException e) {
            verify(latestOrderServiceSpy, never()).prepareLatestOrdersResponse(anyList(), anyList());
        }
    }
    @Test(expected = RechargeSagaBaseException.class)
    public void testGetLatestOrderWrapper_ResponseError() throws RechargeSagaBaseException {
        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setCustomerId(12345L);
        request.setRechargeNumber("1234567890");
        request.setService("financial service");
        request.setOperator("airtel");
        request.setProductId(1L);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(
                request.getCustomerId(),
                request.getService(),
                request.getRechargeNumber(),
                request.getOperator()
        )).thenReturn(new ArrayList<>());
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);
        when(latestOrderServiceSpy.filterRecentsByPlanBucket(anyList(), anyString())).thenReturn(new ArrayList<>());
        when(latestOrderServiceSpy.prepareLatestOrdersResponse(anyList(), anyList()))
                .thenThrow(new RuntimeException("Response error"));
        latestOrderServiceSpy.getLatestOrderWrapper(request);
    }

    @Test
    public void testPrepareLatestOrdersResponse_NoData() {
        List<LatestOrdersResponse> response = latestOrderService.prepareLatestOrdersResponse(null, null);
        assertEquals(0, response.size());
    }
    @Test
    public void testPrepareLatestOrdersResponse_WithSpecialRechargeDataOnly() {
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);

        LatestOrdersResponse mockResponse = new LatestOrdersResponse();
        Mockito.doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            if (args[1] != null && args[1].equals("Special Recharge")) {
                return mockResponse;
            }
            return null;
        }).when(latestOrderServiceSpy).getLatestOrdersResponse(anyList(), anyString());

        Recents recentsSpecialRecharge = createMockRecents("Special Recharge", null, 100);

        List<Recents> recentsSpecialRechargeList = new ArrayList<>();
        recentsSpecialRechargeList.add(recentsSpecialRecharge);

        List<Recents> recentsDataPackList = new ArrayList<>();
        
        List<LatestOrdersResponse> response = latestOrderServiceSpy.prepareLatestOrdersResponse(recentsSpecialRechargeList, recentsDataPackList);
        verify(latestOrderServiceSpy, times(2)).getLatestOrdersResponse(anyList(), anyString());
        assertEquals(1, response.size());
        assertEquals(mockResponse, response.get(0));
    }
    @Test
    public void testPrepareLatestOrdersResponse_WithSpecialDataPackDataOnly() {
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);

        LatestOrdersResponse mockResponse = new LatestOrdersResponse();
        Mockito.doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            if (args[1] != null && args[1].equals("Data Pack")) {
                return mockResponse;
            }
            return null;
        }).when(latestOrderServiceSpy).getLatestOrdersResponse(anyList(), anyString());

        Recents DataPackRecharge = createMockRecents("Data Pack", null, 100);

        List<Recents> recentsSpecialRechargeList = new ArrayList<>();
        List<Recents> recentsDataPackList = new ArrayList<>();
        recentsDataPackList.add(DataPackRecharge);
        List<LatestOrdersResponse> response = latestOrderServiceSpy.prepareLatestOrdersResponse(recentsSpecialRechargeList, recentsDataPackList);
        verify(latestOrderServiceSpy, times(2)).getLatestOrdersResponse(anyList(), anyString());
        assertEquals(1, response.size());
        assertEquals(mockResponse, response.get(0));
    }

    @Test
    public void testPrepareLatestOrdersResponse_WithBothSpecialRechargeDataPackData() {
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);

        LatestOrdersResponse mockResponse = new LatestOrdersResponse();
        Mockito.doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            if (args[1] != null && (args[1].equals("Special Recharge") || args[1].equals("Data Pack"))) {
                return mockResponse;
            }
            return null;
        }).when(latestOrderServiceSpy).getLatestOrdersResponse(anyList(), anyString());
        Recents DataPackRecharge = createMockRecents("Data Pack", null, 100);
        List<Recents> recentsSpecialRechargeList = new ArrayList<>();
        Recents recentsSpecialRecharge = createMockRecents("Special Recharge", null, 100);
        List<Recents> recentsDataPackList = new ArrayList<>();
        recentsSpecialRechargeList.add(recentsSpecialRecharge);
        recentsDataPackList.add(DataPackRecharge);
        System.out.println(recentsDataPackList.size());
        System.out.println("SS"+recentsSpecialRechargeList.size());
        List<LatestOrdersResponse> response = latestOrderServiceSpy.prepareLatestOrdersResponse(recentsSpecialRechargeList, recentsDataPackList);
        verify(latestOrderServiceSpy, times(2)).getLatestOrdersResponse(anyList(), anyString());
        assertEquals(2, response.size());
        assertEquals(mockResponse, response.get(0));
    }

    @Test
    public void filterRecentsByPlanBucketTestSpecialRechargePlanBucketProductIdActive(){
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);
        List<Recents> recents = new ArrayList<>();
        recents.add(createMockRecents("Special Recharge", "Card", 10, 12345L));
        for(int i = 0; i<4; i++){
            recents.add(createMockRecents("Data Pack", "Card", 10, 12345L));
        }
        for(int i = 0; i<3; i++){
            recents.add(createMockRecents("Top Up", "Card", 10, 12345L));
        }
        doReturn(true).when(latestOrderServiceSpy).isValidRecent(recents.get(0),"Special Recharge");
        List<Recents> filteredRecents = latestOrderServiceSpy.filterRecentsByPlanBucket(recents, "Special Recharge");
        assertEquals(1, filteredRecents.size());
        List<Date> txnTimes = filteredRecents.stream()
                .map(Recents::getTxnTime)
                .collect(Collectors.toList());
        for (int i = 0; i < txnTimes.size() - 1; i++) {
            assertTrue(txnTimes.get(i).compareTo(txnTimes.get(i + 1)) >= 0);
        }
    }

    @Test
    public void filterRecentsByPlanBucketTestSpecialRechargePlanBucketProductIdActiveSetPlanByLogic(){
        LatestOrderServiceImpl latestOrderServiceSpy = Mockito.spy(latestOrderService);
        List<Recents> recents = new ArrayList<>();
        Recents recentObj=createMockRecents("", "Card", 10, 12345L);
        recents.add(recentObj);
        PlansMapCache.getInstance().addPlansDetail(recentObj.getKey().getService()+
                "_"+
                recentObj.getKey().getOperator()+
                "_"+
                recentObj.getCircle()+
                "_"+
                (int)Math.round(recentObj.getTxnAmount()),"Special Recharge");

        for(int i = 0; i<4; i++){
            recents.add(createMockRecents("Data Pack", "Card", 10, 12345L));
        }
        for(int i = 0; i<3; i++){
            recents.add(createMockRecents("Top Up", "Card", 10, 12345L));
        }
        doReturn(true).when(latestOrderServiceSpy).isValidRecent(recents.get(0),"Special Recharge");
        List<Recents> filteredRecents = latestOrderServiceSpy.filterRecentsByPlanBucket(recents, "Special Recharge");
        assertEquals(1, filteredRecents.size());
        List<Date> txnTimes = filteredRecents.stream()
                .map(Recents::getTxnTime)
                .collect(Collectors.toList());
        for (int i = 0; i < txnTimes.size() - 1; i++) {
            assertTrue(txnTimes.get(i).compareTo(txnTimes.get(i + 1)) >= 0);
        }
    }

    @Test
    public void testFilterRecentsByPlanBucket_EmptyPlanBucket() {
        List<Recents> recentsList = new ArrayList<>();
        List<Recents> result = latestOrderService.filterRecentsByPlanBucket(recentsList, "");
        assertEquals(0, result.size());
    }

    @Test
    public void testFilterRecentsByPlanBucket_EmptyRecentsList() {
        List<Recents> recentsList = Collections.emptyList();
        List<Recents> result = latestOrderService.filterRecentsByPlanBucket(recentsList, "Special Recharge");
        assertEquals(0, result.size());
    }
    @Test
    public void testGetLatestOrdersResponse_EmptyRecents() {
        List<Recents> recentsList = Collections.emptyList();
        LatestOrdersResponse response = latestOrderService.getLatestOrdersResponse(recentsList, "Special Recharge");
        assertNull(response);
    }

    @Test
    public void testGetLatestOrdersResponse_ProductIdNull() throws RechargeSagaBaseException {
        Recents recents = createMockRecents("Special Recharge", "credit card", 100.0);
        recents.setProductId(null);
        recents.setIsMarkAsPaid(true);
        recents.setDueAmount(null);
        List<Recents> recentsList = new ArrayList<>();
        recentsList.add(recents);
        System.out.println("recentsList"+recentsList);
        Calendar calendar = Calendar.getInstance();

        calendar.set(2023, Calendar.AUGUST, 28, 10, 30, 0); 
        Date txnTime = calendar.getTime();
        calendar.set(2023, Calendar.AUGUST, 28, 12, 0, 0); 
        Date markAsPaidTime = calendar.getTime();

        recents.setTxnTime(txnTime);
        recents.setMarkAsPaidTime(markAsPaidTime);

        LatestOrdersResponse response = latestOrderService.getLatestOrdersResponse(recentsList, "Special Recharge");
        assertNotNull(response);
        assertEquals(recents.getTxnAmount(), response.getTxnAmount());
        assertEquals(recents.getRechargeNumber2(), response.getPlanCircleKey());
        assertEquals(recents.getRechargeNumber3(), response.getPlanId());
        assertNull(response.getPid()); 
        assertEquals("Special Recharge", response.getPlanBucket());
    }
    @Test
    public void testGetLatestOrdersResponse_planBucketDataPack() throws RechargeSagaBaseException {
        Recents recents = createMockRecents("Data Pack", "credit card", 100.0);
        recents.setProductId(null);
        recents.setIsMarkAsPaid(true);
        recents.setDueAmount(null);
        List<Recents> recentsList = new ArrayList<>();
        recentsList.add(recents);
        System.out.println("recentsList"+recentsList);
        Calendar calendar = Calendar.getInstance();

        calendar.set(2023, Calendar.AUGUST, 28, 10, 30, 0); 
        Date txnTime = calendar.getTime();
        calendar.set(2023, Calendar.AUGUST, 28, 12, 0, 0); 
        Date markAsPaidTime = calendar.getTime();

        recents.setTxnTime(txnTime);
        recents.setMarkAsPaidTime(markAsPaidTime);

        LatestOrdersResponse response = latestOrderService.getLatestOrdersResponse(recentsList, "Data Pack");
        assertNotNull(response);
        assertEquals(recents.getTxnAmount(), response.getTxnAmount());
        assertEquals(recents.getRechargeNumber2(), response.getPlanCircleKey());
        assertEquals(recents.getRechargeNumber3(), response.getPlanId());
        assertNull(response.getPid()); 
        assertEquals("Data Pack", response.getPlanBucket());
    }

    @Test
    public void testIsValidRecent_OrderIdNull() {
        Recents recent = createMockRecents("Special Recharge", "Card", 10.0);
        recent.setOrderId(null);
        assertFalse(latestOrderService.isValidRecent(recent, "Special Recharge"));
    }

    private Recents createMockRecents(String planBucket, String payType, double txnAmount, Long... productId) {
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("XXXX XXXX XXXX 1234");
        recentsPrimaryKey.setOperator("hdcf");
        recentsPrimaryKey.setPlanBucket(planBucket);
        recents.setKey(recentsPrimaryKey);
        recents.setPayType(payType);
        recents.setEventSource("AB");
        recents.setMcn("XXXX XXXX XXXX 1234");
        recents.setRechargeNumber2("Tamil Nadu");
        recents.setRechargeNumber3("TNTOPUP100");
        recents.setTxnAmount(txnAmount);
        Random random = new Random();
        recents.setOrderId(random.nextLong());
        recents.setProductId(productId.length > 0 ? productId[0] : null); // Set the productId
    
        try {
            recents.setBillDate(new Date());
            recents.setDueDate(new Date());
            recents.setTxnTime(new Date());
        } catch (Exception e) {
            e.printStackTrace();
        }
    
        return recents;
    }

    @Test
    public void setPlanBucket_ValidPlanBucket_SpecialRecharge() {
        Recents recent = createMockRecents("Special Recharge", "Card", 10.0);
        latestOrderService.setPlanBucket(recent);
        assertEquals("Special Recharge", recent.getKey().getPlanBucket());
    }

    @Test
    public void setPlanBucket_ValidPlanBucket_Topup() {
        Recents recent = createMockRecents("Top up", "Card", 10.0);
        latestOrderService.setPlanBucket(recent);
        assertEquals("Top up", recent.getKey().getPlanBucket());
    }

    @Test
    public void setPlanBucket_ValidPlanBucketRoaming() {
        Recents recent = createMockRecents("Roaming", "Card", 10.0);
        latestOrderService.setPlanBucket(recent);
        assertEquals("Roaming", recent.getKey().getPlanBucket());
    }

    @Test
    public void setPlanBucket_ValidPlanBucketDataPack() {
        Recents recent = createMockRecents("Data Pack", "Card", 10.0);
        latestOrderService.setPlanBucket(recent);
        assertEquals("Data Pack", recent.getKey().getPlanBucket());
    }

    @Test
    public void setPlanBucket_InvalidPlanBucket_UpdatesPlanBucket() {
        Recents recent = createMockRecents("Invalid Bucket", "Card", 10.0);
        String key = recent.getKey().getService() + "_" + recent.getKey().getOperator() + "_" + recent.getCircle() + "_" + (int) Math.round(recent.getTxnAmount());
        PlansMapCache.getInstance().addPlansDetail(key, "Updated Plan");
        latestOrderService.setPlanBucket(recent);
        assertEquals("Updated Plan", recent.getKey().getPlanBucket());
    }

    @Test
    public void setPlanBucket_InvalidPlanBucket_NoUpdateWhenPlanNotFound() {
        Recents recent = createMockRecents("Invalid Bucket", "Card", 10.0);
        latestOrderService.setPlanBucket(recent);
        assertEquals("Invalid Bucket", recent.getKey().getPlanBucket());
    }

    @Test
    public void setPlanBucket_EmptyPlanBucket_NoUpdate() {
        Recents recent = createMockRecents("", "Card", 10.0);
        latestOrderService.setPlanBucket(recent);
        assertEquals("", recent.getKey().getPlanBucket());
    }

    @Test
    public void setPlanBucket_NullPlanBucket_NoUpdate() {
        Recents recent = createMockRecents(null, "Card", 10.0);
        latestOrderService.setPlanBucket(recent);
        assertNull(recent.getKey().getPlanBucket());
    }

    @Test
    public void isValidRecent_OrderIdNull() {
        Recents recent = createMockRecents("Special Recharge", "Card", 10.0);
        recent.setOrderId(null);
        assertFalse(latestOrderService.isValidRecent(recent, "Special Recharge"));
    }

    @Test
    public void isValidRecent_InactivePID_ValidNewPID() {
        Recents inactiveRecent = new Recents();
        String planBucket = "Special Recharge";
        inactiveRecent.setProductId(12345L);
        inactiveRecent.setOrderId(122333L);
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("XXXX XXXX XXXX 1234");
        recentsPrimaryKey.setOperator("hdcf");
        recentsPrimaryKey.setPlanBucket(planBucket);
        inactiveRecent.setKey(recentsPrimaryKey);
        Product inactiveProduct=new Product();
        inactiveProduct.setProductId(12345L);
        inactiveProduct.setStatus(0);
        CVRProductCache.getInstance().addProductDetails(inactiveProduct);

        Recents activeRecent = new Recents();
        activeRecent.setProductId(2323L);
        activeRecent.setOrderId(122333L);
        RecentsPrimaryKey inactiveRecentsPrimaryKey = new RecentsPrimaryKey();
        inactiveRecentsPrimaryKey.setCustomerId(1123L);
        inactiveRecentsPrimaryKey.setService("financial services");
        inactiveRecentsPrimaryKey.setRechargeNumber("XXXX XXXX XXXX 1234");
        inactiveRecentsPrimaryKey.setOperator("hdcf");
        inactiveRecentsPrimaryKey.setPlanBucket(planBucket);
        activeRecent.setKey(inactiveRecentsPrimaryKey);
        Product activeProduct=new Product();
        activeProduct.setProductId(2323L);
        activeProduct.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(activeProduct);

        Map<String, Long> activeInactivePidMap = new HashMap<>();
        activeInactivePidMap.put("12345", 2323L);
        ActiveInactivePidMapCache.getInstance().storePidMapByApi(activeInactivePidMap);

        boolean isValid = latestOrderService.isValidRecent(inactiveRecent, planBucket);
        assertEquals(true, isValid);
    }

}