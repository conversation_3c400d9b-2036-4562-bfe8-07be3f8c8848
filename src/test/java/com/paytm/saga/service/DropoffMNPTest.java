package com.paytm.saga.service;

import static org.mockito.Mockito.when;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.paytm.saga.listeners.DropOffDBHelper;
import com.paytm.saga.repository.DropOffRepository;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;
import com.paytm.saga.repository.ReminderHistoryRepository;

import junit.framework.TestCase;

import com.paytm.saga.model.DropOff;

@RunWith(MockitoJUnitRunner.class)
public class DropoffMNPTest extends TestCase {
    @Mock
    private PlanExpiryHistoryRepository planExpiryHistoryRepository;

    @Mock
    DropOffRepository dropOffRepository;

    @Mock
    private DropOffDBHelper dropOffDBHelper;

    @Mock
    private ReminderHistoryRepository reminderHistoryRepository;

    @InjectMocks
    private ReminderHistoryService reminderHistoryService;

    @InjectMocks
    private PlanExpiryHistoryService planExpiryHistoryService;

    @InjectMocks
    private DropOffService dropOffService;

    DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date date1 = new Date();

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);

        date1 = DateUtils.addHours(new Date(), -12);

    }

    private DropOff getD1() {
        DropOff d1 = new DropOff(11772663L, "1778827772", "1772661662", "187262552", "airtel", "upi", "1700", null, "1","pending", "RECHARGE", null, 771881166611L, "up-east", null, null, null, 17716553L, 131414L, false,17725531315L, "98873613631", "mobile", date1,null,null);
        return d1;
    }

    private DropOff getElectricityD() {
        DropOff d4 = new DropOff(11772663L, "1778827772", "1772661662", "187262552", "reliance", "upi", "1700", null,"1", "pending", "VALIDATION", null, 771881166611L, "up-east", null, null, null, 17716553L, 131414L,false, 17725531315L, "98873613631", "electricity", date1,null,null);
        return d4;
    }

    private List<DropOff> getDropOff() {
        List<DropOff> dropOff = new ArrayList<>();
        DropOff d1 = this.getD1();
        DropOff d2 = new DropOff(11772661L, "1778827772", "1772661662", "187262552", "airtel", "upi", "2000", null, "1","pending", "RECHARGE", null, 771881166611L, "up-east", null, null, null, 17716553L, 131414L, false,17725531315L, "98873613631", "mobile", date1,null,null);
        DropOff d3 = new DropOff(11772663L, "1778827772", "1772661662", "187262552", "jio", "upi", "5000", null, "1","pending", "RECHARGE", null, 771881166611L, "up-east", null, null, null, 17716553L, 131414L, false,177255313112L, "98873613631", "mobile", date1,null,null);
        DropOff d4 = this.getElectricityD();
        dropOff.add(d1);
        dropOff.add(d2);
        dropOff.add(d3);
        dropOff.add(d4);
        return dropOff;
    }

    public List<DropOff> getList() {
        List<DropOff> dropOff = this.getDropOff();
        return dropOff;
    }

    @Test
    public void getDropOffTest() {
        List<String> services = new ArrayList<>();
        services.add("mobile");
        services.add("electricity");
        dropOffRepository = Mockito.spy(DropOffRepository.class);

        when(dropOffService.findByCustomerId(14791846164146L)).thenReturn(this.getList());

        Map<String, DropOff> map = dropOffService.getDropOff(14791846164146L, null, null, null, "false");

        DropOff d1 = this.getD1();
        DropOff d2 = this.getElectricityD();
        assertEquals(d1, map.get("98873613631_mobile_airtel"));
        assertEquals(d2, map.get("98873613631_electricity_reliance"));
        assertEquals(null,map.get("98873613631_mobile_jio"));

    }

}
