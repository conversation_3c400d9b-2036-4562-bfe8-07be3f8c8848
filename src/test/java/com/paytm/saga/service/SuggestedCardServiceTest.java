package com.paytm.saga.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.metrics.MetricsHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.service.impl.ServiceConfig;

@RunWith(MockitoJUnitRunner.class)
public class SuggestedCardServiceTest {
    @InjectMocks
    private SuggestedCardService suggestedCardService;
    @Mock
    ServiceConfig serviceConfig;
    @Mock
    MetricsHelper metricsHelper;
    @Mock
    FeatureConfigCache featureConfigCache;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        String config="{\"bsnl\":{\"haryana\":{\"Data Pack\":{\"amount\":100,\"planId\":\"1234\",\"circleName\":\"Haryana\"},\"Special Recharge\":{\"amount\":130,\"planId\":\"1234\",\"circleName\":\"Haryana\"}},\"maharashtra\":{\"Data Pack\":{\"amount\":120,\"planId\":\"2345\",\"circleName\":\"Maharashtra\"},\"Special Recharge\":{\"amount\":110,\"planId\":\"2345\",\"circleName\":\"Maharashtra\"}}},\"airtel\":{\"delhi ncr\":{\"Data Pack\":{\"amount\":140},\"Special Recharge\":{\"amount\":140}},\"haryana\":{\"Data Pack\":{\"amount\":140},\"Special Recharge\":{\"amount\":140}},\"all circles\":{\"Data Pack\":{\"amount\":140},\"Special Recharge\":{\"amount\":140}}}}";
        Map<String, Object> suggestedCardConfig = new ObjectMapper().readValue(config, new TypeReference<Map>() {});
        FeatureConfigCache.getInstance().setSuggestedCardConfig(suggestedCardConfig);
    }

    @Test
    public void getSuggestedCardData_WhenDataPresentInConfig_airtel() {
        Map<String, Object> suggestedCardData = suggestedCardService.getSuggestedCardData("airtel", "delhi ncr", "Data Pack");

        assertFalse(suggestedCardData.isEmpty());
        assertEquals(140, suggestedCardData.get("amount"));
    }

    @Test
    public void getSuggestedCardData_WhenDataPresentInConfig_bsnl() {
        Map<String, Object> suggestedCardData = suggestedCardService.getSuggestedCardData("bsnl", "haryana", "Data Pack");

        assertFalse(suggestedCardData.isEmpty());
        assertEquals(100, suggestedCardData.get("amount"));
        assertNotNull(suggestedCardData.get("planId"));
        assertNotNull(suggestedCardData.get("circleName"));
    }

    @Test
    public void getSuggestedCardData_WhenDataNotPresentInConfig() {
        Map<String, Object> suggestedCardData = suggestedCardService.getSuggestedCardData("jio", "haryana", "Data Pack");
        assertNull(suggestedCardData);
    }

    @Test
    public void createSuggestedCard_ReturnNullWhenAnyParameterIsNull() {
        assertNull(suggestedCardService.createSuggestedCard(null, "circle", "planBucket", 12345L));
        assertNull(suggestedCardService.createSuggestedCard("operator", null, "planBucket", 12345L));
        assertNull(suggestedCardService.createSuggestedCard("operator", "circle", null, 12345L));
        assertNull(suggestedCardService.createSuggestedCard("operator", "circle", "planBucket", null));
    }

    @Test
    public void createSuggestedCard_WhenDataIsValid() {
        SuggestedCard suggestedCard = suggestedCardService.createSuggestedCard("bsnl", "haryana", "Data Pack", 1245L);

        assertNotNull(suggestedCard);
        assertEquals(100.0, suggestedCard.getAmount(), 0.0);
        assertNotNull(suggestedCard.getPlanId());
        assertNotNull(suggestedCard.getPlanCircleKey());
    }

    @Test
    public void createSuggestedCardForMissingRecents_ReturnsEmptyWhenFeatureDisabled() {
        when(serviceConfig.isSuggestedCardEnabled()).thenReturn(false);
        List<SuggestedCard> result = suggestedCardService.createSuggestedCardForMissingRecents(new ArrayList<>(), new LatestOrdersRequest());
        assertTrue(result.isEmpty());
    }

    @Test
    public void createSuggestedCardForMissingRecents_NoPlanBucketMissing() {
        when(serviceConfig.isSuggestedCardEnabled()).thenReturn(true);


        Product mockProduct = new Product();
        mockProduct.setProductId(12345L);
        mockProduct.setCircle("haryana");

        CVRProductCache.getInstance().addProductDetails(mockProduct);

        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setProductId(12345L);
        request.setOperator("bsnl");
        request.setCustomerId(13123434L);
        request.setRechargeNumber("32874372439");
        request.setService("mobile");

        LatestOrdersResponse response = new LatestOrdersResponse();
        response.setPlanBucket(Constants.SPECIAL_RECHARGE);
        response.setPlanBucket(Constants.DATA_PACK);
        List<LatestOrdersResponse> latestOrdersResponse = Collections.singletonList(response);


        List<SuggestedCard> result = suggestedCardService.createSuggestedCardForMissingRecents(latestOrdersResponse, request);

        assertEquals(1, result.size());
    }

    @Test
    public void createSuggestedCardForMissingRecents_MissingDataPack_Airtel() {
        when(serviceConfig.isSuggestedCardEnabled()).thenReturn(true);


        Product mockProduct = new Product();
        mockProduct.setProductId(12345L);
        mockProduct.setCircle("haryana");

        CVRProductCache.getInstance().addProductDetails(mockProduct);

        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setProductId(12345L);
        request.setOperator("airtel");
        request.setCustomerId(13123434L);
        request.setRechargeNumber("32874372439");
        request.setService("mobile");

        LatestOrdersResponse response = new LatestOrdersResponse();
        response.setPlanBucket(Constants.SPECIAL_RECHARGE);
        List<LatestOrdersResponse> latestOrdersResponse = Collections.singletonList(response);


        List<SuggestedCard> result = suggestedCardService.createSuggestedCardForMissingRecents(latestOrdersResponse, request);


        assertEquals(1, result.size());
        assertEquals(Constants.DATA_PACK, result.get(0).getPlanBucket());
        assertNotNull(result.get(0).getAmount());
    }

    @Test
    public void createSuggestedCardForMissingRecents_MissingDataPack_BSNL() {
        when(serviceConfig.isSuggestedCardEnabled()).thenReturn(true);


        Product mockProduct = new Product();
        mockProduct.setProductId(12345L);
        mockProduct.setCircle("haryana");

        CVRProductCache.getInstance().addProductDetails(mockProduct);

        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setProductId(12345L);
        request.setOperator("bsnl");
        request.setCustomerId(13123434L);
        request.setRechargeNumber("32874372439");
        request.setService("mobile");

        LatestOrdersResponse response = new LatestOrdersResponse();
        response.setPlanBucket(Constants.SPECIAL_RECHARGE);
        List<LatestOrdersResponse> latestOrdersResponse = Collections.singletonList(response);


        List<SuggestedCard> result = suggestedCardService.createSuggestedCardForMissingRecents(latestOrdersResponse, request);


        assertEquals(1, result.size());
        assertEquals(Constants.DATA_PACK, result.get(0).getPlanBucket());
        assertNotNull(result.get(0).getAmount());
        assertNotNull(result.get(0).getPlanId());
        assertNotNull(result.get(0).getPlanCircleKey());
    }

    @Test
    public void createSuggestedCardForMissingRecents_MissingDataPackAndSpecialRecharge_BSNL() {
        when(serviceConfig.isSuggestedCardEnabled()).thenReturn(true);

        Product mockProduct = new Product();
        mockProduct.setProductId(12345L);
        mockProduct.setCircle("haryana");

        CVRProductCache.getInstance().addProductDetails(mockProduct);

        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setProductId(12345L);
        request.setOperator("bsnl");
        request.setCustomerId(13123434L);
        request.setRechargeNumber("32874372439");
        request.setService("mobile");

        LatestOrdersResponse response = new LatestOrdersResponse();
        List<LatestOrdersResponse> latestOrdersResponse = Collections.singletonList(response);

        List<SuggestedCard> result = suggestedCardService.createSuggestedCardForMissingRecents(latestOrdersResponse, request);


        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(result1 -> Constants.DATA_PACK.equals(result1.getPlanBucket())));
        assertTrue(result.stream().anyMatch(result1 -> Constants.SPECIAL_RECHARGE.equals(result1.getPlanBucket())));
        assertTrue(result.stream().allMatch(result1 -> result1.getAmount() != null));
        assertTrue(result.stream().allMatch(result1 -> result1.getPlanId() != null));
        assertTrue(result.stream().allMatch(result1 -> result1.getPlanCircleKey() != null));
    }

    @Test
    public void createSuggestedCardForMissingRecentsWhenProductIdIsNotPresentInProduct() {
        when(serviceConfig.isSuggestedCardEnabled()).thenReturn(true);

        Product mockProduct = new Product();
        mockProduct.setProductId(1234L);
        mockProduct.setCircle("delhi");

        CVRProductCache.getInstance().addProductDetails(mockProduct);

        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setProductId(5673244328L);
        request.setOperator("airtel");
        request.setCustomerId(13123434L);
        request.setRechargeNumber("32874372439");
        request.setService("mobile");

        LatestOrdersResponse response = new LatestOrdersResponse();
        List<LatestOrdersResponse> latestOrdersResponse = Collections.singletonList(response);

        List<SuggestedCard> result = suggestedCardService.createSuggestedCardForMissingRecents(latestOrdersResponse, request);

        assertEquals(0, result.size());
//        assertTrue(result.stream().anyMatch(card -> Constants.DATA_PACK.equals(card.getPlanBucket())));
//        assertTrue(result.stream().anyMatch(card -> Constants.SPECIAL_RECHARGE.equals(card.getPlanBucket())));
//        assertTrue(result.stream().allMatch(card -> card.getAmount() != null));
    }

    @Test
    public void createSuggestedCardForMissingRecentsWhenProductIdIsNotPresentInRequest() {
        when(serviceConfig.isSuggestedCardEnabled()).thenReturn(true);

        Product mockProduct = new Product();
        mockProduct.setProductId(1234L);
        mockProduct.setCircle("delhi");

        CVRProductCache.getInstance().addProductDetails(mockProduct);

        LatestOrdersRequest request = new LatestOrdersRequest();
        request.setOperator("airtel");
        request.setCustomerId(13123434L);
        request.setRechargeNumber("32874372439");
        request.setService("mobile");

        LatestOrdersResponse response = new LatestOrdersResponse();
        List<LatestOrdersResponse> latestOrdersResponse = Collections.singletonList(response);

        List<SuggestedCard> result = suggestedCardService.createSuggestedCardForMissingRecents(latestOrdersResponse, request);

        assertEquals(0, result.size());
//        assertTrue(result.stream().anyMatch(card -> Constants.DATA_PACK.equals(card.getPlanBucket())));
//        assertTrue(result.stream().anyMatch(card -> Constants.SPECIAL_RECHARGE.equals(card.getPlanBucket())));
//        assertTrue(result.stream().allMatch(card -> card.getAmount() != null));
    }
}