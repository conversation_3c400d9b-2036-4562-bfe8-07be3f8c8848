package com.paytm.saga.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.RecentUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.kafka.common.protocol.types.Field;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SmartReminderServiceImplTest {


    @InjectMocks
    private SmartReminderServiceImpl smartReminderService;

    @Mock
    private CustomerBillRepository customerBillRepository;

    @Mock
    StatsDClient monitoringClient;

    @Mock
    FeatureConfigCache featureConfigCache;

    @Mock
    ServiceConfig serviceConfig;

    @Before
    public void setUp() {

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }
    private ObjectMapper objectMapper = new ObjectMapper();


    @Test
    public void testGetClusterKeys() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        String response = "[{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"8052270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}},{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"7777270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}},{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"44444270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}}]";
        List<CustomerBill> customerBills = objectMapper.readValue(response, new TypeReference<List<CustomerBill>>() {
        });
        when(customerBillRepository.findByCustomerIdAndDueDate(any(), any(), any()))
                .thenReturn(customerBills);
        List<RecentsPrimaryKey> recentsPrimaryKeys = smartReminderService.getFilteredRecentTableClusterKeys(request);
        assertEquals(recentsPrimaryKeys.size(),
                3);
        assertEquals(recentsPrimaryKeys.get(0).getService(),
                "electricity");

    }

    @Test
    public void testGetClusterKeysUsingRechargeNumberFilter() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setRechargeNumber("8052270003");
        request.setOnlyReminder(Boolean.TRUE);
        String response = "[{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"8052270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}},{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"7777270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}},{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"44444270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}}]";
        List<CustomerBill> customerBills = objectMapper.readValue(response, new TypeReference<List<CustomerBill>>() {
        });
        when(customerBillRepository.findByCustomerIdAndDueDate(any(), any(), any()))
                .thenReturn(customerBills);
        List<RecentsPrimaryKey> recentsPrimaryKeys = smartReminderService.getFilteredRecentTableClusterKeys(request);
        assertEquals(recentsPrimaryKeys.size(),
                1);

    }

    @Test
    public void testGetClusterKeysUsingServiceFilter() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setServices(Arrays.asList("electricity"));
        request.setOnlyReminder(Boolean.TRUE);
        String response = "[{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"8052270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}},{\"key\":{\"customerId\":1107199327, \"service\":\"mobile\", \"rechargeNumber\":\"7777270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}},{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"44444270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}}]";
        List<CustomerBill> customerBills = objectMapper.readValue(response, new TypeReference<List<CustomerBill>>() {
        });
        when(customerBillRepository.findByCustomerIdAndDueDate(any(), any(), any()))
                .thenReturn(customerBills);
        List<RecentsPrimaryKey> recentsPrimaryKeys = smartReminderService.getFilteredRecentTableClusterKeys(request);
        assertEquals(recentsPrimaryKeys.size(),
                2);

    }

    @Test
    public void testGetClusterKeysUsingServiceOperatorRechargeNumberFilter() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setServices(Arrays.asList("electricity"));
        request.setOperator("tata power");
        request.setRechargeNumber("8052270003");
        request.setOnlyReminder(Boolean.TRUE);
        String response = "[{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"8052270003\", \"operator\":\"tata power\"}},{\"key\":{\"customerId\":1107199327, \"service\":\"mobile\", \"rechargeNumber\":\"7777270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}},{\"key\":{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"44444270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\"}}]";
        List<CustomerBill> customerBills = objectMapper.readValue(response, new TypeReference<List<CustomerBill>>() {
        });
        when(customerBillRepository.findByCustomerIdAndDueDate(any(), any(),any()))
                .thenReturn(customerBills);
        List<RecentsPrimaryKey> recentsPrimaryKeys = smartReminderService.getFilteredRecentTableClusterKeys(request);
        assertEquals(recentsPrimaryKeys.size(),
                1);

    }
    @Test
    public void testDueDateRange() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);
        when(serviceConfig.getBillVisiblityDays()).thenReturn(5);

        List<RecentsPrimaryKey> recentsPrimaryKeys = smartReminderService.getFilteredRecentTableClusterKeys(request);

        ArgumentCaptor<Long> custId =  ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Date> dueDateStart  =  ArgumentCaptor.forClass(Date.class);
        //ArgumentCaptor<Date> dueDateEnd =  ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> limit = ArgumentCaptor.forClass(Integer.class);
        verify(customerBillRepository).findByCustomerIdAndDueDate(custId.capture(),dueDateStart.capture(),limit.capture());
        assertEquals(DateUtil.getZeroTimeDate(DateUtil.addDays(new Date(),-5)),dueDateStart.getValue());
        //assertEquals(DateUtil.getZeroTimeDate(DateUtil.addDays(new Date(),+30)),dueDateEnd.getValue());


    }
}
