package com.paytm.saga.service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.timgroup.statsd.StatsDClient;
import junit.framework.TestCase;
import org.json.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.logging.Logger;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class FrequentOrderServiceImplTest extends TestCase {
    @InjectMocks
    private FrequentOrderServiceImplConcrete frequentOrderServiceImplConcrete;

    @Mock
    private UserAgentService userAgentService;

    @Mock
    private Logger logger;

    @Mock
    private StatsDClient monitoringClient;

    @Mock
    private LocalisationManager localisationManager;

    @Mock
    private ServiceConfig serviceConfig;

    @Mock
    private RecentsService recentsService;

    @Test
    public void testGetFrequentOrderWrapper_when_userIsAgent() throws RechargeSagaBaseException {
        FrequentOrderRequest request = new FrequentOrderRequest();

        when(userAgentService.isAgent(any())).thenReturn(true);

        FrequentOrderResponseWrapper result = frequentOrderServiceImplConcrete.getFrequentOrderWrapper(request);
        assertEquals(true, result.isAgent());
    }
    @Test
    public void testGetFrequentOrderWrapper() throws RechargeSagaBaseException {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setMethodType(Constants.METHOD_POST);
        List<Recents> recents = new ArrayList<>();
        Map<String, DropOffResponse> dropOffMap = new HashMap<>();
        RecentConfig config = new RecentConfig();

        when(userAgentService.isAgent(any())).thenReturn(false);

        FrequentOrderResponseWrapper result = frequentOrderServiceImplConcrete.getFrequentOrderWrapper(request);
        //verify(fastagConfig, times(2)).getFrequentOrderWaitTime();
        verify(monitoringClient, times(5)).count(anyString(), anyLong(), anyString());
    }

    @Test
    public void isValidationRecentWithExpiredValidity_returnsTrue_whenValidityExpired() {
        Recents recent = new Recents();
        recent.setIsValidation(true);
        recent.setExtra(new JSONObject().put("isValidityExpired", true).toString());

        boolean result = frequentOrderServiceImplConcrete.isValidationRecentWithExpiredValidity(recent);

        assertEquals(true, result);
    }

    @Test
    public void isValidationRecentWithExpiredValidity_returnsFalse_whenValidityNotExpired() {
        Recents recent = new Recents();
        recent.setIsValidation(true);
        recent.setExtra("{\"isValidityExpired\": false}");

        boolean result = frequentOrderServiceImplConcrete.isValidationRecentWithExpiredValidity(recent);

        assertFalse(result);
    }

    @Test
    public void isValidationRecentWithExpiredValidity_returnsFalse_whenExtraIsNull() {
        Recents recent = new Recents();
        recent.setIsValidation(true);
        recent.setExtra(null);

        boolean result = frequentOrderServiceImplConcrete.isValidationRecentWithExpiredValidity(recent);

        assertFalse(result);
    }

    @Test
    public void isValidationRecentWithExpiredValidity_returnsFalse_whenIsValidationIsFalse() {
        Recents recent = new Recents();
        recent.setIsValidation(false);
        recent.setExtra(new JSONObject().put("isValidityExpired", true).toString());

        boolean result = frequentOrderServiceImplConcrete.isValidationRecentWithExpiredValidity(recent);

        assertFalse(result);
    }

    @Test
    public void isValidationRecentWithExpiredValidity_returnsFalse_whenValidityExpiredKeyIsMissing() {
        Recents recent = new Recents();
        recent.setIsValidation(true);
        recent.setExtra("{\"OTHER_KEY\": true}");

        boolean result = frequentOrderServiceImplConcrete.isValidationRecentWithExpiredValidity(recent);

        assertFalse(result);
    }

    private static class FrequentOrderServiceImplConcrete extends FrequentOrderServiceImpl {
        @Override
        public FrequentOrderResponseWrapper getFrequentOrderWrapper(FrequentOrderRequest request) throws RechargeSagaBaseException {
            return super.getFrequentOrderWrapper(request);
        }

        @Override
        protected void updateSortedRecentsAndDropoff(List<Recents> recents, Map<String, DropOffResponse> dropOffMap, FrequentOrderRequest request, FrequentOrderResponseWrapper wrapper) throws RechargeSagaBaseException {
            recents.add(0, getRecentsInstance());
            Recents recentsInstance2 = getRecentsInstance();
            recentsInstance2.getKey().setService("slowtag recharge");
        }

        @Override
        protected List<Recents> getRecentDataFromDB(FrequentOrderRequest request) {
            return null;
        }

        @Override
        protected void handleFastagRechargeNumber(List<Recents> recents) {

        }

        @Override
        protected void revertFastagRechargeNumber(List<Recents> recents, Map<String, Recents> filteredRecents, Map<String, Recents> txnStatus, Map<String, Recents> txnStatusForNonRU, RecentConfig config) {

        }

        @Override
        protected boolean skipBasedOnCategory(Map<Long, Integer> categoryWiseCount, Long productId) {
            return false;
        }

        @Override
        protected void addToFavResponse(List<FrequentOrderResponse> favResponse, FrequentOrderResponse response, Map<Long, Integer> categoryWiseMap) {

        }

        @Override
        protected void updateWidgetSpecificFields(Recents recent) {

        }

        @Override
        protected String getUniqueKey(Recents recent, RecentConfig config) {
            StringBuilder key = new StringBuilder();
            if (recent.getKey().getService().equalsIgnoreCase("electricity") && 

                serviceConfig.getWhielistOperatorServicesForDedup().contains(recent.getKey().getOperator().toLowerCase())) {
                // For electricity bills with whitelisted operators, use recharge number and service for deduplication
                key.append(recent.getKey().getRechargeNumber())
                   .append(Constants.Delimiters.UNDERSCORE)
                   .append(recent.getKey().getService());
            } else {
                // For other cases, include operator in the key to prevent deduplication
                key.append(recent.getKey().getRechargeNumber())
                   .append(Constants.Delimiters.UNDERSCORE)
                   .append(recent.getKey().getService())
                   .append(Constants.Delimiters.UNDERSCORE)
                   .append(recent.getKey().getOperator());
            }
            return key.toString();
        }

        @Override
        protected boolean isSkippable(Recents recent, FrequentOrderRequest request) {
            return false;
        }

        @Override
        protected void addSmsCardToFavResponse(List<FrequentOrderResponse> favResponse, FrequentOrderResponse response) {

        }
    }


    private static Recents getRecentsInstance() {
        Recents recents = new Recents();
        recents.setProductId(467017231l);
        recents.setPayType("prepaid");
        recents.setUpdatedAt(new Date());
        recents.setOperator("idfc first bank");

        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1343928637l);
        recentsPrimaryKey.setService("fastag recharge");
        recentsPrimaryKey.setRechargeNumber("tn11t6276");
        recentsPrimaryKey.setOperator("idfc first bank");
        recentsPrimaryKey.setPlanBucket("1343928637l");
        recents.setKey(recentsPrimaryKey);
        recents.setIsValidation(true);
        recents.setCreatedAt(new Date());
        return recents;
    }

    @Test
    public void testGetFilteredRecents_WithDuplicateElectricityCards() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);

        List<Recents> recents = new ArrayList<>();
        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRU = new HashMap<>();
        RecentConfig config = new RecentConfig();

        // Create first electricity card
        Recents recent1 = new Recents();
        recent1.setProductId(1234L);
        RecentsPrimaryKey key1 = new RecentsPrimaryKey();
        key1.setService("electricity");
        key1.setOperator("msedcl");
        key1.setRechargeNumber("*********");
        recent1.setKey(key1);
        recent1.setBillDate(DateUtil.stringToDate("2024-01-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent1.setDueDate(DateUtil.stringToDate("2024-01-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent1.setBillUpdateTime(DateUtil.stringToDate("2024-01-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent1.setDueAmount(1000.0);
        recent1.setUpdatedAt(new Date());
        recents.add(recent1);

        // Create second electricity card with same recharge number but newer bill date
        Recents recent2 = new Recents();
        recent2.setProductId(1234L);
        RecentsPrimaryKey key2 = new RecentsPrimaryKey();
        key2.setService("electricity");
        key2.setOperator("msedcl");
        key2.setRechargeNumber("0*********");
        recent2.setKey(key2);
        recent2.setBillDate(DateUtil.stringToDate("2024-02-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent2.setDueDate(DateUtil.stringToDate("2024-02-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent2.setBillUpdateTime(DateUtil.stringToDate("2024-02-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent2.setDueAmount(2000.0);
        recent2.setUpdatedAt(new Date());
        recents.add(recent2);

        when(serviceConfig.getWhielistOperatorServicesForDedup()).thenReturn(Arrays.asList("msedcl"));

        Map<String, Recents> filteredRecents = frequentOrderServiceImplConcrete.getFilteredRecents(recents, config, request, txnStatus, txnStatusForNonRU);

        // Verify that cards were merged and newer bill details were kept
        assertEquals(1, filteredRecents.size());
        Recents mergedCard = filteredRecents.values().iterator().next();
        assertEquals(recent2.getBillDate(), mergedCard.getBillDate());
        assertEquals(recent2.getDueAmount(), mergedCard.getDueAmount(), 0.01);
    }


    @Test
    public void testGetFilteredRecents_WithDuplicateElectricityCards_DifferentOperators() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);

        List<Recents> recents = new ArrayList<>();
        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRU = new HashMap<>();
        RecentConfig config = new RecentConfig();

        // Create first electricity card for MSEDCL
        Recents recent1 = new Recents();
        recent1.setProductId(1234L);
        RecentsPrimaryKey key1 = new RecentsPrimaryKey();
        key1.setService("electricity");
        key1.setOperator("msedcl");
        key1.setRechargeNumber("*********");
        recent1.setKey(key1);
        recent1.setBillDate(DateUtil.stringToDate("2024-01-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent1.setDueDate(DateUtil.stringToDate("2024-01-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent1.setBillUpdateTime(DateUtil.stringToDate("2024-01-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent1.setDueAmount(1000.0);
        recent1.setUpdatedAt(new Date());
        recents.add(recent1);

        // Create second electricity card for TATA Power
        Recents recent2 = new Recents();
        recent2.setProductId(1235L);
        RecentsPrimaryKey key2 = new RecentsPrimaryKey();
        key2.setService("electricity");
        key2.setOperator("tata");
        key2.setRechargeNumber("0*********");
        recent2.setKey(key2);
        recent2.setBillDate(DateUtil.stringToDate("2024-02-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent2.setDueDate(DateUtil.stringToDate("2024-02-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent2.setBillUpdateTime(DateUtil.stringToDate("2024-02-01 10:00:00", DateFormats.DATE_TIME_FORMAT_2));
        recent2.setDueAmount(2000.0);
        recent2.setUpdatedAt(new Date());
        recents.add(recent2);

        when(serviceConfig.getWhielistOperatorServicesForDedup()).thenReturn(Arrays.asList("msedcl", "tata"));

        Map<String, Recents> filteredRecents = frequentOrderServiceImplConcrete.getFilteredRecents(recents, config, request, txnStatus, txnStatusForNonRU);

        // Verify that cards were not merged since they have different operators
        assertEquals(2, filteredRecents.size());
    }


}