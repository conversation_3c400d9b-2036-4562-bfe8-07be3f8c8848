package com.paytm.saga.service;

import com.paytm.saga.common.configuration.property.RemovePgTokenKafkaProducerConfig;
import com.paytm.saga.dto.RemovePgTokenKafkaRequest;
import com.paytm.saga.dto.RemovePgTokenResponse;
import com.paytm.saga.producers.RemovePgTokenKafkaProducer;
import com.timgroup.statsd.StatsDClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.core.KafkaTemplate;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class RemovePgTokenServiceTest {

    @Mock
    private RemovePgTokenKafkaProducer mockProducer;

    @Mock
    private StatsDClient mockStatsDClient;

    @Mock
    private KafkaTemplate<String, String> removePgTokenKafkaTemplate;

    @Mock
    private RemovePgTokenKafkaProducerConfig removePgTokenKafkaProducerConfig;

    @InjectMocks
    private RemovePgTokenService service;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testPushTopicToKafka_Success() {
        RemovePgTokenKafkaRequest request = new RemovePgTokenKafkaRequest();
        RemovePgTokenResponse actualResponse = service.pushTopicToKafka(request);
        assertEquals("SUCCESS", actualResponse.getMessage());
    }

}
