package com.paytm.saga.service;

import static org.mockito.Mockito.when;

import java.text.DateFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;


import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.DropOffResponse;
import com.paytm.saga.listeners.DropOffDBHelper;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.repository.DropOffRepository;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;

import junit.framework.TestCase;
import org.springframework.data.cassandra.core.CassandraTemplate;

@RunWith(MockitoJUnitRunner.class)
public class DropOffServiceTest extends TestCase {


    @Mock
    PlanExpiryHistoryRepository planExpiryHistoryRepository;

    @Mock
    DropOffRepository dropOffRepository;
    
    @Mock
	DropOffDBHelper dropOffDBHelper;

    @Mock
	ReminderHistoryRepositoryWrapperService reminderHistoryRepository;

    @InjectMocks
    DropOffService dropOffService;

    @InjectMocks
    ReminderHistoryService reminderHistoryService;

    @InjectMocks
    PlanExpiryHistoryService planExpiryHistoryService;

    @Mock
    CassandraTemplate template;


    DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date date1 = new Date(), date2 = new Date(), date3 =new Date(), date4 = new Date(), date5 = new Date(), date_now_minus_30_days = new Date();

    @Before
    public void setup() {
        dropOffService = new DropOffService(dropOffRepository, planExpiryHistoryService, reminderHistoryService,dropOffDBHelper);
        MockitoAnnotations.initMocks(this);

        date1 = DateUtils.addHours(new Date(),-12);
        date2 = DateUtils.addHours(new Date(),-24);
        date3 = DateUtils.addHours(new Date(),-36);
        date4 = DateUtils.addHours(new Date(),-48);
        date5 = DateUtils.addHours(new Date(),-60);

        date_now_minus_30_days = DateUtils.addDays(new Date(),-30);
    }

    private DropOff getDropOffMockDataValidation(){
        Map<String, String> billsObj = new HashMap<String, String>() {};
        billsObj.put("plan_bucket", "Top Up");
        billsObj.put("channel", "WEB 2");

        DropOff d = new DropOff();
        d.setCustomerId(1L);
        d.setAmount(String.valueOf(10.0));
        d.setCircle("delhi-ncr");
        d.setService("mobile");
        d.setPaytype("prepaid");
        d.setEventType("VALIDATION");
        d.setRechargeNumber("1122");
        d.setCategoryId(1L);
        d.setTransactionTime(new Date());
        d.setOrderId(2L);
        d.setProductId(3L);
        d.setOperator("airtel");
        d.setStatus("7");
        d.setIn_response_code("00");
        d.setPayment_status("1");
        d.setDisplayValues(null);
        d.setBillsObj(billsObj);
        d.setItemId(4L);
        return d;
    }

    private DropOff getDropOffMockDataValidationForCC(){
        Map<String, String> billsObj = new HashMap<String, String>() {};

        DropOff d = new DropOff();
        d.setCustomerId(1L);
        d.setAmount(String.valueOf(10.0));
        d.setCircle("delhi-ncr");
        d.setService("financial services");
        d.setPaytype("credit card");
        d.setEventType("VALIDATION");
        d.setRechargeNumber("1122");
        d.setCategoryId(1L);
        d.setTransactionTime(new Date());
        d.setOrderId(2L);
        d.setProductId(3L);
        d.setOperator("visa_sbi");
        d.setStatus("7");
        d.setIn_response_code("00");
        d.setPayment_status("1");
        d.setDisplayValues(null);
        d.setBillsObj(billsObj);
        d.setItemId(4L);
        return d;
    }

    private DropOff getDropOffMockDataRecharge(){
        Map<String, String> billsObj = new HashMap<String, String>() {};
        billsObj.put("plan_bucket", "Top Up");
        billsObj.put("channel", "WEB 2");

        DropOff d = new DropOff();
        d.setCustomerId(1L);
        d.setAmount(String.valueOf(10.0));
        d.setCircle("delhi-ncr");
        d.setService("mobile");
        d.setPaytype("prepaid");
        d.setEventType("RECHARGE");
        d.setRechargeNumber("1122");
        d.setCategoryId(1L);
        d.setTransactionTime(new Date());
        d.setOrderId(2L);
        d.setProductId(3L);
        d.setOperator("airtel");
        d.setStatus("7");
        d.setIn_response_code("00");
        d.setPayment_status("1");
        d.setDisplayValues(null);
        d.setBillsObj(billsObj);
        d.setItemId(4L);
        return d;
    }


    private PlanExpiryHistory getPlanExipryMockData(){
        PlanExpiryHistory p = new PlanExpiryHistory();
        p.setRechargeNumber("1122");
        p.setService("mobile");
        p.setCircle("delhi-ncr");
        p.setOperator("airtel");
        p.setPlan_bucket("Top Up");
        p.setUpdated_at(new Date());
        p.setCustomerid(1L);
        p.setValidity_expiry_date(new Date());
        p.setCreated_at(new Date());
        p.setAmount(20.0);

        return p;
    }

    private ReminderHistory getReminderHistoryMockData(){
        ReminderHistory r = new ReminderHistory();
        r.setRechargeNumber("1122");
        r.setService("mobile");
        r.setCircle("delhi-ncr");
        r.setOperator("airtel");
        r.setCreated_at(new Date());
        r.setAmount(20.0);
        r.setCustomerId(1L);
        r.setPaytype("postpaid");
        r.setBill_date(date1);
        r.setDue_date(date1);
        r.setUpdatedAt(date1);
        return r;
    }

    private ReminderHistory getReminderHistoryMockDataForCC(){
        ReminderHistory r = new ReminderHistory();
        r.setRechargeNumber("1122");
        r.setService("financial services");
        r.setCircle("delhi-ncr");
        r.setOperator("visa_sbi");
        r.setCreated_at(new Date());
        r.setAmount(10.0);
        r.setCustomerId(1L);
        r.setPaytype("credit card");
        r.setBill_date(date1);
        r.setDue_date(date1);
        r.setUpdatedAt(date1);
        return r;
    }


    @Test
    public void testGetDropOffValidation() {

        List<DropOff> dropOffs = new ArrayList<DropOff>();
        DropOff d = getDropOffMockDataValidation();
        dropOffs.add(d);
        List<String> services = new ArrayList<String>();
        services.add("mobile");

        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals( "WEB 2", res.get("1122_mobile_airtel").getChannel());

    }

    /*
    @Test
    public void testGetDropOffValidationConsumerName() {

        List<DropOff> dropOffs = new ArrayList<DropOff>();
        DropOff d = getDropOffMockDataValidation();
        d.getBillsObj().put("consumerName", "N");
        dropOffs.add(d);
        List<String> services = new ArrayList<String>();
        services.add("mobile");

        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals("N" , res.get("1122_mobile_airtel").getConsumerName());


    }

    @Test
    public void testGetDropOffRechargePENDING() {

        List<DropOff> dropOffs = new ArrayList<DropOff>();
        DropOff d = getDropOffMockDataRecharge();
        d.setEventType("RECHARGE");
        d.setStatus("1");
        dropOffs.add(d);
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("PENDING", res.get("1122_mobile_airtel").getBills().get(0).getState());
        assertEquals("RECHARGE", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals("Top Up", res.get("1122_mobile_airtel").getBills().get(0).getPlan_bucket());
    }
    */



    @Test
    public void testGetDropOffRechargeFAILED() {

        List<DropOff> dropOffs = new ArrayList<DropOff>();
        DropOff d = getDropOffMockDataRecharge();
        d.setEventType("RECHARGE");
        d.setStatus("6");
        dropOffs.add(d);
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("FAILURE", res.get("1122_mobile_airtel").getBills().get(0).getState());
        assertEquals("RECHARGE", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals("Top Up", res.get("1122_mobile_airtel").getBills().get(0).getPlan_bucket());
        assertEquals( "WEB 2", res.get("1122_mobile_airtel").getChannel());
    }

    @Test
    public void testGetDropOffRechargeSuccess() {

        List<DropOff> dropOffs = new ArrayList<DropOff>();
        dropOffs.add(getDropOffMockDataRecharge());
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals(0, res.size());
    }
    
    @Test
    public void testGetDropOffCylinderAgencyName() {
    	DropOff dropOff=getDropOffMockDataRecharge();
    	dropOff.setService(Constants.CommonConstants.CYLINDER_SERVICE);
    	dropOff.setPaytype(Constants.CommonConstants.POSTPAID_PAYTYPE);
    	dropOff.setEventType("RECHARGE");
    	dropOff.setStatus("6");
    	dropOff.getBillsObj().put(Constants.CYLINDER_AGENCY_NAME, "AGENCY NAME");
        List<DropOff> dropOffs = new ArrayList<DropOff>();
        dropOffs.add(dropOff);
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add(Constants.CommonConstants.CYLINDER_SERVICE);

        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals(1, res.size());
        assertEquals("AGENCY NAME", res.get("1122_cylinder booking_airtel").getAdditionalData().getCylinderAgencyName());
    }

    @Test
    public void testGetDropOffRechargeNumberUpTo8() {
        DropOff dropOff = getDropOffMockDataRecharge();
        dropOff.setService(Constants.CommonConstants.CYLINDER_SERVICE);
        dropOff.setPaytype(Constants.CommonConstants.POSTPAID_PAYTYPE);
        dropOff.setEventType("RECHARGE");
        dropOff.setStatus("6");
        dropOff.setEventType("RECHARGE");
        dropOff.setRechargeNumber("6868786");
        dropOff.setRecharge_number_3("686789789");
        dropOff.setRecharge_number_4("698797698");
        dropOff.setRecharge_number_5("1167999811");
        dropOff.setRecharge_number_6("231243796");
        dropOff.setRecharge_number_7("1234354686");
        dropOff.setRecharge_number_8("475456546");
        dropOff.setRecharge_number_2("686789789");
        dropOff.getBillsObj().put(Constants.CYLINDER_AGENCY_NAME, "AGENCY NAME");
        List<DropOff> dropOffs = new ArrayList<DropOff>();
        dropOffs.add(dropOff);
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("6868786");
        services.add(Constants.CommonConstants.CYLINDER_SERVICE);
        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());

        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);
        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers, services)).thenReturn(plans);

        Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null);

        assertEquals(1, res.size());
        assertEquals("686789789", res.get("6868786_cylinder booking_airtel").getRecharge_number_2());
        assertEquals("686789789", res.get("6868786_cylinder booking_airtel").getRecharge_number_3());
        assertEquals("698797698", res.get("6868786_cylinder booking_airtel").getRecharge_number_4());
        assertEquals("1167999811", res.get("6868786_cylinder booking_airtel").getRecharge_number_5());
        assertEquals("231243796", res.get("6868786_cylinder booking_airtel").getRecharge_number_6());
        assertEquals("1234354686", res.get("6868786_cylinder booking_airtel").getRecharge_number_7());
        assertEquals("475456546", res.get("6868786_cylinder booking_airtel").getRecharge_number_8());
        assertEquals("AGENCY NAME",
                res.get("6868786_cylinder booking_airtel").getAdditionalData().getCylinderAgencyName());
    }

    @Test
    public void testGetDropOffRechargeSuccessMultipleBuckets_R1B1_R2B2_V1B1_V2B3() {


        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<DropOff> dropOffs = new ArrayList<DropOff>();

        DropOff d1 = getDropOffMockDataRecharge();
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setTransactionTime(date1);
        dropOffs.add(d1);

        DropOff d2 = getDropOffMockDataRecharge();
        d2.getBillsObj().put("plan_bucket", "B2");
        d2.setTransactionTime(date2);
        dropOffs.add(d2);

        DropOff d3 = getDropOffMockDataValidation();
        d3.getBillsObj().put("plan_bucket", "B1");
        d3.setTransactionTime(date3);
        dropOffs.add(d3);

        DropOff d4 = getDropOffMockDataValidation();
        d4.getBillsObj().put("plan_bucket", "B3");
        d4.setTransactionTime(date4);
        dropOffs.add(d4);


        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals("B3", res.get("1122_mobile_airtel").getBills().get(0).getPlan_bucket());
        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());

    }

    @Test
    public void testGetDropOffRechargeSuccessMultipleBuckets_R1B1_R2B2_V1B2_V2B1() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<DropOff> dropOffs = new ArrayList<DropOff>();

        DropOff d1 = getDropOffMockDataRecharge();
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setTransactionTime(date1);
        dropOffs.add(d1);

        DropOff d2 = getDropOffMockDataRecharge();
        d2.getBillsObj().put("plan_bucket", "B2");
        d2.setTransactionTime(date2);
        dropOffs.add(d2);

        DropOff d3 = getDropOffMockDataValidation();
        d3.getBillsObj().put("plan_bucket", "B2");
        d3.setTransactionTime(date3);
        dropOffs.add(d3);

        DropOff d4 = getDropOffMockDataValidation();
        d4.getBillsObj().put("plan_bucket", "B1");
        d4.setTransactionTime(date4);
        dropOffs.add(d4);


        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals(0, res.size());

    }

    @Test
    public void testGetDropOffRechargeSuccessMultipleBuckets_V1B1_R1B2_V2B2() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");


        List<DropOff> dropOffs = new ArrayList<DropOff>();

        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setTransactionTime(date1);
        d1.setCustomerId(1L);
        dropOffs.add(d1);

        DropOff d2 = getDropOffMockDataRecharge();
        d2.getBillsObj().put("plan_bucket", "B2");
        d2.setTransactionTime(date2);
        dropOffs.add(d2);

        DropOff d3 = getDropOffMockDataValidation();
        d3.getBillsObj().put("plan_bucket", "B2");
        d3.setTransactionTime(date3);
        dropOffs.add(d3);



        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals("B1", res.get("1122_mobile_airtel").getBills().get(0).getPlan_bucket());
        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());

    }

    @Test
    public void testGetDropOffRechargeSuccessMultipleBuckets_V1B1_V1B2_V3B3() {

        Date date1 = new Date(), date2 = new Date(), date3 =new Date(), date4 = new Date(), date5 = new Date();

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        try{
            date1 = dateformat.parse("2020-01-25'T'16:00:00.000000");
            date2 = dateformat.parse("2020-01-24'T'16:00:00.000000");
            date3 = dateformat.parse("2020-01-23'T'16:00:00.000000");
            date4 = dateformat.parse("2020-01-22'T'16:00:00.000000");
            date5 = dateformat.parse("2020-01-21'T'16:00:00.000000");

        } catch (Exception e){
        }

        List<DropOff> dropOffs = new ArrayList<DropOff>();

        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setTransactionTime(date1);
        d1.setCustomerId(1L);
        dropOffs.add(d1);

        DropOff d2 = getDropOffMockDataValidation();
        d2.getBillsObj().put("plan_bucket", "B2");
        d2.setTransactionTime(date2);
        dropOffs.add(d2);

        DropOff d3 = getDropOffMockDataValidation();
        d3.getBillsObj().put("plan_bucket", "B3");
        d3.setTransactionTime(date3);
        dropOffs.add(d3);



        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals("B1", res.get("1122_mobile_airtel").getBills().get(0).getPlan_bucket());
        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());

    }


    @Test
    public void testStampExpiry_ValidBucket_ValidExpiry() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();

        PlanExpiryHistory p2 = getPlanExipryMockData();
        p2.setUpdated_at(date2);
        p2.setValidity_expiry_date(date2);
        p2.setPlan_bucket("B1");
        plans.add(p2);

        PlanExpiryHistory p1 = getPlanExipryMockData();
        p1.setUpdated_at(date1);
        p1.setValidity_expiry_date(date1);
        p1.setPlan_bucket("B1");
        plans.add(p1);

        PlanExpiryHistory p3 = getPlanExipryMockData();
        p3.setUpdated_at(date3);
        p3.setValidity_expiry_date(date3);
        p2.setPlan_bucket("B1");
        plans.add(p3);

        PlanExpiryHistory p4 = getPlanExipryMockData();
        p4.setUpdated_at(date4);
        p4.setValidity_expiry_date(date4);
        p4.setPlan_bucket("B2");
        plans.add(p4);




        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "B1");

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Map<String , DropOff> res = dropOffService.stampExpiryForPrepaid(dropOffMap);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedExpiry = formatter.format(date1);


        assertEquals("B1", res.get("1122_mobile_airtel").getBillsObj().get("plan_bucket"));
        assertEquals(1, res.size());
        assertEquals("10.0", res.get("1122_mobile_airtel").getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getEventType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedExpiry , res.get("1122_mobile_airtel").getBillsObj().get("expiry"));


    }

    @Test
    public void testStampExpiry_DifferentBucketExpiryInDB() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();

        PlanExpiryHistory p2 = getPlanExipryMockData();
        p2.setUpdated_at(date2);
        p2.setValidity_expiry_date(date2);
        p2.setPlan_bucket("B3");
        plans.add(p2);

        PlanExpiryHistory p1 = getPlanExipryMockData();
        p1.setUpdated_at(date1);
        p1.setValidity_expiry_date(date1);
        p1.setPlan_bucket("B4");
        plans.add(p1);

        PlanExpiryHistory p3 = getPlanExipryMockData();
        p3.setUpdated_at(date3);
        p3.setValidity_expiry_date(date3);
        p2.setPlan_bucket("B5");
        plans.add(p3);

        PlanExpiryHistory p4 = getPlanExipryMockData();
        p4.setUpdated_at(date4);
        p4.setValidity_expiry_date(date4);
        p4.setPlan_bucket("B6");
        plans.add(p4);




        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "B1");

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Map<String , DropOff> res = dropOffService.stampExpiryForPrepaid(dropOffMap);

        assertEquals("B1", res.get("1122_mobile_airtel").getBillsObj().get("plan_bucket"));
        assertEquals(1, res.size());
        assertEquals("10.0", res.get("1122_mobile_airtel").getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getEventType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(null , res.get("1122_mobile_airtel").getBillsObj().get("expiry"));

    }

    @Test
    public void testFilterByOperatorFalse(){
        List<DropOff> dropOffs = new ArrayList<DropOff>();

        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setOperator("airtel");
        dropOffs.add(d1);

        Boolean res = dropOffService.filterByOperator(d1, "bsnl");
        assertEquals(new Boolean(false), res);

    }

    @Test
    public void testFilterByOperatorTrue(){
        List<DropOff> dropOffs = new ArrayList<DropOff>();

        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setOperator("airtel");
        dropOffs.add(d1);

        Boolean res = dropOffService.filterByOperator(d1, "airtel");
        assertEquals(new Boolean(true), res);

    }

    @Test
    public void testFilterByTimeStampTrue(){

        DropOff d1 = getDropOffMockDataValidation();
        d1.setTransactionTime(date1);

        Boolean res = dropOffService.filterByTimeStamp(d1);
        assertEquals(new Boolean(true), res);

    }

    @Test
    public void testFilterByTimeStampFalse(){

        DropOff d1 = getDropOffMockDataValidation();
        d1.setTransactionTime(date_now_minus_30_days);

        Boolean res = dropOffService.filterByTimeStamp(d1);
        assertEquals(new Boolean(false), res);

    }

    @Test
    public void testpostpaidCheckForPendingAmountTrue(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        reminderRecords.add(getReminderHistoryMockData());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals("postpaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedBillDate , res.get("1122_mobile_airtel").getBillsObj().get("bill_date"));
        assertEquals(expectedDueDate , res.get("1122_mobile_airtel").getBillsObj().get("due_date"));
        assertEquals("20.0" , res.get("1122_mobile_airtel").getBillsObj().get("reminder_amount"));
        assertEquals("VALIDATION" , res.get("1122_mobile_airtel").getEventType());

    }

    @Test
    public void testpostpaidCheckForPendingAmountZeroAmountRemaining(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        ReminderHistory record = getReminderHistoryMockData();
        record.setAmount(0.0);
        reminderRecords.add(record);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals(0, res.size());
    }

    @Test
    public void testpostpaidCheckForPendingAmountTrueMultipleReminderRecords(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        ReminderHistory record1 = getReminderHistoryMockData();
        ReminderHistory record2 = getReminderHistoryMockData();
        ReminderHistory record3 = getReminderHistoryMockData();
        ReminderHistory record4 = getReminderHistoryMockData();
        record2.setUpdatedAt(date3);
        record3.setUpdatedAt(date2);
        record4.setUpdatedAt(date4);

        reminderRecords.add(record2);
        reminderRecords.add(record3);
        reminderRecords.add(record4);
        reminderRecords.add(record1);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals("postpaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedBillDate , res.get("1122_mobile_airtel").getBillsObj().get("bill_date"));
        assertEquals(expectedDueDate , res.get("1122_mobile_airtel").getBillsObj().get("due_date"));
        assertEquals("20.0" , res.get("1122_mobile_airtel").getBillsObj().get("reminder_amount"));
        assertEquals("VALIDATION" , res.get("1122_mobile_airtel").getEventType());
    }

    @Test
    public void testpostpaidCheckForPendingAmountTrueMultipleReminderRecordsRemainingBill(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        ReminderHistory record1 = getReminderHistoryMockData();
        ReminderHistory record2 = getReminderHistoryMockData();
        ReminderHistory record3 = getReminderHistoryMockData();
        ReminderHistory record4 = getReminderHistoryMockData();
        record2.setUpdatedAt(date3);
        record3.setUpdatedAt(date2);
        record4.setUpdatedAt(date4);

        reminderRecords.add(record2);
        reminderRecords.add(record3);
        reminderRecords.add(record4);
        reminderRecords.add(record1);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        List<DropOff> dropOffs = new ArrayList<DropOff>();
        dropOffs.add(d1);

        when(dropOffService.findByCustomerIdAndRechargeNumberAndServiceIn(1L, "1122", services))
                .thenReturn(dropOffs);


        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L,services,"1122","airtel");

        assertEquals("postpaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedBillDate , res.get("1122_mobile_airtel").getBills().get(0).getBill_date());
        assertEquals(expectedDueDate , res.get("1122_mobile_airtel").getBills().get(0).getDue_date());
        assertEquals(20.0 , res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("VALIDATION" , res.get("1122_mobile_airtel").getType());
    }

    @Test
    public void testpostpaidCheckForPendingAmountTrueWithNullBillsObj(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        reminderRecords.add(getReminderHistoryMockData());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");
        d1.setBillsObj(null);

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals("postpaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedBillDate , res.get("1122_mobile_airtel").getBillsObj().get("bill_date"));
        assertEquals(expectedDueDate , res.get("1122_mobile_airtel").getBillsObj().get("due_date"));
        assertEquals("20.0" , res.get("1122_mobile_airtel").getBillsObj().get("reminder_amount"));
        assertEquals("VALIDATION" , res.get("1122_mobile_airtel").getEventType());

    }

    @Test
    public void testpostpaidCheckForPendingAmountTrueMultipleReminderRecordswithNullBillsObj(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        ReminderHistory record1 = getReminderHistoryMockData();
        ReminderHistory record2 = getReminderHistoryMockData();
        ReminderHistory record3 = getReminderHistoryMockData();
        ReminderHistory record4 = getReminderHistoryMockData();
        record2.setUpdatedAt(date3);
        record3.setUpdatedAt(date2);
        record4.setUpdatedAt(date4);

        reminderRecords.add(record2);
        reminderRecords.add(record3);
        reminderRecords.add(record4);
        reminderRecords.add(record1);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");
        d1.setBillsObj(null);


        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals("postpaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedBillDate , res.get("1122_mobile_airtel").getBillsObj().get("bill_date"));
        assertEquals(expectedDueDate , res.get("1122_mobile_airtel").getBillsObj().get("due_date"));
        assertEquals("20.0" , res.get("1122_mobile_airtel").getBillsObj().get("reminder_amount"));
        assertEquals("VALIDATION" , res.get("1122_mobile_airtel").getEventType());
    }


    @Test
    public void testpostpaidCheckForPendingAmountTrueMultipleReminderRecordsRemainingBillForChat(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        ReminderHistory record1 = getReminderHistoryMockData();
        ReminderHistory record2 = getReminderHistoryMockData();
        ReminderHistory record3 = getReminderHistoryMockData();
        ReminderHistory record4 = getReminderHistoryMockData();
        record2.setUpdatedAt(date3);
        record3.setUpdatedAt(date2);
        record4.setUpdatedAt(date4);

        reminderRecords.add(record2);
        reminderRecords.add(record3);
        reminderRecords.add(record4);
        reminderRecords.add(record1);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        List<DropOff> dropOffs = new ArrayList<DropOff>();
        dropOffs.add(d1);

        when(dropOffService.findByCustomerIdAndRechargeNumberAndServiceIn(1L, "1122", services))
                .thenReturn(dropOffs);


        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        DropOff res = dropOffService.getDropOff(1L,"1122", "mobile", "airtel");

        assertEquals("postpaid" , res.getPaytype());
        assertEquals(expectedBillDate , res.getBillsObj().get("bill_date"));
        assertEquals(expectedDueDate , res.getBillsObj().get("due_date"));
        assertEquals("20.0" , res.getAmount());
        assertEquals("VALIDATION" , res.getEventType());
    }

    @Test
    public void testpostpaidCheckForPendingAmountTrueMultipleReminderRecordsRemainingBillForChatForCC(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("financial services");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        ReminderHistory record1 = getReminderHistoryMockDataForCC();
        ReminderHistory record2 = getReminderHistoryMockDataForCC();
        ReminderHistory record3 = getReminderHistoryMockDataForCC();
        ReminderHistory record4 = getReminderHistoryMockDataForCC();
        record2.setUpdatedAt(date3);
        record3.setUpdatedAt(date2);
        record4.setUpdatedAt(date4);

        reminderRecords.add(record2);
        reminderRecords.add(record3);
        reminderRecords.add(record4);
        reminderRecords.add(record1);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidationForCC();
        d1.setPaytype("credit card");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_financial services_ccbp", d1);

        List<DropOff> dropOffs = new ArrayList<DropOff>();
        dropOffs.add(d1);

        when(dropOffService.findByCustomerIdAndRechargeNumberAndServiceIn(1L, "1122", services))
                .thenReturn(dropOffs);


        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        DropOff res = dropOffService.getDropOff(1L,"1122", "financial services", "visa_sbi");

        assertEquals("credit card" , res.getPaytype());
        assertEquals("10.0" , res.getAmount());
        assertEquals("VALIDATION" , res.getEventType());
    }


    @Test
    public void testGetDropOffRechargeSuccessMultipleBuckets_With_DONOT_REMINDER_ME_RDNM_V1B1_R1B2_V2B2() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");


        List<DropOff> dropOffs = new ArrayList<DropOff>();

        DropOff d4 = getDropOffMockDataRecharge();
        d4.getBillsObj().put(Constants.CommonConstants.DONOT_REMINDME_FLAG, "true");
        d4.setTransactionTime(date4);
        dropOffs.add(d4);


        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setTransactionTime(date1);
        d1.setCustomerId(1L);
        dropOffs.add(d1);

        DropOff d2 = getDropOffMockDataRecharge();
        d2.getBillsObj().put("plan_bucket", "B2");
        d2.setTransactionTime(date2);
        dropOffs.add(d2);

        DropOff d3 = getDropOffMockDataValidation();
        d3.getBillsObj().put("plan_bucket", "B2");
        d3.setTransactionTime(date3);
        dropOffs.add(d3);



        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals(res.size(), 0);
    }


    @Test
    public void testGetDropOffRechargeSuccessMultipleBuckets_With_DONOT_REMINDER_ME_V1B1_RDNM_R1B2_V2B2_V3B5() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");


        List<DropOff> dropOffs = new ArrayList<DropOff>();


        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setTransactionTime(date1);
        d1.setCustomerId(1L);
        dropOffs.add(d1);

        DropOff d4 = getDropOffMockDataRecharge();
        d4.getBillsObj().put(Constants.CommonConstants.DONOT_REMINDME_FLAG, "true");
        d4.setTransactionTime(date4);
        dropOffs.add(d4);


        DropOff d2 = getDropOffMockDataRecharge();
        d2.getBillsObj().put("plan_bucket", "B2");
        d2.setTransactionTime(date2);
        dropOffs.add(d2);

        DropOff d3 = getDropOffMockDataValidation();
        d3.getBillsObj().put("plan_bucket", "B2");
        d3.setTransactionTime(date3);
        dropOffs.add(d3);

        DropOff d5 = getDropOffMockDataValidation();
        d5.getBillsObj().put("plan_bucket", "B5");
        d5.setTransactionTime(date5);
        dropOffs.add(d3);




        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals("B1", res.get("1122_mobile_airtel").getBills().get(0).getPlan_bucket());
        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());

    }


    @Test
    public void testStampExpiry_DTH_WithBillsObj() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("dth");


        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();

        PlanExpiryHistory p2 = getPlanExipryMockData();
        p2.setUpdated_at(date2);
        p2.setValidity_expiry_date(date2);
        p2.setPlan_bucket(null);
        p2.setService("dth");
        p2.setOperator("airtel");

        plans.add(p2);


        PlanExpiryHistory p1 = getPlanExipryMockData();
        p1.setUpdated_at(date1);
        p1.setValidity_expiry_date(date1);
        p1.setPlan_bucket(null);
        p1.setService("dth");
        p1.setOperator("airtel");

        plans.add(p1);

        PlanExpiryHistory p3 = getPlanExipryMockData();
        p3.setUpdated_at(date3);
        p3.setValidity_expiry_date(date3);
        p3.setPlan_bucket("B");
        p3.setService("dth");
        p3.setOperator("airtel");

        plans.add(p3);

        PlanExpiryHistory p4 = getPlanExipryMockData();
        p4.setUpdated_at(date4);
        p4.setValidity_expiry_date(date4);
        p4.setPlan_bucket("A");
        p4.setService("dth");
        p4.setOperator("airtel");

        plans.add(p4);




        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("plan_bucket", "Q");
        d1.setService("dth");
        d1.setOperator("airtel");

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_dth_airtel", d1);

        Map<String , DropOff> res = dropOffService.stampExpiryForPrepaid(dropOffMap);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedExpiry = formatter.format(date1);


        assertEquals("Q", res.get("1122_dth_airtel").getBillsObj().get("plan_bucket"));
        assertEquals(1, res.size());
        assertEquals("10.0", res.get("1122_dth_airtel").getAmount());
        assertEquals("VALIDATION", res.get("1122_dth_airtel").getEventType());
        assertEquals("prepaid" , res.get("1122_dth_airtel").getPaytype());
        assertEquals(expectedExpiry , res.get("1122_dth_airtel").getBillsObj().get("expiry"));

    }

    @Test
    public void testStampExpiry_DTH_WithoutBillsObj() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("dth");


        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();

        PlanExpiryHistory p2 = getPlanExipryMockData();
        p2.setUpdated_at(date2);
        p2.setValidity_expiry_date(date2);
        p2.setPlan_bucket(null);
        p2.setService("dth");
        p2.setOperator("airtel");

        plans.add(p2);


        PlanExpiryHistory p1 = getPlanExipryMockData();
        p1.setUpdated_at(date1);
        p1.setValidity_expiry_date(date1);
        p1.setPlan_bucket(null);
        p1.setService("dth");
        p1.setOperator("airtel");

        plans.add(p1);

        PlanExpiryHistory p3 = getPlanExipryMockData();
        p3.setUpdated_at(date3);
        p3.setValidity_expiry_date(date3);
        p3.setPlan_bucket("B");
        p3.setService("dth");
        p3.setOperator("airtel");

        plans.add(p3);

        PlanExpiryHistory p4 = getPlanExipryMockData();
        p4.setUpdated_at(date4);
        p4.setValidity_expiry_date(date4);
        p4.setPlan_bucket("A");
        p4.setService("dth");
        p4.setOperator("airtel");

        plans.add(p4);




        DropOff d1 = getDropOffMockDataValidation();
        d1.setBillsObj(null);
        d1.setService("dth");
        d1.setOperator("airtel");

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_dth_airtel", d1);

        Map<String , DropOff> res = dropOffService.stampExpiryForPrepaid(dropOffMap);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedExpiry = formatter.format(date1);


        assertEquals(1, res.size());
        assertEquals("10.0", res.get("1122_dth_airtel").getAmount());
        assertEquals("VALIDATION", res.get("1122_dth_airtel").getEventType());
        assertEquals("prepaid" , res.get("1122_dth_airtel").getPaytype());
        assertEquals(expectedExpiry , res.get("1122_dth_airtel").getBillsObj().get("expiry"));

    }

    @Test
    public void testStampExpiry_DTH_WithoutBillsObjWithEmptyCircle() {

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("dth");


        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();

        PlanExpiryHistory p2 = getPlanExipryMockData();
        p2.setUpdated_at(date2);
        p2.setValidity_expiry_date(date2);
        p2.setPlan_bucket(null);
        p2.setService("dth");
        p2.setOperator("airtel");

        plans.add(p2);


        PlanExpiryHistory p1 = getPlanExipryMockData();
        p1.setUpdated_at(date1);
        p1.setValidity_expiry_date(date1);
        p1.setPlan_bucket(null);
        p1.setService("dth");
        p1.setOperator("airtel");

        plans.add(p1);

        PlanExpiryHistory p3 = getPlanExipryMockData();
        p3.setUpdated_at(date3);
        p3.setValidity_expiry_date(date3);
        p3.setPlan_bucket("B");
        p3.setService("dth");
        p3.setOperator("airtel");

        plans.add(p3);

        PlanExpiryHistory p4 = getPlanExipryMockData();
        p4.setUpdated_at(date4);
        p4.setValidity_expiry_date(date4);
        p4.setPlan_bucket("A");
        p4.setService("dth");
        p4.setOperator("airtel");

        plans.add(p4);




        DropOff d1 = getDropOffMockDataValidation();
        d1.setBillsObj(null);
        d1.setService("dth");
        d1.setOperator("airtel");
        d1.setCircle(null);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_dth_airtel", d1);

        Map<String , DropOff> res = dropOffService.stampExpiryForPrepaid(dropOffMap);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedExpiry = formatter.format(date1);


        assertEquals(1, res.size());
        assertEquals("10.0", res.get("1122_dth_airtel").getAmount());
        assertEquals("VALIDATION", res.get("1122_dth_airtel").getEventType());
        assertEquals("prepaid" , res.get("1122_dth_airtel").getPaytype());
        assertEquals(expectedExpiry , res.get("1122_dth_airtel").getBillsObj().get("expiry"));

    }

    @Test
    public void testGetDropOffwithAdditionalData() {

        Date date1 = new Date(), date2 = new Date(), date3 =new Date(), date4 = new Date(), date5 = new Date();

        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        try{
            date1 = dateformat.parse("2020-01-25'T'16:00:00.000000");
        } catch (Exception e){
        }

        List<DropOff> dropOffs = new ArrayList<DropOff>();

        DropOff d1 = getDropOffMockDataValidation();
        d1.getBillsObj().put("RMN", "909090909009");
        d1.getBillsObj().put("plan_bucket", "B1");
        d1.setTransactionTime(date1);
        d1.setCustomerId(1L);
        dropOffs.add(d1);

        List<PlanExpiryHistory> plans = new ArrayList<PlanExpiryHistory>();
        plans.add(getPlanExipryMockData());


        when(dropOffService.findByCustomerIdAndServiceIn(1L, services))
                .thenReturn(dropOffs);

        when(planExpiryHistoryService.findByRechargeNumberInAndServiceIn(rechNumbers,services)).thenReturn(plans);

        Map<String , DropOffResponse> res = dropOffService.getDropOffResponse(1L, services , null, null);

        assertEquals("B1", res.get("1122_mobile_airtel").getBills().get(0).getPlan_bucket());
        assertEquals(1, res.size());
        assertEquals(10.0, res.get("1122_mobile_airtel").getBills().get(0).getAmount());
        assertEquals("VALIDATION", res.get("1122_mobile_airtel").getType());
        assertEquals("prepaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals("909090909009", res.get("1122_mobile_airtel").getAdditionalData().getMobileNumber());

    }

    @Test
    public void testDropOffWithPaidOutsideStatus(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory=getReminderHistoryMockData();
        reminderHistory.setStatus(14);
        reminderHistory.setLastPaidAmount(Double.valueOf(100));
        reminderRecords.add(reminderHistory);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals("postpaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedBillDate , res.get("1122_mobile_airtel").getBillsObj().get("bill_date"));
        assertEquals(expectedDueDate , res.get("1122_mobile_airtel").getBillsObj().get("due_date"));
        assertEquals("20.0" , res.get("1122_mobile_airtel").getBillsObj().get("reminder_amount"));
        assertEquals("VALIDATION" , res.get("1122_mobile_airtel").getEventType());
        assertEquals(Constants.PAID_OUTSIDE_PAYTM,res.get("1122_mobile_airtel").getPaidOutside());
        assertEquals("100.0", res.get("1122_mobile_airtel").getBillsObj().get("lastPaidAmount"));

    }
    @Test
    public void testDropOffMapWhenIsAutomaticTrue(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        ReminderHistory record1 = getReminderHistoryMockData();
        record1.setIs_automatic(1);
        reminderRecords.add(record1);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals("postpaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedBillDate , res.get("1122_mobile_airtel").getBillsObj().get("bill_date"));
        assertEquals(expectedDueDate , res.get("1122_mobile_airtel").getBillsObj().get("due_date"));
        assertEquals("20.0" , res.get("1122_mobile_airtel").getBillsObj().get("reminder_amount"));
        assertEquals("VALIDATION" , res.get("1122_mobile_airtel").getEventType());
        assertEquals(Boolean.TRUE, res.get("1122_mobile_airtel").getIsAutomatic());
    }

    @Test
    public void testDropOffMapWhenIsAutomaticFalse(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("1122");
        services.add("mobile");

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        ReminderHistory record1 = getReminderHistoryMockData();
        record1.setIs_automatic(0);
        reminderRecords.add(record1);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("postpaid");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_mobile_airtel", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals("postpaid" , res.get("1122_mobile_airtel").getPaytype());
        assertEquals(expectedBillDate , res.get("1122_mobile_airtel").getBillsObj().get("bill_date"));
        assertEquals(expectedDueDate , res.get("1122_mobile_airtel").getBillsObj().get("due_date"));
        assertEquals("20.0" , res.get("1122_mobile_airtel").getBillsObj().get("reminder_amount"));
        assertEquals("VALIDATION" , res.get("1122_mobile_airtel").getEventType());
        assertEquals(Boolean.FALSE, res.get("1122_mobile_airtel").getIsAutomatic());
    }



}