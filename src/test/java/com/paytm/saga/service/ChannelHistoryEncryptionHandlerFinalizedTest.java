package com.paytm.saga.service;

import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.util.AESUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class ChannelHistoryEncryptionHandlerFinalizedTest {

	@Mock
	private AESUtil aesUtil;

	@InjectMocks
	private ChannelHistoryFinalizedEncryptionHandler channelHistoryFinalizedEncryptionHandler;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		when(aesUtil.encrypt("100")).thenReturn("encrypted100");
		when(aesUtil.encrypt("1234567890")).thenReturn("encrypted1234567890");
		when(aesUtil.encrypt("0987654321")).thenReturn("encrypted0987654321");
		when(aesUtil.encrypt("value1")).thenReturn("encryptedValue1");
		when(aesUtil.decrypt("encrypted100")).thenReturn("100");
		when(aesUtil.decrypt("encrypted1234567890")).thenReturn("1234567890");
		when(aesUtil.decrypt("encrypted0987654321")).thenReturn("0987654321");
		when(aesUtil.decrypt("encryptedValue1")).thenReturn("value1");
	}

	@Test
	public void testEncryptChannelHistoryFinalized() throws AES256Exception {
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		channelHistoryFinalized.setAmount("100");
		channelHistoryFinalized.setRechargeNumber("1234567890");
		channelHistoryFinalized.setRecharge_number_3("0987654321");
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("key1", "value1");
		channelHistoryFinalized.setBillsObj(billsObj);

		ChannelHistoryFinalized encryptedChannelHistoryFinalized = channelHistoryFinalizedEncryptionHandler.encryptChannelHistoryFinalized(channelHistoryFinalized);

		assertEquals("encrypted100", encryptedChannelHistoryFinalized.getAmount());
		assertEquals("encrypted1234567890", encryptedChannelHistoryFinalized.getRechargeNumber());
		assertEquals("encrypted0987654321", encryptedChannelHistoryFinalized.getRecharge_number_3());
		assertEquals("encryptedValue1", encryptedChannelHistoryFinalized.getBillsObj().get("key1"));
		assertEquals(1, encryptedChannelHistoryFinalized.getIsEncrypted().intValue());
	}

	@Test
	public void testDecryptChannelHistoryFinalizedSlice() throws AES256Exception {
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		channelHistoryFinalized.setAmount("encrypted100");
		channelHistoryFinalized.setRechargeNumber("encrypted1234567890");
		channelHistoryFinalized.setRecharge_number_3("encrypted0987654321");
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("key1", "encryptedValue1");
		channelHistoryFinalized.setBillsObj(billsObj);
		channelHistoryFinalized.setIsEncrypted(1);

		Slice<ChannelHistoryFinalized> channelHistoryFinalizedList = new SliceImpl<>(new ArrayList<>(Collections.singletonList(channelHistoryFinalized)));

		channelHistoryFinalizedList = channelHistoryFinalizedEncryptionHandler.decryptChannelHistoryFinalizedSlice(channelHistoryFinalizedList);

		ChannelHistoryFinalized channelHistoryFinalizedDecrypted = channelHistoryFinalizedList.getContent().get(0);
		assertEquals("100", channelHistoryFinalizedDecrypted.getAmount());
		assertEquals("1234567890", channelHistoryFinalizedDecrypted.getRechargeNumber());
		assertEquals("0987654321", channelHistoryFinalizedDecrypted.getRecharge_number_3());
		assertEquals("value1", channelHistoryFinalizedDecrypted.getBillsObj().get("key1"));
		assertEquals(0, channelHistoryFinalizedDecrypted.getIsEncrypted().intValue());
	}

	@Test
	public void testDecryptChannelHistoryFinalizedList() throws AES256Exception {
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		channelHistoryFinalized.setAmount("encrypted100");
		channelHistoryFinalized.setRechargeNumber("encrypted1234567890");
		channelHistoryFinalized.setRecharge_number_3("encrypted0987654321");
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("key1", "encryptedValue1");
		channelHistoryFinalized.setBillsObj(billsObj);
		channelHistoryFinalized.setIsEncrypted(1);

		List<ChannelHistoryFinalized> channelHistoryFinalizedList = new ArrayList<>(Collections.singletonList(channelHistoryFinalized));

		channelHistoryFinalizedList = channelHistoryFinalizedEncryptionHandler.decryptChannelHistoryFinalizedList(channelHistoryFinalizedList);

		ChannelHistoryFinalized channelHistoryFinalizedDecrypted = channelHistoryFinalizedList.get(0);
		assertEquals("100", channelHistoryFinalizedDecrypted.getAmount());
		assertEquals("1234567890", channelHistoryFinalizedDecrypted.getRechargeNumber());
		assertEquals("0987654321", channelHistoryFinalizedDecrypted.getRecharge_number_3());
		assertEquals("value1", channelHistoryFinalizedDecrypted.getBillsObj().get("key1"));
		assertEquals(0, channelHistoryFinalizedDecrypted.getIsEncrypted().intValue());
	}

}
