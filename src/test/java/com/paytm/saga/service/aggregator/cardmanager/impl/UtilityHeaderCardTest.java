package com.paytm.saga.service.aggregator.cardmanager.impl;

import com.paytm.saga.common.constant.*;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.service.aggregator.viewmanager.childCta.impl.UtilityChildCTAManager;
import com.paytm.saga.service.aggregator.viewmanager.cta.impl.UtilityCTAManager;
import com.paytm.saga.service.aggregator.viewmanager.display.impl.UtilityDisplayViewManager;
import com.paytm.saga.service.aggregator.viewmanager.impl.UtilityHeaderManager;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

public class UtilityHeaderCardTest {

    @InjectMocks
    private UtilityHeaderManager utilityHeaderManager;

    @InjectMocks
    private UtilityCTAManager utilityCTAManager;

    @InjectMocks
    private UtilityChildCTAManager utilityChildCTAManager;

    @InjectMocks
    private UtilityDisplayViewManager utilityDisplayViewManager;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void newBillTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_FAILED).setBookingId("BOOK_123")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "Payment Failed");
        assertEquals(viewElementInfos.get(0).getValue(), "768");
    }

    @Test
    public void newBillSuccessTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_SUCCESS).setBookingId("BOOK_123")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Paid");
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }

    @Test
    public void newBillDueTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_DUE).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(display.get(0).getValue(), "Tue, 11 Oct");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertEquals(ctas.get(0).getValue(), "Pay Now");

    }
    @Test
    public void newBillOverDueTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_OVERDUE).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> childCtas = utilityChildCTAManager.getChildCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Overdue");
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(display.get(0).getValue(), "Tue, 11 Oct");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertEquals(ctas.get(0).getValue(), "Pay Now");
        assertEquals(childCtas.get(0).getValue(), "Mark as Paid");
        assertEquals(childCtas.get(1).getValue(), "View Bill Details");
    }


    @Test
    public void newBillDueTodayTest() {
        DateFormat dateFormat = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_DUE_TODAY_OR_TOMORROW).setBookingId("BOOK_123")
                .setBillDate(dateFormat.format(new Date(System.currentTimeMillis() - 864000000)))
                .setDueDate(dateFormat.format(new Date()))
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> childCtas = utilityChildCTAManager.getChildCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Due Today");
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(ctas.get(0).getValue(), "Pay Now");
        assertEquals(childCtas.get(0).getValue(), "Mark as Paid");
        assertEquals(childCtas.get(1).getValue(), "View Bill Details");
    }

    @Test
    public void newBillDueTomorrowTest() {
        DateFormat dateFormat = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_DUE_TODAY_OR_TOMORROW).setBookingId("BOOK_123")
                .setBillDate(dateFormat.format(new Date(System.currentTimeMillis() - 864000000)))
                .setDueDate(dateFormat.format(new Date(System.currentTimeMillis() + 86400000)))
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> childCtas = utilityChildCTAManager.getChildCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Due Tomorrow");
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(ctas.get(0).getValue(), "Pay Now");
        assertEquals(childCtas.get(0).getValue(), "Mark as Paid");
        assertEquals(childCtas.get(1).getValue(), "View Bill Details");
    }

    @Test
    public void newAutomaticTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.AUTOMATIC_PAYMENT).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(display.get(0).getValue(), "Tue, 11 Oct");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertEquals(ctas.get(0).getValue(), "Pay Now");
    }

    @Test
    public void notPaidTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_NOT_PAID_PAYTM).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Not Paid on Paytm");
        assertEquals(viewElementInfos.get(1).getType(), "footer1");
        assertEquals(viewElementInfos.size(), 2);
        assertEquals(display.get(0).getValue(), "Tue, 11 Oct");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertNull(ctas);
    }
    @Test
    public void automaticWaitTest() throws ParseException {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.AUTOMATIC_PAYMENT).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setAmount(768)
                .setIvrsBooking(false).build();
        historyView.setAutomaticDate(new SimpleDateFormat("yyy-MM-dd").parse("2022-10-11"));
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> childCtas = utilityChildCTAManager.getChildCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getType(), "footer1");
        assertEquals(ctas.get(0).getValue(), "Pay Now");
        assertEquals(display.get(0).getValue(), "Tue, 11 Oct");
        assertEquals(display.size(), 1);
        assertEquals(childCtas.get(0).getValue(), "Mark as Paid");
        assertEquals(childCtas.get(1).getValue(), "View Bill Details");

    }

    @Test
    public void billMarkedPaidTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_MARKED_PAID).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Marked as Paid");
        assertEquals(viewElementInfos.get(1).getType(), "footer1");
        assertEquals(viewElementInfos.size(), 2);
        assertEquals(display.get(0).getValue(), "Tue, 11 Oct");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertNull(ctas);
    }

    @Test
    public void rechargePendingTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.RECHARGE_PENDING).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Payment Pending");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
    }

    @Test
    public void billPendingTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_PENDING).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Payment Pending");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
    }
    @Test
    public void automaticPendingTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.AUTOMATIC_PAYMENT_PENDING).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Automatic Payment Pending");
        assertEquals(viewElementInfos.size(),2);
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
    }

    @Test
    public void suggestedRechargeTest() throws ParseException {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.SUGGESTED_RECHARGE).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        historyView.setAutomaticDate(new SimpleDateFormat("yyy-MM-dd").parse("2022-10-11"));
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(ctas.get(0).getValue(), "Pay");
        assertNull(display);
        assertEquals(ctas.size(), 1);
    }

    @Test
    public void rechargeFailedTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.RECHARGE_FAILED).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Payment Failed");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }

    @Test
    public void biiCancelledTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_CANCELLED).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Payment Cancelled");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }
    @Test
    public void billFailedTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_FAILED).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Payment Failed");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }
    @Test
    public void rechargeCancelledTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.RECHARGE_CANCELLED).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Payment Cancelled");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }

    @Test
    public void automaticSuccessTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.AUTOMATIC_PAYMENT_SUCCESS).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "Paid Automatically");
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }


    @Test
    public void automaticCancelledTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.AUTOMATIC_PAYMENT_CANCELLED).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Automatic Payment Cancelled");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }

    @Test
    public void automaticFailedTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.AUTOMATIC_PAYMENT_FAILED).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = utilityDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Automatic Payment Failed");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }

    @Test
    public void rechargeSuccessTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.RECHARGE_SUCCESS).setBookingId("BOOK_123")
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setAmount(768)
                .setIvrsBooking(false).build();
        List<ViewElementInfo> viewElementInfos = utilityHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = utilityCTAManager.getCTAs(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Paid");
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }
}
