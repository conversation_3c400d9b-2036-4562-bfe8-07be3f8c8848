package com.paytm.saga.service.aggregator.viewmanager;

import static org.junit.Assert.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.paytm.saga.common.constant.UtilityThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.service.aggregator.viewmanager.cta.impl.CreditCardCTAManager;
import com.paytm.saga.service.aggregator.viewmanager.display.impl.CreditCardDisplayViewManager;
import com.paytm.saga.service.aggregator.viewmanager.impl.CreditCardHeaderManager;

@RunWith(MockitoJUnitRunner.class)
public class CreditCardHeaderManagerImplTest {

    @InjectMocks
    private CreditCardHeaderManager creditCardHeaderManager;

    @InjectMocks
    private CreditCardCTAManager creditCardCTAManager;

    @InjectMocks
    private CreditCardDisplayViewManager creditCardDisplayViewManager;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void newBillDueTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.CREDIT_CARD_BILL_DUE)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(200.0)
                .setAmount(768).build();
        List<ViewElementInfo> viewElementInfos = creditCardHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = creditCardCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = creditCardDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "768");
        assertEquals(viewElementInfos.get(0).getValue(), "Total Due");
        assertEquals(viewElementInfos.get(1).getType(), "header1");
        assertEquals(display.get(0).getValue(), "200.0");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertEquals(ctas.get(0).getValue(), "Select Amount to Pay");

    }

    @Test
    public void newBillOverDueTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.CREDIT_CARD_BILL_OVERDUE)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(400.0)
                .setAmount(768).build();
        List<ViewElementInfo> viewElementInfos = creditCardHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = creditCardCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = creditCardDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "768");
        assertEquals(viewElementInfos.get(0).getValue(), "Total Due");
        assertEquals(viewElementInfos.get(1).getType(), "header1");
        assertEquals(viewElementInfos.get(2).getValue(), "Bill Overdue");
        assertEquals(viewElementInfos.get(2).getType(), "footer1");
        assertEquals(display.get(0).getValue(), "400.0");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertEquals(ctas.get(0).getValue(), "Select Amount to Pay");

    }

    @Test
    public void notPaidTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.CREDIT_CARD_NOT_PAID_ON_PAYTM)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(400.0)
                .setAmount(768).build();
        List<ViewElementInfo> viewElementInfos = creditCardHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = creditCardCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = creditCardDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "768");
        assertEquals(viewElementInfos.get(0).getType(), "header2");
        assertEquals(viewElementInfos.get(0).getValue(), "Total Due");
        assertEquals(viewElementInfos.get(1).getType(), "header1");
        assertEquals(viewElementInfos.get(2).getValue(), "Bill Not Paid on Paytm");
        assertEquals(viewElementInfos.get(2).getType(), "footer1");
        assertEquals(viewElementInfos.size(), 3);
        assertEquals(display.get(0).getValue(), "400.0");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertNull(ctas);
    }


    @Test
    public void billMarkedPaidTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.CREDIT_CARD_MARKED_AS_PAID)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(400.0)
                .setAmount(768).build();
        List<ViewElementInfo> viewElementInfos = creditCardHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = creditCardCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = creditCardDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "768");
        assertEquals(viewElementInfos.get(0).getValue(), "Total Due");
        assertEquals(viewElementInfos.get(1).getType(), "header1");
        assertEquals(viewElementInfos.get(2).getValue(), "Bill Marked as Paid");
        assertEquals(viewElementInfos.get(2).getType(), "footer1");
        assertEquals(viewElementInfos.size(), 3);
        assertEquals(display.get(0).getValue(), "400.0");
        assertEquals(display.get(1).getValue(), "Tue, 25 Oct");
        assertNull(ctas);
    }

    @Test
    public void billPendingTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_PENDING)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(400.0)
                .setAmount(768).build();
        List<ViewElementInfo> viewElementInfos = creditCardHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = creditCardCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = creditCardDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Payment Pending");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
    }

    @Test
    public void suggestedRechargeTest() throws ParseException {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.SUGGESTED_RECHARGE)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(400.0)
                .setAmount(768).build();
        historyView.setAutomaticDate(new SimpleDateFormat("yyy-MM-dd").parse("2022-10-11"));
        List<ViewElementInfo> ctas = creditCardCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = creditCardDisplayViewManager.getDisplayValues(historyView);
        assertEquals(ctas.get(0).getValue(), "Pay Bill");
        assertEquals(ctas.get(1).getValue(), "Get Latest Bill");
        assertNull(display);
        assertEquals(ctas.size(), 2);
    }

    @Test
    public void billFailedTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.BILL_FAILED)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(400.0)
                .setAmount(768).build();
        List<ViewElementInfo> viewElementInfos = creditCardHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = creditCardCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = creditCardDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Payment Failed");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }

    @Test
    public void rechargeCancelledTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.RECHARGE_CANCELLED)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(400.0)
                .setAmount(768).build();
        List<ViewElementInfo> viewElementInfos = creditCardHeaderManager.getHeaders(historyView);
        List<ViewElementInfo> ctas = creditCardCTAManager.getCTAs(historyView);
        List<ViewElementInfo> display = creditCardDisplayViewManager.getDisplayValues(historyView);
        assertEquals(viewElementInfos.get(0).getValue(), "768");
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Payment Cancelled");
        assertNull(display);
        assertEquals(ctas.get(0).getValue(), "View Details");
        assertEquals(ctas.size(), 1);
    }


    @Test
    public void rechargeSuccessTest() {
        HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
                .setThemeType(UtilityThemeTypes.RECHARGE_SUCCESS)
                .setBillDate("2022-10-11 20:10:48")
                .setDueDate("2022-10-25 12:56:23")
                .setMinimumDueAmount(400.0)
                .setAmount(768).build();
        List<ViewElementInfo> viewElementInfos = creditCardHeaderManager.getHeaders(historyView);
        assertEquals(viewElementInfos.get(1).getValue(), "Bill Paid");
        assertEquals(viewElementInfos.get(0).getValue(), "768");

    }
}
