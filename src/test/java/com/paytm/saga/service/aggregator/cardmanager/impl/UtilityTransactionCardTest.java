package com.paytm.saga.service.aggregator.cardmanager.impl;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.UtilityThemeTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class UtilityTransactionCardTest {

    @Mock
    public ViewManager ctaManager;
    @Mock
    public ViewManager displayValuesManager;
    @Mock
    public ViewManager headersViewManager;

    @InjectMocks
    private UtilityTransactionCard utilityTransactionCard;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    private static final Date TRANSACTION_DATE = DateUtil.minutesIncrDecr(new Date(), -5);
    private static final Date TRANSACTION_UPDATE_DATE = DateUtil.minutesIncrDecr(new Date(), -4);
    private static final Date CREATED_DATE = DateUtil.minutesIncrDecr(new Date(), -3);

    Map<String, String> billsObject = new HashMap<>();
    @Test
    public void rechargeSuccess() {
        CardInfoDto cardInfoDto = new CardInfoDto();
        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
                .setCategoryId(26L).setOperator("hubli electricity supply company ltd. (hescom)")
                .setCreatedTime(CREATED_DATE).setCustomerId(450471692L).setEventType("RECHARGE")
                .setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L).setOrderId(12862601947L)
                .setPaytype("prepaid").setProductId(110083094L).setRechargeNumber("0070204115")
                .setService("electricity").setStatus("7").setTransactionTime(TRANSACTION_DATE)
                .setTransactionUpdateTime(TRANSACTION_UPDATE_DATE).build();
        cardInfoDto.setChannelHistory(channelHistory);
        HistoryView historyView = utilityTransactionCard.getCardInfo(cardInfoDto);
        assertEquals(UtilityThemeTypes.RECHARGE_SUCCESS, historyView.getThemeType());
    }


    @Test
    public void billSuccess() {
        CardInfoDto cardInfoDto = new CardInfoDto();
        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
                .setCategoryId(26L).setOperator("hubli electricity supply company ltd. (hescom)")
                .setCreatedTime(CREATED_DATE).setCustomerId(450471692L).setEventType("RECHARGE")
                .setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L).setOrderId(12862601947L)
                .setPaytype("postpaid").setProductId(110083094L).setRechargeNumber("0070204115")
                .setService("electricity").setStatus("7").setTransactionTime(TRANSACTION_DATE)
                .setTransactionUpdateTime(TRANSACTION_UPDATE_DATE).build();
        cardInfoDto.setChannelHistory(channelHistory);
        HistoryView historyView = utilityTransactionCard.getCardInfo(cardInfoDto);
        assertEquals(UtilityThemeTypes.BILL_SUCCESS, historyView.getThemeType());
    }

    @Test
    public void automaticSuccess() {
        CardInfoDto cardInfoDto = new CardInfoDto();
        billsObject.put("channel", Constants.ReminderConstants.AUTOMATIC_CHANNEL);
        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
                .setCategoryId(26L).setOperator("hubli electricity supply company ltd. (hescom)")
                .setCreatedTime(CREATED_DATE).setCustomerId(450471692L).setEventType("RECHARGE")
                .setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L).setOrderId(12862601947L)
                .setPaytype("postpaid").setProductId(110083094L).setRechargeNumber("0070204115")
                .setService("electricity").setStatus("7").setTransactionTime(TRANSACTION_DATE)
                .setTransactionUpdateTime(TRANSACTION_UPDATE_DATE).build();
        cardInfoDto.setChannelHistory(channelHistory);
        HistoryView historyView = utilityTransactionCard.getCardInfo(cardInfoDto);
        assertEquals(UtilityThemeTypes.AUTOMATIC_PAYMENT_SUCCESS, historyView.getThemeType());
    }

}