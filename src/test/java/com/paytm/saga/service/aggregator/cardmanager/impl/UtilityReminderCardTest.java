package com.paytm.saga.service.aggregator.cardmanager.impl;

import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.common.constant.UtilityThemeTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.ReminderHistoryBuilder;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.service.aggregator.viewmanager.display.impl.UtilityDisplayViewManager;
import com.paytm.saga.util.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.Format;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static org.junit.Assert.assertEquals;

public class UtilityReminderCardTest {

    @Mock
    public ViewManager ctaManager;
    @Mock
    public ViewManager displayValuesManager;
    @Mock
    public ViewManager headersViewManager;

    @InjectMocks
    private UtilityReminderCard reminderCard;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void newBillTest() {
        CardInfoDto cardInfoDto = new CardInfoDto();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
                .setBill_date(new Date()).setDue_date(new Date(System.currentTimeMillis() + 259200000)).setAmount(100.0).build();
        cardInfoDto.setReminderHistory(reminderHistory);
        System.out.println(reminderHistory.getBill_date());
        HistoryView historyView = reminderCard.getCardInfo(cardInfoDto);
        assertEquals(EventTypes.BILL, historyView.getEventType());
        assertEquals(UtilityThemeTypes.BILL_DUE, historyView.getThemeType());
    }

    @Test
    public void newBillTestDueToday() {
        CardInfoDto cardInfoDto = new CardInfoDto();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
                .setBill_date(DateUtil.dateIncrDecr(new Date(), -3)).setDue_date(new Date()).setAmount(100.0).build();
        cardInfoDto.setReminderHistory(reminderHistory);
        System.out.println(reminderHistory.getBill_date());
        HistoryView historyView = reminderCard.getCardInfo(cardInfoDto);
        assertEquals(EventTypes.BILL, historyView.getEventType());
        assertEquals(UtilityThemeTypes.BILL_DUE_TODAY_OR_TOMORROW, historyView.getThemeType());
    }

    @Test
    public void newBillTestDueTomorrow() {
        CardInfoDto cardInfoDto = new CardInfoDto();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
                .setBill_date(DateUtil.dateIncrDecr(new Date(), -3))
                .setDue_date(DateUtil.dateIncrDecr(new Date(), +1)).
                setAmount(100.0).build();
        cardInfoDto.setReminderHistory(reminderHistory);
        System.out.println(reminderHistory.getBill_date());
        HistoryView historyView = reminderCard.getCardInfo(cardInfoDto);
        assertEquals(EventTypes.BILL, historyView.getEventType());
        assertEquals(UtilityThemeTypes.BILL_DUE_TODAY_OR_TOMORROW, historyView.getThemeType());
    }
    @Test
    public void utilityDateFormatTest() throws ParseException {
        CardInfoDto cardInfoDto = new CardInfoDto();
        Date date=new SimpleDateFormat("dd/MM/yyyy").parse("10/11/2022");
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
                .setBill_date(date).setDue_date(date).setAmount(100.0).build();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView historyView = reminderCard.getCardInfo(cardInfoDto);
        historyView.setBillDate(DateUtil.dateFormatter(cardInfoDto.getReminderHistory().getBill_date(),
                DateFormats.DATE_TIME_FORMAT_2));
        historyView.setDueDate(DateUtil.dateFormatter(cardInfoDto.getReminderHistory().getDue_date(),
                DateFormats.DATE_TIME_FORMAT_2));
        ViewElementInfo viewElementInfo = new ViewElementInfo();
        viewElementInfo.setValue(DateUtil.dateFormatter(
                DateUtil.stringToDate(historyView.getDueDate(), DateFormats.DATE_TIME_FORMAT_2),
                DateFormats.D_MMM_EEEE));
       assertEquals("10 Nov, Thursday",viewElementInfo.getValue());



    }


    @Test
    public void billOverDueTest() {
        CardInfoDto cardInfoDto = new CardInfoDto();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
                .setBill_date(DateUtil.dateIncrDecr(new Date(), -CommonConsts.BILL_OVERDUE_DAYS))
                .setDue_date(DateUtil.dateIncrDecr(new Date(), -CommonConsts.BILL_OVERDUE_DAYS)).setAmount(100.0)
                .setIsPartial(1).build();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView historyView = reminderCard.getCardInfo(cardInfoDto);
        assertEquals(EventTypes.BILL, historyView.getEventType());
        assertEquals(UtilityThemeTypes.BILL_OVERDUE, historyView.getThemeType());
    }

    @Test
    public void automaticBilTest() {
        CardInfoDto cardInfoDto = new CardInfoDto();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
                .setBill_date(DateUtil.dateIncrDecr(new Date(), -CommonConsts.BILL_OVERDUE_DAYS - 1))
                .setDue_date(DateUtil.dateIncrDecr(new Date(), 5)).setAmount(100.0)
                .setIsPartial(1).build();
        reminderHistory.setAutomaticData(DateUtil.dateIncrDecr(new Date(), 1));
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView historyView = reminderCard.getCardInfo(cardInfoDto);
        assertEquals(UtilityThemeTypes.AUTOMATIC_PAYMENT, historyView.getThemeType());
    }
}

