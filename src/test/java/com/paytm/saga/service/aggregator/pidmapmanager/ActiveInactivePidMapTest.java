package com.paytm.saga.service.aggregator.pidmapmanager;

import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;

import com.paytm.saga.model.ActiveInactiveMap;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.dto.CvrActiveInactiveApiResponse;
import com.paytm.saga.service.GenericRestClient;

@RunWith(MockitoJUnitRunner.class)
public class ActiveInactivePidMapTest {
	@InjectMocks
	ActiveInactivePidMap activeInactivePidMap;
	
    @Mock
    private GenericRestClient genericRestClient;
	
	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}
	

	@Test
	public void storeActiveInactivePidMapWithApiHit() throws RestTemplateServiceException, JsonParseException, JsonMappingException, IOException {
        ObjectMapper objectMapper = new ObjectMapper();
		String responseString = "{\"status\":200,\"data\":{\"2\":40,\"30\":400,\"238\":270,\"301\":500,\"313\":54400335,\"10101010\":1200139386,\"1200763862\":1200999999,\"1200764525\":1201251717,\"1234567890\":1200167124}}";
		CvrActiveInactiveApiResponse response = objectMapper.readValue(responseString, CvrActiveInactiveApiResponse.class);
		when(genericRestClient.get(any(),any(),any(),any(),any())).thenReturn(response);
		activeInactivePidMap.storeActiveInactivePidMap();
		
		assertEquals(response.getData().get("30"),activeInactivePidMap.getActivePid(30l));
		
	}
}
