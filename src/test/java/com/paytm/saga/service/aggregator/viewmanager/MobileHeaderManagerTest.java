package com.paytm.saga.service.aggregator.viewmanager;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.service.aggregator.viewmanager.impl.MobileHeaderManager;
import com.paytm.saga.util.DateUtil;

public class MobileHeaderManagerTest {
	@InjectMocks
	public MobileHeaderManager mobileHeaderManager;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testSuggestedCardHeader() {
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME)
				.setAmount(100).setInvalidPlan(true).build();
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(2, viewElementInfo.size());
		assertEquals("Plan Discontinued", viewElementInfo.get(0).getValue());
		assertEquals("header1", viewElementInfo.get(0).getType());
		assertEquals("₹100", viewElementInfo.get(1).getValue());
		assertEquals("header2", viewElementInfo.get(1).getType());
	}
	
	@Test
	public void testDateChangeCardTodayHeader() {
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME)
				.setAmount(100).setEventDate(new Date()).setLastCard(true).build();
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(1, viewElementInfo.size());
		assertEquals("Today", viewElementInfo.get(0).getValue());
		assertNull(viewElementInfo.get(0).getType());
	}
	
	@Test
	public void testDateChangeCardHeader() throws ParseException {
		Date eventDate=DateUtil.dateIncrDecr(new Date(), -3);
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME)
				.setAmount(100).setEventDate(eventDate).setLastCard(true).build();
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(1, viewElementInfo.size());
		assertEquals(DateUtil.dateFormatter(DateUtil.timestampToDate(historyView.getEventDate()), DateFormats.DATE_FORMAT), viewElementInfo.get(0).getValue());
		assertNull(viewElementInfo.get(0).getType());
	}
	
	@Test
	public void testMnpCardHeader() {
		Date eventDate=DateUtil.dateIncrDecr(new Date(), -3);
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.MNP_CARD_THEME)
				.setAmount(100).setOperator("operator").setEventDate(eventDate).setLastCard(true).build();
		historyView.setPreviousOperator("jio");
		historyView.setOperator("vodafone idea");
		historyView.setPayType("postpaid");
		historyView.setPreviousPayType("postpaid");
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(1, viewElementInfo.size());
		System.out.println(viewElementInfo.get(0).getValue());
		assertEquals("We detected an operator change from Jio Postpaid to Vodafone Idea Postpaid",viewElementInfo.get(0).getValue());
		assertNull(viewElementInfo.get(0).getType());
	}
	@Test
	public void testFailureCardHeader() {
		Date eventDate=DateUtil.dateIncrDecr(new Date(), -3);
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.FAILURE_RECHARGE_CARD_THEME)
				.setAmount(100).setOperator("operator").setEventDate(eventDate).setLastCard(true).build();
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(2, viewElementInfo.size());
		assertEquals("Recharge Failed",viewElementInfo.get(0).getValue());
		assertEquals("header1", viewElementInfo.get(0).getType());
		assertEquals("₹100", viewElementInfo.get(1).getValue());
		assertEquals("header2", viewElementInfo.get(1).getType());
	}
	@Test
	public void testPendingCardHeader() {
		Date eventDate=DateUtil.dateIncrDecr(new Date(), -3);
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.PENDING_RECHARGE_CARD_THEME)
				.setAmount(100).setOperator("operator").setEventDate(eventDate).setLastCard(true).build();
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(2, viewElementInfo.size());
		assertEquals("Recharge Pending",viewElementInfo.get(0).getValue());
		assertEquals("header1", viewElementInfo.get(0).getType());
		assertEquals("₹100", viewElementInfo.get(1).getValue());
		assertEquals("header2", viewElementInfo.get(1).getType());
	}
	
	@Test
	public void testCancelledCardHeader() {
		Date eventDate=DateUtil.dateIncrDecr(new Date(), -3);
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME)
				.setAmount(100).setOperator("operator").setEventDate(eventDate).setLastCard(true).build();
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(2, viewElementInfo.size());
		assertEquals("Recharge Cancelled",viewElementInfo.get(0).getValue());
		assertEquals("header1", viewElementInfo.get(0).getType());
		assertEquals("₹100", viewElementInfo.get(1).getValue());
		assertEquals("header2", viewElementInfo.get(1).getType());
	}
	
	@Test
	public void testSuccessCardHeader() {
		Date eventDate=DateUtil.dateIncrDecr(new Date(), -3);
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME)
				.setAmount(100).setOperator("operator").setEventDate(eventDate).setLastCard(true).build();
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(2, viewElementInfo.size());
		assertEquals("Recharge Successful",viewElementInfo.get(0).getValue());
		assertEquals("header1", viewElementInfo.get(0).getType());
		assertEquals("₹100", viewElementInfo.get(1).getValue());
		assertEquals("header2", viewElementInfo.get(1).getType());
	}
	
	@Test
	public void testMarkAsPaidCardHeader() {
		Date eventDate=DateUtil.dateIncrDecr(new Date(), -3);
		HistoryView historyView = new HistoryViewBuilder().setThemeType(CardThemeTypes.MARKED_AS_PAID_CARD_THEME)
				.setAmount(100).setOperator("operator").setEventDate(eventDate).setLastCard(true).build();
		List<ViewElementInfo> viewElementInfo = mobileHeaderManager.getHeaders(historyView);
		assertEquals(2, viewElementInfo.size());
		assertEquals("Marked as Recharged",viewElementInfo.get(0).getValue());
		assertEquals("header1", viewElementInfo.get(0).getType());
		assertEquals("₹100", viewElementInfo.get(1).getValue());
		assertEquals("header2", viewElementInfo.get(1).getType());
	}
}
