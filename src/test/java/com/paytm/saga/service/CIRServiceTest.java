package com.paytm.saga.service;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.CIRRequest;
import com.paytm.saga.dto.CIRResponse;
import com.paytm.saga.dto.DeleteCIRRequest;
import com.paytm.saga.dto.DeleteCIRResponse;
import com.paytm.saga.model.DeleteCIR;
import com.paytm.saga.repository.DeleteCIRRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class CIRServiceTest {
    @Mock
    DeleteCIRRepository deleteCIRRepository;

    @InjectMocks
    CIRService cirService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testInsertDeleteCIR() throws RechargeSagaBaseException {
        DeleteCIRRequest deleteCIRRequest = new DeleteCIRRequest();
        deleteCIRRequest.setCustomerId(356458893L);
        deleteCIRRequest.setBureauName("CIBIL");
        deleteCIRRequest.setBankName("SBI");
        ArgumentCaptor<DeleteCIR> deleteCIR = ArgumentCaptor.forClass(DeleteCIR.class);
        DeleteCIRResponse deleteCIRResponse =cirService.deleteCIR(deleteCIRRequest);
        verify(deleteCIRRepository,times(1)).insertData(any(), any(), any(), any(), any());
        assertEquals("SUCCESS", deleteCIRResponse.getMessage());
    }

    @Test
    public void testGetBankNameCIR() throws RechargeSagaBaseException {
        CIRRequest cirRequest = new CIRRequest();
        cirRequest.setCustomerId(356458893L);
        cirRequest.setBureauName("CIBIL");
        DeleteCIR deleteCIR1 = new DeleteCIR();
        deleteCIR1.setCustomerId(356458893L);
        deleteCIR1.setBureauName("CIBIL");
        deleteCIR1.setBankName("SBI");
        deleteCIR1.setDeleteAt(new Date());
        DeleteCIR deleteCIR2 = new DeleteCIR();
        deleteCIR2.setCustomerId(356458893L);
        deleteCIR2.setBureauName("CIBIL");
        deleteCIR2.setBankName("HDFC");
        deleteCIR2.setDeleteAt(new Date());
        List<DeleteCIR> banknames= new ArrayList<>();
        banknames.add(deleteCIR1);
        banknames.add(deleteCIR2);
        when(deleteCIRRepository.findBankNamesbyCustomerIdandBureauName(any(),any())).thenReturn(banknames);

        CIRResponse cirResponse =cirService.getBankNames(cirRequest);
        assertEquals("sbi",cirResponse.getBankNames().get(0));
        assertEquals("hdfc",cirResponse.getBankNames().get(1));
    }

}
