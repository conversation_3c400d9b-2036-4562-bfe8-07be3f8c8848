package com.paytm.saga.service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.NumberConstants;
import com.paytm.saga.common.constant.ServiceNameConstants;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.JsonUtils;
import com.timgroup.statsd.StatsDClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ServiceConfigTest {

    @InjectMocks
    private ServiceConfig serviceConfig;

    @Mock
    private StatsDClient monitoringClient;

    @Before
    public void setUp() {
        FeatureConfigCache.getInstance().setFeatureConfigMap(new HashMap<>());
    }

    @Test
    public void testDefaultValue() throws Exception {
        assertEquals(NumberConstants.AGENT_LIMIT.intValue(), serviceConfig.getAgentLimit());
        assertEquals(NumberConstants.BILL_VISIBLITY_DAY.intValue(), serviceConfig.getBillVisiblityDays().intValue());
        assertEquals(NumberConstants.BILL_VISIBLITY_DAY.intValue(), serviceConfig.getBillVisiblityDays("").intValue());
        assertEquals(NumberConstants.FREQUENT_ORDER_EXECUTOR_TIMEOUT.intValue(), serviceConfig.getFrequentOrderExecutorTimeOut());
        assertEquals(NumberConstants.FREQUENT_ORDER_EXECUTOR_TIMEOUT.intValue(), serviceConfig.getFrequentOrderExecutorTimeOut());
        assertEquals(NumberConstants.RECENT_DB_READ_TIMEOUT.intValue(), serviceConfig.getRecentReadTimeout());
        assertEquals(NumberConstants.BILL_END_RANGE_CURRENT_DATE, serviceConfig.getBillVisiblityDaysBefore());

        ArgumentCaptor<String> metricNameArgument = ArgumentCaptor.forClass(String.class);

        ArgumentCaptor<String[]> tagArgument = ArgumentCaptor.forClass(String[].class);

        verify(monitoringClient, times(7)).incrementCounter(metricNameArgument.capture(), tagArgument.capture());
        assertEquals(Constants.MetricConstants.FREQUENT_ORDER, metricNameArgument.getValue());

    }

    @Test
    public void testDefaultServiceConfig() throws Exception {

        assertEquals(NumberConstants.PREPAID_END_DAYS.intValue(), serviceConfig.getPrepaidEndDay());
        assertEquals(NumberConstants.POSTPAID_END_DAYS.intValue(), serviceConfig.getPostpaidEndDay());
        assertEquals(NumberConstants.PREPAID_START_DAYS.intValue(), serviceConfig.getPrepaidStartDay());
        assertEquals(NumberConstants.POSTPAID_END_DAYS.intValue(), serviceConfig.getPostpaidStartDay());

        ArgumentCaptor<String> metricNameArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String[]> tagArgument = ArgumentCaptor.forClass(String[].class);

        verify(monitoringClient, times(4)).incrementCounter(metricNameArgument.capture(), tagArgument.capture());
        assertEquals(Constants.MetricConstants.FREQUENT_ORDER, metricNameArgument.getValue());

    }

    @Test
    public void testBillVisiblityDays() {
        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(3, (int)serviceConfig.getBillVisiblityDays());
    }
    @Test
    public void testBillVisiblityDaysByService() {
        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartRecoEndDays\":{\"postpaid\":5},\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(5, (int)serviceConfig.getBillVisiblityDays("postpaid"));
    }

    @Test
    public void testenableSavedCardInRecoWithTrue() {
        String config = "{\"disableDropOff\":false,\"enableSavedCardInReco\":true,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(Boolean.TRUE, serviceConfig.enableSavedCardInReco());
    }

    @Test
    public void testenableSavedCardInRecoWithFalse() {
        String config = "{\"disableDropOff\":false,\"enableSavedCardInReco\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(Boolean.FALSE, serviceConfig.enableSavedCardInReco());
    }

    @Test
    public void testenableSavedCardInRecoWithNull() {
        String config = "{\"disableDropOff\":false,\"enableSavedCardInReco\":null,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(Boolean.FALSE, serviceConfig.enableSavedCardInReco());
    }

    @Test
    public void testgetRechargeNumberToCustomerIdLimitNull() {
        String config = "{\"disableDropOff\":false,\"enableSavedCardInReco\":true,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"rechargeNumberToCustomerIdLimit\":null}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(100, serviceConfig.getRechargeNumberToCustomerIdLimit());
    }

    @Test
    public void testgetRechargeNumberToCustomerIdLimitNonNull() {
        String config = "{\"disableDropOff\":false,\"enableSavedCardInReco\":true,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"rechargeNumberToCustomerIdLimit\":120}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(120, serviceConfig.getRechargeNumberToCustomerIdLimit());
    }

    @Test
    public void testgetFetchRecentLimitNull() {
        String config = "{\"disableDropOff\":false,\"enableSavedCardInReco\":true,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"fetchRecentLimit\":null}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(100, (int)serviceConfig.getFetchRecentLimit());
    }

    @Test
    public void testgetFetchRecentLimitNonNull() {
        String config = "{\"disableDropOff\":false,\"enableSavedCardInReco\":true,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"fetchRecentLimit\":150}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(150, (int)serviceConfig.getFetchRecentLimit());
    }

    @Test
    public void testGetSmartRecentsEnabledServices() {
        try {
            Map<String, Object> serviceConfig = new HashMap<>();
            serviceConfig.put(ServiceNameConstants.SMART_RECENTS_ENABLED_SERVICES, new ArrayList<String>(){{add("mobile");add("dth");}});
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(2, serviceConfig.getSmartRecentsEnabledServices().size());
    }

    @Test
    public void testGetSmartRecentRecoEnabledServices_servicesAdded() {
        Map<String, Object> svcConfig = new HashMap<>();
        svcConfig.put(ServiceNameConstants.SMART_RECENTS_RECO_ENABLED_SERVICES, new ArrayList<String>(){{add("mobile");add("dth");}});
        FeatureConfigCache.getInstance().setFeatureConfigMap(svcConfig);
        assertEquals(2, serviceConfig.getSmartRecentRecoEnabledServices().size());
    }

    @Test
    public void testGetSmartRecentRecoEnabledServices_configNotPresent() {
        Map<String, Object> svcConfig = new HashMap<>();
        FeatureConfigCache.getInstance().setFeatureConfigMap(svcConfig);
        assertEquals(0, serviceConfig.getSmartRecentRecoEnabledServices().size());
    }

    @Test
    public void testgetSMSBillUpdateServicesWhenNull() {
        String config = "{\"disableDropOff\":false,\"smsBillUpdateServices\":null,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(Collections.emptyList(), serviceConfig.getSMSBillUpdateServices());
    }

    @Test
    public void testgetSMSBillUpdateServicesWhenEmpty() {
        String config = "{\"disableDropOff\":false,\"smsBillUpdateServices\":[],\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(Collections.emptyList(), serviceConfig.getSMSBillUpdateServices());
    }

    @Test
    public void testgetSMSBillUpdateServicesWhenNonEmpty() {
        String config = "{\"disableDropOff\":false,\"smsBillUpdateServices\":[\"electricity\"],\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }

        assertEquals(Collections.singletonList("electricity"), serviceConfig.getSMSBillUpdateServices());
    }

    @Test
    public void isSuggestedCardEnabledReturnsTrueWhenEnabled() {
        String config = "{\"enableSuggestedCards\":true,\"smsBillUpdateServices\":null}";
        Map<String, Object> parsedServiceConfig = JsonUtils.parseMapJson(config);
        FeatureConfigCache.getInstance().setFeatureConfigMap(parsedServiceConfig);

        assertEquals(true, serviceConfig.isSuggestedCardEnabled());
    }

    @Test
    public void isSuggestedCardEnabledReturnsFalseWhenDisabled() {
        String config = "{\"enableSuggestedCards\":false,\"smsBillUpdateServices\":null}";
        Map<String, Object> parsedServiceConfig = JsonUtils.parseMapJson(config);
        FeatureConfigCache.getInstance().setFeatureConfigMap(parsedServiceConfig);

        assertEquals(false, serviceConfig.isSuggestedCardEnabled());
    }

    @Test
    public void isSuggestedCardEnabledReturnsFalseWhenNull() {
        String config = "{\"enableSuggestedCards\":null,\"smsBillUpdateServices\":null}";
        Map<String, Object> parsedServiceConfig = JsonUtils.parseMapJson(config);
        FeatureConfigCache.getInstance().setFeatureConfigMap(parsedServiceConfig);

        assertEquals(false, serviceConfig.isSuggestedCardEnabled());
    }

    @Test
    public void isSuggestedCardEnabledReturnsFalseWhenObjectNotPresent() {
        String config = "{\"smsBillUpdateServices\":null}";
        Map<String, Object> parsedServiceConfig = JsonUtils.parseMapJson(config);
        FeatureConfigCache.getInstance().setFeatureConfigMap(parsedServiceConfig);

        assertEquals(false, serviceConfig.isSuggestedCardEnabled());
    }

    @Test
    public void testGetSystemFetchBillSources(){
        String config = "{\"systemFetchBillSources\":[\"systemFetch1\",\"systemFetch2\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(2, serviceConfig.getSystemFetchBillSources().size());
    }

    @Test
    public void testGetSmsFetchBillSources(){
        String config = "{\"smsBillSources\":[\"sms1\",\"sms2\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(2, serviceConfig.getSmsBillSources().size());
    }

    @Test
    public void testGetBillSourcesServices(){
        String config = "{\"billSourcesServices\":[\"service1\",\"service2\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(2, serviceConfig.getBillSourcesServices().size());
    }

    @Test
    public void testGetRUPartialBillRecoServices(){
        String config = "{\"ru_partial_bill_reco_services\":[\"loan\",\"electricity\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        List<String> services = serviceConfig.getRUPartialBillRecoServices();
        assertEquals(2, services.size());
        assertEquals("loan", services.get(0));
        assertEquals("electricity", services.get(1));

    }

    @Test
    public void testGetRUPartialBillRecoDueDateOffset(){
        String config = "{\"ru_partial_bill_customer_bill_due_date_offset\": 3}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        int offset = serviceConfig.getRUPartialBillRecoDueDateOffset();
        assertEquals(3, offset);
    }
    
    @Test
    public void testCheckIfSubcriberConfigAllowedService() {
        String service= "electricity";
        List<String> allowedServices = new ArrayList<>();
        allowedServices.add(service);
        Map<String,Object> serviceConfigMap = new HashMap<>();
        serviceConfigMap.put(ServiceNameConstants.CONSUMER_NAME_ALLOWED_SERVICES, allowedServices);
        FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfigMap);
        assertEquals(true, serviceConfig.checkIfSubcriberConfigAllowed(service));
    }

    @Test
    public void testCheckIfSubcriberConfigNotAllowedService() {
        String service= "electricity";
        List<String> allowedServices = new ArrayList<>();
        allowedServices.add("mobile");
        Map<String,Object> serviceConfigMap = new HashMap<>();
        serviceConfigMap.put(ServiceNameConstants.CONSUMER_NAME_ALLOWED_SERVICES, allowedServices);
        FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfigMap);
        assertEquals(false, serviceConfig.checkIfSubcriberConfigAllowed(service));
    }
}
