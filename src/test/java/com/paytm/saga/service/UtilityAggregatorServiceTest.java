package com.paytm.saga.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.*;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.dto.builder.ReminderHistoryBuilder;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.dto.catalogue.ProductAttributes;
import com.paytm.saga.model.AutomaticData;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.repository.AutomaticDataRepository;
import com.paytm.saga.service.aggregator.UtilityAggregatorServiceImpl;
import com.paytm.saga.service.aggregator.cardmanager.CardManager;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.service.aggregator.cardmanager.DateCard;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.hamcrest.CoreMatchers;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class UtilityAggregatorServiceTest {

    private static final Date TRANSACTION_DATE = DateUtil.minutesIncrDecr(new Date(), -5);
    private static final Date DUE_DATE_5DAYS = DateUtil.dateIncrDecr(new Date(), 5);
    private static final Date AUTOMATIC_DATE_3DAYS = DateUtil.dateIncrDecr(new Date(), 3);

    private static final Date TRANSACTION_UPDATE_DATE = DateUtil.minutesIncrDecr(new Date(), -4);
    private static final Date CREATED_DATE = DateUtil.minutesIncrDecr(new Date(), -3);
    ObjectMapper objectMapper = new ObjectMapper();

    @InjectMocks
    private UtilityAggregatorServiceImpl utilityAggregatorService;

    @Mock
    private CardManager cardManager;
    @Mock
    private AutomaticCardService automaticCardService;
    @Mock
    private ChannelHistoryFinalizedService channelHistoryFinalizedService;
    @Mock
    private ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
    @Mock
    private DateCard dateCardService;

    @Mock
    private ChannelHistoryService channelHistoryService;
    @Mock
    private ReminderHistoryService reminderHistoryService;

    @Mock
    private AutomaticDataRepository automaticDataRepository;

    @Mock
    private RecentsRepositoryWrapperService recentsRepository;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(UtilityAggregatorServiceImpl.class));


    @Test
    public void testWhenNoTXNButBillWithoutAutomatic() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";

        Date transactionDate = TRANSACTION_DATE;
        Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
        Date eventUpdateDate = CREATED_DATE;

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(new Date()).setIs_automatic(0)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).build();
        reminderHistoryList.add(reminderHistory);


        when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(null);


        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(new ArrayList<ChannelHistory>());

        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setAvailableNext(false);
        channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 0, null))
                .thenReturn(channelHistoryPage);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                service, operator)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView historyView = objectMapper.readValue("{\"themeType\":\"utility_bill_not_paid_paytm\",\"createdDate\":\"29-04-2022 17:09:35\",\"amount\":200.55,\"orderId\":0,\"headings\":[{\"value\":\"200.55\",\"type\":\"header1\"},{\"value\":\"Bill Not Paid on Paytm\",\"type\":\"footer1\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Mon 06 Sep\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Mark as Paid\",\"type\":\"cta1\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2021-09-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(historyView);
        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        HistoryPage response = utilityAggregatorService.aggregateHistoryInfo(historyPageRequest, null, null);
        System.out.println(objectMapper.writeValueAsString(response));
        assertEquals(1, response.getCards().size());
    }


    @Test
    public void testWhenPendingTXNButBillWithoutAutomatic() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";

        Date transactionDate = TRANSACTION_DATE;
        Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
        Date eventUpdateDate = CREATED_DATE;

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2021-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(0)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).build();
        reminderHistoryList.add(reminderHistory);

        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        channelHistories.add(channelHistory);

        when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(null);


        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(channelHistories);

        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setAvailableNext(false);
        channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 1, null))
                .thenReturn(channelHistoryPage);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                service, operator)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        System.out.println("reminderView view is " + objectMapper.writeValueAsString(reminderView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);

        cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        HistoryView rechargeView = objectMapper.readValue("{\"themeType\":\"utility_recharge_pending\",\"createdDate\":\"01-04-2021 14:03:29\",\"amount\":200.55,\"status\":\"PENDING\",\"orderId\":1,\"headings\":[{\"value\":\"200.55\",\"type\":\"header1\"},{\"value\":\"Recharge Pending\",\"type\":\"header2\"}],\"circle\":\"delhi ncr\",\"operator\":\"tata sky\",\"service\":\"electricity\",\"payType\":\"prepaid\",\"eventType\":\"RECHARGE\",\"billDate\":\"2020-03-10\",\"dueDate\":\"2022-05-06\",\"statusCode\":\"1\",\"showInfoIcon\":true,\"utility\":true}", HistoryView.class);
        System.out.println("rechargeView view is " + objectMapper.writeValueAsString(rechargeView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(rechargeView);

        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        HistoryPage response = utilityAggregatorService.aggregateHistoryInfo(historyPageRequest, null, null);
        System.out.println(objectMapper.writeValueAsString(response));
        assertEquals(1, response.getCards().size());
    }

    @Test
    public void testWhenFailedTXNButBillWithoutAutomatic() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";

        Date transactionDate = TRANSACTION_DATE;
        Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
        Date eventUpdateDate = CREATED_DATE;

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2021-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.20).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(0)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).build();
        reminderHistoryList.add(reminderHistory);

        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100.20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("6").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        channelHistories.add(channelHistory);

        when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(null);


        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(channelHistories);

        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setAvailableNext(false);
        channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 1, null))
                .thenReturn(channelHistoryPage);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                service, operator)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        System.out.println("reminderView view is " + objectMapper.writeValueAsString(reminderView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);

        HistoryView rechargeView = objectMapper.readValue("{\"themeType\":\"utility_recharge_pending\",\"createdDate\":\"01-04-2021 14:03:29\",\"amount\":200.55,\"status\":\"PENDING\",\"orderId\":1,\"headings\":[{\"value\":\"200.55\",\"type\":\"header1\"},{\"value\":\"Recharge Pending\",\"type\":\"header2\"}],\"circle\":\"delhi ncr\",\"operator\":\"tata sky\",\"service\":\"electricity\",\"payType\":\"prepaid\",\"eventType\":\"RECHARGE\",\"billDate\":\"2020-03-10\",\"dueDate\":\"2022-05-06\",\"statusCode\":\"1\",\"showInfoIcon\":true,\"utility\":true}", HistoryView.class);
        System.out.println("rechargeView view is " + objectMapper.writeValueAsString(rechargeView));
        cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        when(cardManager.getCardInfo(any())).thenReturn(rechargeView);

        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        HistoryPage response = utilityAggregatorService.aggregateHistoryInfo(historyPageRequest, null, null);
        System.out.println(objectMapper.writeValueAsString(response));
        assertEquals(2, response.getCards().size());
    }


    @Test
    public void testNoTransactionElectricyPrepaid() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "prepaid";

        Date transactionDate = TRANSACTION_DATE;
        Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
        Date eventUpdateDate = CREATED_DATE;

        ViewElementInfo dateHeader = new ViewElementInfo();
        dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));

        List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
        headers.add(dateHeader);
        HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
                .setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
                .setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
                .setHeadings(headers).build();
        when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);


        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(new ArrayList<ChannelHistory>());

        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setAvailableNext(false);
        channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 0, null))
                .thenReturn(channelHistoryPage);
        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                "cylinder booking", operator)).thenReturn(null);
        HistoryView rechargeViewCard = objectMapper.readValue("{\"themeType\":\"utility_suggested_recharge\",\"createdDate\":\"28-04-2022 17:38:48\",\"amount\":0,\"orderId\":0,\"headings\":[{\"value\":\"Proceed to do an instant recharge on Paytm\",\"type\":\"header2\"}],\"cta\":[{\"value\":\"Recharge\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"}],\"operator\":\"VODAFONE IDEA\",\"service\":\"ELECTRICITY\",\"eventType\":\"RECHARGE\",\"showInfoIcon\":true,\"utility\":true}", HistoryView.class);
        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setHistoryPageRequest(historyPageRequest);
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        historyPageRequest.setPayType(paytype);
        HistoryPage response = utilityAggregatorService.aggregateHistoryInfo(historyPageRequest, null, null);

        assertEquals(response.getCards().get(0), rechargeViewCard);
    }


    @Test
    public void testTransactionElectricyPrepaid() throws Exception {
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "prepaid";

        Date transactionDate = TRANSACTION_DATE;
        Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
        Date eventUpdateDate = CREATED_DATE;

        ViewElementInfo dateHeader = new ViewElementInfo();
        dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));

        List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
        headers.add(dateHeader);
        HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
                .setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
                .setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
                .setHeadings(headers).build();
        when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);
        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767")
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        channelHistories.add(channelHistory);

        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(channelHistories);

        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setAvailableNext(false);
        channelHistoryPage.setChannelHistories(new ArrayList<>());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, channelHistories.size(), null))
                .thenReturn(channelHistoryPage);

        HistoryView rechargeViewCard = objectMapper.readValue("{\"themeType\":\"utility_recharge_success\",\"createdDate\":\"01-04-2021 14:03:29\",\"amount\":200.55,\"status\":\"SUCCESS\",\"orderId\":1,\"headings\":[{\"value\":\"200.55\",\"type\":\"header1\"},{\"value\":\"Recharge Successful\",\"type\":\"header2\"}],\"circle\":\"delhi ncr\",\"operator\":\"tata sky\",\"service\":\"electricity\",\"payType\":\"prepaid\",\"eventType\":\"RECHARGE\",\"billDate\":\"2020-03-10\",\"dueDate\":\"2021-03-01\",\"statusCode\":\"7\",\"showInfoIcon\":true,\"utility\":true}", HistoryView.class);
        HistoryView staticViewCard = objectMapper.readValue("{\"themeType\":\"utility_suggested_recharge\",\"createdDate\":\"28-04-2022 17:38:48\",\"amount\":0,\"orderId\":0,\"headings\":[{\"value\":\"Proceed to do an instant recharge on Paytm\",\"type\":\"header2\"}],\"cta\":[{\"value\":\"Recharge\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"}],\"operator\":\"VODAFONE IDEA\",\"service\":\"ELECTRICITY\",\"eventType\":\"RECHARGE\",\"showInfoIcon\":true,\"utility\":true}", HistoryView.class);
        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();

        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);
        cardInfoDto = new CardInfoDto();
        cardInfoDto.setHistoryPageRequest(historyPageRequest);
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(staticViewCard);


        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        historyPageRequest.setPayType(paytype);
        HistoryPage response = utilityAggregatorService.aggregateHistoryInfo(historyPageRequest, null, null);
        System.out.println(objectMapper.writeValueAsString(response));

        assertEquals(2, response.getCards().size());
    }

    @Test
    public void testWhenPartialPendingTXNButBillWithoutAutomatic() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";

        Date transactionDate = TRANSACTION_DATE;
        Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
        Date eventUpdateDate = CREATED_DATE;

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2021-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(0)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).build();
        reminderHistoryList.add(reminderHistory);

        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("96.50").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        channelHistories.add(channelHistory);

        when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(null);


        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(channelHistories);

        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setAvailableNext(false);
        channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 1, null))
                .thenReturn(channelHistoryPage);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                service, operator)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        System.out.println("reminderView view is " + objectMapper.writeValueAsString(reminderView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);

        cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        HistoryView rechargeView = objectMapper.readValue("{\"themeType\":\"utility_recharge_pending\",\"createdDate\":\"01-04-2021 14:03:29\",\"amount\":200.55,\"status\":\"PENDING\",\"orderId\":1,\"headings\":[{\"value\":\"200.55\",\"type\":\"header1\"},{\"value\":\"Recharge Pending\",\"type\":\"header2\"}],\"circle\":\"delhi ncr\",\"operator\":\"tata sky\",\"service\":\"electricity\",\"payType\":\"prepaid\",\"eventType\":\"RECHARGE\",\"billDate\":\"2020-03-10\",\"dueDate\":\"2022-05-06\",\"statusCode\":\"1\",\"showInfoIcon\":true,\"utility\":true}", HistoryView.class);
        System.out.println("rechargeView view is " + objectMapper.writeValueAsString(rechargeView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(rechargeView);

        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        HistoryPage response = utilityAggregatorService.aggregateHistoryInfo(historyPageRequest, null, null);
        System.out.println(objectMapper.writeValueAsString(response));
        assertEquals(2, response.getCards().size());
        assertThat(appender.getOutput(), CoreMatchers.containsString("subtracting bill amount for partial pending Transaction"));
    }


    @Test
    public void testWhenPartialSuccessTXNButBillWithoutAutomatic() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";

        Date transactionDate = TRANSACTION_DATE;
        Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
        Date eventUpdateDate = CREATED_DATE;

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2021-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(0)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).build();
        reminderHistoryList.add(reminderHistory);

        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("7").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        channelHistories.add(channelHistory);

        when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(null);


        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(channelHistories);

        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setAvailableNext(false);
        channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 1, null))
                .thenReturn(channelHistoryPage);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                service, operator)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        System.out.println("reminderView view is " + objectMapper.writeValueAsString(reminderView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);

        cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        HistoryView rechargeView = objectMapper.readValue("{\"themeType\":\"utility_bill_success\",\"createdDate\":\"01-04-2021 14:03:29\",\"amount\":200.55,\"status\":\"SUCCESS\",\"orderId\":1,\"headings\":[{\"value\":\"200.55\",\"type\":\"header1\"},{\"value\":\"Bill Paid\",\"type\":\"header2\"}],\"circle\":\"delhi ncr\",\"operator\":\"tata sky\",\"service\":\"electricity\",\"payType\":\"postpaid\",\"eventType\":\"RECHARGE\",\"billDate\":\"2020-03-10\",\"dueDate\":\"2022-05-06\",\"statusCode\":\"7\",\"showInfoIcon\":true,\"utility\":true}", HistoryView.class);
        System.out.println("rechargeView view is " + objectMapper.writeValueAsString(rechargeView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(rechargeView);

        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        HistoryPage response = utilityAggregatorService.aggregateHistoryInfo(historyPageRequest, null, null);
        System.out.println(objectMapper.writeValueAsString(response));
        assertEquals(2, response.getCards().size());

    }


    @Test
    public void testNoTxnAutomaticBill() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";

        Date transactionDate = TRANSACTION_DATE;
        Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
        Date eventUpdateDate = CREATED_DATE;

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2021-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(1)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).build();
        reminderHistoryList.add(reminderHistory);

        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("7").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        channelHistories.add(channelHistory);

        AutomaticData automaticData = JsonUtils.parseJson("{\"customerId\":1107199327,\"rechargeNumber\":\"8052270003\",\"service\":\"electricity\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"updated_at\":\"2021-09-06 12:08:02.972+0000\",\"automatic_date\":\"2022-04-30 12:08:02.972+0000\"}", AutomaticData.class);
        automaticData.setAutomaticDate(AUTOMATIC_DATE_3DAYS);
        List<AutomaticData> automaticDataList = new ArrayList<>();
        automaticDataList.add(automaticData);
        when(automaticDataRepository.findByKeyCustomerIdAndKeyRechargeNumberAndKeyServiceAndKeyOperator(customerId, rechargeNumber, service, operator)).thenReturn(automaticDataList);
        when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(null);


        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(channelHistories);

        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setAvailableNext(false);
        channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 1, null))
                .thenReturn(channelHistoryPage);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                service, operator)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        System.out.println("reminderView view is " + objectMapper.writeValueAsString(reminderView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);

        cardInfoDto = new CardInfoDto();
        cardInfoDto.setChannelHistory(channelHistory);
        HistoryView rechargeView = objectMapper.readValue("{\"themeType\":\"utility_bill_success\",\"createdDate\":\"01-04-2021 14:03:29\",\"amount\":200.55,\"status\":\"SUCCESS\",\"orderId\":1,\"headings\":[{\"value\":\"200.55\",\"type\":\"header1\"},{\"value\":\"Bill Paid\",\"type\":\"header2\"}],\"circle\":\"delhi ncr\",\"operator\":\"tata sky\",\"service\":\"electricity\",\"payType\":\"postpaid\",\"eventType\":\"RECHARGE\",\"billDate\":\"2020-03-10\",\"dueDate\":\"2022-05-06\",\"statusCode\":\"7\",\"showInfoIcon\":true,\"utility\":true}", HistoryView.class);
        System.out.println("rechargeView view is " + objectMapper.writeValueAsString(rechargeView));
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(rechargeView);

        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        HistoryPage response = utilityAggregatorService.aggregateHistoryInfo(historyPageRequest, null, null);
        ArgumentCaptor<CardInfoDto> argument = ArgumentCaptor.forClass(CardInfoDto.class);
        verify(cardManager, times(2)).getCardInfo(argument.capture());
        List<CardInfoDto> dtCardInfo = argument.getAllValues();
        ReminderHistory reminder = dtCardInfo.get(0).getReminderHistory();
        System.out.println(objectMapper.writeValueAsString(reminder) + ":" + reminder.getAutomaticData());
        assertEquals(AUTOMATIC_DATE_3DAYS, automaticData.getAutomaticDate());

    }

    @Test
    public void testFilterOutLatestStateOfOrdersWithMultipleEventTypes() throws Exception{
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";
        Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
        Date transactionDate = DateUtil.stringToDate("2022-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date transactionUpdateDate = DateUtil.stringToDate("2022-03-20 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date eventUpdateDate = DateUtil.stringToDate("2021-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        List<ChannelHistory> channelHistories = new ArrayList<>();

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2022-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("7").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        ChannelHistory channelHistory1 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-1)).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-1))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-1)).build();
        ChannelHistory channelHistory2 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-30)).setCustomerId(450471692L)
                .setEventType("MARK_AS_PAID").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-30))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-30)).build();
        ChannelHistory channelHistory3 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-60)).setCustomerId(450471692L)
                .setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-60))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-60)).build();
        channelHistories.add(channelHistory);
        channelHistories.add(channelHistory1);
        channelHistories.add(channelHistory2);
        channelHistories.add(channelHistory3);

        FilteredEvents filteredEvents = utilityAggregatorService.filterOutLatestStateOfOrders(channelHistories,finalizationDate);

        assertEquals(4,filteredEvents.getMarkFinaliseData().size());
        assertEquals(3,filteredEvents.getMoveToFinaliseData().size());
        assertEquals(3,filteredEvents.getEvents().size());


    }

    @Test
    public void testFilterOutLatestStateOfOrdersWithMultipleRechargeStates() throws Exception{
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";
        Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
        Date transactionDate = DateUtil.stringToDate("2022-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date transactionUpdateDate = DateUtil.stringToDate("2022-03-20 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date eventUpdateDate = DateUtil.stringToDate("2021-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        List<ChannelHistory> channelHistories = new ArrayList<>();

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2022-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("7").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        ChannelHistory channelHistory1 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-1)).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-1))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-1)).build();
        channelHistories.add(channelHistory);
        channelHistories.add(channelHistory1);

        FilteredEvents filteredEvents = utilityAggregatorService.filterOutLatestStateOfOrders(channelHistories,finalizationDate);

        assertEquals(2,filteredEvents.getMarkFinaliseData().size());
        assertEquals(1,filteredEvents.getMoveToFinaliseData().size());
        assertEquals(1,filteredEvents.getEvents().size());


    }

    @Test
    public void testFilterOutLatestStateOfOrdersWithMultipleNonRechargeEvents() throws Exception{
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";
        Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
        Date transactionDate = DateUtil.stringToDate("2022-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date transactionUpdateDate = DateUtil.stringToDate("2022-03-20 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date eventUpdateDate = DateUtil.stringToDate("2021-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        List<ChannelHistory> channelHistories = new ArrayList<>();

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2022-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("7").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        ChannelHistory channelHistory1 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-30)).setCustomerId(450471692L)
                .setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-30))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-30)).build();
        ChannelHistory channelHistory2 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-60)).setCustomerId(450471692L)
                .setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-60))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-60)).build();
        ChannelHistory channelHistory3 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-90)).setCustomerId(450471692L)
                .setEventType("MARK_AS_PAID").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-90))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-90)).build();
        channelHistories.add(channelHistory);
        channelHistories.add(channelHistory1);
        channelHistories.add(channelHistory2);
        channelHistories.add(channelHistory3);

        FilteredEvents filteredEvents = utilityAggregatorService.filterOutLatestStateOfOrders(channelHistories,finalizationDate);

        assertEquals(4,filteredEvents.getMarkFinaliseData().size());
        assertEquals(4,filteredEvents.getMoveToFinaliseData().size());
        assertEquals(4,filteredEvents.getEvents().size());


    }
    @Test
    public void testAutomaticEnabledCardFromReminder() throws Exception {
        List<HistoryView> historyViews = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "financial services";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";


        Product product = new Product();
        product.setProductId(4l);
        ProductAttributes attributes = new ProductAttributes();
        attributes.setBankCode("hdfc");
        attributes.setIsPaytmFirstCard("1");
        attributes.setCardNetwork("rupay");
        attributes.setSchedulable(1);
        product.setAttributes(objectMapper.writeValueAsString(attributes));
        CVRProductCache.getInstance().addProductDetails(product);

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(1)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).setIs_automatic(1).setProductId(4l).build();
        reminderHistory.setReference_id("**********");
        reminderHistoryList.add(reminderHistory);

        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setCategoryId(26L).setCircle(null).setCustomerId(customerId)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(4l).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus("7").build();
        channelHistories.add(channelHistory);

        AutomaticData automaticData = JsonUtils.parseJson("{\"customerId\":1107199327,\"rechargeNumber\":\"8052270003\",\"service\":\"electricity\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"updated_at\":\"2021-09-06 12:08:02.972+0000\",\"automatic_date\":\"2022-04-30 12:08:02.972+0000\"}", AutomaticData.class);
        automaticData.setAutomaticDate(AUTOMATIC_DATE_3DAYS);
        List<AutomaticData> automaticDataList = new ArrayList<>();
        automaticDataList.add(automaticData);
        when(automaticDataRepository.findByKeyCustomerIdAndKeyRechargeNumberAndKeyServiceAndKeyOperator(customerId, rechargeNumber, service, operator)).thenReturn(automaticDataList);
        when(dateCardService.addDateInfoCard(null, null, true)).thenReturn(null);
        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber,
                service)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        historyViews.add(reminderView);
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);

        AutomaticCardView automaticCard = objectMapper.readValue("{\"themeType\":\"chat_automatic_enabled\",\"headings\":[{\"value\":\"Automatic Payment Active\",\"type\":\"header1\"}],\"cta\":[{\"value\":\"Manage\",\"type\":\"cta1\",\"actionType\":\"automaticManaged\"}]}",AutomaticCardView.class);
        when(automaticCardService.getAutomaticCard(1)).thenReturn(automaticCard);

        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);
        historyPageRequest.setProductId(4l);


        AutomaticCardView automaticCardView = utilityAggregatorService.addReminderHistoryCard(historyPageRequest,historyViews,channelHistories,null, null);
        assertNotNull(automaticCardView);
        assertEquals(Constants.AUTOMATIC_ENABLED,automaticCardView.getHeadings().get(0).getValue());
    }
    //    @Test
//    public void testAutomaticDisabledCardFromReminder() throws Exception {
//        List<HistoryView> historyViews = new ArrayList<>();
//        ObjectMapper objectMapper = new ObjectMapper();
//        Long customerId = 1008952334L;
//        String rechargeNumber = "**********";
//        String service = "electricity";
//        String operator = "hubli electricity supply company ltd. (hescom)";
//        String paytype = "postpaid";
//
//        String attributes = "{\"schedulable\":\"1\"}";
//        Product product = new Product();
//        product.setProductId(4l);
//        product.setAttributes(attributes);
//
//        CVRProductCache.getInstance().addProductDetails(product);
//
//        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
//        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
//                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(1)
//                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
//                .setService(service).setStatus(1).setUpdatedAt(new Date()).setIs_automatic(0).setProductId(4l).build();
//        reminderHistoryList.add(reminderHistory);
//
//        List<ChannelHistory> channelHistories = new ArrayList<>();
//
//        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20")
//                .setCategoryId(26L).setCircle(null).setCustomerId(customerId)
//                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
//                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
//                .setPaytype(paytype).setProductId(4l).setRechargeNumber(rechargeNumber)
//                .setService(service).setStatus("7").build();
//        channelHistories.add(channelHistory);
//
//        AutomaticData automaticData = JsonUtils.parseJson("{\"customerId\":1107199327,\"rechargeNumber\":\"8052270003\",\"service\":\"electricity\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"updated_at\":\"2021-09-06 12:08:02.972+0000\",\"automatic_date\":\"2022-04-30 12:08:02.972+0000\"}", AutomaticData.class);
//        automaticData.setAutomaticDate(AUTOMATIC_DATE_3DAYS);
//        List<AutomaticData> automaticDataList = new ArrayList<>();
//        automaticDataList.add(automaticData);
//        when(automaticDataRepository.findByKeyCustomerIdAndKeyRechargeNumberAndKeyServiceAndKeyOperator(customerId, rechargeNumber, service, operator)).thenReturn(automaticDataList);
//        when(dateCardService.addDateInfoCard(null, null, true)).thenReturn(null);
//        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
//                service, operator)).thenReturn(reminderHistoryList);
//        CardInfoDto cardInfoDto = new CardInfoDto();
//        cardInfoDto.setReminderHistory(reminderHistory);
//        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
//        historyViews.add(reminderView);
//        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);
//
//        AutomaticCardView automaticCard = objectMapper.readValue("{\"themeType\":\"chat_automatic_disabled\",\"headings\":[{\"value\":\"Automatic Payment Inactive\",\"type\":\"header1\"}],\"cta\":[{\"value\":\"Set up\",\"type\":\"cta1\",\"actionType\":\"automaticSetup\"}]}",AutomaticCardView.class);
//        when(automaticCardService.getAutomaticCard(0)).thenReturn(automaticCard);
//
//        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
//        historyPageRequest.setService(service);
//        historyPageRequest.setRecharge_number(rechargeNumber);
//        historyPageRequest.setOperator(operator);
//        historyPageRequest.setCustomerId(customerId);
//
//        AutomaticCardView automaticCardView = utilityAggregatorService.addReminderHistoryCard(historyPageRequest,historyViews,channelHistories,null);
//        assertNotNull(automaticCardView);
//        assertEquals(Constants.AUTOMATIC_DISABLED,automaticCardView.getHeadings().get(0).getValue());
//    }
    @Test
    public void testAutomaticEnabledCardFromRecents() throws Exception {
        List<HistoryView> historyViews = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";

        String attributes = "{\"schedulable\":\"1\"}";
        Product product = new Product();
        product.setProductId(4l);

        product.setAttributes(attributes);

        CVRProductCache.getInstance().addProductDetails(product);

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(0.0).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(1)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).setIs_automatic(0).setProductId(4l).build();;
        reminderHistoryList.add(reminderHistory);
        List<Recents> recents = new ArrayList<>();
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setOperator(operator);
        recentsPrimaryKey.setCustomerId(customerId);
        recentsPrimaryKey.setRechargeNumber(rechargeNumber);
        recent.setKey(recentsPrimaryKey);
        recent.setAutomaticStatus(1);
        recent.setProductId(4l);
        recents.add(recent);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(customerId,service,rechargeNumber)).thenReturn(recents);

        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setCategoryId(26L).setCircle(null).setCustomerId(customerId)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L).setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(4l).setRechargeNumber(rechargeNumber).setService(service).setStatus("7").build();
        channelHistories.add(channelHistory);

        AutomaticData automaticData = JsonUtils.parseJson("{\"customerId\":1107199327,\"rechargeNumber\":\"8052270003\",\"service\":\"electricity\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"updated_at\":\"2021-09-06 12:08:02.972+0000\",\"automatic_date\":\"2022-04-30 12:08:02.972+0000\"}", AutomaticData.class);
        automaticData.setAutomaticDate(AUTOMATIC_DATE_3DAYS);
        List<AutomaticData> automaticDataList = new ArrayList<>();
        automaticDataList.add(automaticData);
        when(automaticDataRepository.findByKeyCustomerIdAndKeyRechargeNumberAndKeyServiceAndKeyOperator(customerId, rechargeNumber, service, operator)).thenReturn(automaticDataList);
        when(dateCardService.addDateInfoCard(null, null, true)).thenReturn(null);
        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                service, operator)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        historyViews.add(reminderView);
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);

        AutomaticCardView automaticCard = objectMapper.readValue("{\"themeType\":\"chat_automatic_enabled\",\"headings\":[{\"value\":\"Automatic Payment Active\",\"type\":\"header1\"}],\"cta\":[{\"value\":\"Manage\",\"type\":\"cta1\",\"actionType\":\"automaticManaged\"}]}",AutomaticCardView.class);
        when(automaticCardService.getAutomaticCard(recent.getAutomaticStatus())).thenReturn(automaticCard);

        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);


        AutomaticCardView automaticCardView = utilityAggregatorService.addReminderHistoryCard(historyPageRequest,historyViews,channelHistories,null, null);
        assertNotNull(automaticCardView);
        assertEquals(Constants.AUTOMATIC_ENABLED,automaticCardView.getHeadings().get(0).getValue());
    }
    @Test
    public void testAutomaticWhenIsSchedulableFalse() throws Exception {
        List<HistoryView> historyViews = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "**********";
        String service = "electricity";
        String operator = "hubli electricity supply company ltd. (hescom)";
        String paytype = "postpaid";

        String attributes = "{\"schedulable\":\"0\"}";
        Product product = new Product();
        product.setProductId(1l);
        product.setAttributes(attributes);

        CVRProductCache.getInstance().addProductDetails(product);

        List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
                .setCreated_at(new Date()).setCustomerId(customerId).setDue_date(DUE_DATE_5DAYS).setIs_automatic(1)
                .setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
                .setService(service).setStatus(1).setUpdatedAt(new Date()).setIs_automatic(0).setProductId(1l).build();;
        reminderHistoryList.add(reminderHistory);
        List<Recents> recents = new ArrayList<>();
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setOperator(operator);
        recentsPrimaryKey.setCustomerId(customerId);
        recentsPrimaryKey.setRechargeNumber(rechargeNumber);
        recent.setKey(recentsPrimaryKey);
        recent.setAutomaticStatus(0);
        recents.add(recent);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(customerId,service,rechargeNumber)).thenReturn(recents);

        List<ChannelHistory> channelHistories = new ArrayList<>();

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setCategoryId(26L).setCircle(null).setCustomerId(customerId)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L).setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(1l).setRechargeNumber(rechargeNumber).setService(service).setStatus("7").build();
        channelHistories.add(channelHistory);

        AutomaticData automaticData = JsonUtils.parseJson("{\"customerId\":1107199327,\"rechargeNumber\":\"8052270003\",\"service\":\"electricity\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"updated_at\":\"2021-09-06 12:08:02.972+0000\",\"automatic_date\":\"2022-04-30 12:08:02.972+0000\"}", AutomaticData.class);
        automaticData.setAutomaticDate(AUTOMATIC_DATE_3DAYS);
        List<AutomaticData> automaticDataList = new ArrayList<>();
        automaticDataList.add(automaticData);
        when(automaticDataRepository.findByKeyCustomerIdAndKeyRechargeNumberAndKeyServiceAndKeyOperator(customerId, rechargeNumber, service, operator)).thenReturn(automaticDataList);
        when(dateCardService.addDateInfoCard(null, null, true)).thenReturn(null);
        when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
                .thenReturn(channelHistories);
        when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
                service, operator)).thenReturn(reminderHistoryList);
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setReminderHistory(reminderHistory);
        HistoryView reminderView = objectMapper.readValue("{\"themeType\":\"utility_bill_due\",\"createdDate\":\"02-05-2022 13:06:55\",\"amount\":200,\"orderId\":0,\"headings\":[{\"value\":\"200\",\"type\":\"header1\"},{\"value\":\"Bill Due\",\"type\":\"header2\"}],\"displayValues\":[{\"value\":\"09 Jan\",\"key\":\"Bill Date\",\"type\":\"ds1\"},{\"value\":\"Fri 06 May\",\"key\":\"Due Date\",\"type\":\"ds2\"}],\"cta\":[{\"value\":\"Pay Now\",\"type\":\"cta1\",\"actionType\":\"retry_recharge\"},{\"value\":\"Mark as Paid\",\"type\":\"cta2\",\"actionType\":\"mark_as_paid\"}],\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"service\":\"electricity\",\"eventType\":\"BILL\",\"billDate\":\"2021-01-09\",\"dueDate\":\"2022-05-06\",\"showInfoIcon\":false,\"utility\":true}", HistoryView.class);
        historyViews.add(reminderView);
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(reminderView);

        AutomaticCardView automaticCard = objectMapper.readValue("{\"themeType\":\"chat_automatic_disabled\",\"headings\":[{\"value\":\"Automatic Payment Inactive\",\"type\":\"header1\"}],\"cta\":[{\"value\":\"Set up\",\"type\":\"cta1\",\"actionType\":\"automaticSetup\"}]}",AutomaticCardView.class);
        when(automaticCardService.getAutomaticCard(recent.getAutomaticStatus())).thenReturn(automaticCard);

        GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
        historyPageRequest.setService(service);
        historyPageRequest.setRecharge_number(rechargeNumber);
        historyPageRequest.setOperator(operator);
        historyPageRequest.setCustomerId(customerId);


        AutomaticCardView automaticCardView = utilityAggregatorService.addReminderHistoryCard(historyPageRequest,historyViews,channelHistories,null, null);
        assertNull(automaticCardView);
    }

}
