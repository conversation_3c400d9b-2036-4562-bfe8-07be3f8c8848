package com.paytm.saga.service;

import com.paytm.saga.common.configuration.property.EsPropertiesConfig;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.dto.*;
import com.paytm.saga.listeners.OMSListeners;
import com.paytm.saga.service.external.EsService;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static junit.framework.TestCase.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class ESServiceTest {
    @Mock
    EsRestTemplateService restTemplateService;
    @Mock
    EsPropertiesConfig esPropertiesConfig;
    @Mock
    ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
    @InjectMocks
    EsService esService;

    @Mock
    DateUtil dateUtil;

    @InjectMocks
    OMSListeners omsListeners;
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        String config = "{\"disableDropOff\": false, \"categoryMapping\": {\"mobile\": [17, 21], \"financial services\": [131655]}, \"disableChatHistory\": false, \"smsEnabledServices\": [\"financial services\"], \"agentIdentificationLimit\": 2, \"smartReminderPrepaidEndDays\": 3, \"smartReminderPostpaidEndDays\": 3, \"smartReminderPrepaidStartDays\": 5, \"smartReminderCustomerBillLimit\": 60, \"recentDataUpdateAllowedServices\": [\"dth\", \"financial services\", \"tuition fee\",\"mobile\",\"electricity\"]}";
        try{
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e){
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }

    @Test
    public void testEsQuery(){
        List<Long> ordersId = new ArrayList<>();
        ordersId.add(20461215121L);
        ordersId.add(20529864362L);
        ordersId.add(20507009180L);
       String actualRes = esService.getEsRequest(ordersId);
       String exptectedRes="{\"size\":3,\"query\":{\"bool\":{\"should\":[{\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20461215121\"}},{\"match\":{\"inStatusMap_responseCode\":\"00\"}}]}}, {\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20461215121\"}},{\"match\":{\"inStatusMap_responseCode\":\"11\"}}]}}, {\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20461215121\"}},{\"match\":{\"productInfo_paytype\":\"credit card\"}},{\"match\":{\"inStatusMap_responseCode\":\"08\"}}]}}, {\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20529864362\"}},{\"match\":{\"inStatusMap_responseCode\":\"00\"}}]}}, {\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20529864362\"}},{\"match\":{\"inStatusMap_responseCode\":\"11\"}}]}}, {\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20529864362\"}},{\"match\":{\"productInfo_paytype\":\"credit card\"}},{\"match\":{\"inStatusMap_responseCode\":\"08\"}}]}}, {\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20507009180\"}},{\"match\":{\"inStatusMap_responseCode\":\"00\"}}]}}, {\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20507009180\"}},{\"match\":{\"inStatusMap_responseCode\":\"11\"}}]}}, {\"bool\":{\"must\":[{\"match\":{\"orderInfo_order_id\":\"20507009180\"}},{\"match\":{\"productInfo_paytype\":\"credit card\"}},{\"match\":{\"inStatusMap_responseCode\":\"08\"}}]}}]}}, \"collapse\": { \"field\": \"orderInfo_order_id\" }}";
        assertEquals(exptectedRes,actualRes);
    }

    @Test
    public void testEsQuery1() throws Exception {
        PaymentTxnModel paymentTxnModel = new PaymentTxnModel();
        paymentTxnModel.setCategoryId(123L);
        paymentTxnModel.setCustomerId(123L);
        paymentTxnModel.setOrderId(123L);
        ArrayList<String> states = new ArrayList<>();
        states.add("00");
        states.add("07");
//        esService.getEsDetailsForOrderCount(paymentTxnModel);
        String actualRes = esService.getEsRequestForOrderCount(paymentTxnModel,states);
        omsListeners.upsertTxnCount(getOMSResponseModelMock());
        String exptectedRes= "{\n" +
                "    \"size\": 10000,\n" +
                "    \"_source\": [\"orderInfo_order_id\"],\n" +
                "    \"query\": {\n" +
                "        \"bool\": {\n" +
                "            \"must\": [\n" +
                "                {\n" +
                "                    \"term\": {\n" +
                "                        \"customerInfo_customer_id\": \"123\"\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"term\": {\n" +
                "                        \"productInfo_categoryId\": \"123\"\n" +
                "                    }\n" +
                "                }\n" +
                "            ],\n" +
                "            \"must_not\": [\n" +
                "                {\n" +
                "                    \"terms\": {\n" +
                "                        \"inStatusMap_responseCode\": [\"00\", \"07\"]\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    }\n" +
                "}";

        System.out.println("exxx "+exptectedRes);
                System.out.println(actualRes);
        assertEquals(exptectedRes,actualRes);
    }
    private OMSResponseModel getOMSResponseModelMock(){

        OMSResponseModel omsResponseModel = new OMSResponseModel();
        omsResponseModel.setCustomerId(1231314L);
        omsResponseModel.setOrderId(231333L);
        omsResponseModel.setPayment_status(3);
        omsResponseModel.setChannelId("WEB 2");

        OMSItemModel omsItemModel = new OMSItemModel();
        omsItemModel.setItemId(1233111L);
        omsItemModel.setMrp("5");
        omsItemModel.setVerticalId(4);
        omsItemModel.setStatus(7L);
        omsItemModel.setOrderId(231333L);
        omsItemModel.setPrice(10.5);
        omsItemModel.setFulfillmentReq("{\"price\":20,\"recharge_number\":\"123456789013\"}");

        OMSItemProductModel omsItemProductModel = new OMSItemProductModel();
        OMSItemAttributeModel omsItemAttributeModel = new OMSItemAttributeModel();
        omsItemAttributeModel.setCircle("delhi-ncr");
        omsItemAttributeModel.setOperator("airtel");
        omsItemAttributeModel.setPaytype("postpaid");
        omsItemAttributeModel.setService("mobile");

        omsItemProductModel.setProductId(12331L);
        omsItemProductModel.setCategoryId(11123L);
        omsItemProductModel.setAttributes(omsItemAttributeModel);

        omsItemModel.setProduct(omsItemProductModel);
//        omsItemModel.setCreatedAt(dateString);
//        omsItemModel.setUpdatedAt(dateString);

        List<OMSItemModel> omsItemModels = new ArrayList<OMSItemModel>();

        omsItemModels.add(omsItemModel);
        omsResponseModel.setItems(omsItemModels);
        return omsResponseModel;

    }

}
