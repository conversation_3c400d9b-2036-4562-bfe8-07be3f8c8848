package com.paytm.saga.service;

import com.paytm.saga.dto.FeatureConfigCache;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.*;
import static com.paytm.saga.common.constant.EncryptionConstants.*;
import static org.junit.Assert.*;

public class EncryptionDecisionTest {

	@Mock
	private FeatureConfigCache featureConfigCache;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testIsDataEncryptionRequired_GlobalFlagEnabled() {
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, true);
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);

		assertTrue(EncryptionDecision.isDataEncryptionRequired(123L));
	}

	@Test
	public void testIsDataEncryptionRequired_CustomerFlagEnabled_CustomerIdPresent() {
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, false);
		configMap.put(ENABLE_DATA_ENCRYPTION_ON_CUSTOMER_FLAG, true);
		configMap.put(ENCRYPTION_ON_CUSTOMERIDS, new HashSet<>(Arrays.asList(123L, 456L)));
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);
		assertTrue(EncryptionDecision.isDataEncryptionRequired(123L));
	}

	@Test
	public void testIsDataEncryptionRequired_CustomerFlagEnabled_CustomerIdNotPresent() {
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, false);
		configMap.put(ENABLE_DATA_ENCRYPTION_ON_CUSTOMER_FLAG, true);
		configMap.put(ENCRYPTION_ON_CUSTOMERIDS, new HashSet<>(Arrays.asList(125L, 456L)));
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);
		assertFalse(EncryptionDecision.isDataEncryptionRequired(123L));
	}

	@Test
	public void testIsDataEncryptionRequired_NoFlagsEnabled() {
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, false);
		configMap.put(ENABLE_DATA_ENCRYPTION_ON_CUSTOMER_FLAG, false);
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);
		assertFalse(EncryptionDecision.isDataEncryptionRequired(123L));
	}
}
