package com.paytm.saga.service.external;

import com.paytm.saga.dto.BillsSyncMarkAsPaidRequest;
import com.paytm.saga.service.external.BillsSyncService;
import com.paytm.saga.service.GenericRestClient;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.BillsSyncMarkAsPaidResponse;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.mockito.Mockito.*;

public class BillsSyncServiceTest {

    @InjectMocks
    private BillsSyncService billsSyncService;

    @Mock
    private GenericRestClient genericRestClient;

    @Mock
    private ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;

    @Mock
    private MetricsHelper metricsHelper;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    private BillsSyncMarkAsPaidResponse createMockBillsSyncMarkAsPaidSuccessResponse(){
        BillsSyncMarkAsPaidResponse billsSyncMarkAsPaidResponse = new BillsSyncMarkAsPaidResponse();
        billsSyncMarkAsPaidResponse.setRowUpdated("1");
        billsSyncMarkAsPaidResponse.setMessage("Record Marked as Paid for future use");
        return billsSyncMarkAsPaidResponse;
    }

    private BillsSyncMarkAsPaidRequest createMockBillsSyncMarkAsPaidRequestBody(){
        return new BillsSyncMarkAsPaidRequest("123456","7363","mobile","airtel");

    }

    @Test
    public void testMarkAsPaidTaskWithSuccessBillsSyncResponse() throws Exception {
        String rechargeNumber = "123456";
        Long customerId = 7363L;
        String service = "mobile";
        String operator = "airtel";
        BillsSyncMarkAsPaidRequest billsSyncMarkAsPaidRequest = createMockBillsSyncMarkAsPaidRequestBody();
        when(genericRestClient.post(any(),eq(billsSyncMarkAsPaidRequest),eq(BillsSyncMarkAsPaidResponse.class))).thenReturn(ResponseEntity.ok(createMockBillsSyncMarkAsPaidSuccessResponse()));

        billsSyncService.markAsPaidTask(rechargeNumber, customerId, service, operator).call();

        verify(genericRestClient, times(1)).post(any(), any(), any());
        verify(metricsHelper, times(1)).recordSuccessRate(anyString());
    }

    @Test
    public void testMarkAsPaidTaskWithException() throws Exception {
        String rechargeNumber = "123456";
        Long customerId = 1L;
        String service = "service";
        String operator = "operator";

        when(genericRestClient.post(anyString(), any(), any())).thenThrow(new RuntimeException());

        try {
            billsSyncService.markAsPaidTask(rechargeNumber, customerId, service, operator).call();
        } catch (Exception e) {
            verify(genericRestClient, times(1)).post(anyString(), any(), any());
            verify(metricsHelper, times(1)).recordErrorRate(anyString());
        }
    }

}