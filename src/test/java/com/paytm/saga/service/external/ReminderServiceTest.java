package com.paytm.saga.service.external;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.common.exception.SmsCardException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.GetSmsCardsResponse;
import com.paytm.saga.dto.SmsCard;
import com.paytm.saga.service.ReminderRestTemplateService;
import com.paytm.saga.service.SmsCardsRestTemplateService;
import com.paytm.saga.util.JsonUtils;

import junit.framework.TestCase;

@RunWith(MockitoJUnitRunner.class)
public class ReminderServiceTest extends TestCase {
	@InjectMocks
	ReminderService reminderService;
	@Mock
	ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
	@Mock
	ReminderRestTemplateService restTemplateService;
	@Mock
	SmsCardsRestTemplateService smsCardsRestTemplateService;
	@Mock
	MetricsHelper metricsHelper;
	private Long customerId;
	@Rule
	public ExpectedException exceptionRule = ExpectedException.none();

	@Before
	public void setup() {
		customerId = 11065108L;
	}

	private GetSmsCardsResponse getSMSCardResponseMockResponseForSingleService() {
		String s = "{\"status\":200,\"data\":{\"financial services\":[{\"id\":9,\"customer_id\":111,\"recharge_number\":\"XXXX XXXX XXXX 1234\",\"product_id\":112,\"operator\":\"hdfc\",\"amount\":400,\"due_date\":\"2018-11-28T06:14:02.000Z\",\"bill_fetch_date\":\"2018-11-20T12:31:12.000Z\",\"paytype\":\"credit card\",\"service\":\"financial services\",\"customer_mobile\":\"**********\",\"customer_email\":\"<EMAIL>\",\"status\":7,\"createdAt\":\"2018-11-20T11:16:28.000Z\",\"updatedAt\":\"2018-11-28T06:14:02.000Z\",\"billDate\":\"2018-11-28T06:14:02.000Z\",\"notification_status\":1,\"card_network\":\"BLANK\",\"bank_name\":\"hdfc\",\"customerOtherInfo\":{\"currentBillAmount\":100}}]}}";
		return JsonUtils.parseJson(s, GetSmsCardsResponse.class);
	}

	private GetSmsCardsResponse getSMSCardResponseMockResponseForMultipleService() {
		String s = "{\"status\":200,\"data\":{\"financial services\":[{\"id\":9,\"customer_id\":111,\"recharge_number\":\"XXXX XXXX XXXX 1234\",\"product_id\":112,\"operator\":\"hdfc\",\"amount\":400,\"due_date\":\"2018-11-28T06:14:02.000Z\",\"bill_fetch_date\":\"2018-11-20T12:31:12.000Z\",\"paytype\":\"credit card\",\"service\":\"financial services\",\"customer_mobile\":\"**********\",\"customer_email\":\"<EMAIL>\",\"status\":7,\"createdAt\":\"2018-11-20T11:16:28.000Z\",\"updatedAt\":\"2018-11-28T06:14:02.000Z\",\"billDate\":\"2018-11-28T06:14:02.000Z\",\"notification_status\":1,\"card_network\":\"BLANK\",\"bank_name\":\"hdfc\",\"customerOtherInfo\":{\"currentBillAmount\":100}}],\"mobile\":[{\"id\":9,\"customer_id\":111,\"recharge_number\":\"XXXX XXXX XXXX 2211\",\"product_id\":112,\"operator\":\"hdfc\",\"amount\":400,\"due_date\":\"2018-11-28T06:14:02.000Z\",\"bill_fetch_date\":\"2018-11-20T12:31:12.000Z\",\"paytype\":\"credit card\",\"service\":\"mobile\",\"customer_mobile\":\"**********\",\"customer_email\":\"<EMAIL>\",\"status\":7,\"createdAt\":\"2018-11-20T11:16:28.000Z\",\"updatedAt\":\"2018-11-28T06:14:02.000Z\",\"billDate\":\"2018-11-28T06:14:02.000Z\",\"notification_status\":1,\"card_network\":\"BLANK\",\"bank_name\":\"hdfc\",\"customerOtherInfo\":{\"currentBillAmount\":100}}]}}";
		return JsonUtils.parseJson(s, GetSmsCardsResponse.class);
	}

	private GetSmsCardsResponse getSMSCardResponseMockResponseNoCard() {
		String s = "{\"status\":204,\"error\":\"No Data found!!!\"}";
		return JsonUtils.parseJson(s, GetSmsCardsResponse.class);
	}

	@Test
	public void getSmsCardsWithValidCardsWithSingleService() throws SmsCardException, RestTemplateServiceException {
		when(smsCardsRestTemplateService.executeGetRequest(any(), any(), any(), any()))
				.thenReturn(getSMSCardResponseMockResponseForSingleService());

		List<SmsCard> savedCards = reminderService.fetchSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNotNull(savedCards);
		assertTrue(savedCards.size() > 0);
	}

	@Test
	public void getSmsCardsWithValidCardsWithMultipleService() throws SmsCardException, RestTemplateServiceException {
		when(smsCardsRestTemplateService.executeGetRequest(any(), any(), any(), any()))
				.thenReturn(getSMSCardResponseMockResponseForMultipleService());
		List<String> services = new ArrayList<>();
		services.add("financial services");
		services.add("mobile");
		List<SmsCard> savedCards = reminderService.fetchSmsCards(customerId, services);
		assertNotNull(savedCards);
		assertEquals(2,savedCards.size());
		assertEquals("financial services",savedCards.get(0).getService());
		assertEquals("XXXX XXXX XXXX 1234",savedCards.get(0).getRecharge_number());
		assertEquals("mobile",savedCards.get(1).getService());
		assertEquals("XXXX XXXX XXXX 2211",savedCards.get(1).getRecharge_number());

	}

	@Test
	public void getSmsCardsWithNoCards() throws SmsCardException, RestTemplateServiceException {
		when(smsCardsRestTemplateService.executeGetRequest(any(), any(), any(), any()))
				.thenReturn(getSMSCardResponseMockResponseNoCard());

		List<SmsCard> savedCards = reminderService.fetchSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNull(savedCards);
	}

	@Test(expected = SmsCardException.class)
	public void getSmsCardsWithNullResponse() throws SmsCardException, RestTemplateServiceException {
		when(smsCardsRestTemplateService.executeGetRequest(any(), any(), any(), any())).thenReturn(null);
		reminderService.fetchSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
	}

}
