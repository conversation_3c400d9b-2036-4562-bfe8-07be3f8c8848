package com.paytm.saga.service.external;

import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.configuration.property.PgPropertiesConfig;
import com.paytm.saga.common.exception.ErrorMessages;
import com.paytm.saga.common.exception.PgServiceException;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.dto.PgSavedCardResponse;
import com.paytm.saga.dto.SavedCardDetails;
import com.paytm.saga.service.PgRestTemplateService;
import com.paytm.saga.util.JsonUtils;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PGServiceTest extends TestCase {

    @InjectMocks
    PGService pgService;

    @Mock
    PgRestTemplateService restTemplateService;

    @Mock
    PgPropertiesConfig pgPropertiesConfig;

    @Mock
    ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;

    private Long customerId;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Before
    public void setup() {
        customerId = 11065108L;
    }

    @Test
    public void getSavedCardsWithValidCards() throws PgServiceException, RestTemplateServiceException {
        when(restTemplateService.executePostRequest(any(),any(),any())).thenReturn(getPgSavedCardResponseMockResponse());
        when(pgPropertiesConfig.getSecretKey()).thenReturn(String.valueOf("dummy-key"));

        List<SavedCardDetails> savedCards = pgService.getSavedCards(customerId, false);
        assertNotNull(savedCards);
        assertTrue(savedCards.size() > 0);
    }

    @Test
    public void getSavedCardsWithNullSecret(){
        when(pgPropertiesConfig.getSecretKey()).thenReturn(null);

        try{
            List<SavedCardDetails> savedCards = pgService.getSavedCards(customerId, false);
        } catch (PgServiceException e) {
            Assert.assertEquals(e.getMessage(),"java.lang.IllegalArgumentException: The Secret cannot be null");
        }
    }

    @Test
    public void getSavedCardsWithFailedResponse() throws RestTemplateServiceException {
        when(restTemplateService.executePostRequest(any(),any(),any())).thenReturn(getPgSavedCardFailedResponseMockResponse());
        when(pgPropertiesConfig.getSecretKey()).thenReturn(String.valueOf("dummy-key"));
        try {
            pgService.getSavedCards(customerId, false);
        } catch (PgServiceException e) {
            Assert.assertEquals(e.getMessage(), ErrorMessages.PG_SAVED_CARDS_MALFORMED_RESPONSE_ERROR.getErrorMessage());
        }
    }

    @Test(expected=PgServiceException.class)
    public void getSavedCardsWithPgApiFailure() throws RestTemplateServiceException, PgServiceException{
        when(restTemplateService.executePostRequest(any(),any(),any())).thenThrow(RestTemplateServiceException.class);
        when(pgPropertiesConfig.getSecretKey()).thenReturn(String.valueOf("dummy-key"));
        pgService.getSavedCards(customerId, false);
    }

    private PgSavedCardResponse getPgSavedCardResponseMockResponse() {
        String s = "{\"responseStatus\":\"SUCCESS\",\"httpCode\":\"200\",\"httpSubCode\":\"200\",\"codeDetail\":\"Success\",\"response\":[{\"savedCardId\":\"618b6273d83f4545dc117294\",\"cardScheme\":\"VISA\",\"expiryDate\":\"082022\",\"issuingBankName\":\"CITI\",\"issuingBankCardVariant\":\"Regalia\",\"cardSuffix\":\"3216\",\"cardType\":\"CC\",\"displayName\":\"CITI\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013021295362620166880000\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\"},{\"savedCardId\":\"************* c7ef9cd6628a76c1937bf1393d8a414b\",\"cardScheme\":\"VISA\",\"expiryDate\":\"102022\",\"issuingBankName\":\"CITI\",\"issuingBankCardVariant\":\"Regalia\",\"cardSuffix\":\"0456\",\"cardType\":\"DC\",\"displayName\":\"CITI\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"452055\",\"issuerCode\":\"UNI\",\"isEligibleForCoft\":true}]}";
        return JsonUtils.parseJson(s, PgSavedCardResponse.class);
    }

    private PgSavedCardResponse getPgSavedCardFailedResponseMockResponse() {
        String s = "{\"responseStatus\":\"FAILURE\",\"httpCode\":\"100\",\"httpSubCode\":\"100\",\"codeDetail\":\"Failure\"}]}";
        return JsonUtils.parseJson(s, PgSavedCardResponse.class);
    }
}