package com.paytm.saga.service;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FetchRecentsRequest;
import com.paytm.saga.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Map;

import static org.mockito.Mockito.*;

public class FetchRecentsServiceTest {
    @InjectMocks
    FetchRecentsService fetchRecentsService;
    @Mock
    RecentsServiceImpl recentService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testFetchRecentsByCustId() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2l);
        ArgumentCaptor<FetchRecentsRequest> argument = ArgumentCaptor.forClass(FetchRecentsRequest.class);

        fetchRecentsService.fetchRecents(fetchRecentsRequest);
        verify(recentService,times(1)).fetchRecentsByCustomerId(argument.capture());
        verify(recentService,times(0)).fetchRecentsByRechargeNumber(argument.capture());


    }
    @Test
    public void testFetchRecentsByRechargeNumber() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setRechargeNumber("23413453");
        ArgumentCaptor<FetchRecentsRequest> argument = ArgumentCaptor.forClass(FetchRecentsRequest.class);

        fetchRecentsService.fetchRecents(fetchRecentsRequest);
        verify(recentService,times(1)).fetchRecentsByRechargeNumber(argument.capture());
        verify(recentService,times(0)).fetchRecentsByCustomerId(argument.capture());
    }

    @Test
    public void testFetchRecentsWhenRequestIsNull() throws RechargeSagaBaseException {
        ArgumentCaptor<FetchRecentsRequest> argument = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        fetchRecentsService.fetchRecents(null);
        verify(recentService,times(0)).fetchRecentsByRechargeNumber(argument.capture());
        verify(recentService,times(0)).fetchRecentsByCustomerId(argument.capture());
    }
}
