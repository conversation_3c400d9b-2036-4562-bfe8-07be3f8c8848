package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.exception.*;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.CustomerBillDao;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.dto.cdc.LongEntity;
import com.paytm.saga.dto.cdc.ReminderAfter;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.dto.cdc.StringEntity;
import com.paytm.saga.model.BillerAccountKafkaModel;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.RecentUtils;
import com.paytm.saga.util.TTLUtils;
import junit.framework.TestCase;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.paytm.saga.common.constant.Constants.EVENTSOURCE_SMS;
import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class RecentsServiceTest extends TestCase {
    @Mock
    RecentsRepositoryWrapperService recentsRepository;
    @Mock
    SmartRecentsRepository smartRecentsRepository;

    @Mock
    RecentDataToKafkaService recentDataToKafkaService;

    @Mock ServiceConfig serviceConfig;
    @Mock MetricsHelper metricsHelper;
    @Mock MappingService mappingService;
    @Mock CustomerBillDao customerBillDao;
    @Mock KafkaProducerService kafkaProducerService;
    @Mock SmartRecentsService smartRecentsService;

    @InjectMocks
    RecentService recentService;

    private ReminderResponseModel getReminderKafkaResponseMock(){

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();

        reminderResponseModel.setTable("bill_icici");
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderData.setAmount(100.0);
        reminderData.setRecharge_number("1231111");
        reminderData.setCustomer_id(1L);
        reminderData.setPaymentDate("2021-03-05 05:44:11");

        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(99.0);

        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(oldData);

        return reminderResponseModel;
    }
    private Recents mockRecentsData(){
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(1l);
        recents.getKey().setOperator("ccbp");
        recents.getKey().setService("financial services");
        recents.getKey().setRechargeNumber("123");
        recents.getKey().setPlanBucket("");
        recents.setPayType("credit card");
        recents.setMcn("XXXX XXXX XXXX 1234");
        recents.setProductId(1232l);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setCreatedAt(DateUtil.stringToDate("2023-02-27 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        return recents;
    }

    @Before
    public void setup() {
        String config = "{\"billerDecryptionKey\":\"Zi5F0boOKlfZiq0Bs4I7CzY+Y+FOh7OXS2O3paEITTg=\",\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"newAccountServices\": [\"mobile\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        MockitoAnnotations.initMocks(this);
        Product product=new Product();
        product.setOperator("axis");
        product.setProductId(1201258433L);
        CVRProductCache.getInstance().addProductDetails(product);

    }
    @Test
    public void testReminderNotPaidOnPaytm(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setExtra("{\"last_paid_amount\":1000.00}");
        reminderResponseModel.getData().setStatus(14);
        reminderResponseModel.getData().setPaymentDate("2023-01-01 13:42:23");

        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setLastPaidAmount(100.00);
        reminderHistory.setStatus(14);
        reminderHistory.setPaytype(Constants.CREDIT_CARD_PAYTYPE);
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1l);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1231111");
        recents.setKey(recentsPrimaryKey);
        recentService.reminderCheckForNotPaidOnPaytm(recents,reminderHistory,reminderResponseModel);
        assertEquals("1",String.valueOf(recents.getNotPaidOnPaytm()));
        assertEquals(reminderHistory.getLastPaidAmount(),recents.getTxnAmount());

    }
    @Test
    public void testReminderNotPaidOnPaytmWrongStatus(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setExtra("{\"last_paid_amount\":1000.00}");
        reminderResponseModel.getData().setStatus(14);
        reminderResponseModel.getData().setPaymentDate("2023-01-01 13:42:23");

        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setLastPaidAmount(100.00);
        reminderHistory.setStatus(4);
        reminderHistory.setPaytype(Constants.CREDIT_CARD_PAYTYPE);
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1l);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1231111");
        recents.setKey(recentsPrimaryKey);

        recentService.reminderCheckForNotPaidOnPaytm(recents,reminderHistory,reminderResponseModel);
        assertEquals(null,recents.getNotPaidOnPaytm());
    }
    @Test
    public void testReminderNotPaidOnPaytmLastPaidAmountNull(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setStatus(14);
        reminderResponseModel.getData().setPaymentDate("2023-01-01 13:42:23");
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getOld().setAmount(0.0);
        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setStatus(14);
        reminderHistory.setPaytype(Constants.CREDIT_CARD_PAYTYPE);

        Recents recents = new Recents();

        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1l);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1231111");
        recents.setKey(recentsPrimaryKey);

        recentService.reminderCheckForNotPaidOnPaytm(recents,reminderHistory,reminderResponseModel);
        assertEquals(null,recents.getNotPaidOnPaytm());
    }
    @Test
    public void testReminderNotPaidOnPaytmWrongPaytype(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setStatus(14);
        reminderResponseModel.getData().setPaymentDate("2023-01-01 13:42:23");
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getOld().setAmount(0.0);
        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setStatus(14);
        reminderHistory.setPaytype(Constants.PREPAID_PAYTYPE);

        Recents recents = new Recents();

        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1l);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1231111");
        recents.setKey(recentsPrimaryKey);

        recentService.reminderCheckForNotPaidOnPaytm(recents,reminderHistory,reminderResponseModel);
        assertEquals(null,recents.getNotPaidOnPaytm());
    }
    @Test
    public void testNotPaidOnPaytmWithExceptionWhileParsing() throws ParseException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setStatus(14);
        reminderResponseModel.getData().setPaymentDate("2023-0101 13:42:23");
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getOld().setAmount(0.0);
        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setStatus(14);
        reminderHistory.setPaytype(Constants.PREPAID_PAYTYPE);

        Recents recents = new Recents();

        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1l);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1231111");
        recents.setKey(recentsPrimaryKey);

        SimpleDateFormat date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        recentService.reminderCheckForNotPaidOnPaytm(recents,reminderHistory,reminderResponseModel);
        assertEquals(null,recents.getNotPaidOnPaytm());
    }
    @Test
    public void testReminderUpdateInRecents() throws ReminderListenerException, RecentDataToKafkaException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date createdAt = DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String cardSkin = "{\"source\":\"DATABASE_MATCH\",\"url\":\"https://staticgw.paytm.in/bin-center/00/d4c7-15da-5b33-ca98/v1/hd0a.png\"}";
        String cardVariant = "Axis Flipkart";
        String extra = "{\"isAmountEditable\":true}";
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setData(new ReminderDataResponseModel());

        List<Recents> recentsList = new ArrayList<>();
        recentsList.add(mockRecentsData());

        Integer Ttl = TTLUtils.getTTL(service)+Math.toIntExact(mockRecentsData().getTxnTime().getTime()/1000-new Date().getTime()/1000);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,null,null,null,null,null,null,null,null,reminderResponseModel,null, null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate,cardVariant,cardSkin,extra,null, null, null);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),old_updated_at.capture(),ttl.capture());
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recents.capture(),source.capture());
        assertEquals(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),old_updated_at.getValue());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals(DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getDueDate());
        // assertEquals(Ttl,ttl.getValue());
    }
    @Test
    public void testReminderUpdateInRecents1() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date createdAt = DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String cardSkin = "{\"source\":\"DATABASE_MATCH\",\"url\":\"https://staticgw.paytm.in/bin-center/00/d4c7-15da-5b33-ca98/v1/hd0a.png\"}";
        String cardVariant = "Axis Flipkart";
        String extra = "{\"isAmountEditable\":true}";
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setData(new ReminderDataResponseModel());

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recentsList.add(recent);

        Integer Ttl = TTLUtils.getTTL(service)+Math.toIntExact(recent.getCreatedAt().getTime()/1000-new Date().getTime()/1000);
        if(Ttl<1)Ttl=1;

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,null,null,null,null,null,null,null,null, reminderResponseModel,null, null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, cardVariant,cardSkin,extra,null, null, null);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),old_updated_at.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),old_updated_at.getValue());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals(DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getDueDate());
//        assertEquals(Ttl,ttl.getValue());
    }

    @Test
    public void testReminderUpdateInRecents2() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setOperationType("update");

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);

        Integer Ttl = TTLUtils.getTTL(service);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,null,null,null,null,null,null,null,null, reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),old_updated_at.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),old_updated_at.getValue());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals(DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getDueDate());
        assertEquals(Ttl,ttl.getValue());
    }
    @Test
    public void testReminderUpdateInRecents3() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer notPaidNotPaytm = 1;
        Boolean isMarkAsPaid = true;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Date markAsPaidDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double markAsPaidAmount = 100.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setData(new ReminderDataResponseModel());

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,isMarkAsPaid,markAsPaidDate,markAsPaidAmount,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,notPaidNotPaytm,txnTime,txnAmount,null,null,null,null,null,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),old_updated_at.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),old_updated_at.getValue());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals(DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getTxnTime());
    }
    @Test
    public void testReminderUpdateInRecents4() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer notPaidNotPaytm = 0;
        Boolean isMarkAsPaid = false;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setData(new ReminderDataResponseModel());

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recentsList.add(recent);

        Integer Ttl = TTLUtils.getTTL(service)+Math.toIntExact(recent.getTxnTime().getTime()/1000-new Date().getTime()/1000);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,isMarkAsPaid,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,notPaidNotPaytm,txnTime,txnAmount,null,null,null,null,null,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate,null,null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),old_updated_at.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),old_updated_at.getValue());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        //assertEquals(Ttl,ttl.getValue());
    }
    @Test(expected = ReminderListenerException.class)
    public void testReminderUpdateInRecents5() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recentsList.add(recent);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(false);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,null,null,null,null,null,null,null,null,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),old_updated_at.capture(),ttl.capture());
    }
    @Test
    public void testReminderUpdateInRecents6() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer notPaidNotPaytm = 0;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setData(new ReminderDataResponseModel());

        Integer Ttl = TTLUtils.getTTL(service);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(null);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,notPaidNotPaytm,txnTime,txnAmount,null,null,null,null,null,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals(Ttl,ttl.getValue());
    }
    @Test
    public void testReminderUpdateInRecents7() throws ReminderListenerException, RecentDataToKafkaException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;

        List<Recents> recentsList = new ArrayList<>();

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setOperationType("update");

        Integer Ttl = TTLUtils.getTTL(service);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,notPaidNotPaytm,txnTime,txnAmount,null,null,null,null,null,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null, null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals(DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getTxnTime());
        assertEquals("1",recents.getValue().getNotPaidOnPaytm().toString());
    }
    @Test
    public void testReminderUpdateInRecents8() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setOperationType("update");

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(null);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,notPaidNotPaytm,txnTime,txnAmount,null,null,null,null,null,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals("1",recents.getValue().getNotPaidOnPaytm().toString());
    }

    @Test
    public void testReminderUpdateInRecents_PG_Deleted_AMW() throws ReminderListenerException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1234";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        String extra = "{\"type\":\"PG_Deleted_AMW\"}";
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderData.setExtra(extra);
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOperationType("insert");
        Boolean isValidation = true;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date newBillUpdatedAt = new Date();
        String eventSource = "PG_Deleted_AMW";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);

        String attributes = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product1 = new Product();
        product1.setProductId(1232l);
        product1.setAttributes(attributes);
        CVRProductCache.getInstance().addProductDetails(product1);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals("123w2531r23r23re1",recents.getValue().getKey().getRechargeNumber());
        assertEquals("PG_Deleted_AMW", recents.getValue().getEventSource());
    }
    @Test
    public void testReminderUpdateInRecents9() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1234";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);

        String attributes = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product1 = new Product();
        product1.setProductId(1232l);
        product1.setAttributes(attributes);
        CVRProductCache.getInstance().addProductDetails(product1);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null, null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals("123w2531r23r23re1",recents.getValue().getKey().getRechargeNumber());
    }
    @Test
    public void testReminderUpdateInRecents10() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1239";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals("123w2531r23r23re1",recents.getValue().getKey().getRechargeNumber());
    }
    @Test
    public void testReminderUpdateInRecents11() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1239";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(null);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals("123w2531r23r23re1",recents.getValue().getKey().getRechargeNumber());
    }
    @Test(expected = ReminderListenerException.class)
    public void testReminderUpdateInRecents12() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "mobile";
        String operator = "airtel";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "postpaid";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recentsList.add(recent);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(null);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(false);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,null,null,null,null,null,null,null,null,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
    }
    @Test
    public void testReminderUpdateInRecents13() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1239";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setProductId(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);


        when(recentsRepository.findByCustomerIdAndService(customerId, service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(), any())).thenReturn(null);

        recentService.updateRecentBillFromReminderListener(customerId, service, operator, planBucket, null, null, null, null, billDate, dueDate, dueAmount, null, null, null, pid, null, null, null, null, null, updatedAt, rechargeNumber, null, null, null, null, null, null, null, null, payType, mcn, null, notPaidNotPaytm, txnTime, txnAmount, newBillUpdatedAt, null, isValidation, null, eventSource, reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(), ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2), recents.getValue().getUpdatedAt());
        assertEquals("123w2531r23r23re1", recents.getValue().getKey().getRechargeNumber());
    }
    @Test
    public void testReminderUpdateInRecents14() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1234";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recent.getKey().setRechargeNumber("123w2531r23r23re1");
        recentsList.add(recent);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);


        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null, null, null, null);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recents.capture(),old_updated_at.capture(),ttl.capture());
    }
    @Test
    public void testReminderUpdateInRecents15() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1234";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recent.getKey().setRechargeNumber("123w2531r23r23re1");
        recentsList.add(recent);
        Recents recent1 = mockRecentsData();
        recent1.getKey().setRechargeNumber("42r52");
        recentsList.add(recent1);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);


        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recents.capture(),old_updated_at.capture(),ttl.capture());
    }
    @Test
    public void testReminderUpdateInRecents16() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1231l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1234";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);

        String attributes = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product1 = new Product();
        product1.setProductId(1232l);
        product1.setAttributes(attributes);
        CVRProductCache.getInstance().addProductDetails(product1);

        String attributes1 = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product2 = new Product();
        product2.setProductId(1231l);
        product2.setAttributes(attributes1);
        CVRProductCache.getInstance().addProductDetails(product2);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,null, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals("123w2531r23r23re1",recents.getValue().getKey().getRechargeNumber());
    }
    @Test
    public void testReminderUpdateInRecents17() throws ReminderListenerException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1231l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1234";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";
        String reconId = "TestValue";

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);

        String attributes = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product1 = new Product();
        product1.setProductId(1232l);
        product1.setAttributes(attributes);
        CVRProductCache.getInstance().addProductDetails(product1);

        String attributes1 = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product2 = new Product();
        product2.setProductId(1231l);
        product2.setAttributes(attributes1);
        CVRProductCache.getInstance().addProductDetails(product2);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,reconId, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals("123w2531r23r23re1",recents.getValue().getKey().getRechargeNumber());
        assertEquals("TestValue", recents.getValue().getReconId());
    }

    @Test
    public void testReminderUpdateInRecents18() throws ReminderListenerException {
        Long customerId = 1l;
        String rechargeNumber = "123w2531r23r23re1";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1231l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        String mcn = "XXXX XXXX XXXX 1234";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("insert");
        reminderResponseModel.setData(new ReminderDataResponseModel());
        Boolean isValidation = true;
        Date newBillUpdatedAt = new Date();
        String eventSource = "validation";
        String reconId = null;

        List<Recents> recentsList = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recentsList.add(recent);

        String attributes = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product1 = new Product();
        product1.setProductId(1232l);
        product1.setAttributes(attributes);
        CVRProductCache.getInstance().addProductDetails(product1);

        String attributes1 = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product2 = new Product();
        product2.setProductId(1231l);
        product2.setAttributes(attributes1);
        CVRProductCache.getInstance().addProductDetails(product2);


        when(recentsRepository.findByCustomerIdAndService(customerId,service)).thenReturn(recentsList);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,mcn,null,notPaidNotPaytm,txnTime,txnAmount,newBillUpdatedAt,null,isValidation,null,eventSource,reminderResponseModel,null,null,null,null,reconId, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null,null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals("123w2531r23r23re1",recents.getValue().getKey().getRechargeNumber());
        assertNull(recents.getValue().getReconId());
    }

    @Test
    public void testReminderUpdateInRecents19() throws ReminderListenerException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String reconId = null;

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setData(new ReminderDataResponseModel());

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(null);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,notPaidNotPaytm,txnTime,txnAmount,null,null,null,null,null,reminderResponseModel,null,null,null,null,reconId, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null, null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals("1",recents.getValue().getNotPaidOnPaytm().toString());
        assertNull(recents.getValue().getReconId());
    }

    @Test
    public void testReminderUpdateInRecents20() throws ReminderListenerException {
        Long customerId = 1l;
        String rechargeNumber = "123";
        String service = "financial services";
        String operator = "ccbp";
        String planBucket = "";
        Boolean nextBillFetchDateFlag = true;
        Date dueDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Date billDate = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double dueAmount = 100.0;
        Long pid = 1232l;
        Date updatedAt = DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String payType = "credit card";
        Integer notPaidNotPaytm = 1;
        Date txnTime = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double txnAmount = 199.0;
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        String reconId = "TestValue";
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setData(new ReminderDataResponseModel());

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId,service,rechargeNumber,operator,planBucket)).thenReturn(null);

        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentBillFromReminderListener(customerId,service,operator,planBucket,null,null,null,null,billDate,dueDate,dueAmount,null,null,null,pid,null,null,null,null,null,updatedAt,rechargeNumber,null,null,null,null,null,null,null,null,payType,null,null,notPaidNotPaytm,txnTime,txnAmount,null,null,null,null,null,reminderResponseModel,null,null,null,null,reconId, nextBillFetchDateFlag,reminderStatus, oldBillFetchDate, null, null, null, null, null, null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recents.capture(),ttl.capture());
        assertEquals(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),recents.getValue().getUpdatedAt());
        assertEquals("1",recents.getValue().getNotPaidOnPaytm().toString());
        assertEquals("TestValue", recents.getValue().getReconId());
    }

    @Test
    public void updateNotificationStatusInRecents() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        reminderResponseModel.getData().setService("mobile");
        Integer notificationStatus = 1;
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer reminderStatus = 5;
        Date oldBillFetchDate = DateUtil.stringToDate("2023-06-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateNotificationStatusInRecents(notificationStatus,operator,rechargeNumber,reminderResponseModel);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("1",recent1.getValue().getNotificationStatus().toString());
    }
    @Test
    public void updateNotificationStatusInRecents1() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        reminderResponseModel.getData().setService("mobile");
        Integer notificationStatus = 1;
        String operator = "airtel";
        String rechargeNumber = "23234521";
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.setTxnTime(null);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateNotificationStatusInRecents(notificationStatus,operator,rechargeNumber,reminderResponseModel);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("1",recent1.getValue().getNotificationStatus().toString());
    }
    @Test
    public void updateNotificationStatusInRecents2() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        reminderResponseModel.getData().setService("mobile");
        Integer notificationStatus = 0;
        String operator = "airtel";
        String rechargeNumber = "23234521";
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile");

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateNotificationStatusInRecents(notificationStatus,operator,rechargeNumber,reminderResponseModel);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("0",recent1.getValue().getNotificationStatus().toString());
        assertEquals(Ttl,ttl.getValue());
    }
    @Test
    public void updateNotificationStatusInRecents3() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        reminderResponseModel.getData().setService("mobile");
        Integer notificationStatus = 1;
        String operator = "airtel";
        String rechargeNumber = "23234521";
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.setCreatedAt(null);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateNotificationStatusInRecents(notificationStatus,operator,rechargeNumber,reminderResponseModel);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("1",recent1.getValue().getNotificationStatus().toString());
    }
    @Test(expected = ReminderListenerException.class)
    public void updateNotificationStatusInRecents4() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        Integer notificationStatus = 1;
        String operator = "airtel";
        String rechargeNumber = "23234521";
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile")+Math.toIntExact(recent.getTxnTime().getTime()/1000-new Date().getTime()/1000);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);

        recentService.updateNotificationStatusInRecents(notificationStatus,operator,rechargeNumber,reminderResponseModel);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),null);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void updateNotificationStatusInRecents5() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setService("mobile");
        Integer notificationStatus = 1;
        String operator = "airtel";
        String rechargeNumber = "23234521";
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.setCreatedAt(null);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(null);

        recentService.updateNotificationStatusInRecents(notificationStatus,operator,rechargeNumber,reminderResponseModel);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenNoExistingData(recent1.capture(),ttl.capture());
    }
    @Test
    public void updateAutomaticStatusInRecents() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        Long customerId = 1l;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer automaticStatus = 1;
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile")+Math.toIntExact(recent.getTxnTime().getTime()/1000-new Date().getTime()/1000);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateRecentAutomaticStateInfo(automaticStatus,customerId,service,rechargeNumber,operator,planBucket);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("1",recent1.getValue().getAutomaticStatus().toString());
        //assertEquals(Ttl,ttl.getValue());
    }
    @Test
    public void updateAutomaticStatusInRecents1() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        Long customerId = 1l;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer automaticStatus = 1;
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.setTxnTime(null);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile")+Math.toIntExact(recent.getCreatedAt().getTime()/1000-new Date().getTime()/1000);
        if(Ttl<1)Ttl=1;

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentAutomaticStateInfo(automaticStatus,customerId,service,operator,rechargeNumber,planBucket);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("1",recent1.getValue().getAutomaticStatus().toString());
//        assertEquals(Ttl,ttl.getValue());
    }
    @Test
    public void updateAutomaticStatusInRecents2() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        reminderResponseModel.getData().setService("mobile");
        Long customerId = 1l;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer automaticStatus = 1;
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile");

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateRecentAutomaticStateInfo(automaticStatus,customerId,service,operator,rechargeNumber,planBucket);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("1",recent1.getValue().getAutomaticStatus().toString());
        assertEquals(Ttl,ttl.getValue());
    }
    @Test
    public void updateAutomaticStatusInRecents3() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        Long customerId = 1l;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer automaticStatus = 1;
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.setCreatedAt(null);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile")+Math.toIntExact(recent.getTxnTime().getTime()/1000-new Date().getTime()/1000);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateRecentAutomaticStateInfo(automaticStatus,customerId,service,operator,rechargeNumber,planBucket);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("1",recent1.getValue().getAutomaticStatus().toString());
        //assertEquals(Ttl,ttl.getValue());
    }
    @Test(expected = ReminderListenerException.class)
    public void updateAutomaticStatusInRecents4() throws ReminderListenerException, RecentDataToKafkaException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setCreated_at("2023-03-05 05:44:11");
        Long customerId = 1l;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer automaticStatus = 1;
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile")+Math.toIntExact(recent.getTxnTime().getTime()/1000-new Date().getTime()/1000);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(false);

        recentService.updateRecentAutomaticStateInfo(automaticStatus,customerId,service,operator,rechargeNumber,planBucket);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),null);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void updateAutomaticStatusInRecents5() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer automaticStatus = 1;

        Integer Ttl = TTLUtils.getTTL("mobile");

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateRecentAutomaticStateInfo(automaticStatus,customerId,service,operator,rechargeNumber,planBucket);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenNoExistingData(recent1.capture(),ttl.capture());
        assertEquals(Ttl,ttl.getValue());
    }
    @Test(expected = ReminderListenerException.class)
    public void updateAutomaticStatusInRecents6() throws ReminderListenerException, RecentDataToKafkaException {
        Long customerId = 1l;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer automaticStatus = 1;

        Integer Ttl = TTLUtils.getTTL("mobile");

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(false);

        recentService.updateRecentAutomaticStateInfo(automaticStatus,customerId,service,operator,rechargeNumber,planBucket);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),null);

        verify(recentsRepository).updateRecentWhenNoExistingData(recent1.capture(),ttl.capture());
        assertEquals(Ttl,ttl.getValue());
    }

    @Test
    public void testUpdateRecentConsentValidTillInfo_Success() throws Exception {
        Date consentValidTill = new Date();
        Long customerId = 1L;
        String service = FINANCIAL_SERVICE;
        String rechargeNumber = "1234567890";
        String operator = "ccbp";
        String planBucket = "";
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setRechargeNumber(rechargeNumber);
        recents.getKey().setService(service);
        recents.getKey().setOperator(operator);
        recents.getKey().setPlanBucket(planBucket);
        recents.setTxnTime(new Date());
        recents.setCreatedAt(new Date());
        recents.setUpdatedAt(new Date());
        recentsList.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(anyLong(), anyString(), anyString(), anyString(), anyString())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(Recents.class), any(Date.class), anyInt())).thenReturn(true);
        recentService.updateRecentConsentValidTillInfo(consentValidTill, customerId, service, rechargeNumber, operator, planBucket);
        verify(recentsRepository, times(1)).findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, service, rechargeNumber, operator, planBucket);
        verify(recentsRepository, times(1)).updateRecentWhenDataAlreadyExist(any(Recents.class), any(Date.class), anyInt());
    }
    @Test(expected = ReminderListenerException.class)
    public void testUpdateRecentConsentValidTillInfo_UpdateFailure() throws Exception {
        Date consentValidTill = new Date();
        Long customerId = 1L;
        String service = FINANCIAL_SERVICE;
        String rechargeNumber = "1234567890";
        String operator = "ccbp";
        String planBucket = "";
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setRechargeNumber(rechargeNumber);
        recents.getKey().setService(service);
        recents.getKey().setOperator(operator);
        recents.getKey().setPlanBucket(planBucket);
        recents.setTxnTime(new Date());
        recents.setCreatedAt(new Date());
        recents.setUpdatedAt(new Date());
        recentsList.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(anyLong(), anyString(), anyString(), anyString(), anyString())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(Recents.class), any(Date.class), anyInt())).thenReturn(false);
        recentService.updateRecentConsentValidTillInfo(consentValidTill, customerId, service, rechargeNumber, operator, planBucket);
    }
    @Test(expected = ReminderListenerException.class)
    public void testUpdateRecentConsentValidTillInfo_ExceptionHandling() throws Exception {
        Date consentValidTill = new Date();
        Long customerId = 1L;
        String service = FINANCIAL_SERVICE;
        String rechargeNumber = "1234567890";
        String operator = "ccbp";
        String planBucket = "";
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setRechargeNumber(rechargeNumber);
        recents.getKey().setService(service);
        recents.getKey().setOperator(operator);
        recents.getKey().setPlanBucket(planBucket);
        recents.setTxnTime(new Date());
        recents.setCreatedAt(new Date());
        recents.setUpdatedAt(new Date());
        recentsList.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(anyLong(), anyString(), anyString(), anyString(), anyString())).thenThrow(new RuntimeException());
        recentService.updateRecentConsentValidTillInfo(consentValidTill, customerId, service, rechargeNumber, operator, planBucket);
        verify(metricsHelper, times(1)).recordSuccessRate(anyString(), anyString());
    }
    @Test
    public void testInsertRecentConsentValidTillInfo_Success() throws Exception {
        Date consentValidTill = new Date();
        Long customerId = 1L;
        String service = FINANCIAL_SERVICE;
        String rechargeNumber = "1234567890";
        String operator = "ccbp";
        String planBucket = "";
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(anyLong(), anyString(), anyString(), anyString(), anyString())).thenReturn(new ArrayList<>());
        when(recentsRepository.updateRecentWhenNoExistingData(any(Recents.class), anyInt())).thenReturn(true);
        recentService.updateRecentConsentValidTillInfo(consentValidTill, customerId, service, rechargeNumber, operator, planBucket);
        verify(recentsRepository, times(1)).findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, service, rechargeNumber, operator, planBucket);
        verify(recentsRepository, times(1)).updateRecentWhenNoExistingData(any(Recents.class), anyInt());
    }

    @Test
    public void testUpdateAutomaticDateInRecents() throws AutomaticListenerException, RecentDataToKafkaException {
        Long customerId = 1L;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer subscriberId = 556677;
        double automaticAmount = 100.0;
        Long pid = 123L;
        Date updatedAt = new Date();
        Date automaticDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recent.setAutomaticAmount(automaticAmount);
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile")+Math.toIntExact(recent.getTxnTime().getTime()/1000-new Date().getTime()/1000);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateAutomaticInfo(customerId,automaticDate,service,operator,planBucket,updatedAt,updatedAt,rechargeNumber,pid,subscriberId, true, automaticAmount);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(automaticDate,recent1.getValue().getAutomaticDate());
        //assertEquals(Ttl,ttl.getValue());
    }
    @Test
    public void testUpdateAutomaticDateInRecents1() throws AutomaticListenerException, RecentDataToKafkaException {
        Long customerId = 1L;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Integer subscriberId = 556677;
        Long pid = 123L;
        Double automaticAmount = 100.0;
        Date updatedAt = new Date();
        Date automaticDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recent.setAutomaticAmount(automaticAmount);
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile")+Math.toIntExact(recent.getCreatedAt().getTime()/1000-new Date().getTime()/1000);
        if(Ttl<1)Ttl=1;

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateAutomaticInfo(customerId,automaticDate,service,operator,planBucket,updatedAt,updatedAt,rechargeNumber,pid,subscriberId, true, automaticAmount);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(automaticDate,recent1.getValue().getAutomaticDate());
//        assertEquals(Ttl,ttl.getValue());

    }
    @Test
    public void testUpdateAutomaticDateInRecents2() throws AutomaticListenerException, RecentDataToKafkaException {
        Long customerId = 1L;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Long pid = 123L;
        Double automaticAmount = 100.0;
        Date updatedAt = new Date();
        Date automaticDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setCreatedAt(null);
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recent.setAutomaticAmount(automaticAmount);
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile")+Math.toIntExact(recent.getTxnTime().getTime()/1000-new Date().getTime()/1000);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateAutomaticInfo(customerId,automaticDate,service,operator,planBucket,updatedAt,updatedAt,rechargeNumber,pid,null, true, automaticAmount);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(automaticDate,recent1.getValue().getAutomaticDate());
        assertEquals(recent.getTxnTime(),recent1.getValue().getCreatedAt());
        //assertEquals(Ttl,ttl.getValue());

    }
    @Test
    public void testUpdateAutomaticDateInRecents3() throws AutomaticListenerException, RecentDataToKafkaException {
        Long customerId = 1L;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Long pid = 123L;
        Double automaticAmount = 100.0;
        Date updatedAt = new Date();
        Date automaticDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recent.setAutomaticAmount(automaticAmount);
        recents.add(recent);

        Integer Ttl = TTLUtils.getTTL("mobile");

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);

        recentService.updateAutomaticInfo(customerId,automaticDate,service,operator,planBucket,updatedAt,updatedAt,rechargeNumber,pid,null, true, automaticAmount);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(automaticDate,recent1.getValue().getAutomaticDate());
        assertEquals(Ttl,ttl.getValue());

    }
    @Test(expected = AutomaticListenerException.class)
    public void testUpdateAutomaticDateInRecents4() throws AutomaticListenerException, RecentDataToKafkaException {
        Long customerId = 1L;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Long pid = 123L;
        Date updatedAt = new Date();
        Date automaticDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);
        Double automaticAmount = 100.0;
        List<Recents> recents = new ArrayList<>();
        Recents recent = mockRecentsData();
        recent.getKey().setCustomerId(1L);
        recent.getKey().setRechargeNumber("1231111");
        recents.add(recent);

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(false);

        recentService.updateAutomaticInfo(customerId,automaticDate,service,operator,planBucket,updatedAt,updatedAt,rechargeNumber,pid,null, true, automaticAmount);
        verify(recentDataToKafkaService,times(0)).setRecentConsumerSource(recent1.capture(),null);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent1.capture(),oldUpdatedAt.capture(),ttl.capture());

    }
    @Test
    public void testUpdateAutomaticDateInRecents5() throws AutomaticListenerException, RecentDataToKafkaException {
        Long customerId = 1L;
        String service = "mobile";
        String planBucket = "";
        String operator = "airtel";
        String rechargeNumber = "23234521";
        Long pid = 123L;
        Double automaticAmount = 100.0;


        Date updatedAt = new Date();
        Date automaticDate = DateUtil.stringToDate("2023-05-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2);

        Integer Ttl = TTLUtils.getTTL("mobile");

        ArgumentCaptor<Recents> recent1 = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);

        recentService.updateAutomaticInfo(customerId,automaticDate,service,operator,planBucket,updatedAt,updatedAt,rechargeNumber,pid,null, true, automaticAmount);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recent1.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenNoExistingData(recent1.capture(),ttl.capture());
        assertEquals(automaticDate,recent1.getValue().getAutomaticDate());
        assertEquals(Ttl,ttl.getValue());

    }
    @Test
    public void testMarkAsPaidApiUpdateInRecents() throws RecentDataToKafkaException {
        Recents recent = mockRecentsData();
        recent.setIsMarkAsPaid(true);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);

        recentService.updateMarkAsPaidDetailsInRecents(recent,recent.getUpdatedAt());
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recents.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),updatedAt.capture(),ttl.capture());

    }
    @Test
    public void testMarkAsPaidApiUpdateInRecents1() throws RecentDataToKafkaException {
        Recents recent = mockRecentsData();
        recent.setIsMarkAsPaid(true);
        recent.setCreatedAt(null);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        recentService.updateMarkAsPaidDetailsInRecents(recent,recent.getUpdatedAt());
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recents.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),updatedAt.capture(),ttl.capture());

    }
    @Test
    public void testMarkAsPaidApiUpdateInRecents2() throws RecentDataToKafkaException {
        Recents recent = mockRecentsData();
        recent.setIsMarkAsPaid(true);
        recent.setTxnTime(null);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        recentService.updateMarkAsPaidDetailsInRecents(recent,recent.getUpdatedAt());
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recents.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),updatedAt.capture(),ttl.capture());

    }
    @Test
    public void testMarkAsPaidApiUpdateInRecents3() throws RecentDataToKafkaException {
        Recents recent = mockRecentsData();
        recent.setIsMarkAsPaid(true);
        recent.setTxnTime(null);
        recent.setCreatedAt(null);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);

        recentService.updateMarkAsPaidDetailsInRecents(recent,recent.getUpdatedAt());
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recents.capture(),source.capture());

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recents.capture(),updatedAt.capture(),ttl.capture());

    }
    @Test(expected=FsRechargeListenerException.class)
    public void FsRechargeListenerExceptionTest() throws ParseException, FsRechargeListenerException, RecentDataToKafkaException {
        String str="{\"recentData\":{},\"@timestamp\":\"2023-05-01T01:17:32.855Z\",\"@version\":\"1\",\"billPaymentSource\":\"\",\"catalogProductID\":*********,\"currentGw\":\"viprepaid\",\"customerInfo_channel_id\":\"ANDROIDAPP 10.22.0\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_id\":**********,\"customerInfo_customer_phone\":\"9414017083\",\"customerInfo_customer_type\":1,\"customerInfo_remote_ip\":\"**********\",\"doc_as_upsert\":true,\"gwMachineId\":\"digitalingwnode_billpayments_v1_36_61\",\"inStatusMap_gwErrorMsg\":\"\",\"inStatusMap_responseCode\":\"00\",\"inStatusMap_transactionStatus\":\"SUCCESS\",\"logs\":\"\",\"offUsclientRefID\":\"\",\"orderInfo_apptxn_id\":**********,\"orderInfo_br_id\":567960,\"orderInfo_c_sid\":1,\"orderInfo_cat_id\":17,\"orderInfo_cust_id\":**********,\"orderInfo_item_id\":***********,\"orderInfo_merchant_id\":2,\"orderInfo_order_id\":***********,\"orderInfo_order_item_count\":1,\"orderInfo_pg_cf\":1.9,\"orderInfo_pg_cf_pm\":{\"CREDIT_CARD\":1.9},\"orderInfo_previousResponseCode\":\"IR\",\"orderInfo_rule_id\":51118,\"orderInfo_totalItemsAmount\":239,\"originalPid\":*********,\"paymentInfo_bankTransId\":\"************\",\"paymentInfo_cardScheme\":\"DINERS\",\"paymentInfo_paymentBank\":\"WALLET\",\"paymentInfo_paymentMode\":\"PAYTM CASH\",\"paymentInfo_riskInfo\":\"\",\"productInfo_categoryId\":17,\"productInfo_circle\":\"rajasthan\",\"productInfo_operator\":\"vodafone idea\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"topup\",\"productInfo_service\":\"mobile\",\"productInfo_verticalId\":4,\"rechargeAttempt\":1,\"rechargeGwResponse_connectionErrorCode\":\"\",\"rechargeGwResponse_connectionErrorMessage\":\"\",\"rechargeGwResponse_gwRefId\":\"ONR2305010647180070\",\"rechargeGwResponse_gwTxnErrorCode\":\"200\",\"rechargeGwResponse_gwTxnErrorMessage\":\"\",\"rechargeGwResponse_operatorResponseCode\":\"200\",\"reqType\":\"RECHARGE\",\"serverId\":\"ffrgreennode_billpayments_v1_36_153_284620\",\"statuscheck_ResCode\":\"\",\"timestamps_current\":\"2023-05-01 06:47:17\",\"timestamps_init\":\"2023-05-15T15:56:20.774Z\",\"timestamps_mis_update\":\"2023-05-01 06:47:32\",\"useExtendedData\":false,\"userData_amount\":239,\"userData_recharge_number\":\"8955157354\",\"userData_recharge_number_2_length\":0,\"userData_recharge_number_length\":10,\"userData_totalItemsPrice\":240.9}";
        FsRechargeConsumerModel fsRechargeConsumerModel= JsonUtils.parseJson(str,FsRechargeConsumerModel.class);
        fsRechargeConsumerModel.setTxnInit(new Date());
        when(recentsRepository.findById(any())).thenReturn(null);
        recentService.updateRecentData(fsRechargeConsumerModel);
    }

    @Test()
    public void FsRechargeListenerSuccess() throws ParseException, FsRechargeListenerException, RecentDataToKafkaException {
        String str="{\"recentData\":{},\"@timestamp\":\"2023-05-01T01:17:32.855Z\",\"@version\":\"1\",\"billPaymentSource\":\"\",\"catalogProductID\":*********,\"currentGw\":\"viprepaid\",\"customerInfo_channel_id\":\"ANDROIDAPP 10.22.0\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_id\":**********,\"customerInfo_customer_phone\":\"9414017083\",\"customerInfo_customer_type\":1,\"customerInfo_remote_ip\":\"**********\",\"doc_as_upsert\":true,\"gwMachineId\":\"digitalingwnode_billpayments_v1_36_61\",\"inStatusMap_gwErrorMsg\":\"\",\"inStatusMap_responseCode\":\"00\",\"inStatusMap_transactionStatus\":\"SUCCESS\",\"logs\":\"\",\"offUsclientRefID\":\"\",\"orderInfo_apptxn_id\":**********,\"orderInfo_br_id\":567960,\"orderInfo_c_sid\":1,\"orderInfo_cat_id\":17,\"orderInfo_cust_id\":**********,\"orderInfo_item_id\":***********,\"orderInfo_merchant_id\":2,\"orderInfo_order_id\":***********,\"orderInfo_order_item_count\":1,\"orderInfo_pg_cf\":1.9,\"orderInfo_pg_cf_pm\":{\"CREDIT_CARD\":1.9},\"orderInfo_previousResponseCode\":\"IR\",\"orderInfo_rule_id\":51118,\"orderInfo_totalItemsAmount\":239,\"originalPid\":*********,\"paymentInfo_bankTransId\":\"************\",\"paymentInfo_cardScheme\":\"DINERS\",\"paymentInfo_paymentBank\":\"WALLET\",\"paymentInfo_paymentMode\":\"PAYTM CASH\",\"paymentInfo_riskInfo\":\"\",\"productInfo_categoryId\":17,\"productInfo_circle\":\"rajasthan\",\"productInfo_operator\":\"vodafone idea\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"topup\",\"productInfo_service\":\"mobile\",\"productInfo_verticalId\":4,\"rechargeAttempt\":1,\"rechargeGwResponse_connectionErrorCode\":\"\",\"rechargeGwResponse_connectionErrorMessage\":\"\",\"rechargeGwResponse_gwRefId\":\"ONR2305010647180070\",\"rechargeGwResponse_gwTxnErrorCode\":\"200\",\"rechargeGwResponse_gwTxnErrorMessage\":\"\",\"rechargeGwResponse_operatorResponseCode\":\"200\",\"reqType\":\"RECHARGE\",\"serverId\":\"ffrgreennode_billpayments_v1_36_153_284620\",\"statuscheck_ResCode\":\"\",\"timestamps_current\":\"2023-05-01 06:47:17\",\"timestamps_init\":\"2023-05-01T06:47:17.862Z\",\"timestamps_mis_update\":\"2023-05-01 06:47:32\",\"useExtendedData\":false,\"userData_amount\":239,\"userData_recharge_number\":\"8955157354\",\"userData_recharge_number_2_length\":0,\"userData_recharge_number_length\":10,\"userData_totalItemsPrice\":240.9}";
        FsRechargeConsumerModel fsRechargeConsumerModel= JsonUtils.parseJson(str,FsRechargeConsumerModel.class);
        fsRechargeConsumerModel.setTxnInit(new Date());
        when(recentsRepository.findById(any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentData(fsRechargeConsumerModel);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),source.capture());
        verify(recentsRepository, times(1)).updateRecentWhenNoExistingData(any(),any());
    }
    @Test()
    public void FsRechargeListenerSuccessExistingCustomer() throws ParseException, FsRechargeListenerException, RecentDataToKafkaException {
        String str="{\"recentData\":{},\"@timestamp\":\"2023-05-01T01:17:32.855Z\",\"@version\":\"1\",\"billPaymentSource\":\"\",\"catalogProductID\":*********,\"currentGw\":\"viprepaid\",\"customerInfo_channel_id\":\"ANDROIDAPP 10.22.0\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_id\":**********,\"customerInfo_customer_phone\":\"9414017083\",\"customerInfo_customer_type\":1,\"customerInfo_remote_ip\":\"**********\",\"doc_as_upsert\":true,\"gwMachineId\":\"digitalingwnode_billpayments_v1_36_61\",\"inStatusMap_gwErrorMsg\":\"\",\"inStatusMap_responseCode\":\"00\",\"inStatusMap_transactionStatus\":\"SUCCESS\",\"logs\":\"\",\"offUsclientRefID\":\"\",\"orderInfo_apptxn_id\":**********,\"orderInfo_br_id\":567960,\"orderInfo_c_sid\":1,\"orderInfo_cat_id\":17,\"orderInfo_cust_id\":**********,\"orderInfo_item_id\":***********,\"orderInfo_merchant_id\":2,\"orderInfo_order_id\":***********,\"orderInfo_order_item_count\":1,\"orderInfo_pg_cf\":1.9,\"orderInfo_pg_cf_pm\":{\"CREDIT_CARD\":1.9},\"orderInfo_previousResponseCode\":\"IR\",\"orderInfo_rule_id\":51118,\"orderInfo_totalItemsAmount\":239,\"originalPid\":*********,\"paymentInfo_bankTransId\":\"************\",\"paymentInfo_cardScheme\":\"DINERS\",\"paymentInfo_paymentBank\":\"WALLET\",\"paymentInfo_paymentMode\":\"PAYTM CASH\",\"paymentInfo_riskInfo\":\"\",\"productInfo_categoryId\":17,\"productInfo_circle\":\"rajasthan\",\"productInfo_operator\":\"vodafone idea\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"topup\",\"productInfo_service\":\"mobile\",\"productInfo_verticalId\":4,\"rechargeAttempt\":1,\"rechargeGwResponse_connectionErrorCode\":\"\",\"rechargeGwResponse_connectionErrorMessage\":\"\",\"rechargeGwResponse_gwRefId\":\"ONR2305010647180070\",\"rechargeGwResponse_gwTxnErrorCode\":\"200\",\"rechargeGwResponse_gwTxnErrorMessage\":\"\",\"rechargeGwResponse_operatorResponseCode\":\"200\",\"reqType\":\"RECHARGE\",\"serverId\":\"ffrgreennode_billpayments_v1_36_153_284620\",\"statuscheck_ResCode\":\"\",\"timestamps_current\":\"2023-05-01 06:47:17\",\"timestamps_init\":\"2023-05-01T06:47:17.862Z\",\"timestamps_mis_update\":\"2023-05-01 06:47:32\",\"useExtendedData\":false,\"userData_amount\":239,\"userData_recharge_number\":\"8955157354\",\"userData_recharge_number_2_length\":0,\"userData_recharge_number_length\":10,\"userData_totalItemsPrice\":240.9}";
        FsRechargeConsumerModel fsRechargeConsumerModel= JsonUtils.parseJson(str,FsRechargeConsumerModel.class);
        fsRechargeConsumerModel.setTxnInit(new Date());
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(fsRechargeConsumerModel.getCustomerId());
        recentsPrimaryKey.setService(fsRechargeConsumerModel.getService());
        recentsPrimaryKey.setRechargeNumber(fsRechargeConsumerModel.getRecharge_number());
        recentsPrimaryKey.setOperator(fsRechargeConsumerModel.getOperator());
        recentsPrimaryKey.setPlanBucket(fsRechargeConsumerModel.getPlanBucket());
        recents.setKey(recentsPrimaryKey);
        Optional<Recents> returnCacheValue = Optional.of(Recents.class).of(recents);
        when(recentsRepository.findById(any())).thenReturn(returnCacheValue.get());
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentData(fsRechargeConsumerModel);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),source.capture());

        verify(recentsRepository, times(1)).updateRecentWhenDataAlreadyExist(any(),any(),any());
    }
    @Test(expected = PlanValidityListenerException.class)
    public void exceptionWhileUpdatingPlanExpiryData() throws PlanValidityListenerException, RecentDataToKafkaException{
        Date expiryDate=null;
        String kafkaPckt="{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"update\",\"ts\":1683006590,\"xid\":6234120002,\"commit\":true,\"data\":{\"id\":1670901969,\"recharge_number\":\"9983143527\",\"customer_id\":239349430,\"service\":\"mobile\",\"operator\":\"vodafone idea\",\"circle\":\"rajasthan\",\"amount\":719,\"validity_expiry_date\":\"2023-07-24 23:59:59\",\"order_date\":\"2022-11-11 19:03:28\",\"latest_recharge_date\":\"2023-05-02 11:18:54\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan\",\"created_at\":\"2023-02-28 13:53:58\",\"updated_at\":\"2023-05-02 05:49:50\",\"cust_mobile\":\"9887243234\",\"cust_email\":\"<EMAIL>\",\"rn_customer_id\":null,\"order_ids\":\",20493234745,20747747341,20994060203\",\"status\":11,\"extra\":\"{\\\"customer_type\\\":1}\",\"notification_status\":1,\"cust_rech_meta\":\"\",\"product_id\":*********},\"old\":{\"notification_status\":2}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(kafkaPckt, PlanValidityResponseModel.class);

        try{
            planValidityResponseModel.getData().setValidity_expiry_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            planValidityResponseModel.getData().setCreated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            planValidityResponseModel.getData().setUpdated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            expiryDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityResponseModel.getData().getUpdated_at());
        } catch (Exception e) {
        }
        when(serviceConfig.getRechargeNumberToCustomerIdLimit()).thenReturn(100);
        PlanValidityDataResponseModel planValidityDataResponseModel=planValidityResponseModel.getData();
        PlanExpiryHistory planExpiryHistory= new PlanExpiryHistory(planValidityDataResponseModel.getRecharge_number()
                , planValidityDataResponseModel.getService()
                , planValidityDataResponseModel.getCircle()
                , planValidityDataResponseModel.getOperator()
                , planValidityDataResponseModel.getPlan_bucket()
                , expiryDate
                , planValidityDataResponseModel.getCustomer_id()
                , planValidityDataResponseModel.getCategory_name()
                , expiryDate
                , expiryDate
                , new HashMap<String, String>()
                , planValidityDataResponseModel.getAmount());

        Integer notificationStatus = planValidityResponseModel.getData().getNotification_status();
        Recents recentObj = this.prepareRecentsData(planExpiryHistory,planValidityResponseModel);
        recentObj.setNotificationStatus(notificationStatus);
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(planValidityDataResponseModel.getService());
        recentsPrimaryKey.setRechargeNumber(planValidityDataResponseModel.getRecharge_number());
        recentsPrimaryKey.setCustomerId(planValidityDataResponseModel.getCustomer_id());
        recentsPrimaryKey.setPlanBucket(planValidityDataResponseModel.getPlan_bucket());
        recentsPrimaryKey.setOperator(planValidityDataResponseModel.getPlan_bucket());
        recents1.setKey(recentsPrimaryKey);
        recents1.setUpdatedAt(new Date());
        recents1.setOrderId(1234L);

        recents.add(recents1);
        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(false);
        recentService.insertIntoRecentAndCustomerBill(recentObj,planExpiryHistory);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),source.capture());

        verify(customerBillDao,never()).saveRecentToCustomerBill(any(),any());
    }

    @Test
    public void planDetailsSuccessFullyUpdated() throws PlanValidityListenerException, RecentDataToKafkaException{
        Date expiryDate=null;
        String kafkaPckt="{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"update\",\"ts\":1683006590,\"xid\":6234120002,\"commit\":true,\"data\":{\"id\":1670901969,\"recharge_number\":\"9983143527\",\"customer_id\":239349430,\"service\":\"mobile\",\"operator\":\"vodafone idea\",\"circle\":\"rajasthan\",\"amount\":719,\"validity_expiry_date\":\"2023-07-24 23:59:59\",\"order_date\":\"2022-11-11 19:03:28\",\"latest_recharge_date\":\"2023-05-02 11:18:54\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan\",\"created_at\":\"2023-02-28 13:53:58\",\"updated_at\":\"2023-05-02 05:49:50\",\"cust_mobile\":\"9887243234\",\"cust_email\":\"<EMAIL>\",\"rn_customer_id\":null,\"order_ids\":\",20493234745,20747747341,20994060203\",\"status\":11,\"extra\":\"{\\\"customer_type\\\":1}\",\"notification_status\":1,\"cust_rech_meta\":\"\",\"product_id\":*********},\"old\":{\"notification_status\":2}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(kafkaPckt, PlanValidityResponseModel.class);

        try{
            planValidityResponseModel.getData().setValidity_expiry_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            planValidityResponseModel.getData().setCreated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            planValidityResponseModel.getData().setUpdated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            expiryDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityResponseModel.getData().getUpdated_at());
        } catch (Exception e) {
        }
        when(serviceConfig.getRechargeNumberToCustomerIdLimit()).thenReturn(100);
        PlanValidityDataResponseModel planValidityDataResponseModel=planValidityResponseModel.getData();
        PlanExpiryHistory planExpiryHistory= new PlanExpiryHistory(planValidityDataResponseModel.getRecharge_number()
                , planValidityDataResponseModel.getService()
                , planValidityDataResponseModel.getCircle()
                , planValidityDataResponseModel.getOperator()
                , planValidityDataResponseModel.getPlan_bucket()
                , expiryDate
                , planValidityDataResponseModel.getCustomer_id()
                , planValidityDataResponseModel.getCategory_name()
                , expiryDate
                , expiryDate
                , new HashMap<String, String>()
                , planValidityDataResponseModel.getAmount());

        Integer notificationStatus = planValidityResponseModel.getData().getNotification_status();
        Recents recentObj = this.prepareRecentsData(planExpiryHistory,planValidityResponseModel);
        recentObj.setNotificationStatus(notificationStatus);
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(planValidityDataResponseModel.getService());
        recentsPrimaryKey.setRechargeNumber(planValidityDataResponseModel.getRecharge_number());
        recentsPrimaryKey.setCustomerId(planValidityDataResponseModel.getCustomer_id());
        recentsPrimaryKey.setPlanBucket(planValidityDataResponseModel.getPlan_bucket());
        recentsPrimaryKey.setOperator(planValidityDataResponseModel.getPlan_bucket());
        recents1.setKey(recentsPrimaryKey);
        recents1.setUpdatedAt(new Date());
        recents1.setOrderId(1234L);
        recents.add(recents1);
        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.insertIntoRecentAndCustomerBill(recentObj,planExpiryHistory);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),source.capture());
        verify(customerBillDao,times(1)).saveRecentToCustomerBill(any(),any());
    }

    @Test
    public void updateExpriyInfoForNewCustomerDBSuccess() throws PlanValidityListenerException, RecentDataToKafkaException{
        Date expiryDate=null;
        String kafkaPckt="{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"update\",\"ts\":1683006590,\"xid\":6234120002,\"commit\":true,\"data\":{\"id\":1670901969,\"recharge_number\":\"9983143527\",\"customer_id\":239349430,\"service\":\"mobile\",\"operator\":\"vodafone idea\",\"circle\":\"rajasthan\",\"amount\":719,\"validity_expiry_date\":\"2023-07-24 23:59:59\",\"order_date\":\"2022-11-11 19:03:28\",\"latest_recharge_date\":\"2023-05-02 11:18:54\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan\",\"created_at\":\"2023-02-28 13:53:58\",\"updated_at\":\"2023-05-02 05:49:50\",\"cust_mobile\":\"9887243234\",\"cust_email\":\"<EMAIL>\",\"rn_customer_id\":null,\"order_ids\":\",20493234745,20747747341,20994060203\",\"status\":11,\"extra\":\"{\\\"customer_type\\\":1}\",\"notification_status\":1,\"cust_rech_meta\":\"\",\"product_id\":*********},\"old\":{\"notification_status\":2}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(kafkaPckt, PlanValidityResponseModel.class);

        try{
            planValidityResponseModel.getData().setValidity_expiry_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            planValidityResponseModel.getData().setCreated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            planValidityResponseModel.getData().setUpdated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            expiryDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityResponseModel.getData().getUpdated_at());
        } catch (Exception e) {
        }
        when(serviceConfig.getRechargeNumberToCustomerIdLimit()).thenReturn(100);
        PlanValidityDataResponseModel planValidityDataResponseModel=planValidityResponseModel.getData();
        PlanExpiryHistory planExpiryHistory= new PlanExpiryHistory(planValidityDataResponseModel.getRecharge_number()
                , planValidityDataResponseModel.getService()
                , planValidityDataResponseModel.getCircle()
                , planValidityDataResponseModel.getOperator()
                , planValidityDataResponseModel.getPlan_bucket()
                , expiryDate
                , planValidityDataResponseModel.getCustomer_id()
                , planValidityDataResponseModel.getCategory_name()
                , expiryDate
                , expiryDate
                , new HashMap<String, String>()
                , planValidityDataResponseModel.getAmount());

        Integer notificationStatus = planValidityResponseModel.getData().getNotification_status();
        Recents recentObj = this.prepareRecentsData(planExpiryHistory,planValidityResponseModel);
        recentObj.setNotificationStatus(notificationStatus);
        List<Recents> recents=new ArrayList<>();
        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.insertIntoRecentAndCustomerBill(recentObj,planExpiryHistory);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),source.capture());
        verify(customerBillDao,times(1)).saveRecentToCustomerBill(any(),any());
    }

    @Test(expected = PlanValidityListenerException.class)
    public void updateExpriyInfoForNewCustomerDBFailure() throws PlanValidityListenerException, RecentDataToKafkaException{
        Date expiryDate=null;
        String kafkaPckt="{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"update\",\"ts\":1683006590,\"xid\":6234120002,\"commit\":true,\"data\":{\"id\":1670901969,\"recharge_number\":\"9983143527\",\"customer_id\":239349430,\"service\":\"mobile\",\"operator\":\"vodafone idea\",\"circle\":\"rajasthan\",\"amount\":719,\"validity_expiry_date\":\"2023-07-24 23:59:59\",\"order_date\":\"2022-11-11 19:03:28\",\"latest_recharge_date\":\"2023-05-02 11:18:54\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan\",\"created_at\":\"2023-02-28 13:53:58\",\"updated_at\":\"2023-05-02 05:49:50\",\"cust_mobile\":\"9887243234\",\"cust_email\":\"<EMAIL>\",\"rn_customer_id\":null,\"order_ids\":\",20493234745,20747747341,20994060203\",\"status\":11,\"extra\":\"{\\\"customer_type\\\":1}\",\"notification_status\":1,\"cust_rech_meta\":\"\",\"product_id\":*********},\"old\":{\"notification_status\":2}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(kafkaPckt, PlanValidityResponseModel.class);

        try{
            planValidityResponseModel.getData().setValidity_expiry_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            planValidityResponseModel.getData().setCreated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            planValidityResponseModel.getData().setUpdated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            expiryDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityResponseModel.getData().getUpdated_at());
        } catch (Exception e) {
        }
        when(serviceConfig.getRechargeNumberToCustomerIdLimit()).thenReturn(100);
        PlanValidityDataResponseModel planValidityDataResponseModel=planValidityResponseModel.getData();
        PlanExpiryHistory planExpiryHistory= new PlanExpiryHistory(planValidityDataResponseModel.getRecharge_number()
                , planValidityDataResponseModel.getService()
                , planValidityDataResponseModel.getCircle()
                , planValidityDataResponseModel.getOperator()
                , planValidityDataResponseModel.getPlan_bucket()
                , expiryDate
                , planValidityDataResponseModel.getCustomer_id()
                , planValidityDataResponseModel.getCategory_name()
                , expiryDate
                , expiryDate
                , new HashMap<String, String>()
                , planValidityDataResponseModel.getAmount());

        Integer notificationStatus = planValidityResponseModel.getData().getNotification_status();
        Recents recentObj = this.prepareRecentsData(planExpiryHistory,planValidityResponseModel);
        recentObj.setNotificationStatus(notificationStatus);
        List<Recents> recents=new ArrayList<>();
        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(false);
        recentService.insertIntoRecentAndCustomerBill(recentObj,planExpiryHistory);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),null);
        verify(customerBillDao,times(1)).saveRecentToCustomerBill(any(),any());
    }

    @Test(expected = PlanValidityListenerException.class)
    public void exceptionWhileUpdatingPlanExpiryDataCase2() throws PlanValidityListenerException, RecentDataToKafkaException {
        Date expiryDate=null;
        String kafkaPckt="{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"update\",\"ts\":1683006590,\"xid\":6234120002,\"commit\":true,\"data\":{\"id\":1670901969,\"recharge_number\":\"9983143527\",\"customer_id\":239349430,\"service\":\"mobile\",\"operator\":\"vodafone idea\",\"circle\":\"rajasthan\",\"amount\":719,\"validity_expiry_date\":\"2023-07-24 23:59:59\",\"order_date\":\"2022-11-11 19:03:28\",\"latest_recharge_date\":\"2023-05-02 11:18:54\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan\",\"created_at\":\"2023-02-28 13:53:58\",\"updated_at\":\"2023-05-02 05:49:50\",\"cust_mobile\":\"9887243234\",\"cust_email\":\"<EMAIL>\",\"rn_customer_id\":null,\"order_ids\":\",20493234745,20747747341,20994060203\",\"status\":11,\"extra\":\"{\\\"customer_type\\\":1}\",\"notification_status\":1,\"cust_rech_meta\":\"\",\"product_id\":*********},\"old\":{\"notification_status\":2}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(kafkaPckt, PlanValidityResponseModel.class);

        try{
            planValidityResponseModel.getData().setValidity_expiry_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            planValidityResponseModel.getData().setCreated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            planValidityResponseModel.getData().setUpdated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            expiryDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityResponseModel.getData().getUpdated_at());
        } catch (Exception e) {
        }
        List<Long> customerIds=new ArrayList<>();
        customerIds.add(planValidityResponseModel.getData().getCustomer_id());
        customerIds.add(planValidityResponseModel.getData().getCustomer_id()+1);
        when(mappingService.getCustomerIdWithLimit(any(), any(), any(), any())).thenReturn(customerIds);
        when(serviceConfig.getRechargeNumberToCustomerIdLimit()).thenReturn(100);
        PlanValidityDataResponseModel planValidityDataResponseModel=planValidityResponseModel.getData();
        PlanExpiryHistory planExpiryHistory= new PlanExpiryHistory(planValidityDataResponseModel.getRecharge_number()
                , planValidityDataResponseModel.getService()
                , planValidityDataResponseModel.getCircle()
                , planValidityDataResponseModel.getOperator()
                , planValidityDataResponseModel.getPlan_bucket()
                , expiryDate
                , planValidityDataResponseModel.getCustomer_id()
                , planValidityDataResponseModel.getCategory_name()
                , expiryDate
                , expiryDate
                , new HashMap<String, String>()
                , planValidityDataResponseModel.getAmount());

        Integer notificationStatus = planValidityResponseModel.getData().getNotification_status();
        Recents recentObj = this.prepareRecentsData(planExpiryHistory,planValidityResponseModel);
        recentObj.setNotificationStatus(notificationStatus);
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(planValidityDataResponseModel.getService());
        recentsPrimaryKey.setRechargeNumber(planValidityDataResponseModel.getRecharge_number());
        recentsPrimaryKey.setCustomerId(planValidityDataResponseModel.getCustomer_id());
        recentsPrimaryKey.setPlanBucket(planValidityDataResponseModel.getPlan_bucket());
        recentsPrimaryKey.setOperator(planValidityDataResponseModel.getPlan_bucket());
        recents1.setKey(recentsPrimaryKey);
        recents1.setUpdatedAt(new Date());
        recents1.setOrderId(1234L);
        recents.add(recents1);

        Recents recents2=new Recents();
        RecentsPrimaryKey recentsPrimaryKey2=new RecentsPrimaryKey();
        recentsPrimaryKey2.setService(planValidityDataResponseModel.getService());
        recentsPrimaryKey2.setRechargeNumber(planValidityDataResponseModel.getRecharge_number());
        recentsPrimaryKey2.setCustomerId(planValidityDataResponseModel.getCustomer_id()+1);
        recentsPrimaryKey2.setPlanBucket(planValidityDataResponseModel.getPlan_bucket()+"_2");
        recentsPrimaryKey2.setOperator(planValidityDataResponseModel.getPlan_bucket());
        recents2.setKey(recentsPrimaryKey2);
        recents2.setUpdatedAt(new Date());
        recents2.setOrderId(1234L);
        recents.add(recents2);

        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.updateRecentWhenNoExistingData(any(), any())).thenReturn(false);
        recentService.insertIntoRecentAndCustomerBill(recentObj,planExpiryHistory);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),null);

        verify(customerBillDao,never()).saveRecentToCustomerBill(any(),any());
    }
    @Test()
    public void planDetailsSuccessFullyUpdatedCase2() throws PlanValidityListenerException, RecentDataToKafkaException {
        Date expiryDate=null;
        String kafkaPckt="{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"update\",\"ts\":1683006590,\"xid\":6234120002,\"commit\":true,\"data\":{\"id\":1670901969,\"recharge_number\":\"9983143527\",\"customer_id\":239349430,\"service\":\"mobile\",\"operator\":\"vodafone idea\",\"circle\":\"rajasthan\",\"amount\":719,\"validity_expiry_date\":\"2023-07-24 23:59:59\",\"order_date\":\"2022-11-11 19:03:28\",\"latest_recharge_date\":\"2023-05-02 11:18:54\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan\",\"created_at\":\"2023-02-28 13:53:58\",\"updated_at\":\"2023-05-02 05:49:50\",\"cust_mobile\":\"9887243234\",\"cust_email\":\"<EMAIL>\",\"rn_customer_id\":null,\"order_ids\":\",20493234745,20747747341,20994060203\",\"status\":11,\"extra\":\"{\\\"customer_type\\\":1}\",\"notification_status\":1,\"cust_rech_meta\":\"\",\"product_id\":*********},\"old\":{\"notification_status\":2}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(kafkaPckt, PlanValidityResponseModel.class);

        try{
            planValidityResponseModel.getData().setValidity_expiry_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            planValidityResponseModel.getData().setCreated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            planValidityResponseModel.getData().setUpdated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            expiryDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityResponseModel.getData().getUpdated_at());
        } catch (Exception e) {
        }
        List<Long> customerIds=new ArrayList<>();
        customerIds.add(planValidityResponseModel.getData().getCustomer_id());
        customerIds.add(planValidityResponseModel.getData().getCustomer_id()+1);
        when(mappingService.getCustomerIdWithLimit(any(), any(), any(), any())).thenReturn(customerIds);
        when(serviceConfig.getRechargeNumberToCustomerIdLimit()).thenReturn(100);
        PlanValidityDataResponseModel planValidityDataResponseModel=planValidityResponseModel.getData();
        PlanExpiryHistory planExpiryHistory= new PlanExpiryHistory(planValidityDataResponseModel.getRecharge_number()
                , planValidityDataResponseModel.getService()
                , planValidityDataResponseModel.getCircle()
                , planValidityDataResponseModel.getOperator()
                , planValidityDataResponseModel.getPlan_bucket()
                , expiryDate
                , planValidityDataResponseModel.getCustomer_id()
                , planValidityDataResponseModel.getCategory_name()
                , expiryDate
                , expiryDate
                , new HashMap<String, String>()
                , planValidityDataResponseModel.getAmount());

        Integer notificationStatus = planValidityResponseModel.getData().getNotification_status();
        Recents recentObj = this.prepareRecentsData(planExpiryHistory,planValidityResponseModel);
        recentObj.setNotificationStatus(notificationStatus);
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(planValidityDataResponseModel.getService());
        recentsPrimaryKey.setRechargeNumber(planValidityDataResponseModel.getRecharge_number());
        recentsPrimaryKey.setCustomerId(planValidityDataResponseModel.getCustomer_id());
        recentsPrimaryKey.setPlanBucket(planValidityDataResponseModel.getPlan_bucket());
        recentsPrimaryKey.setOperator(planValidityDataResponseModel.getPlan_bucket());
        recents1.setKey(recentsPrimaryKey);
        recents1.setUpdatedAt(new Date());
        recents1.setOrderId(1234L);

        recents.add(recents1);

        Recents recents2=new Recents();
        RecentsPrimaryKey recentsPrimaryKey2=new RecentsPrimaryKey();
        recentsPrimaryKey2.setService(planValidityDataResponseModel.getService());
        recentsPrimaryKey2.setRechargeNumber(planValidityDataResponseModel.getRecharge_number());
        recentsPrimaryKey2.setCustomerId(planValidityDataResponseModel.getCustomer_id()+1);
        recentsPrimaryKey2.setPlanBucket(planValidityDataResponseModel.getPlan_bucket()+"_2");
        recentsPrimaryKey2.setOperator(planValidityDataResponseModel.getPlan_bucket());
        recents2.setKey(recentsPrimaryKey2);
        recents2.setUpdatedAt(new Date());
        recents2.setOrderId(1234L);
        recents.add(recents2);

        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.updateRecentWhenNoExistingData(any(), any())).thenReturn(true);
        recentService.insertIntoRecentAndCustomerBill(recentObj,planExpiryHistory);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);
        verify(recentDataToKafkaService,times(2)).setRecentConsumerSource(recentsArgumentCaptor.capture(),source.capture());
        verify(customerBillDao,times(1)).saveRecentToCustomerBill(any(),any());
    }


    @Test()
    public void planDetailsSuccessFullyUpdatedCase3() throws PlanValidityListenerException, RecentDataToKafkaException {
        Date expiryDate=null;
        String kafkaPckt="{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"update\",\"ts\":1683006590,\"xid\":6234120002,\"commit\":true,\"data\":{\"id\":1670901969,\"recharge_number\":\"9983143527\",\"customer_id\":239349430,\"service\":\"mobile\",\"operator\":\"vodafone idea\",\"circle\":\"rajasthan\",\"amount\":719,\"validity_expiry_date\":\"2023-07-24 23:59:59\",\"order_date\":\"2022-11-11 19:03:28\",\"latest_recharge_date\":\"2023-05-02 11:18:54\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan\",\"created_at\":\"2023-02-28 13:53:58\",\"updated_at\":\"2023-05-02 05:49:50\",\"cust_mobile\":\"9887243234\",\"cust_email\":\"<EMAIL>\",\"rn_customer_id\":null,\"order_ids\":\",20493234745,20747747341,20994060203\",\"status\":11,\"extra\":\"{\\\"customer_type\\\":1}\",\"notification_status\":1,\"cust_rech_meta\":\"\",\"product_id\":*********},\"old\":{\"notification_status\":2}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(kafkaPckt, PlanValidityResponseModel.class);

        try{
            planValidityResponseModel.getData().setValidity_expiry_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            planValidityResponseModel.getData().setCreated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            planValidityResponseModel.getData().setUpdated_at(planValidityResponseModel.getData().getValidity_expiry_date());
            expiryDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityResponseModel.getData().getUpdated_at());
        } catch (Exception e) {
        }
        List<Long> customerIds=new ArrayList<>();
        customerIds.add(planValidityResponseModel.getData().getCustomer_id());
        customerIds.add(planValidityResponseModel.getData().getCustomer_id()+1);
        when(mappingService.getCustomerIdWithLimit(any(), any(), any(), any())).thenReturn(customerIds);
        when(serviceConfig.getRechargeNumberToCustomerIdLimit()).thenReturn(100);
        PlanValidityDataResponseModel planValidityDataResponseModel=planValidityResponseModel.getData();
        PlanExpiryHistory planExpiryHistory= new PlanExpiryHistory(planValidityDataResponseModel.getRecharge_number()
                , planValidityDataResponseModel.getService()
                , planValidityDataResponseModel.getCircle()
                , planValidityDataResponseModel.getOperator()
                , planValidityDataResponseModel.getPlan_bucket()
                , expiryDate
                , planValidityDataResponseModel.getCustomer_id()
                , planValidityDataResponseModel.getCategory_name()
                , expiryDate
                , expiryDate
                , new HashMap<String, String>()
                , planValidityDataResponseModel.getAmount());

        Integer notificationStatus = planValidityResponseModel.getData().getNotification_status();
        Recents recentObj = this.prepareRecentsData(planExpiryHistory,planValidityResponseModel);
        recentObj.setNotificationStatus(notificationStatus);
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(planValidityDataResponseModel.getService());
        recentsPrimaryKey.setRechargeNumber(planValidityDataResponseModel.getRecharge_number());
        recentsPrimaryKey.setCustomerId(planValidityDataResponseModel.getCustomer_id());
        recentsPrimaryKey.setPlanBucket(planValidityDataResponseModel.getPlan_bucket());
        recentsPrimaryKey.setOperator(planValidityDataResponseModel.getPlan_bucket());
        recents1.setKey(recentsPrimaryKey);
        recents1.setUpdatedAt(new Date());
        recents1.setOrderId(1234L);

        recents.add(recents1);

        Recents recents2=new Recents();
        RecentsPrimaryKey recentsPrimaryKey2=new RecentsPrimaryKey();
        recentsPrimaryKey2.setService(planValidityDataResponseModel.getService());
        recentsPrimaryKey2.setRechargeNumber(planValidityDataResponseModel.getRecharge_number());
        recentsPrimaryKey2.setCustomerId(planValidityDataResponseModel.getCustomer_id()+1);
        recentsPrimaryKey2.setPlanBucket(planValidityDataResponseModel.getPlan_bucket()+"_2");
        recentsPrimaryKey2.setOperator(planValidityDataResponseModel.getPlan_bucket());
        recents2.setKey(recentsPrimaryKey2);
        recents2.setUpdatedAt(new Date());
        //recents2.setOrderId(1234L);
        recents.add(recents2);

        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(any(),any(),any(),any())).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        //when(recentsRepository.updateRecentWhenNoExistingData(any(), any())).thenReturn(true);
        recentService.insertIntoRecentAndCustomerBill(recentObj,planExpiryHistory);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<String> source = ArgumentCaptor.forClass(String.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),source.capture());
        verify(customerBillDao,times(1)).saveRecentToCustomerBill(any(),any());
    }

    private Recents prepareRecentsData(PlanExpiryHistory planExpiryHistory, PlanValidityResponseModel planValidityData){
        try {
            Date bill_update_time = new Date();
            Recents recents = new Recents();
            recents.setKey(new RecentsPrimaryKey());
            recents.getKey().setRechargeNumber(planExpiryHistory.getRechargeNumber());
            recents.getKey().setService(planExpiryHistory.getService());
            recents.getKey().setOperator(planExpiryHistory.getOperator());
            recents.getKey().setCustomerId(planExpiryHistory.getCustomerid());
            recents.setDueDate(planExpiryHistory.getValidity_expiry_date());
            recents.setDueAmount(planExpiryHistory.getAmount());
            if(StringUtils.isEmpty(planExpiryHistory.getPlan_bucket()))
                recents.getKey().setPlanBucket("");
            else{
                recents.getKey().setPlanBucket(planExpiryHistory.getPlan_bucket());
            }
            recents.setBillUpdateTime(bill_update_time);
            if ((Objects.nonNull(planValidityData.getOld()) && Objects.nonNull(planValidityData.getOld().getValidity_expiry_date()) || StringUtils.equalsIgnoreCase(planValidityData.getOperationType(), Constants.OP_INSERT)))
            {
                recents.setMarkAsPaidTime(null);
                recents.setIsMarkAsPaid(false);
                recents.setMarkAsPaidAmount(null);
                recents.setMarkAsPaidSource(null);
            }
            recents.setUpdatedAt(bill_update_time);
            recents.setPayType(Constants.CommonConstants.PREPAID_PAYTYPE);
            recents.setCircle(planExpiryHistory.getCircle());
            recents.setPlanName(planExpiryHistory.getCategory_name());
            return recents;
        }catch(Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Test
    public void omsTest1() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String lastFailureTxn = null;
        String lastPendingTxn = null;
        Boolean isTransaction = true;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,  rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",null,null,true,"7",false, "202", "true");
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),any());
        verify(recentsRepository,times(1)).updateRecentWhenNoExistingData(any(),any());
    }

    @Test
    public void omsTestIgnoreOldData() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        List<Recents> recentsList=new ArrayList<>();
        Recents recents=new Recents();
        recents.setTxnUpdatedAt(DateUtil.minutesIncrDecr(transactionUpdateTime,1));
        //recents.setExtra("{\"transactionUpdateTime\":\""+DateUtil.dateFormatter(DateUtil.minutesIncrDecr(transactionUpdateTime,1),TRANSACTION_UPDATED_DATE_FORMAT)+"\"}");
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recentsList.add(recents);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(any(), any(), any(), any())).thenReturn(recentsList);
        //when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,  rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",null,null,true,"7",false,"202","true");
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        verify(recentDataToKafkaService,times(0)).setRecentConsumerSource(recentsArgumentCaptor.capture(),any());
        verify(recentsRepository,times(0)).updateRecentWhenNoExistingData(any(),any());
    }

    @Test(expected = OMSListenerException.class)
    public void omsTest2() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(false);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,  rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",null,null,true,"",false, "202", "true");
        //verify(recentsRepository,times(1)).updateRecentWhenNoExistingData(any(),any());
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),null);

    }

    @Test(expected = OMSListenerException.class)
    public void omsTest3() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setRechargeNumber(rechargeNumber);
        recentsPrimaryKey.setCustomerId(customerId);
        recentsPrimaryKey.setPlanBucket(planBucket);
        recentsPrimaryKey.setOperator(operator);
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents.add(recents1);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(false);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,  rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",null,null,true,"",false,"202","true");
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        verify(recentDataToKafkaService,times(1)).setRecentConsumerSource(recentsArgumentCaptor.capture(),null);

        //verify(recentsRepository,times(1)).updateRecentWhenNoExistingData(any(),any());
    }
    @Test
    public void omsTest4() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setRechargeNumber(rechargeNumber);
        recentsPrimaryKey.setCustomerId(customerId);
        recentsPrimaryKey.setPlanBucket(planBucket);
        recentsPrimaryKey.setOperator(operator);
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents.add(recents1);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,  rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",null,null,true,"",false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(any(),any(),any());
    }

    @Test
    public void omsTestFailure() throws OMSListenerException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="08";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String status = "6";
        JSONObject lastFailureTxn = new JSONObject();
        String lastPenidngTxn = null ;
        lastFailureTxn.put("txn_id", 1234L);
        lastFailureTxn.put("txn_status", "6");
        lastFailureTxn.put("txn_time", transactionUpdateTime);
        lastFailureTxn.put("txn_amount", 342.0);

        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setRechargeNumber(rechargeNumber);
        recentsPrimaryKey.setCustomerId(customerId);
        recentsPrimaryKey.setPlanBucket(planBucket);
        recentsPrimaryKey.setOperator(operator);
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        List<String> smartReminderAllowedServices=new ArrayList<>();
        smartReminderAllowedServices.add("service");
        Map<String, Object> key=new HashMap<>();
        when(serviceConfig.getSmartRecentsEnabledServices()).thenReturn(smartReminderAllowedServices);
        FeatureConfigCache.getInstance().setFeatureConfigMap(key);
        recents1.setUpdatedAt(new Date());
        recents.add(recents1);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn.toString(),lastPenidngTxn,true,status,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(any(),any(),any());
        verify(smartRecentsRepository,times(1)).deleteRecentByCustomerIdAndServiceAndOperator(any(),any(),any());
        assertTrue(Objects.nonNull(recent.getValue().getTxnUpdatedAt()));
    }

    @Test
    public void omsTestPending() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="07";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String status = "15";
        JSONObject lastPenidngTxn = new JSONObject();
        lastPenidngTxn.put("txn_id", 12334L);
        lastPenidngTxn.put("txn_status", status);
        lastPenidngTxn.put("txn_time", transactionTime);
        lastPenidngTxn.put("txn_amount", 847.0);
        String lastFailureTxn = null;

        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setRechargeNumber(rechargeNumber);
        recentsPrimaryKey.setCustomerId(customerId);
        recentsPrimaryKey.setPlanBucket(planBucket);
        recentsPrimaryKey.setOperator(operator);
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents.add(recents1);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,lastPenidngTxn.toString(),true,status,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(any(),any(),any());
    }

    @Test
    public void omsTestFailureWithNoExistingData() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="08";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String status = "6";
        JSONObject lastFailureTxn = new JSONObject();
        String lastPenidngTxn = null ;
        lastFailureTxn.put("txn_id", 1234L);
        lastFailureTxn.put("txn_status", status);
        lastFailureTxn.put("txn_time", transactionUpdateTime);
        lastFailureTxn.put("txn_amount", 342.0);
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,  rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn.toString(),lastPenidngTxn,true,status,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenNoExistingData(any(),any());
    }

    @Test
    public void omsTestPendingWithNoExistingData() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="electricity";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="07";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String status = "15";
        JSONObject lastPenidngTxn = new JSONObject();
        lastPenidngTxn.put("txn_id", 12334L);
        lastPenidngTxn.put("txn_status", status);
        lastPenidngTxn.put("txn_time", transactionTime);
        lastPenidngTxn.put("txn_amount", 847.0);
        String lastFailureTxn = null;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,  rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,lastPenidngTxn.toString(),true,status,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenNoExistingData(any(),any());
    }


    @Test
    public void omsTestDeleteValidationRecord() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="airtel super recharge";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="prepaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String status = "";
        String lastFailureTxn = null;
        String lastPenidngTxn = null ;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setRechargeNumber(rechargeNumber);
        recentsPrimaryKey.setCustomerId(customerId);
        recentsPrimaryKey.setPlanBucket("airtel special");
        recentsPrimaryKey.setOperator(operator);
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setOrderId(null);
        recents1.setPayType("prepaid");
        recents.add(recents1);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6,  rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,lastPenidngTxn,true,status,false,"202","true");
        //verify(recentsRepository,times(1)).deleteRecentByCustomerIdAndServiceAndRecharge(any(),any(),any(),any(),any());
        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(any(),any(),any());
    }


    @Test
    public void testUpdateP2pDataInRecents() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"72006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        reminderCDC.getAfter().getUpdatedAt().setValue(new Date().getTime());
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("p2p");
        recents.getKey().setService("p2p");
        recents.getKey().setRechargeNumber("72006361538");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("cdc");
        recents.setEventSource("p2p");
        recents.setProductId(123L);
        //recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        recents.setUpdatedAt(DateUtil.stringToDate(DateUtil.formatDate(DateUtil.dateIncrDecr(new Date(),-1),DateFormats.DATE_TIME_FORMAT_2),DateFormats.DATE_TIME_FORMAT_2));

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(new Date(reminderCDC.getAfter().getDueDate().getValue()),recent.getValue().getDueDate());
        assertEquals("cook",recent.getValue().getTag());
        assertEquals("shubham",recent.getValue().getNickName());
        assertEquals("input2",recent.getValue().getRechargeNumber2());
    }

    @Test()
    public void testRecoveryPacketIgnore() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"72006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        reminderCDC.getAfter().getUpdatedAt().setValue(new Date().getTime());
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("p2p");
        recents.getKey().setService("p2p");
        recents.getKey().setRechargeNumber("72006361538");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("cdc");
        recents.setEventSource("p2p");
        recents.setProductId(123L);
        recentsList.add(recents);
        recents.setUpdatedAt(DateUtil.stringToDate(DateUtil.formatDate(DateUtil.dateIncrDecr(new Date(),1),DateFormats.DATE_TIME_FORMAT_2),DateFormats.DATE_TIME_FORMAT_2));

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        //when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }
    @Test
    public void testUpdateP2pDataInRecents1() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"72006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        reminderCDC.getAfter().getUpdatedAt().setValue(new Date().getTime());
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("p2p");
        recents.getKey().setService("p2p");
        recents.getKey().setRechargeNumber("72006361538");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("cdc");
        recents.setEventSource("p2p");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate(DateUtil.formatDate(DateUtil.dateIncrDecr(new Date(),-1),DateFormats.DATE_TIME_FORMAT_2),DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }
    @Test
    public void testUpdateP2pDataInRecents2() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"72006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
    }
    @Test(expected = CDCReminderListenerException.class)
    public void testUpdateP2pDataInRecents3() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"72006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(null);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
        assertEquals(new Date(reminderCDC.getAfter().getDueDate().getValue()),recent.getValue().getDueDate());
    }
    @Test(expected = CDCReminderListenerException.class)
    public void testUpdateP2pDataInRecents4() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"72006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("p2p");
        recents.getKey().setService("p2p");
        recents.getKey().setRechargeNumber("72006361538");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("cdc");
        recents.setEventSource("p2p");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        //when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(false);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(new Date(reminderCDC.getAfter().getDueDate().getValue()),recent.getValue().getDueDate());
    }
    @Test
    public void testUpdateP2pDataInRecents5() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"72006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":-23.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname32\\\":\\\"shubham\\\",\\\"tag23\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_32\\\":\\\"input2\\\",\\\"recharge_number_323\\\":\\\"input3\\\",\\\"recharge_number_442\\\":\\\"input6\\\",\\\"recharge_number_5131\\\":\\\"input4\\\",\\\"recharge_number_6121\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        reminderCDC.getAfter().getUpdatedAt().setValue(new Date().getTime());
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("p2p");
        recents.getKey().setService("p2p");
        recents.getKey().setRechargeNumber("72006361538");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("cdc");
        recents.setEventSource("p2p");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate(DateUtil.formatDate(DateUtil.dateIncrDecr(new Date(),-1),DateFormats.DATE_TIME_FORMAT_2),DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void test_updateRecentFromReminderCdcConsumer_where_partialBillStateIsPresent() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"72006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":-23.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname32\\\":\\\"shubham\\\",\\\"tag23\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_32\\\":\\\"input2\\\",\\\"recharge_number_323\\\":\\\"input3\\\",\\\"recharge_number_442\\\":\\\"input6\\\",\\\"recharge_number_5131\\\":\\\"input4\\\",\\\"recharge_number_6121\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        reminderCDC.getAfter().getUpdatedAt().setValue(new Date().getTime());
        StringEntity extraEntity = new StringEntity();
        JSONObject extraInfo = new JSONObject();
        extraInfo.put(Constants.PARTIAL_BILL_STATE, Constants.PARTIAL_BILL_STATE);
        extraEntity.setValue(extraInfo.toString());
        reminderCDC.getAfter().setExtra(extraEntity);
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("p2p");
        recents.getKey().setService("p2p");
        recents.getKey().setRechargeNumber("72006361538");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("cdc");
        recents.setEventSource("p2p");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate(DateUtil.formatDate(DateUtil.dateIncrDecr(new Date(),-1),DateFormats.DATE_TIME_FORMAT_2),DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        Integer expectedValue = 1;
        assertEquals(expectedValue, ttl.getValue());
    }
    @Test
    public void testUpdateSmsDataInRecentsWhenBillPaidEventNoRecentsFound() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":null,\"payment_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":null,\"amount\":{\"value\":-1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":null,\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\",\\\"eventState\\\":\\\"bill_paid\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
    }
    @Test
    public void testUpdateSmsDataInRecentsWhenBillPaidEventRecentsFound() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":null,\"payment_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":null,\"amount\":{\"value\":-1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":null,\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\",\\\"eventState\\\":\\\"bill_paid\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("hdfc");
        recents.getKey().setService("financial services");
        recents.getKey().setRechargeNumber("XXXX XXXX XXXX 1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("credit card");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        assertFalse(recentsList.isEmpty());

    }
    @Test
    public void testUpdateSmsDataInRecents() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("hdfc");
        recents.getKey().setService("financial services");
        recents.getKey().setRechargeNumber("XXXX XXXX XXXX 1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("credit card");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(new Date(reminderCDC.getAfter().getDueDate().getValue()),recent.getValue().getDueDate());
    }
    @Test
    public void testUpdateSmsDataInRecents1() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"null\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":null,\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("hdfc");
        recents.getKey().setService("financial services");
        recents.getKey().setRechargeNumber("XXXX XXXX XXXX 1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("credit card");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }
    @Test
    public void testUpdateSmsDataInRecents2() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"null\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":null,\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
    }
    @Test(expected = CDCReminderListenerException.class)
    public void testUpdateSmsDataInRecents3() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(null);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
        assertEquals(new Date(reminderCDC.getAfter().getDueDate().getValue()),recent.getValue().getDueDate());
    }
    @Test(expected = CDCReminderListenerException.class)
    public void testUpdateSmsDataInRecents4() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("hdfc");
        recents.getKey().setService("financial services");
        recents.getKey().setRechargeNumber("XXXX XXXX XXXX 1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("credit card");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        //when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(false);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(new Date(reminderCDC.getAfter().getDueDate().getValue()),recent.getValue().getDueDate());
    }
    //@Test
    public void testUpdateSmsDataInRecents5() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("hdfc");
        recents.getKey().setService("financial services");
        recents.getKey().setRechargeNumber("XXXX XXXX XXXX 1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("credit card");
        recents.setEventSource(null);
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }
    @Test
    public void testUpdateSmsDataInRecents6() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"5765876565565vfgf\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":1231.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"circle\":{\"value\":\"all circle\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBigfhgllAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("5486447464grbfy65");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("prepaid");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("all circle",recent.getValue().getCircle());
    }
    @Test
    public void testUpdateSmsDataInRecents7() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"5765876565565vfgf\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":-89.0,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":1231.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBigfhgllAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("5486447464grbfy65");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("prepaid");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void testRentPaymentSavingIntoRecentsNoDataExists() throws BillerAccountListenerException, RecentDataToKafkaException {
        BillerAccountKafkaModel billerAccountResponseModel = new BillerAccountKafkaModel();
        billerAccountResponseModel.setAccountId("03376a33d241a3f364b55ab95dd9ad90");
        billerAccountResponseModel.setId(1234L);
        billerAccountResponseModel.setBankAccountName("lata");
        billerAccountResponseModel.setIfscCode("HB 005");
        billerAccountResponseModel.setAccountStatus(1);
        billerAccountResponseModel.setCategoryId(215903L);
        billerAccountResponseModel.setName("mann");
        billerAccountResponseModel.setMaskAccountId("XX 45");
        billerAccountResponseModel.setUpdatedAt(new Date().toString());
        billerAccountResponseModel.setCreatedAt(new Date().toString());
        billerAccountResponseModel.setProductId(274545379L);
        billerAccountResponseModel.setCustomerId(310329952L);

        ProductMin product = new ProductMin();
        product.setProductId(274545379L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.insertionForRent(product,billerAccountResponseModel);
        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
        assertEquals("1234",recent.getValue().getKey().getRechargeNumber());
        assertEquals("**********",recent.getValue().getRechargeNumber7());


    }

    @Test
    public void testRentPaymentSavingIntoRecentsDataExists() throws BillerAccountListenerException, RecentDataToKafkaException {
        BillerAccountKafkaModel billerAccountResponseModel = new BillerAccountKafkaModel();
        billerAccountResponseModel.setAccountId("03376a33d241a3f364b55ab95dd9ad90");
        billerAccountResponseModel.setId(1234L);
        billerAccountResponseModel.setBankAccountName("lata");
        billerAccountResponseModel.setIfscCode("HB 005");
        billerAccountResponseModel.setAccountStatus(1);
        billerAccountResponseModel.setCategoryId(215903L);
        billerAccountResponseModel.setName("mann");
        billerAccountResponseModel.setMaskAccountId("XX 45");
        billerAccountResponseModel.setUpdatedAt(new Date().toString());
        billerAccountResponseModel.setCreatedAt(new Date().toString());
        billerAccountResponseModel.setProductId(274545379L);
        billerAccountResponseModel.setCustomerId(310329952L);

        ProductMin product = new ProductMin();
        product.setProductId(274545379L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("rent payment");
        recents.getKey().setService("rent payment");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("postpaid");
        recents.setProductId(274545379L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.insertionForRent(product,billerAccountResponseModel);
        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("1234",recent.getValue().getKey().getRechargeNumber());
        assertEquals("**********",recent.getValue().getRechargeNumber7());


    }
    @Test
    public void testUpdateSmsDataInRecents8() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"***********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("***********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        Recents recents1 = new Recents();
        recents1.setKey(new RecentsPrimaryKey());
        recents1.getKey().setCustomerId(********L);
        recents1.getKey().setOperator("jio");
        recents1.getKey().setService("mobile");
        recents1.getKey().setRechargeNumber("***********");
        recents1.getKey().setPlanBucket("data pack");
        recents1.setTxnAmount(100.0);
        recents1.setPayType("prepaid");
        recents1.setEventSource(null);
        recents1.setOrderId(233L);
        recents1.setProductId(123L);
        recents1.setUpdatedAt(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents1);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void testUpdateSmsDataInRecents19() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"***********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        when(serviceConfig.getWhitelistedCustIdsForMobileNonRUPersist()).thenReturn(null);
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("***********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        Recents recents1 = new Recents();
        recents1.setKey(new RecentsPrimaryKey());
        recents1.getKey().setCustomerId(********L);
        recents1.getKey().setOperator("jio");
        recents1.getKey().setService("mobile");
        recents1.getKey().setRechargeNumber("***********");
        recents1.getKey().setPlanBucket("data pack");
        recents1.setTxnAmount(100.0);
        recents1.setPayType("prepaid");
        recents1.setEventSource(null);
        recents1.setOrderId(233L);
        recents1.setProductId(123L);
        recents1.setUpdatedAt(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents1);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);

        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }
    // @Test
    public void testUpdateSmsDataInRecents9() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"***********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("***********");
        recents.getKey().setPlanBucket("special recharge");
        recents.setPayType("prepaid");
        recents.setEventSource(null);
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        Recents recents1 = new Recents();
        recents1.setKey(new RecentsPrimaryKey());
        recents1.getKey().setCustomerId(********L);
        recents1.getKey().setOperator("jio");
        recents1.getKey().setService("mobile");
        recents1.getKey().setRechargeNumber("***********");
        recents1.getKey().setPlanBucket("data pack");
        recents1.setTxnAmount(100.0);
        recents1.setPayType("prepaid");
        recents1.setEventSource(null);
        recents1.setProductId(123L);
        recents1.setUpdatedAt(DateUtil.stringToDate("2023-04-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents1);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void testInsertValidationDataInRecents() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(Collections.emptyList());
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
        assertTrue(recent.getValue().getIsValidation());
        assertEquals("validation",recent.getValue().getEventSource());
    }

    @Test
    public void testInsertValidationDataInRecents2() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(Collections.emptyList());
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
        assertTrue(recent.getValue().getIsSmsParsed());
        assertEquals("validation",recent.getValue().getEventSource());
    }

    @Test
    public void testUpdateValidationDataInRecents2() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":121202,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"special recharge\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        ObjectMapper objectMapper = new ObjectMapper();

        Map<String, String> map = new HashMap<>();
        map.put("plan_bucket", "Special Recharge");
        String jsonString="";
        try{
            jsonString = objectMapper.writeValueAsString(map);

        }
        catch(Exception ex){

        }

        reminderCDC.getAfter().getExtra().setValue(jsonString);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(121202L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("special recharge");
        recents.setPayType("prepaid");
        recents.setEventSource("validation");
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);

        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        List<String> prepaidServices = new ArrayList<>();
        prepaidServices.add("mobile");
        prepaidServices.add("dth");
        when(serviceConfig.getPrepaidServicesEligibleForNonRuValidation()).thenReturn(prepaidServices);
        List<String> smsBillUpdateServices=new ArrayList<>();
        smsBillUpdateServices.add("mobile");
        //when(serviceConfig.getSMSBillUpdateServices()).thenReturn(smsBillUpdateServices);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertTrue(recent.getValue().getIsSmsParsed());
        assertEquals("validation",recent.getValue().getEventSource());
    }

    @Test
    public void testUpdateValidationDataInRecents3() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":121202,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"special recharge\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        when(serviceConfig.getWhitelistedCustIdsForMobileNonRUPersist()).thenReturn(null);
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(121202L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("special recharge");
        recents.setPayType("prepaid");
        recents.setEventSource(null);
        recents.setOrderId(1236L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        //when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
//        assertEquals("validation",recent.getValue().getEventSource());
//        assertTrue(recent.getValue().getIsValidation());
    }

    @Test
    public void testIsUpdatedSourceValidation() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":121202,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"special recharge\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        assertTrue(recentService.isUpdatedSourceValidation(reminderCDC));
    }


    @Test
    public void testIsUpdatedSourceSms() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":121202,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"special recharge\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        boolean isUpdatedSourceValidation  =  recentService.isUpdatedSourceValidation(reminderCDC);
        assertTrue(!isUpdatedSourceValidation);
    }

    @Test
    public void  testExtrasNull() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":121202,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"special recharge\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        boolean isUpdatedSourceValidation  =  recentService.isUpdatedSourceValidation(reminderCDC);
        assertTrue(!isUpdatedSourceValidation);
    }

    @Test
    public void  testExtrasNull2() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":121202,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"special recharge\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":null,\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        boolean isUpdatedSourceValidation  =  recentService.isUpdatedSourceValidation(reminderCDC);
        assertTrue(!isUpdatedSourceValidation);
    }

    @Test
    public void testExtrasNull3() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":121202,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"special recharge\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        boolean isUpdatedSourceValidation  =  recentService.isUpdatedSourceValidation(reminderCDC);
        assertTrue(!isUpdatedSourceValidation);
    }

    @Test
    public void testUnsetBillsValidation() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":1688538099106,\"op\":\"d\",\"source\":{\"version\":\"2.1.4.Final\",\"connector\":\"cassandra\",\"name\":\"recon\",\"ts_ms\":1688538091696,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"test_reminder\",\"file\":\"CommitLog-7-*************.log\",\"pos\":9203742,\"keyspace\":\"reminder\",\"table\":\"bills_non_paytm\"},\"after\":{\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"customer_id\":{\"value\":900010,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bank_name\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bill_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bill_fetch_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"card_network\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"circle\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"create_at\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_email\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_mobile\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_other_info\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"extra\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"notification_status\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"payment_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"paytype\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"product_id\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"status\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"update_at\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"user_data\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"_range_start\":null,\"_range_end\":null}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);

        reminderCDC.getAfter().getPaytype().setValue("prepaid");

        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(900010L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setEventSource("validation");
        recents.setOrderId(null);
        recents.setDueDate(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setBillDate(DateUtil.stringToDate("2023-03-20 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueAmount(800.0);
        recents.setMinDueAmount(80.0);
        recents.setOriginalMinDueAmount(80.0);
        recents.setOriginalDueAmount(80.0);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.unsetBillForDeleteValidation(reminderCDC);

        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(0.0,recent.getValue().getDueAmount());
        assertEquals(0.0,recent.getValue().getMinDueAmount());
        assertEquals(0.0,recent.getValue().getOriginalMinDueAmount());
        assertEquals(null,recent.getValue().getDueDate());
        assertEquals(null,recent.getValue().getBillDate());


    }

    @Test
    public void testUnsetBillsValidation2() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":1688538099106,\"op\":\"d\",\"source\":{\"version\":\"2.1.4.Final\",\"connector\":\"cassandra\",\"name\":\"recon\",\"ts_ms\":1688538091696,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"test_reminder\",\"file\":\"CommitLog-7-*************.log\",\"pos\":9203742,\"keyspace\":\"reminder\",\"table\":\"bills_non_paytm\"},\"after\":{\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true},\"customer_id\":{\"value\":900010,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bank_name\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bill_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bill_fetch_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"card_network\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"circle\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"create_at\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_email\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_mobile\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_other_info\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"extra\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"notification_status\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"payment_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"paytype\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"product_id\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"status\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"update_at\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"user_data\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"_range_start\":null,\"_range_end\":null}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.getAfter().getPaytype().setValue("prepaid");

        reminderCDC.setRecoveryPacket(true);
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(900010L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("special rechagre");
        recents.setPayType("prepaid");
        recents.setEventSource(null);
        recents.setOrderId(null);
        recents.setDueDate(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setBillDate(DateUtil.stringToDate("2023-03-20 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueAmount(800.0);
        recents.setMinDueAmount(80.0);
        recents.setOriginalMinDueAmount(80.0);
        recents.setOriginalDueAmount(80.0);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.unsetBillForDeleteValidation(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());



    }


    @Test
    public void testTxnExists() throws CDCReminderListenerException {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(900010L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("postpaid");
        recents.setEventSource(null);
        recents.setOrderId(null);
        recents.setDueDate(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setBillDate(DateUtil.stringToDate("2023-03-20 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));

        Boolean isNotTxnRecord = recentService.isNotTxnRecord(recents);
        assertTrue(isNotTxnRecord);
    }

    @Test
    public void testTxnExists2() throws CDCReminderListenerException {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(900010L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("postpaid");
        recents.setEventSource(null);
        recents.setOrderId(null);
        recents.setLastFailureTxn("{\"txn_id\":2453451432,\"txn_time\":\"2023-05-04T13:00:00.000Z\",\"txn_amount\":401,\"txn_status\":\"08\",\"channel_id\":\"WEB 2\"}");
        recents.setDueDate(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setBillDate(DateUtil.stringToDate("2023-03-20 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));

        Boolean isNotTxnRecord = recentService.isNotTxnRecord(recents);
        assertFalse(isNotTxnRecord);
    }

    @Test
    public void testTxnExists3() throws CDCReminderListenerException {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(900010L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("postpaid");
        recents.setEventSource(null);
        recents.setOrderId(null);
        recents.setLastFailureTxn("{\"txn_id\":12342434231,\"txn_time\":\"2023-05-04T13:00:00.000Z\",\"txn_amount\":401,\"txn_status\":\"08\",\"channel_id\":\"WEB 2\"}");
        recents.setLastPendingTxn("{\"txn_id\":234543235234,\"txn_time\":\"2023-05-04T13:00:00.000Z\",\"txn_amount\":401,\"txn_status\":\"08\",\"channel_id\":\"WEB 2\"}");
        recents.setDueDate(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setBillDate(DateUtil.stringToDate("2023-03-20 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));

        Boolean isNotTxnRecord = recentService.isNotTxnRecord(recents);
        assertFalse(isNotTxnRecord);
    }

    @Test
    public void testTxnExists4() throws CDCReminderListenerException {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(900010L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("postpaid");
        recents.setEventSource(null);
        recents.setOrderId(1234L);
        recents.setDueDate(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setBillDate(DateUtil.stringToDate("2023-03-20 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));

        Boolean isNotTxnRecord = recentService.isNotTxnRecord(recents);
        assertFalse(isNotTxnRecord);
    }

    @Test
    public void testUpdateAllowedsmsBillUpdateServicesSMSDataInRecents() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":6655448,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"electricity\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"9874859745\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"bses rajdhani\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"postpaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":1715259406000,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        List<String> smsBillUpdateServices = new ArrayList<>();
        smsBillUpdateServices.add("electricity");
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(6655448L);
        recents.getKey().setOperator("bses rajdhani");
        recents.getKey().setService("electricity");
        recents.getKey().setRechargeNumber("9874859745");
        recents.getKey().setPlanBucket("");
        recents.setPayType("postpaid");
        recents.setEventSource(null);
        recents.setOrderId(1236L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(serviceConfig.getSMSBillUpdateServices()).thenReturn(smsBillUpdateServices);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(reminderCDC.getAfter().getCustomerId().getValue(),reminderCDC.getAfter().getService().getValue(),reminderCDC.getAfter().getRechargeNumber().getValue(),reminderCDC.getAfter().getOperator().getValue())).thenReturn(recentsList);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(recents.getKey().getCustomerId(),recents.getKey().getService(),recents.getKey().getRechargeNumber(),recents.getKey().getOperator(),recents.getKey().getPlanBucket())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void testUpdateNonAllowedsmsBillUpdateServicesSMSDataInRecents() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":6655448,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"9874859745\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"bses rajdhani\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"postpaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":1715259400000,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        List<String> smsBillUpdateServices = new ArrayList<>();
        smsBillUpdateServices.add("electricity");
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(6655448L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("9874859745");
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setEventSource(null);
        recents.setOrderId(1236L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(serviceConfig.getSMSBillUpdateServices()).thenReturn(smsBillUpdateServices);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(reminderCDC.getAfter().getCustomerId().getValue(),reminderCDC.getAfter().getService().getValue(),reminderCDC.getAfter().getRechargeNumber().getValue(),reminderCDC.getAfter().getOperator().getValue())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void testUpdateEmptyAllowedsmsBillUpdateServicesSMSDataInRecents() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":6655448,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"9874859745\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"bses rajdhani\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"postpaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        List<String> smsBillUpdateServices = new ArrayList<>();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(6655448L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("9874859745");
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setEventSource(null);
        recents.setOrderId(1236L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(serviceConfig.getSMSBillUpdateServices()).thenReturn(smsBillUpdateServices);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(reminderCDC.getAfter().getCustomerId().getValue(),reminderCDC.getAfter().getService().getValue(),reminderCDC.getAfter().getRechargeNumber().getValue(),reminderCDC.getAfter().getOperator().getValue())).thenReturn(recentsList);

        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }

    @Test
    public void testUpdatenonAllowedsmsBillUpdateServicesSMSDataInRecents1() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":6655448,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"electricity\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"9874859745\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"bses rajdhani\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"postpaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"transaction_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);
        List<String> smsBillUpdateServices = new ArrayList<>();
        smsBillUpdateServices.add("landline");
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(6655448L);
        recents.getKey().setOperator("bses rajdhani");
        recents.getKey().setService("electricity");
        recents.getKey().setRechargeNumber("9874859745");
        recents.getKey().setPlanBucket("");
        recents.setPayType("postpaid");
        recents.setEventSource(null);
        recents.setOrderId(1236L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(serviceConfig.getSMSBillUpdateServices()).thenReturn(smsBillUpdateServices);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(reminderCDC.getAfter().getCustomerId().getValue(),reminderCDC.getAfter().getService().getValue(),reminderCDC.getAfter().getRechargeNumber().getValue(),reminderCDC.getAfter().getOperator().getValue())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
    }


    @Test
    public void testFetchAndUpdateNickName() throws NickNameServiceException {
        NickNameRequest nickNameRequest = new NickNameRequest();
        nickNameRequest.setNickName("Hello");
        nickNameRequest.setOperator("jio");
        nickNameRequest.setPaytype("poastpaid");
        nickNameRequest.setService("mobile");
        nickNameRequest.setCustomerId(123456L);
        nickNameRequest.setRechargeNumber("***********");


        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("***********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("postpaid");
        recents.setProductId(123L);
        recents.setTxnTime(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        when(recentsRepository.selectNonCreditRecents(any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(Boolean.TRUE);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        recentService.fetchAndUpdateNickNameInRecents(nickNameRequest);

        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("Hello",recent.getValue().getNickName());

    }

    @Test
    public void testFetchAndUpdateNickNameForCC() throws NickNameServiceException {
        NickNameRequest nickNameRequest = new NickNameRequest();
        nickNameRequest.setNickName("Hello");
        nickNameRequest.setOperator("ccbp");
        nickNameRequest.setPaytype("credit card");
        nickNameRequest.setService("financial services");
        nickNameRequest.setCustomerId(123456L);
        nickNameRequest.setRechargeNumber("123w2531r23r23re1");
        nickNameRequest.setPanUniqueReference("123w2531r23r23re113423");


        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("ccbp");
        recents.getKey().setService("financial services");
        recents.getKey().setRechargeNumber("123w2531r23r23re113423");
        recents.getKey().setPlanBucket("");
        recents.setPayType("credit card");
        recents.setProductId(123L);
        recents.setTxnTime(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        when(recentsRepository.selectCreditCardRecents(any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(Boolean.TRUE);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        recentService.fetchAndUpdateNickNameInRecents(nickNameRequest);

        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("Hello",recent.getValue().getNickName());
    }

    @Test
    public void testTuitionFeeSavingIntoRecentsDataExists() throws BillerAccountListenerException, RecentDataToKafkaException {
        BillerAccountKafkaModel billerAccountResponseModel = new BillerAccountKafkaModel();
        billerAccountResponseModel.setAccountId("03376a33d241a3f364b55ab95dd9ad90");
        billerAccountResponseModel.setId(1234L);
        billerAccountResponseModel.setBankAccountName("lata");
        billerAccountResponseModel.setIfscCode("HB 005");
        billerAccountResponseModel.setAccountStatus(1);
        billerAccountResponseModel.setCategoryId(215903L);
        billerAccountResponseModel.setName("mann");
        billerAccountResponseModel.setMaskAccountId("XX 45");
        billerAccountResponseModel.setUpdatedAt(new Date().toString());
        billerAccountResponseModel.setCreatedAt(new Date().toString());
        billerAccountResponseModel.setProductId(2745479L);
        billerAccountResponseModel.setCustomerId(310329952L);
        billerAccountResponseModel.setPhoneNumber(123456L);

        ProductMin product = new ProductMin();
        product.setProductId(2745479L);
        product.setService("tuition fees");
        product.setOperator("tuition fees");
        product.setCategoryId(215903L);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("tuition fees");
        recents.getKey().setService("tuition fees");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("postpaid");
        recents.setProductId(2745479L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.insertionForTuitionFee(product,billerAccountResponseModel);
        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("mann",recent.getValue().getRechargeNumber7());
        assertEquals("1234",recent.getValue().getKey().getRechargeNumber());


    }

    @Test
    public void testTuitionFeeSavingIntoRecentsNoDataExists() throws BillerAccountListenerException, RecentDataToKafkaException {
        BillerAccountKafkaModel billerAccountResponseModel = new BillerAccountKafkaModel();
        billerAccountResponseModel.setAccountId("03376a33d241a3f364b55ab95dd9ad90");
        billerAccountResponseModel.setId(1234L);
        billerAccountResponseModel.setBankAccountName("lata");
        billerAccountResponseModel.setIfscCode("HB 005");
        billerAccountResponseModel.setAccountStatus(1);
        billerAccountResponseModel.setCategoryId(215903L);
        billerAccountResponseModel.setName("mann");
        billerAccountResponseModel.setMaskAccountId("XX 45");
        billerAccountResponseModel.setUpdatedAt(new Date().toString());
        billerAccountResponseModel.setCreatedAt(new Date().toString());
        billerAccountResponseModel.setProductId(2745479L);
        billerAccountResponseModel.setCustomerId(310329952L);
        billerAccountResponseModel.setPhoneNumber(**********L);

        ProductMin product = new ProductMin();
        product.setProductId(2745479L);
        product.setService("tuition fees");
        product.setOperator("tuition fees");
        product.setCategoryId(215903L);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(null);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.insertionForTuitionFee(product,billerAccountResponseModel);
        verify(recentsRepository).updateRecentWhenNoExistingData(recent.capture(),ttl.capture());
        assertEquals("1234",recent.getValue().getKey().getRechargeNumber());
        assertEquals("mann",recent.getValue().getRechargeNumber7());
        assertEquals("**********",recent.getValue().getRechargeNumber4());




    }

    @Test
    public void testRentTFData() throws JsonProcessingException {

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("tuition fees");
        recents.getKey().setService("tuition fees");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("postpaid");
        recents.setRechargeNumber5("XX 45");
        recents.setProductId(2745479L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        Map<String, Object> rentTfData = new HashMap<>();
        rentTfData.put("accountType",1);
        rentTfData.put("bankName","bank");
        rentTfData.put("bankAccountName","BANK");
        rentTfData.put("selfTransfer",1);
        recents.setRentTFData(new ObjectMapper().writeValueAsString(rentTfData));
        RecentUtils.getRentTFDataFE(recents);
    }

    @Test
    public void testFetchLatestMatchingRecentRepositoryThrowsException() {
        Long customerId = 123L;
        String service = "serviceTest";
        String last4Digits = "1234";
        Long productId = 456L;
        when(recentsRepository.findByCustomerIdAndService(customerId, service))
                .thenThrow(new RuntimeException("Database Error"));
        List<Recents> result = recentService.fetchLatestMatchingRecent(customerId, service);
        assertNull(result);
    }
    @Test
    public void testFetchLatestMatchingRecentRepository() {
        Long customerId = 999L;
        String service = "financial services";
        String last4Digits = "7889";
        Long productId = 1201258433L;
        List<Recents> recentsList=new ArrayList<>();
        Recents recent=mockRecentData();
        ProductMin product = new ProductMin();
        product.setProductId(1201258433L);
        product.setService("financial services");
        product.setOperator("axis");
        product.setPayType("credit card");
        product.setStatus(1);
        recentsList.add(recent);
        when(recentsRepository.findByCustomerIdAndService(customerId, service)).thenReturn(recentsList);
        List<Recents> result = recentService.fetchLatestMatchingRecent(customerId, service);
        assertNotNull(result);
        assertEquals(result.get(0).getPgCardId(),"test_id");
    }
    private Recents mockRecentData(){
        Recents recent=new Recents();
        recent.setProductId(1201258433L);
        RecentsPrimaryKey newRecentKey = new RecentsPrimaryKey();
        newRecentKey.setCustomerId(999L);
        newRecentKey.setService("existingService");
        newRecentKey.setOperator("existingOperator");
        newRecentKey.setPlanBucket("existingPlanBucket");
        newRecentKey.setRechargeNumber("existingRechargeNumber");
        recent.setCreatedAt(new Date());
        recent.setKey(newRecentKey);
        recent.setPgCardId("test_id");
        recent.setMcn("XXXX XXXX XXXX 7889");
        recent.setUpdatedAt(new Date());
        recent.setCreatedAt(new Date());
        return recent;
    }
    @Test
    public void testUpdateRecentTxnDetailsWhenRecentsListIsEmpty() throws OMSListenerException {
        String mcn="XXXX XXXX XXXX 7889";
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="service";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="airtel super recharge";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1201258433L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6="7889";
        String circle="circle";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String status = "7";
        String lastFailureTxn = null;
        String lastPenidngTxn = null ;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        String paytype = "credit card";
        List<Recents> recentsList=new ArrayList<>();
        ProductMin product = new ProductMin();
        product.setProductId(1201258433L);
        product.setService("financial services");
        product.setOperator("axis");
        product.setPayType("credit card");
        product.setStatus(1);
        Recents recent=mockRecentData();
        recentsList.add(recent);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6, null,  null,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,lastPenidngTxn,true,status,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenNoExistingData(any(),any());
    }

    @Test
    public void testUpdateRecentTxnDetailsWhenRecentsListIsEmptyFastag() throws OMSListenerException {
        String mcn="HR26BR9250";
        Long customerId=1L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="fastag recharge";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="airtel super recharge";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=1201258433L;
        Date transactionUpdateTime=new Date();
        String rechargeNumber="123";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6="7889";
        String circle="circle";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String status = "7";
        String lastFailureTxn = null;
        String lastPenidngTxn = null ;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        String paytype = "prepaid";
        List<Recents> recentsList=new ArrayList<>();
        ProductMin product = new ProductMin();
        product.setProductId(1201258433L);
        product.setService(service);
        product.setOperator(operator);
        product.setPayType(paytype);
        product.setStatus(1);
        Recents recent=mockRecentData();
        recentsList.add(recent);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, "LOW_BALANCE")).thenReturn(recentsList);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6, null,  null,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,lastPenidngTxn,true,status,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenNoExistingData(any(),any());
        verify(recentsRepository,times(2)).deleteRecentByCustomerIdAndServiceAndRecharge(any(), any(),any(),any(),any(),any());
    }


    @Test
    public void omsTestRepeatedMessage() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=310329952L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="mobile";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=2745479L;
        Date transactionUpdateTime=new Date(System.currentTimeMillis() - 3600 * 1000);
        String rechargeNumber="1234";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String lastFailureTxn = null;
        String lastPendingTxn = null;
        Boolean isTransaction = true;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;

        List<Recents> recentsList = new ArrayList<>();

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("operator");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setOrderId(1L);
        recents.setPayType("postpaid");
        recents.setRechargeNumber5("XX 45");
        recents.setProductId(2745479L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnUpdatedAt(new Date());
        recentsList.add(recents);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recentsList);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6, null,  null,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,null,true,null,false,"202","true");
        verify(recentsRepository,times(0)).updateRecentWhenDataAlreadyExist(any(),any(),any());
    }

    @Test
    public void omsTestNewMessage() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=310329952L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="mobile";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=2745479L;
        Date transactionUpdateTime=new Date(System.currentTimeMillis() + 3600 * 1000);
        String rechargeNumber="1234";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String lastFailureTxn = null;
        String lastPendingTxn = null;
        Boolean isTransaction = true;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;

        List<Recents> recentsList = new ArrayList<>();

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("operator");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setOrderId(1L);
        recents.setPayType("postpaid");
        recents.setRechargeNumber5("XX 45");
        recents.setProductId(2745479L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        JSONObject extraInfo = new JSONObject();
        extraInfo.put(Constants.TRANSACTION_UPDATE_TIME, String.valueOf(new Date()));
        recents.setExtra(extraInfo.toString());
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6, null,  null,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,null,true,null,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(any(),any(),any());
        assertEquals(false,recent.getValue().getExtra().isEmpty());
    }

    @Test
    public void omsTestNewMessageNoExtraInfo() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=310329952L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="mobile";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=2745479L;
        Date transactionUpdateTime=new Date(System.currentTimeMillis() + 3600 * 1000);
        String rechargeNumber="1234";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String lastFailureTxn = null;
        String lastPendingTxn = null;
        Boolean isTransaction = true;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;

        List<Recents> recentsList = new ArrayList<>();

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("operator");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setOrderId(1L);
        recents.setPayType("postpaid");
        recents.setRechargeNumber5("XX 45");
        recents.setProductId(2745479L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6, null,  null,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,null,true,null,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(any(),any(),any());
    }

    @Test
    public void omsTestNewMessageExtraInfoWithOutDate() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=310329952L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="mobile";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=2745479L;
        Date transactionUpdateTime=new Date(System.currentTimeMillis() + 3600 * 1000);
        String rechargeNumber="1234";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String lastFailureTxn = null;
        String lastPendingTxn = null;
        Boolean isTransaction = true;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;

        List<Recents> recentsList = new ArrayList<>();

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("operator");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setOrderId(1L);
        recents.setPayType("postpaid");
        recents.setRechargeNumber5("XX 45");
        recents.setProductId(2745479L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("createdSource", "sms");
        recents.setExtra(extraInfo.toString());
        recentsList.add(recents);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6, null,  null,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,null,true,null,false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(any(),any(),any());
    }
    @Test
    public void omsTestNewMessageExtraInfoWithOutDateWithPartialInfo() throws OMSListenerException, RecentDataToKafkaException {
        String mcn=null;
        Long customerId=310329952L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="mobile";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="abc";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=2745479L;
        Date transactionUpdateTime=new Date(System.currentTimeMillis() + 3600 * 1000);
        String rechargeNumber="1234";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="postpaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String lastFailureTxn = null;
        String lastPendingTxn = null;
        Boolean isTransaction = true;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;

        List<Recents> recentsList = new ArrayList<>();

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("operator");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setOrderId(1L);
        recents.setPayType("prepaid");
        recents.setProductId(2745479L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("createdSource", "sms");
        extraInfo.put("partialBillState", "EXPIRED");
        recents.setExtra(extraInfo.toString());
        recentsList.add(recents);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.updateRecentWhenNoExistingData(any(),any())).thenReturn(true);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> old_updated_at = ArgumentCaptor.forClass(Date.class);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6, null,  null,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,null,true,"7",false,"202","true");
        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(recentsArgumentCaptor.capture(),old_updated_at.capture(),ttl.capture());
        verify(recentsRepository,times(1)).updateRecentWhenNoExistingData(any(),any());
        assertEquals("{\"createdSource\":\"sms\",\"pgRespCode\":\"202\",\"is_retry_exhausted\":\"true\"}",recentsArgumentCaptor.getValue().getExtra());
    }//=

    @Test
    public void testUnsetBillsValidation3() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":1688538099106,\"op\":\"d\",\"source\":{\"version\":\"2.1.4.Final\",\"connector\":\"cassandra\",\"name\":\"recon\",\"ts_ms\":1688538091696,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"test_reminder\",\"file\":\"CommitLog-7-*************.log\",\"pos\":9203742,\"keyspace\":\"reminder\",\"table\":\"bills_non_paytm\"},\"after\":{\"recharge_number\":{\"value\":\"**********\",\"deletion_ts\":null,\"set\":true,\"plan_bucket\":\"Special Recharge\"},\"customer_id\":{\"value\":900010,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"airtel\",\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bank_name\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bill_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"bill_fetch_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"card_network\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"circle\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"create_at\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_email\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_mobile\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"customer_other_info\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"extra\":{\"value\":\"{\\\"plan_bucket\\\":\\\"Special Recharge\\\",\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\"},\"notification_status\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"payment_date\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"paytype\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"product_id\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"status\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"update_at\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"user_data\":{\"value\":null,\"deletion_ts\":****************,\"set\":true},\"_range_start\":null,\"_range_end\":null}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        reminderCDC.getAfter().getPaytype().setValue("prepaid");

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(900010L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("Special Recharge");
        recents.setPayType("prepaid");
        recents.setEventSource("validation");
        recents.setOrderId(null);
        recents.setDueDate(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setBillDate(DateUtil.stringToDate("2023-03-20 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueAmount(800.0);
        recents.setMinDueAmount(80.0);
        recents.setOriginalMinDueAmount(80.0);
        recents.setOriginalDueAmount(80.0);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.unsetBillForDeleteValidation(reminderCDC);

        verify(recentsRepository,times(1)).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals(0.0,recent.getValue().getDueAmount());
        assertEquals(0.0,recent.getValue().getMinDueAmount());
        assertEquals(0.0,recent.getValue().getOriginalMinDueAmount());
        assertEquals(null,recent.getValue().getDueDate());
        assertEquals(null,recent.getValue().getBillDate());


    }

    @Test
    public void testUpdateSmsDataInRecentsWithPlanBucketInExtra() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"5765876565565vfgf\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":1231.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"circle\":{\"value\":\"all circle\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"plan_bucket\\\":\\\"Special Recharge\\\",\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"plan_bucket\":\"Special Recharge\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBigfhgllAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("5486447464grbfy65");
        recents.getKey().setPlanBucket("Special Recharge");
        recents.setTxnAmount(100.0);
        recents.setPayType("prepaid");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        //assertEquals("all circle",recent.getValue().getCircle());
    }
    private Recents getRecentsTestData(){
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(310329952L);
        recents.getKey().setOperator("operator");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setPlanBucket("special");
        recents.setTxnAmount(100.0);
        recents.setOrderId(1L);
        recents.setPayType("prepaid");
        recents.setRechargeNumber5("XX 45");
        recents.setProductId(2745479L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("createdSource", "sms");
        recents.setExtra(extraInfo.toString());
        return recents;
    }
    @Test
    public void omsSuccessTestRecoDeletionForPrepaid() throws OMSListenerException {
        String mcn=null;
        Long customerId=310329952L;
        Long orderId=1L;
        Double txn_amount=100.0;
        String service="mobile";
        String operator="operator";
        String nickName="nickName";
        Date transactionTime=new Date();
        String planBucket="special";
        String cin=null;
        String par=null;
        String in_response_code="00";
        String tin=null;
        Boolean isTokenizedTransaction=false;
        Long productId=2745479L;
        Date transactionUpdateTime=new Date(System.currentTimeMillis() + 3600 * 1000);
        String status = "7";
        String rechargeNumber="1234";
        String rechargeNumber2=null;
        String rechargeNumber3=null;
        String rechargeNumber4=null;
        String rechargeNumber5=null;
        String rechargeNumber6=null;
        String rechargeNumber7=null;
        String rechargeNumber8=null;
        String circle="circle";
        String paytype="prepaid";
        String consumerName="consumerName";
        String cylinderAgencyName="cylinderAgencyName";
        String channel="Android";
        String lastFailureTxn = null;
        String lastPendingTxn = null;
        Boolean isTransaction = true;
        boolean isNickNameUpdated=true;
        Integer notPaidOnPaytm=0;
        List<Recents> recentsList = new ArrayList<>();
        Recents recents1 = getRecentsTestData();
        recentsList.add(recents1);
        Recents recents2 = getRecentsTestData();
        recents2.getKey().setPlanBucket(Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET);
        recentsList.add(recents2);
        ArgumentCaptor<Long> customerIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketCaptor = ArgumentCaptor.forClass(String.class);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId,txn_amount, service, operator, nickName,transactionTime,  planBucket,  cin,  par,  in_response_code,  tin,  isTokenizedTransaction,  productId,  transactionUpdateTime,  rechargeNumber,  rechargeNumber2,  rechargeNumber3,  rechargeNumber4,  rechargeNumber5,
                rechargeNumber6, rechargeNumber7,  rechargeNumber8,  circle,  paytype,  consumerName,  cylinderAgencyName,  channel, isNickNameUpdated, notPaidOnPaytm,"",lastFailureTxn,null,true,status,false,"202","true");
        verify(recentsRepository,times(1)).deleteRecentByCustomerIdAndServiceAndRecharge(any(),customerIdCaptor.capture(),serviceCaptor.capture(),rechargeNumberCaptor.capture(),operatorCaptor.capture(),planBucketCaptor.capture());
        assertEquals(customerId,customerIdCaptor.getValue());
        assertEquals(rechargeNumber,rechargeNumberCaptor.getValue());
        assertEquals(operator,operatorCaptor.getValue());
        assertEquals(Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET,planBucketCaptor.getValue());
    }
    @Test
    public void omsFailureTestRecoDeletionForPrepaid() throws OMSListenerException {
        String mcn = null;
        Long customerId = 310329952L;
        Long orderId = 1L;
        Double txn_amount = 100.0;
        String service = "mobile";
        String operator = "operator";
        String nickName = "nickName";
        Date transactionTime = new Date();
        String planBucket = Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET;
        String cin = null;
        String par = null;
        String in_response_code = "8";
        String tin = null;
        Boolean isTokenizedTransaction = false;
        Long productId = 2745479L;
        Date transactionUpdateTime = new Date(System.currentTimeMillis() + 3600 * 1000);
        String status = "6";
        String rechargeNumber = "1234";
        String rechargeNumber2 = null;
        String rechargeNumber3 = null;
        String rechargeNumber4 = null;
        String rechargeNumber5 = null;
        String rechargeNumber6 = null;
        String rechargeNumber7 = null;
        String rechargeNumber8 = null;
        String circle = "circle";
        String paytype = "prepaid";
        String consumerName = "consumerName";
        String cylinderAgencyName = "cylinderAgencyName";
        String channel = "Android";
        String lastFailureTxn = null;
        String lastPendingTxn = null;
        Boolean isTransaction = true;
        boolean isNickNameUpdated = true;
        Integer notPaidOnPaytm = 0;
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = getRecentsTestData();
        recentsList.add(recents);
        when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, service, operator)).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenNoExistingData(any(), any())).thenReturn(true);
        recentService.updateRecentTxnDetails(mcn, customerId, orderId, txn_amount, service, operator, nickName, transactionTime, planBucket, cin, par, in_response_code, tin, isTokenizedTransaction, productId, transactionUpdateTime, rechargeNumber, rechargeNumber2, rechargeNumber3, rechargeNumber4, rechargeNumber5,
                rechargeNumber6, rechargeNumber7, rechargeNumber8, circle, paytype, consumerName, cylinderAgencyName, channel, isNickNameUpdated, notPaidOnPaytm, "", lastFailureTxn, null, true, status, false,"202","true");
        verify(recentsRepository, times(0)).deleteRecentByCustomerIdAndServiceAndRecharge(any(),any(), any(), any(), any(), any());
    }
    @Test
    public void testUpdateSmsDataAllCirclesInRecents() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"5765876565565vfgf\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":1231.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"circle\":{\"value\":\"all circles\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBigfhgllAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("5486447464grbfy65");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("prepaid");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("all circles",recent.getValue().getCircle());
    }
    @Test
    public void testUpdateSmsDataSpecificCircleInRecents() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"5765876565565vfgf\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":1231.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"circle\":{\"value\":\"haryana\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBigfhgllAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("5486447464grbfy65");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("prepaid");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("haryana",recent.getValue().getCircle());
    }
    @Test
    public void testUpdateSmsDataAllCircleInRecentsCircleAlreadyExists() throws CDCReminderListenerException {
        String message = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"5765876565565vfgf\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":1231.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"circle\":{\"value\":\"all circles\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBigfhgllAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(message, ReminderCDC.class);
        reminderCDC.setRecoveryPacket(true);

        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setCustomerId(********L);
        recents.getKey().setOperator("jio");
        recents.getKey().setService("mobile");
        recents.getKey().setRechargeNumber("5486447464grbfy65");
        recents.getKey().setPlanBucket("");
        recents.setTxnAmount(100.0);
        recents.setPayType("prepaid");
        recents.setEventSource("sms");
        recents.setProductId(123L);
        recents.setPayType("prepaid");
        recents.setUpdatedAt(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setCircle("haryana");
        recentsList.add(recents);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldUpdatedAt = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(any(),any(),any(),any(),any())).thenReturn(recentsList);
        recentService.updateRecentFromReminderCdcConsumer(reminderCDC);

        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(),oldUpdatedAt.capture(),ttl.capture());
        assertEquals("haryana",recent.getValue().getCircle());
    }

    @Test
    public void updateReminderCdcCircleAndProductId_PayTypeMatched() {
        Recents recent1 = new Recents();
        recent1.setCircle("existingCircle1");
        recent1.setProductId(123L);
        recent1.setPayType("prepaid");
        recent1.setUpdatedAt(new Date());

        Recents recent2 = new Recents();
        recent2.setCircle("existingCircle2");
        recent2.setProductId(124L);
        recent2.setPayType("postpaid");
        recent2.setUpdatedAt(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));

        Recents recent3 = new Recents();
        recent3.setCircle("existingCircle3");
        recent3.setProductId(125L);
        recent3.setPayType("prepaid");
        recent3.setUpdatedAt(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));

        ReminderCDC reminderCDC = new ReminderCDC();
        reminderCDC.setAfter(new ReminderAfter());
        reminderCDC.getAfter().setCircle(new StringEntity("all circles"));
        reminderCDC.getAfter().setEventSource(new StringEntity(EVENTSOURCE_SMS));
        reminderCDC.getAfter().setProductId(new LongEntity(456L));
        reminderCDC.getAfter().setPaytype(new StringEntity("prepaid"));

        recentService.updateReminderCdcCircleAndProductId(Arrays.asList(recent1, recent2, recent3), reminderCDC);

        TestCase.assertEquals("existingCircle1", reminderCDC.getAfter().getCircle().getValue());
        TestCase.assertEquals(new Long(123L), reminderCDC.getAfter().getProductId().getValue());
    }

    @Test
    public void updateReminderCdcCircleAndProductId_PayTypeNotMatched() {
        Recents recent1 = new Recents();
        recent1.setCircle("existingCircle1");
        recent1.setProductId(123L);
        recent1.setPayType("postpaid");
        recent1.setUpdatedAt(new Date());

        Recents recent2 = new Recents();
        recent2.setCircle("existingCircle2");
        recent2.setProductId(124L);
        recent2.setPayType("postpaid");
        recent2.setUpdatedAt(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));

        ReminderCDC reminderCDC = new ReminderCDC();
        reminderCDC.setAfter(new ReminderAfter());
        reminderCDC.getAfter().setCircle(new StringEntity("all circles"));
        reminderCDC.getAfter().setEventSource(new StringEntity(EVENTSOURCE_SMS));
        reminderCDC.getAfter().setProductId(new LongEntity(456L));
        reminderCDC.getAfter().setPaytype(new StringEntity("prepaid"));

        recentService.updateReminderCdcCircleAndProductId(Arrays.asList(recent1, recent2), reminderCDC);

        TestCase.assertEquals("all circles", reminderCDC.getAfter().getCircle().getValue());
        TestCase.assertEquals(new Long(456L), reminderCDC.getAfter().getProductId().getValue());
    }

    @Test
    public void updateReminderCdcCircleAndProductId_PayTypeMatchedCircleNull() {
        Recents recent1 = new Recents();
        recent1.setCircle(null);
        recent1.setProductId(123L);
        recent1.setPayType("prepaid");
        recent1.setUpdatedAt(new Date());

        ReminderCDC reminderCDC = new ReminderCDC();
        reminderCDC.setAfter(new ReminderAfter());
        reminderCDC.getAfter().setCircle(new StringEntity("all circles"));
        reminderCDC.getAfter().setEventSource(new StringEntity(EVENTSOURCE_SMS));
        reminderCDC.getAfter().setProductId(new LongEntity(456L));
        reminderCDC.getAfter().setPaytype(new StringEntity("prepaid"));

        recentService.updateReminderCdcCircleAndProductId(Collections.singletonList(recent1), reminderCDC);

        TestCase.assertNull(reminderCDC.getAfter().getCircle().getValue());
        TestCase.assertEquals(new Long(123L), reminderCDC.getAfter().getProductId().getValue());
    }

    @Test
    public void updateReminderCdcCircleAndProductId_NoRecent() {
        ReminderCDC reminderCDC = new ReminderCDC();
        reminderCDC.setAfter(new ReminderAfter());
        reminderCDC.getAfter().setCircle(new StringEntity("all circles"));
        reminderCDC.getAfter().setEventSource(new StringEntity(EVENTSOURCE_SMS));
        reminderCDC.getAfter().setProductId(new LongEntity(456L));
        reminderCDC.getAfter().setPaytype(new StringEntity("prepaid"));

        recentService.updateReminderCdcCircleAndProductId(Collections.emptyList(), reminderCDC);

        TestCase.assertEquals("all circles", reminderCDC.getAfter().getCircle().getValue());
        TestCase.assertEquals(new Long(456L), reminderCDC.getAfter().getProductId().getValue());
    }

    @Test
    public void updateReminderCdcCircleAndProductId_PayTypeMatchedPidNull() {
        Recents recent1 = new Recents();
        recent1.setCircle("exitingCircle1");
        recent1.setProductId(null);
        recent1.setPayType("prepaid");
        recent1.setUpdatedAt(new Date());

        ReminderCDC reminderCDC = new ReminderCDC();
        reminderCDC.setAfter(new ReminderAfter());
        reminderCDC.getAfter().setCircle(new StringEntity("all circles"));
        reminderCDC.getAfter().setEventSource(new StringEntity(EVENTSOURCE_SMS));
        reminderCDC.getAfter().setProductId(new LongEntity(456L));
        reminderCDC.getAfter().setPaytype(new StringEntity("prepaid"));

        recentService.updateReminderCdcCircleAndProductId(Collections.singletonList(recent1), reminderCDC);

        TestCase.assertEquals("exitingCircle1", reminderCDC.getAfter().getCircle().getValue());
        TestCase.assertNull(reminderCDC.getAfter().getProductId().getValue());
    }
}
