package com.paytm.saga.service;

import static org.junit.Assert.*;

import com.paytm.saga.dto.*;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;

import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.Constants;


public class AutomaticCardServiceTest {
    @InjectMocks
    AutomaticCardService automaticCardService;
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void testAutomaticEnabledCard(){
        Integer isAutomatic = 1;
        AutomaticCardView automaticCard = automaticCardService.getAutomaticCard(isAutomatic);
        assertEquals(Constants.AUTOMATIC_ENABLED,automaticCard.getHeadings().get(0).getValue());
        System.out.println(automaticCard.getHeadings().get(0).getValue());
        assertEquals("Manage",automaticCard.getCta().get(0).getValue());
    }
}