package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.dto.catalogue.ProductAttributes;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.UpiCreditCardMessage;
import com.paytm.saga.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static com.paytm.saga.common.constant.Constants.ENABLE_UPI_CARD_WRITES;
import static com.paytm.saga.common.constant.Constants.WHITE_LISTED_CUST_IDS_UPI;
import static com.paytm.saga.common.constant.EncryptionConstants.ENABLE_DATA_ENCRYPTION_FLAG;

@RunWith(MockitoJUnitRunner.class)
public class UpiCreditCardServiceTest {

    @Mock
    private RecentsRepositoryWrapperService recentsRepositoryWrapperService;

    @Mock
    private MetricsHelper metricsHelper;

    @InjectMocks
    private UpiCreditCardService upiCreditCardService;

    @Before
    public void setUp() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();

        Map<String, String> bankNameMapping = new HashMap<>();
        bankNameMapping.put("AU Small Finance Bank Rupay Credit Card", "AUBL");
        bankNameMapping.put("Bank of Baroda Rupay Credit Card", "BOB");
        bankNameMapping.put("CITY UNION BANK LIMITED CREDIT CARD", "CITIUB");
        bankNameMapping.put("Canara Bank Credit Card", "CANARA");
        bankNameMapping.put("Canara Bank Rupay Credit Card", "CANARA");
        bankNameMapping.put("CSB Bank Rupay Credit Card", "CSB");
        bankNameMapping.put("Federal Bank Rupay Credit Card", "FDEB");
        bankNameMapping.put("HDFC Bank Rupay Credit Card", "HDFC");
        bankNameMapping.put("HDFC BANK NEW MOBILE", "HDFC");
        bankNameMapping.put("ICICI Bank Rupay Credit Card", "ICICI");
        bankNameMapping.put("IDFC FIRST Rupay Credit Card", "IDFC");
        bankNameMapping.put("Indian Bank Rupay Credit Card", "INDB");
        bankNameMapping.put("Indusind Bank Rupay Credit Card", "INDS");
        bankNameMapping.put("Kotak Mahindra Bank Rupay Credit Card", "NKMB");
        bankNameMapping.put("Punjab National Bank Rupay Credit Card", "PNB");
        bankNameMapping.put("RBL Bank RuPay Credit Card", "RATN");
        bankNameMapping.put("State Bank of India PSP", "SBI");
        bankNameMapping.put("SBI Rupay Credit Card", "SBI");
        bankNameMapping.put("Saraswat Co-op Bank RuPay Credit Card", "STB");
        bankNameMapping.put("SBM BANK INDIA RuPay Credit Card", "SBMI");
        bankNameMapping.put("Union Bank Rupay Credit Card", "UNI");
        bankNameMapping.put("Axis Bank Credit Card", "AXIS");
        bankNameMapping.put("Axis Bank Rupay Credit Card", "AXIS");
        bankNameMapping.put("Yes Bank Rupay Credit Card", "YES");

        String bankNameMapJson = objectMapper.writeValueAsString(bankNameMapping);
        String ifscJson = objectMapper.writeValueAsString(new HashMap<>()); // or `Collections.emptyMap()`

        ReflectionTestUtils.setField(upiCreditCardService, "bankNameMappingJson", bankNameMapJson);
        ReflectionTestUtils.setField(upiCreditCardService, "ifscCodeMappingJson", ifscJson);

        FeatureConfigCache.getInstance().setFeatureConfigMap(new HashMap<>());
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(ENABLE_DATA_ENCRYPTION_FLAG, true);
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(ENABLE_UPI_CARD_WRITES, true);
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(WHITE_LISTED_CUST_IDS_UPI, Arrays.asList("*********"));
        Product product = new Product();
        ProductAttributes productAttributes = new ProductAttributes();
        product.setProductId(123456L);
        product.setStatus(1);
        product.setService(Constants.FINANCIAL_SERVICES);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        productAttributes.setBankCode("HDFC");
        productAttributes.setCardNetwork(Constants.CardNetworkType.RUPAY);
        product.setAttributes(JsonUtils.serialiseJson(productAttributes));
        CVRProductCache.getInstance().addProductDetails(product);

        upiCreditCardService.init();
    }

    @Test
    public void processMessageTestWhenSuccessful(){
        String message = "{\"bankName\" : \"HDFC Bank Rupay Credit Card\", \"customerId\": \"*********\", \"last2CardDigits\": \"16\"}";
        UpiCreditCardMessage upiCreditCardMessage = JsonUtils.parseJson(message, UpiCreditCardMessage.class);
        upiCreditCardService.processUpiCreditCardMessage(upiCreditCardMessage, message, Constants.UPI_CREDIT_CARD_CONSUMER);
        Mockito.verify(recentsRepositoryWrapperService, Mockito.times(1)).updateRecentWhenNoExistingData(Mockito.any(Recents.class), Mockito.anyInt());
        Mockito.verify(metricsHelper, Mockito.times(1)).recordSuccessRate(Constants.UPI_CREDIT_CARD_CONSUMER, Constants.SUCCESS_EVENT);
    }


}
