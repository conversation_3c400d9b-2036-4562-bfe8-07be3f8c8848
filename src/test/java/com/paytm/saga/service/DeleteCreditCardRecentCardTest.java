package com.paytm.saga.service;

import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.dto.DeleteRequestBody;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.impl.DeleteCreditCardRecentCard;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class DeleteCreditCardRecentCardTest {
    @Mock
    RecentsRepositoryWrapperService recentsRepository;
    @Mock
    CustomerBillRepository customerBillRepository;
    @Mock
    RecentDataToKafkaService recentDataToKafkaService;
    @InjectMocks
    DeleteCreditCardRecentCard deleteCreditCardRecentCard;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    private Recents mockCCRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1221334");
        recentsPrimaryKey.setOperator("ccbp");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("credit card");
        recents.setMcn("XXXX XXXX XXXX 1234");
        recents.setDueDate(new Date());
        recents.setProductId(1234L);
        return recents;
    }

    private Recents mockCCRecents2(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("XXXX XXXX XXXX 1234");
        recentsPrimaryKey.setOperator("hdfc");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("credit card");
        recents.setMcn("XXXX XXXX XXXX 1234");
        recents.setDueDate(new Date());
        return recents;
    }

    @Test
    public void testDeleteRecentSmsCardWithRechargeNumberAndOperatorMatched() throws RecentDataToKafkaException {
        Recents recents=mockCCRecents2();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getKey().getRechargeNumber());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setCin(null);
        deleteRequestBody.setPar(null);
        deleteRequestBody.setOperator("visa_hdfc");
        deleteRequestBody.setProductId(1234L);
        String attributes = "{\"card_network\":\"dummyNetwork\",\"bank_code\":\"HDFC\"}";
        Product product = new Product();
        product.setProductId(1234L);
        product.setAttributes(attributes);
        CVRProductCache.getInstance().addProductDetails(product);
        deleteCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());


        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgument.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgument.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgument.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgument.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgument.getValue());

        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgumentCustomerBill.getValue());
    }

    @Test
    public void testDeleteRecentCardWhenMcnCardNetworkAndBankNameMatches() throws RecentDataToKafkaException {
        Recents recents=mockCCRecents();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getMcn());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setPar("1221334");
        deleteRequestBody.setProductId(1234L);
        String attributes = "{\"card_network\":\"visa\",\"bank_code\":\"HDFC\"}";
        Product product = new Product();
        product.setProductId(1234L);
        product.setAttributes(attributes);
        CVRProductCache.getInstance().addProductDetails(product);
        deleteCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());


        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgument.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgument.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgument.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgument.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgument.getValue());

        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgumentCustomerBill.getValue());
    }

    @Test
    public void testDeleteRecentCardWhenMcnMismatch() throws RecentDataToKafkaException {
        Recents recents=mockCCRecents();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("XXXX XXXX XXXX 1252");
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setCin("1221334");
        deleteRequestBody.setProductId(1234L);
        String attributes = "{\"card_network\":\"visa\",\"bank_code\":\"HDFC\"}";
        Product product = new Product();
        product.setProductId(1234L);
        product.setAttributes(attributes);
        CVRProductCache.getInstance().addProductDetails(product);
        deleteCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository,times(0)).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository,times(0)).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());

    }

    @Test
    public void testDeleteRecentCardWhenBankCodeMismatch() throws RecentDataToKafkaException {
        Recents recents=mockCCRecents();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getMcn());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setCin("1221334");
        deleteRequestBody.setProductId(12345L);
        String attributes1 = "{\"card_network\":\"visa\",\"bank_code\":\"HDFC\"}";
        Product product1 = new Product();
        product1.setProductId(1234L);
        product1.setAttributes(attributes1);
        CVRProductCache.getInstance().addProductDetails(product1);
        String attributes2 = "{\"card_network\":\"visa\",\"bank_code\":\"SBI\"}";
        Product product2 = new Product();
        product2.setProductId(12345L);
        product2.setAttributes(attributes2);
        CVRProductCache.getInstance().addProductDetails(product2);
        deleteCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository,times(0)).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository,times(0)).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());

    }

    @Test
    public void testDeleteRecentCardWhenCardNetworkMismatch() throws RecentDataToKafkaException {
        Recents recents=mockCCRecents();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getMcn());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setCin("1221334");
        deleteRequestBody.setProductId(12345L);
        String attributes1 = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product1 = new Product();
        product1.setProductId(1234L);
        product1.setAttributes(attributes1);
        CVRProductCache.getInstance().addProductDetails(product1);
        String attributes2 = "{\"card_network\":\"visa\",\"bank_code\":\"HDFC\"}";
        Product product2 = new Product();
        product2.setProductId(12345L);
        product2.setAttributes(attributes2);
        CVRProductCache.getInstance().addProductDetails(product2);
        deleteCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository,times(0)).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository,times(0)).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());

    }

    @Test
    public void testDeleteRecentCardWhenProductInfoViaRequestIsNull() throws RecentDataToKafkaException {
        Recents recents=mockCCRecents();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getMcn());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setCin("1221334");
        deleteRequestBody.setProductId(12345L);
        String attributes1 = "{\"card_network\":\"neft\",\"bank_code\":\"HDFC\"}";
        Product product1 = new Product();
        product1.setProductId(1234L);
        product1.setAttributes(attributes1);
        CVRProductCache.getInstance().addProductDetails(product1);
        deleteCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository,times(0)).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository,times(0)).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());

    }

    @Test
    public void testDeleteRecentCardWhenProductInfoViaRecentsIsNull() throws RecentDataToKafkaException {
        Recents recents=mockCCRecents();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getMcn());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setCin("1221334");
        deleteRequestBody.setProductId(12345L);
        String attributes2 = "{\"card_network\":\"visa\",\"bank_code\":\"HDFC\"}";
        Product product2 = new Product();
        product2.setProductId(12345L);
        product2.setAttributes(attributes2);
        CVRProductCache.getInstance().addProductDetails(product2);
        deleteCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository,times(0)).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository,times(0)).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());

    }

}
