package com.paytm.saga.service;

import com.paytm.saga.dto.CustomerNameRequest;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;


public class CustomerNameServiceTest {


    @Mock
    RecentsRepositoryWrapperService recentsRepository;

    @InjectMocks
    CustomerNameService customerNameService;


    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    private CustomerNameRequest mockHistoryCustomerNameRequest() {
        CustomerNameRequest customerNameRequest = new CustomerNameRequest();
        customerNameRequest.setService("mobile");
        customerNameRequest.setCustomer_id(1123L);
        customerNameRequest.setOperator("airtel");
        customerNameRequest.setRecharge_number("9812316169");
        customerNameRequest.setPid(null);
        return customerNameRequest;
    }

    @Test
    public void getCustomerNameWithNickNameTest(){
        CustomerNameRequest customerNameRequest = mockHistoryCustomerNameRequest();
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("postpaid");
        recents.setConsumerName("consumer name");
        recents.setNickName("nick name");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customerNameRequest.getCustomer_id(),customerNameRequest.getService(),
                customerNameRequest.getRecharge_number(),customerNameRequest.getOperator())).thenReturn(recentsList);

        String customerName  = customerNameService.getCustomerName(customerNameRequest.getCustomer_id(),customerNameRequest.getService(),
                customerNameRequest.getRecharge_number(),customerNameRequest.getOperator());
        assertEquals("nick name",customerName);

    }

    @Test
    public void getCustomerNameWithConsumerNameTest(){
        CustomerNameRequest customerNameRequest = mockHistoryCustomerNameRequest();
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("postpaid");
        recents.setConsumerName("consumer name");
        recents.setNickName(null);
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customerNameRequest.getCustomer_id(),customerNameRequest.getService(),
                customerNameRequest.getRecharge_number(),customerNameRequest.getOperator())).thenReturn(recentsList);

        String customerName  = customerNameService.getCustomerName(customerNameRequest.getCustomer_id(),customerNameRequest.getService(),
                customerNameRequest.getRecharge_number(),customerNameRequest.getOperator());
        assertEquals("consumer name",customerName);

    }

    @Test
    public void getCustomerNameWithBothNullTest(){
        CustomerNameRequest customerNameRequest = mockHistoryCustomerNameRequest();
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("postpaid");
        recents.setConsumerName(null);
        recents.setNickName(null);
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customerNameRequest.getCustomer_id(),customerNameRequest.getService(),
                customerNameRequest.getRecharge_number(),customerNameRequest.getOperator())).thenReturn(recentsList);
        String customerName  = customerNameService.getCustomerName(customerNameRequest.getCustomer_id(),customerNameRequest.getService(),
                customerNameRequest.getRecharge_number(),customerNameRequest.getOperator());
        assertEquals(null,customerName);

    }
}
