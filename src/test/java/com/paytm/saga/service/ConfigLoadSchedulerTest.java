package com.paytm.saga.service;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.configuration.ConfigLoadScheduler;
import com.paytm.saga.dto.DropOffConfigByService;
import com.paytm.saga.model.ApplicationConfig;
import com.paytm.saga.repository.ApplicationConfigRepository;

public class ConfigLoadSchedulerTest {
	@Mock
	ApplicationConfigRepository applicationConfigRepository;
	@Mock
	DropOffConfigByService dropOffConfigByService;
	@InjectMocks
	ConfigLoadScheduler configLoadScheduler;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	private ApplicationConfig mockApplicationConfig() {
		ApplicationConfig applicationConfig = new ApplicationConfig();
		applicationConfig.setCategory("mobile");
		applicationConfig.setService("dropoff");
		applicationConfig.setConfig("{\"newUserTimeDropOffTime\":100,\"recurringUserDropOffTime\":500}");
		return applicationConfig;
	}

	@Test
	public void loadConfigTest() {
		List<ApplicationConfig> applicationConfigList = new ArrayList<ApplicationConfig>();
		applicationConfigList.add(mockApplicationConfig());
		when(applicationConfigRepository.findByService("dropoff")).thenReturn(applicationConfigList);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> argument2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> argument3 = ArgumentCaptor.forClass(String.class);
		configLoadScheduler.loadConfig();
		verify(dropOffConfigByService).updateConfigConfigMap(argument1.capture(),argument2.capture(),argument3.capture());
	}
	@Test
	public void loadConfigTestWithNoAnyConfig() {
		List<ApplicationConfig> applicationConfigList = new ArrayList<ApplicationConfig>();
		when(applicationConfigRepository.findByService("dropoff")).thenReturn(applicationConfigList);
		configLoadScheduler.loadConfig();
	}
}
