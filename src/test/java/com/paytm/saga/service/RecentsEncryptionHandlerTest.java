package com.paytm.saga.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.util.AESUtil;
import com.paytm.saga.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.paytm.saga.common.constant.EncryptionConstants.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class RecentsEncryptionHandlerTest {

	@InjectMocks
	private RecentsEncryptionHandler recentsEncryptionHandler;

	@Mock
	private AESUtil aesUtil;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
		when(aesUtil.encrypt("XXXX XXXX XXXX 1234")).thenReturn("Enc Recharge Number");
		when(aesUtil.encrypt("100.0")).thenReturn("Enc Due Amount");
		when(aesUtil.encrypt("50.0")).thenReturn("Enc Min Due Amount");
		when(aesUtil.encrypt("150.0")).thenReturn("Enc Original Due Amount");
		when(aesUtil.encrypt("75.0")).thenReturn("Enc Original Min Due Amount");
		when(aesUtil.encrypt("2025-01-01 00:00:00.000")).thenReturn("Enc Due Date");
		when(aesUtil.encrypt("200.0")).thenReturn("Enc Current Outstanding Amount");
		when(aesUtil.encrypt("Card Variant")).thenReturn("Enc Card Variant");
		when(aesUtil.encrypt("Ram")).thenReturn("Enc Consumer Name");
		when(aesUtil.decrypt("Enc Recharge Number")).thenReturn("XXXX XXXX XXXX 1234");
		when(aesUtil.decrypt("Enc Due Amount")).thenReturn("100.0");
		when(aesUtil.decrypt("Enc Min Due Amount")).thenReturn("50.0");
		when(aesUtil.decrypt("Enc Original Due Amount")).thenReturn("150.0");
		when(aesUtil.decrypt("Enc Original Min Due Amount")).thenReturn("75.0");
		when(aesUtil.decrypt("Enc Due Date")).thenReturn("2025-01-01 00:00:00.000");
		when(aesUtil.decrypt("Enc Current Outstanding Amount")).thenReturn("200.0");
		when(aesUtil.decrypt("Enc Card Variant")).thenReturn("Card Variant");
		when(aesUtil.decrypt("Enc Consumer Name")).thenReturn("Ram");
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS, Collections.singletonList("issuingBankCardVariant"));
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);
	}

	@Test
	public void decryptRecentListWithNullInputReturnsEmptyList() throws AES256Exception {
		List<Recents> result = recentsEncryptionHandler.decryptRecentList(null);
		assertTrue(result.isEmpty());
	}

	@Test
	public void decryptRecentListWithEncryptioedRecentsReturnsDecryptedRecents() throws AES256Exception, JsonProcessingException {
		Recents recents = getEncryptedRecent();
		List<Recents> result = recentsEncryptionHandler.decryptRecentList(Collections.singletonList(recents));
		assertEquals(1, result.size());
		Recents decryptedRecents = result.get(0);
		assertEquals("XXXX XXXX XXXX 1234", decryptedRecents.getKey().getRechargeNumber());
		assertEquals(100.0, decryptedRecents.getDueAmount(), 0.0);
		assertEquals(50.0, decryptedRecents.getMinDueAmount(), 0.0);
		assertEquals(150.0, decryptedRecents.getOriginalDueAmount(), 0.0);
		assertEquals(75.0, decryptedRecents.getOriginalMinDueAmount(), 0.0);
		assertNotNull(decryptedRecents.getDueDate());
		assertEquals(200.0, decryptedRecents.getCurrentOutstandingAmount(), 0.0);
		assertEquals("Card Variant", decryptedRecents.getCardVariant());
		assertEquals("Ram", decryptedRecents.getConsumerName());
		Map<String, Object> extra = JsonUtils.parseJson(decryptedRecents.getExtra(), Map.class);
		assertEquals("Card Variant", extra.get("issuingBankCardVariant"));
	}

	@Test
	public void decryptRecentListWithEncryptioedRecentsReturnsDecryptedRecentsExtraNull() throws AES256Exception, ParseException, JsonProcessingException {
		Recents recents = getEncryptedRecent();
		recents.setExtra(null);
		List<Recents> result = recentsEncryptionHandler.decryptRecentList(Collections.singletonList(recents));
		assertEquals(1, result.size());
		Recents decryptedRecents = result.get(0);
		assertEquals("XXXX XXXX XXXX 1234", decryptedRecents.getKey().getRechargeNumber());
		assertEquals(100.0, decryptedRecents.getDueAmount(), 0.0);
		assertEquals(50.0, decryptedRecents.getMinDueAmount(), 0.0);
		assertEquals(150.0, decryptedRecents.getOriginalDueAmount(), 0.0);
		assertEquals(75.0, decryptedRecents.getOriginalMinDueAmount(), 0.0);
		assertNotNull(decryptedRecents.getDueDate());
		assertEquals(200.0, decryptedRecents.getCurrentOutstandingAmount(), 0.0);
		assertEquals("Card Variant", decryptedRecents.getCardVariant());
		assertEquals("Ram", decryptedRecents.getConsumerName());
		assertNull(decryptedRecents.getExtra());
	}

	@Test
	public void decryptRecentListWithEncryptioedRecentsReturnsDecryptedRecentsExtraEmpty() throws AES256Exception, JsonProcessingException {
		Recents recents = getEncryptedRecent();
		recents.setExtra("");
		List<Recents> result = recentsEncryptionHandler.decryptRecentList(Collections.singletonList(recents));
		assertEquals(1, result.size());
		Recents decryptedRecents = result.get(0);
		assertEquals("XXXX XXXX XXXX 1234", decryptedRecents.getKey().getRechargeNumber());
		assertEquals(100.0, decryptedRecents.getDueAmount(), 0.0);
		assertEquals(50.0, decryptedRecents.getMinDueAmount(), 0.0);
		assertEquals(150.0, decryptedRecents.getOriginalDueAmount(), 0.0);
		assertEquals(75.0, decryptedRecents.getOriginalMinDueAmount(), 0.0);
		assertNotNull(decryptedRecents.getDueDate());
		assertEquals(200.0, decryptedRecents.getCurrentOutstandingAmount(), 0.0);
		assertEquals("Card Variant", decryptedRecents.getCardVariant());
		assertEquals("Ram", decryptedRecents.getConsumerName());
		assertEquals("",decryptedRecents.getExtra());
	}

	@Test
	public void decryptRecentListWithEmptyListReturnsEmptyList() throws AES256Exception {
		List<Recents> result = recentsEncryptionHandler.decryptRecentList(Collections.emptyList());
		assertTrue(result.isEmpty());
	}


	@Test
	public void isEncryptedWithNullRecentsReturnsFalse() {
		assertFalse(recentsEncryptionHandler.isEncrypted(null));
	}

	@Test
	public void isEncryptedWithNonEncryptedRecentsReturnsFalse() {
		Recents recents = new Recents();
		recents.setIsEncrypted(0);
		assertFalse(recentsEncryptionHandler.isEncrypted(recents));
	}

	@Test
	public void isEncryptedWithEncryptedRecentsReturnsTrue() {
		Recents recents = new Recents();
		recents.setIsEncrypted(1);
		assertTrue(recentsEncryptionHandler.isEncrypted(recents));
	}

	@Test
	public void encryptRecentsWithDecryptedRecentsReturnsEncryptedRecents() throws AES256Exception, ParseException {
		Recents recents = getDecryptedRecent();
		Recents encryptedRecents = recentsEncryptionHandler.encryptRecent(recents);

		assertEquals("Enc Recharge Number", encryptedRecents.getKey().getRechargeNumber());
		assertEquals("Enc Due Amount", encryptedRecents.getEncDueAmount());
		assertEquals("Enc Min Due Amount", encryptedRecents.getEncMinDueAmount());
		assertEquals("Enc Original Due Amount", encryptedRecents.getEncOriginalDueAmount());
		assertEquals("Enc Original Min Due Amount", encryptedRecents.getEncOriginalMinDueAmount());
		assertEquals("Enc Due Date", encryptedRecents.getEncDueDate());
		assertEquals("Enc Current Outstanding Amount", encryptedRecents.getEncCurrentOutstandingAmount());
		assertEquals("Enc Card Variant", encryptedRecents.getCardVariant());
		assertEquals("Enc Consumer Name", encryptedRecents.getConsumerName());
		Map<String, Object> extra = JsonUtils.parseJson(encryptedRecents.getExtra(), Map.class);
		assertEquals("Enc Card Variant", extra.get("issuingBankCardVariant"));
	}

	@Test
	public void encryptRecentsWithDecryptedRecentsReturnsEncryptedRecentsExtraNull() throws AES256Exception, ParseException {
		Recents recents = getDecryptedRecent();
		recents.setExtra(null);
		Recents encryptedRecents = recentsEncryptionHandler.encryptRecent(recents);

		assertEquals("Enc Recharge Number", encryptedRecents.getKey().getRechargeNumber());
		assertEquals("Enc Due Amount", encryptedRecents.getEncDueAmount());
		assertEquals("Enc Min Due Amount", encryptedRecents.getEncMinDueAmount());
		assertEquals("Enc Original Due Amount", encryptedRecents.getEncOriginalDueAmount());
		assertEquals("Enc Original Min Due Amount", encryptedRecents.getEncOriginalMinDueAmount());
		assertEquals("Enc Due Date", encryptedRecents.getEncDueDate());
		assertEquals("Enc Current Outstanding Amount", encryptedRecents.getEncCurrentOutstandingAmount());
		assertEquals("Enc Card Variant", encryptedRecents.getCardVariant());
		assertEquals("Enc Consumer Name", encryptedRecents.getConsumerName());
		assertNull(encryptedRecents.getExtra());
	}

	@Test
	public void encryptRecentsWithDecryptedRecentsReturnsEncryptedRecentsExtraEmpty() throws AES256Exception, ParseException {
		Recents recents = getDecryptedRecent();
		recents.setExtra("");
		Recents encryptedRecents = recentsEncryptionHandler.encryptRecent(recents);

		assertEquals("Enc Recharge Number", encryptedRecents.getKey().getRechargeNumber());
		assertEquals("Enc Due Amount", encryptedRecents.getEncDueAmount());
		assertEquals("Enc Min Due Amount", encryptedRecents.getEncMinDueAmount());
		assertEquals("Enc Original Due Amount", encryptedRecents.getEncOriginalDueAmount());
		assertEquals("Enc Original Min Due Amount", encryptedRecents.getEncOriginalMinDueAmount());
		assertEquals("Enc Due Date", encryptedRecents.getEncDueDate());
		assertEquals("Enc Current Outstanding Amount", encryptedRecents.getEncCurrentOutstandingAmount());
		assertEquals("Enc Card Variant", encryptedRecents.getCardVariant());
		assertEquals("Enc Consumer Name", encryptedRecents.getConsumerName());
		assertEquals("",encryptedRecents.getExtra());
	}

	private Recents getEncryptedRecent() throws JsonProcessingException {
		Recents recents = new Recents();
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setRechargeNumber("Enc Recharge Number");
		recents.setKey(key);
		recents.setEncDueAmount("Enc Due Amount");
		recents.setEncMinDueAmount("Enc Min Due Amount");
		recents.setEncOriginalDueAmount("Enc Original Due Amount");
		recents.setEncOriginalMinDueAmount("Enc Original Min Due Amount");
		recents.setEncDueDate("Enc Due Date");
		recents.setEncCurrentOutstandingAmount("Enc Current Outstanding Amount");
		recents.setCardVariant("Enc Card Variant");
		recents.setConsumerName("Enc Consumer Name");
		Map<String, String> extra = new HashMap<>();
		extra.put("key1", "value1");
		extra.put("issuingBankCardVariant", "enc issuingBankCardVariant");
		recents.setExtra("{\"updated_source\":\"validationSync\",\"updated_data_source\":\"validationSync\",\"created_source\":\"validationSync\",\"source_subtype_2\":\"PARTIAL_BILL\",\"is_bbps\":true,\"recon_id\":\"uA4FnvuBIObe06HbxIypENet6KY=\",\"issuingBankCardVariant\":\"Enc Card Variant\",\"user_type\":\"RU\"}");
		recents.setIsEncrypted(1);
		return recents;
	}

	private Recents getDecryptedRecent() throws ParseException {
		Recents recents = new Recents();
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setRechargeNumber("XXXX XXXX XXXX 1234");
		recents.setKey(key);
		recents.setDueAmount(100.0);
		recents.setMinDueAmount(50.0);
		recents.setOriginalDueAmount(150.0);
		recents.setOriginalMinDueAmount(75.0);
		recents.setDueDate(new SimpleDateFormat(ENCRYPTION_DATE_FORMAT).parse("2025-01-01 00:00:00.000") );
		recents.setCurrentOutstandingAmount(200.0);
		recents.setCardVariant("Card Variant");
		recents.setConsumerName("Ram");
		Map<String, String> extra = new HashMap<>();
		extra.put("key1", "value1");
		extra.put("issuingBankCardVariant", "Card Variant");
		recents.setExtra("{\"updated_source\":\"validationSync\",\"updated_data_source\":\"validationSync\",\"created_source\":\"validationSync\",\"source_subtype_2\":\"PARTIAL_BILL\",\"is_bbps\":true,\"recon_id\":\"uA4FnvuBIObe06HbxIypENet6KY=\",\"issuingBankCardVariant\":\"Card Variant\",\"user_type\":\"RU\"}");
		recents.setIsEncrypted(0);
		return recents;
	}
}