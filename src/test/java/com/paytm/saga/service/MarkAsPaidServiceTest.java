package com.paytm.saga.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

import java.util.*;
import java.util.concurrent.*;

import com.paytm.saga.common.constant.FulfillmentTxnStatus;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.model.*;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.repository.*;
import com.paytm.saga.service.external.BillsSyncService;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.service.impl.ServiceConfig;
import com.timgroup.statsd.StatsDClient;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.configuration.SchedulerExecutorConfiguration;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.common.exception.MarkAsPaidServiceException;
import com.paytm.saga.common.exception.MarkAsPaidServiceWithDisplayMessageException;
import com.paytm.saga.dto.builder.ReminderHistoryBuilder;
import com.paytm.saga.listeners.DropOffDBHelper;
import com.paytm.saga.service.external.RecentCRUDService;
import com.paytm.saga.service.external.ReminderService;
import com.paytm.saga.util.JsonUtils;
import org.springframework.http.HttpStatus;

public class MarkAsPaidServiceTest {
	@Mock
	ChannelHistoryRepositoryWrapperService channelHistoryRepository;
	@Mock
	public DropOffRepository dropOffRepository;
	@Mock
	DropOffDBHelper dropOffDBHelper;
	@Mock
	ChannelHistoryService channelHistoryService;
	@Mock
	ReminderService reminderService;
	@Mock
	RecentService recentService;
	@Mock
	RecentCRUDService recentCRUDService;
	@Mock
	RecentsRepositoryWrapperService recentsRepository;
	@Mock
	ReminderHistoryRepositoryWrapperService reminderHistoryRepository;
	@Mock
	ChannelHistoryFinalizedRepositoryWrapperService channelHistoryFinalizedRepository;
	@Mock
	KafkaProducerService kafkaProducerService;
	@Mock
	private SchedulerExecutorConfiguration schedulerExecutorConfiguration;
	@Mock
	private DCATService dcatService;
	@Mock
	StatsDClient monitoringClient;
	@Mock
	private RPSService rpsService;
	@Mock
	private BillsSyncService billsSyncService;
	@Mock
	private ServiceConfig serviceConfig;

	@InjectMocks
	public MarkAsPaidService markAsPaidService;

	@Before
	public void setup() {
		Map<String,Object> featureConfigMap = new HashMap<>();
		featureConfigMap.put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,new ArrayList<>(Collections.emptyList()));
		FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfigMap);
		MockitoAnnotations.initMocks(this);
	}

	private ReminderStatusRequest mockReminderStatusUpdateRequest() {
		String data = "{\"rechargeNumber\": \"111111\",\"operator\" : \"airtel\",\"customerId\" : 121222,\"referenceId\" : \"XXXX\", \"paytype\" : \"prepaid\",\"circle\" : \"Andra Pradesh\",\"service\" : \"mobile\",\"productId\" : 1222323234,\"amount\" : 10}";
		return JsonUtils.parseJson(data, ReminderStatusRequest.class);
	}

	private MarkAsPaidRequest mockMarkAsPaidRequest() {
		String data = "{\"rechargeNumber\": \"111111\",\"operator\" : \"airtel\",\"customerId\" : 121222,\"referenceId\" : \"XXXX\", \"paytype\" : \"prepaid\",\"circle\" : \"Andra Pradesh\",\"service\" : \"mobile\",\"productId\" : 1222323234,\"amount\" : 10}";
		return JsonUtils.parseJson(data, MarkAsPaidRequest.class);
	}

	@Test
	public void updateDropOffReminderStatusTest() {
		ReminderStatusRequest markAsPaidRequest = mockReminderStatusUpdateRequest();
		ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
		markAsPaidService.createDropOffForDoNotRemindme(markAsPaidRequest);
		verify(dropOffDBHelper).insertDropOff(argument.capture());

		assertEquals(markAsPaidRequest.getCustomerId(), argument.getValue().getCustomerId());
		assertEquals(markAsPaidRequest.getProductId(), argument.getValue().getProductId());
		assertEquals(markAsPaidRequest.getRechargeNumber(), argument.getValue().getRechargeNumber());
		assertEquals(Constants.CommonConstants.RECHARGE_MESSAGE_TYPE, argument.getValue().getEventType());
		assertEquals("7", argument.getValue().getStatus());
		assertEquals("NA", argument.getValue().getBillsObj().get("plan_bucket"));
		assertEquals("true", argument.getValue().getBillsObj().get("donotRemindMeDropOffFlag"));
	}

	@Test(expected = MarkAsPaidServiceException.class)
	public void testIntiateProcessPrepaidPlanBucketNull()
			throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPlanBucket(null);
		markAsPaidService.intiateProcess(markAsPaidRequest);

	}

	@Test(expected = MarkAsPaidServiceException.class)
	public void testIntiateProcessPrepaidExpiryNull()
			throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setExpiry(null);
		markAsPaidService.intiateProcess(markAsPaidRequest);
	}

	@Test(expected = MarkAsPaidServiceException.class)
	public void testIntiateProcessPrepaidExpiryInvalid()
			throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setExpiry("23-08-2021");
		markAsPaidService.intiateProcess(markAsPaidRequest);
	}

	//@Test(expected = MarkAsPaidServiceException.class)
	public void testIntiateProcessPostpaidDueDateNull()
			throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("postpaid");
		markAsPaidRequest.setDue_date(null);
		markAsPaidService.intiateProcess(markAsPaidRequest);
	}

	@Test(expected = MarkAsPaidServiceException.class)
	public void testIntiateProcessPostpaidDueDateInValid()
			throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("postpaid");
		markAsPaidRequest.setDue_date("23-08-2021");
		markAsPaidService.intiateProcess(markAsPaidRequest);
	}

	@Test(expected = MarkAsPaidServiceException.class)
	public void testIntiateProcessPostpaidBillDateInValid()
			throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("postpaid");
		markAsPaidRequest.setDue_date("2021-08-08");
		markAsPaidRequest.setBill_date("23-08-2021");
		markAsPaidService.intiateProcess(markAsPaidRequest);
	}

	@Test
	public void testIntiateProcessPostpaidBillWhenOperationSuccessful()
			throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {
		MarkAsPaidService markAsPaidService2 = Mockito.spy(markAsPaidService);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("postpaid");
		markAsPaidRequest.setDue_date("2021-08-08");
		markAsPaidRequest.setBill_date("2021-08-08");
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(3));

		Callable<Object> res = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				return null;
			}
		};

		when(markAsPaidService2.serviceRequestTask(markAsPaidRequest)).thenReturn(res);
		MarkAsPaidResponse markAsPaidResponse = markAsPaidService2.intiateProcess(markAsPaidRequest);
		assertEquals(200, markAsPaidResponse.getStatusCode());
		assertEquals("Success", markAsPaidResponse.getDisplayMessage());
		assertEquals(null, markAsPaidResponse.getErrorCode());
		assertEquals(null, markAsPaidResponse.getErrorMessage());
		verify(markAsPaidService2, times(0)).createDropOffForMarkAsPaid(markAsPaidRequest);
		verify(markAsPaidService2, times(0)).createChannelHistoryForMarkAsPaid(markAsPaidRequest);
	}

	@Test
	public void testIntiateProcessPostpaidBillDateEmptyCase()
			throws ExecutionException, InterruptedException {
		MarkAsPaidService markAsPaidService2 = spy(markAsPaidService);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("postpaid");
		markAsPaidRequest.setDue_date("2021-08-08");
		markAsPaidRequest.setBill_date("");
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = new ReminderMarkAsPaidResponse();
				reminderMarkAsPaidResponse.setStatusCode("200");
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(3));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
						.thenReturn(recentRes);
		when(channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(Mockito.anyLong(),
				Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(null);
		when(channelHistoryFinalizedRepository.findByCustomerIdAndRechargeNumberAndService(Mockito.any(), Mockito.any(),
				Mockito.any())).thenReturn(null);
		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				markAsPaidRequest.getCustomerId(), markAsPaidRequest.getRechargeNumber(), markAsPaidRequest.getService().toLowerCase(), "false"
		)).thenReturn(null);


		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService2.serviceRequestTask(markAsPaidRequest));
		future.get();
		verify(markAsPaidService2, times(1)).createDropOffForMarkAsPaid(markAsPaidRequest);
		verify(markAsPaidService2, times(1)).createChannelHistoryForMarkAsPaid(markAsPaidRequest);
	}

	@Test
	public void testIntiateProcessPostpaidReminderResException()
			throws ExecutionException, InterruptedException {
		MarkAsPaidService markAsPaidServiceMock = Mockito.spy(markAsPaidService);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("postpaid");
		markAsPaidRequest.setDue_date("2021-08-08");
		markAsPaidRequest.setBill_date("");
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = null;
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(2));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
						.thenReturn(recentRes);

		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidServiceMock.serviceRequestTask(markAsPaidRequest));
		future.get();
		verify(markAsPaidServiceMock, times(1)).createDropOffForMarkAsPaid(markAsPaidRequest);
		verify(markAsPaidServiceMock, times(1)).createChannelHistoryForMarkAsPaid(markAsPaidRequest);
	}

	@Test
	public void testIntiateProcessPrepaidReminderResException()
			throws ExecutionException, InterruptedException {
		MarkAsPaidService markAsPaidService2 = spy(markAsPaidService);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("prepaid");
		markAsPaidRequest.setPlanBucket("plan");
		markAsPaidRequest.setExpiry("2021-08-12");
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = null;
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(2));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
						.thenReturn(recentRes);

		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService2.serviceRequestTask(markAsPaidRequest));
		future.get();

		verify(markAsPaidService2, times(1)).createDropOffForMarkAsPaid(markAsPaidRequest);
		verify(markAsPaidService2, times(1)).createChannelHistoryForMarkAsPaid(markAsPaidRequest);

	}

	@Test
	public void testIntiateProcessPostpaidRecentException()
			throws ExecutionException, InterruptedException {

		MarkAsPaidService markAsPaidService2 = spy(markAsPaidService);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("postpaid");
		markAsPaidRequest.setDue_date("2021-08-08");
		markAsPaidRequest.setBill_date("");
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = new ReminderMarkAsPaidResponse();
				reminderMarkAsPaidResponse.setStatusCode("200");
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = null;
				return recentCRUDResponse;
			}
		};
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(2));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
						.thenReturn(recentRes);

		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService2.serviceRequestTask(markAsPaidRequest));
		future.get();

		verify(markAsPaidService2, times(1)).createDropOffForMarkAsPaid(markAsPaidRequest);
		verify(markAsPaidService2, times(1)).createChannelHistoryForMarkAsPaid(markAsPaidRequest);

	}
	
	@Test
	public void testIntiateProcessPrepaidRecentException()
			throws ExecutionException, InterruptedException {

		MarkAsPaidService markAsPaidService2= spy(markAsPaidService);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setPaytype("prepaid");
		markAsPaidRequest.setPlanBucket("plan");
		markAsPaidRequest.setExpiry("2021-08-12");
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = new ReminderMarkAsPaidResponse();
				reminderMarkAsPaidResponse.setStatusCode("200");
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = null;
				return recentCRUDResponse;
			}
		};
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(2));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
						.thenReturn(recentRes);
		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService2.serviceRequestTask(markAsPaidRequest));
		future.get();

		verify(markAsPaidService2, times(1)).createDropOffForMarkAsPaid(markAsPaidRequest);
		verify(markAsPaidService2, times(1)).createChannelHistoryForMarkAsPaid(markAsPaidRequest);

	}
	@Test
	public void testCreateMarkAsPaidInChannelHistoryForPostpaidCase1() throws ExecutionException, InterruptedException {
		MarkAsPaidService markAsPaidService2 = spy(markAsPaidService);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setReferenceId("12345");
		markAsPaidRequest.setPaytype(Constants.CREDIT_CARD_PAYTYPE);
		markAsPaidRequest.setService(Constants.FINANCIAL_SERVICE);
		markAsPaidRequest.setDue_date("2021-08-08");
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = new ReminderMarkAsPaidResponse();
				reminderMarkAsPaidResponse.setStatusCode("200");
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};
		
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(2));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
						.thenReturn(recentRes);

		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService2.serviceRequestTask(markAsPaidRequest));
		future.get();

		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<String> refId = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> finalStatus = ArgumentCaptor.forClass(Boolean.class);
		verify(channelHistoryRepository).findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState(customerId.capture(), refId.capture(),
				service.capture(),finalStatus.capture());		
		
		assertEquals(markAsPaidRequest.getReferenceId(), refId.getValue());
		verify(markAsPaidService2, times(1)).createDropOffForMarkAsPaid(markAsPaidRequest);
		verify(markAsPaidService2, times(1)).createChannelHistoryForMarkAsPaid(markAsPaidRequest);

	}
	@Test
	public void testCreateMarkAsPaidInChannelHistoryForPostpaidCase4() throws ExecutionException, InterruptedException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setDue_date("2021-08-08");
		markAsPaidRequest.setBill_date("2021-08-07");
		markAsPaidRequest.setCircle("circle");
		markAsPaidRequest.setPaytype("postpaid");
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = new ReminderMarkAsPaidResponse();
				reminderMarkAsPaidResponse.setStatusCode("200");
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};
		
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(3));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
						.thenReturn(recentRes);

		List<ReminderHistory> reminderHistories=new ArrayList<ReminderHistory>();
		reminderHistories.add(new ReminderHistoryBuilder().setRechargeNumber(markAsPaidRequest.getRechargeNumber()).build());

		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndOperator(Mockito.any(),
				Mockito.any(),
				Mockito.any(),
				Mockito.any())).thenReturn(reminderHistories);
		
		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService.serviceRequestTask(markAsPaidRequest));
		future.get();
		
		ArgumentCaptor<ChannelHistory> channelHistory = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(channelHistory.capture(),service.capture());
		
		ArgumentCaptor<ReminderHistory> reminderHistory = ArgumentCaptor.forClass(ReminderHistory.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		verify(reminderHistoryRepository).save(reminderHistory.capture());

		
		assertEquals(EventTypes.MARKED_AS_PAID, channelHistory.getValue().getEventType());
		assertEquals(new Integer(15), reminderHistory.getValue().getStatus());
	}


	
	
	@Test
	public void testCreateMarkAsPaidInChannelHistoryForPrepaidCase1() throws ExecutionException, InterruptedException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setExpiry("2021-08-08");
		markAsPaidRequest.setCircle("circle");
		markAsPaidRequest.setPaytype("prepaid");
		markAsPaidRequest.setService("mobile");
		markAsPaidRequest.setPlanBucket("plan");
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = new ReminderMarkAsPaidResponse();
				reminderMarkAsPaidResponse.setStatusCode("200");
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};
		
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(3));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
						.thenReturn(recentRes);
		
		DCATGetPlanResponse dcatGetPlanResponse =new DCATGetPlanResponse();
		dcatGetPlanResponse.setAddon_benefit1("addon_benefit1");
		dcatGetPlanResponse.setAddon_benefit2("addon_benefit2");
		List<String> addonBenefit=new ArrayList<String>();
		dcatGetPlanResponse.setAddon_benefit(addonBenefit);
		dcatGetPlanResponse.setAddon_benefit3("addon_benefit3");
		dcatGetPlanResponse.setAddon_benefit4("addon_benefit4");
		dcatGetPlanResponse.setData("data");
		dcatGetPlanResponse.setDescription("description");
		dcatGetPlanResponse.setPlan_bucket("plan_bucket");
		dcatGetPlanResponse.setPlan_id("plan_id");
		dcatGetPlanResponse.setPrice("100");
		dcatGetPlanResponse.setSms("10");
		dcatGetPlanResponse.setTalktime("20");
		dcatGetPlanResponse.setValidity("20 days");
		
		when(dcatService.getPlanDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(dcatGetPlanResponse);

		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService.serviceRequestTask(markAsPaidRequest));
		future.get();

		ArgumentCaptor<ChannelHistory> channelHistory = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(channelHistory.capture(),service.capture());


		assertEquals(Constants.CommonConstants.MARK_AS_PAID_MESSAGE_TYPE, channelHistory.getValue().getEventType());
		assertEquals(FulfillmentTxnStatus.SUCCESS, channelHistory.getValue().getStatus());
	}
//	@Test
//	public void testUpdateRecentsTable() throws MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException, InterruptedException, RecentDataToKafkaException {
//		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
//		markAsPaidRequest.setPlanBucket("gkk");
//		markAsPaidRequest.setExpiry("2020-01-07");
//		markAsPaidRequest.setPaytype(Constants.PREPAID_PAYTYPE);
//		List<Recents> recentsList=new ArrayList<>();
//		Recents recents=new Recents();
//		recents.setKey(new RecentsPrimaryKey());
//		recents.getKey().setPlanBucket(markAsPaidRequest.getPlanBucket());
//		recents.getKey().setRechargeNumber(markAsPaidRequest.getRechargeNumber());
//		recentsList.add(recents);
//		when(recentService.updateMarkAsPaidDetailsInRecents(any(),any())).thenReturn(true);
//		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(Mockito.any(),
//				Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
//				.thenReturn(recentsList);
//		when(schedulerExecutorConfiguration.getScheduledExecutorService())
//				.thenReturn(Executors.newScheduledThreadPool(3));
//		ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);
//		ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
//		markAsPaidService.intiateProcess(markAsPaidRequest);
//		Thread.sleep(1000);
//		verify(recentService).updateMarkAsPaidDetailsInRecents(recentsArgumentCaptor.capture(),updatedAt.capture());
//		assertEquals(markAsPaidRequest.getPlanBucket(),recentsArgumentCaptor.getValue().getKey().getPlanBucket());
//	}

//	@Test
//	public void testCreateMarkAsPaidForPostpaidCaseWithRetry() throws ExecutionException, InterruptedException, MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException, RecentDataToKafkaException {
//		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
//		markAsPaidRequest.setDue_date("2021-08-08");
//		markAsPaidRequest.setBill_date("2021-08-07");
//		markAsPaidRequest.setCircle("circle");
//		markAsPaidRequest.setPaytype("postpaid");
//		List<Recents> recentsList=new ArrayList<>();
//		Recents recents=new Recents();
//		recents.setKey(new RecentsPrimaryKey());
//		recents.getKey().setPlanBucket(markAsPaidRequest.getPlanBucket());
//		recents.getKey().setRechargeNumber(markAsPaidRequest.getRechargeNumber());
//		recentsList.add(recents);
//		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(Mockito.any(),
//				Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
//				.thenReturn(recentsList);
//		when(recentService.updateMarkAsPaidDetailsInRecents(any(),any())).thenReturn(true);
//		when(schedulerExecutorConfiguration.getScheduledExecutorService())
//				.thenReturn(Executors.newScheduledThreadPool(3));
//		ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);
//		ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
//		markAsPaidService.intiateProcess(markAsPaidRequest);
//		Thread.sleep(1000);
//		verify(recentService).updateMarkAsPaidDetailsInRecents(recentsArgumentCaptor.capture(),updatedAt.capture());
//		assertEquals(true, recentsArgumentCaptor.getValue().getIsMarkAsPaid());
//
//	}

	@Test
	public void testCreateMarkAsPaidForPostpaidCaseWithNoRecent() throws ExecutionException, InterruptedException, MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException {
		MarkAsPaidService markAsPaidService2 = spy(markAsPaidService);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setDue_date("2021-08-08");
		markAsPaidRequest.setBill_date("2021-08-07");
		markAsPaidRequest.setCircle("circle");
		markAsPaidRequest.setPaytype("postpaid");
		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(Mockito.any(),
				Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
				.thenReturn(null);
		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(3));
		MarkAsPaidResponse markAsPaidResponse=markAsPaidService.intiateProcess(markAsPaidRequest);
		assertEquals(HttpStatus.OK,markAsPaidResponse.getHttpStatus());
	}
//
//	@Test
//	public void testUpdateRecentsForCC() throws ExecutionException, InterruptedException, MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException, RecentDataToKafkaException {
//		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
//		markAsPaidRequest.setDue_date("2020-01-07");
//		markAsPaidRequest.setPaytype(Constants.CREDIT_CARD_PAYTYPE);
//		markAsPaidRequest.setReferenceId("123w123");
//		markAsPaidRequest.setRechargeNumber("XXXX XXXX XXXX 1234");
//		markAsPaidRequest.setProductId(1234L);
//		markAsPaidRequest.setService(Constants.FINANCIAL_SERVICE);
//		String attributes = "{\"card_network\":\"visa\",\"bank_code\":\"HDFC\"}";
//		Product product = new Product();
//		product.setProductId(markAsPaidRequest.getProductId());
//		product.setAttributes(attributes);
//		CVRProductCache.getInstance().addProductDetails(product);
//		List<Recents> recentsList=new ArrayList<>();
//		Recents recents=new Recents();
//		recents.setKey(new RecentsPrimaryKey());
//		recents.getKey().setPlanBucket("");
//		recents.getKey().setRechargeNumber(markAsPaidRequest.getReferenceId());
//		recents.setMcn(markAsPaidRequest.getRechargeNumber());
//		recents.setPayType(Constants.CREDIT_CARD_PAYTYPE);
//		recents.getKey().setService(Constants.FINANCIAL_SERVICE);
//		recentsList.add(recents);
//		when(recentService.updateMarkAsPaidDetailsInRecents(any(),any())).thenReturn(true);
//		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(Mockito.any(),Mockito.any(),Mockito.any()))
//				.thenReturn(recentsList);
//		//when(kafkaProducerService.sendMessage(any())
//		when(schedulerExecutorConfiguration.getScheduledExecutorService())
//				.thenReturn(Executors.newScheduledThreadPool(3));
//		ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
//		ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);
//		markAsPaidService.intiateProcess(markAsPaidRequest);
//		Thread.sleep(1000);
//		verify(recentService).updateMarkAsPaidDetailsInRecents(recentsArgumentCaptor.capture(),updatedAt.capture());
//		assertEquals("",recentsArgumentCaptor.getValue().getKey().getPlanBucket());
//		assertEquals(markAsPaidRequest.getReferenceId(),recentsArgumentCaptor.getValue().getKey().getRechargeNumber());
//	}


//	@Test
//	public void testUpdateRecentsForSMS() throws ExecutionException, InterruptedException, MarkAsPaidServiceException, MarkAsPaidServiceWithDisplayMessageException, RecentDataToKafkaException {
//		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
//		markAsPaidRequest.setDue_date("2020-01-07");
//		markAsPaidRequest.setPaytype(Constants.CREDIT_CARD_PAYTYPE);
//		markAsPaidRequest.setReferenceId(null);
//		markAsPaidRequest.setRechargeNumber("XXXX XXXX XXXX 1234");
//		markAsPaidRequest.setProductId(1234L);
//		markAsPaidRequest.setService(Constants.FINANCIAL_SERVICE);
//		String attributes = "{\"card_network\":\"dummyNetwork\",\"bank_code\":\"HDFC\"}";
//		Product product = new Product();
//		product.setProductId(markAsPaidRequest.getProductId());
//		product.setAttributes(attributes);
//		CVRProductCache.getInstance().addProductDetails(product);
//		List<Recents> recentsList=new ArrayList<>();
//		Recents recents=new Recents();
//		recents.setKey(new RecentsPrimaryKey());
//		recents.getKey().setPlanBucket("");
//		recents.getKey().setRechargeNumber(markAsPaidRequest.getRechargeNumber());
//		recents.getKey().setOperator("hdfc");
//		recents.setMcn(markAsPaidRequest.getRechargeNumber());
//		recents.setPayType(Constants.CREDIT_CARD_PAYTYPE);
//		recents.getKey().setService(Constants.FINANCIAL_SERVICE);
//		recentsList.add(recents);
//		when(recentService.updateMarkAsPaidDetailsInRecents(any(),any())).thenReturn(true);
//		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
//				.thenReturn(recentsList);
//		//when(kafkaProducerService.sendMessage(any())
//		when(schedulerExecutorConfiguration.getScheduledExecutorService())
//				.thenReturn(Executors.newScheduledThreadPool(3));
//		ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
//		ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);
//		markAsPaidService.intiateProcess(markAsPaidRequest);
//		Thread.sleep(1000);
//		verify(recentService).updateMarkAsPaidDetailsInRecents(recentsArgumentCaptor.capture(),updatedAt.capture());
//		assertEquals("",recentsArgumentCaptor.getValue().getKey().getPlanBucket());
//		assertEquals(markAsPaidRequest.getRechargeNumber(),recentsArgumentCaptor.getValue().getKey().getRechargeNumber());
//		assertEquals("hdfc",recentsArgumentCaptor.getValue().getKey().getOperator());
//
//	}



	@Test
	public void testCreateMarkAsPaidInChannelHistoryForRpsResponse1() throws ExecutionException, InterruptedException {
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setExpiry("2021-08-08");
		markAsPaidRequest.setCircle("delhi ncr");
		markAsPaidRequest.setPaytype("prepaid");
		markAsPaidRequest.setService("mobile");
		markAsPaidRequest.setPlanBucket("plan");
		markAsPaidRequest.setProductId(Long.valueOf(185));
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = new ReminderMarkAsPaidResponse();
				reminderMarkAsPaidResponse.setStatusCode("200");
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};

		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(3));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(recentRes);

		String response = "{\"plan_bucket\":null,\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":null}";
		DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);


		when(rpsService.getPlanDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);

		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService.serviceRequestTask(markAsPaidRequest));
		future.get();

		ArgumentCaptor<ChannelHistory> channelHistory = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(channelHistory.capture(),service.capture());

		assertEquals(Constants.CommonConstants.MARK_AS_PAID_MESSAGE_TYPE, channelHistory.getValue().getEventType());
		assertEquals(FulfillmentTxnStatus.SUCCESS, channelHistory.getValue().getStatus());
		assertEquals("2.5GB/day",channelHistory.getValue().getDisplayValues().get("data"));
		assertEquals("100/Day",channelHistory.getValue().getDisplayValues().get("sms"));
		assertEquals("plan",channelHistory.getValue().getBillsObj().get("plan_bucket"));
	}
	@Test
	public void testCreateMarkAsPaidInChannelHistoryForRpsResponse2() throws ExecutionException, InterruptedException {
		FeatureConfigCache.getInstance().setFeatureConfigMap(null);
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setExpiry("2021-08-08");
		markAsPaidRequest.setCircle("delhi ncr");
		markAsPaidRequest.setPaytype("prepaid");
		markAsPaidRequest.setService("mobile");
		markAsPaidRequest.setPlanBucket("plan");
		markAsPaidRequest.setProductId(null);
		Callable<Object> reminderRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				ReminderMarkAsPaidResponse reminderMarkAsPaidResponse = new ReminderMarkAsPaidResponse();
				reminderMarkAsPaidResponse.setStatusCode("200");
				return reminderMarkAsPaidResponse;
			}
		};

		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};

		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(3));
		when(reminderService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderRes);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(recentRes);

		String response = "{\"plan_bucket\":\"Special Recharge\",\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":null}";
		DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);


		when(rpsService.getPlanDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);

		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService.serviceRequestTask(markAsPaidRequest));
		future.get();

		ArgumentCaptor<ChannelHistory> channelHistory = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(channelHistory.capture(),service.capture());

		assertEquals(Constants.CommonConstants.MARK_AS_PAID_MESSAGE_TYPE, channelHistory.getValue().getEventType());
		assertEquals(FulfillmentTxnStatus.SUCCESS, channelHistory.getValue().getStatus());
		assertEquals("2.5GB/day",channelHistory.getValue().getDisplayValues().get("data"));
		assertEquals("100/Day",channelHistory.getValue().getDisplayValues().get("sms"));
		assertEquals("Special Recharge",channelHistory.getValue().getBillsObj().get("plan_bucket"));
	}

	@Test
	public void testCreateMarkAsPaidInChannelHistoryWhenBillSyncIsCalled() throws ExecutionException, InterruptedException {
		FeatureConfigCache.getInstance().getList(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED).add("1234");
		MarkAsPaidRequest markAsPaidRequest = mockMarkAsPaidRequest();
		markAsPaidRequest.setExpiry("2021-08-08");
		markAsPaidRequest.setCircle("delhi ncr");
		markAsPaidRequest.setPaytype("prepaid");
		markAsPaidRequest.setService("mobile");
		markAsPaidRequest.setPlanBucket("plan");
		markAsPaidRequest.setProductId(Long.valueOf(1234));
		Callable<Object> billSyncResponse = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				BillsSyncMarkAsPaidResponse billsSyncMarkAsPaidResponse = new BillsSyncMarkAsPaidResponse();
				billsSyncMarkAsPaidResponse.setRowUpdated("1");
				billsSyncMarkAsPaidResponse.setMessage("Record Marked as Paid for future use");
				return billsSyncMarkAsPaidResponse;
			}
		};


		Callable<Object> recentRes = new Callable<Object>() {

			@Override
			public Object call() throws Exception {
				RecentCRUDResponse recentCRUDResponse = new RecentCRUDResponse();
				recentCRUDResponse.setStatus_code("200");
				return recentCRUDResponse;
			}
		};

		when(schedulerExecutorConfiguration.getScheduledExecutorService())
				.thenReturn(Executors.newScheduledThreadPool(3));
		when(billsSyncService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(billSyncResponse);
		when(recentCRUDService.markAsPaidTask(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(recentRes);

		String response = "{\"plan_bucket\":null,\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":null}";
		DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);


		when(rpsService.getPlanDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);

		Future<Void> future = schedulerExecutorConfiguration.getScheduledExecutorService().submit(markAsPaidService.serviceRequestTask(markAsPaidRequest));
		future.get();

		ArgumentCaptor<ChannelHistory> channelHistory = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(channelHistory.capture(),service.capture());

		assertEquals(Constants.CommonConstants.MARK_AS_PAID_MESSAGE_TYPE, channelHistory.getValue().getEventType());
		assertEquals(FulfillmentTxnStatus.SUCCESS, channelHistory.getValue().getStatus());
		assertEquals("2.5GB/day",channelHistory.getValue().getDisplayValues().get("data"));
		assertEquals("100/Day",channelHistory.getValue().getDisplayValues().get("sms"));
		assertEquals("plan",channelHistory.getValue().getBillsObj().get("plan_bucket"));
	}

	@Test
	public void filterRecentsByDueDate_ShouldRemovePastDueDates() {
		List<Recents> recents = new ArrayList<>();
		Recents recent1 = new Recents();
		recent1.setDueDate(new Date(System.currentTimeMillis() - 10000)); // past date
		Recents recent2 = new Recents();
		recent2.setDueDate(new Date(System.currentTimeMillis() + 10000)); // future date
		recents.add(recent1);
		recents.add(recent2);

		markAsPaidService.filterRecentsByDueDate(recents);

		assertEquals(1, recents.size());
		assertEquals(recent2, recents.get(0));
	}

	@Test
	public void filterRecentsByDueDate_ShouldAlsoHaveNullDueDates() {
		List<Recents> recents = new ArrayList<>();
		Recents recent1 = new Recents();
		recent1.setDueDate(null); // null date
		Recents recent2 = new Recents();
		recent2.setDueDate(new Date(System.currentTimeMillis() + 10000)); // future date
		recents.add(recent1);
		recents.add(recent2);

		markAsPaidService.filterRecentsByDueDate(recents);

		assertEquals(2, recents.size());
	}

	@Test
	public void filterRecentsByDueDate_ShouldHandleEmptyList() {
		List<Recents> recents = new ArrayList<>();

		markAsPaidService.filterRecentsByDueDate(recents);

		assertEquals(0, recents.size());
	}

	@Test
	public void filterRecentsByDueDate_ShouldHandleNullList() {
		markAsPaidService.filterRecentsByDueDate(null);
		// No exception should be thrown
	}

	@Test
	public void filterRecentsByDueDate_ShouldHandleListWithAllPastDueDates() {
		List<Recents> recents = new ArrayList<>();
		Recents recent1 = new Recents();
		recent1.setDueDate(new Date(System.currentTimeMillis() - 10000)); // past date
		Recents recent2 = new Recents();
		recent2.setDueDate(new Date(System.currentTimeMillis() - 20000)); // past date
		recents.add(recent1);
		recents.add(recent2);

		markAsPaidService.filterRecentsByDueDate(recents);

		assertEquals(0, recents.size());
	}

	@Test
	public void testIsMarkAsPaidRequestOnTime_BillDateBeforeTxnTime() {
		MarkAsPaidRequest markAsPaidRequest = new MarkAsPaidRequest();
		markAsPaidRequest.setPaymentDate("2023-10-10");
		Recents recentObj = new Recents();
		recentObj.setBillDate(new Date(System.currentTimeMillis() - 10000));
		recentObj.setTxnTime(new Date());
		boolean result = markAsPaidService.isMarkAsPaidRequestOnTime(markAsPaidRequest, recentObj);
		assertFalse(result);
	}

	@Test
	public void testIsMarkAsPaidRequestOnTime_BillDateBeforeMarkAsPaidTime() {
		MarkAsPaidRequest markAsPaidRequest = new MarkAsPaidRequest();
		markAsPaidRequest.setPaymentDate("2023-10-10");
		Recents recentObj = new Recents();
		recentObj.setBillDate(new Date(System.currentTimeMillis() - 10000));
		recentObj.setMarkAsPaidTime(new Date());
		recentObj.setTxnTime(null);
		boolean result = markAsPaidService.isMarkAsPaidRequestOnTime(markAsPaidRequest, recentObj);
		assertFalse(result);
	}

	@Test
	public void testIsMarkAsPaidRequestOnTime_UPMSAndPaymentDateAfterBillDate() {
		MarkAsPaidRequest markAsPaidRequest = new MarkAsPaidRequest();
		markAsPaidRequest.setPaymentDate("2023-10-10");
		markAsPaidRequest.setMarkAsPaidSource(Constants.UPMS);
		Recents recentObj = new Recents();
		recentObj.setBillDate(new Date(System.currentTimeMillis() - 10000));
		recentObj.setTxnTime(new Date(System.currentTimeMillis() - 1000000));
		recentObj.setMarkAsPaidTime(null);
		boolean result = markAsPaidService.isMarkAsPaidRequestOnTime(markAsPaidRequest, recentObj);
		assertFalse(result);
	}

	@Test
	public void testIsMarkAsPaidRequestOnTime_UPMSAndPaymentDateEqualBillDate() {
		MarkAsPaidRequest markAsPaidRequest = new MarkAsPaidRequest();
		markAsPaidRequest.setPaymentDate("2023-10-10");
		markAsPaidRequest.setMarkAsPaidSource(Constants.UPMS);
		Recents recentObj = new Recents();
		recentObj.setBillDate(DateUtil.stringToDate("2023-10-10 00:00:00", Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT));
		recentObj.setMarkAsPaidTime(DateUtil.stringToDate("2023-10-09 00:00:00", Constants.CommonConstants.RECENT_CRUD_DATE_FORMAT));
		boolean result = markAsPaidService.isMarkAsPaidRequestOnTime(markAsPaidRequest, recentObj);
		assertTrue(result);
	}
}
