package com.paytm.saga.service;

import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.SmartRecentDropOffRequest;
import com.paytm.saga.dto.SmartRecentDropoffResponse;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SmartRecentsServiceTest {

    @InjectMocks
    private SmartRecentsService smartRecentsService;
    @Mock
    private SmartRecentsRepository smartRecentsRepository;

    @Mock
    private KafkaProducerService kafkaProducerService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void saveSmartRecentDropOff() {
        SmartRecentDropOffRequest request = new SmartRecentDropOffRequest();
        request.setService("Test service");
        request.setOperator("Test Operator");
        request.setCustomerId(123L);
        request.setRechargeNumber("9971953");
        request.setProductId(1245L);
        request.setSource("formPage");

        doNothing().when(kafkaProducerService).sendMessage(anyString());
        ArgumentCaptor<SmartRecents> smartRecentsArgumentCaptor = ArgumentCaptor.forClass(SmartRecents.class);
        SmartRecentDropoffResponse smartRecentDropoffResponse = smartRecentsService.saveSmartRecentDropOff(request);
        verify(smartRecentsRepository, times(1)).save(smartRecentsArgumentCaptor.capture());

        assertEquals(new Integer(200),smartRecentDropoffResponse.getStatusCode());
        assertEquals("success",smartRecentDropoffResponse.getErrorMessage());
        assertEquals(request.getService().toLowerCase(),smartRecentsArgumentCaptor.getValue().getKey().getService());
        assertEquals(request.getOperator().toLowerCase(),smartRecentsArgumentCaptor.getValue().getKey().getOperator());
        assertEquals(request.getCustomerId(),smartRecentsArgumentCaptor.getValue().getKey().getCustomerId());
        assertEquals(request.getRechargeNumber(),smartRecentsArgumentCaptor.getValue().getRechargeNumber());
    }

    @Test
    public void saveSmartRecentDropOffException() {
        SmartRecentDropOffRequest request = new SmartRecentDropOffRequest();
        request.setService("Test service");
        request.setOperator("Test Operator");
        request.setCustomerId(123L);
        request.setRechargeNumber("9971953");
        request.setProductId(1245L);
        request.setSource("formPage");

        when(smartRecentsRepository.save(any())).thenThrow(new RuntimeException());
        SmartRecentDropoffResponse smartRecentDropoffResponse = smartRecentsService.saveSmartRecentDropOff(request);
        assertEquals(new Integer(500),smartRecentDropoffResponse.getStatusCode());
    }
    @Test
    public void testGetSmartRecentsForFrequentOrderCLP() {
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(556655L);
        frequentOrderRequest.setServices(new ArrayList<String>(){{add("mobile");}});

        when(smartRecentsRepository.findByCustomerIdAndService(any(), any())).thenReturn(new ArrayList<>());
        assertEquals(new ArrayList<>(), smartRecentsService.getSmartRecentsForFrequentOrderCLP(frequentOrderRequest));
    }

    @Test
    public void testGetSmartRecentsForFrequentOrderCLPException() {
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(556655L);
        frequentOrderRequest.setServices(new ArrayList<String>(){{add("mobile");}});

        when(smartRecentsRepository.findByCustomerIdAndService(any(), any())).thenThrow(new RuntimeException());
        assertNull(smartRecentsService.getSmartRecentsForFrequentOrderCLP(frequentOrderRequest));
    }

    @Test
    public void testSaveSmartRecents() {
        SmartRecents smartRecents = new SmartRecents();
        SmartRecentsPrimaryKey smartRecentsPrimaryKey = new SmartRecentsPrimaryKey();
        smartRecentsPrimaryKey.setCustomerId(123L);
        smartRecentsPrimaryKey.setService("mobile");
        smartRecentsPrimaryKey.setOperator("Airtel");
        smartRecents.setKey(smartRecentsPrimaryKey);
        when(smartRecentsRepository.save(any())).thenReturn(smartRecents);
        smartRecentsService.save(smartRecents);
    }

    @Test
    public void testSaveSmartRecentsException() {
        SmartRecents smartRecents = new SmartRecents();
        SmartRecentsPrimaryKey smartRecentsPrimaryKey = new SmartRecentsPrimaryKey();
        smartRecentsPrimaryKey.setCustomerId(123L);
        smartRecentsPrimaryKey.setService("mobile");
        smartRecentsPrimaryKey.setOperator("Airtel");
        smartRecents.setKey(smartRecentsPrimaryKey);
        when(smartRecentsRepository.save(any())).thenThrow(new RuntimeException());
        smartRecentsService.save(smartRecents);
    }

    @Test
    public void testDeleteSmartRecents() {
        SmartRecents smartRecents = new SmartRecents();
        SmartRecentsPrimaryKey smartRecentsPrimaryKey = new SmartRecentsPrimaryKey();
        smartRecentsPrimaryKey.setCustomerId(123L);
        smartRecentsPrimaryKey.setService("mobile");
        smartRecentsPrimaryKey.setOperator("Airtel");
        smartRecents.setKey(smartRecentsPrimaryKey);
        doThrow(new RuntimeException()).when(kafkaProducerService).sendMessage(anyString());
        doNothing().when(smartRecentsRepository).delete(any());
        smartRecentsService.delete(smartRecents);
    }

    @Test
    public void testDeleteSmartRecentsExceptionCase() {
        SmartRecents smartRecents = new SmartRecents();
        SmartRecentsPrimaryKey smartRecentsPrimaryKey = new SmartRecentsPrimaryKey();
        smartRecentsPrimaryKey.setCustomerId(123L);
        smartRecentsPrimaryKey.setService("mobile");
        smartRecentsPrimaryKey.setOperator("Airtel");
        smartRecents.setKey(smartRecentsPrimaryKey);
        doThrow(RuntimeException.class).when(smartRecentsRepository).delete(any());
        smartRecentsService.delete(smartRecents);
    }

}