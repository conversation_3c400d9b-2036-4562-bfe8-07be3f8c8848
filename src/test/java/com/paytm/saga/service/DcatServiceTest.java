package com.paytm.saga.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.saga.common.configuration.property.DCATConfig;
import com.paytm.saga.common.configuration.property.ExternalEndpointsPropertiesConfig;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RestTemplateServiceException;
import com.paytm.saga.dto.DCATCategoryResponseModel;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.model.CommonCache;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.util.JsonUtils;

public class DcatServiceTest {

	private static final Logger logger = LogManager.getLogger(SavedCardService.class);

	@Mock
	private DcatRestTemplateService restTemplateService;
	@Mock
	private DCATConfig dcatConfig;
	@Mock
	private ExternalEndpointsPropertiesConfig externalEndpointsPropertiesConfig;
	@Mock
	private CommonCacheService commonCacheService;
	@InjectMocks
	private DCATService dCATService;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	private DCATCategoryResponseModel getCategoryResponseMockObj() {
		String data = "{\"bottomStripUtilities\":[],\"showFastForward\":0,\"showUpgrade\":0,\"groupingCollapsible\":0,\"metaDescription\":\"Pay the bill of your Visa, Master, Amex, and Diners Credit Cards of all major banks using the Paytm credit cards bill payment facility & get cashback offers on Paytm\",\"quotes\":[],\"disclaimerFastForward\":null,\"protectionUrl\":{\"web\":null,\"html5\":null,\"defaultValue\":\"\",\"android\":null,\"ios\":null},\"showHelp\":0,\"skipDeals\":1,\"topLevelCategoryHeader\":{},\"productListInLastGrouping\":1,\"upperAmountThreshold\":null,\"categoryHeader\":\"Credit Card Bill Payment\",\"logoUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/M/MS/MSSHRTBLZ_GREY_1INGRMM/1602595341546_16.png\",\"staticForm\":0,\"applicableCities\":[],\"displayName\":\"Credit-Card\",\"lowerAmountThreshold\":null,\"disclaimerHtml\":\"\",\"inputFields\":[{\"configKey\":\"credit_card_number\",\"regex\":\"\",\"showPhoneBook\":0,\"title\":\"Enter Credit Card Number\",\"type\":\"input\"}],\"fastForwardText\":\"Fast Forward\",\"alert\":null,\"buttonObject\":{\"prefetch\":[{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"},{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"}]},\"recentsPrefill\":0,\"disclaimer\":\"Pay for all Visa, Mastercard, American Express, Diners & Rupay Credit Cards issued by all major banks.\",\"extraDescription\":\"\",\"canonicalUrl\":\"\",\"aggrAttrs\":[[{\"aggType\":\"terms\",\"value\":\"operator_label\"},{\"aggType\":\"terms\",\"value\":\"card_network\"}]],\"dealsFastForward\":0,\"extnAttrs\":{\"prefillFirstRecent\":0,\"hideEnabled\":0},\"message\":\"\",\"verticalId\":56,\"metaTitle\":\"Credit Card Payment - Pay Bills of Visa, Master, Amex & Diners Credit Cards\",\"attributes\":[{\"attrId\":1211,\"name\":\"card_network\",\"description\":null,\"type\":{\"app\":null,\"web\":null,\"html5\":null,\"defaultValue\":\"GRID\",\"android\":null,\"bcandroidapp\":null,\"ios\":null,\"windows\":null},\"displayName\":\"Card Network Type\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true},{\"attrId\":1179,\"name\":\"operator_label\",\"description\":null,\"type\":{\"app\":\"GRID\",\"web\":\"DROPDOWN\",\"html5\":\"GRID\",\"defaultValue\":\"DROPDOWN\",\"android\":\"GRID\",\"bcandroidapp\":null,\"ios\":\"GRID\",\"windows\":null},\"displayName\":\"Select your Bank\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true}],\"gaKey\":\"\",\"metaKeyword\":\"\",\"categoryId\":156705,\"groupings\":{\"aggKey\":\"operator_label\",\"aggs\":[{\"value\":\"AMEX\",\"displayValue\":\"AMEX\",\"bank_code\":\"AMEX\",\"schedulable\":\"0\",\"operator_min_android_version\":\"9.0.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"9.0.0\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"AMEX\",\"displayValue\":\"AMEX\",\"schedulable\":\"0\",\"operator_min_android_version\":\"9.0.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"9.0.0\",\"productList\":[{\"price\":1,\"productId\":*********,\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"AMEX\",\"enable_visa_direct\":\"0\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill Payment of AMEX Credit Card\",\"catalogProductId\":*********,\"operator_min_android_version\":\"9.0.0\",\"disclaimer\":\"Your payment will be posted to your card in the next 6 hours.\",\"bank_code\":\"AMEX\",\"credit_card_length\":\"15\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"neft_AMEX\",\"schedulable\":\"0\",\"operator_display_label\":\"AMEX\",\"paytype_display_label\":\"Bill Payment\",\"image\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"AMEX\",\"payTypeSupported\":{\"cc\":0,\"emi\":0,\"nb\":1,\"ppi\":0,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":0,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"operator_min_ios_version\":\"9.0.0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/2.png\",\"recharge_number_label\":\"Amount\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTOCL-1083910D1C0F477/0x1920/70/4.png\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/1.png\",\"service_display_label\":\"Credit Card\",\"proceed_directly_to_PG\":\"1\",\"productResources\":[{\"resourceId\":**********,\"isDefault\":0,\"type\":\"image\",\"url\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTOCL-1083910D1C0F477/3.png\"}],\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"chat_channel_id\":\"e00a3b98-a8b4-467c-b2eb-6432e6d0334a\"}]}]},{\"value\":\"AU Small Finance Bank\",\"displayValue\":\"AU Small Finance Bank\",\"bank_code\":\"AUBL\",\"schedulable\":\"0\",\"operator_min_android_version\":\"8.1.1\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"8.1.1\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Visa\",\"displayValue\":\"Visa\",\"schedulable\":\"0\",\"operator_min_android_version\":\"8.1.1\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"8.1.1\",\"productList\":[{\"price\":1,\"productId\":*********,\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"AU Small Finance Bank\",\"enable_visa_direct\":\"1\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill Payment of AU Small Finance Bank Credit Card\",\"catalogProductId\":*********,\"operator_min_android_version\":\"8.1.1\",\"disclaimer\":\"Your bank may take upto 3 working days in reflecting this payment to your credit card account.\",\"salesforceCaseId\":\"Default\",\"bank_code\":\"AUBL\",\"credit_card_length\":\"16\",\"verticalId\":56,\"operator\":\"visa_aufsb\",\"schedulable\":\"0\",\"operator_display_label\":\"AU Small Finance Bank\",\"paytype_display_label\":\"Bill Payment\",\"image\":\"https://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"Visa\",\"payTypeSupported\":{\"cc\":0,\"emi\":0,\"nb\":1,\"ppi\":0,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":0,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"operator_min_ios_version\":\"8.1.1\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\"}],\"recharge_number_label\":\"Amount\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTPAYT886062453598CA/1.png\",\"service_display_label\":\"Credit Card\",\"proceed_directly_to_PG\":\"1\",\"productResources\":[{\"resourceId\":**********,\"isDefault\":0,\"type\":\"image\",\"url\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTPAYT886062453598CA/0..jpg\"}],\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"chat_channel_id\":\"e00a3b98-a8b4-467c-b2eb-6432e6d0334a\"}]}]},{\"value\":\"Allahabad Bank\",\"displayValue\":\"Allahabad Bank\",\"bank_code\":\"ALH\",\"schedulable\":\"0\",\"operator_min_android_version\":\"7.3.1\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"8.0.0\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Visa\",\"displayValue\":\"Visa\",\"schedulable\":\"0\",\"operator_min_android_version\":\"7.3.1\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"8.0.0\",\"productList\":[{\"price\":1,\"productId\":*********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/22.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"Allahabad Bank\",\"enable_visa_direct\":\"1\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill Payment of Allahabad Bank Credit Card\",\"catalogProductId\":*********,\"operator_min_android_version\":\"7.3.1\",\"disclaimer\":\"Your bank may take upto 3 working days in reflecting this payment to your credit card account.\",\"bank_code\":\"ALH\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"visa_Allahabad\",\"schedulable\":\"0\",\"operator_display_label\":\"Allahabad Bank\",\"paytype_display_label\":\"Bill Payment\",\"image\":\"https://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"Visa\",\"payTypeSupported\":{\"cc\":0,\"emi\":0,\"nb\":1,\"ppi\":0,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":0,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"operator_min_ios_version\":\"8.0.0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"recharge_number_label\":\"Amount\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA88606286C081ED/0x1920/70/3.png\",\"service_display_label\":\"Credit Card\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"chat_channel_id\":\"e00a3b98-a8b4-467c-b2eb-6432e6d0334a\"}]}]}]},\"relatedCategories\":[]}";
		DCATCategoryResponseModel dcatCategoryResponseModel = JsonUtils.parseJson(data,
				DCATCategoryResponseModel.class);
		return dcatCategoryResponseModel;
	}

	@Test
	public void hitCategoryGroupingApiTest() {
		//
		DCATCategoryResponseModel dcatCategoryResponseModel = getCategoryResponseMockObj();
		Map<String, String> queryMap = new HashMap<String, String>();
		queryMap.put("locale", Constants.CommonConstants.DCAT_LOCALE);
		queryMap.put("version", Constants.CommonConstants.DCAT_VERSION);
		queryMap.put("channel", Constants.CommonConstants.DCAT_CHANNEL);
		try {
			when(restTemplateService.executeGetRequest(
					"https://digitalcatalog.paytm.com/dcat/v1/category/131655/getcategory", queryMap,
					DCATCategoryResponseModel.class)).thenReturn(dcatCategoryResponseModel);
			when(dcatConfig.getCategoryUrl()).thenReturn("https://digitalcatalog.paytm.com/dcat/v1/category/");
		} catch (RestTemplateServiceException e) {
			logger.error("RestTemplateServiceException: " + e.getMessage());
		}
		DCATCategoryResponseModel dcatCategoryResponseModel2 = dCATService.hitGetCategoryApi("financial services");

		assertEquals(
				dcatCategoryResponseModel.getGroupings().getAggs().get(0).getAggs().get(0).getProductList().get(0)
						.getBankCode(),
				dcatCategoryResponseModel2.getGroupings().getAggs().get(0).getAggs().get(0).getProductList().get(0)
						.getBankCode());
	}

	@Test
	public void getPlanDetailsTest() {
		String categoryId = "7";
		String version = "2.3";
		String channel = "android";
		String amount = "100";
		String operator = "airtel";
		String circle = "punjab";
		String service = "mobile";
		
		String categoryResponse="{\"bottomStripUtilities\":[],\"showFastForward\":1,\"showUpgrade\":0,\"groupingCollapsible\":0,\"metaDescription\":\"Online recharge - Fast & easy mobile recharge online at Paytm. Prepaid recharge for all operators- Airtel, Jio, Vodafone Idea, BSNL & MTNL ✔Phone Recharge ✔Win Cashback\",\"quotes\":[],\"aggrAttrs\":[[{\"aggType\":\"terms\",\"value\":\"operator\"},{\"aggType\":\"terms\",\"value\":\"circle\"},{\"aggType\":\"terms\",\"value\":\"producttype_label\"}]],\"dealsFastForward\":0,\"extnAttrs\":{\"prefillFirstRecent\":0,\"catProperties\":[{\"image\":\"https://assetscdn1.paytm.com/images/catalog/product/D/DE/DEAGET-CONSULTAPAYT4676FDD0F/30x30/48.png\",\"deeplink\":\"paytmmp://cst_flow?featuretype=vertical_detail&verticalId=1&showHomeOnBack=false\",\"newUser\":\"9.4.0\",\"exisisting_user\":\"9.4.0\",\"title\":\"Help & Support\",\"gaKey\":\"customer_support\"}],\"browsePlansCategory\":\"mobile/7166\",\"bbps_logo_landing\":0,\"hideEnabled\":0,\"errorImage\":false},\"message\":\"\",\"verticalId\":4,\"metaTitle\":\"Online Recharge - Online Mobile Recharge & Prepaid Recharge Plans\",\"attributes\":[{\"attrId\":470,\"name\":\"circle\",\"description\":null,\"type\":{\"app\":\"DROPDOWN\",\"web\":\"DROPDOWN\",\"html5\":null,\"defaultValue\":\"DROPDOWN\",\"android\":\"DROPDOWN\",\"bcandroidapp\":null,\"ios\":\"DROPDOWN\",\"windows\":null},\"displayName\":\"Circle\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[]},{\"attrId\":469,\"name\":\"operator\",\"description\":null,\"type\":{\"app\":\"GRID\",\"web\":\"DROPDOWN\",\"html5\":\"GRID\",\"defaultValue\":\"DROPDOWN\",\"android\":\"GRID\",\"bcandroidapp\":null,\"ios\":\"GRID\",\"windows\":null},\"displayName\":\"Operator\",\"error\":null,\"placeholder\":\"Operator-Circle\",\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"accordion_label\":\"Operator\",\"updateOperatorText\":true,\"child\":\"circle\"},{\"attrId\":476,\"name\":\"producttype_label\",\"description\":null,\"type\":{\"app\":\"RADIO\",\"web\":\"RADIO\",\"html5\":\"RADIO\",\"defaultValue\":\"RADIO\",\"android\":\"RADIO\",\"bcandroidapp\":null,\"ios\":\"RADIO\",\"windows\":null},\"displayName\":\"Recharge Type\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"default\":\"Talktime Topup\",\"showSingle\":false,\"show_after_inputfield\":true,\"showProd\":true}],\"gaKey\":\"mobile_prepaid\",\"metaKeyword\":\"null\",\"categoryId\":17,\"groupings\":{\"aggKey\":\"operator\",\"aggs\":[{\"value\":\"Airtel\",\"displayValue\":\"Airtel\",\"regEx\":\"^([6-9][0-9]{9})\",\"display_header\":\"Airtel Prepaid Mobile Recharge\",\"schedulable\":\"0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/1626280427152.png\",\"group_config_key\":\"price\",\"aggKey\":\"circle\",\"aggs\":[{\"value\":\"Punjab\",\"displayValue\":\"Punjab\",\"operator_alert\":\"1\",\"display_header\":\"Airtel Prepaid Mobile Recharge\",\"schedulable\":\"0\",\"operator\":\"Airtel\",\"aggKey\":\"producttype_label\",\"aggs\":[{\"value\":\"Topup\",\"displayValue\":\"Topup\",\"schedulable\":\"0\",\"productList\":[{\"show_browse_plan\":\"1\",\"reminder_flag\":\"1\",\"regEx\":\"^([6-9][0-9]{9})\",\"input_field-isAlphanumeric-2\":\"true\",\"input_field-isAlphanumeric-1\":\"true\",\"service_label\":\"Mobile\",\"circle\":\"Punjab\",\"operator_label\":\"Airtel\",\"One2One_offer_disclaimer\":\"While we support most recharges, we request you to verify with your operator once before proceeding. ' Best Offers For You ' are offers available for a class of Airtel subscribers.\",\"displayName\":\"Recharge of Airtel Mobile\",\"catalogProductId\":217,\"input_field-show_phonebook-1\":\"0\",\"input_field-type-2\":\"text\",\"input_field-type-1\":\"text\",\"ussd_code\":\"*121*51#\",\"extnAttrs\":{\"conv_fee\":0},\"dynamic_plan_category_label\":\"Best Offers For You\",\"max_amount\":\"20000\",\"circle_label\":\"Punjab\",\"operator\":\"Airtel\",\"display_header\":\"Airtel Prepaid Mobile Recharge\",\"post_order_view_type\":\"storefront_view\",\"paytype_label\":\"Recharge\",\"prefetch\":\"0\",\"One2One_offer_text\":\"Now avail Best Offers For You\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/A/Ai/AirtelPunjabprepaid217/0x1920/70/6.png\",\"service\":\"Mobile\",\"email_logo\":\"https://paytmofferlive.wpengine.com/wp-content/uploads/2017/05/AIRTEL_LOGO.jpg\",\"paytype\":\"prepaid\",\"chat_channel_id\":\"b562cdef-b677-4784-ae8b-703a95023bc0\",\"operator_alert\":\"1\",\"price\":1,\"amount_read_only\":\"0\",\"amount_title\":\"Amount\",\"productId\":217,\"group_config_key\":\"price\",\"status\":\"1\",\"verticalId\":4,\"One2One_offer\":\"true\",\"recharge_type\":\"N/A\",\"dynamic_plans_grouping\":[{\"name\":\"Popular\",\"id\":81876},{\"name\":\"Validity\",\"id\":7177},{\"name\":\"Cricket Plans\",\"id\":266801},{\"name\":\"Data Add On\",\"id\":7180},{\"name\":\"Top Up\",\"id\":7182},{\"name\":\"Roaming\",\"id\":7181}],\"dynamic_plan\":\"1\",\"producttype\":\"Topup\",\"schedulable\":\"0\",\"operator_display_label\":\"Airtel\",\"paytype_display_label\":\"Recharge\",\"min_amount\":\"10\",\"producttype_label\":\"Topup\",\"isFastForward\":-1,\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"inputFields\":[{\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"5000\",\"showField\":0,\"isAlphanumeric\":1,\"isShown\":0,\"optional\":0,\"readOnly\":0,\"type\":\"text\",\"dynamicLength\":0,\"mandatory\":1},{\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"20000\",\"showField\":0,\"isAlphanumeric\":1,\"isShown\":0,\"optional\":0,\"readOnly\":0,\"type\":\"text\",\"dynamicLength\":0,\"mandatory\":1},{\"configKey\":\"price\",\"regex\":\"^[1-9][0-9]{0,5}$\",\"showPhoneBook\":0,\"min\":\"10\",\"max\":\"20000\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"block_invoice\":\"1\",\"amount_regex\":\"^[1-9][0-9]{0,5}$\",\"offer_widget_url\":\"https://storefront.paytm.com/v2/h/prior-recharge-screen\",\"service_display_label\":\"Mobile\"}]}]}]}]}}";
		DCATCategoryResponseModel dcatCategoryResponseModel=JsonUtils.parseJson(categoryResponse, DCATCategoryResponseModel.class);
		when(externalEndpointsPropertiesConfig.getBrowsePlanUrl()).thenReturn("https://digitalcatalog.paytm.com/dcat/v1/browseplans/");
		
		try {
			Map<String,String> queryMap = new HashMap<>();
			queryMap.put("locale",Constants.CommonConstants.DCAT_LOCALE);
			queryMap.put("version", version);
			queryMap.put("channel", channel);
			queryMap.put("operator", "Airtel");
			queryMap.put("amount", amount);

			if (service != null && !service.equals("dth")) {
				queryMap.put("circle", "Punjab");
			}
			String planRes="{\"data\":\"1 GB\",\"metaDescription\":\"Recharge of Rs 129 By Airtel online and get exciting cashback offers at Paytm.com\",\"operator\":\"Airtel\",\"producttype\":\"Recharge\",\"merchantId\":22,\"price\":129.0,\"productId\":62263779,\"ranking_score_81876\":8,\"name\":\"recharge_of_rs_129_by_airtel_airtelorissa129-s\",\"circle\":\"Orissa\",\"marchentId\":22,\"plan_bucket\":\"Special Recharge\",\"displayName\":\"Recharge of Rs 129 By Airtel\",\"catalogProductId\":62263779,\"description\":\"Enjoy Truly unlimited Local STD calls on any network 1GB data & 300 SMS.\",\"talktime\":\"NA\",\"sms\":\"300 SMS\",\"addon_benefit\":[\"addon1\",\"addon2\",\"addon3\",\"addon4\"],\"disclaimer\":\"The plans are subjected to change. Please cross verify\",\"validate\":0,\"extnAttrs\":{\"conv_fee\":0},\"ranking_score_7177\":27,\"service\":\"Mobile\",\"metaTitle\":\"Recharge of Rs 129 By Airtel | Paytm.com\",\"validity\":\"24 Days\",\"paytype\":\"Prepaid\",\"categoryId\":7166,\"amount_plan_desc\":\"0\"}";
			DCATGetPlanResponse dcatGetPlanResponse=JsonUtils.parseJson(planRes, DCATGetPlanResponse.class);
			when(restTemplateService.executeGetRequest("https://digitalcatalog.paytm.com/dcat/v1/browseplans/7/getplan",queryMap,DCATGetPlanResponse.class)).thenReturn(dcatGetPlanResponse);
			when(restTemplateService.executePostRequest(Mockito.any(), Mockito.any(), Mockito.any(),
					Mockito.any(), Mockito.any())).thenReturn(dcatCategoryResponseModel);
		} catch (RestTemplateServiceException e) {
			logger.error("RestTemplateServiceException: " + e.getMessage());
		}
		
		DCATGetPlanResponse dcatGetPlanResponse=dCATService.getPlanDetails(categoryId, version, channel, operator, amount, circle, service);
		
		assertNotNull(dcatGetPlanResponse);
		assertEquals("1 GB",dcatGetPlanResponse.getData());
		assertEquals("Special Recharge",dcatGetPlanResponse.getPlan_bucket());
		assertEquals("24 Days",dcatGetPlanResponse.getValidity());
	}
	
	@Test
	public void getPlanDetailsFromCacheTest() {
		String categoryId = "7";
		String version = "2.3";
		String channel = "android";
		String amount = "100";
		String operator = "airtel";
		String circle = "punjab";
		String service = "mobile";
		
		List<CommonCache> listCommonCache = new ArrayList<>();
		CommonCache cache=new CommonCache();
		cache.setCacheKey("DCAT_PLAN_7_2.3_android_airtel_100_punjab");
		cache.setCacheValue("{\"plan_bucket\":\"Special Recharge\",\"data\":\"1 GB\",\"price\":\"129.0\",\"validity\":\"24 Days\",\"talktime\":\"NA\",\"sms\":\"300 SMS\",\"description\":\"Enjoy Truly unlimited Local STD calls on any network 1GB data & 300 SMS.\",\"productId\":\"62263779\"}");
		listCommonCache.add(cache);
		
		when(commonCacheService.getCache("DCAT_PLAN_7_2.3_android_airtel_100_punjab")).thenReturn(listCommonCache);
		
		DCATGetPlanResponse dcatGetPlanResponse=dCATService.getPlanDetails(categoryId, version, channel, operator, amount, circle, service);
		assertNotNull(dcatGetPlanResponse);
		assertEquals("1 GB",dcatGetPlanResponse.getData());
		assertEquals("Special Recharge",dcatGetPlanResponse.getPlan_bucket());
		assertEquals("24 Days",dcatGetPlanResponse.getValidity());
	}
	@Test
	public void getPlanDetailsCachedCategoryResTest() {
		String categoryId = "7";
		String version = "2.3";
		String channel = "android";
		String amount = "100";
		String operator = "airtel";
		String circle = "punjab";
		String service = "mobile";
		
		String categoryResponse="{\"groupings\":{\"aggKey\":\"operator\",\"aggs\":[{\"value\":\"Airtel\",\"displayValue\":\"Airtel\",\"aggKey\":\"circle\",\"aggs\":[{\"value\":\"Punjab\",\"displayValue\":\"Punjab\"}]}]}}";
		//DCATCategoryResponseModel dcatCategoryResponseModel=JsonUtils.parseJson(categoryResponse, DCATCategoryResponseModel.class);
		List<CommonCache> listCommonCache = new ArrayList<>();
		CommonCache cache=new CommonCache();
		cache.setCacheKey("DCAT_CATEGORY_mobile");
		cache.setCacheValue(categoryResponse);
		listCommonCache.add(cache);
		when(commonCacheService.getCache("DCAT_CATEGORY_mobile")).thenReturn(listCommonCache);
		when(externalEndpointsPropertiesConfig.getBrowsePlanUrl()).thenReturn("https://digitalcatalog.paytm.com/dcat/v1/browseplans/");
		
		try {
			Map<String,String> queryMap = new HashMap<>();
			queryMap.put("locale",Constants.CommonConstants.DCAT_LOCALE);
			queryMap.put("version", version);
			queryMap.put("channel", channel);
			queryMap.put("operator", "Airtel");
			queryMap.put("amount", amount);

			if (service != null && !service.equals("dth")) {
				queryMap.put("circle", "Punjab");
			}
			String planRes="{\"data\":\"1 GB\",\"metaDescription\":\"Recharge of Rs 129 By Airtel online and get exciting cashback offers at Paytm.com\",\"operator\":\"Airtel\",\"producttype\":\"Recharge\",\"merchantId\":22,\"price\":129.0,\"productId\":62263779,\"ranking_score_81876\":8,\"name\":\"recharge_of_rs_129_by_airtel_airtelorissa129-s\",\"circle\":\"Orissa\",\"marchentId\":22,\"plan_bucket\":\"Special Recharge\",\"displayName\":\"Recharge of Rs 129 By Airtel\",\"catalogProductId\":62263779,\"description\":\"Enjoy Truly unlimited Local STD calls on any network 1GB data & 300 SMS.\",\"talktime\":\"NA\",\"sms\":\"300 SMS\",\"disclaimer\":\"The plans are subjected to change. Please cross verify\",\"validate\":0,\"extnAttrs\":{\"conv_fee\":0},\"ranking_score_7177\":27,\"service\":\"Mobile\",\"metaTitle\":\"Recharge of Rs 129 By Airtel | Paytm.com\",\"validity\":\"24 Days\",\"paytype\":\"Prepaid\",\"categoryId\":7166,\"amount_plan_desc\":\"0\"}";
			DCATGetPlanResponse dcatGetPlanResponse=JsonUtils.parseJson(planRes, DCATGetPlanResponse.class);
			when(restTemplateService.executeGetRequest("https://digitalcatalog.paytm.com/dcat/v1/browseplans/7/getplan",queryMap,DCATGetPlanResponse.class)).thenReturn(dcatGetPlanResponse);
		} catch (RestTemplateServiceException e) {
			logger.error("RestTemplateServiceException: " + e.getMessage());
		}
		
		DCATGetPlanResponse dcatGetPlanResponse=dCATService.getPlanDetails(categoryId, version, channel, operator, amount, circle, service);
		
		assertNotNull(dcatGetPlanResponse);
		assertEquals("1 GB",dcatGetPlanResponse.getData());
		assertEquals("Special Recharge",dcatGetPlanResponse.getPlan_bucket());
		assertEquals("24 Days",dcatGetPlanResponse.getValidity());
	}
	

}
