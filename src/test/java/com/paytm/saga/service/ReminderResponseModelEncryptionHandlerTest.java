package com.paytm.saga.service;

import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.ReminderDataResponseModel;
import com.paytm.saga.dto.ReminderResponseModel;
import com.paytm.saga.util.AESUtil;
import com.paytm.saga.util.JsonUtils;
import org.apache.logging.log4j.Logger;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static com.paytm.saga.common.constant.EncryptionConstants.CUSTOMER_OTHER_INFO_ENCRYPTED_KEYS;
import static com.paytm.saga.common.constant.EncryptionConstants.REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ReminderResponseModelEncryptionHandlerTest {

	@Mock
	private AESUtil aesUtil;

	@Mock
	private MetricsHelper metricsHelper;

	@Mock
	private Logger logger;

	@InjectMocks
	private ReminderResponseModelEncryptionHandler reminderResponseModelEncryptionHandler;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		when(aesUtil.decrypt("encrypted Amount")).thenReturn("100.0");
		when(aesUtil.decrypt("encrypted DueDate")).thenReturn("2024-11-21 00:00:00");
		when(aesUtil.decrypt("encrypted RechargeNumber")).thenReturn("**********");
		when(aesUtil.decrypt("encrypted Reference_id")).thenReturn("ref123");
		when(aesUtil.decrypt("enc lastCC")).thenReturn("1234");
		when(aesUtil.decrypt("enc rawLastCC")).thenReturn("1234");
		when(aesUtil.decrypt("enc currentBillAmount")).thenReturn("100.0");
		when(aesUtil.decrypt("enc currentMinBillAmount")).thenReturn("90.0");
		when(aesUtil.decrypt("Enc bill Due Date")).thenReturn("2024-11-21 00:00:00");
		when(aesUtil.decrypt("enc debugKey")).thenReturn("debugKey");
		when(aesUtil.decrypt("enc amount")).thenReturn("100.0");
		when(aesUtil.decrypt("enc Card Variant")).thenReturn("Card Variant");


		Map<String, Object> configMap = new HashMap<>();
		configMap.put(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS, Collections.singletonList("issuingBankCardVariant"));
		configMap.put(CUSTOMER_OTHER_INFO_ENCRYPTED_KEYS, new HashSet<>(Arrays.asList("lastCC", "rawLastCC", "currentBillAmount", "currentMinBillAmount", "billDate", "billDueDate", "debugKey", "amount")));
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);
	}

	@Test
	public void decryptReminderResponseModel_test() {
		ReminderResponseModel reminderResponseModel = getEncryptedReminderResponseModel();
		ReminderResponseModel decryptedModel = reminderResponseModelEncryptionHandler.decryptReminderResponseModel(reminderResponseModel);
		assertNotNull(decryptedModel);
		assertNotNull(decryptedModel.getData());
		assertNotNull(decryptedModel.getOld());
		assertEquals(Double.valueOf(100.0), decryptedModel.getData().getAmount());
		assertEquals("2024-11-21 00:00:00", decryptedModel.getData().getDue_date());
		assertEquals("**********", decryptedModel.getData().getRecharge_number());
		assertEquals("ref123", decryptedModel.getData().getReference_id());
		Map<String, Object> customerOtherInfo = JsonUtils.parseJson(decryptedModel.getData().getCustomerOtherInfo(), Map.class);
		assertNotNull(customerOtherInfo);
		assertEquals("1234", customerOtherInfo.get("lastCC"));
		assertEquals("1234", customerOtherInfo.get("rawLastCC"));
		assertEquals("100.0", customerOtherInfo.get("currentBillAmount"));
		assertEquals("90.0", customerOtherInfo.get("currentMinBillAmount"));
		assertEquals("2024-11-21 00:00:00", customerOtherInfo.get("billDueDate"));
		assertEquals("debugKey", customerOtherInfo.get("debugKey"));
		assertEquals("100.0", customerOtherInfo.get("amount"));
		Map<String, Object> extra = JsonUtils.parseJson(decryptedModel.getData().getExtra(), Map.class);
		assertNotNull(extra);
		assertEquals("Card Variant", extra.get("issuingBankCardVariant"));
	}



	private ReminderResponseModel getEncryptedReminderResponseModel() {
		ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
		ReminderDataResponseModel data = new ReminderDataResponseModel();
		data.setEncAmount("encrypted Amount");
		data.setEncDueDate("encrypted DueDate");
		data.setRecharge_number("encrypted RechargeNumber");
		data.setReference_id("encrypted Reference_id");
		data.setCustomerOtherInfo("{\"customerId\":*********,\"lastCC\":\"enc lastCC\",\"rawLastCC\":\"enc rawLastCC\",\"currentBillAmount\":\"enc currentBillAmount\",\"currentMinBillAmount\":\"enc currentMinBillAmount\",\"billDate\":\"2024-11-21 00:00:00\",\"billDueDate\":\"Enc bill Due Date\",\"sender_id\":\"AD-HDFCBK\",\"sms_id\":\"\",\"dwh_classId\":\"\",\"bankName\":\"HDFC\",\"billConsumeTimestamp\":\"2024-11-22 11:13:57\",\"msgId\":\"\",\"appCount\":null,\"appVersion\":\"\",\"rtspId\":-1,\"category\":null,\"refId\":\"de089180-a8c2-11ef-8af2-b13ec054fc81\",\"sms_date_time\":*************,\"isPartial\":false,\"dwhKafkaPublishedTime\":*************,\"debugKey\":\"enc debugKey\",\"action\":\"update\",\"amount\":\"enc amount\"}");
		data.setExtra("{\"updated_source\":\"validationSync\",\"updated_data_source\":\"validationSync\",\"created_source\":\"validationSync\",\"source_subtype_2\":\"PARTIAL_BILL\",\"is_bbps\":true,\"recon_id\":\"uA4FnvuBIObe06HbxIypENet6KY=\",\"issuingBankCardVariant\":\"enc Card Variant\",\"user_type\":\"RU\"}");
		data.setIsEncrypted(1);
		reminderResponseModel.setData(data);
		ReminderDataResponseModel old = new ReminderDataResponseModel();
		old.setEncAmount("encrypted Amount");
		old.setEncDueDate("encrypted DueDate");
		old.setRecharge_number("encrypted RechargeNumber");
		old.setReference_id("encrypted Reference_id");
		old.setCustomerOtherInfo("{\"customerId\":*********,\"lastCC\":\"enc lastCC\",\"rawLastCC\":\"enc rawLastCC\",\"currentBillAmount\":\"enc currentBillAmount\",\"currentMinBillAmount\":\"enc currentMinBillAmount\",\"billDate\":\"2024-11-21 00:00:00\",\"billDueDate\":\"Enc bill Due Date\",\"sender_id\":\"AD-HDFCBK\",\"sms_id\":\"\",\"dwh_classId\":\"\",\"bankName\":\"HDFC\",\"billConsumeTimestamp\":\"2024-11-22 11:13:57\",\"msgId\":\"\",\"appCount\":null,\"appVersion\":\"\",\"rtspId\":-1,\"category\":null,\"refId\":\"de089180-a8c2-11ef-8af2-b13ec054fc81\",\"sms_date_time\":*************,\"isPartial\":false,\"dwhKafkaPublishedTime\":*************,\"debugKey\":\"enc debugKey\",\"action\":\"update\",\"amount\":\"enc amount\"}");
		old.setExtra("{\"updated_source\":\"validationSync\",\"updated_data_source\":\"validationSync\",\"created_source\":\"validationSync\",\"source_subtype_2\":\"PARTIAL_BILL\",\"is_bbps\":true,\"recon_id\":\"uA4FnvuBIObe06HbxIypENet6KY=\",\"issuingBankCardVariant\":\"enc Card Variant\",\"user_type\":\"RU\"}");
		old.setIsEncrypted(1);
		reminderResponseModel.setOld(old);
		return reminderResponseModel;
	}
}
