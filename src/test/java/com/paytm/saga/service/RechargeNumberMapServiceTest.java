package com.paytm.saga.service;

import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.repository.RechargeNumberMapRepository;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.JsonUtils;
import junit.framework.TestCase;
import org.apache.logging.log4j.LogManager;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.cassandra.CassandraInvalidQueryException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

public class RechargeNumberMapServiceTest {
    @Mock
    private RechargeNumberMapRepository rechargeNumberMapRepository;

    @Mock
    private ServiceConfig serviceConfig;

    @InjectMocks
    RechargeNumberMapService rechargeNumberMapService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        rechargeNumberMapService = new RechargeNumberMapService(rechargeNumberMapRepository, serviceConfig);
        String config = "{\"disableDropOff\": false, \"categoryMapping\": {\"mobile\": [17, 21], \"financial services\": [131655]}, \"categoryWiseTtl\": {\"insurance\": 63072000}, \"fetchRecentLimit\": 50, \"disableChatHistory\": false, \"smsEnabledServices\": [\"financial services\", \"electricity\", \"mobile\"], \"sagaEnabledServices\": [\"financial services\", \"electricity\", \"mobile\"], \"smartReminderCacheTTL\": 30, \"agentIdentificationLimit\": 300, \"smartReminderPrepaidEndDays\": 3, \"smartReminderPostpaidEndDays\": 3, \"smartReminderPrepaidStartDays\": 5, \"smartReminderCustomerBillLimit\": 30, \"smartReminderPostpaidStartDays\": 31, \"recentDataUpdateAllowedServices\": [\"dth\", \"financial services\", \"tuition fees\", \"mobile\"]}";
        try{
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e){
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(RechargeNumberMapService.class));

    @Test
    public void testFetchCustomerIdsFromDbWithNullResponseAndLimitWithServiceConfig(){
        String rechargeNumber="123456789";
        String service = "mobile";
        String operator = "airtel";
        Integer limit = FeatureConfigCache.getInstance().getInteger(Constants.SERVICE_CONFIG_CONSTANTS.FETCH_RECENT_LIMIT);
        when(serviceConfig.getFetchRecentLimit()).thenReturn(50);
        when(rechargeNumberMapRepository.getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumber,service,operator,limit)).thenReturn(null);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> limitArgument = ArgumentCaptor.forClass(Integer.class);
        List<Long> customerIdsResponse = rechargeNumberMapService.fetchCustomerIdsFromDb(rechargeNumber,service,operator);
       verify(rechargeNumberMapRepository,times(1)).getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumberArgument.capture(),serviceArgument.capture(),operatorArgument.capture(),limitArgument.capture());
        TestCase.assertEquals(rechargeNumber,rechargeNumberArgument.getValue());
        TestCase.assertEquals(service,serviceArgument.getValue());
        TestCase.assertEquals(operator,operatorArgument.getValue());
        TestCase.assertEquals(limit,limitArgument.getValue());
        TestCase.assertNull(customerIdsResponse);
    }

    @Test
    public void testFetchCustomerIdsFromDbWithNullResponseAndDefaultLimit(){
        String rechargeNumber="123456789";
        String service = "mobile";
        String operator = "airtel";
        FeatureConfigCache.getInstance().setFeatureConfigMap(null);
        Integer limit = 100;
        when(serviceConfig.getFetchRecentLimit()).thenReturn(100);
        when(rechargeNumberMapRepository.getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumber,service,operator,limit)).thenReturn(null);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> limitArgument = ArgumentCaptor.forClass(Integer.class);
        List<Long> customerIdsResponse = rechargeNumberMapService.fetchCustomerIdsFromDb(rechargeNumber,service,operator);
        verify(rechargeNumberMapRepository,times(1)).getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumberArgument.capture(),serviceArgument.capture(),operatorArgument.capture(),limitArgument.capture());
        TestCase.assertEquals(rechargeNumber,rechargeNumberArgument.getValue());
        TestCase.assertEquals(service,serviceArgument.getValue());
        TestCase.assertEquals(operator,operatorArgument.getValue());
        TestCase.assertEquals(limit,limitArgument.getValue());
        TestCase.assertNull(customerIdsResponse);
    }

    @Test
    public void testFetchCustomerIdsFromDbWithNonNullResponseAndLimitWithServiceConfig(){
        String rechargeNumber="123456789";
        String service = "mobile";
        String operator = "airtel";
        Integer limit = FeatureConfigCache.getInstance().getInteger(Constants.SERVICE_CONFIG_CONSTANTS.FETCH_RECENT_LIMIT);
        List<Long> customerids= new ArrayList<>();
        customerids.add(1122L);
        customerids.add(233L);
        when(serviceConfig.getFetchRecentLimit()).thenReturn(50);
        when(rechargeNumberMapRepository.getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumber,service,operator,limit)).thenReturn(customerids);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> limitArgument = ArgumentCaptor.forClass(Integer.class);
        List<Long> customerIdsResponse = rechargeNumberMapService.fetchCustomerIdsFromDb(rechargeNumber,service,operator);
        verify(rechargeNumberMapRepository,times(1)).getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumberArgument.capture(),serviceArgument.capture(),operatorArgument.capture(),limitArgument.capture());
        TestCase.assertEquals(rechargeNumber,rechargeNumberArgument.getValue());
        TestCase.assertEquals(service,serviceArgument.getValue());
        TestCase.assertEquals(operator,operatorArgument.getValue());
        TestCase.assertEquals(limit,limitArgument.getValue());
        TestCase.assertEquals(customerids,customerIdsResponse);
    }

    @Test
    public void testFetchCustomerIdsFromDbWithExceptionFromDb(){
        String rechargeNumber="123456789";
        String service = "mobile";
        String operator = "airtel";
        Integer limit = FeatureConfigCache.getInstance().getInteger(Constants.SERVICE_CONFIG_CONSTANTS.FETCH_RECENT_LIMIT);
        when(serviceConfig.getFetchRecentLimit()).thenReturn(50);
        when(rechargeNumberMapRepository.getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumber,service,operator,limit)).thenThrow(new CassandraInvalidQueryException("Invalid"));
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> limitArgument = ArgumentCaptor.forClass(Integer.class);
        List<Long> customerIdsResponse = rechargeNumberMapService.fetchCustomerIdsFromDb(rechargeNumber,service,operator);
        verify(rechargeNumberMapRepository,times(1)).getCustomerIdsByRechargeNumberAndServiceAndOperator(rechargeNumberArgument.capture(),serviceArgument.capture(),operatorArgument.capture(),limitArgument.capture());
        TestCase.assertEquals(rechargeNumber,rechargeNumberArgument.getValue());
        TestCase.assertEquals(service,serviceArgument.getValue());
        TestCase.assertEquals(operator,operatorArgument.getValue());
        TestCase.assertEquals(limit,limitArgument.getValue());
        TestCase.assertNull(customerIdsResponse);
    }
}
