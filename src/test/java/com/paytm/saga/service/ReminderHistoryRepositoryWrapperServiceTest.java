package com.paytm.saga.service;

import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.repository.ReminderHistoryRepository;
import com.paytm.saga.util.AESUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.ParseException;
import java.util.*;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static com.paytm.saga.common.constant.EncryptionConstants.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ReminderHistoryRepositoryWrapperServiceTest {

	@Mock
	private ReminderHistoryRepository reminderHistoryRepository;

	@Mock
	private ReminderHistoryEncryptionHandler reminderHistoryEncryptionHandler;

	@Mock
	private MetricsHelper metricsHelper;

	@Mock
	private AESUtil aesUtil;

	@InjectMocks
	private ReminderHistoryRepositoryWrapperService service;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, true);
		configMap.put(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS, Collections.singletonList("issuingBankCardVariant"));
		configMap.put(CUSTOMER_OTHER_INFO_ENCRYPTED_KEYS, new HashSet<>(Arrays.asList("lastCC", "rawLastCC", "currentBillAmount", "currentMinBillAmount", "billDate", "billDueDate", "debugKey", "amount")));
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);
	}

	@Test
	public void testSave_withEncryption() throws AES256Exception {
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setService(FINANCIAL_SERVICES);
		reminderHistory.setCustomerId(12345L);
		int ttl = 3600;

		ReminderHistory encryptedReminderHistory = new ReminderHistory();
		when(reminderHistoryEncryptionHandler.encryptReminderHistory(reminderHistory)).thenReturn(encryptedReminderHistory);

		service.save(reminderHistory, ttl);

		verify(reminderHistoryRepository, times(1)).save(encryptedReminderHistory, ttl);
	}

	@Test
	public void testSave_withoutEncryption() {
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setService("non-financial");
		int ttl = 3600;

		service.save(reminderHistory, ttl);

		verify(reminderHistoryRepository, times(1)).save(reminderHistory, ttl);
	}

	@Test(expected = AES256Exception.class)
	public void testSave_withEncryptionError() throws AES256Exception {
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setService(FINANCIAL_SERVICES);
		reminderHistory.setCustomerId(12345L);
		int ttl = 3600;

		when(reminderHistoryEncryptionHandler.encryptReminderHistory(reminderHistory)).thenThrow(new AES256Exception("Encryption error"));

		service.save(reminderHistory, ttl);
	}

	@Test
	public void testFindByCustomerIdAndRechargeNumberAndService_withEncryption() throws AES256Exception, ParseException {
		Long customerId = 12345L;
		String rechargeNumber = "1234567890";
		String serviceType = FINANCIAL_SERVICES;
		List<String> rechargeNumberList = Arrays.asList(rechargeNumber, "encrypted1234567890");
		List<ReminderHistory> encryptedList = Collections.singletonList(new ReminderHistory());

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted1234567890");
		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, rechargeNumberList, serviceType)).thenReturn(encryptedList);
		when(reminderHistoryEncryptionHandler.decryptReminderHistory(encryptedList)).thenReturn(encryptedList);

		List<ReminderHistory> result = service.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, serviceType);

		assertEquals(encryptedList, result);
	}

	@Test
	public void testFindByCustomerIdAndRechargeNumberAndService_withoutEncryption() {
		Long customerId = 12345L;
		String rechargeNumber = "1234567890";
		String serviceType = "non-financial";
		List<ReminderHistory> list = Collections.singletonList(new ReminderHistory());

		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, serviceType)).thenReturn(list);

		List<ReminderHistory> result = service.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, serviceType);

		assertEquals(list, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindByCustomerIdAndRechargeNumberAndService_withEncryptionError() throws AES256Exception, ParseException {
		Long customerId = 12345L;
		String rechargeNumber = "1234567890";
		String serviceType = FINANCIAL_SERVICES;

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted1234567890");
		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, Arrays.asList(rechargeNumber, "encrypted1234567890"), serviceType)).thenThrow(new AES256Exception("Encryption error"));

		service.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, serviceType);
	}

	@Test
	public void testFindByCustomerIdAndRechargeNumberInAndServiceIn_withEncryption() throws AES256Exception, ParseException {
		Long customerId = 12345L;
		List<String> rechargeNumbers = Arrays.asList("1234567890");
		List<String> services = Arrays.asList(FINANCIAL_SERVICES);
		List<String> encryptedRechargeNumbers = Arrays.asList("encrypted1234567890");
		List<ReminderHistory> encryptedList = Collections.singletonList(new ReminderHistory());

		when(aesUtil.encrypt("1234567890")).thenReturn("encrypted1234567890");
		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, Arrays.asList("1234567890", "encrypted1234567890"), services)).thenReturn(encryptedList);
		when(reminderHistoryEncryptionHandler.decryptReminderHistory(encryptedList)).thenReturn(encryptedList);

		List<ReminderHistory> result = service.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumbers, services);

		assertEquals(encryptedList, result);
	}

	@Test
	public void testFindByCustomerIdAndRechargeNumberInAndServiceIn_withoutEncryption() {
		Long customerId = 12345L;
		List<String> rechargeNumbers = Arrays.asList("1234567890");
		List<String> services = Arrays.asList("non-financial");
		List<ReminderHistory> list = Collections.singletonList(new ReminderHistory());

		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumbers, services)).thenReturn(list);

		List<ReminderHistory> result = service.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumbers, services);

		assertEquals(list, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindByCustomerIdAndRechargeNumberInAndServiceIn_withEncryptionError() throws AES256Exception, ParseException {
		Long customerId = 12345L;
		List<String> rechargeNumbers = Arrays.asList("1234567890");
		List<String> services = Arrays.asList(FINANCIAL_SERVICES);

		when(aesUtil.encrypt("1234567890")).thenReturn("encrypted1234567890");
		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, Arrays.asList("1234567890", "encrypted1234567890"), services)).thenThrow(new AES256Exception("Encryption error"));

		service.findByCustomerIdAndRechargeNumberInAndServiceIn(customerId, rechargeNumbers, services);
	}

	@Test
	public void testFindByCustomerIdAndRechargeNumberAndServiceAndOperator_withEncryption() throws AES256Exception, ParseException {
		Long customerId = 12345L;
		String rechargeNumber = "1234567890";
		String serviceType = FINANCIAL_SERVICES;
		String operator = "operator";
		List<String> rechargeNumberList = Arrays.asList(rechargeNumber, "encrypted1234567890");
		List<ReminderHistory> encryptedList = Collections.singletonList(new ReminderHistory());

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted1234567890");
		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceAndOperator(customerId, rechargeNumberList, serviceType, operator)).thenReturn(encryptedList);
		when(reminderHistoryEncryptionHandler.decryptReminderHistory(encryptedList)).thenReturn(encryptedList);

		List<ReminderHistory> result = service.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber, serviceType, operator);

		assertEquals(encryptedList, result);
	}

	@Test
	public void testFindByCustomerIdAndRechargeNumberAndServiceAndOperator_withoutEncryption() {
		Long customerId = 12345L;
		String rechargeNumber = "1234567890";
		String serviceType = "non-financial";
		String operator = "operator";
		List<ReminderHistory> list = Collections.singletonList(new ReminderHistory());

		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber, serviceType, operator)).thenReturn(list);

		List<ReminderHistory> result = service.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber, serviceType, operator);

		assertEquals(list, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindByCustomerIdAndRechargeNumberAndServiceAndOperator_withEncryptionError() throws AES256Exception, ParseException {
		Long customerId = 12345L;
		String rechargeNumber = "1234567890";
		String serviceType = FINANCIAL_SERVICES;
		String operator = "operator";

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted1234567890");
		when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberInAndServiceAndOperator(customerId, Arrays.asList(rechargeNumber, "encrypted1234567890"), serviceType, operator)).thenThrow(new AES256Exception("Encryption error"));

		service.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber, serviceType, operator);
	}

}
