package com.paytm.saga.service;

import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.dto.DeleteRequestBody;
import com.paytm.saga.factory.DummyRechargeNumberGeneratorFactory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.impl.DeleteNonCreditCardRecentCard;
import com.paytm.saga.service.impl.ServiceConfig;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import junit.framework.TestCase;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class DeleteNonCreditCardRecentCardTest {
    @Mock
    RecentsRepositoryWrapperService recentsRepository;
    @Mock
    CustomerBillRepository customerBillRepository;
    @Mock
    RecentDataToKafkaService recentDataToKafkaService;
    @InjectMocks
    DeleteNonCreditCardRecentCard deleteNonCreditCardRecentCard;
    @Mock
    DummyRechargeNumberGeneratorFactory dummyRechargeNumberGeneratorFactory;
    @Mock
    ServiceConfig serviceConfig;
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    private Recents mockNonCCRecent(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("prepaid");
        recents.setDueDate(new Date());
        recents.setTxnAmount(100.0);
        return recents;
    }

    private Recents mockNonCCRecentRentpayment(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("rent payment");
        recentsPrimaryKey.setRechargeNumber("101010");
        recentsPrimaryKey.setOperator("house rent");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("postpaid");
        recents.setRechargeNumber7("**********");
        recents.setDueDate(new Date());
        recents.setTxnAmount(100.0);
        return recents;
    }
    @Test
    public void testDeleteRecentCardWithRechargeNumberMatched() throws RecentDataToKafkaException {
        Recents recents=mockNonCCRecent();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getKey().getRechargeNumber());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteNonCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());


        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgument.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgument.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgument.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgument.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgument.getValue());

        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgumentCustomerBill.getValue());
    }

    @Test
    public void testDeleteRecentCardWithRechargeNumberMatchedRentPayment() throws RecentDataToKafkaException {
        Recents recents=mockNonCCRecentRentpayment();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("**********");
        deleteRequestBody.setBillerAccountId(101010);
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteNonCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());


        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgument.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgument.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgument.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgument.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgument.getValue());

        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgumentCustomerBill.getValue());
    }

    @Test
    public void testDeleteRecentCardWithRechargeNumberWithAmountMatched() throws RecentDataToKafkaException {
        Recents recents=mockNonCCRecent();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getKey().getRechargeNumber());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setTxnAmount(recents.getTxnAmount());
        deleteNonCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());


        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgument.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgument.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgument.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgument.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgument.getValue());

        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgumentCustomerBill.getValue());
    }

    @Test
    public void testDeleteRecentCardWithRechargeNumberWithAmountNotMatched() throws RecentDataToKafkaException {
        Recents recents=mockNonCCRecent();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getKey().getRechargeNumber());
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteRequestBody.setTxnAmount(recents.getTxnAmount()+1);
        deleteNonCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        ArgumentCaptor<Long> customerIdArgument = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(recentsArgument.capture(),customerIdArgument.capture(),
                serviceArgument.capture(),
                rechargeNumberArgument.capture(),
                operatorArgument.capture(),
                planBucketArgument.capture());
        ArgumentCaptor<Long> customerIdArgumentCustomerBill = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketArgumentCustomerBill = ArgumentCaptor.forClass(String.class);
        verify(customerBillRepository).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(customerIdArgumentCustomerBill.capture(),
                any(Date.class),
                serviceArgumentCustomerBill.capture(),
                rechargeNumberArgumentCustomerBill.capture(),
                operatorArgumentCustomerBill.capture(),
                planBucketArgumentCustomerBill.capture());


        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgument.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgument.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgument.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgument.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgument.getValue());

        TestCase.assertEquals(recents.getKey().getRechargeNumber(),rechargeNumberArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getCustomerId(),customerIdArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getService(),serviceArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getOperator(),operatorArgumentCustomerBill.getValue());
        TestCase.assertEquals(recents.getKey().getPlanBucket(),planBucketArgumentCustomerBill.getValue());
    }

    @Test
    public void testDeleteRecentCardWithRechargeNumberNotMatched() throws RecentDataToKafkaException {
        Recents recents=mockNonCCRecent();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber(recents.getKey().getRechargeNumber()+"123");
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteNonCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        verify(recentsRepository, never())
                .deleteRecentByCustomerIdAndServiceAndRecharge(any(Recents.class),any(Long.class),
                        any(String.class),
                        any(String.class),
                        any(String.class),
                        any(String.class));
        verify(customerBillRepository,never()).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(any(Long.class),
                any(Date.class),
                any(String.class),
                any(String.class),
                any(String.class),
                any(String.class));
    }

    @Test
    public void testDeleteRecentCardWithBillerIdNotMatched() throws RecentDataToKafkaException {
        Recents recents=mockNonCCRecentRentpayment();
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("**********");
        deleteRequestBody.setBillerAccountId(101100);
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());
        deleteNonCreditCardRecentCard.deleteRecentCard(recents,deleteRequestBody);
        verify(recentsRepository, never())
                .deleteRecentByCustomerIdAndServiceAndRecharge(any(Recents.class),any(Long.class),
                        any(String.class),
                        any(String.class),
                        any(String.class),
                        any(String.class));
        verify(customerBillRepository,never()).deleteByCustomerIdAndDueDateANDServiceAndRechargeNumber(any(Long.class),
                any(Date.class),
                any(String.class),
                any(String.class),
                any(String.class),
                any(String.class));
    }

    @Test
    public void deleteRecentCard_withFastagServiceAndMatchingRechargeNumber_deletesRecent() throws RecentDataToKafkaException {
        Recents recents = mockNonCCRecent();
        recents.getKey().setService("fastag recharge");
        recents.getKey().setRechargeNumber("wb95a1170");
        recents.getKey().setOperator("ICICI_Test");
        recents.getKey().setPlanBucket("");
        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("wb95a1170");
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());

        deleteNonCreditCardRecentCard.deleteRecentCard(recents, deleteRequestBody);

        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(
                any(Recents.class), eq(recents.getKey().getCustomerId()), eq(recents.getKey().getService()), eq(recents.getKey().getRechargeNumber()),  eq(recents.getKey().getOperator()),  eq(recents.getKey().getPlanBucket()));
    }

    @Test
    public void deleteRecentCard_withFastagServiceAndNonMatchingRechargeNumber_doesNotDeleteRecent() throws RecentDataToKafkaException {
        Recents recents = mockNonCCRecent();
        recents.getKey().setService("fastag recharge");
        recents.getKey().setRechargeNumber("wb95a1170");
        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("nonMatchingNumber");
        deleteRequestBody.setCustomerId(recents.getKey().getCustomerId());

        deleteNonCreditCardRecentCard.deleteRecentCard(recents, deleteRequestBody);

        verify(recentsRepository, never()).deleteRecentByCustomerIdAndServiceAndRecharge(any(Recents.class), anyLong(), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    public void deleteRecent_withFastagServiceAndNonLowBalanceOperator_deletesRecent() throws RecentDataToKafkaException {
        Recents recents = mockNonCCRecent();
        recents.getKey().setService("fastag recharge");
        recents.getKey().setOperator("ICICI_Test");
        recents.getKey().setPlanBucket("");
        recents.getKey().setRechargeNumber("wb95a1170");
        recents.setDueDate(new Date());

        deleteNonCreditCardRecentCard.deleteRecent(recents);

        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(
                any(Recents.class), eq(recents.getKey().getCustomerId()), eq(recents.getKey().getService()), eq(recents.getKey().getRechargeNumber()), eq(recents.getKey().getOperator()), eq(recents.getKey().getPlanBucket()));
    }
}
