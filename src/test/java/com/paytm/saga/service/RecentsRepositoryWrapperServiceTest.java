package com.paytm.saga.service;

import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.NickNameRequest;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.util.AESUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static com.paytm.saga.common.constant.EncryptionConstants.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class RecentsRepositoryWrapperServiceTest {

	@Mock
	private RecentsRepository recentsRepository;

	@Mock
	private RecentsEncryptionHandler recentsEncryptionHandler;

	@Mock
	private AESUtil aesUtil;

	@Mock
	private MetricsHelper metricsHelper;

	@InjectMocks
	private RecentsRepositoryWrapperService service;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, true);
		configMap.put(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS, Collections.singletonList("issuingBankCardVariant"));
		configMap.put(CUSTOMER_OTHER_INFO_ENCRYPTED_KEYS, new HashSet<>(Arrays.asList("lastCC", "rawLastCC", "currentBillAmount", "currentMinBillAmount", "billDate", "billDueDate", "debugKey", "amount")));
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);
	}

	@Test
	public void testFindById_withEncryption() throws AES256Exception {
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setCustomerId(12345L);
		key.setRechargeNumber("**********");
		key.setService(FINANCIAL_SERVICES);
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, false);
		configMap.put(ALLOWED_ROLLOUT_PERCENTAGE, 100);
		configMap.put(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS, Collections.singletonList("issuingBankCardVariant"));
		configMap.put(CUSTOMER_OTHER_INFO_ENCRYPTED_KEYS, new HashSet<>(Arrays.asList("lastCC", "rawLastCC", "currentBillAmount", "currentMinBillAmount", "billDate", "billDueDate", "debugKey", "amount")));
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);

		Recents encryptedRecents = new Recents();
		Recents decryptedRecents = new Recents();

		when(aesUtil.encrypt(key.getRechargeNumber())).thenReturn("encrypted**********");
		when(recentsRepository.findById(any(RecentsPrimaryKey.class))).thenReturn(Optional.of(encryptedRecents));
		when(recentsEncryptionHandler.decryptRecent(encryptedRecents)).thenReturn(decryptedRecents);

		Recents result = service.findById(key);

		assertEquals(decryptedRecents, result);
	}

	@Test
	public void testFindById_withoutEncryption() {
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setCustomerId(12345L);
		key.setRechargeNumber("**********");
		key.setService(FINANCIAL_SERVICES);		Recents recents = new Recents();

		when(recentsRepository.findById(key)).thenReturn(Optional.of(recents));

		Recents result = service.findById(key);

		assertEquals(recents, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindById_withEncryptionError() throws AES256Exception {
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setCustomerId(12345L);
		key.setRechargeNumber("**********");
		key.setService(FINANCIAL_SERVICES);
		when(aesUtil.encrypt(key.getRechargeNumber())).thenThrow(new AES256Exception("Encryption error"));

		service.findById(key);
	}

	@Test
	public void testUpdateRecentWhenNoExistingData_withEncryption() throws AES256Exception {
		Recents recents = new Recents();
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setCustomerId(12345L);
		key.setRechargeNumber("**********");
		key.setService(FINANCIAL_SERVICES);
		recents.setKey(key);
		Integer ttl = 100;
		Recents encryptedRecents = new Recents();

		when(recentsEncryptionHandler.encryptRecent(recents)).thenReturn(encryptedRecents);
		when(recentsRepository.updateRecentWhenNoExistingData(encryptedRecents, ttl)).thenReturn(true);

		Boolean result = service.updateRecentWhenNoExistingData(recents, ttl);

		assertTrue(result);
	}

	@Test
	public void testUpdateRecentWhenNoExistingData_withoutEncryption() {
		Recents recents = new Recents();

		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setCustomerId(12345L);
		key.setRechargeNumber("**********");
		key.setService(FINANCIAL_SERVICES);
		recents.setKey(key);
		Integer ttl = 100;

		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, false);
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);

		when(recentsRepository.updateRecentWhenNoExistingData(recents, ttl)).thenReturn(true);

		Boolean result = service.updateRecentWhenNoExistingData(recents, ttl);

		assertTrue(result);
	}

	@Test(expected = AES256Exception.class)
	public void testUpdateRecentWhenNoExistingData_withEncryptionError() throws AES256Exception {
		Recents recents = new Recents();
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setCustomerId(12345L);
		key.setRechargeNumber("**********");
		key.setService(FINANCIAL_SERVICES);
		recents.setKey(key);
		Integer ttl = 100;

		when(recentsEncryptionHandler.encryptRecent(recents)).thenThrow(new AES256Exception("Encryption error"));

		service.updateRecentWhenNoExistingData(recents, ttl);
	}

	@Test
	public void testFindByCustomerIdAndService_withEncryption() throws AES256Exception {
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;
		List<Recents> encryptedRecentsList = Arrays.asList(new Recents());
		List<Recents> decryptedRecentsList = Arrays.asList(new Recents());

		when(recentsRepository.findByCustomerIdAndService(customerId, serviceType)).thenReturn(encryptedRecentsList);
		when(recentsEncryptionHandler.decryptRecentList(encryptedRecentsList)).thenReturn(decryptedRecentsList);

		List<Recents> result = service.findByCustomerIdAndService(customerId, serviceType);

		assertEquals(decryptedRecentsList, result);
	}

	@Test
	public void testFindByCustomerIdAndService_withoutEncryption() {
		Long customerId = 12345L;
		String serviceType = "NON_FINANCIAL_SERVICE";
		List<Recents> recentsList = Arrays.asList(new Recents());

		when(recentsRepository.findByCustomerIdAndService(customerId, serviceType)).thenReturn(recentsList);

		List<Recents> result = service.findByCustomerIdAndService(customerId, serviceType);

		assertEquals(recentsList, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindByCustomerIdAndService_withEncryptionError() throws AES256Exception {
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;

		when(recentsRepository.findByCustomerIdAndService(customerId, serviceType)).thenThrow(new AES256Exception("Encryption error"));

		service.findByCustomerIdAndService(customerId, serviceType);
	}

	@Test
	public void testFindBycustomerIdAndserviceAndrechargeNumber_withEncryption() throws AES256Exception {
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;
		String rechargeNumber = "**********";
		List<Recents> recentsList = new ArrayList<>();

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");
		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberIN(customerId, serviceType, Arrays.asList(rechargeNumber, "encrypted**********"))).thenReturn(recentsList);
		when(recentsEncryptionHandler.decryptRecentList(recentsList)).thenReturn(recentsList);

		List<Recents> result = service.findBycustomerIdAndserviceAndrechargeNumber(customerId, serviceType, rechargeNumber);

		assertEquals(recentsList, result);
	}

	@Test
	public void testFindBycustomerIdAndserviceAndrechargeNumber_withoutEncryption() {
		Long customerId = 12345L;
		String serviceType = "NON_FINANCIAL_SERVICE";
		String rechargeNumber = "**********";
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(customerId, serviceType, rechargeNumber)).thenReturn(recentsList);

		List<Recents> result = service.findBycustomerIdAndserviceAndrechargeNumber(customerId, serviceType, rechargeNumber);

		assertEquals(recentsList, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindBycustomerIdAndserviceAndrechargeNumber_withEncryptionError() throws AES256Exception {
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;
		String rechargeNumber = "**********";

		when(aesUtil.encrypt(rechargeNumber)).thenThrow(new AES256Exception("Encryption error"));

		service.findBycustomerIdAndserviceAndrechargeNumber(customerId, serviceType, rechargeNumber);
	}

	@Test
	public void testFindBycustomerIdAndserviceAndrechargeNumberAndoperator_withEncryption() throws AES256Exception {
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;
		String rechargeNumber = "**********";
		String operator = "operator";
		List<Recents> recentsList = new ArrayList<>();

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");
		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberINAndoperator(customerId, serviceType, Arrays.asList(rechargeNumber, "encrypted**********"), operator)).thenReturn(recentsList);
		when(recentsEncryptionHandler.decryptRecentList(recentsList)).thenReturn(recentsList);

		List<Recents> result = service.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customerId, serviceType, rechargeNumber, operator);

		assertEquals(recentsList, result);
	}

	@Test
	public void testFindBycustomerIdAndserviceAndrechargeNumberAndoperator_withoutEncryption() {
		Long customerId = 12345L;
		String serviceType = "NON_FINANCIAL_SERVICE";
		String rechargeNumber = "**********";
		String operator = "operator";
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customerId, serviceType, rechargeNumber, operator)).thenReturn(recentsList);

		List<Recents> result = service.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customerId, serviceType, rechargeNumber, operator);

		assertEquals(recentsList, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindBycustomerIdAndserviceAndrechargeNumberAndoperator_withEncryptionError() throws AES256Exception {
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;
		String rechargeNumber = "**********";
		String operator = "operator";

		when(aesUtil.encrypt(rechargeNumber)).thenThrow(new AES256Exception("Encryption error"));

		service.findBycustomerIdAndserviceAndrechargeNumberAndoperator(customerId, serviceType, rechargeNumber, operator);
	}

	@Test
	public void testFindByCustomerId_withEncryption() throws AES256Exception {
		Long customerId = 12345L;
		int agentLimit = 10;
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.findByCustomerId(customerId, agentLimit)).thenReturn(recentsList);
		when(recentsEncryptionHandler.decryptRecentList(recentsList)).thenReturn(recentsList);

		List<Recents> result = service.findByCustomerId(customerId, agentLimit);

		assertEquals(recentsList, result);
	}

	@Test
	public void testFindByCustomerId_withoutEncryption() {
		Long customerId = 12345L;
		int agentLimit = 10;
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.findByCustomerId(customerId, agentLimit)).thenReturn(recentsList);

		List<Recents> result = service.findByCustomerId(customerId, agentLimit);

		assertEquals(recentsList, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindByCustomerId_withEncryptionError() throws AES256Exception {
		Long customerId = 12345L;
		int agentLimit = 10;

		when(recentsRepository.findByCustomerId(customerId, agentLimit)).thenThrow(new AES256Exception("Encryption error"));

		service.findByCustomerId(customerId, agentLimit);
	}

	@Test
	public void testDeleteRecentByCustomerIdAndServiceAndRecharge_withEncryption() throws AES256Exception {
		Recents recents = new Recents();
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;
		String rechargeNumber = "**********";
		String operator = "operator";
		String planBucket = "planBucket";

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");

		service.deleteRecentByCustomerIdAndServiceAndRecharge(recents, customerId, serviceType, rechargeNumber, operator, planBucket);

		verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRechargeIN(recents, customerId, serviceType, Arrays.asList(rechargeNumber, "encrypted**********"), operator, planBucket);
	}

	@Test
	public void testDeleteRecentByCustomerIdAndServiceAndRecharge_withoutEncryption() {
		Recents recents = new Recents();
		Long customerId = 12345L;
		String serviceType = "NON_FINANCIAL_SERVICE";
		String rechargeNumber = "**********";
		String operator = "operator";
		String planBucket = "planBucket";

		service.deleteRecentByCustomerIdAndServiceAndRecharge(recents, customerId, serviceType, rechargeNumber, operator, planBucket);

		verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRecharge(recents, customerId, serviceType, rechargeNumber, operator, planBucket);
	}

	@Test
	public void testFindBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket_withEncryption() throws AES256Exception {
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;
		String rechargeNumber = "**********";
		String operator = "operator";
		String planBucket = "planBucket";
		List<Recents> recentsList = new ArrayList<>();

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");
		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberINAndoperatorAndplanBucket(customerId, serviceType, Arrays.asList(rechargeNumber, "encrypted**********"), operator, planBucket)).thenReturn(recentsList);
		when(recentsEncryptionHandler.decryptRecentList(recentsList)).thenReturn(recentsList);

		List<Recents> result = service.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, serviceType, rechargeNumber, operator, planBucket);

		assertEquals(recentsList, result);
	}

	@Test
	public void testFindBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket_withoutEncryption() {
		Long customerId = 12345L;
		String serviceType = "NON_FINANCIAL_SERVICE";
		String rechargeNumber = "**********";
		String operator = "operator";
		String planBucket = "planBucket";
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, serviceType, rechargeNumber, operator, planBucket)).thenReturn(recentsList);

		List<Recents> result = service.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, serviceType, rechargeNumber, operator, planBucket);

		assertEquals(recentsList, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket_withEncryptionError() throws AES256Exception {
		Long customerId = 12345L;
		String serviceType = FINANCIAL_SERVICES;
		String rechargeNumber = "**********";
		String operator = "operator";
		String planBucket = "planBucket";

		when(aesUtil.encrypt(rechargeNumber)).thenThrow(new AES256Exception("Encryption error"));

		service.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(customerId, serviceType, rechargeNumber, operator, planBucket);
	}

	@Test
	public void testFindBycustomerIdAndrechargeNumberAndservice_withEncryption() throws AES256Exception {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = FINANCIAL_SERVICES;
		List<Recents> recentsList = new ArrayList<>();

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");
		when(recentsRepository.findBycustomerIdAndrechargeNumberINAndservice(customerId, Arrays.asList(rechargeNumber, "encrypted**********"), serviceType)).thenReturn(recentsList);
		when(recentsEncryptionHandler.decryptRecentList(recentsList)).thenReturn(recentsList);

		List<Recents> result = service.findBycustomerIdAndrechargeNumberAndservice(customerId, rechargeNumber, serviceType);

		assertEquals(recentsList, result);
	}

	@Test
	public void testFindBycustomerIdAndrechargeNumberAndservice_withoutEncryption() {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = "NON_FINANCIAL_SERVICE";
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(customerId, rechargeNumber, serviceType)).thenReturn(recentsList);

		List<Recents> result = service.findBycustomerIdAndrechargeNumberAndservice(customerId, rechargeNumber, serviceType);

		assertEquals(recentsList, result);
	}

	@Test(expected = AES256Exception.class)
	public void testFindBycustomerIdAndrechargeNumberAndservice_withEncryptionError() throws AES256Exception {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = FINANCIAL_SERVICES;

		when(aesUtil.encrypt(rechargeNumber)).thenThrow(new AES256Exception("Encryption error"));

		service.findBycustomerIdAndrechargeNumberAndservice(customerId, rechargeNumber, serviceType);
	}

	@Test
	public void testSelectNonCreditRecents() {
		NickNameRequest nickNameRequest = new NickNameRequest();
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.selectNonCreditRecents(nickNameRequest)).thenReturn(recentsList);

		List<Recents> result = service.selectNonCreditRecents(nickNameRequest);

		assertEquals(recentsList, result);
	}

	@Test
	public void testSelectCreditCardRecents_withEncryption() throws AES256Exception {
		NickNameRequest nickNameRequest = new NickNameRequest();
		nickNameRequest.setService(FINANCIAL_SERVICES);
		nickNameRequest.setCustomerId(12345L);
		nickNameRequest.setRechargeNumber("**********");
		List<Recents> recentsList = new ArrayList<>();

		when(aesUtil.encrypt(nickNameRequest.getRechargeNumber())).thenReturn("encrypted**********");
		when(recentsRepository.selectCreditCardRecentsRechargeNumberIN(nickNameRequest, Arrays.asList(nickNameRequest.getRechargeNumber(), "encrypted**********"))).thenReturn(recentsList);
		when(recentsEncryptionHandler.decryptRecentList(recentsList)).thenReturn(recentsList);

		List<Recents> result = service.selectCreditCardRecents(nickNameRequest);

		assertEquals(recentsList, result);
	}

	@Test
	public void testSelectCreditCardRecents_withoutEncryption() {
		NickNameRequest nickNameRequest = new NickNameRequest();
		nickNameRequest.setService("NON_FINANCIAL_SERVICE");
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.selectCreditCardRecents(nickNameRequest)).thenReturn(recentsList);

		List<Recents> result = service.selectCreditCardRecents(nickNameRequest);

		assertEquals(recentsList, result);
	}

	@Test(expected = AES256Exception.class)
	public void testSelectCreditCardRecents_withEncryptionError() throws AES256Exception {
		NickNameRequest nickNameRequest = new NickNameRequest();
		nickNameRequest.setService(FINANCIAL_SERVICES);
		nickNameRequest.setCustomerId(12345L);
		nickNameRequest.setRechargeNumber("**********");

		when(aesUtil.encrypt(nickNameRequest.getRechargeNumber())).thenThrow(new AES256Exception("Encryption error"));

		service.selectCreditCardRecents(nickNameRequest);
	}

	@Test
	public void testSelectPrepaidRecentsWithoutPlanBucket_withEncryption() throws AES256Exception {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = FINANCIAL_SERVICES;
		String operator = "operator";
		List<Recents> recentsList = new ArrayList<>();

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");
		when(recentsRepository.selectPrepaidRecentsWithoutPlanBucketRechargeNumberIN(customerId, Arrays.asList(rechargeNumber, "encrypted**********"), serviceType, operator)).thenReturn(recentsList);
		when(recentsEncryptionHandler.decryptRecentList(recentsList)).thenReturn(recentsList);

		List<Recents> result = service.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, serviceType, operator);

		assertEquals(recentsList, result);
	}

	@Test
	public void testSelectPrepaidRecentsWithoutPlanBucket_withoutEncryption() {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = "NON_FINANCIAL_SERVICE";
		String operator = "operator";
		List<Recents> recentsList = new ArrayList<>();

		when(recentsRepository.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, serviceType, operator)).thenReturn(recentsList);

		List<Recents> result = service.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, serviceType, operator);

		assertEquals(recentsList, result);
	}

	@Test(expected = AES256Exception.class)
	public void testSelectPrepaidRecentsWithoutPlanBucket_withEncryptionError() throws AES256Exception {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = FINANCIAL_SERVICES;
		String operator = "operator";

		when(aesUtil.encrypt(rechargeNumber)).thenThrow(new AES256Exception("Encryption error"));

		service.selectPrepaidRecentsWithoutPlanBucket(customerId, rechargeNumber, serviceType, operator);
	}

	@Test
	public void testFindCountByCustomerId() {
		Long customerId = 12345L;
		Integer limit = 10;
		Set<Recents> recentsSet = new HashSet<>();

		when(recentsRepository.findCountByCustomerId(customerId, limit)).thenReturn(recentsSet);

		Set<Recents> result = service.findCountByCustomerId(customerId, limit);

		assertEquals(recentsSet, result);
	}

	@Test
	public void testFindByCustomerIdINAndServiceAndRechargeNumberAndOperator_withEncryption() throws AES256Exception {
		List<Long> customerIds = Arrays.asList(12345L);
		String serviceType = FINANCIAL_SERVICES;
		String rechargeNumber = "**********";
		String operator = "operator";
		List<Recents> recentsList = new ArrayList<>();

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");
		when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberINAndOperator(customerIds, serviceType, Arrays.asList(rechargeNumber, "encrypted**********"), operator)).thenReturn(recentsList);
		when(recentsEncryptionHandler.decryptRecentList(recentsList)).thenReturn(recentsList);

		List<Recents> result = service.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(customerIds, serviceType, rechargeNumber, operator);

		assertEquals(recentsList, result);
	}

	@Test
	public void testUpdateRecentWhenDataAlreadyExist_nonFinancialService() {
		Recents recents = new Recents();
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setService("NON_FINANCIAL_SERVICE");
		recents.setKey(key);
		Date oldUpdatedAt = new Date();
		Integer ttl = 3600;

		when(recentsRepository.updateRecentWhenDataAlreadyExist(recents, oldUpdatedAt, ttl)).thenReturn(true);

		Boolean result = service.updateRecentWhenDataAlreadyExist(recents, oldUpdatedAt, ttl);

		assertTrue(result);
		verify(recentsRepository, times(1)).updateRecentWhenDataAlreadyExist(recents, oldUpdatedAt, ttl);
	}

	@Test
	public void testUpdateRecentWhenDataAlreadyExist_noEncryptionRequired() {
		Recents recents = new Recents();
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setService(FINANCIAL_SERVICES);
		key.setCustomerId(12345L);
		recents.setKey(key);
		Date oldUpdatedAt = new Date();
		Integer ttl = 3600;
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, false);
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);

		when(recentsRepository.updateRecentWhenDataAlreadyExist(recents, oldUpdatedAt, ttl)).thenReturn(true);

		Boolean result = service.updateRecentWhenDataAlreadyExist(recents, oldUpdatedAt, ttl);

		assertTrue(result);
		verify(recentsRepository, times(1)).updateRecentWhenDataAlreadyExist(recents, oldUpdatedAt, ttl);
	}


	@Test
	public void testUpdateRecentWhenDataAlreadyExistInInsert_nonFinancialService() {
		Recents recents = new Recents();
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setService("NON_FINANCIAL_SERVICE");
		recents.setKey(key);
		Integer ttl = 3600;

		doNothing().when(recentsRepository).updateRecentWhenDataAlreadyExistInInsert(recents, ttl);

		service.updateRecentWhenDataAlreadyExistInInsert(recents, ttl);

		verify(recentsRepository, times(1)).updateRecentWhenDataAlreadyExistInInsert(recents, ttl);
	}

	@Test
	public void testUpdateRecentWhenDataAlreadyExistInInsert_noEncryptionRequired() {
		Recents recents = new Recents();
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setService(FINANCIAL_SERVICES);
		key.setCustomerId(12345L);
		recents.setKey(key);
		Integer ttl = 3600;
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, false);
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);

		doNothing().when(recentsRepository).updateRecentWhenDataAlreadyExistInInsert(recents, ttl);

		service.updateRecentWhenDataAlreadyExistInInsert(recents, ttl);

		verify(recentsRepository, times(1)).updateRecentWhenDataAlreadyExistInInsert(recents, ttl);
	}
}
