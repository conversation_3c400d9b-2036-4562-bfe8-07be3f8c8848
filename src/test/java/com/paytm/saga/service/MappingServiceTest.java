package com.paytm.saga.service;

import com.paytm.saga.dto.EvictCacheResponse;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.UpsertCacheResponse;
import com.paytm.saga.repository.CustomerMapRepository;
import com.paytm.saga.repository.RechargeNumberMapRepository;
import com.paytm.saga.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static junit.framework.TestCase.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

public class MappingServiceTest {
    @Mock
    CustomerMapRepository customerMapRepository;
    @Mock
    RechargeNumberMapRepository rechargeNumberMapRepository;
    @InjectMocks MappingService mappingService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetRechargeNumbersByCustomerAndService(){
        when(customerMapRepository.findByCustomerIdAndService(any(),any()))
                .thenReturn(new ArrayList<>());
        assertEquals(0,mappingService.getRechargeNumbers(1L,"service").size());
    }

    @Test
    public void testGetRechargeNumbersByCustomerAndServiceAndOperator(){
        when(customerMapRepository.findByCustomerIdAndServiceAndOperator(any(),any(),any()))
                .thenReturn(new ArrayList<>());
        assertEquals(0,mappingService.getRechargeNumbers(1L,"service","operator").size());
    }

    @Test
    public void testGetCustomerIdsByRechargeNumberAndService(){
        when(rechargeNumberMapRepository.findByRechargeNumberAndService(any(),any()))
                .thenReturn(new ArrayList<>());
        assertEquals(0,mappingService.getCustomerIds("1","service").size());
    }

    @Test
    public void testGetCustomerIdsByRechargeNumberAndServiceAndOperator(){
        when(rechargeNumberMapRepository.findByRechargeNumberAndService(any(),any()))
                .thenReturn(new ArrayList<>());
        assertEquals(0,mappingService.getCustomerIds("1","service","operator").size());
    }

    @Test
    public void testSave(){
        mappingService.setRechargeNumberMap("rechargeNumber", 1L, "service", "operator", new Date());
        verify(rechargeNumberMapRepository, times(1)).save(any());
    }


}
