package com.paytm.saga.service;

import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.util.AESUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import static com.paytm.saga.common.constant.EncryptionConstants.ENCRYPTION_DATE_FORMAT;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ReminderHistoryEncryptionHandlerTest {

	@Mock
	private AESUtil aesUtil;

	@InjectMocks
	private ReminderHistoryEncryptionHandler reminderHistoryEncryptionHandler;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		when(aesUtil.encrypt("100.0")).thenReturn("encrypted Amount");
		when(aesUtil.encrypt("50.0")).thenReturn("encrypted CurrentMinBillAmount");
		when(aesUtil.encrypt("2025-01-01 00:00:00.000")).thenReturn("encrypted DueDate");
		when(aesUtil.encrypt("200.0")).thenReturn("encrypted OriginalAmount");
		when(aesUtil.encrypt("150.0")).thenReturn("encrypted OriginalMinBillAmount");
		when(aesUtil.encrypt("1234567890")).thenReturn("encrypted RechargeNumber");
		when(aesUtil.encrypt("ref123")).thenReturn("encrypted Reference_id");
		when(aesUtil.decrypt("encrypted Amount")).thenReturn("100.0");
		when(aesUtil.decrypt("encrypted CurrentMinBillAmount")).thenReturn("50.0");
		when(aesUtil.decrypt("encrypted DueDate")).thenReturn("2025-01-01 00:00:00.000");
		when(aesUtil.decrypt("encrypted OriginalAmount")).thenReturn("200.0");
		when(aesUtil.decrypt("encrypted OriginalMinBillAmount")).thenReturn("150.0");
		when(aesUtil.decrypt("encrypted RechargeNumber")).thenReturn("1234567890");
		when(aesUtil.decrypt("encrypted Reference_id")).thenReturn("ref123");
	}

	@Test
	public void testEncryptReminderHistory() throws ParseException {
		ReminderHistory reminderHistory = getDecryptedReminderHistory();
		ReminderHistory encryptedReminderHistory = reminderHistoryEncryptionHandler.encryptReminderHistory(reminderHistory);
		assertNotNull(encryptedReminderHistory);
		assertEquals("encrypted Amount", encryptedReminderHistory.getEncAmount());
		assertEquals("encrypted CurrentMinBillAmount", encryptedReminderHistory.getEncCurrentMinBillAmount());
		assertEquals("encrypted DueDate", encryptedReminderHistory.getEncDueDate());
		assertEquals("encrypted OriginalAmount", encryptedReminderHistory.getEncOriginalAmount());
		assertEquals("encrypted OriginalMinBillAmount", encryptedReminderHistory.getEncOriginalMinBillAmount());
		assertEquals("encrypted RechargeNumber", encryptedReminderHistory.getRechargeNumber());
		assertEquals("encrypted Reference_id", encryptedReminderHistory.getReference_id());
		assertEquals(Integer.valueOf(1), encryptedReminderHistory.getIsEncrypted());
	}

	@Test
	public void testDecryptReminderHistory() throws ParseException {
		ReminderHistory reminderHistory = getEncryptedReminderHistory();
		ReminderHistory decryptedReminderHistory = reminderHistoryEncryptionHandler.decryptReminderHistory(reminderHistory);
		assertNotNull(decryptedReminderHistory);
		assertEquals(Double.valueOf(100.0), decryptedReminderHistory.getAmount());
		assertEquals(Double.valueOf(50.0), decryptedReminderHistory.getCurrentMinBillAmount());
		assertNotNull(decryptedReminderHistory.getDue_date());
		assertEquals(Double.valueOf(200.0), decryptedReminderHistory.getOriginalAmount());
		assertEquals(Double.valueOf(150.0), decryptedReminderHistory.getOriginalMinBillAmount());
		assertEquals("1234567890", decryptedReminderHistory.getRechargeNumber());
		assertEquals("ref123", decryptedReminderHistory.getReference_id());
		assertEquals(Integer.valueOf(0), decryptedReminderHistory.getIsEncrypted());
	}

	@Test
	public void testDecryptReminderHistoryList() throws ParseException {
		List<ReminderHistory> reminderHistoryList = new ArrayList<>();
		ReminderHistory reminderHistory = getDecryptedReminderHistory();
		reminderHistoryList.add(reminderHistory);

		List<ReminderHistory> decryptedReminderHistoryList = reminderHistoryEncryptionHandler.decryptReminderHistory(reminderHistoryList);

		assertNotNull(decryptedReminderHistoryList);
		assertEquals(1, decryptedReminderHistoryList.size());
		ReminderHistory decryptedReminderHistory = decryptedReminderHistoryList.get(0);
		assertEquals(Double.valueOf(100.0), decryptedReminderHistory.getAmount());
		assertEquals(Double.valueOf(50.0), decryptedReminderHistory.getCurrentMinBillAmount());
		assertNotNull(decryptedReminderHistory.getDue_date());
		assertEquals(Double.valueOf(200.0), decryptedReminderHistory.getOriginalAmount());
		assertEquals(Double.valueOf(150.0), decryptedReminderHistory.getOriginalMinBillAmount());
		assertEquals("1234567890", decryptedReminderHistory.getRechargeNumber());
		assertEquals("ref123", decryptedReminderHistory.getReference_id());
		assertEquals(Integer.valueOf(0), decryptedReminderHistory.getIsEncrypted());
	}

	@Test
	public void testIsEncrypted() {
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setIsEncrypted(1);
		assertTrue(reminderHistoryEncryptionHandler.isEncrypted(reminderHistory));
		reminderHistory.setIsEncrypted(0);
		assertFalse(reminderHistoryEncryptionHandler.isEncrypted(reminderHistory));
	}

	private ReminderHistory getDecryptedReminderHistory() throws ParseException {
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setAmount(100.0);
		reminderHistory.setCurrentMinBillAmount(50.0);
		reminderHistory.setDue_date(new SimpleDateFormat(ENCRYPTION_DATE_FORMAT).parse("2025-01-01 00:00:00.000"));
		reminderHistory.setOriginalAmount(200.0);
		reminderHistory.setOriginalMinBillAmount(150.0);
		reminderHistory.setRechargeNumber("1234567890");
		reminderHistory.setReference_id("ref123");
		reminderHistory.setIsEncrypted(0);
		return reminderHistory;
	}

	private ReminderHistory getEncryptedReminderHistory(){
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setEncAmount("encrypted Amount");
		reminderHistory.setEncCurrentMinBillAmount("encrypted CurrentMinBillAmount");
		reminderHistory.setEncDueDate("encrypted DueDate");
		reminderHistory.setEncOriginalAmount("encrypted OriginalAmount");
		reminderHistory.setEncOriginalMinBillAmount("encrypted OriginalMinBillAmount");
		reminderHistory.setRechargeNumber("encrypted RechargeNumber");
		reminderHistory.setReference_id("encrypted Reference_id");
		reminderHistory.setIsEncrypted(1);
		return reminderHistory;
	}
}
