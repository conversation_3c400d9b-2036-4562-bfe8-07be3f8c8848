package com.paytm.saga.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.common.constant.UtilityThemeTypes;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.repository.AutomaticDataRepository;
import com.paytm.saga.service.aggregator.CommonAggregatorService;
import com.paytm.saga.service.aggregator.UtilityAggregatorServiceImpl;
import com.paytm.saga.service.aggregator.cardmanager.CardManager;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.service.aggregator.cardmanager.DateCard;
import com.paytm.saga.util.DateUtil;
import org.apache.logging.log4j.LogManager;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class UtilityMnpTest {

    private static final Date TRANSACTION_DATE = DateUtil.minutesIncrDecr(new Date(), -5);
    private static final Date DUE_DATE_5DAYS = DateUtil.dateIncrDecr(new Date(), 5);
    private static final Date AUTOMATIC_DATE_3DAYS = DateUtil.dateIncrDecr(new Date(), 3);

    private static final Date TRANSACTION_UPDATE_DATE = DateUtil.minutesIncrDecr(new Date(), -4);
    private static final Date CREATED_DATE = DateUtil.minutesIncrDecr(new Date(), -3);
    ObjectMapper objectMapper = new ObjectMapper();

    @InjectMocks
    private UtilityAggregatorServiceImpl utilityAggregatorService;

    @Mock
    private CardManager cardManager;

    @Mock
    private ChannelHistoryFinalizedService channelHistoryFinalizedService;
    @Mock
    private ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
    @Mock
    private DateCard dateCardService;

    @Mock
    private ChannelHistoryService channelHistoryService;
    @Mock
    private ReminderHistoryService reminderHistoryService;

    @Mock
    private AutomaticDataRepository automaticDataRepository;

    @Mock
    private CommonAggregatorService commonAggregatorService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(UtilityAggregatorServiceImpl.class));

    private HistoryView getCards(){
        HistoryView historyView = new HistoryView();
        historyView.setEventType(CardTypes.MNP_CARD);
        historyView.setEventDate(new Date());
        return historyView;
    }

    private List<ChannelHistory> getChannelHistory(){
        List<ChannelHistory> channelHistories = new ArrayList<>();
        ChannelHistory channelHistory = new ChannelHistory();
        channelHistory.setCustomerId(7091L);
        channelHistory.setOperator("airtel landline");
        channelHistory.setService("landline");
        channelHistory.setAmount("300.0");
        channelHistory.setRechargeNumber("12222");
        channelHistory.setTransactionTime(new Date());
        channelHistory.setOrderId(1L);
        channelHistory.setItemId(1L);
        channelHistory.setTransactionUpdateTime(new Date());
        channelHistory.setEventType("RECHARGE");
        channelHistory.setPaytype("POSTPAID");
        channelHistories.add(channelHistory);
        return channelHistories;
    }

    private ChannelHistoryPage getChannelFinalized(){
        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setLastCardId("1234");
        channelHistoryPage.setMarkFinaliseData(null);
        channelHistoryPage.setChannelHistories(getChannelHistory());
        return channelHistoryPage;
    }
    private List<ChannelHistory> getChannelHistoryForPaytype(){
        List<ChannelHistory> channelHistories = new ArrayList<>();
        ChannelHistory channelHistory = new ChannelHistory();
        channelHistory.setCustomerId(7091L);
        channelHistory.setOperator("jio");
        channelHistory.setService("mobile");
        channelHistory.setAmount("300.0");
        channelHistory.setRechargeNumber("12222");
        channelHistory.setTransactionTime(new Date());
        channelHistory.setOrderId(1L);
        channelHistory.setItemId(1L);
        channelHistory.setTransactionUpdateTime(new Date());
        channelHistory.setEventType("RECHARGE");
        channelHistory.setPaytype("Prepaid");
        channelHistories.add(channelHistory);
        return channelHistories;
    }


    private ChannelHistoryPage getChannelFinalizedForPaytype(){
        ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
        channelHistoryPage.setLastCardId("1234");
        channelHistoryPage.setMarkFinaliseData(null);
        channelHistoryPage.setChannelHistories(getChannelHistoryForPaytype());
        return channelHistoryPage;
    }

    @Test
    public void testChangeOperatorData(){
        GetHistoryPageDTO historyPage = new GetHistoryPageDTO();
        historyPage.setCustomerId(7091L);
        historyPage.setService("landline");
        historyPage.setOperator("airtel landline");
        historyPage.setRecharge_number("12222");
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setEventType(CardTypes.OPERATOR_CHANGE_CARD);
        cardInfoDto.setThemeType(UtilityThemeTypes.MNP);
        cardInfoDto.setPreviousOperator("bsnl");
        cardInfoDto.setHistoryPageRequest(historyPage);
        when(channelHistoryService.getPageOfHistory(7091L,"12222","mobile")).thenReturn(getChannelHistory());
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(7091L,"12222","landline",0,null)).thenReturn(getChannelFinalized());
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(getCards());
        when(cardManager.getCardInfo(any())).thenReturn(getCards());

        HistoryPage history = utilityAggregatorService.aggregateHistoryInfo(historyPage,"bsnl",null);

        assertEquals(history.getCards().get(0).getEventType(),"mnp");

    }

    @Test
    public void testChangeOperatorAndPaytypeData(){
        GetHistoryPageDTO historyPage = new GetHistoryPageDTO();
        historyPage.setCustomerId(7091L);
        historyPage.setService("mobile");
        historyPage.setOperator("airtel");
        historyPage.setRecharge_number("12222");
        historyPage.setPayType("Postpaid");
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setEventType(CardTypes.OPERATOR_CHANGE_CARD);
        cardInfoDto.setThemeType(UtilityThemeTypes.MNP);
        cardInfoDto.setMnpType(CardTypes.OPERATOR_PAYTYPE_CHANGE_CARD);
        cardInfoDto.setPreviousOperator("jio");
        cardInfoDto.setPreviousPayType("Prepaid");
        cardInfoDto.setHistoryPageRequest(historyPage);
        when(channelHistoryFinalizedUtil.convertFinalizedToHistory(7091L,"12222","mobile",0,null)).thenReturn(getChannelFinalizedForPaytype());
        when(cardManager.getCardInfo(cardInfoDto)).thenReturn(getCards());
        when(cardManager.getCardInfo(any())).thenReturn(getCards());
        HistoryPage history = utilityAggregatorService.aggregateHistoryInfo(historyPage,"jio",null);
        assertEquals(history.getDedupeids()[1],"jio");
        assertEquals(history.getDedupeids()[2],"Prepaid");
        assertEquals(history.getCards().get(0).getEventType(),"mnp");
    }

}
