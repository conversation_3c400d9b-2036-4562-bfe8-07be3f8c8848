package com.paytm.saga.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.RecentDao;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.enums.BillState;
import com.paytm.saga.enums.EventState;
import com.paytm.saga.enums.EventType;
import com.paytm.saga.enums.Service;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.RecentUtils;
import com.timgroup.statsd.StatsDClient;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.cassandra.core.CassandraTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.*;

import static com.paytm.saga.common.constant.Constants.*;
import static com.paytm.saga.service.FrequentOrderServiceImpl.setDateAndSource;
import static com.paytm.saga.util.DateUtil.formatDate;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RecentWidgetServiceImplTest {

    @Mock
    CassandraTemplate template;
    @Mock
    StatsDClient monitoringClient;
    @InjectMocks
    private RecentWidgetDataService frequentOrderService;
    @Mock
    private UserAgentService userAgentService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    private DropOffService dropOffService;

    @Mock
    private RecentDao recentDao;

    @Mock
    private ServiceConfig serviceConfig;

    @Mock
    private List<Future<Object>> recentAndDropOffTaskFuture;

    @Mock
    private LocalisationManager localisationManager;

    @Mock
    private SmartRecentsService smartRecentsService;

    @Mock
    private MetricsHelper metricsHelper;


    @Before
    public void setUp() {

        String config = "{\"isSMSLive\": \"{\\\"iosapp\\\":{\\\"versions\\\":{\\\"10.13.0\\\":[64739]}},\\\"androidapp\\\":{\\\"versions\\\":{\\\"10.13.0\\\":[64739,17]}}}\",\"recentCategoryLimit\":2,\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"newAccountServices\": [\"mobile\"]}";
        Map<String,List<Integer>> categoryMapping = null;
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
            categoryMapping = objectMapper.readValue("{\"mobile\":[17,21],\"financial services\":[64739]}", new TypeReference<Map<String,List<Integer>>>() {
            });
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        ReflectionTestUtils.setField(frequentOrderService, "includeOperatorInKeyServices", Arrays.asList("dth"));

        ExecutorService executorService = new ThreadPoolExecutor(20, 20, 10, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(10));
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderDropOffExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderSmartRecentExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderRecentExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderSmartRecentExecutor", executorService);
        when(serviceConfig.getPrepaidEndDay()).thenReturn(3);
        when(serviceConfig.getPrepaidStartDay()).thenReturn(5);
        when(serviceConfig.getPostpaidEndDay()).thenReturn(3);
        when(serviceConfig.getRecentCategoryLimit()).thenReturn(2);
        when(serviceConfig.getCategoryMapping()).thenReturn(categoryMapping);

        String  jsonConfig = "{\"androidapp\":{\"dth\":{\"removeFromMyAccount\":\"9.19.3\",\"browsePlans\":\"9.19.3\",\"paymentHistory\":\"9.19.3\",\"nickname\":\"9.19.3\"},\"electricity\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"cylinderbooking\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"broadband\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"landline\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"water\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"gas\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"insurance\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"donation\":{\"paymentHistory\":\"10.6.0\",\"removeFromMyAccount\":\"10.6.0\",\"nickname\":\"10.6.0\"},\"devotion \":{\"paymentHistory\":\"10.6.0 \",\"removeFromMyAccount\":\"10.6.0\",\"nickname\":\"10.6.0\"},\"versions\":{\"9.10.0\":[156705,166690,26,18,17,21,132935,127781,37217,64739,208100,198239,206334,199289,206896,262072,289829],\"9.8.0\":[166690,26,18,17,21,132935,127781,37217,64739,208100,198239,206334,199289,206896],\"9.6.0\":[17,21]},\"paymentHistory\":\"9.16.0\",\"nickname\":\"9.17.0\"},\"iosapp\":{\"electricity\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"cylinderbooking\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"dth\":{\"removeFromMyAccount\":\"9.19.3\",\"browsePlans\":\"9.19.3\",\"paymentHistory\":\"9.19.3\",\"nickname\":\"9.19.3\"},\"broadband\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"landline\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"water\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"gas\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"insurance\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"donation\":{\"paymentHistory\":\"10.6.0\",\"removeFromMyAccount\":\"10.6.0\",\"nickname\":\"10.6.0\"},\"devotion \":{\"paymentHistory \":\"10.6.0\",\"removeFromMyAccount\":\"10.6.0\",\"nickname\":\"10.6.0\"},\"versions\":{\"9.16.0\":[156705,166690,26,18,17,21,132935,127781,37217,64739,208100,198239,206334,199289,206896,262072,289829],\"9.11.0\":[166690,26,18,17,21,132935,127781,37217,64739,208100,198239,206334,199289,206896],\"9.6.0\":[17,21]},\"paymentHistory\":\"9.16.0\",\"nickname\":\"9.17.0\"}}";
        String[] keys = new String[]{"en-IN", "recents", "drop_off_config"};
        when(localisationManager.getMessage(keys, false, Collections.emptyMap())).thenReturn(jsonConfig);


    }


    @Test
    public void testMobileServiceSameRechargeNumberDifferentOperator() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        List<Recents> recents = objectMapper.readValue(" [{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"airtel\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1149,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {

        });
        recents.get(1).setDueDate(new Date());

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(1149L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals("1149",
                favResponse.get(0).getPid());
    }

    @Test
    public void testRecentIfInactivePIDPresent() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2223,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        recents.get(1).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        recents.get(2).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));


        Product product = new Product();
        product.setProductId(222L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(2223L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setStatus(0);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());

    }

    @Test
    public void testMultiplePlanBucketOneInRange() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(10)));
        recents.get(1).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        recents.get(2).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(10)));

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(15)));
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(1).setTxnAmount(955.0);
        recents.get(1).setDueAmount(1006.0);
        recents.get(1).setAutomaticAmount(944.0);

        Product product = new Product();
        product.setProductId(222L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);

        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals((Double) 1006.0,
                favResponse.get(0).getAmount());
    }


    @Test
    public void testMultiplePlanBucketBothInRange() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        recents.get(1).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2).minusHours(2)));
        recents.get(2).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(1).setDueAmount(1005.0);
        recents.get(0).setAutomaticAmount(500.0);
        recents.get(1).setAutomaticAmount(450.0);

        Product product = new Product();
        product.setProductId(222L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals((Double) 1005.0,
                favResponse.get(0).getAmount());
    }


    @Test
    public void testMultiplePlanBucketInRangeOrderIdOnlyInOne() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        recents.get(1).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2).minusHours(2)));
        recents.get(2).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        recents.get(1).setProductId(null);
        recents.get(2).setProductId(null);
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(1).setDueAmount(1005.0);
        recents.get(1).setAutomaticAmount(670.0);

        Product product = new Product();
        product.setProductId(222L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals((Double) 1005.0,
                favResponse.get(0).getAmount());
    }


    @Test
    public void testMultiplePlanBucketOutsideRange() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":222,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(10)));
        recents.get(1).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(10)));
        recents.get(2).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(10)));

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(15)));
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusMinutes(5)));
        recents.get(1).setTxnAmount(955.0);

        Product product = new Product();
        product.setProductId(222L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
    }

    @Test
    public void testIfSkippingRecentBasedOnCategory() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());

    }

    @Test
    public void testIfRecentDataNotEmpty() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals("My Institute",
                favResponse.get(0).getOperatorRecentData().get("instituteName"));

    }

    @Test
    public void testFilterMCNInRequestCC() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("XXXX XXXX 22212");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"2355 XXXX 22212\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());

    }

    @Test
    public void testUserAgent() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("XXXX XXXX 22212");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.TRUE);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }

    @Test
    public void testCCRecentWeb() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.WEB);
        request.setVersion("2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"2355 XXXX 22212\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }

    @Test
    public void testCCRecentIOSWithLowVersion() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.1");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"2355 XXXX 22212\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }

    @Test
    public void testCCRecentIOSWithHighVersion() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());

    }

    @Test
    public void testCCRecentwhenOrderIdIsNullOnlyReminderTrueEnabledSavedCardTrue() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.enableSavedCardInReco()).thenReturn(Boolean.TRUE);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());

    }

    @Test
    public void testCCRecentwhenOrderIdIsNullOnlyReminderFalseEnableSavedCardTrue() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.FALSE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);

        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }

    @Test
    public void testCCRecentwhenOrderIdIsNullOnlyReminderTrueEnableSavedCardFalse() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.enableSavedCardInReco()).thenReturn(Boolean.FALSE);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }

    @Test
    public void testCCRecentwhenOrderIdIsNullOnlyReminderFalseEnableSavedCardFalse() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.FALSE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }

    @Test
    public void testCCRecentwhenOrderIdIsNullOnlyReminderFalseEnableSavedCardTruePAYTYPEnULL() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.FALSE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);

        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":null,\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":null,\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }

    @Test
    public void testCCRecentwhenOrderIdIsNullOnlyReminderNullEnableSavedCardTrue() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(null);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);

        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":null,\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":true,\"payType\":null,\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }


    @Test
    public void testNullReturnFromDropOffAndRecent() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(null);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }


    @Test
    public void testDropOffExcludeFlag() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"**********_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"**********\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"22\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":*************,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }


    @Test
    public void testDropOffWhenRecentNotExist() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("10.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");


        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("bses");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"**********_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"**********\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":*************,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());

    }

    @Test
    public void testDTHRecent() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());

    }



    @Test
    public void testOperatorDataAndRecentData() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setPayType(Constants.CREDIT_CARD_PAYTYPE);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertTrue(favResponse.get(0).getOperatorRecentData().containsKey("instituteName"));
        assertTrue(!favResponse.get(0).getOperatorData().containsKey("instituteName"));
        assertTrue(favResponse.get(0).getOperatorRecentData().containsKey(Constants.CREDIT_CARD.CREDIT_CARD_ID));
        assertTrue(favResponse.get(0).getOperatorData().containsKey(Constants.CREDIT_CARD.CREDIT_CARD_ID));


    }


    @Test
    public void testDTHRecentData() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"displayValues\": [{ \"Mobile No.\":\"9711xxxxxx\" ,\"Subscriber ID\" : \"123450000\", \"customCode\":\"custom2\" }] }");

        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertTrue(favResponse.get(0).getAdditionalInfo().containsKey("mobile_number"));
        assertTrue(favResponse.get(0).getAdditionalInfo().containsKey("subscription_id"));
        assertTrue(favResponse.get(0).getAdditionalInfo().containsKey("customCode"));
        assertTrue(ObjectUtils.isEmpty(favResponse.get(0).getOperatorRecentData().get("displayValues")));


    }



    @Test
    public void testDTHRecentDataWithoutCustomCode() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"displayValues\": [{ \"Mobile No.\":\"9711xxxxxx\" ,\"Subscriber ID\" : \"123450000\", \"customCode1\":\"custom2\" }] }");

        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertTrue(favResponse.get(0).getAdditionalInfo().containsKey("mobile_number"));
        assertTrue(favResponse.get(0).getAdditionalInfo().containsKey("subscription_id"));
        assertTrue(!ObjectUtils.isEmpty(favResponse.get(0).getOperatorRecentData().get("displayValues")));


    }

    @Test
    public void testSortingOfRecents() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952271112\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(2).setChannelId("channel 3");
        recents.get(3).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(15)));
        recents.get(3).setChannelId("channel 4");
        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals("channel 1",
                favResponse.get(0).getChannel());
        assertEquals("channel 3",
                favResponse.get(1).getChannel());

    }


    @Test
    public void testDropOffWhenRecentNotExistOlderVersion() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");


        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("bses");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"**********_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"**********\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":*************,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }

    @Test
    public void testDTHWithServiceLevelBillVisiblity() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });


        when(serviceConfig.getBillVisiblityStartDaysByService("dth")).thenReturn(12);


        recents.get(0).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(10)));
        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals(BillState.WILL_EXPIRE,
                favResponse.get(0).getBillState());


    }


    @Test
    public void testDTHWithoutServiceLevelBillVisiblity() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });




        recents.get(0).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(10)));
        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals(BillState.NO_DUE,
                favResponse.get(0).getBillState());


    }
    @Test
    public void testGetFilteredRecentsDueDateIsOlder() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        recentsList.add(dueRecent());
        recentsList.add(txnRecent());
        System.out.println("recents "+recentsList);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2022-08-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getTxnTime());

    }
    @Test
    public void setOperatorValidatedAt_updatesWithNewerDate() {
        Map<String, Recents> uniqueMap = new HashMap<>();
        RecentsPrimaryKey primaryKey=new RecentsPrimaryKey();
        primaryKey.setService(Service.MOBILE.name());
        String key = "testKey";
        Recents recent = new Recents();
        Recents existingRecent = new Recents();
        OperatorValidationDto existingDto = new OperatorValidationDto();
        existingDto.setDate(new Date(System.currentTimeMillis() - 1000));
        existingRecent.setKey(primaryKey);
        existingRecent.setOperatorValidationDto(existingDto);
        uniqueMap.put(key, existingRecent);

        OperatorValidationDto newDto = new OperatorValidationDto();
        newDto.setDate(new Date());
        recent.setKey(primaryKey);
        recent.setTxnTime(newDto.getDate());
        recent.setOperatorValidationDto(newDto);
        existingRecent.setKey(primaryKey);

        frequentOrderService.setOperatorValidatedAt(uniqueMap, key, recent);

        assertEquals(newDto.getDate(), recent.getOperatorValidationDto().getDate());
    }

    @Test
    public void setOperatorValidatedAt_keepsExistingWithNewerDate() {
        Map<String, Recents> uniqueMap = new HashMap<>();
        RecentsPrimaryKey primaryKey=new RecentsPrimaryKey();
        primaryKey.setService(Service.MOBILE.name());
        String key = "testKey";
        Recents recent = new Recents();
        recent.setKey(primaryKey);
        Recents existingRecent = new Recents();
        existingRecent.setKey(primaryKey);
        OperatorValidationDto existingDto = new OperatorValidationDto();
        existingDto.setDate(new Date());
        existingRecent.setOperatorValidationDto(existingDto);
        existingRecent.setTxnTime(existingDto.getDate());
        uniqueMap.put(key, existingRecent);

        OperatorValidationDto newDto = new OperatorValidationDto();
        newDto.setDate(new Date(System.currentTimeMillis() - 1000));
        recent.setOperatorValidationDto(newDto);
        recent.setTxnTime(newDto.getDate());
        frequentOrderService.setOperatorValidatedAt(uniqueMap, key, recent);
        assertEquals(existingDto.getDate(), recent.getOperatorValidationDto().getDate());
    }

    @Test
    public void setOperatorValidatedAt_setsNewWhenExistingDateIsNull() {
        Map<String, Recents> uniqueMap = new HashMap<>();
        String key = "testKey";
        RecentsPrimaryKey primaryKey=new RecentsPrimaryKey();
        primaryKey.setService(Service.MOBILE.name());
        Recents recent = new Recents();
        recent.setKey(primaryKey);
        Recents existingRecent = new Recents();
        existingRecent.setKey(primaryKey);
        OperatorValidationDto existingDto = new OperatorValidationDto();
        existingRecent.setOperatorValidationDto(existingDto);
        uniqueMap.put(key, existingRecent);

        OperatorValidationDto newDto = new OperatorValidationDto();
        newDto.setDate(new Date());
        recent.setTxnTime(newDto.getDate());

        frequentOrderService.setOperatorValidatedAt(uniqueMap, key, recent);

        assertEquals(newDto.getDate(), recent.getOperatorValidationDto().getDate());
    }

    @Test
    public void setOperatorValidatedAt_setsNewWhenNoExistingRecent() {
        Map<String, Recents> uniqueMap = new HashMap<>();
        String key = "testKey";
        Recents recent = new Recents();

        OperatorValidationDto newDto = new OperatorValidationDto();
        newDto.setDate(new Date());
        recent.setOperatorValidationDto(newDto);
        RecentsPrimaryKey primaryKey=new RecentsPrimaryKey();
        primaryKey.setService(Service.MOBILE.name());
        recent.setKey(primaryKey);
        recent.setTxnTime(newDto.getDate());

        frequentOrderService.setOperatorValidatedAt(uniqueMap, key, recent);

        assertEquals(newDto.getDate(), recent.getOperatorValidationDto().getDate());
    }
    @Test
    public void setDateAndSource_setsDateAndSourceForSmsParsingSource() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(UPDATED_SOURCE_KEY, "SMS_PARSING_DWH_MANUAL");
        jsonObject.put(SMS_DATE_TIME_KEY, "2023-10-01 10:00:00");
        OperatorValidationDto operatorValidationResponse = new OperatorValidationDto();

        setDateAndSource(jsonObject, operatorValidationResponse);

        assertEquals(FrequentOrderServiceImpl.formatDate("2023-10-01 10:00:00"), operatorValidationResponse.getDate());
        assertEquals(SMS_DATE_TIME_KEY, operatorValidationResponse.getSource());
    }

    @Test
    public void setDateAndSource_setsDateAndSourceForTransactionSource() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(UPDATED_SOURCE_KEY, TRANSACTION_SOURCE_KEY);
        jsonObject.put(PAYMENT_DATE_TIME_KEY, "2023-10-01 10:00:00");
        OperatorValidationDto operatorValidationResponse = new OperatorValidationDto();

        setDateAndSource(jsonObject, operatorValidationResponse);

        assertEquals(FrequentOrderServiceImpl.formatDate("2023-10-01 10:00:00"), operatorValidationResponse.getDate());
        assertEquals(PAYMENT_DATE_TIME_KEY, operatorValidationResponse.getSource());
    }

    @Test
    public void setDateAndSource_setsDateAndSourceForValidationSyncSource() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(UPDATED_SOURCE_KEY, VALIDATION_SYNC_SOURCE_KEY);
        jsonObject.put(BILL_FETCH_DATE_TIME_KEY, "2023-10-01 10:00:00");
        OperatorValidationDto operatorValidationResponse = new OperatorValidationDto();

        setDateAndSource(jsonObject, operatorValidationResponse);

        assertEquals(FrequentOrderServiceImpl.formatDate("2023-10-01 10:00:00"), operatorValidationResponse.getDate());
        assertEquals(BILL_FETCH_DATE_TIME_KEY, operatorValidationResponse.getSource());
    }

    @Test
    public void setDateAndSource_doesNotSetDateWhenKeyIsMissing() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(UPDATED_SOURCE_KEY, "SMS_PARSING_DWH_MANUAL");
        OperatorValidationDto operatorValidationResponse = new OperatorValidationDto();

        setDateAndSource(jsonObject, operatorValidationResponse);

        assertNull(operatorValidationResponse.getDate());
        assertNull(operatorValidationResponse.getSource());
    }

    @Test
    public void setDateAndSource_doesNotSetDateWhenValueIsNull() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(UPDATED_SOURCE_KEY, "SMS_PARSING_DWH_MANUAL");
        jsonObject.put(SMS_DATE_TIME_KEY, JSONObject.NULL);
        OperatorValidationDto operatorValidationResponse = new OperatorValidationDto();

        setDateAndSource(jsonObject, operatorValidationResponse);

        assertNull(operatorValidationResponse.getDate());
        assertNull(operatorValidationResponse.getSource());
    }

    @Test
    public void operatorValidatedAt_returnsEmptyDtoWhenServiceIsNotMobile() {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService("non-mobile");

        OperatorValidationDto result = frequentOrderService.operatorValidatedAt(recents);

        assertNotNull(result);
        assertNull(result.getDate());
        assertNull(result.getSource());
    }

    @Test
    public void operatorValidatedAt_setsDateAndSourceFromExtraJson() {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService("mobile");
        recents.setExtra("{\"updated_data_source\":\"SMS_PARSING_DWH_REALTIME\",\"sms_date_time\":\"2023-10-01 10:00:00\"}");

        OperatorValidationDto result = frequentOrderService.operatorValidatedAt(recents);

        assertNotNull(result.getDate());
        assertEquals("sms_date_time", result.getSource());
    }

    @Test
    public void operatorValidatedAt_setsMaxDateWhenDateIsNull() {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService("mobile");
        recents.setExtra("{\"sms_date_time\":\"2023-10-01 10:00:00\"}");

        OperatorValidationDto result = frequentOrderService.operatorValidatedAt(recents);

        assertNotNull(result.getDate());
        assertEquals("MAX_DATE", result.getSource());
    }

    @Test
    public void operatorValidatedAt_setsTxnTimeWhenDateIsNull() {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService("mobile");
        recents.setTxnTime(new Date());

        OperatorValidationDto result = frequentOrderService.operatorValidatedAt(recents);

        assertNotNull(result.getDate());
        assertEquals("TXN_TIME", result.getSource());
    }

    @Test
    public void operatorValidatedAt_logsErrorWhenExceptionOccurs() {
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService("mobile");
        recents.setExtra("{invalid_json}");

        OperatorValidationDto result = frequentOrderService.operatorValidatedAt(recents);

        assertNotNull(result);
    }

    @Test
    public void testGetFilteredRecentsDueDateIsEligible() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = dueRecent();
        recents.setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        recentsList.add(recents);
        recentsList.add(txnRecent());
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(null,
                uniqueMap.get("**********_dth_airteltv").getTxnTime());

    }
    @Test
    public void testGetFilteredRecentsClosestDueDate() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Date d1 = DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(1));
        Recents recents = dueRecent();
        recents.setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        Recents recents1 = dueRecent();
        recents1.setDueDate(d1);
        recentsList.add(recents);
        recentsList.add(recents1);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(d1,uniqueMap.get("**********_dth_airteltv").getDueDate());


    }
    @Test
    public void testGetFilteredRecentsMarkedAsPaid() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = dueRecent();
        recents.setMarkAsPaidTime(DateUtil.stringToDate("2022-12-09 18:30:00", DateFormats.DATE_TIME_FORMAT_2));
        recents.setIsMarkAsPaid(Boolean.TRUE);
        recentsList.add(recents);
        recentsList.add(txnRecent());
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2022-12-09 18:30:00", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getMarkAsPaidTime());

    }
    @Test
    public void testGetFilteredRecentsDueBillPreferenceOverMarkedAsPaid() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Date d1 = DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(1));
        Recents recents = dueRecent();
        recents.setDueDate(d1);
        Recents recents1 = dueRecent();
        recents1.setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(0)));
        recents1.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)));
        recents1.setIsMarkAsPaid(Boolean.TRUE);
        recentsList.add(recents);
        recentsList.add(recents1);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(d1,uniqueMap.get("**********_dth_airteltv").getDueDate());


    }
    @Test
    public void testGetFilteredRecentsMarkedAsPaidPreferenceOverTxnTime() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Date d1 = DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(0));
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(1)));
        recents.setMarkAsPaidTime(d1);
        recents.setTxnTime(DateUtil.stringToDate("2023-03-10 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents.setIsMarkAsPaid(Boolean.TRUE);
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(d1,
                uniqueMap.get("**********_dth_airteltv").getMarkAsPaidTime());

    }
    @Test
    public void testGetFilteredRecentsTxnTimePreferenceOverMarkedAsPaid() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(-3)));
        recents.setMarkAsPaidTime(DateUtil.stringToDate("2023-03-12 14:21:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-03-10 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents.setIsMarkAsPaid(Boolean.TRUE);
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);
        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getTxnTime());

    }
    @Test
    public void testGetFilteredRecentsForSameDataTxnTimePreferenceOverMarkedAsPaid() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(-3)));
        recents.setMarkAsPaidTime(DateUtil.stringToDate("2023-03-12 14:21:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-03-18 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents.setIsMarkAsPaid(Boolean.TRUE);
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);
        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2023-03-18 01:46:51", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getTxnTime());

    }
    @Test
    public void testGetFilteredRecentsLatestMarkAsPaid() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.stringToDate("2023-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setMarkAsPaidTime(DateUtil.stringToDate("2023-03-26 14:21:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-03-10 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents.setIsMarkAsPaid(Boolean.TRUE);
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents1.setMarkAsPaidTime(DateUtil.stringToDate("2023-03-27 14:21:39", DateFormats.DATE_TIME_FORMAT_2));
        recents1.setIsMarkAsPaid(Boolean.TRUE);
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2023-03-27 14:21:39", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getMarkAsPaidTime());

    }
    @Test
    public void testGetFilteredRecentsLatestTxnPreference() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.stringToDate("2023-03-14 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-03-18 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents1.setDueDate(DateUtil.stringToDate("2023-03-12 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2023-03-18 01:46:51", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getTxnTime());

    }
    @Test
    public void testGetFilteredRecentsTxnTimeNullInBoth() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.stringToDate("2022-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(null);
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(null);
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2022-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getDueDate());

    }
    @Test
    public void testGetFilteredRecentsTxnTimeNullForFirst() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.stringToDate("2022-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(null);
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getTxnTime());

    }
    @Test
    public void testGetFilteredRecentsTxnTimeNullForSecond() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.stringToDate("2022-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(null);
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getTxnTime());

    }

    @Test
    public void testSequencingOfFinalResponse() {
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.stringToDate("2022-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        recents.setLastPendingTxn("{\"txn_id\":100075978642,\"txn_time\":\"2023-06-16T16:50:42.545Z\",\"txn_amount\":750.0,\"txn_status\":\"2\",\"channel_id\":\"web\"}");
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(null);
        recents1.setDueDate(null);
        recents.setLastFailureTxn("{\"txn_id\":100075978642,\"txn_time\":\"2023-06-18T16:50:42.545Z\",\"txn_amount\":750.0,\"txn_status\":\"2\",\"channel_id\":\"web\"}");
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2),
                uniqueMap.get("**********_dth_airteltv").getTxnTime());

    }

    @Test
    public void testPrepaidAutomaticDueAmount() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Date d0 = DateUtil.convertToDateViaInstant(LocalDateTime.now().toLocalDate().atTime(23,00,00));
        Date d1 = DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(1));
        Date d2 = DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2));
        Recents recents0 = automaticRecent();
        recents0.setDueDate(d0);
        recents0.setAutomaticStatus(1);
        recents0.setAutomaticDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().toLocalDate().atTime(23,59,59)));
        recents0.setAutomaticAmount(100.0);
        recentsList.add(recents0);
        Recents recents1 = automaticRecent();
        recents1.setDueDate(d1);
        recents1.setAutomaticStatus(1);
        recents1.setAutomaticDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().toLocalDate().atTime(23,59,59)));
        recents1.setAutomaticAmount(101.0);
        recentsList.add(recents1);

        Recents recents2 = automaticRecent();
        recents2.setDueDate(d2);
        recents2.setAutomaticStatus(1);
        recents2.setAutomaticDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().toLocalDate().atTime(23,59,59)));
        recents2.setAutomaticAmount(102.0);
        recentsList.add(recents2);

        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("mobile");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");
        Product product = new Product();
        product.setProductId(972L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        double expectedAmount = 100.0;
        for(Recents recents : recentsList){
            Double actualAmount = RecentUtils.getFrequentOrderResponseAmount(recents);
            //assertEquals(expectedAmount, actualAmount,0.0);
            expectedAmount+=1.0;
        }
    }

    @Test
    //WHen automatic amount is not set, it should return existing due amount
    public void shouldReturnNullWhenDueAmountIsNull() {
        Recents recents = automaticRecent();
        Double result = RecentUtils.getFrequentOrderResponseAmount(recents);
        assertEquals(20.0, result,0.0);
    }

    private Recents dueRecent() {
        String str = "{\"customerid\":1434239357,\"service\":\"dth\",\"recharge_number\":\"**********\",\"operator\":\"airteltv\",\"plan_bucket\":\"\",\"automatic_date\":null,\"automatic_status\":0,\"bill_date\":null,\"bill_update_time\":\"2022-10-11 05:04:37.866Z\",\"channel_id\":null,\"cin\":null,\"circle\":\"\",\"consumername\":null,\"consumername_v2\":null,\"cylinder_agency_name\":null,\"cylinder_agency_name_v2\":null,\"dismiss_action_time\":null,\"due_amount\":240.0,\"due_date\":\"2022-10-09 18:30:00.000Z\",\"is_mark_as_paid\":false,\"is_tokenized_transaction\":null,\"mark_as_paid_amount\":null,\"mark_as_paid_time\":null,\"mcn\":null,\"min_due_amount\":null,\"nick_name\":null,\"nick_name_v2\":null,\"not_paid_on_paytm\":null,\"notification_status\":null,\"order_id\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"par\":null,\"paytype\":\"prepaid\",\"plan_name\":null,\"product_id\":972,\"recentdata\":null,\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"reminder_notification_enable\":null,\"tin\":null,\"txn_amount\":null,\"txn_status\":null,\"txn_time\":null,\"updated_at\":\"2022-10-11 05:04:37.866Z\"}";
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recents.setKey(recentsPrimaryKey);
        recents.getKey().setService("dth");
        recents.getKey().setCustomerId(1434239357L);
        recents.getKey().setOperator("airteltv");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setBillUpdateTime(DateUtil.stringToDate("2022-10-11 05:04:37", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueDate(DateUtil.stringToDate("2022-10-09 18:30:00", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueAmount(20.0);
        recents.setProductId(972L);

        return recents;
    }
    private Recents txnRecent(){
        String str = "{\"customerid\":1434239357,\"service\":\"dth\",\"recharge_number\":\"**********\",\"operator\":\"airteltv\",\"plan_bucket\":\"240\",\"automatic_date\":null,\"automatic_status\":null,\"bill_date\":null,\"bill_update_time\":\"2022-08-09 07:34:39.000Z\",\"channel_id\":null,\"cin\":null,\"circle\":\"\",\"consumername\":null,\"consumername_v2\":null,\"cylinder_agency_name\":null,\"cylinder_agency_name_v2\":null,\"dismiss_action_time\":null,\"due_amount\":240.0,\"due_date\":\"2022-09-05 18:29:59.000Z\",\"is_mark_as_paid\":null,\"is_tokenized_transaction\":null,\"mark_as_paid_amount\":null,\"mark_as_paid_time\":null,\"mcn\":null,\"min_due_amount\":null,\"nick_name\":null,\"nick_name_v2\":null,\"not_paid_on_paytm\":null,\"notification_status\":null,\"order_id\":18806731390,\"original_due_amount\":null,\"original_min_due_amount\":null,\"par\":null,\"paytype\":\"prepaid\",\"plan_name\":null,\"product_id\":972,\"recentdata\":null,\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"reminder_notification_enable\":null,\"tin\":null,\"txn_amount\":240.0,\"txn_status\":\"00\",\"txn_time\":\"2022-08-09 07:34:39.000Z\",\"updated_at\":\"2022-08-09 07:34:39.000Z\"}";

        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recents.setKey(recentsPrimaryKey);
        recents.getKey().setService("dth");
        recents.getKey().setCustomerId(1434239357L);
        recents.getKey().setOperator("airteltv");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("20");
        recents.setPayType("prepaid");
        recents.setBillUpdateTime(DateUtil.stringToDate("2022-10-11 05:04:37", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueDate(DateUtil.stringToDate("2022-10-09 18:30:00", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueAmount(20.0);
        recents.setTxnAmount(200.0);
        recents.setTxnTime(DateUtil.stringToDate("2022-08-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setOrderId(18806731390l);
        recents.setUpdatedAt(DateUtil.stringToDate("2022-08-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setProductId(972L);
        return recents;
    }

    private Recents automaticRecent(){
        String str = "{\"mcn\": null,\"isTokenizedTransaction\": null,\"billDate\": *************,\"dueDate\": *************,\"dueAmount\": 10,\"minDueAmount\": null,\"originalDueAmount\": null,\"originalMinDueAmount\": null,\"nickName\": null,\"orderId\": 5555,\"productId\": 222,\"txnAmount\": null,\"txnTime\": null,\"txnStatus\": null,\"automaticDate\": *************,\"automaticStatus\": 1,\"billUpdateTime\": null,\"dismissActionTime\": null,\"isMarkAsPaid\": null,\"markAsPaidTime\": null,\"cin\": null,\"par\": null,\"tin\": null,\"updatedAt\": *************,\"payType\": \"prepaid\",\"key\": {    \"customerId\": **********,    \"service\": \"mobile\",    \"rechargeNumber\": \"**********\",    \"operator\": \"jio\",    \"planBucket\": \"Special Recharge\"},\"recharge_number_2\": null,\"recharge_number_3\": null,\"recharge_number_4\": null,\"recharge_number_5\": null,\"recharge_number_6\": null,\"recharge_number_7\": null,\"recharge_number_8\": null  }";

        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recents.setKey(recentsPrimaryKey);
        recents.getKey().setService("mobile");
        recents.getKey().setCustomerId(1434239357L);
        recents.getKey().setOperator("airtel");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("20");
        recents.setPayType("prepaid");
        recents.setBillUpdateTime(DateUtil.stringToDate("2022-10-11 05:04:37", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueDate(DateUtil.stringToDate("2022-10-09 18:30:00", DateFormats.DATE_TIME_FORMAT_2));
        recents.setDueAmount(20.0);
        recents.setTxnAmount(200.0);
        recents.setTxnTime(DateUtil.stringToDate("2022-08-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setOrderId(18806731390l);
        recents.setUpdatedAt(DateUtil.stringToDate("2022-08-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setProductId(972L);
        return recents;
    }

    @Test
    public void testGetFilteredRecentsTxnTimeNullForSecondTuitionFee() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.TUITION_FEES);
        recents.getKey().setCustomerId(1L);
        recents.getKey().setRechargeNumber("123");
        recents.setRentTFData("{\"bankName\":\"My Bank\"}");
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.stringToDate("2022-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(null);
        recents1.setKey(new RecentsPrimaryKey());
        recents1.getKey().setService(Constants.TUITION_FEES);
        recents1.getKey().setCustomerId(1L);
        recents1.getKey().setRechargeNumber("123");
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(uniqueMap.containsKey("123_tuition fees"), true);
        assertEquals(recents1.getRentTFData(), recents.getRentTFData());
    }

    @Test
    public void testGetFilteredRecentsTxnTimeNullForSecondTuitionFeeCase2() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = txnRecent();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.TUITION_FEES);
        recents.getKey().setCustomerId(1L);
        recents.getKey().setRechargeNumber("123");
        recents.setOrderId(20541429211l);
        recents.setDueDate(DateUtil.stringToDate("2022-03-30 01:46:55", DateFormats.DATE_TIME_FORMAT_2));
        recents.setTxnTime(DateUtil.stringToDate("2023-03-16 01:46:51", DateFormats.DATE_TIME_FORMAT_2));
        Recents recents1 = txnRecent();
        recents1.setOrderId(20612665032l);
        recents1.setTxnTime(null);
        recents1.setRentTFData("{\"bankName\":\"My Bank\"}");
        recents1.setKey(new RecentsPrimaryKey());
        recents1.getKey().setService(Constants.TUITION_FEES);
        recents1.getKey().setCustomerId(1L);
        recents1.getKey().setRechargeNumber("123");
        recents1.setDueDate(null);
        recentsList.add(recents1);
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("dth");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");

        Map<String, Recents> txnStatus = new HashMap<>();

        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        featureMap.put("smartReminderPrepaidEndDays",3);
        featureMap.put("billEndRangeFromCurrentDate",30);
        featureMap.put("smartReminderPostpaidEndDays",5);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("dth");
        product.setOperator("airteltv");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList,config,frequentOrderRequest,txnStatus,txnStatusForNonRu);
        assertEquals(uniqueMap.containsKey("123_tuition fees"), true);
        assertEquals(recents1.getRentTFData(), recents.getRentTFData());
    }

    @Test
    public void testMarkAsPaidRecents() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");
        LocalDateTime date = LocalDateTime.now();
        recents.get(0).setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        recents.get(0).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(0).setDueAmount(20.0);
        recents.get(0).setMarkAsPaidAmount(20.0);
        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals(recents.get(0).getMarkAsPaidAmount(),favResponse.get(0).getAmount());


    }
    @Test
    public void testNewAccountCreatedFalseWhenOrderIdNullAndIsValidationFalseAndCreatedAtlessthanAccountCreation() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.disableDropOffService()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("credit card");
        List<Recents> recents = objectMapper.readValue("[{\"createdAt\":************* ,\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":null,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"financial services\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isValidation\":false}]", new TypeReference<List<Recents>>() {
        });

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("credit card");
        product.setOperator("neft_hdfc");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,favResponse.size());
    }

    @Test
    public void testNewAccountCreatedWhenOrderIdNullAndIsValidationFalse() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.disableDropOffService()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("mobile");
        List<Recents> recents = objectMapper.readValue("[{\"createdAt\":*************,\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"isSavedCard\":null,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isValidation\":false}]", new TypeReference<List<Recents>>() {
        });

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,favResponse.size());
    }
    @Test
    public void testNewAccountCreatedWhenOrderIdNullAndIsValidationTrue() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.disableDropOffService()).thenReturn(false);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("mobile");
        when(serviceConfig.newAccountServices()).thenReturn(newAccountServices);
        List<Recents> recents = objectMapper.readValue("[{\"createdAt\":*************,\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isValidation\":true}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.NEW_ACCOUNT,favResponse.get(0).getEventState());
    }
    @Test
    public void testNewAccountCreatedWhenOrderIdNullAndIsValidationTrueWithEmptyAmount() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.disableDropOffService()).thenReturn(false);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("mobile");
        when(serviceConfig.newAccountServices()).thenReturn(newAccountServices);
        List<Recents> recents = objectMapper.readValue("[{\"createdAt\":*************,\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":0,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isValidation\":true}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.NEW_ACCOUNT_BROWSE_PLAN,favResponse.get(0).getEventState());
    }
    @Test
    public void testNewAccountCreatedWhenOrderIdNonNullAndIsValidationTrue() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isValidation\":true}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_SUCCESS,favResponse.get(0).getEventState());
    }
    @Test
    public void testNewAccountCreatedWhenOrderIdNonNullAndIsValidationFalse() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isValidation\":false}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_SUCCESS,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargeFailureRecordWithNoBillDue() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-16T16:50:42.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargeFailureRecordWithNoNullDueDate() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-16T16:50:42.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargeFailureRecordAutomatic() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 16 16:50:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });

        JSONObject jsonObject = new JSONObject(recents.get(0).getLastFailureTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        jsonObject.put(Constants.CHANNEL_ID, Constants.ReminderConstants.AUTOMATIC_CHANNEL);
        recents.get(0).setLastFailureTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_AUTOMATIC_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargeFailureRecordDiffChannel() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 16 16:50:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });

        JSONObject jsonObject = new JSONObject(recents.get(0).getLastFailureTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        jsonObject.put(Constants.CHANNEL_ID, "abc");
        recents.get(0).setLastFailureTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargeFailureRecordWithNewBillUpdatedAfterFailureTxn() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-16T16:50:42.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"abc\\\"}\",\"newBillUpdatedAt\":*************}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setNewBillUpdatedAt(new Date());
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargeFailureRecordWithNewBillUpdatedBeforeFailureTxn() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 20 16:50:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\",\"newBillUpdatedAt\":1686767400000}]", new TypeReference<List<Recents>>() {
        });

        JSONObject jsonObject = new JSONObject(recents.get(0).getLastFailureTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        recents.get(0).setLastFailureTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingRecordWithNoBillDue() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\"}]", new TypeReference<List<Recents>>() {
        });

        JSONObject jsonObject = new JSONObject(recents.get(0).getLastPendingTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        recents.get(0).setLastPendingTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_PENDING,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingRecordWithNoNullDueDate() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        JSONObject jsonObject = new JSONObject(recents.get(0).getLastPendingTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        recents.get(0).setLastPendingTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_PENDING,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingRecordAutomatic() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        JSONObject jsonObject = new JSONObject(recents.get(0).getLastPendingTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        jsonObject.put(Constants.CHANNEL_ID, Constants.ReminderConstants.AUTOMATIC_CHANNEL);
        recents.get(0).setLastPendingTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_AUTOMATIC_PENDING,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingRecordDiffChannel() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        JSONObject jsonObject = new JSONObject(recents.get(0).getLastPendingTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        jsonObject.put(Constants.CHANNEL_ID, "abc");
        recents.get(0).setLastPendingTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_PENDING,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingRecordWithNewBillUpdatedAfterPendingTxn() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-12T23:12:42.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\",\"newBillUpdatedAt\":*************}]", new TypeReference<List<Recents>>() {
        });

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_SUCCESS,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingRecordWithNewBillUpdatedBeforePendingTxn() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\",\"newBillUpdatedAt\":1688767400000}]", new TypeReference<List<Recents>>() {
        });
        JSONObject jsonObject = new JSONObject(recents.get(0).getLastPendingTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        recents.get(0).setLastPendingTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_PENDING,favResponse.get(0).getEventState());
    }

    @Test
    public void testRechargePendingRecordWithNewUserWithoutAmount() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\",\"newBillUpdatedAt\":1688767400000}]", new TypeReference<List<Recents>>() {
        });
        JSONObject jsonObject = new JSONObject(recents.get(0).getLastPendingTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        recents.get(0).setLastPendingTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_PENDING,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingRecordWhenTxnTimeOlderThanThreshold() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2022-06-20T03:55:46.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\",\"newBillUpdatedAt\":1686767400000}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_SUCCESS,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingAndFailureRecordWithLatestFailure() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":*************,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-21T23:12:42.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\",\"lastFailureTxn\":\"{\\\"txn_id\\\":78970868608,\\\"txn_time\\\":\\\"2023-06-21T23:12:42.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingAndFailureRecordWithLatestPending() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":*************,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\",\"lastFailureTxn\":\"{\\\"txn_id\\\":78970868608,\\\"txn_time\\\":\\\"Wed Jun 20 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\"}]", new TypeReference<List<Recents>>() {
        });

        JSONObject jsonObject = new JSONObject(recents.get(0).getLastPendingTxn());
        jsonObject.put("txn_time", formatDate(new Date(),Constants.DATE_FORMAT));
        recents.get(0).setLastPendingTxn(jsonObject.toString());

        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_PENDING,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingAndFailureRecordWithLatestTxnSuccess() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":1687408184345,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\",\"lastFailureTxn\":\"{\\\"txn_id\\\":78970868608,\\\"txn_time\\\":\\\"Wed Jun 21 23:12:42 IST 2023\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_SUCCESS,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargePendingAndFailureAndSuccesSameTxnTime() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":1687408184000,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-22T09:59:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\",\"lastFailureTxn\":\"{\\\"txn_id\\\":78970868608,\\\"txn_time\\\":\\\"2023-06-22T09:59:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testOnlyRechargePendingRecordWhenTxnTimeOlderThanThreshold() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastPendingTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2022-06-20T16:50:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\",\"newBillUpdatedAt\":1686767400000}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,favResponse.size());
    }

    @Test
    public void testNewAccountCreatedWhenFlagIsFalse() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("financial services");
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isValidation\":false}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,favResponse.size());
    }
    @Test
    public void testNewAccountCreatedWhenServiceNotAllowed() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("financial services");
        when(serviceConfig.disableDropOffService()).thenReturn(false);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isValidation\":false}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,favResponse.size());
    }
    @Test
    public void testRechargeFailureRecordBasedOnFlag() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        String config = "{\"recentCategoryLimit\":2,\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"newAccountServices\": [\"financial services\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":45766,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"channel_id\\\":\\\"ANDROID\\\",\\\"txn_time\\\":\\\"2023-06-16T09:59:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testOnlyRechargeFailureRecordBasedOnFlag() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.getWhitelistedCustIdsForMobilePrepaidNonRu()).thenReturn(null);
        when(serviceConfig.disableDropOffService()).thenReturn(false);
        String config = "{\"recentCategoryLimit\":2,\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"newAccountServices\": [\"financial services\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-16T09:59:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,favResponse.size());
    }

    @Test
    public void testOnlyRechargeFailureRecordBasedOnFlagForWhitelistedNonRUForRecentCard() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.disableDropOffService()).thenReturn(false);
        String config = "{\"recentCategoryLimit\":2,\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"newAccountServices\": [\"financial services\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-16T09:59:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,favResponse.size());
    }
    @Test
    public void testOnlyRechargeFailureRecordBasedOnDiffService() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.disableDropOffService()).thenReturn(false);
        when(serviceConfig.getWhitelistedCustIdsForMobilePrepaidNonRu()).thenReturn(null);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("financial services");
        when(serviceConfig.newAccountServices()).thenReturn(newAccountServices);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-16T09:59:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,favResponse.size());
    }
    @Test
    public void testOnlyRechargeFailureRecordBasedOnSameService() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.disableDropOffService()).thenReturn(false);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("mobile");
        when(serviceConfig.newAccountServices()).thenReturn(newAccountServices);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"txn_time\\\":\\\"2023-06-16T09:59:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\",\\\"channel_id\\\":\\\"web\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testRechargeFailureRecordBasedOnSameService() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);

        String config = "{\"recentCategoryLimit\":2,\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"newAccountServices\": [\"financial services\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":null,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":65975,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"isValidation\",\"isTransaction\":true,\"lastFailureTxn\":\"{\\\"txn_id\\\":100075978642,\\\"channel_id\\\":\\\"ANDROID\\\",\\\"txn_time\\\":\\\"2023-06-16T09:59:44.545Z\\\",\\\"txn_amount\\\":750.0,\\\"txn_status\\\":\\\"2\\\"}\"}]", new TypeReference<List<Recents>>() {
        });
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(EventState.RECHARGE_FAILURE,favResponse.get(0).getEventState());
    }
    @Test
    public void testDropOffWhenServiceDiff() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("10.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("electricity");
        when(serviceConfig.newAccountServices()).thenReturn(newAccountServices);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");


        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("bses");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"**********_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"**********\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":*************,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());

    }
    @Test
    public void testDropOffWhenServiceSame() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("10.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("mobile");
        when(serviceConfig.newAccountServices()).thenReturn(newAccountServices);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"***********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"12121111122\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");

        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");


        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("bses");
        product.setCategoryId(111L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"**********_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"**********\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":*************,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());

    }
    @Test
    public void testCCBPFavourateResponseWithDueAndNonDuePaidOutSide() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"XXXX XXXX XXXX 1234\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":0,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"2kk2222abc465jh75oj764\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"2kk2222abc465jh75oj764\",\"operator\":\"hdfc\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}," +
                "{\"mcn\":\"XXXX XXXX XXXX 1235\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":0,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"2kk2222abc465jh75oj767\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"2kk2222abc465jh75oj767\",\"operator\":\"hdfc\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"notPaidOnPaytm\":1}" +
                "]", new TypeReference<List<Recents>>() {
        });


        when(serviceConfig.getBillVisiblityStartDaysByService("financial services")).thenReturn(12);


        recents.get(0).setDueDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(10)));
        Product product = new Product();
        product.setProductId(1212L);
        product.setService("financial services");
        product.setOperator("hdfc");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("financial services");
        product.setOperator("hdfc");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals(BillState.WILL_EXPIRE,
                favResponse.get(0).getBillState());
        assertEquals(BillState.NO_DUE,
                favResponse.get(1).getBillState());
        assertFalse(favResponse.get(0).isNotPaidOnPaytm());
        assertTrue(favResponse.get(1).isNotPaidOnPaytm());


    }
    @Test
    public void testSMSCardRecentFinancialService() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setVersion("10.13.1");
        request.setClient("androidapp");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> smsEnabledService = Arrays.asList("financial services");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        Map<String,List<Integer>> categoryMapping = objectMapper.readValue("{\"mobile\":[17,21],\"financial services\":[64739]}", new TypeReference<Map<String,List<Integer>>>() {
        });

        when(serviceConfig.getCategoryMapping()).thenReturn(categoryMapping);

        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"XXXX XXXX 4433\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"XXXX XXXX 4454\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952271112\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setOrderId(null);
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(1).setOrderId(null);
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(2).setChannelId("channel 3");
        recents.get(2).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(2).getKey().setService("financial services");
        recents.get(2).setOrderId(null);
        recents.get(3).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(15)));
        recents.get(3).setChannelId("channel 4");
        recents.get(3).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(3).getKey().setService("financial services");
        recents.get(3).setOrderId(null);
        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals("channel 4",
                favResponse.get(1).getChannel());
    }

    @Test
    public void testSMSEventStateNoBillAmountNoDueDate() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setClient("androidapp");
        request.setVersion("10.13.0");
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> smsEnabledService = Arrays.asList("financial services");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"XXXX XXXX 4433\",\"isTokenizedTransaction\":null,\"billDate\":"+Calendar.getInstance().getTimeInMillis()+",\"dueDate\":null,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).getKey().setService("financial services");

        Product product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1, favResponse.size());
        assertEquals(EventState.SMS_CARD_NO_AMOUNT, favResponse.get(0).getEventState());
        assertEquals(BillState.NO_DATE_NO_AMOUNT, favResponse.get(0).getBillState());
    }

    @Test
    public void testSMSEventStateNoBillAmountPastBillDate() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setClient("androidapp");
        request.setVersion("10.13.0");
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> smsEnabledService = Arrays.asList("financial services");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"XXXX XXXX 4433\",\"isTokenizedTransaction\":null,\"billDate\":"+Calendar.getInstance().getTimeInMillis()+",\"dueDate\":1691018652392,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).getKey().setService("financial services");

        Product product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1, favResponse.size());
        assertEquals(EventState.SMS_CARD_NO_AMOUNT, favResponse.get(0).getEventState());
        assertEquals(BillState.NO_DUE, favResponse.get(0).getBillState());
    }

    @Test
    public void testSMSEventStateZeroBillAmount() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient("androidapp");
        request.setVersion("10.13.0");

        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> smsEnabledService = Arrays.asList("financial services");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);

        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"XXXX XXXX 4433\",\"isTokenizedTransaction\":null,\"billDate\":1690223400098,\"dueDate\":1682505600000,\"dueAmount\":0,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).getKey().setService("financial services");

        Product product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1, favResponse.size());
        assertEquals(EventState.SMS_CARD, favResponse.get(0).getEventState());
        assertEquals(BillState.NO_DUE, favResponse.get(0).getBillState());
    }


    @Test
    public void testSMSEventStateValidBillAmountNullDueDate() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setClient("androidapp");
        request.setVersion("10.13.0");
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> smsEnabledService = Arrays.asList("financial services");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"XXXX XXXX 4433\",\"isTokenizedTransaction\":null,\"billDate\":"+Calendar.getInstance().getTimeInMillis()+",\"dueDate\":null,\"dueAmount\":700,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).getKey().setService("financial services");

        Product product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1, favResponse.size());
        assertEquals(EventState.SMS_CARD, favResponse.get(0).getEventState());
        assertEquals(BillState.NO_DATE, favResponse.get(0).getBillState());
    }

    @Test
    public void testSMSEventStateValidBillAmountNotNullDueDate() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setClient("androidapp");
        request.setVersion("10.13.0");
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> smsEnabledService = Arrays.asList("financial services");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"XXXX XXXX 4433\",\"isTokenizedTransaction\":null,\"billDate\":1690223400098,\"dueDate\":1691018652392,\"dueAmount\":700,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).getKey().setService("financial services");

        Product product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        LocalDate date = LocalDate.of(2023, 10, 1);
        long epochTime = date.atStartOfDay(ZoneOffset.UTC).toInstant().toEpochMilli();
        System.out.println("ADSFASDFFSGF ::: " + epochTime);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1, favResponse.size());
        assertEquals(EventState.SMS_CARD, favResponse.get(0).getEventState());
        assertEquals(BillState.NO_DUE, favResponse.get(0).getBillState());
    }


    @Test
    public void testSMSCardRecentNoServiceEnabled() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        List<String> smsEnabledService = new ArrayList<>();
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952271112\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(2).setChannelId("channel 3");
        recents.get(2).setOrderId(null);
        recents.get(2).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(2).getKey().setService("financial services");
        recents.get(3).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(15)));
        recents.get(3).setChannelId("channel 4");
        recents.get(3).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(3).getKey().setService("financial services");
        recents.get(3).setOrderId(null);
        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());
    }
    @Test
    public void testDedupeSMSDropoff() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("9.10.2");
        List<String> smsEnabledService = Arrays.asList("financial services");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"XXXX XXXX XX43\",\"eventSource\":\"sms\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"XXXX XXXX XX43\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"XXXX XXXX XX33\",\"eventSource\":\"sms\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"credit card\",\"key\":{\"customerId\":**********,\"service\":\"financial services\",\"rechargeNumber\":\"XXXX XXXX XX33\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setRecentData("{\"instituteName\":\"My Institute\"}");
        Product product = new Product();
        product.setProductId(2221L);
        product.setService("financial services");
        product.setOperator("hdfc bank");
        product.setCategoryId(21L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("bses");
        product.setCategoryId(21L);
        product.setStatus(1);
        product.setAttributes("{\"bank_code\":\"ss\",\"card_network\":\"rupay\"}");
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"XXXX XXXX 4443_financial services_hdfc\":{\"recharge_number\":\"XXXX XXXX 4443\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"2221\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":*************,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null},\n" +
                        "\"XXXX XXXX 4433_financial services_hdfc\":{\"recharge_number\":\"XXXX XXXX 4433\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"2221\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":*************,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null},\n" +
                        "\"XXXX XXXX 4443_financial services_hdfc\":{\"recharge_number\":\"XXXX XXXX 4443\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"2221\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":*************,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(frequentOrderService.getRecentDataFromDB(request)).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(dropOffResponseMap);
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals(EventType.RECHARGE, favResponse.get(0).getEventType());
        assertEquals(EventType.RECHARGE, favResponse.get(1).getEventType());
    }

    @Test
    public void testSMSCardRecentFinancialServiceForOlderVersion() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setVersion("10.12.1");
        request.setClient("androidapp");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> smsEnabledService = Arrays.asList("financial services");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        Map<String,List<Integer>> categoryMapping = objectMapper.readValue("{\"mobile\":[17,21],\"financial services\":[64739]}", new TypeReference<Map<String,List<Integer>>>() {
        });

        when(serviceConfig.getCategoryMapping()).thenReturn(categoryMapping);

        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"XXXX XXXX 4433\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"XXXX XXXX 4454\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952271112\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setOrderId(null);
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(2).setChannelId("channel 3");
        recents.get(2).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(2).getKey().setService("financial services");
        recents.get(2).setOrderId(null);
        recents.get(3).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(15)));
        recents.get(3).setChannelId("channel 4");
        recents.get(3).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(3).getKey().setService("financial services");
        recents.get(3).setOrderId(null);
        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());
    }

    @Test
    public void testSmartRecent() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setMethodType(Constants.METHOD_POST);
        request.setServices(new ArrayList<String>() {{
            add("mobile");
        }});
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.getSmartRecentsEnabledServices()).thenReturn(new ArrayList<String>() {{
            add("mobile");
        }});
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952270003\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":22234,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"9952271112\",\"operator\":\"jio\",\"planBucket\":\"Combo Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource("SMART_RECENT_VALIDATION");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(2).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(10)));
        recents.get(2).setChannelId("channel 3");
        recents.get(3).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(15)));
        recents.get(3).setChannelId("channel 4");
        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        SmartRecents smartRecents = new SmartRecents();
        SmartRecentsPrimaryKey key = new SmartRecentsPrimaryKey();
        key.setCustomerId(**********L);
        key.setService("mobile");
        key.setOperator("airtel");
        smartRecents.setKey(key);
        smartRecents.setCircle("delhi");
        smartRecents.setPayType("postpaid");
        smartRecents.setEventSource("SMART_RECENT_VALIDATION");
        smartRecents.setProductId(2535622L);
        smartRecents.setCreatedAt(Date.from(Instant.now()));
        smartRecents.setUpdatedAt(Date.from(Instant.now()));

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(smartRecentsService.getSmartRecentsForFrequentOrderCLP(any())).thenReturn(new ArrayList<SmartRecents>() {{
            add(smartRecents);
        }});

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals("channel 1",
                favResponse.get(0).getChannel());
        assertEquals("channel 3",
                favResponse.get(1).getChannel());

    }

    @Test
    public void testNewAccountCreatedWhenEventSourcePGDeleted() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("financial services");
        when(serviceConfig.disableDropOffService()).thenReturn(true);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"PG_DELETED_AMW\",\"isValidation\":false,\"isSavedCard\":true}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(new Date());
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,favResponse.size());
    }

    @Test
    public void testNewAccountCreatedWhenPGCardIdEqualsRechargeNumber() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(124L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.disableDropOffService()).thenReturn(true);
        List<String> newAccountServices = new ArrayList<>();
        newAccountServices.add("financial services");
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":**********,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"cin\",\"par\":\"par\",\"bbpsRefId\":\"bbpsRefId\",\"tin\":null,\"pgCardId\":\"**********\",\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":124,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"eventSource\":\"ru_sms\",\"isValidation\":false,\"isSavedCard\":true}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(new Date());
        Product product = new Product();
        product.setProductId(**********L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setCategoryId(298616L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,favResponse.size());
    }
    @Test
    public void testSkinAndVariant() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        request.setIsCardSkinRequired(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"cardVariant\":\"Axis Regalia\",\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"cardVariant\":\"Axis Regalia\",\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        LocalDateTime date = LocalDateTime.now();
        recents.get(0).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(0).setDueAmount(20.0);
        recents.get(0).setCardSkin("{\"source\":\"DATABASE_MATCH\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");
        recents.get(1).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(1).setDueAmount(20.0);
        recents.get(1).setCardSkin("{\"source\":\"DATABASE_M\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");


        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals("Axis Regalia",favResponse.get(0).getIssuingBankCardVariant());
        assertEquals("Axis Regalia",favResponse.get(1).getIssuingBankCardVariant());
        assertNotNull(favResponse.get(0).getMediaAssets());

    }

    @Test
    public void testSkinAndVariant1() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        request.setIsCardSkinRequired(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"cardVariant\":\"Axis Regalia\",\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"cardVariant\":\"Axis Regalia\",\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        LocalDateTime date = LocalDateTime.now();
        recents.get(0).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(0).setDueAmount(20.0);
        recents.get(0).setCardSkin("{\"source\":\"DATABASE_MATCH\"}");
        recents.get(1).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(1).setDueAmount(20.0);
        recents.get(1).setCardSkin("{\"source\":\"DATABASE_M\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");


        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals(0,favResponse.get(0).getMediaAssets().size());
        assertEquals(1,favResponse.get(1).getMediaAssets().size());


    }

    @Test
    public void testSkinAndVarianSanitise() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        request.setIsCardSkinRequired(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"cardVariant\":\"Axis generic\",\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"cardVariant\":\"Axis Regalia\",\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        LocalDateTime date = LocalDateTime.now();
        recents.get(0).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(0).setDueAmount(20.0);
        recents.get(0).setCardSkin("{\"source\":\"DATABASE_MATCH\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");
        recents.get(1).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(1).setDueAmount(20.0);
        recents.get(1).setCardSkin("{\"source\":\"DATABASE_M\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");


        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        ObjectMapper objectMapper = new ObjectMapper();


        String config = "{\"generic\":{\"keepCardVariant\":false,\"keepCardSkin\":false},\"debit\":{\"keepCardVariant\":false,\"keepCardSkin\":false},\"paytm sbi card\":{\"keepCardVariant\":true,\"keepCardSkin\":false},\"your bank\":{\"keepCardVariant\":false,\"keepCardSkin\":false}}";
        try {
            Map<String, CardDetails> allowedSkingVariantMap = objectMapper.readValue(config, new TypeReference<Map<String, CardDetails>>() {});
//            Map<String, CardDetails> allowedSkingVariantMap = (Map<String, CardDetails>) JsonUtils.parseJson(config,Map.class);
            FeatureConfigCache.getInstance().setAllowedSkinAndVariant(allowedSkingVariantMap);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertNull(favResponse.get(0).getIssuingBankCardVariant());
        assertEquals("Axis Regalia",favResponse.get(1).getIssuingBankCardVariant());
        assertNull(favResponse.get(0).getMediaAssets());

    }
    @Test
    public void testSkinAndVarianSanitise1() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        request.setIsCardSkinRequired(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"cardVariant\":\"Axis paytm sbi card\",\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"cardVariant\":\"Axis Regalia\",\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        LocalDateTime date = LocalDateTime.now();
        recents.get(0).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(0).setDueAmount(20.0);
        recents.get(0).setCardSkin("{\"source\":\"DATABASE_MATCH\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");
        recents.get(1).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(1).setDueAmount(20.0);
        recents.get(1).setCardSkin("{\"source\":\"DATABASE_M\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");


        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        ObjectMapper objectMapper = new ObjectMapper();


        String config = "{\"generic\":{\"keepCardVariant\":false,\"keepCardSkin\":false},\"debit\":{\"keepCardVariant\":false,\"keepCardSkin\":false},\"paytm sbi card\":{\"keepCardVariant\":true,\"keepCardSkin\":false},\"your bank\":{\"keepCardVariant\":false,\"keepCardSkin\":false}}";
        try {
            Map<String, CardDetails> allowedSkingVariantMap = objectMapper.readValue(config, new TypeReference<Map<String, CardDetails>>() {});
//            Map<String, CardDetails> allowedSkingVariantMap = (Map<String, CardDetails>) JsonUtils.parseJson(config,Map.class);
            FeatureConfigCache.getInstance().setAllowedSkinAndVariant(allowedSkingVariantMap);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertNotNull(favResponse.get(0).getIssuingBankCardVariant());
        assertEquals("Axis Regalia",favResponse.get(1).getIssuingBankCardVariant());
        assertNull(favResponse.get(0).getMediaAssets());

    }

    @Test
    public void testSkinAndVarianSanitise2() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        request.setIsCardSkinRequired(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"cardVariant\":\"Axis paytm sbi card\",\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"cardVariant\":\"Axis Regalia\",\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        LocalDateTime date = LocalDateTime.now();
        recents.get(0).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(0).setDueAmount(20.0);
        recents.get(0).setCardSkin("{\"source\":\"DATABASE_MATCH\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");
        recents.get(1).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(1).setDueAmount(20.0);
        recents.get(1).setCardSkin("{\"source\":\"DATABASE_M\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");


        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        ObjectMapper objectMapper = new ObjectMapper();


        String config = "{\"generic\":{\"keepCardVariant\":false,\"keepCardSkin\":false},\"debit\":{\"keepCardVariant\":false,\"keepCardSkin\":false},\"paytm sbi card\":{\"keepCardVariant\":true,\"keepCardSkin\":true},\"your bank\":{\"keepCardVariant\":false,\"keepCardSkin\":false}}";
        try {
            Map<String, CardDetails> allowedSkingVariantMap = objectMapper.readValue(config, new TypeReference<Map<String, CardDetails>>() {});
//            Map<String, CardDetails> allowedSkingVariantMap = (Map<String, CardDetails>) JsonUtils.parseJson(config,Map.class);
            FeatureConfigCache.getInstance().setAllowedSkinAndVariant(allowedSkingVariantMap);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertNotNull(favResponse.get(0).getIssuingBankCardVariant());
        assertEquals("Axis Regalia",favResponse.get(1).getIssuingBankCardVariant());
        assertNotNull(favResponse.get(0).getMediaAssets());

    }

    @Test
    public void testSkinAndVarianSanitise3() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setClient(Constants.CLIENT.IOS);
        request.setVersion("8.0.2");
        request.setExcludeDropoff(Boolean.TRUE);
        request.setIsCardSkinRequired(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":\"********\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1212,\"cardVariant\":\"Axis paytm sbi card\",\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":\"22222\",\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113344\",\"operator\":\"tata sky\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"2355 XXXX 22232\",\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"cardVariant\":\"Axis Regalia\",\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1213,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"dth\",\"rechargeNumber\":\"22113355\",\"operator\":\"airtel tv\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });

        LocalDateTime date = LocalDateTime.now();
        recents.get(0).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(0).setDueAmount(20.0);
        recents.get(0).setCardSkin("{\"source\":\"DATABASE_MATCH\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");
        recents.get(1).setTxnTime(DateUtil.convertToDateViaInstant(date));
        recents.get(1).setDueAmount(20.0);
        recents.get(1).setCardSkin("{\"source\":\"DATABASE_M\",\"url\":\"https://staticgw.paytm.in/bin-center/00/2a62-73b6-43b7-f7d5/v1/hd0a.png\"}");


        Product product = new Product();
        product.setProductId(1212L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        product = new Product();
        product.setProductId(1213L);
        product.setService("dth");
        product.setOperator("tata sky");
        product.setCategoryId(111L);
        product.setStatus(1);
        ObjectMapper objectMapper = new ObjectMapper();


        String config = "{\"generic\":{\"keepCardVariant\":false,\"keepCardSkin\":false},\"debit\":{\"keepCardVariant\":false,\"keepCardSkin\":false},\"paytm sbi card\":{\"keepCardVariant\":false,\"keepCardSkin\":true},\"your bank\":{\"keepCardVariant\":false,\"keepCardSkin\":false}}";
        try {
            Map<String, CardDetails> allowedSkingVariantMap = objectMapper.readValue(config, new TypeReference<Map<String, CardDetails>>() {});
//            Map<String, CardDetails> allowedSkingVariantMap = (Map<String, CardDetails>) JsonUtils.parseJson(config,Map.class);
            FeatureConfigCache.getInstance().setAllowedSkinAndVariant(allowedSkingVariantMap);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }


        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertNull(favResponse.get(0).getIssuingBankCardVariant());
        assertEquals("Axis Regalia",favResponse.get(1).getIssuingBankCardVariant());
        assertNotNull(favResponse.get(0).getMediaAssets());

    }

    @Test
    public void testIsPartialBillExpiryWithinRange_NullInputs() {
        assertFalse(frequentOrderService.isPartialBillExpiryWithinRange(null, "electricity", new Date()));
        assertFalse(frequentOrderService.isPartialBillExpiryWithinRange("postpaid", null, new Date()));
        assertFalse(frequentOrderService.isPartialBillExpiryWithinRange("postpaid", "electricity", null));
        assertFalse(frequentOrderService.isPartialBillExpiryWithinRange(null, null, null));
    }

    @Test
    public void testIsPartialBillExpiryWithinRange_nullServiceConfig() {
        when(serviceConfig.getPartialBillVisibilityDays()).thenReturn(null);
        assertTrue(frequentOrderService.isPartialBillExpiryWithinRange("postpaid", "electricity", new Date()));
    }

    @Test
    public void testIsPartialBillExpiryWithinRange_ValidInputs() {
        // When billDate is within the allowed range
        when(serviceConfig.getPartialBillVisibilityDays()).thenReturn(10);; // Assuming 10 days is allowed
        assertTrue(frequentOrderService.isPartialBillExpiryWithinRange("postpaid", "electricity", new Date()));
    }

    private Recents getFastagTxnRecent() {
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recents.setKey(recentsPrimaryKey);
        recents.getKey().setService("fastag recharge");
        recents.getKey().setCustomerId(1434239357L);
        recents.getKey().setOperator("axis");
        recents.getKey().setRechargeNumber("**********");
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setDueDate(DateUtil.stringToDate("2024-06-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setUpdatedAt(DateUtil.stringToDate("2024-06-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setProductId(972L);
        return recents;
    }
    @Test
    public void testGetFilteredRecentsSkipForFastagLowBalance() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = getFastagTxnRecent();
        recents.setOperator(Constants.FASTAG_LOW_BALANCE_OPERATOR);
        recents.setDueDate(new Date());
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("fastag recharge");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("**********");
        frequentOrderRequest.setVersion("10.5");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();
        Map<String,Object> ttlConfig = objectMapper.readValue("{\"fastag recharge\":86400}",Map.class);
        featureMap.put("categoryWiseExhaustEventTtl", ttlConfig);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("fastag recharge");
        product.setOperator("axis");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList, config, frequentOrderRequest, txnStatus, txnStatusForNonRu);
        assertEquals(0, uniqueMap.size());
    }

    @Test
    public void testSMSCardWithEventSourceAsSMSInExtraForOneCard() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();

        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
       // when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        List<String> smsEnabledService = new ArrayList<>();
        smsEnabledService.add("mobile");
       // when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        //recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        //recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(0).setExtra("{\"created_source\":\"sms\"}");
        //recents.get(1).setExtra("{\"created_source\":\"sms\"}");


        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        //when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
               // .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
    }


    @Test
    public void testSMSCardWithEventSourceAsSMSInExtraForTwoCard() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);


        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        //when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        List<String> smsEnabledService = new ArrayList<>();
        smsEnabledService.add("mobile");
        //when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        //recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        //recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(0).setExtra("{\"created_source\":\"sms\"}");
        recents.get(1).setExtra("{\"created_source\":\"sms\"}");
        recents.get(1).getKey().setPlanBucket("Special Recharge");
        recents.get(0).getKey().setPlanBucket("Special Recharge");

        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
    }


    @Test
    public void testSMSCardWithEventSourceAsSMSInExtraForTwoCardButOneCardSkippable() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);


        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        //when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        List<String> smsEnabledService = new ArrayList<>();
        smsEnabledService.add("mobile");
        //when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        //recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        //recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(0).setExtra("{\"created_source\":\"sms\"}");
        recents.get(1).setExtra("{\"created_source\":\"sms\"}");


        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
    }




    @Test
    public void testSMSCardWithEventSourceAsSMSInOneCardAndInExtraForOtherCard() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);

        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        List<String> smsEnabledService = new ArrayList<>();
        smsEnabledService.add("mobile");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        recents.get(1).getKey().setRechargeNumber("886554433556");
        //recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(1).setExtra("{\"created_source\":\"sms\"}");
        //recents.get(1).setExtra("{\"created_source\":\"sms\"}");

        recents.get(1).getKey().setPlanBucket("Special Recharge");
        recents.get(0).getKey().setPlanBucket("Special Recharge");


        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
    }


    @Test
    public void testSMSCardWithEventSourceAsSMSInBothCards() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);

        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        List<String> smsEnabledService = new ArrayList<>();
        smsEnabledService.add("mobile");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(1).getKey().setPlanBucket("Special Recharge");
        recents.get(0).getKey().setPlanBucket("Special Recharge");
        recents.get(1).setExtra("{\"plan_bucket\":\"Special Recharge\"}");


        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
    }

    @Test
    public void testSMSCardWithEventSourceAsSMSInBothCardsButNotWhitelisted() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);

        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<String> whitelistedCustId=new ArrayList<>();
        whitelistedCustId.add("12334");
        when(serviceConfig.getWhitelistedCustIdsForMobilePrepaidNonRu()).thenReturn(whitelistedCustId);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        List<String> smsEnabledService = new ArrayList<>();
        smsEnabledService.add("mobile");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(1).getKey().setPlanBucket("Special Recharge");
        recents.get(0).getKey().setPlanBucket("Special Recharge");
        recents.get(1).setExtra("{\"plan_bucket\":\"Special Recharge\"}");


        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());
    }


    @Test
    public void testSMSCardWithEventSourceAsSMSInOneCardAndInExtraForOtherCardForNonWhitelisted() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);

        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);
        List<String> whitelistedCustId=new ArrayList<>();
        whitelistedCustId.add("12334");
        when(serviceConfig.getWhitelistedCustIdsForMobilePrepaidNonRu()).thenReturn(whitelistedCustId);
        List<String> smsEnabledService = new ArrayList<>();
        smsEnabledService.add("mobile");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        recents.get(1).getKey().setRechargeNumber("886554433556");
        //recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(1).setExtra("{\"created_source\":\"sms\"}");
        //recents.get(1).setExtra("{\"created_source\":\"sms\"}");

        recents.get(1).getKey().setPlanBucket("Special Recharge");
        recents.get(0).getKey().setPlanBucket("Special Recharge");


        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(0,
                favResponse.size());
    }


    @Test
    public void testSMSCardWithEventSourceAsSMSInOneCardAndInExtraForOtherCardButOneIsDataPack() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);

        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(serviceConfig.enableSMSCardInRecent()).thenReturn(true);

        List<String> smsEnabledService = new ArrayList<>();
        smsEnabledService.add("mobile");
        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"**********\",\"operator\":\"jio\",\"planBucket\":\"Special Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":*************,\"dueDate\":*************,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":*************,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"mobile\",\"rechargeNumber\":\"8052272223\",\"operator\":\"jio\",\"planBucket\":\"Data Recharge\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(0).setOrderId(null);
        recents.get(0).getKey().setService("mobile");
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");
        recents.get(1).setOrderId(null);
        recents.get(1).getKey().setRechargeNumber("886554433556");
        //recents.get(1).setEventSource(Constants.EVENT_SOURCE.SMS);
        recents.get(1).getKey().setService("mobile");
        recents.get(1).setExtra("{\"created_source\":\"sms\"}");
        //recents.get(1).setExtra("{\"created_source\":\"sms\"}");

        recents.get(1).getKey().setPlanBucket("Special Recharge");
        recents.get(0).getKey().setPlanBucket("Data Pack");


        Product product = new Product();
        product.setProductId(22211L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(2221L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(22234L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null))
                .thenReturn(Collections.emptyMap());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
    }

    @Test
    public void testFastagDuplicate() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(**********L);
        request.setOnlyReminder(Boolean.TRUE);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);

        request.setClient(Constants.CLIENT.ANDROID);
        request.setVersion("20.0.2");
        request.setMethodType("POST");
        request.setServices(Arrays.asList("fastag recharge"));
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);

        List<String> smsEnabledService = new ArrayList<>();
//        when(serviceConfig.getSMSEnabledServices()).thenReturn(smsEnabledService);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":null,\"dueDate\":null,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":*************,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"fastag recharge\",\"rechargeNumber\":\"MH12AB1234\",\"operator\":\"axis\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":null,\"dueDate\":null,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":2221,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":null,\"automaticStatus\":null,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029968000,\"payType\":\"prepaid\",\"key\":{\"customerId\":**********,\"service\":\"fastag recharge\",\"rechargeNumber\":\"mh12ab1234\",\"operator\":\"axis\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(5)));
        recents.get(0).setChannelId("channel 1");
        recents.get(0).setOrderId(null);
        recents.get(1).setUpdatedAt(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(20)));
        recents.get(1).setChannelId("channel 2");


        Product product = new Product();
        product.setProductId(2221L);
        product.setService("fastag recharge");
        product.setOperator("axis");
        product.setCategoryId(17L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
//        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), request.getIsCoft() != null ? Boolean.toString(request.getIsCoft()) : null)).thenReturn(Collections.emptyMap());

//        when(smartRecentsService.getSmartRecentsForFrequentOrderCLP(request)).thenReturn(Collections.emptyList());
        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
    }



}
