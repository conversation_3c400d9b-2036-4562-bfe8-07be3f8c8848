package com.paytm.saga.service;

import com.paytm.saga.dto.WhatsappRemindLaterRequest;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.util.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WhatsappRemindLaterServiceTest {
    @InjectMocks
    WhatsappReminderLaterService whatsappReminderLaterService;

    @Mock
    RecentService recentService;
    @Mock
    RecentsRepository recentsRepository;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSetRemindLaterDate() {
        List<Recents> recents = new ArrayList<>();
        WhatsappRemindLaterRequest whatsappRemindLaterRequest = new WhatsappRemindLaterRequest();
        whatsappRemindLaterRequest.setRemindLaterDate(new Date());
        whatsappRemindLaterRequest.setService("electricity");
        whatsappRemindLaterRequest.setCustomerId("123");
        whatsappRemindLaterRequest.setOperator("TNEB");
        whatsappRemindLaterRequest.setRechargeNumber("1234567890");
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setRechargeNumber("1234567890");
        recentsPrimaryKey.setOperator("TNEB");
        recentsPrimaryKey.setCustomerId(123L);
        recent.setKey(recentsPrimaryKey);

        recents.add(recent);

        whatsappReminderLaterService.setRemindLaterDate(whatsappRemindLaterRequest);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> offset = ArgumentCaptor.forClass(Integer.class);
        verify(recentService).updateRemindLater(recentsArgumentCaptor.capture(),offset.capture());
        assertEquals(whatsappRemindLaterRequest.getRemindLaterDate(), recentsArgumentCaptor.getValue().getRemindLaterDate());
    }

    @Test
    public void testSetRemindLaterDate1() {
        List<Recents> recents = new ArrayList<>();
        WhatsappRemindLaterRequest whatsappRemindLaterRequest = new WhatsappRemindLaterRequest();
        whatsappRemindLaterRequest.setRemindLaterDate(new Date());
        whatsappRemindLaterRequest.setService("electricity");
        whatsappRemindLaterRequest.setCustomerId("123");
        whatsappRemindLaterRequest.setOperator("TNEB");
        whatsappRemindLaterRequest.setRechargeNumber("1234567890");
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setRechargeNumber("1234567890");
        recentsPrimaryKey.setOperator("TNEB");
        recentsPrimaryKey.setCustomerId(123L);
        recent.setKey(recentsPrimaryKey);

        recents.add(recent);

        whatsappReminderLaterService.setRemindLaterDate(whatsappRemindLaterRequest);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> offset = ArgumentCaptor.forClass(Integer.class);
        verify(recentService).updateRemindLater(recentsArgumentCaptor.capture(),offset.capture());
        assertEquals(whatsappRemindLaterRequest.getRemindLaterDate(), recentsArgumentCaptor.getValue().getRemindLaterDate());
    }

    @Test
    public void testSetRemindLaterDateOffset() {
        List<Recents> recents = new ArrayList<>();
        WhatsappRemindLaterRequest whatsappRemindLaterRequest = new WhatsappRemindLaterRequest();
        whatsappRemindLaterRequest.setRemindLaterOffset(2);
        whatsappRemindLaterRequest.setService("electricity");
        whatsappRemindLaterRequest.setCustomerId("123");
        whatsappRemindLaterRequest.setOperator("TNEB");
        whatsappRemindLaterRequest.setRechargeNumber("1234567890");
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setRechargeNumber("1234567890");
        recentsPrimaryKey.setOperator("TNEB");
        recentsPrimaryKey.setCustomerId(123L);
        recent.setKey(recentsPrimaryKey);
        recent.setDueDate(DateUtil.addHours(new Date(),2));

        recents.add(recent);

        whatsappReminderLaterService.setRemindLaterDate(whatsappRemindLaterRequest);
        ArgumentCaptor<Recents> recentsArgumentCaptor = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> reminderOffset = ArgumentCaptor.forClass(Integer.class);
        verify(recentService).updateRemindLater(recentsArgumentCaptor.capture(),reminderOffset.capture());
    }


}
