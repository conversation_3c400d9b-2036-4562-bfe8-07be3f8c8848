package com.paytm.saga.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.common.constant.UtilityThemeTypes;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.repository.ChannelHistoryRepository;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;
import com.paytm.saga.service.aggregator.cardmanager.PlanExpiry;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class PlanExpiryServiceTest {
    @Mock
    private RPSService rpsService;
    @Mock
    private Recents recents;
    @Mock
    private RecentsPrimaryKey recentsPrimaryKey;
    @Mock
    private RecentsRepository recentsRepository;
    @Mock
    private ChannelHistoryRepository channelHistoryRepository;
    @Mock
    private PlanExpiryHistoryService planExpiryHistoryService;
    @InjectMocks
    private PlanExpiry planExpiry;
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }
    private List<ChannelHistory> mockChannelHistory(){
        ChannelHistory channelHistory1 = new ChannelHistory();
        channelHistory1.setCustomerId(Long.parseLong("23"));
        channelHistory1.setRechargeNumber("82878996");
        channelHistory1.setService("mobile");
        channelHistory1.setProductId(Long.parseLong("185"));
        channelHistory1.setEventType(EventTypes.RECHARGE);
        List<ChannelHistory> channelHistories = new ArrayList<>();
        channelHistories.add(channelHistory1);
        return channelHistories;
    }
    private List<Recents> mockRecents(){
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey1 = new RecentsPrimaryKey();
        recentsPrimaryKey1.setCustomerId(Long.parseLong("23"));
        recentsPrimaryKey1.setRechargeNumber("82878996");
        recentsPrimaryKey1.setService("mobile");
        recentsPrimaryKey1.setOperator("airtel");
        recents.setKey(recentsPrimaryKey1);
        recents.setProductId(Long.parseLong("185"));
        recents.setCircle("delhi ncr");
        // channelHistory1.setEventType(EventTypes.RECHARGE);
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents);
        return recents1;
    }
    private List<PlanExpiryHistory> mockPlanExpiryHistory(){
        PlanExpiryHistory planExpiryHistory = new PlanExpiryHistory();
        planExpiryHistory.setPlan_bucket("Special Recharge");
        planExpiryHistory.setService("mobile");
        planExpiryHistory.setValidity_expiry_date(new Date());
        planExpiryHistory.setOperator("airtel");
        planExpiryHistory.setAmount(399.0);
        planExpiryHistory.setRechargeNumber("82878996");
        planExpiryHistory.setUpdated_at(new Date());
        planExpiryHistory.setCategory_name("er");
        planExpiryHistory.setCircle("delhi ncr");
        planExpiryHistory.setCreated_at(new Date());
        planExpiryHistory.setCustomerid(Long.parseLong("23"));

        Map<String,String> displayValues = new HashMap<>();
        displayValues.put("data","2.5 GB/Day");
        displayValues.put("description","Unlimited All India Calls | Data : 2.5GB/Day | SMS : 100/Day | Validity : 28 Days.OTT Subscription :Disney+ Hotstar Subscription for 3 months");
        displayValues.put("sms","100 SMS/Day");
        displayValues.put("talktime","NA");
        displayValues.put("validity","28 Days");
        planExpiryHistory.setDisplay_values(displayValues);
        List<PlanExpiryHistory> planExpiryHistories = new ArrayList<>();
        planExpiryHistories.add(planExpiryHistory);
        return planExpiryHistories;
    }
    @Test
    public void testGetSuggestionCardRpsResponseNonNull(){
        List<ChannelHistory> channelHistories = mockChannelHistory();
        List<PlanExpiryHistory> planExpiryHistories = mockPlanExpiryHistory();
        List<Recents> recents = mockRecents();

        String response = "{\"plan_bucket\":\"Special Recharge\",\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":45245}";
        DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);

        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberAndService(Long.parseLong("23"),"82878996","mobile")).thenReturn(channelHistories);
        when(rpsService.getPlanDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);
        when(planExpiryHistoryService.getPlanExpiryByRecharge("82878996","airtel","delhi ncr","mobile")).thenReturn(planExpiryHistories);

        HistoryView historyView = planExpiry.getRechargeSuggestionCard("82878996","airtel:mobile:delhi ncr",channelHistories);

        assertEquals(DateUtil.dateFormatter(planExpiryHistories.get(0).getValidity_expiry_date(), DateFormats.DATE_FORMAT_2), historyView.getPlanExpiryDate());
    }
    @Test
    public void testGetSuggestionCardRpsResponseNull(){
        List<ChannelHistory> channelHistories = mockChannelHistory();
        List<PlanExpiryHistory> planExpiryHistories = mockPlanExpiryHistory();
        List<Recents> recents = mockRecents();

        when(rpsService.getPlanDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        when(planExpiryHistoryService.getPlanExpiryByRecharge("82878996","airtel","delhi ncr","mobile")).thenReturn(planExpiryHistories);
        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberAndService(Long.parseLong("23"),"82878996","mobile")).thenReturn(channelHistories);

        HistoryView historyView = planExpiry.getRechargeSuggestionCard("82878996","airtel:mobile:delhi ncr",channelHistories);

        assertEquals(planExpiryHistories.get(0).getDisplay_values().get("data"), historyView.getPlanDetail().get("data"));
    }
}
