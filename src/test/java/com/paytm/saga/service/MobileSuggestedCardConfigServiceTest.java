package com.paytm.saga.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.paytm.saga.dto.FeatureConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.ParameterizedTypeReference;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FeatureConfigResponse;

import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class MobileSuggestedCardConfigServiceTest {

    @Mock
    private GenericRestClient genericRestClient;

    @Mock
    private MetricsHelper metricsHelper;

    @InjectMocks
    private MobileSuggestedCardConfigService mobileSuggestedCardConfigService;

    @InjectMocks
    private FeatureConfig featureConfig;

    @InjectMocks
    private FeatureConfigService featureConfigService;

    @Test
    public void setMobileSuggestedCardConfigSuccessfulResponse() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        String jsonResponse = "{\"status\":200,\"data\":[{\"id\":1104,\"service_name\":\"mobileSuggestedCards\",\"config\":\"{\\\"bsnl\\\":{\\\"haryana\\\":{\\\"DataPack\\\":{\\\"amount\\\":100,\\\"planId\\\":\\\"1234\\\",\\\"circleName\\\":\\\"Haryana\\\"},\\\"SpecialRecharge\\\":{\\\"amount\\\":130,\\\"planId\\\":\\\"1234\\\",\\\"circleName\\\":\\\"Haryana\\\"}},\\\"maharashtra\\\":{\\\"DataPack\\\":{\\\"amount\\\":120,\\\"planId\\\":\\\"2345\\\",\\\"circleName\\\":\\\"Maharashtra\\\"},\\\"SpecialRecharge\\\":{\\\"amount\\\":110,\\\"planId\\\":\\\"2345\\\",\\\"circleName\\\":\\\"Maharashtra\\\"}}},\\\"airtel\\\":{\\\"delhincr\\\":{\\\"DataPack\\\":{\\\"amount\\\":140},\\\"SpecialRecharge\\\":{\\\"amount\\\":140}}}}\"}]}";
        FeatureConfigResponse response = mapper.readValue(jsonResponse, FeatureConfigResponse.class);

        when(genericRestClient.get(any(), any(), any(), any(), any(ParameterizedTypeReference.class)))
                .thenReturn(response);

        mobileSuggestedCardConfigService.setMobileSuggestedCardConfig();
        Map<String, Object> suggestedCardConfig = FeatureConfigCache.getInstance().getSuggestedCardConfig();
        assertNotNull(suggestedCardConfig);
        assertNotNull(suggestedCardConfig.get("bsnl"));
        assertNotNull(suggestedCardConfig.get("airtel"));
    }

    @Test
    public void setMobileSuggestedCardConfigNullResponse() {
        when(genericRestClient.get(any(), any(), any(), any(), any(ParameterizedTypeReference.class)))
                .thenReturn(null);

        mobileSuggestedCardConfigService.setMobileSuggestedCardConfig();
        Map<String, Object> suggestedCardConfig = FeatureConfigCache.getInstance().getSuggestedCardConfig();
        assertNotNull(suggestedCardConfig);
        assertNull(suggestedCardConfig.get("bsnl"));
        assertNull(suggestedCardConfig.get("airtel"));
    }
}