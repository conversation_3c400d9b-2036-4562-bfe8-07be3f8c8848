package com.paytm.saga.service;

import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.util.AESUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ChannelHistoryEncryptionHandlerTest {

	@Mock
	private AESUtil aesUtil;

	@InjectMocks
	private ChannelHistoryEncryptionHandler channelHistoryEncryptionHandler;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		when(aesUtil.encrypt("100")).thenReturn("encrypted100");
		when(aesUtil.encrypt("1234567890")).thenReturn("encrypted1234567890");
		when(aesUtil.encrypt("0987654321")).thenReturn("encrypted0987654321");
		when(aesUtil.encrypt("value1")).thenReturn("encryptedValue1");
		when(aesUtil.decrypt("encrypted100")).thenReturn("100");
		when(aesUtil.decrypt("encrypted1234567890")).thenReturn("1234567890");
		when(aesUtil.decrypt("encrypted0987654321")).thenReturn("0987654321");
		when(aesUtil.decrypt("encryptedValue1")).thenReturn("value1");
	}

	@Test
	public void testEncryptChannelHistory() throws AES256Exception {
		ChannelHistory channelHistory = new ChannelHistory();
		channelHistory.setAmount("100");
		channelHistory.setRechargeNumber("1234567890");
		channelHistory.setRecharge_number_3("0987654321");
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("key1", "value1");
		channelHistory.setBillsObj(billsObj);

		ChannelHistory encryptedChannelHistory = channelHistoryEncryptionHandler.encryptChannelHistory(channelHistory);

		assertEquals("encrypted100", encryptedChannelHistory.getAmount());
		assertEquals("encrypted1234567890", encryptedChannelHistory.getRechargeNumber());
		assertEquals("encrypted0987654321", encryptedChannelHistory.getRecharge_number_3());
		assertEquals("encryptedValue1", encryptedChannelHistory.getBillsObj().get("key1"));
		assertEquals(1, encryptedChannelHistory.getIsEncrypted().intValue());
	}

	@Test
	public void testDecryptChannelHistorySlice() throws AES256Exception {
		ChannelHistory channelHistory = new ChannelHistory();
		channelHistory.setAmount("encrypted100");
		channelHistory.setRechargeNumber("encrypted1234567890");
		channelHistory.setRecharge_number_3("encrypted0987654321");
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("key1", "encryptedValue1");
		channelHistory.setBillsObj(billsObj);
		channelHistory.setIsEncrypted(1);

		Slice<ChannelHistory> channelHistoryList = new SliceImpl<>(new ArrayList<>(Collections.singletonList(channelHistory)));

		channelHistoryList = channelHistoryEncryptionHandler.decryptChannelHistorySlice(channelHistoryList);

		ChannelHistory channelHistoryDecrypted = channelHistoryList.getContent().get(0);
		assertEquals("100", channelHistoryDecrypted.getAmount());
		assertEquals("1234567890", channelHistoryDecrypted.getRechargeNumber());
		assertEquals("0987654321", channelHistoryDecrypted.getRecharge_number_3());
		assertEquals("value1", channelHistoryDecrypted.getBillsObj().get("key1"));
		assertEquals(0, channelHistoryDecrypted.getIsEncrypted().intValue());
	}

	@Test
	public void testDecryptChannelHistoryList() throws AES256Exception {
		ChannelHistory channelHistory = new ChannelHistory();
		channelHistory.setAmount("encrypted100");
		channelHistory.setRechargeNumber("encrypted1234567890");
		channelHistory.setRecharge_number_3("encrypted0987654321");
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("key1", "encryptedValue1");
		channelHistory.setBillsObj(billsObj);
		channelHistory.setIsEncrypted(1);

		List<ChannelHistory> channelHistoryList = new ArrayList<>(Collections.singletonList(channelHistory));

		channelHistoryList = channelHistoryEncryptionHandler.decryptChannelHistoryList(channelHistoryList);

		ChannelHistory channelHistoryDecrypted = channelHistoryList.get(0);
		assertEquals("100", channelHistoryDecrypted.getAmount());
		assertEquals("1234567890", channelHistoryDecrypted.getRechargeNumber());
		assertEquals("0987654321", channelHistoryDecrypted.getRecharge_number_3());
		assertEquals("value1", channelHistoryDecrypted.getBillsObj().get("key1"));
		assertEquals(0, channelHistoryDecrypted.getIsEncrypted().intValue());
	}

}
