package com.paytm.saga.service;

import com.datastax.oss.driver.api.core.ConsistencyLevel;
import com.datastax.oss.driver.api.core.cql.SimpleStatement;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.RecentDao;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.FrequentOrderResponse;
import com.paytm.saga.dto.RecentConfig;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.SmartRecents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.RecentUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.logging.log4j.LogManager;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.cassandra.core.CassandraTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class RecoWidgetServiceImplTest {

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(FrequentOrderServiceImpl.class));

    @InjectMocks
    private RecoWidgetDataService frequentOrderService;

    @Mock
    private SmartRecentsService smartRecentsService;

    @Mock
    private UserAgentService userAgentService;

    @Mock
    private SmartReminderService smartReminderService;

    @Mock
    CassandraTemplate template;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    private DropOffService dropOffService;

    @Mock
    @Qualifier("monitoringClient")
    protected StatsDClient monitoringClient;

    @Mock
    private RecentDao recentDao;

    @Mock
    private ServiceConfig serviceConfig;

    @Mock
    private LocalisationManager localisationManager;

    @Mock
    private MetricsHelper metricsHelper;

    @Before
    public void setUp() {

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"newAccountServices\": [\"mobile\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
          //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        ReflectionTestUtils.setField(frequentOrderService, "includeOperatorInKeyServices", Arrays.asList("dth"));
        ExecutorService executorService = new ThreadPoolExecutor(20, 20, 10, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(10));
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderDropOffExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderSmartRecentExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderRecentExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderSmartRecentExecutor", executorService);

    }


    @Test
    public void testSingleRecordInOnlyRecent() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        List<String> serviceList = new ArrayList<>();
        serviceList.add("dth");
        request.setServices(serviceList);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"8052270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(smartReminderService.getFilteredRecentTableClusterKeys(any()))
                .thenReturn(recentsPrimaryKeys);
        List<Recents> recents = objectMapper.readValue(" [{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":65656,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":null,\"payType\":null,\"key\":{\"customerId\":1107199327,\"service\":\"electricity\",\"rechargeNumber\":\"8052270003\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setDueDate(new Date());
        recents.get(0).setUpdatedAt(new Date());
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        //  when(activeInactivePidMap.getActivePid(any())).thenReturn(1139L);


        when(recentDao.findByQuery(any())).thenReturn(recents);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());


    }

    @Test
    public void testMobileServiceSameRechargeNumberDifferentOperator() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        List<String> serviceList = new ArrayList<>();
        serviceList.add("dth");
        request.setServices(serviceList);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        String response = "[{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"8052270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        when(smartReminderService.getFilteredRecentTableClusterKeys(any()))
                .thenReturn(recentsPrimaryKeys);
        List<Recents> recents = objectMapper.readValue(" [{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":null,\"key\":{\"customerId\":1107199327,\"service\":\"mobile\",\"rechargeNumber\":\"8052270003\",\"operator\":\"airtel\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1149,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":null,\"key\":{\"customerId\":1107199327,\"service\":\"mobile\",\"rechargeNumber\":\"8052270003\",\"operator\":\"jio\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(1).setDueDate(new Date());

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(1149L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByQuery(any())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals("1149",
                favResponse.get(0).getPid());


    }

   //@Test
    public void testSmartRecentReco() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        List<String> serviceList = new ArrayList<>();
        serviceList.add("electricity");
        request.setServices(serviceList);
        SmartRecents smartRecents = new SmartRecents();
        SmartRecentsPrimaryKey key = new SmartRecentsPrimaryKey();
        key.setCustomerId(1107199327L);
        key.setService("electricity");
        key.setOperator("msedcl");
        smartRecents.setKey(key);
        smartRecents.setCircle("");
        smartRecents.setPayType("prepaid");
        smartRecents.setEventSource("SMART_RECENT_VALIDATION");
        smartRecents.setProductId(1139L);
        smartRecents.setCreatedAt(Date.from(Instant.now()));
        smartRecents.setUpdatedAt(Date.from(Instant.now()));

        when(smartRecentsService.getSmartRecentsForFrequentOrderCLP(any())).thenReturn(new ArrayList<SmartRecents>() {{
            add(smartRecents);
        }});
        when(serviceConfig.getSmartRecentRecoEnabledServices()).thenReturn(new ArrayList<String>() {{
            add("electricity");
        }});
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"\", \"operator\":\"msedcl\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        when(smartReminderService.getFilteredRecentTableClusterKeys(any()))
                .thenReturn(recentsPrimaryKeys);
        List<Recents> recents = objectMapper.readValue("[{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"prepaid\",\"key\":{\"customerId\":1107199327,\"service\":\"mobile\",\"rechargeNumber\":\"8052270003\",\"operator\":\"airtel\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"circle\":\"\",\"eventSource\":\"SMART_RECENT_DROPOFF\"},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":5555,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"prepaid\",\"key\":{\"customerId\":1107199327,\"service\":\"mobile\",\"rechargeNumber\":\"8052270003\",\"operator\":\"airtel\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null,\"circle\":\"\",\"eventSource\":\"SMART_RECENT_VALIDATION\"}]", new TypeReference<List<Recents>>() {
        });
        recents.get(1).setDueDate(new Date());

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("msedcl");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(1149L);
        product.setService("electricity");
        product.setOperator("tsspdcl");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByQuery(any())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(3,
                favResponse.size());
        assertEquals("1139",
                favResponse.get(0).getPid());


    }

    @Test
    public void testDTHServiceSameRechargeNumberDifferentOperator() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        List<String> serviceList = new ArrayList<>();
        serviceList.add("dth");
        request.setServices(serviceList);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        when(smartReminderService.getFilteredRecentTableClusterKeys(any()))
                .thenReturn(recentsPrimaryKeys);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        List<Recents> recents = objectMapper.readValue("\n" +
                " [{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":6666,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":null,\"key\":{\"customerId\":1107199327,\"service\":\"dth\",\"rechargeNumber\":\"8052270003\",\"operator\":\"dish tv\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":null,\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":7777,\"productId\":1149,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":null,\"key\":{\"customerId\":1107199327,\"service\":\"dth\",\"rechargeNumber\":\"8052270003\",\"operator\":\"tata sky\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recents.get(0).setDueDate(new Date());
        recents.get(1).setDueDate(new Date());

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("mobile");
        product.setOperator("dish tv");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        product = new Product();
        product.setProductId(1149L);
        product.setService("mobile");
        product.setOperator("tata sky");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

       // when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByQuery(any())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);

        assertEquals(2,
                favResponse.size());


    }


    //@Test
    public void testCCServiceSameMCNDifferentOperator() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        List<String> serviceList = new ArrayList<>();
        serviceList.add("dth");
        request.setServices(serviceList);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        when(smartReminderService.getFilteredRecentTableClusterKeys(any()))
                .thenReturn(recentsPrimaryKeys);
        List<Recents> recents=new ArrayList<>();
        List<Recents> recentsTemp = objectMapper.readValue("\n" +
                "[{\"mcn\":\"4699 89XX XXXX 2346\",\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":8,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":8888,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":null,\"key\":{\"customerId\":1107199327,\"service\":\"financial services\",\"rechargeNumber\":\"222223444555\",\"operator\":\"axis\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"4699 89XX XXXX 2346\",\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":8,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":9999,\"productId\":1149,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":null,\"key\":{\"customerId\":1107199327,\"service\":\"financial services\",\"rechargeNumber\":\"8888883444555\",\"operator\":\"rupayboi\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]", new TypeReference<List<Recents>>() {
        });
        recentsTemp.get(0).setDueDate(new Date());
        recentsTemp.get(1).setDueDate(new Date());
        recents=recentsTemp;
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("financial services");
        product.setOperator("axis");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(1149L);
        product.setService("financial services");
        product.setOperator("rupayboi");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
//        when(activeInactivePidMap.getActivePid(any())).thenReturn(1139L);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        SimpleStatement simpleStatement = SimpleStatement.newInstance(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys))
                .setConsistencyLevel(ConsistencyLevel.LOCAL_QUORUM);

        when(recentDao.findByQuery(any())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(2,
                favResponse.size());
        assertEquals("1149",
                favResponse.get(0).getPid());
        assertEquals("1139",
                favResponse.get(1).getPid());

    }
    @Test
    public void testCCServiceSameMCNDifferentOperatorWithCoftTrue() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setIsCoft(Boolean.TRUE);
        List<String> serviceList = new ArrayList<>();
        serviceList.add("dth");
        request.setServices(serviceList);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        when(smartReminderService.getFilteredRecentTableClusterKeys(any()))
                .thenReturn(recentsPrimaryKeys);
        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"mcn\":\"4610 89XX XXXX 2346\",\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":"+ new Date().getTime()
                        +",\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":767676" +
                        ",\"productId\":1149,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"credit card\",\"key\":{\"customerId\":1107199327,\"service\":\"financial services\",\"rechargeNumber\":\"222223444555\",\"operator\":\"axis\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"4699 89XX XXXX 2346\",\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":" + new Date().getTime() + ",\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":121233,\"productId\":1149,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"credit card\",\"key\":{\"customerId\":1107199327,\"service\":\"financial services\",\"rechargeNumber\":\"8888883444555\",\"operator\":\"axis\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });

        Product product = new Product();
        product.setProductId(1149L);
        product.setService("financial services");
        product.setOperator("axis");
        product.setPayType("credit card");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(1149L);
        product.setService("financial services");
        product.setOperator("axis");
        product.setPayType("credit card");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        recents.get(0).setUpdatedAt(new Date());
        recents.get(1).setUpdatedAt(DateUtil.addMinutes(new Date(), 15));

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);

        when(recentDao.findByQuery(any())).thenReturn(recents);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals("1149",
                favResponse.get(0).getPid());

    }

    private Recents getFastagTxnRecent() {
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recents.setKey(recentsPrimaryKey);
        recents.getKey().setService("fastag recharge");
        recents.getKey().setCustomerId(1434239357L);
        recents.getKey().setOperator("axis");
        recents.getKey().setRechargeNumber("3040250564");
        recents.getKey().setPlanBucket("");
        recents.setPayType("prepaid");
        recents.setDueDate(DateUtil.stringToDate("2024-06-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setUpdatedAt(DateUtil.stringToDate("2024-06-09 07:34:39", DateFormats.DATE_TIME_FORMAT_2));
        recents.setProductId(972L);
        return recents;
    }
    @Test
    public void testGetFilteredRecentsForFastagLowBalance() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = getFastagTxnRecent();
        recents.setOperator(Constants.FASTAG_LOW_BALANCE_OPERATOR);
        recents.setDueDate(new Date());
        recentsList.add(recents);
        RecentConfig config = new RecentConfig();
        ArrayList<String> service = new ArrayList<>();
        service.add("fastag recharge");
        config.setIncludeOperatorInKeyServices(service);
        FrequentOrderRequest frequentOrderRequest = new FrequentOrderRequest();
        frequentOrderRequest.setCustomerId(1434239357l);
        frequentOrderRequest.setServices(service);
        frequentOrderRequest.setRechargeNumber("3040250564");
        frequentOrderRequest.setVersion("10.5");

        Map<String, Recents> txnStatus = new HashMap<>();
        Map<String, Recents> txnStatusForNonRu = new HashMap<>();

        Map<String, Object> featureMap = new HashMap<>();

        Map<String,Object> ttlConfig = objectMapper.readValue("{\"fastag recharge\":86400}",Map.class);
        featureMap.put("categoryWiseExhaustEventTtl", ttlConfig);
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureMap);

        Product product = new Product();
        product.setProductId(972L);
        product.setService("fastag recharge");
        product.setOperator("axis");
        product.setPayType("prepaid");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        Map<String, Recents> uniqueMap = frequentOrderService.getFilteredRecents(recentsList, config, frequentOrderRequest, txnStatus, txnStatusForNonRu);
        assertEquals(1, uniqueMap.size());
    }
}
