package com.paytm.saga.service;

import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.repository.ChannelHistoryFinalizedRepository;
import com.paytm.saga.util.AESUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.cassandra.core.query.CassandraPageRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.FINANCIAL_SERVICES;
import static com.paytm.saga.common.constant.EncryptionConstants.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ChannelHistoryFinalizedRepositoryWrapperServiceTest {

	@Mock
	private AESUtil aesUtil;

	@Mock
	private ChannelHistoryFinalizedEncryptionHandler channelHistoryFinalizedEncryptionHandler;

	@Mock
	private ChannelHistoryFinalizedRepository channelHistoryFinalizedRepository;

	@Mock
	private MetricsHelper metricsHelper;

	@InjectMocks
	private ChannelHistoryFinalizedRepositoryWrapperService service;

	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		Map<String, Object> configMap = new HashMap<>();
		configMap.put(ENABLE_DATA_ENCRYPTION_FLAG, true);
		configMap.put(REMINDER_RESPONSE_EXTRA_ENCRYPTED_KEYS, Collections.singletonList("issuingBankCardVariant"));
		configMap.put(CUSTOMER_OTHER_INFO_ENCRYPTED_KEYS, new HashSet<>(Arrays.asList("lastCC", "rawLastCC", "currentBillAmount", "currentMinBillAmount", "billDate", "billDueDate", "debugKey", "amount")));
		FeatureConfigCache.getInstance().setFeatureConfigMap(configMap);
	}

	@Test
	public void findByCustomerIdAndRechargeNumberAndService_withEncryption() throws AES256Exception {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = FINANCIAL_SERVICES;
		PageRequest pageRequest = PageRequest.of(0, 10);

		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		Slice<ChannelHistoryFinalized> encryptedSlice = new SliceImpl<>(Collections.singletonList(channelHistoryFinalized));
		Slice<ChannelHistoryFinalized> decryptedSlice = new SliceImpl<>(Collections.singletonList(channelHistoryFinalized));

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");
		when(channelHistoryFinalizedRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, Arrays.asList(rechargeNumber, "encrypted**********"), serviceType, pageRequest))
			.thenReturn(encryptedSlice);
		when(channelHistoryFinalizedEncryptionHandler.decryptChannelHistoryFinalizedSlice(encryptedSlice)).thenReturn(decryptedSlice);

		Slice<ChannelHistoryFinalized> result = service.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, serviceType, pageRequest);

		assertEquals(decryptedSlice, result);
	}

	@Test
	public void findByCustomerIdAndRechargeNumberAndService_withoutEncryption() throws AES256Exception {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = "non-financial";
		PageRequest pageRequest = PageRequest.of(0, 10);

		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		Slice<ChannelHistoryFinalized> slice = new SliceImpl<>(Collections.singletonList(channelHistoryFinalized));

		when(channelHistoryFinalizedRepository.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, serviceType, pageRequest))
			.thenReturn(slice);

		Slice<ChannelHistoryFinalized> result = service.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, serviceType, pageRequest);

		assertEquals(slice, result);
	}

	@Test(expected = AES256Exception.class)
	public void findByCustomerIdAndRechargeNumberAndService_withEncryptionError() throws AES256Exception {
		Long customerId = 12345L;
		String rechargeNumber = "**********";
		String serviceType = FINANCIAL_SERVICES;
		PageRequest pageRequest = PageRequest.of(0, 10);

		when(aesUtil.encrypt(rechargeNumber)).thenReturn("encrypted**********");
		when(channelHistoryFinalizedRepository.findByCustomerIdAndRechargeNumberInAndService(customerId, Arrays.asList(rechargeNumber, "encrypted**********"), serviceType, pageRequest))
			.thenThrow(new AES256Exception("Encryption error"));

		service.findByCustomerIdAndRechargeNumberAndService(customerId, rechargeNumber, serviceType, pageRequest);
	}

	@Test
	public void findAll_withDecryption() throws AES256Exception {
		CassandraPageRequest cassandraPageRequest = CassandraPageRequest.of(0, 10);
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		Slice<ChannelHistoryFinalized> encryptedSlice = new SliceImpl<>(Collections.singletonList(channelHistoryFinalized));
		Slice<ChannelHistoryFinalized> decryptedSlice = new SliceImpl<>(Collections.singletonList(channelHistoryFinalized));

		when(channelHistoryFinalizedRepository.findAll(cassandraPageRequest)).thenReturn(encryptedSlice);
		when(channelHistoryFinalizedEncryptionHandler.decryptChannelHistoryFinalizedSlice(encryptedSlice)).thenReturn(decryptedSlice);

		Slice<ChannelHistoryFinalized> result = service.findAll(cassandraPageRequest);

		assertEquals(decryptedSlice, result);
	}

	@Test(expected = AES256Exception.class)
	public void findAll_withDecryptionError() throws AES256Exception {
		CassandraPageRequest cassandraPageRequest = CassandraPageRequest.of(0, 10);
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		Slice<ChannelHistoryFinalized> encryptedSlice = new SliceImpl<>(Collections.singletonList(channelHistoryFinalized));

		when(channelHistoryFinalizedRepository.findAll(cassandraPageRequest)).thenReturn(encryptedSlice);
		when(channelHistoryFinalizedEncryptionHandler.decryptChannelHistoryFinalizedSlice(encryptedSlice)).thenThrow(new AES256Exception("Decryption error"));

		service.findAll(cassandraPageRequest);
	}

	@Test
	public void saveAll_withEncryption() throws AES256Exception {
		List<ChannelHistoryFinalized> channelHistoryFinalizedList = new ArrayList<>();
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		channelHistoryFinalized.setService(FINANCIAL_SERVICES);
		channelHistoryFinalized.setCustomerId(12345L);
		channelHistoryFinalizedList.add(channelHistoryFinalized);
		Integer ttl = 3600;

		ChannelHistoryFinalized encryptedChannelHistoryFinalized = new ChannelHistoryFinalized();
		when(channelHistoryFinalizedEncryptionHandler.encryptChannelHistoryFinalized(channelHistoryFinalized))
			.thenReturn(encryptedChannelHistoryFinalized);

		service.saveAll(channelHistoryFinalizedList, ttl);

		verify(channelHistoryFinalizedRepository, times(1)).saveAll(anyList(), eq(ttl));
	}

	@Test
	public void saveAll_withoutEncryption() throws AES256Exception {
		List<ChannelHistoryFinalized> channelHistoryFinalizedList = new ArrayList<>();
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		channelHistoryFinalized.setService("non-financial");
		channelHistoryFinalizedList.add(channelHistoryFinalized);
		Integer ttl = 3600;

		service.saveAll(channelHistoryFinalizedList, ttl);

		verify(channelHistoryFinalizedRepository, times(1)).saveAll(channelHistoryFinalizedList, ttl);
	}

	@Test(expected = AES256Exception.class)
	public void saveAll_withEncryptionError() throws AES256Exception {
		List<ChannelHistoryFinalized> channelHistoryFinalizedList = new ArrayList<>();
		ChannelHistoryFinalized channelHistoryFinalized = new ChannelHistoryFinalized();
		channelHistoryFinalized.setService(FINANCIAL_SERVICES);
		channelHistoryFinalized.setCustomerId(12345L);
		channelHistoryFinalizedList.add(channelHistoryFinalized);
		Integer ttl = 3600;

		when(channelHistoryFinalizedEncryptionHandler.encryptChannelHistoryFinalized(channelHistoryFinalized))
			.thenThrow(new AES256Exception("Encryption error"));

		service.saveAll(channelHistoryFinalizedList, ttl);
	}
}