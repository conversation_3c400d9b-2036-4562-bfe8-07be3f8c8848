package com.paytm.saga.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.BillerAccountListenerException;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.DeleteRequestBody;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FetchRecentsRequest;
import com.paytm.saga.dto.NickNameResponse;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.dto.cdc.LongEntity;
import com.paytm.saga.dto.cdc.ReminderAfter;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.dto.cdc.StringEntity;
import com.paytm.saga.model.BillerAccountKafkaModel;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import com.paytm.saga.service.impl.DeleteCreditCardRecentCard;
import com.paytm.saga.service.impl.DeleteNonCreditCardRecentCard;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.timgroup.statsd.StatsDClient;
import junit.framework.TestCase;
import org.apache.logging.log4j.LogManager;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.*;
import org.springframework.data.cassandra.CassandraInvalidQueryException;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.paytm.saga.common.constant.Constants.billerAccountListenerConstants.RECENTS_DATE_FORMAT;
import static org.mockito.Mockito.*;

public class RecentServiceTest {
    @Mock
    MetricsHelper metricsHelper;
    @Mock
    StatsDClient monitoringClient;
    @Mock
	RecentsRepositoryWrapperService recentsRepository;
    @Mock
    RecentDataToKafkaService recentDataToKafkaService;
    @Mock
    DeleteCreditCardRecentCard deleteCreditCardRecentCard;
    @Mock
    DeleteNonCreditCardRecentCard deleteNonCreditCardRecentCard;

    @Mock
    SmartRecentsRepository smartRecentsRepository;

    @InjectMocks
    RecentsServiceImpl recentsServiceImpl;

    @Mock
    ServiceConfig serviceConfig;

    @InjectMocks
    RecentService recentService;

    private List<Recents> mockRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("**********");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("prepaid");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);


        Recents recents2=new Recents();
        RecentsPrimaryKey recentsPrimaryKey2=new RecentsPrimaryKey();
        recentsPrimaryKey2.setCustomerId(1123L);
        recentsPrimaryKey2.setService("mobile");
        recentsPrimaryKey2.setRechargeNumber("**********");
        recentsPrimaryKey2.setOperator("jio");
        recentsPrimaryKey2.setPlanBucket("Special Recharge");
        recents2.setKey(recentsPrimaryKey2);
        recents2.setPayType("prepaid");
        recents2.setTxnAmount(100.0);
        recentsList.add(recents2);

        return recentsList;
    }

    @Before
    public void setup() {
        String config = "{\"billerDecryptionKey\":\"Zi5F0boOKlfZiq0Bs4I7CzY+Y+FOh7OXS2O3paEITTg=\",\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"newAccountServices\": [\"mobile\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        MockitoAnnotations.initMocks(this);
        recentService = new RecentService(recentsRepository, serviceConfig);
    }




    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(RecentsServiceImpl.class));

    private List<Recents> mockCCRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1221334");
        recentsPrimaryKey.setOperator("hdcf");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("credit card");
        recents.setMcn("XXXX XXXX XXXX 1234");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        return recentsList;
    }

    private List<Recents> mockBPRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("business payment");
        recentsPrimaryKey.setRechargeNumber("********");
        recentsPrimaryKey.setOperator("biller_businesspayment_bank_vendorpayment");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("postpaid");
        recents.setRechargeNumber7("**********");
        recents.setTxnAmount(4000.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        return recentsList;
    }
    private List<Recents> mockNonCCRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("**********");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("prepaid");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        return recentsList;
    }

    private List<Recents> mockMultiNonCCRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("**********");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("prepaid");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);


        Recents recents2=new Recents();
        RecentsPrimaryKey recentsPrimaryKey2=new RecentsPrimaryKey();
        recentsPrimaryKey2.setCustomerId(1123L);
        recentsPrimaryKey2.setService("mobile");
        recentsPrimaryKey2.setRechargeNumber("**********");
        recentsPrimaryKey2.setOperator("jio");
        recentsPrimaryKey2.setPlanBucket("Special Recharge");
        recents2.setKey(recentsPrimaryKey2);
        recents2.setPayType("prepaid");
        recents2.setTxnAmount(100.0);
        recentsList.add(recents2);

        return recentsList;
    }

    @Test
    public void testRemoveRecentForCC() throws RecentDataToKafkaException {
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockCCRecents());
        when(serviceConfig.getAgentLimit()).thenReturn(300);
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("XXXX XXXX XXXX 1234");
        deleteRequestBody.setPaytype("credit card");
        deleteRequestBody.setPar("1221334");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        verify(deleteCreditCardRecentCard, times(1))
                .deleteRecentCard(recentsArgument.capture(),deleteRequestBodyArgument.capture());
        TestCase.assertEquals("1221334", recentsArgument.getValue().getKey().getRechargeNumber());
    }


    @Test
    public void testRemoveRecentForBP() throws RecentDataToKafkaException {
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockBPRecents());
        when(serviceConfig.getAgentLimit()).thenReturn(300);
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("**********");
        deleteRequestBody.setBillerAccountId(********);
        deleteRequestBody.setPaytype("postpaid");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        verify(deleteNonCreditCardRecentCard, times(1))
                .deleteRecentCard(recentsArgument.capture(),deleteRequestBodyArgument.capture());
        TestCase.assertEquals("********", recentsArgument.getValue().getKey().getRechargeNumber());
    }

    @Test
    public void testRemoveRecentForCCFinalResponse(){
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockCCRecents());
        when(serviceConfig.getAgentLimit()).thenReturn(300);
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("XXXX XXXX XXXX 1234");
        deleteRequestBody.setPaytype("credit card");
        deleteRequestBody.setPar("1221334");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        NickNameResponse nickNameResponse=recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        TestCase.assertEquals(200,nickNameResponse.getStatusCode().intValue());
    }

    @Test
    public void testRemoveSmartRecentResponse(){
        long customerId=1123L;
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("");
        deleteRequestBody.setService("electricity");
        deleteRequestBody.setOperator("operator");
        NickNameResponse nickNameResponse=recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        TestCase.assertEquals(200,nickNameResponse.getStatusCode().intValue());
        verify(smartRecentsRepository,times(1)).deleteRecentByCustomerIdAndServiceAndOperator(any(),any(),any());

    }

    @Test
    public void testRemoveRecentForCCSMSFinalResponse(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("XXXX XXXX XXXX 1234");
        recentsPrimaryKey.setOperator("hdcf");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("credit card");
        recents.setMcn("XXXX XXXX XXXX 1234");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(recentsList);
        when(serviceConfig.getAgentLimit()).thenReturn(300);
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("XXXX XXXX XXXX 1234");
        deleteRequestBody.setPaytype("credit card");
        deleteRequestBody.setPar(null);
        deleteRequestBody.setCin(null);
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        NickNameResponse nickNameResponse=recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        TestCase.assertEquals(200,nickNameResponse.getStatusCode().intValue());
    }

    @Test
    public void testRemoveRecentForNonCC() throws RecentDataToKafkaException {
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockNonCCRecents());
        when(serviceConfig.getAgentLimit()).thenReturn(300);
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("**********");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        verify(deleteNonCreditCardRecentCard, times(1))
                .deleteRecentCard(recentsArgument.capture(),deleteRequestBodyArgument.capture());
        TestCase.assertEquals("**********", recentsArgument.getValue().getKey().getRechargeNumber());
    }

    @Test
    public void testRemoveMultipleRecentForNonCC() throws RecentDataToKafkaException {
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockMultiNonCCRecents());
        when(serviceConfig.getAgentLimit()).thenReturn(300);
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("**********");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        verify(deleteNonCreditCardRecentCard, times(2))
                .deleteRecentCard(recentsArgument.capture(),deleteRequestBodyArgument.capture());
        Recents args1=recentsArgument.getAllValues().get(0);
        Recents args2=recentsArgument.getAllValues().get(1);
        TestCase.assertEquals("**********", args1.getKey().getRechargeNumber());
        TestCase.assertEquals("airtel", args1.getKey().getOperator());
        TestCase.assertEquals("jio", args2.getKey().getOperator());
        TestCase.assertEquals("**********", args2.getKey().getRechargeNumber());
    }

    private FetchRecentsRequest mockFetchRecentsByRechargeNumberRequest(){
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setService("mobile");
        fetchRecentsRequest.setOperator("airtel");
        fetchRecentsRequest.setRechargeNumber("**********");
        return fetchRecentsRequest;
    }

    @Test
    public void testGetRecentsWithNullFromDb(){
        List<Recents> recentsList = mockRecents();
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        List<Long> customerIds = new ArrayList<>();
        customerIds.add(112L);
        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(customerIds,fetchRecentsRequest.getService(),fetchRecentsRequest.getRechargeNumber(),fetchRecentsRequest.getOperator())).thenReturn(null);
        List<Recents> res = recentService.getRecents(fetchRecentsRequest,customerIds);
        TestCase.assertNull(res);
    }

    @Test
    public void testGetRecentsWithNonNullFromDb(){
        List<Recents> recentsList = mockRecents();
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        List<Long> customerIds = new ArrayList<>();
        customerIds.add(112L);
        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(customerIds,fetchRecentsRequest.getService(),fetchRecentsRequest.getRechargeNumber(),fetchRecentsRequest.getOperator())).thenReturn(mockRecents());
        List<Recents> res = recentService.getRecents(fetchRecentsRequest,customerIds);
        TestCase.assertEquals(recentsList,res);
    }

    @Test()
    public void testGetRecentsWithExceptionFromDb(){
        List<Recents> recentsList = mockRecents();
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        List<Long> customerIds = new ArrayList<>();
        customerIds.add(112L);
        when(recentsRepository.findByCustomerIdINAndServiceAndRechargeNumberAndOperator(customerIds,fetchRecentsRequest.getService(),fetchRecentsRequest.getRechargeNumber(),fetchRecentsRequest.getOperator())).thenThrow(new CassandraInvalidQueryException("Invalid"));
        List<Recents> res = recentService.getRecents(fetchRecentsRequest,customerIds);
        TestCase.assertNull(res);
    }

    private BillerAccountKafkaModel getBillerAccountResponseModelMock(){

        BillerAccountKafkaModel billerAccountResponseModel = new BillerAccountKafkaModel();
        billerAccountResponseModel.setAccountId("fb4f7268507197e330f21ab6705b2cdd");
        billerAccountResponseModel.setId(1234L);
        billerAccountResponseModel.setBankAccountName("lata");
        billerAccountResponseModel.setIfscCode("HB 005");
        billerAccountResponseModel.setAccountStatus(1);
        billerAccountResponseModel.setPhoneNumber(12345668L);
        billerAccountResponseModel.setCategoryId(215903L);
        billerAccountResponseModel.setName("mann");
        billerAccountResponseModel.setMaskAccountId("XX 45");
        billerAccountResponseModel.setUpdatedAt(new Date().toString());
        billerAccountResponseModel.setCreatedAt(new Date().toString());
        billerAccountResponseModel.setProductId(274545379L);
        billerAccountResponseModel.setCustomerId(310329952L);


        return billerAccountResponseModel;

    }



    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithRentBillerInfo() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenNoExistingData(Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.insertBillerAccountRecents(model);

    }

    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithBPBillerInfo() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("business payment");
        product.setOperator("business payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenNoExistingData(Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.insertBillerAccountRecents(model);

    }

    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithTuitionBillerInfo() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("tuition fees");
        product.setOperator("tuition fees");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(292581L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenNoExistingData(Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.insertBillerAccountRecents(model);


    }

    @Test
    public void testNullProductDetails() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        model.setProductId(274L);
        recentService.insertBillerAccountRecents(model);


    }


    @Test(expected = BillerAccountListenerException.class)
    public void testIsUpdatedFalseForTuitionFee() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("tuition fees");
        product.setOperator("tuition fees");
        product.setCategoryId(111L);
        product.setStatus(1);
        model.setCategoryId(292581L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenNoExistingData(Mockito.any(),Mockito.any())).thenReturn(false);
        recentService.insertBillerAccountRecents(model);


    }

    @Test(expected = BillerAccountListenerException.class)
    public void testIsUpdatedFalseForRent() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(111L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenNoExistingData(Mockito.any(),Mockito.any())).thenReturn(false);
        recentService.insertBillerAccountRecents(model);


    }

    @Test(expected = BillerAccountListenerException.class)
    public void testIsUpdatedFalseForBP() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("business payment");
        product.setOperator("business payment");
        product.setCategoryId(111L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenNoExistingData(Mockito.any(),Mockito.any())).thenReturn(false);
        recentService.insertBillerAccountRecents(model);


    }



    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithBillerInfoWithExistingRecords() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        model.setCategoryId(215903L);
        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        recentService.insertBillerAccountRecents(model);


    }

    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithBillerInfoWithExistingRecordsBP() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        model.setCategoryId(215903L);
        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("business payment");
        product.setOperator("business payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        recentService.insertBillerAccountRecents(model);


    }

    @Test
    public void testDeleteValidRecordButParsingExceptionOccurred() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();

        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        recents.setUpdatedAt(new Date(System.currentTimeMillis() - 3600 * 1000));
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(111L);
        model.setUpdatedAt(new Date().toString());
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.deleteValidRecord(model);


    }

    @Test
    public void testDeleteValidRecordButParsingExceptionOccurredBP() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();

        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        recents.setUpdatedAt(new Date(System.currentTimeMillis() - 3600 * 1000));
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("business payment");
        product.setOperator("business payment");
        product.setCategoryId(111L);
        model.setUpdatedAt(new Date().toString());
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.deleteValidRecord(model);


    }


    @Test
    public void testDeleteValidRecord() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();

        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        recents.setUpdatedAt(new Date(System.currentTimeMillis() - 3600 * 1000));
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("tuition fees");
        product.setOperator("tuition fees");
        product.setCategoryId(111L);
        String currentDate = new SimpleDateFormat(RECENTS_DATE_FORMAT).format(new Date());
        model.setUpdatedAt(currentDate);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.deleteValidRecord(model);


    }


    @Test
    public void testInValidRecordFortDeletion() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();

        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        recents.setKey(key);
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setService("rent payment");
        recents.setUpdatedAt(new Date());
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(111L);
        String currentDate = new SimpleDateFormat(RECENTS_DATE_FORMAT).format(new Date(System.currentTimeMillis() - 3600 * 1000));
        model.setUpdatedAt(currentDate);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.deleteValidRecord(model);


    }


    @Test
    public void testInValidRecordForDeletion() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();

        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        recents.setKey(key);
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setService("rent payment");
        recents.setUpdatedAt(new Date());
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("tuition fees");
        product.setOperator("tuition fees");
        product.setCategoryId(111L);
        String currentDate = new SimpleDateFormat(RECENTS_DATE_FORMAT).format(new Date(System.currentTimeMillis() - 3600 * 1000));
        model.setUpdatedAt(currentDate);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.deleteValidRecord(model);


    }


    @Test
    public void testIsNotTxnRecord(){

        Recents recents=new Recents();
        recents.setMcn("1234");
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        recents.setKey(key);
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setService("rent payment");
        Map<String,Long> pending=new HashMap<>();
        pending.put(Constants.TXN_ID,12345L);
        //recents.setLastPendingTxn(String.valueOf(pending));
        recents.setUpdatedAt(new Date());
        recentService.isNotTxnRecord(recents);

    }


    @Test
    public void testIsNotTxnRecordBP(){

        Recents recents=new Recents();
        recents.setMcn("1234");
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        recents.setKey(key);
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setService("business payment");
        Map<String,Long> pending=new HashMap<>();
        pending.put(Constants.TXN_ID,12345L);
        //recents.setLastPendingTxn(String.valueOf(pending));
        recents.setUpdatedAt(new Date());
        recentService.isNotTxnRecord(recents);

    }
    @Test
    public void testIsNotTxnRecord2()  {

        Recents recents=new Recents();
        recents.setMcn("1234");
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        recents.setKey(key);
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setService("rent payment");
        Map<String,Long> last=new HashMap<>();
        last.put(Constants.TXN_ID,12345L);
        //recents.setLastFailureTxn(new ObjectMapper().writeValueAsString(last));
        recents.setUpdatedAt(new Date());
        recentService.isNotTxnRecord(recents);

    }
    @Test
    public void testIsNotTxnRecord1(){

        Recents recents=new Recents();
        recents.setMcn("1234");
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        recents.setKey(key);
        recents.getKey().setRechargeNumber("1234");
        recents.getKey().setService("rent payment");
        recents.setUpdatedAt(new Date());
        recentService.isNotTxnRecord(recents);

    }



    @Test
    public void testGetOrderIdFromPendingAndFailureTxn(){

        recentService.getOrderIdFromPendingAndFailureTxn(null);

    }

    @Test
    public void testFetchRecentsFromDb(){

        recentService.fetchRecentsFromDb(12345L,"RENT ","1234","OPERATOR","");

    }


    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithTuitionBillerInfo1() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        model.setPhoneNumber(null);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("tuition fees");
        product.setOperator("tuition fees");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(292581L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenNoExistingData(Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        recentService.insertBillerAccountRecents(model);


    }

    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithBillerInfoWithExistingRecords1() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        model.setPhoneNumber(null);
        model.setCategoryId(215903L);
        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        recentService.insertBillerAccountRecents(model);


    }


    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithBillerInfoWithExistingRecords1BP() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        model.setPhoneNumber(null);
        model.setCategoryId(215903L);
        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("business payment");
        product.setOperator("business payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        recentService.insertBillerAccountRecents(model);


    }


    @Test(expected = BillerAccountListenerException.class)
    public void testInsertRecentsWithBillerInfoWithExistingRecords2() throws BillerAccountListenerException {
        BillerAccountKafkaModel model = getBillerAccountResponseModelMock();
        model.setPhoneNumber(null);
        model.setCategoryId(215903L);
        List<Recents> existingRecents =new ArrayList<>();
        Recents recents=new Recents();
        recents.setMcn("1234");
        existingRecents.add(recents);
        Product product = new Product();
        product.setProductId(274545379L);
        product.setService("tuition fees");
        product.setOperator("tuition fess");
        product.setCategoryId(215903L);
        product.setStatus(1);
        model.setCategoryId(215903L);
        model.setAccountId("6077D2D29D675720E533913308C89EB2");
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(existingRecents);
        recentService.insertBillerAccountRecents(model);


    }

    @Test
    public void testFetchLatestRecentWithReferenceId() {
        Long customerId = 123L;
        String service = "financial_services";
        List<Recents> recentsList = new ArrayList<>();
        Recents recent1 = new Recents();
        recent1.setCin("274379");
        recent1.setPar("284923");
        recent1.setProductId(1234L);
        recent1.setMcn("XXXX XXXX XXXX 2356");
        recent1.setOperator("sbi");
        recent1.setUpdatedAt(new Date());
        recentsList.add(recent1);
        when(recentsRepository.findByCustomerIdAndService(customerId, service)).thenReturn(recentsList);
        Product product = new Product();
        product.setProductId(1234L);
        product.setService("sbi");
        product.setOperator("sbi");
        product.setCategoryId(215903L);
        CVRProductCache.getInstance().addProductDetails(product);
        List<Recents> result = recentService.fetchLatestMatchingRecent(customerId, service);
        verify(recentsRepository).findByCustomerIdAndService(customerId, service);
        TestCase.assertNotNull(result);
        TestCase.assertEquals("274379", result.get(0).getCin());
        TestCase.assertEquals("284923", result.get(0).getPar());
        TestCase.assertEquals("XXXX XXXX XXXX 2356", result.get(0).getMcn());
        TestCase.assertEquals("sbi", result.get(0).getOperator());
    }

    @Test(expected = NullPointerException.class)
    public void test_updateP2pAndSmsBillsInRecent_where_planBucketIsCustomerPlanBucketInReminderCDC() throws RecentDataToKafkaException {
        RecentDataToKafkaService dataToKafkaServiceMock = mock(RecentDataToKafkaService.class);
        List<Recents> recents = mockRecents();
        recents.get(0).getKey().setPlanBucket(Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET);
        Recents finalRecent = new Recents();
        finalRecent.setKey(new RecentsPrimaryKey());
        String msg = "{\"ts_ms\":1665403274444,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":1665391901241,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-1664518904681.log\",\"pos\":16287689,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":65552050,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":1665391901241,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":1665391901241,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":1665391901241,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":1665391901241,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\",\\\"exhausted_date\\\":\\\"2024-05-07 19:09:00\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        ArrayList<Recents> existingRecents = new ArrayList<>();
        existingRecents.add(recents.get(1));
        doNothing().when(dataToKafkaServiceMock).pushRecentDataToKafka(any(), any());
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(anyLong(), anyString(), anyString(), anyString(), anyString())).thenReturn(existingRecents);
        recentService.updateP2pAndSmsBillsInRecent(recents, finalRecent, reminderCDC);
        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> date = ArgumentCaptor.forClass(Date.class);
        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(), date.capture(), ttl.capture());
    }
    @Test
    public void isSkippedDueToExistingRURecordForPostpaid() throws Exception{
        when(serviceConfig.getTimeDiffThresholdBetweenRecentsAndCDC()).thenReturn(60);
        Method method = recentService.getClass().getDeclaredMethod("isSkippedDueToExistingRURecord", Recents.class, ReminderCDC.class);
        method.setAccessible(true);

        Recents recents = mockRecents("electricity", "postpaid", Constants.VALIDATION_SYNC, "2024-05-08 09:30:00");
        ReminderCDC reminderCDC = mockReminderCDC("electricity", "postpaid", Constants.EVENTSOURCE_SMS, "2024-05-08 10:00:00");

        boolean isSkippedDueToExistingRURecord = (boolean) method.invoke(recentService, recents, reminderCDC);
        TestCase.assertTrue(isSkippedDueToExistingRURecord);
    }

    @Test
    public void isSkippedDueToExistingRURecordForMobilePrepaid() throws Exception{
        when(serviceConfig.getTimeDiffThresholdBetweenRecentsAndCDC()).thenReturn(60);
        List<String> prepaidServices = new ArrayList<>();
        prepaidServices.add("mobile");
        prepaidServices.add("dth");
        when(serviceConfig.getPrepaidServicesEligibleForNonRuValidation()).thenReturn(prepaidServices);

        Method method = recentService.getClass().getDeclaredMethod("isSkippedDueToExistingRURecord", Recents.class, ReminderCDC.class);
        method.setAccessible(true);

        Recents recents = mockRecents("mobile", "prepaid", Constants.VALIDATION_SYNC, "2024-05-08 09:30:00");
        ReminderCDC reminderCDC = mockReminderCDC("mobile", "prepaid", Constants.EVENTSOURCE_SMS, "2024-05-08 10:00:00");

        boolean isSkippedDueToExistingRURecord = (boolean) method.invoke(recentService, recents, reminderCDC);
        TestCase.assertTrue(isSkippedDueToExistingRURecord);

        Recents recents1 = mockRecents("mobile", "prepaid", Constants.EVENTSOURCE_VALIDATION, "2024-05-08 09:30:00");
        ReminderCDC reminderCDC1 = mockReminderCDC("mobile", "prepaid", Constants.EVENTSOURCE_SMS, "2024-05-08 10:00:00");

        boolean isSkippedDueToExistingRURecord1 = (boolean) method.invoke(recentService, recents1, reminderCDC1);
        TestCase.assertFalse(isSkippedDueToExistingRURecord1);

        Recents recents2 = mockRecents("dth", "prepaid", Constants.EVENTSOURCE_VALIDATION, "2024-05-08 09:30:00");
        ReminderCDC reminderCDC2 = mockReminderCDC("dth", "prepaid", Constants.EVENTSOURCE_SMS, "2024-05-08 10:00:00");

        boolean isSkippedDueToExistingRURecord2 = (boolean) method.invoke(recentService, recents2, reminderCDC2);
        TestCase.assertFalse(isSkippedDueToExistingRURecord2);
    }

    @Test
    public void isSkippedDueToExistingRURecordForOldExistingRecord() throws Exception{
        when(serviceConfig.getTimeDiffThresholdBetweenRecentsAndCDC()).thenReturn(60);
        Method method = recentService.getClass().getDeclaredMethod("isSkippedDueToExistingRURecord", Recents.class, ReminderCDC.class);
        method.setAccessible(true);

        Recents recents = mockRecents("electricity", "postpaid", Constants.VALIDATION_SYNC, "2024-04-08 09:30:00");
        ReminderCDC reminderCDC = mockReminderCDC("electricity", "postpaid", Constants.EVENTSOURCE_SMS, "2024-05-08 10:00:00");

        boolean isSkippedDueToExistingRURecord = (boolean) method.invoke(recentService, recents, reminderCDC);
        TestCase.assertFalse(isSkippedDueToExistingRURecord);
    }

    @Test
    public void testCreateTagsForMetricCategoryName() throws Exception{
        Method method = recentService.getClass().getDeclaredMethod("createTagsForRecentsRecordMetric", String.class, Recents.class);
        method.setAccessible(true);

        Recents recents = mockRecents("electricity", "postpaid", Constants.VALIDATION_SYNC, "2024-04-08 09:30:00");

        List<String> tags = (List<String>) method.invoke(recentService, Constants.MetricConstants.MESSAGE_SKIPPED_TYPE, recents);
        TestCase.assertEquals(3, tags.size());
        TestCase.assertEquals(Constants.MetricConstants.API_KEY + ":" + Constants.MetricConstants.MESSAGE_SKIPPED_TYPE, tags.get(0));
        TestCase.assertEquals(Constants.MetricConstants.CATEGORY_METRIC_KEY+":electricity", tags.get(1));
        TestCase.assertEquals(Constants.MetricConstants.PAYTYPE_METRIC_KEY+":postpaid", tags.get(2));
    }


    @Test
    public void setIsPaytmVPAFlagForAutomaticWhenDataSkipped_withExistingExtra() throws Exception{
        Method method = RecentService.class.getDeclaredMethod("setIsPaytmVPAFlagForAutomaticWhenDataSkipped",Recents.class);
        method.setAccessible(true);
        Map<String, Object> existingExtra = new HashMap<>();
        existingExtra.put("someKey", "someValue");
        String existingExtraJson = new ObjectMapper().writeValueAsString(existingExtra);
        Recents recents =new Recents();
        recents.setExtra(existingExtraJson);
        method.invoke(recentService,recents);
        String expectedExtraJson = "{\"someKey\":\"someValue\",\"isPaytmVPA\":0}";
        TestCase.assertEquals(expectedExtraJson, recents.getExtra());
    }
    @Test
    public void setIsPaytmVPAFlagForAutomaticWhenDataSkipped_withNullExtra() throws Exception {
        Method method = RecentService.class.getDeclaredMethod("setIsPaytmVPAFlagForAutomaticWhenDataSkipped",Recents.class);
        method.setAccessible(true);
        Recents recents = new Recents();
        recents.setExtra(null);
        method.invoke(recentService,recents);
        TestCase.assertEquals("{\"isPaytmVPA\":0}", recents.getExtra());
    }
    @Test
    public void setIsPaytmVPAFlagForAutomaticWhenDataSkipped_withEmptyExtra() throws Exception {
        Method method = RecentService.class.getDeclaredMethod("setIsPaytmVPAFlagForAutomaticWhenDataSkipped",Recents.class);
        method.setAccessible(true);
        Recents recents = new Recents();
        recents.setExtra("");
        method.invoke(recentService,recents);
        TestCase.assertEquals("{\"isPaytmVPA\":0}", recents.getExtra());
    }
    @Test
    public void setIsPaytmVPAFlagForAutomaticWhenDataSkipped_withInvalidJsonExtra() throws Exception {
        Method method = RecentService.class.getDeclaredMethod("setIsPaytmVPAFlagForAutomaticWhenDataSkipped",Recents.class);
        method.setAccessible(true);
        Recents recents = new Recents();
        recents.setExtra("{cnd");
        method.invoke(recentService,recents);
        TestCase.assertEquals("{cnd", recents.getExtra());
    }

    private Recents mockRecents(String service, String payType, String eventSource, String updatedAt) {
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setRechargeNumber("9999900000");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType(payType);
        recents.setTxnAmount(100.0);
        recents.setEventSource(eventSource);
        recents.setUpdatedAt(DateUtil.stringToDate(updatedAt, "yyyy-MM-dd HH:mm:ss"));
        return recents;
    }

    private ReminderCDC mockReminderCDC(String service, String payType, String eventSource, String updatedAt) {
        ReminderCDC reminderCDC = new ReminderCDC();
        reminderCDC.setRecoveryPacket(true);
        ReminderAfter after = new ReminderAfter();
        reminderCDC.setAfter(after);
        after.setService(mockStringEntity(service));
        after.setPaytype(mockStringEntity(payType));
        after.setRechargeNumber(mockStringEntity("9999900000"));
        StringEntity stringEntity = new StringEntity();
        stringEntity.setValue(eventSource);
        after.setEventSource(stringEntity);
        Date updatedAtDate = DateUtil.stringToDate(updatedAt, "yyyy-MM-dd HH:mm:ss");
        if(updatedAtDate != null) {
            after.setUpdatedAt(mockLongEntity(updatedAtDate.getTime()));
        }
        return reminderCDC;
    }

    private StringEntity mockStringEntity(String value) {
        StringEntity stringEntity = new StringEntity();
        stringEntity.setValue(value);
        return stringEntity;
    }

    private LongEntity mockLongEntity(Long value) {
        LongEntity longEntity = new LongEntity();
        longEntity.setValue(value);
        return longEntity;
    }
//    @Test(expected = NullPointerException.class)
//    public void test_updateP2pAndSmsBillsInRecent_where_planBucketIsCustomerPlanBucketInReminderCDC() throws RecentDataToKafkaException {
//        RecentDataToKafkaService dataToKafkaServiceMock = mock(RecentDataToKafkaService.class);
//        List<Recents> recents = mockRecents();
//        recents.get(0).getKey().setPlanBucket(Constants.DATA_EXHAUST.DATA_EXHAUST_PLAN_BUCKET);
//        Recents finalRecent = new Recents();
//        finalRecent.setKey(new RecentsPrimaryKey());
//        String msg = "{\"ts_ms\":1665403274444,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":1665391901241,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-1664518904681.log\",\"pos\":16287689,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":65552050,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":1665391901000,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":1665391901241,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":1665391901241,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":1665391901241,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":1665391901241,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\",\\\"exhausted_date\\\":\\\"2024-05-07 19:09:00\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
//        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
//        ArrayList<Recents> existingRecents = new ArrayList<>();
//        existingRecents.add(recents.get(1));
//        doNothing().when(dataToKafkaServiceMock).pushRecentDataToKafka(any(), any());
//        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(anyLong(), anyString(), anyString(), anyString(), anyString())).thenReturn(existingRecents);
//        recentService.updateP2pAndSmsBillsInRecent(recents, finalRecent, reminderCDC);
//        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
//        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
//        ArgumentCaptor<Date> date = ArgumentCaptor.forClass(Date.class);
//        verify(recentsRepository).updateRecentWhenDataAlreadyExist(recent.capture(), date.capture(),ttl.capture());
//
//    }

}
