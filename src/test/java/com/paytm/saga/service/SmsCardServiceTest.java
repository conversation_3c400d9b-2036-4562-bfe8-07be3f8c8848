package com.paytm.saga.service;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import java.util.*;

import com.paytm.saga.common.exception.PgServiceException;
import com.paytm.saga.common.exception.SavedCardServiceException;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ReminderHistory;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.SmsCardException;
import com.paytm.saga.service.external.ReminderService;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;

import junit.framework.TestCase;

@RunWith(MockitoJUnitRunner.class)
public class SmsCardServiceTest extends TestCase {
	@InjectMocks
	SmsCardService smsCardService;
	@Mock
	ReminderService reminderService;
	private Long customerId;
	@Rule
	public ExpectedException exceptionRule = ExpectedException.none();

	@Before
	public void setup() {
		customerId = 11065108L;
	}

	private List<SmsCard> getSMSCardResponseForSingleServiceMockResponse() {
		String s = "{\"status\":200,\"data\":{\"financial services\":[{\"id\":9,\"customer_id\":111,\"recharge_number\":\"XXXX XXXX XXXX 1234\",\"product_id\":112,\"operator\":\"hdfc\",\"amount\":400,\"due_date\":\"2018-11-28T06:14:02.000Z\",\"bill_fetch_date\":\"2018-11-20T12:31:12.000Z\",\"paytype\":\"credit card\",\"service\":\"financial services\",\"customer_mobile\":\"**********\",\"customer_email\":\"<EMAIL>\",\"status\":7,\"createdAt\":\"2018-11-20T11:16:28.000Z\",\"updatedAt\":\"2018-11-28T06:14:02.000Z\",\"billDate\":\"2018-11-28T06:14:02.000Z\",\"notification_status\":1,\"card_network\":\"BLANK\",\"bank_name\":\"hdfc\",\"customerOtherInfo\":{\"currentBillAmount\":100}}]}}";
		GetSmsCardsResponse getSmsCardsResponse = JsonUtils.parseJson(s, GetSmsCardsResponse.class);
		List<SmsCard> smsCards = new ArrayList<>();
		for(Map.Entry<String,List<SmsCard>> smsCardsData : getSmsCardsResponse.getData().entrySet()){
			for (SmsCard smsCard : smsCardsData.getValue()){
				smsCards.add(smsCard);
			}
		}
		return smsCards;
	}

	private List<SmsCard> getSMSCardResponseForSingleServiceMockResponseWithDueAmount() {
		String s = "{\"status\":200,\"data\":{\"financial services\":[{\"id\":9,\"customer_id\":111,\"recharge_number\":\"XXXX XXXX XXXX 1234\",\"product_id\":112,\"operator\":\"hdfc\",\"amount\":400,\"due_amount\":400.34,\"due_date\":\"2018-11-28T06:14:02.000Z\",\"bill_fetch_date\":\"2018-11-20T12:31:12.000Z\",\"paytype\":\"credit card\",\"service\":\"financial services\",\"customer_mobile\":\"**********\",\"customer_email\":\"<EMAIL>\",\"status\":7,\"createdAt\":\"2018-11-20T11:16:28.000Z\",\"updatedAt\":\"2018-11-28T06:14:02.000Z\",\"billDate\":\"2018-11-28T06:14:02.000Z\",\"notification_status\":1,\"card_network\":\"BLANK\",\"bank_name\":\"hdfc\",\"customerOtherInfo\":{\"currentBillAmount\":100}}]}}";
		GetSmsCardsResponse getSmsCardsResponse = JsonUtils.parseJson(s, GetSmsCardsResponse.class);
		List<SmsCard> smsCards = new ArrayList<>();
		for(Map.Entry<String,List<SmsCard>> smsCardsData : getSmsCardsResponse.getData().entrySet()){
			for (SmsCard smsCard : smsCardsData.getValue()){
				smsCards.add(smsCard);
			}
		}
		return smsCards;
	}
	private List<SmsCard> getSMSCardResponseForMultipleServiceMockResponse(){
		String s = "{\"status\":200,\"data\":{\"financial services\":[{\"id\":9,\"customer_id\":111,\"recharge_number\":\"XXXX XXXX XXXX 1234\",\"product_id\":112,\"operator\":\"hdfc\",\"amount\":400,\"due_date\":\"2018-11-28T06:14:02.000Z\",\"bill_fetch_date\":\"2018-11-20T12:31:12.000Z\",\"paytype\":\"credit card\",\"service\":\"financial services\",\"customer_mobile\":\"**********\",\"customer_email\":\"<EMAIL>\",\"status\":7,\"createdAt\":\"2018-11-20T11:16:28.000Z\",\"updatedAt\":\"2018-11-28T06:14:02.000Z\",\"billDate\":\"2018-11-28T06:14:02.000Z\",\"notification_status\":1,\"card_network\":\"BLANK\",\"bank_name\":\"hdfc\",\"customerOtherInfo\":{\"currentBillAmount\":100}}],\"mobile\":[{\"id\":9,\"customer_id\":111,\"recharge_number\":\"XXXX XXXX XXXX 2211\",\"product_id\":112,\"operator\":\"hdfc\",\"amount\":400,\"due_date\":\"2018-11-28T06:14:02.000Z\",\"bill_fetch_date\":\"2018-11-20T12:31:12.000Z\",\"paytype\":\"credit card\",\"service\":\"mobile\",\"customer_mobile\":\"**********\",\"customer_email\":\"<EMAIL>\",\"status\":7,\"createdAt\":\"2018-11-20T11:16:28.000Z\",\"updatedAt\":\"2018-11-28T06:14:02.000Z\",\"billDate\":\"2018-11-28T06:14:02.000Z\",\"notification_status\":1,\"card_network\":\"BLANK\",\"bank_name\":\"hdfc\",\"customerOtherInfo\":{\"currentBillAmount\":100}}]}}";
		GetSmsCardsResponse getSmsCardsResponse = JsonUtils.parseJson(s, GetSmsCardsResponse.class);
		List<SmsCard> smsCards = new ArrayList<>();
		for(Map.Entry<String,List<SmsCard>> smsCardsData : getSmsCardsResponse.getData().entrySet()){
			for (SmsCard smsCard : smsCardsData.getValue()){
				smsCards.add(smsCard);
			}
		}
		return smsCards;
	}

	@Test
	public void getSmsCardsWithValidCardsWithExpiredDueDateWithSingleService() throws SmsCardException {
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(getSMSCardResponseForSingleServiceMockResponse());
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
	}

	@Test
	public void getSmsCardsWithValidCardsWithExpiredDueDateWithMultipleService() throws SmsCardException {
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(getSMSCardResponseForMultipleServiceMockResponse());
		List<String> services = new ArrayList<>();
		services.add("financial services");
		services.add("mobile");
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, services);
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(2,savedCardResponse.getSavedCardApiResponses().size());
		assertEquals("financial services",savedCardResponse.getSavedCardApiResponses().get(0).getProduct().getService());
		assertEquals("XXXX XXXX XXXX 1234",savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
		assertEquals("mobile",savedCardResponse.getSavedCardApiResponses().get(1).getProduct().getService());
		assertEquals("XXXX XXXX XXXX 2211",savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
	}
	@Test
	public void getSmsCardsWithValidCardsWithValidDueDateWithSingleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForSingleServiceMockResponse();
		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(SavedCardBillStateEnum.MIN_DUE,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
	}

	@Test
	public void getSmsCardsWithValidCardsWithValidDueDateWithSingleServiceDueAmount() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForSingleServiceMockResponseWithDueAmount();
		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(SavedCardBillStateEnum.MIN_DUE,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals(400.34,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getAmount());
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
	}

	@Test
	public void getSmsCardsWithValidCardsWithValidDueDateWithMultipleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForMultipleServiceMockResponse();
		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		smsCards.get(1).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		List<String> services = new ArrayList<>();
		services.add("financial services");
		services.add("mobile");
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, services);
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(2,savedCardResponse.getSavedCardApiResponses().size());
		assertEquals(SavedCardBillStateEnum.MIN_DUE,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
		assertEquals(SavedCardBillStateEnum.MIN_DUE,
				savedCardResponse.getSavedCardApiResponses().get(1).getBills().get(0).getBillState());
		assertEquals("XXXX XXXX XXXX 2211",
				savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumberForDisplay());
	}
	@Test
	public void getSmsCardsWithValidCardsWithValidDueDateCase2WithSingleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForSingleServiceMockResponse();
		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		smsCards.get(0).setRecharge_number("XXXX XXXX XXXX XX11");
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(SavedCardBillStateEnum.MIN_DUE,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals("XXXX XXXX XXXX XX11",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
	}

	@Test
	public void getSmsCardsWithValidCardsWithValidDueDateMinDueAndPaidOutsideWithSingleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForSingleServiceMockResponse();
		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		smsCards.get(0).setStatus(14);
		smsCards.get(0).setExtra_info(new ExtraInfo());
		smsCards.get(0).getExtra_info().setLast_paid_amount(100.0);
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(SavedCardBillStateEnum.MIN_DUE,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
	}



	@Test
	public void getSmsCardsWithValidCardsWithValidDueDateMinDueAndPaidOutsideWithMultipleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForMultipleServiceMockResponse();
		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		smsCards.get(0).setStatus(14);
		smsCards.get(0).setExtra_info(new ExtraInfo());
		smsCards.get(0).getExtra_info().setLast_paid_amount(100.0);
		smsCards.get(1).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		smsCards.get(1).setStatus(14);
		smsCards.get(1).setExtra_info(new ExtraInfo());
		smsCards.get(1).getExtra_info().setLast_paid_amount(100.0);
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		List<String> services = new ArrayList<>();
		services.add("financial services");
		services.add("mobile");
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, services);
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(2,savedCardResponse.getSavedCardApiResponses().size());
		assertEquals(SavedCardBillStateEnum.MIN_DUE,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
		assertEquals(SavedCardBillStateEnum.MIN_DUE,
				savedCardResponse.getSavedCardApiResponses().get(1).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(1).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 2211",
				savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumberForDisplay());
	}
//	@Test
//	public void getSmsCardsWithValidCardsWithValidDueDateMinDueAndPaidOutside() throws SmsCardException {
//		List<SmsCard> smsCards = getSMSCardResponseForSingleServiceMockResponse();
//		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
//		smsCards.get(0).setStatus(14);
//		smsCards.get(0).setExtra_info(new ExtraInfo());
//		smsCards.get(0).getExtra_info().setLast_paid_amount(100.0);
//		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
//		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
//		assertNotNull(savedCardResponse.getSavedCardApiResponses());
//		assertEquals(SavedCardBillStateEnum.MIN_DUE,
//				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
//		assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
//		assertEquals("XXXX XXXX XXXX 1234",
//				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
//	}

	@Test
	public void getSavedCardInfoWithFullyPaidAndPaidOutsideWithSingleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForSingleServiceMockResponse();
		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		smsCards.get(0).setBillDate(DateUtil.dateIncrDecr(new Date(), 1));
		smsCards.get(0).setStatus(14);
		smsCards.get(0).setAmount(0.0);
		smsCards.get(0).setExtra_info(new ExtraInfo());
		smsCards.get(0).getExtra_info().setLast_paid_amount(100.0);
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(SavedCardBillStateEnum.FULLY_PAID,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
	}

	@Test
	public void getSavedCardInfoWithFullyPaidAndPaidOutsideWithMultipleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForMultipleServiceMockResponse();
		smsCards.get(0).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		smsCards.get(0).setBillDate(DateUtil.dateIncrDecr(new Date(), 1));
		smsCards.get(0).setStatus(14);
		smsCards.get(0).setAmount(0.0);
		smsCards.get(0).setExtra_info(new ExtraInfo());
		smsCards.get(0).getExtra_info().setLast_paid_amount(100.0);
		smsCards.get(1).setDue_date(DateUtil.dateIncrDecr(new Date(), 3));
		smsCards.get(1).setBillDate(DateUtil.dateIncrDecr(new Date(), 1));
		smsCards.get(1).setStatus(14);
		smsCards.get(1).setAmount(0.0);
		smsCards.get(1).setExtra_info(new ExtraInfo());
		smsCards.get(1).getExtra_info().setLast_paid_amount(100.0);
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		List<String> services = new ArrayList<>();
		services.add("financial services");
		services.add("mobile");
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, services);
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(2,savedCardResponse.getSavedCardApiResponses().size());
		assertEquals(SavedCardBillStateEnum.FULLY_PAID,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
		assertEquals(SavedCardBillStateEnum.FULLY_PAID,
				savedCardResponse.getSavedCardApiResponses().get(1).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(1).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 2211",
				savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumberForDisplay());
	}
	@Test
	public void getSavedCardInfoWithNoBillAndPaidOutsideWithSingleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForSingleServiceMockResponse();
		smsCards.get(0).setStatus(14);
		smsCards.get(0).setAmount(0.0);
		smsCards.get(0).setExtra_info(new ExtraInfo());
		smsCards.get(0).getExtra_info().setLast_paid_amount(100.0);
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, Collections.singletonList(Constants.FINANCIAL_SERVICE));
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(SavedCardBillStateEnum.NO_BILL,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
	}

	@Test
	public void getSavedCardInfoWithNoBillAndPaidOutsideWithMultipleService() throws SmsCardException {
		List<SmsCard> smsCards = getSMSCardResponseForMultipleServiceMockResponse();
		smsCards.get(0).setStatus(14);
		smsCards.get(0).setAmount(0.0);
		smsCards.get(0).setExtra_info(new ExtraInfo());
		smsCards.get(0).getExtra_info().setLast_paid_amount(100.0);
		smsCards.get(1).setStatus(14);
		smsCards.get(1).setAmount(0.0);
		smsCards.get(1).setExtra_info(new ExtraInfo());
		smsCards.get(1).getExtra_info().setLast_paid_amount(100.0);
		when(reminderService.fetchSmsCards(any(), any())).thenReturn(smsCards);
		List<String> services = new ArrayList<>();
		services.add("financial services");
		services.add("mobile");
		SavedCardResponse savedCardResponse = smsCardService.getSmsCards(customerId, services);
		assertNotNull(savedCardResponse.getSavedCardApiResponses());
		assertEquals(2,savedCardResponse.getSavedCardApiResponses().size());
		assertEquals(SavedCardBillStateEnum.NO_BILL,
				savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 1234",
				savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumberForDisplay());
		assertEquals(SavedCardBillStateEnum.NO_BILL,
				savedCardResponse.getSavedCardApiResponses().get(1).getBills().get(0).getBillState());
		assertEquals(savedCardResponse.getSavedCardApiResponses().get(1).getPaidOutside(),Constants.PAID_OUTSIDE_PAYTM);
		assertEquals("XXXX XXXX XXXX 2211",
				savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumberForDisplay());
	}





}
