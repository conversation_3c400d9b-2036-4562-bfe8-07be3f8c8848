package com.paytm.saga.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

import com.paytm.saga.dto.ThemeDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.dto.ThemeResponse;
import com.paytm.saga.model.CommonCache;
import com.paytm.saga.model.Theme;
import com.paytm.saga.repository.CommonCacheRepository;
import com.paytm.saga.repository.ThemeRepository;
import com.paytm.saga.util.JsonUtils;

public class ThemeServiceTest {
	@Mock
	private CommonCacheRepository commonCacheRepository;
	@Mock
	private ThemeRepository themeRepository;
	@InjectMocks
	public ThemeService themeService;

	@InjectMocks
	public NewThemeService newThemeService;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	public Theme mockThemeObj(String viewItem, String themeType) {
		Theme theme = new Theme();
		theme.setBgColor("808080");
		theme.setBorderColor("borderColor");
		theme.setDisplayValuesAlignment("displayValuesAlignment");
		theme.setHeadingColor("headingColor");
		theme.setIsAmount(true);
		theme.setKeyColor("keyColor");
		theme.setKeyFontWeight("keyFontWeight");
		theme.setLeftThumbnail("leftThumbnail");
		theme.setRichTextColor("richTextColor");
		theme.setRightThumbImage("rightThumbImage");
		theme.setShowDisplayValue("showDisplayValue");
		theme.setThemeType(themeType);
		theme.setValueColor("valueColor");
		theme.setValueFontWeight("valueFontWeight");
		theme.setViewItem(viewItem);
		return theme;
	}

	@Test
	public void fetchThemeTestCase1() {
		List<Theme> themes = new ArrayList<Theme>();
		Theme themeHeader1 = mockThemeObj("header1", "themeType1");
		themes.add(themeHeader1);
		Theme themeHeader2 = mockThemeObj("header2", "themeType1");
		themes.add(themeHeader2);
		Theme themeHeader3 = mockThemeObj("header3", "themeType1");
		themes.add(themeHeader3);
		Theme themeHeader4 = mockThemeObj("header4", "themeType1");
		themes.add(themeHeader4);
		Theme themeHeader5 = mockThemeObj("header5", "themeType1");
		themes.add(themeHeader5);
		Theme themeCta1 = mockThemeObj("cta1", "themeType1");
		themes.add(themeCta1);
		Theme themeCta2 = mockThemeObj("cta2", "themeType1");
		themes.add(themeCta2);
		Theme themeCta3 = mockThemeObj("cta3", "themeType1");
		themes.add(themeCta3);
		Theme themeCta4 = mockThemeObj("cta4", "themeType1");
		themes.add(themeCta4);
		Theme themeCta5 = mockThemeObj("cta5", "themeType1");
		themes.add(themeCta5);
		Theme themeDs1 = mockThemeObj("ds1", "themeType1");
		themes.add(themeDs1);
		Theme themeDs2 = mockThemeObj("ds2", "themeType1");
		themes.add(themeDs2);
		Theme themeDs3 = mockThemeObj("ds3", "themeType1");
		themes.add(themeDs3);
		Theme themeDs4 = mockThemeObj("ds4", "themeType1");
		themes.add(themeDs4);
		Theme themeDs5 = mockThemeObj("ds5", "themeType1");
		themes.add(themeDs5);
		Theme themeFooter1 = mockThemeObj("footer1", "themeType1");
		themes.add(themeFooter1);
		Theme themeFooter2 = mockThemeObj("footer2", "themeType1");
		themes.add(themeFooter2);
		
		Theme key=new Theme();
		key.setThemeHash("key");
		List<Theme> keys=new ArrayList<Theme>();
		keys.add(key);
		when(themeRepository.findByThemeType("theme_hash_key")).thenReturn(keys);
		when(themeRepository.findAll()).thenReturn(themes);
		ThemeResponse res=themeService.getThemes("Oldkey");
		ArgumentCaptor<CommonCache> argument = ArgumentCaptor.forClass(CommonCache.class);
		ArgumentCaptor<Integer> argument2 = ArgumentCaptor.forClass(Integer.class);
		verify(commonCacheRepository).save(argument.capture(), argument2.capture());
		assertEquals("key", res.getThemehash());
		
	}
	
	@Test
	public void fetchThemeTestCase2() {
		//List<Theme> themes = null;
		String themeStr="[{\"themeType\":\"themeType1\",\"themeDetail\":{\"header1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"footer1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"footer2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true}}}]";
		//themes = JsonUtils.parseJsonCI(themeStr, new ArrayList<Theme>().getClass());
		
		Theme key=new Theme();
		key.setThemeHash("key");
		List<Theme> keys=new ArrayList<Theme>();
		keys.add(key);
		when(themeRepository.findByThemeType("theme_hash_key")).thenReturn(keys);
		
		List<CommonCache> commonCaches=new ArrayList<>();
		CommonCache commonCache=new CommonCache();
		commonCache.setCacheKey("key");
		commonCache.setCacheValue(themeStr);
		commonCaches.add(commonCache);
		when(commonCacheRepository
				.findByCacheKey(Mockito.any())).thenReturn(commonCaches);
		ThemeResponse res=themeService.getThemes("Oldkey");
		assertEquals("key", res.getThemehash());
		
	}

	@Test
	public void getThemesForNativeCategorytest(){
		ThemeResponse response = newThemeService.getThemesForNativeCategory();
		assertNotNull(response);
		assertEquals(43,response.getThemes().size());
	}


}
