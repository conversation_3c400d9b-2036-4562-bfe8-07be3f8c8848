package com.paytm.saga.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.FilteredEvents;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.AggregatorService;
import com.paytm.saga.util.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertEquals;

public class AggregatorServiceTest {

    @InjectMocks
    private AggregatorService aggregatorService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testFilterOutLatestStateOfOrdersWithMultipleEventTypes() throws Exception{
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "0557622222";
        String service = "mobile";
        String operator = "airtel";
        String paytype = "prepaid";
        Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
        Date transactionDate = DateUtil.stringToDate("2022-03-19 12:00:11", DateFormats.DATE_TIME_FORMAT_2);
        Date transactionUpdateDate = DateUtil.stringToDate("2022-03-20 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date eventUpdateDate = DateUtil.stringToDate("2021-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date DUE_DATE_5DAYS = DateUtil.dateIncrDecr(new Date(), 5);
        List<ChannelHistory> channelHistories = new ArrayList<>();

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2022-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("7").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        ChannelHistory channelHistory1 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-1)).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-1))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-1)).build();
        ChannelHistory channelHistory2 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-30)).setCustomerId(450471692L)
                .setEventType("MARK_AS_PAID").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-30))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-30)).build();
        ChannelHistory channelHistory3 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-60)).setCustomerId(450471692L)
                .setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-60))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-60)).build();
        channelHistories.add(channelHistory);
        channelHistories.add(channelHistory1);
        channelHistories.add(channelHistory2);
        channelHistories.add(channelHistory3);

        FilteredEvents filteredEvents = aggregatorService.filterOutLatestStateOfOrders(channelHistories,finalizationDate);

        assertEquals(4,filteredEvents.getMarkFinaliseData().size());
        assertEquals(3,filteredEvents.getMoveToFinaliseData().size());
        assertEquals(3,filteredEvents.getEvents().size());


    }

    @Test
    public void testFilterOutLatestStateOfOrdersWithMultipleRechargeStates() throws Exception{
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "0557622222";
        String service = "mobile";
        String operator = "airtel";
        String paytype = "prepaid";
        Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
        Date transactionDate = DateUtil.stringToDate("2022-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date transactionUpdateDate = DateUtil.stringToDate("2022-03-20 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date eventUpdateDate = DateUtil.stringToDate("2021-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date DUE_DATE_5DAYS = DateUtil.dateIncrDecr(new Date(), 5);
        List<ChannelHistory> channelHistories = new ArrayList<>();

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2022-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("7").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        ChannelHistory channelHistory1 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-1)).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-1))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-1)).build();
        channelHistories.add(channelHistory);
        channelHistories.add(channelHistory1);

        FilteredEvents filteredEvents = aggregatorService.filterOutLatestStateOfOrders(channelHistories,finalizationDate);

        assertEquals(2,filteredEvents.getMarkFinaliseData().size());
        assertEquals(1,filteredEvents.getMoveToFinaliseData().size());
        assertEquals(1,filteredEvents.getEvents().size());


    }

    @Test
    public void testFilterOutLatestStateOfOrdersWithMultipleNonRechargeEvents() throws Exception{
        ObjectMapper objectMapper = new ObjectMapper();
        Long customerId = 1008952334L;
        String rechargeNumber = "0557622222";
        String service = "mobile";
        String operator = "airtel";
        String paytype = "prepaid";
        Date finalizationDate = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.NUMBER_OF_FINALIZE_DAYS));
        Date transactionDate = DateUtil.stringToDate("2022-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date transactionUpdateDate = DateUtil.stringToDate("2022-03-20 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date eventUpdateDate = DateUtil.stringToDate("2021-03-19 12:00:11",DateFormats.DATE_TIME_FORMAT_2);
        Date DUE_DATE_5DAYS = DateUtil.dateIncrDecr(new Date(), 5);
        List<ChannelHistory> channelHistories = new ArrayList<>();

        Map<String, String> billsObject = new HashMap<>();
        DateFormat format = new SimpleDateFormat(DateFormats.DATE_TIME_FORMAT_2);

        billsObject.put(Constants.OMSConstants.BILL_DATE, "2022-03-19 12:00:11");
        billsObject.put(Constants.OMSConstants.DUE_DATE, format.format(DUE_DATE_5DAYS));

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("7").setTransactionTime(transactionDate)
                .setTransactionUpdateTime(transactionUpdateDate).build();
        ChannelHistory channelHistory1 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-30)).setCustomerId(450471692L)
                .setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-30))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-30)).build();
        ChannelHistory channelHistory2 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-60)).setCustomerId(450471692L)
                .setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-60))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-60)).build();
        ChannelHistory channelHistory3 = new ChannelHistoryBuilder().setAmount("20").setBillsObj(billsObject)
                .setCategoryId(26L).setCircle(null).setCreatedTime(DateUtil.dateIncrDecr(eventUpdateDate,-90)).setCustomerId(450471692L)
                .setEventType("MARK_AS_PAID").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype(paytype).setProductId(110083094L).setRechargeNumber("38864034684")
                .setService(service).setStatus("1").setTransactionTime(DateUtil.dateIncrDecr(transactionDate,-90))
                .setTransactionUpdateTime(DateUtil.dateIncrDecr(transactionUpdateDate,-90)).build();
        channelHistories.add(channelHistory);
        channelHistories.add(channelHistory1);
        channelHistories.add(channelHistory2);
        channelHistories.add(channelHistory3);

        FilteredEvents filteredEvents = aggregatorService.filterOutLatestStateOfOrders(channelHistories,finalizationDate);

        assertEquals(4,filteredEvents.getMarkFinaliseData().size());
        assertEquals(4,filteredEvents.getMoveToFinaliseData().size());
        assertEquals(4,filteredEvents.getEvents().size());


    }
}
