package com.paytm.saga.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.RecentDao;
import com.paytm.saga.dto.DropOffResponse;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.FrequentOrderResponse;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.enums.BillState;
import com.paytm.saga.enums.EventType;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.timgroup.statsd.StatsDClient;
import org.apache.kafka.common.protocol.types.Field;
import org.apache.logging.log4j.LogManager;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.cassandra.core.CassandraTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.paytm.saga.common.constant.Constants.CommonConstants.DATE_FORMAT;
import static com.paytm.saga.common.constant.Constants.CommonConstants.TOTAL_DUE;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RecentWidgetServiceImplDropoffTest {

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(RecentWidgetServiceImplDropoffTest.class));

    @InjectMocks
    private RecentWidgetDataService frequentOrderService;

    @Mock
    private LocalisationManager localisationManager;

    @Mock
    SmartRecentsRepository smartRecentsRepository;

    @Mock
    private UserAgentService userAgentService;

    @Mock
    private SmartReminderService smartReminderService;

    @Mock
    private SmartRecentsService smartRecentsService;

    @Mock
    CassandraTemplate template;

    @Mock
    StatsDClient monitoringClient;

    @Mock
    MetricsHelper metricsHelper;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    private DropOffService dropOffService;

    @Mock
    private RecentDao recentDao;

    @Mock
    private ServiceConfig serviceConfig;

    @Before
    public void setUp() {
        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"newAccountServices\": [\"mobile\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        ReflectionTestUtils.setField(frequentOrderService, "includeOperatorInKeyServices", Arrays.asList("dth"));

        ExecutorService executorService = new ThreadPoolExecutor(20, 20, 10, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(10));
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderDropOffExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderSmartRecentExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderRecentExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "frequentOrderSmartRecentExecutor", executorService);
        ReflectionTestUtils.setField(frequentOrderService, "metricsHelper", metricsHelper);
        when(serviceConfig.getPrepaidEndDay()).thenReturn(3);
        when(serviceConfig.getPrepaidStartDay()).thenReturn(5);
        when(serviceConfig.getPostpaidEndDay()).thenReturn(3);
        when(serviceConfig.getRecentCategoryLimit()).thenReturn(2);


        String  jsonConfig = "{\"androidapp\":{\"dth\":{\"removeFromMyAccount\":\"9.19.3\",\"browsePlans\":\"9.19.3\",\"paymentHistory\":\"9.19.3\",\"nickname\":\"9.19.3\"},\"electricity\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"cylinderbooking\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"broadband\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"landline\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"water\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"gas\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"insurance\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"donation\":{\"paymentHistory\":\"10.6.0\",\"removeFromMyAccount\":\"10.6.0\",\"nickname\":\"10.6.0\"},\"devotion \":{\"paymentHistory\":\"10.6.0 \",\"removeFromMyAccount\":\"10.6.0\",\"nickname\":\"10.6.0\"},\"versions\":{\"9.10.0\":[156705,166690,26,18,17,21,132935,127781,37217,64739,208100,198239,206334,199289,206896,262072,289829],\"9.8.0\":[166690,26,18,17,21,132935,127781,37217,64739,208100,198239,206334,199289,206896],\"9.6.0\":[17,21]},\"paymentHistory\":\"9.16.0\",\"nickname\":\"9.17.0\"},\"iosapp\":{\"electricity\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"cylinderbooking\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"dth\":{\"removeFromMyAccount\":\"9.19.3\",\"browsePlans\":\"9.19.3\",\"paymentHistory\":\"9.19.3\",\"nickname\":\"9.19.3\"},\"broadband\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"landline\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"water\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"gas\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"insurance\":{\"removeFromMyAccount\":\"9.20.0\",\"paymentHistory\":\"9.20.0\",\"nickname\":\"9.20.0\"},\"donation\":{\"paymentHistory\":\"10.6.0\",\"removeFromMyAccount\":\"10.6.0\",\"nickname\":\"10.6.0\"},\"devotion \":{\"paymentHistory \":\"10.6.0\",\"removeFromMyAccount\":\"10.6.0\",\"nickname\":\"10.6.0\"},\"versions\":{\"9.16.0\":[156705,166690,26,18,17,21,132935,127781,37217,64739,208100,198239,206334,199289,206896,262072,289829],\"9.11.0\":[166690,26,18,17,21,132935,127781,37217,64739,208100,198239,206334,199289,206896],\"9.6.0\":[17,21]},\"paymentHistory\":\"9.16.0\",\"nickname\":\"9.17.0\"}}";
        String[] keys = new String[]{"en-IN", "recents", "drop_off_config"};
        when(localisationManager.getMessage(keys, false, Collections.emptyMap())).thenReturn(jsonConfig);


    }


   // @Test
    public void utilityTestRechargeEventDropoffandRecentDifferentRechargeNumber() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
       List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"mcn\":\"4610 89XX XXXX 2346\",\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"credit card\",\"key\":{\"customerId\":1107199327,\"service\":\"financial services\",\"rechargeNumber\":\"222223444555\",\"operator\":\"axis\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null},{\"mcn\":\"4699 89XX XXXX 2346\",\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":10,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":1149,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657116367000,\"payType\":\"credit card\",\"key\":{\"customerId\":1107199327,\"service\":\"financial services\",\"rechargeNumber\":\"8888883444555\",\"operator\":\"axis\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("financial services");
        product.setOperator("axis");
        product.setPayType("credit card");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        product = new Product();
        product.setProductId(1149L);
        product.setService("financial services");
        product.setOperator("axis");
        product.setPayType("credit card");
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);


        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        System.out.println(objectMapper.writeValueAsString(favResponse));
        assertEquals(2,
                favResponse.size());

    }

    @Test
    public void testSameRechargeNumberInRechargeEventDropoffandRecent() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
       List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":100,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"postpaid\",\"key\":{\"customerId\":1107199327,\"service\":\"electricity\",\"rechargeNumber\":\"8052270003\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueDate(new Date());
        recents.get(0).setDueAmount(100.0);
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"RECHARGE\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        System.out.println(objectMapper.writeValueAsString(favResponse));
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.RECHARGE,
                favResponse.get(0).getEventType());

    }

    @Test
    public void testSameRechargeNumberValidationDropoffandRecent() throws Exception {
        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":null,\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"postpaid\",\"key\":{\"customerId\":1107199327,\"service\":\"electricity\",\"rechargeNumber\":\"8052270003\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueAmount(100.0);
        recents.get(0).setDueDate(new Date());
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);


        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"2021-03-01 17:53:02\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        System.out.println(objectMapper.writeValueAsString(favResponse));
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.VALIDATION,
                favResponse.get(0).getEventType());

    }

    @Test
    public void testPrepaidWithinRangeValidationDropoffandRecent() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":\"55566\",\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"prepaid\",\"key\":{\"customerId\":1107199327,\"service\":\"electricity\",\"rechargeNumber\":\"8052270003\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueAmount(100.0);
        recents.get(0).setDueDate(new Date());
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("prepaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + localFormat.format(new Date()) + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(), Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.RECENT,
                favResponse.get(0).getEventType());

    }
    @Test
    public void testFreqResponseForDropOffWhenServicesIsNull() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";

        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":\"222\",\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"prepaid\",\"key\":{\"customerId\":1107199327,\"service\":\"electricity\",\"rechargeNumber\":\"8052270003\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueDate(new Date());
        recents.get(0).setDueAmount(100.0);
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("prepaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);

        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + localFormat.format(new Date()) + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        System.out.println(objectMapper.writeValueAsString(favResponse));
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.RECENT,
                favResponse.get(0).getEventType());

    }

    @Test
    public void testFreqResponseWhenServicesNotNull() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("dth");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":\"22223\",\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"prepaid\",\"key\":{\"customerId\":1107199327,\"service\":\"electricity\",\"rechargeNumber\":\"8052270003\",\"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":null,\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueDate(new Date());
        recents.get(0).setDueAmount(100.0);
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("prepaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);


        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + localFormat.format(new Date()) + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.RECENT,
                favResponse.get(0).getEventType());

    }

    @Test
    public void testFreqResponseRentPayment() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("rent payment");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"rent payment\", \"rechargeNumber\":\"101010\", \"operator\":\"house rent\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":\"22223\",\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"postpaid\",\"key\":{\"customerId\":1107199327,\"service\":\"rent payment\",\"rechargeNumber\":\"101010\",\"operator\":\"house rent\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":\"9876543210\",\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueDate(new Date());
        recents.get(0).setDueAmount(100.0);
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("rent payment");
        product.setOperator("house rent");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);


        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"101010_rent payment_house rent\":{\"recharge_number\":\"101010\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"rent payment\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + localFormat.format(new Date()) + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.VALIDATION,
                favResponse.get(0).getEventType());
        //assertEquals("9876543210",null);
        //assertEquals("101010",
          //      favResponse.get(0).getRechargeNumber1());

    }

    @Test
    public void testFreqResponseMobile() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("mobile");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"mobile\", \"rechargeNumber\":\"101010\", \"operator\":\"house business payment\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":\"22223\",\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"postpaid\",\"key\":{\"customerId\":1107199327,\"service\":\"business payment\",\"rechargeNumber\":\"101010\",\"operator\":\"house business payment\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":\"9876543210\",\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueDate(new Date());
        recents.get(0).setDueAmount(100.0);
        recents.get(0).getKey().setService("mobile");
        recents.get(0).setExtra("{\"updated_data_source\":\"SMS_PARSING_DWH_REALTIME\",\"sms_date_time\":\"1725835499301\"}");
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("mobile");
        product.setOperator("house business payment");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);


        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"101010_business payment_house business payment \":{\"recharge_number\":\"101010\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"business payment\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + localFormat.format(new Date()) + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.RECENT,
                favResponse.get(0).getEventType());

        //assertEquals("9876543210",null);
        assertEquals("101010",
                favResponse.get(0).getRechargeNumber1());
        assertEquals("9876543210",
                favResponse.get(0).getRechargeNumber7());
        assertNotNull(favResponse.get(0).getOperatorValidatedAt());

    }

    @Test
    public void testFreqResponseMobileCase2() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("mobile");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"mobile\", \"rechargeNumber\":\"101010\", \"operator\":\"house business payment\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":\"22223\",\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"postpaid\",\"key\":{\"customerId\":1107199327,\"service\":\"business payment\",\"rechargeNumber\":\"101010\",\"operator\":\"house business payment\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":\"9876543210\",\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueDate(new Date());
        recents.get(0).setDueAmount(100.0);
        recents.get(0).getKey().setService("mobile");
        recents.get(0).setExtra("{\"updated_data_source\":\"SMS_PARSING_DWH_REALTIME\",\"sms_date_time\":\"2024-08-09 04:12:19\"}");
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("mobile");
        product.setOperator("house business payment");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);


        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"101010_business payment_house business payment \":{\"recharge_number\":\"101010\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"business payment\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + localFormat.format(new Date()) + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.RECENT,
                favResponse.get(0).getEventType());

        //assertEquals("9876543210",null);
        assertEquals("101010",
                favResponse.get(0).getRechargeNumber1());
        assertEquals("9876543210",
                favResponse.get(0).getRechargeNumber7());
        assertNotNull(favResponse.get(0).getOperatorValidatedAt());

    }

    @Test
    public void testFreqResponseBusinessPayment() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("business payment");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"business payment\", \"rechargeNumber\":\"101010\", \"operator\":\"house business payment\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        List<Recents> recents = objectMapper.readValue("\n" +
                        "[{\"isTokenizedTransaction\":null,\"billDate\":1656590882972,\"dueDate\":1659182882972,\"dueAmount\":null,\"minDueAmount\":null,\"originalDueAmount\":null,\"originalMinDueAmount\":null,\"nickName\":null,\"orderId\":\"22223\",\"productId\":1139,\"txnAmount\":null,\"txnTime\":null,\"txnStatus\":null,\"automaticDate\":1657022882972,\"automaticStatus\":1,\"billUpdateTime\":null,\"dismissActionTime\":null,\"isMarkAsPaid\":null,\"markAsPaidTime\":null,\"cin\":null,\"par\":null,\"tin\":null,\"updatedAt\":1657029967000,\"payType\":\"postpaid\",\"key\":{\"customerId\":1107199327,\"service\":\"business payment\",\"rechargeNumber\":\"101010\",\"operator\":\"house business payment\",\"planBucket\":\"\"},\"recharge_number_2\":null,\"recharge_number_3\":null,\"recharge_number_4\":null,\"recharge_number_5\":null,\"recharge_number_6\":null,\"mcn\":\"XX33\",\"recharge_number_7\":\"9876543210\",\"recharge_number_8\":null}]"
                , new TypeReference<List<Recents>>() {
                });
        recents.get(0).setDueDate(new Date());
        recents.get(0).setDueAmount(100.0);
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("business payment");
        product.setOperator("house business payment");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(recents);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);


        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"101010_business payment_house business payment \":{\"recharge_number\":\"101010\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"business payment\",\"paytype\":\"postpaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + localFormat.format(new Date()) + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals(EventType.RECENT,
                favResponse.get(0).getEventType());

        //assertEquals("9876543210",null);
        assertEquals("101010",
              favResponse.get(0).getRechargeNumber1());
        assertEquals("9876543210",
                favResponse.get(0).getRechargeNumber7());

    }

    @Test
    public void tesDropOffPostPaidOutsideExpiry() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("electricity");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
         Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(serviceConfig.getPostpaidEndDay() + 1)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        System.out.println(objectMapper.writeValueAsString(favResponse));
        assertEquals(1,
                favResponse.size());
        assertNull(favResponse.get(0).getBill());
        assertEquals(BillState.NO_DUE, favResponse.get(0).getBillState());

    }

    @Test
    public void tesDropOffPostPaidInsideExpiry() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("electricity");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(serviceConfig.getPostpaidEndDay()-1)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":\"220\",\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertNotNull(favResponse.get(0).getBill());
        assertEquals(TOTAL_DUE, favResponse.get(0).getBill().getBillState());
        assertTrue(favResponse.get(0).getBill().getIsBillDue());

    }

    @Test
    public void tesDropOffPrepaidOutsideExpiry() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("electricity");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("mobile");
        product.setOperator("jio");
        product.setPayType("prepaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(serviceConfig.getPrepaidStartDay()+1)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":null,\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertNull(favResponse.get(0).getBill());

    }

    @Test
    public void tesDropOffBillStateAlreadyExpired() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("electricity");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().minusDays(serviceConfig.getPostpaidEndDay()-1)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":\"220\",\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertNotNull(favResponse.get(0).getBill());
        assertEquals(TOTAL_DUE, favResponse.get(0).getBill().getBillState());
        assertEquals(BillState.ALREADY_EXPIRED, favResponse.get(0).getBillState());

    }

    @Test
    public void tesDropOffBillStateWillExpired() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("electricity");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);
        String response = "[{\"customerId\":1107199327, \"service\":\"dth\", \"rechargeNumber\":\"8052270003\", \"operator\":\"dish\", \"planBucket\":\"\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        Product product = new Product();
        product.setProductId(1139L);
        product.setService("electricity");
        product.setOperator("jaipur vidyut vitran nigam ltd. (jvvnl)");
        product.setPayType("postpaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(serviceConfig.getPostpaidEndDay()-1)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_electricity_jaipur vidyut vitran nigam ltd. (jvvnl)\":{\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"electricity\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":\"220\",\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":null,\"panUniqueReference\":null,\"tin\":null,\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertNotNull(favResponse.get(0).getBill());
        assertEquals(TOTAL_DUE, favResponse.get(0).getBill().getBillState());
        assertEquals(BillState.WILL_EXPIRE, favResponse.get(0).getBillState());

    }


    @Test
    public void tesDropOffCCOperatorData() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("financial services");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("financial services");
        product.setOperator("hdfc");
        product.setPayType("credit card");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(serviceConfig.getPostpaidEndDay()-3)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"XXXX XXXX XXXX 2346_financial services\":{\"recharge_number\":\"XXXX XXXX XXXX 2346\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"financial services\",\"paytype\":\"credit card\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":\"220\",\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":\"22222356\",\"panUniqueReference\":\"22222458\",\"tin\":\"222224589\",\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertNotNull(favResponse.get(0).getBill());
        assertNotNull( favResponse.get(0).getOperatorRecentData());
        assertEquals("22222458", favResponse.get(0).getOperatorRecentData().get(Constants.CREDIT_CARD.PAN_UNIQUE_REFERENCE));
        assertEquals( "222224589", favResponse.get(0).getOperatorData().get(Constants.CREDIT_CARD.TIN));

    }



    @Test
    public void tesDropOffCCOperatorDataCIN() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("financial services");
        //services.add("dth");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("financial services");
        product.setOperator("hdfc");
        product.setPayType("credit card");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(serviceConfig.getPostpaidEndDay()-3)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"XXXX XXXX XXXX 2346_financial services\":{\"recharge_number\":\"XXXX XXXX XXXX 2346\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"financial services\",\"paytype\":\"credit card\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":\"220\",\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":\"22222356\",\"panUniqueReference\": null,\"tin\":\"222224589\",\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null,null,null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertNotNull(favResponse.get(0).getBill());
        assertNotNull( favResponse.get(0).getOperatorRecentData());
        assertEquals("22222356", favResponse.get(0).getOperatorRecentData().get(Constants.CREDIT_CARD.CREDIT_CARD_ID));
        assertEquals( "22222356", favResponse.get(0).getOperatorData().get(Constants.CREDIT_CARD.CREDIT_CARD_ID));

    }


    @Test
    public void tesDropOffAutomaticStatus() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("financial services");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("financial services");
        product.setOperator("hdfc");
        product.setPayType("credit card");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(serviceConfig.getPostpaidEndDay()-4)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"XXXX XXXX XXXX 2346_financial services\":{\"isAutomaticActive\":true,\"recharge_number\":\"XXXX XXXX XXXX 2346\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"financial services\",\"paytype\":\"credit card\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":\"220\",\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":\"22222356\",\"panUniqueReference\": null,\"tin\":\"222224589\",\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertNotNull(favResponse.get(0).getBill());
        assertNotNull( favResponse.get(0).getOperatorRecentData());
        assertEquals("22222356", favResponse.get(0).getOperatorRecentData().get(Constants.CREDIT_CARD.CREDIT_CARD_ID));
        assertEquals( "22222356", favResponse.get(0).getOperatorData().get(Constants.CREDIT_CARD.CREDIT_CARD_ID));
        assertEquals( new Integer(1), favResponse.get(0).getAutomaticState());

    }

    @Test
    public void tesDropOffPrepaidWithExpiryDay() throws Exception {
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        FrequentOrderRequest request = new FrequentOrderRequest();
        List<String> services = new ArrayList<>();
        services.add("mobile");
        request.setServices(services);
        request.setCustomerId(1107199327L);
        request.setOnlyReminder(Boolean.TRUE);
        request.setRechargeNumber("8052270003");
        request.setIsCoft(Boolean.TRUE);
        request.setVersion("9.10.0");
        request.setClient("androidapp");
        request.setExcludeDropoff(Boolean.FALSE);
        when(serviceConfig.getFrequentOrderExecutorTimeOut()).thenReturn(1000L);
        when(userAgentService.isAgent(any()))
                .thenReturn(Boolean.FALSE);

        Product product = new Product();
        product.setProductId(1139L);
        product.setService("mobile");
        product.setOperator("airtel");
        product.setPayType("prepaid");
        product.setStatus(1);
        product.setCategoryId(21L);
        CVRProductCache.getInstance().addProductDetails(product);
        when(recentDao.findByParams(request.getCustomerId(), request.getServices(), request.getRechargeNumber(), request.getOperator(),serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout())).thenReturn(null);
        //when(template.select(RecentUtils.getDynamicQuery(request.getCustomerId(), recentsPrimaryKeys), Recents.class)).thenReturn(recents);
        String expiryDate = localFormat.format(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(serviceConfig.getPostpaidEndDay()-1)));

        Map<String, DropOffResponse> dropOffResponseMap = objectMapper.readValue("{\"8052270003_mobile\":{\"isAutomaticActive\":true,\"recharge_number\":\"8052270003\",\"recharge_number_2\":\"22222\",\"recharge_number_3\":null,\"recharge_number_4\":null,\"category_id\":\"21\",\"service\":\"mobile\",\"paytype\":\"prepaid\",\"product_id\":\"1139\",\"bills\":[{\"expiry\":null,\"plan_bucket\":null,\"amount\":\"220\",\"orderId\":null,\"state\":\"PENDING\",\"bill_date\":\"2021-01-09 00:00:00\",\"due_date\":\"" + expiryDate + "\",\"cin\":\"22222356\",\"panUniqueReference\": null,\"tin\":\"222224589\",\"min_due_amount\":null,\"original_due_amount\":null,\"original_min_due_amount\":null,\"reminder_amount\":null,\"previousOrderId\":\"exist\"}],\"type\":\"VALIDATION\",\"timestamp\":1657533649897,\"consumerName\":null,\"additionalData\":{\"mobileNumber\":null,\"cylinderAgencyName\":null},\"channel\":null}}"
                , new TypeReference<Map<String, DropOffResponse>>() {
                });

        String expiryDay = DateUtil.formatDate(DateUtil.convertToDateViaInstant(LocalDateTime.now().plusDays(2)), DateFormats.DATE_TIME_FORMAT_2);
        dropOffResponseMap.get("8052270003_mobile").getBills().get(0).setExpiry(expiryDay);
        when(dropOffService.getDropOffResponse(request.getCustomerId(), null, null, null, Boolean.toString(request.getIsCoft())))
                .thenReturn(dropOffResponseMap);

        List<FrequentOrderResponse> favResponse = frequentOrderService.getFrequentOrders(request);
        assertEquals(1,
                favResponse.size());
        assertEquals(BillState.WILL_EXPIRE, favResponse.get(0).getBillState());

    }

}
