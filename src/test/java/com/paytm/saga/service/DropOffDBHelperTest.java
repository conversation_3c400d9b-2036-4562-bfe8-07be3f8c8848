package com.paytm.saga.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.DropOffConfig;
import com.paytm.saga.dto.DropOffConfigByService;
import com.paytm.saga.dto.ResponsePage;
import com.paytm.saga.listeners.DropOffDBHelper;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.repository.DropOffRepository;

public class DropOffDBHelperTest {
	@Mock
	ChannelHistoryService channelHistoryService;
	@Mock
	DropOffRepository dropOffRepository;
	@Mock
	DropOffConfigByService dropOffConfigByService;
	@InjectMocks
	DropOffDBHelper dropOffDBHelper;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	private DropOff getDropOffMockDataValidation() {
		Map<String, String> billsObj = new HashMap<String, String>();
		billsObj.put("plan_bucket", "Top Up");

		DropOff d = new DropOff();
		d.setCustomerId(1L);
		d.setAmount(String.valueOf(10.0));
		d.setCircle("delhi-ncr");
		d.setService("mobile");
		d.setPaytype("prepaid");
		d.setEventType("VALIDATION");
		d.setRechargeNumber("1122");
		d.setCategoryId(1L);
		d.setTransactionTime(new Date());
		d.setOrderId(2L);
		d.setProductId(3L);
		d.setOperator("airtel");
		d.setStatus("7");
		d.setIn_response_code("00");
		d.setPayment_status("1");
		d.setDisplayValues(null);
		d.setBillsObj(billsObj);
		d.setItemId(4L);
		return d;
	}

	@Test
	public void isNewCustomer() {
		ResponsePage<ChannelHistory> chPage = new ResponsePage<ChannelHistory>(0, new ArrayList<>(), null, false);
		when(channelHistoryService.getPageOfHistory(123L, "rechargeNumber", "service", 1, null)).thenReturn(chPage);
		boolean isNewCustomer = dropOffDBHelper.isNewCustomer(123L, "rechargeNumber", "service");
		assertTrue(isNewCustomer);
	}

	@Test
	public void isRecurringCustomer() {
		ResponsePage<ChannelHistory> chPage = new ResponsePage<ChannelHistory>(1, new ArrayList<>(), null, false);
		when(channelHistoryService.getPageOfHistory(123L, "rechargeNumber", "service", 1, null)).thenReturn(chPage);
		boolean isNewCustomer = dropOffDBHelper.isNewCustomer(123L, "rechargeNumber", "service");
		assertFalse(isNewCustomer);
	}

	@Test
	public void testGetDropOffThresholdNoDBConfig() {
		when(dropOffConfigByService.getDropOffConfig("mobile")).thenReturn(null);
		int threshold = dropOffDBHelper.getDropOffThreshold(getDropOffMockDataValidation(), Constants.DROP_OFF_READ);
		assertEquals(0, threshold);
	}

	@Test
	public void testGetDropOffThresholdWithDBConfigAndNewCustomer() {
		DropOff dropOff = getDropOffMockDataValidation();
		when(dropOffConfigByService.getDropOffConfig("mobile")).thenReturn(new DropOffConfig(60, 50));
		ResponsePage<ChannelHistory> chPage = new ResponsePage<ChannelHistory>(0, new ArrayList<>(), null, false);
		when(channelHistoryService.getPageOfHistory(dropOff.getCustomerId(), dropOff.getRechargeNumber(),
				dropOff.getService(), 1, null)).thenReturn(chPage);
		int threshold = dropOffDBHelper.getDropOffThreshold(dropOff, Constants.DROP_OFF_WRITE);
		assertEquals(60, threshold);
	}

	@Test
	public void testGetDropOffThresholdWithDBConfigAndOldCustomer() {
		DropOff dropOff = getDropOffMockDataValidation();
		when(dropOffConfigByService.getDropOffConfig("mobile")).thenReturn(new DropOffConfig(60, 50));
		ResponsePage<ChannelHistory> chPage = new ResponsePage<ChannelHistory>(1, new ArrayList<>(), null, false);
		when(channelHistoryService.getPageOfHistory(dropOff.getCustomerId(), dropOff.getRechargeNumber(),
				dropOff.getService(), 1, null)).thenReturn(chPage);
		int threshold = dropOffDBHelper.getDropOffThreshold(dropOff, Constants.DROP_OFF_WRITE);
		assertEquals(50, threshold);
	}

	@Test
	public void testIsNewCustomerWrite() {
		DropOff dropOff = getDropOffMockDataValidation();
		ResponsePage<ChannelHistory> chPage = new ResponsePage<ChannelHistory>(0, new ArrayList<>(), null, false);
		when(channelHistoryService.getPageOfHistory(dropOff.getCustomerId(), dropOff.getRechargeNumber(),
				dropOff.getService(), 1, null)).thenReturn(chPage);
		boolean isNew = dropOffDBHelper.isNewCustomer(dropOff, Constants.DROP_OFF_WRITE);
		assertTrue(isNew);
		assertEquals(dropOff.getBillsObj().get(Constants.NEW_USER_IDENTIFIER), "true");
	}

	@Test
	public void testIsRecurringCustomerWrite() {
		DropOff dropOff = getDropOffMockDataValidation();
		ResponsePage<ChannelHistory> chPage = new ResponsePage<ChannelHistory>(1, new ArrayList<>(), null, false);
		when(channelHistoryService.getPageOfHistory(dropOff.getCustomerId(), dropOff.getRechargeNumber(),
				dropOff.getService(), 1, null)).thenReturn(chPage);
		boolean isNew = dropOffDBHelper.isNewCustomer(dropOff, Constants.DROP_OFF_WRITE);
		assertFalse(isNew);
		assertNull(dropOff.getBillsObj().get(Constants.NEW_USER_IDENTIFIER));

	}

	@Test
	public void testIsNewCustomerRead() {
		DropOff dropOff = getDropOffMockDataValidation();
		dropOff.getBillsObj().put(Constants.NEW_USER_IDENTIFIER, "true");
		boolean isNew = dropOffDBHelper.isNewCustomer(dropOff, Constants.DROP_OFF_READ);
		assertTrue(isNew);
	}

	@Test
	public void testIsRecurringCustomerRead() {
		DropOff dropOff = getDropOffMockDataValidation();
		boolean isNew = dropOffDBHelper.isNewCustomer(dropOff, Constants.DROP_OFF_READ);
		assertFalse(isNew);
	}

	@Test
	public void testInsertDropOffCase1() {
		DropOff dropOff = getDropOffMockDataValidation();
		ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
		ArgumentCaptor<Integer> argument2 = ArgumentCaptor.forClass(Integer.class);
		dropOffDBHelper.insertDropOff(dropOff);
		verify(dropOffRepository).save(argument.capture(),argument2.capture());
	}
	
	@Test
	public void testInsertDropOffCase2() {
		DropOff dropOff = getDropOffMockDataValidation();
		when(dropOffConfigByService.getDropOffConfig("mobile")).thenReturn(new DropOffConfig(60, 50));
		ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
		ArgumentCaptor<Integer> argument2 = ArgumentCaptor.forClass(Integer.class);
		dropOffDBHelper.insertDropOff(dropOff);
		verify(dropOffRepository).save(argument.capture(),argument2.capture());
	}

}
