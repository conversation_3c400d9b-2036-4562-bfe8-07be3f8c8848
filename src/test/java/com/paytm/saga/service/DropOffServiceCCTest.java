package com.paytm.saga.service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.DropOffResponse;
import com.paytm.saga.listeners.DropOffDBHelper;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.repository.DropOffRepository;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;
import junit.framework.TestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.cassandra.core.CassandraTemplate;

import java.text.DateFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DropOffServiceCCTest extends TestCase {
	@Mock
	PlanExpiryHistoryRepository planExpiryHistoryRepository;

	@Mock
	DropOffRepository dropOffRepository;

	@Mock
	ReminderHistoryRepositoryWrapperService reminderHistoryRepository;
	@Mock
	DropOffDBHelper dropOffDBHelper;

	@InjectMocks
	DropOffService dropOffService;

	@InjectMocks
	ReminderHistoryService reminderHistoryService;

	@InjectMocks
	PlanExpiryHistoryService planExpiryHistoryService;

	@Mock
	CassandraTemplate template;

	DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	Date date1 = new Date(), date2 = new Date(), date3 = new Date(), date4 = new Date(), date5 = new Date(),
			date_now_minus_10_days = new Date(),date_now_minus_6_days = new Date();

	@Before
	public void setup() {
		dropOffService = new DropOffService(dropOffRepository, planExpiryHistoryService, reminderHistoryService,dropOffDBHelper);
		MockitoAnnotations.initMocks(this);

		date1 = DateUtils.addHours(new Date(), -12);
		date2 = DateUtils.addHours(new Date(), -24);
		date3 = DateUtils.addHours(new Date(), -36);
		date4 = DateUtils.addHours(new Date(), -48);
		date5 = DateUtils.addHours(new Date(), -60);

		date_now_minus_10_days = DateUtils.addDays(new Date(), -10);
		date_now_minus_6_days = DateUtils.addDays(new Date(), -6);
	}

	private DropOff getDropOffMockDataValidation() {
		Map<String, String> billsObj = new HashMap<String, String>() {
		};
		billsObj.put("mcn", "4207 39XX XXXX 0803");

		DropOff d = new DropOff();
		d.setCustomerId(1L);
		d.setAmount(String.valueOf(10.0));
		d.setCircle("delhi-ncr");
		d.setService(Constants.FINANCIAL_SERVICE);
		d.setPaytype("credit card");
		d.setEventType("VALIDATION");
		d.setRechargeNumber("1122");
		d.setCategoryId(1L);
		d.setTransactionTime(new Date());
		d.setOrderId(2L);
		d.setProductId(3L);
		d.setOperator("visa_sbi");
		d.setStatus("7");
		d.setIn_response_code("00");
		d.setPayment_status("1");
		d.setDisplayValues(null);
		d.setBillsObj(billsObj);
		d.setItemId(4L);
		return d;
	}

	private ReminderHistory getReminderHistoryMockData(){
        ReminderHistory r = new ReminderHistory();
        r.setRechargeNumber("1122");
        r.setService(Constants.FINANCIAL_SERVICE);
        r.setCircle("delhi-ncr");
        r.setOperator("visa_sbi");
        r.setReference_id("1122");
        r.setCurrentMinBillAmount(5.0);
        r.setCreated_at(new Date());
        r.setAmount(20.0);
        r.setCustomerId(1L);
        r.setPaytype("credit card");
        r.setBill_date(date1);
        r.setDue_date(date1);
        r.setUpdatedAt(date1);
        return r;
    }

	@Test
	public void testGetDropOffValidation() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d = getDropOffMockDataValidation();
		dropOffs.add(d);
		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(1L, services)).thenReturn(dropOffs);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null, "true");
		assertEquals(1, res.size());
		assertEquals(10.0, res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getAmount());
		assertEquals("VALIDATION", res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getType());
	}

	@Test
	public void testGetDropOffValidationWhenTwoDifferentMcn() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d1 = getDropOffMockDataValidation();
		d1.setTransactionTime(new Date());
		d1.setRechargeNumber("11213nn213vA");
		d1.getBillsObj().put("mcn","4207 39XX XXXX 0801");
		d1.getBillsObj().put("par", "11213nn213vA");
		d1.getBillsObj().put("cin", null);
		DropOff d2 = getDropOffMockDataValidation();
		d2.setTransactionTime(DateUtils.addHours(new Date(), -4));
		d2.getBillsObj().put("mcn", "4207 39XX XXXX 0803");
		d2.getBillsObj().put("cin", "1122");
		dropOffs.add(d1);
		dropOffs.add(d2);
		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d1.getRechargeNumber());
		rechNumbers.add(d2.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(1L, services)).thenReturn(dropOffs);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null, "true");
		assertEquals(2, res.size());
		assertEquals(10.0, res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getAmount());
		assertEquals("VALIDATION", res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getType());

	}

	@Test
	public void testGetDropOffValidationWhenSameMcnWthParAndCinEntryInDropOff() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d1 = getDropOffMockDataValidation();
		d1.setTransactionTime(new Date());
		d1.setRechargeNumber("11213nn213vA");
		d1.getBillsObj().put("par", "11213nn213vA");
		DropOff d2 = getDropOffMockDataValidation();
		d2.setTransactionTime(DateUtils.addHours(new Date(), -4));
		d2.getBillsObj().put("cin", "1122");
		dropOffs.add(d1);
		dropOffs.add(d2);
		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d1.getRechargeNumber());
		rechNumbers.add(d2.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(1L, services)).thenReturn(dropOffs);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null, "true");
		assertEquals(1, res.size());
		assertEquals(10.0, res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getAmount());
		assertEquals("VALIDATION", res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getType());
		assertEquals(d1.getBillsObj().get("mcn"), res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getRecharge_number());
		assertEquals(d1.getBillsObj().get("par"), res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getPanUniqueReference());
	}

	@Test
	public void testGetDropOffValidationWhenSameMcnWthParAndCinEntryInDropOffWithDifferetOperator() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d1 = getDropOffMockDataValidation();
		d1.setTransactionTime(new Date());
		d1.setRechargeNumber("11213nn213vA");
		d1.getBillsObj().put("par", "11213nn213vA");
		DropOff d2 = getDropOffMockDataValidation();
		d2.setTransactionTime(DateUtils.addHours(new Date(), -4));
		d2.getBillsObj().put("cin", "1122");
		dropOffs.add(d1);
		dropOffs.add(d2);
		d2.setOperator("visa_kotak");
		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d1.getRechargeNumber());
		rechNumbers.add(d2.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(1L, services)).thenReturn(dropOffs);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null, "true");
		assertEquals(1, res.size());
		assertEquals(10.0, res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getAmount());
		assertEquals("VALIDATION", res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getType());
		assertEquals(d1.getBillsObj().get("mcn"), res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getRecharge_number());
		assertEquals(d1.getBillsObj().get("par"), res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getPanUniqueReference());
	}


	@Test
	public void testWhenMultipleSameMcnInDropOffWith() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d1 = getDropOffMockDataValidation();
		d1.setTransactionTime(new Date());
		d1.setRechargeNumber("11213nn213vA");
		d1.getBillsObj().put("par", "11213nn213vA");
		DropOff d2 = getDropOffMockDataValidation();
		d2.setTransactionTime(DateUtils.addHours(new Date(), -4));
		d2.getBillsObj().put("cin", "1122");
		d2.setOperator("visa_kotak");
		DropOff d3 = getDropOffMockDataValidation();
		d3.setTransactionTime(DateUtils.addHours(new Date(), 3));
		d3.getBillsObj().put("cin", "2222");
		d3.getBillsObj().put("mcn", "4207 39XX XXXX 1903");
		d3.setOperator("rupay_axis");
		DropOff d4 = getDropOffMockDataValidation();
		d4.setTransactionTime(DateUtils.addHours(new Date(), 7));
		d4.getBillsObj().put("cin", "222244");
		d4.getBillsObj().put("mcn", "4207 39XX XXXX 1903");
		d4.setOperator("rupay_hdfc");
		DropOff d5 = getDropOffMockDataValidation();
		d5.setTransactionTime(DateUtils.addHours(new Date(), 2));
		d5.setRechargeNumber("25465336");
		d5.getBillsObj().put("cin", "667777");
		d5.getBillsObj().put("mcn", "4207 39XX XXXX 1903");
		d5.setOperator("rupay_yes");

		dropOffs.add(d1);
		dropOffs.add(d2);
		dropOffs.add(d3);
		dropOffs.add(d4);
		dropOffs.add(d5);

		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d1.getRechargeNumber());
		rechNumbers.add(d2.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(1L, services)).thenReturn(dropOffs);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null, "true");
		System.out.println(res);
		assertEquals(2, res.size());
		assertEquals(10.0, res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getAmount());
		assertEquals(10.0, res.get("XXXX XXXX XXXX 1903_financial services_ccbp").getBills().get(0).getAmount());
		assertEquals("VALIDATION", res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getType());
		assertEquals(d1.getBillsObj().get("mcn"), res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getRecharge_number());
		assertEquals(d1.getBillsObj().get("par"), res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getPanUniqueReference());
	}

	@Test
	public void testGetDropOffValidationWhenSameMcnWthParAndCinEntryInDropOf() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d1 = getDropOffMockDataValidation();
		d1.setTransactionTime(new Date());
		d1.setRechargeNumber("11213nn213vA");
		d1.getBillsObj().put("par", "11213nn213vA");
		DropOff d2 = getDropOffMockDataValidation();
		d2.setTransactionTime(DateUtils.addHours(new Date(), 4));
		d2.getBillsObj().put("cin", "1122");
		dropOffs.add(d1);
		dropOffs.add(d2);
		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d1.getRechargeNumber());
		rechNumbers.add(d2.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(1L, services)).thenReturn(dropOffs);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null, "true");
		assertEquals(1, res.size());
		assertEquals(10.0, res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getAmount());
		assertEquals("VALIDATION", res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getType());
		assertEquals(d1.getBillsObj().get("mcn"), res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getRecharge_number());
		assertEquals(d1.getBillsObj().get("cin"), res.get("XXXX XXXX XXXX 0803_financial services_ccbp").getBills().get(0).getPanUniqueReference());

	}

	@Test
	public void testGetDropOffValidationWhenSameMcnWthParAndCinEntryInDropOffCoftFalse() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d1 = getDropOffMockDataValidation();
		d1.setTransactionTime(new Date());
		d1.setRechargeNumber("11213nn213vA");
		d1.getBillsObj().put("par", "11213nn213vA");
		DropOff d2 = getDropOffMockDataValidation();
		d2.setTransactionTime(DateUtils.addHours(new Date(), 4));
		d2.getBillsObj().put("cin", "1122");
		dropOffs.add(d1);
		dropOffs.add(d2);
		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d1.getRechargeNumber());
		rechNumbers.add(d2.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(1L, services)).thenReturn(dropOffs);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null, "false");
		assertEquals(1, res.size());
		assertEquals(10.0, res.get("4207 39XX XXXX 0803_financial services_ccbp").getBills().get(0).getAmount());
		assertEquals("VALIDATION", res.get("4207 39XX XXXX 0803_financial services_ccbp").getType());
		assertEquals(d1.getBillsObj().get("mcn"), res.get("4207 39XX XXXX 0803_financial services_ccbp").getRecharge_number());
		assertEquals(d1.getBillsObj().get("cin"), res.get("4207 39XX XXXX 0803_financial services_ccbp").getBills().get(0).getPanUniqueReference());

	}

	@Test
	public void testprepareBillsObjWhenRemainAmountIsZero() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d1 = getDropOffMockDataValidation();
		d1.setTransactionTime(new Date());
		d1.setRechargeNumber("11213nn213vA");
		d1.getBillsObj().put("par", "11213nn213vA");
		DropOff d2 = getDropOffMockDataValidation();
		d2.setTransactionTime(DateUtils.addHours(new Date(), 4));
		d2.getBillsObj().put("cin", "1122");
		d2.getBillsObj().put("reminder_amount", "0.0");
		d2.getBillsObj().put("bill_date", "01-12-2022");
		d2.getBillsObj().put("due_date", "12-12-2022");
		dropOffs.add(d1);
		dropOffs.add(d2);
		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d1.getRechargeNumber());
		rechNumbers.add(d2.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(1L, services)).thenReturn(dropOffs);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null, "false");
		assertEquals(null, res.get("4207 39XX XXXX 0803_financial services_ccbp").getBills().get(0).getBill_date());
		assertEquals(null, res.get("4207 39XX XXXX 0803_financial services_ccbp").getBills().get(0).getDue_date());


	}

	//@Test
	public void testGetDropOffFor6DaysNewUser() {

		List<DropOff> dropOffs = new ArrayList<DropOff>();
		DropOff d = getDropOffMockDataValidation();
		d.setTransactionTime(date_now_minus_6_days);
		d.getBillsObj().put(Constants.NEW_USER_IDENTIFIER, "true");
		dropOffs.add(d);
		List<String> services = new ArrayList<String>();
		List<String> rechNumbers = new ArrayList<String>();
		rechNumbers.add(d.getRechargeNumber());
		services.add(Constants.FINANCIAL_SERVICE);
		when(dropOffService.findByCustomerIdAndServiceIn(Mockito.any(), Mockito.any())).thenReturn(dropOffs);
		when(dropOffDBHelper.getDropOffThreshold(Mockito.any(),Mockito.any())).thenReturn(60*24*60);
		Map<String, DropOffResponse> res = dropOffService.getDropOffResponse(1L, services, null, null);
		assertEquals(1, res.size());
	}
	
	@Test
    public void testpostpaidCheckForPendingAmountTrue(){
        List<String> services = new ArrayList<String>();
        List<String> rechNumbers = new ArrayList<String>();
        rechNumbers.add("4207 39XX XXXX 0803");
        services.add(Constants.FINANCIAL_SERVICE);

        List<ReminderHistory> reminderRecords = new ArrayList<ReminderHistory>();

        reminderRecords.add(getReminderHistoryMockData());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(1L, rechNumbers,services)).thenReturn(reminderRecords);

        DropOff d1 = getDropOffMockDataValidation();
        d1.setPaytype("credit card");

        Map<String , DropOff> dropOffMap = new HashMap<String, DropOff>();
        dropOffMap.put("1122_financial services_ccbp", d1);

        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String expectedBillDate = formatter.format(date1);
        String expectedDueDate = formatter.format(date1);


        Map<String , DropOff> res = dropOffService.postpaidCheckForPendingAmount(dropOffMap,1L);
        assertEquals("credit card" , res.get("1122_financial services_ccbp").getPaytype());
        assertEquals("20.0" , res.get("1122_financial services_ccbp").getBillsObj().get("reminder_amount"));

    }
	
	//reminder
}
