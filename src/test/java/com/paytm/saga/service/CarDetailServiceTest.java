package com.paytm.saga.service;

import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.*;
import com.paytm.saga.model.primaryKeys.CarVariantPrimaryKey;
import com.paytm.saga.recent.repository.CarDetailColourRepository;
import com.paytm.saga.recent.repository.CarDetailRepository;
import com.paytm.saga.recent.repository.CarVariantDetailRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Slice;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CarDetailServiceTest {

    @Mock
    CarDetailRepository carDetailRepository;
    @Mock
    CarDetailColourRepository carDetailColourRepository;
    @Mock
    CarVariantDetailRepository carVariantDetailRepository;
    @InjectMocks
    CarDetailService carDetailService;

    @Mock
    MetricsHelper metricsHelper;

    @Mock
    @Qualifier("fastagVehicleDetailsExecutor")
    ExecutorService fastagVehicleDetailsExecutor;

    @Before
    public void setup() {
//        carDetailService = new CarDetailService(carDetailRepository, carDetailColourRepository, carVariantDetailRepository);
        ReflectionTestUtils.setField(carDetailService, "metricsHelper", metricsHelper);
    }

    @Test
    public void testFetchCarDetails(){
        CarDetail carDetail = new CarDetail();
        CarDetailPrimaryKey carDetailPrimaryKey = new CarDetailPrimaryKey();
        carDetailPrimaryKey.setMake("Audi");
        carDetailPrimaryKey.setModel("TT Coupe");
        carDetailPrimaryKey.setVariantId(2234L);
        carDetail.setKey(carDetailPrimaryKey);
        Slice<CarDetail> carDetailSlice = new PageImpl<>(Collections.singletonList(carDetail));
        when(carDetailRepository.findByMake(any(), any())).thenReturn(carDetailSlice);

        ResponseEntity<CarDetailResponse> response = carDetailService.fetchCardDetails("Audi", null);
        assertEquals("Audi", Objects.requireNonNull(response.getBody()).getData().get(0).getMake());

    }

    @Test
    public void testFetchCarDetailsErrorCase(){
        doThrow(NullPointerException.class).when(carDetailRepository).findByMake(any(), any());
        ResponseEntity<CarDetailResponse> response = carDetailService.fetchCardDetails("Audi", null);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

    }

    @Test
    public void testFetchCarColourDetails(){
        CarDetailColour carDetailColour = new CarDetailColour();
        CarDetailColourPrimaryKey carDetailColourPrimaryKey = new CarDetailColourPrimaryKey();
        carDetailColourPrimaryKey.setMake("Audi");
        carDetailColourPrimaryKey.setModel("TT Coupe");
        carDetailColourPrimaryKey.setColour("BLACK");
        carDetailColour.setKey(carDetailColourPrimaryKey);
        Slice<CarDetailColour> carDetailSlice = new PageImpl<>(Collections.singletonList(carDetailColour));
        when(carDetailColourRepository.findByMake(any(), any())).thenReturn(carDetailSlice);

        ResponseEntity<CarDetailColourResponse> response = carDetailService.fetchCarColourDetails("Audi", null);
        assertEquals("Audi", Objects.requireNonNull(response.getBody()).getData().get(0).getMake());

    }

    @Test
    public void testFetchCarColourDetailsErrorCase(){
        doThrow(NullPointerException.class).when(carDetailColourRepository).findByMake(any(), any());
        ResponseEntity<CarDetailColourResponse> response = carDetailService.fetchCarColourDetails("Audi", null);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

    }

    @Test
//  when mocked executor service return completedFuture of instance of CarVariantDetailDTO
    public void testFetchCarVariantImageDetails_1() {

        List<CarColourImageDetailDTO> carColourImageDetailDTOList = new ArrayList<>();
        CarColourImageDetailDTO carColourImageDetailDTO = new CarColourImageDetailDTO();
        carColourImageDetailDTO.setImageURL("http://example.com/image.jpg");
        carColourImageDetailDTOList.add(carColourImageDetailDTO);

        CompletableFuture<CarVariantDetailDTO> carVariantDetailDTOCompletableFuture = CompletableFuture.completedFuture(getCarVariantDetailDTO());
//        when(fastagVehicleDetailsExecutor.submit(any(Callable.class))).thenReturn(carVariantDetailDTOCompletableFuture);

//        when(carVariantDetailRepository.findByVariantId(anyLong())).thenReturn(getCarVariantDetail());
//        when(carDetailColourRepository.findByMakeIdAndModelId(anyString(), anyString())).thenReturn(getCarDetailColourList());

        CarVariantImageRequest request = new CarVariantImageRequest();
        request.setMakeId("123");
        request.setModelId("2123");
        request.setVariantId("123123");

        ResponseEntity<CarVariantImageResponse> response = carDetailService.fetchCarVariantImageDetails(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    @Test
//  when mocked executor service return completedFuture of List of CarColourImageDetailDTO
    public void testFetchCarVariantImageDetails_2() {

        List<CarColourImageDetailDTO> carColourImageDetailDTOList = new ArrayList<>();
        CarColourImageDetailDTO carColourImageDetailDTO = new CarColourImageDetailDTO();
        carColourImageDetailDTO.setImageURL("http://example.com/image.jpg");
        carColourImageDetailDTOList.add(carColourImageDetailDTO);

        CompletableFuture<List<CarColourImageDetailDTO>> listCompletableFuture = CompletableFuture.completedFuture(getCarColourImageDetailDTOList());
//        when(fastagVehicleDetailsExecutor.submit(any(Callable.class))).thenReturn(listCompletableFuture);

//        when(carVariantDetailRepository.findByVariantId(anyLong())).thenReturn(getCarVariantDetail());
//        when(carDetailColourRepository.findByMakeIdAndModelId(anyString(), anyString())).thenReturn(getCarDetailColourList());

        CarVariantImageRequest request = new CarVariantImageRequest();
        request.setMakeId("123");
        request.setModelId("2123");
        request.setVariantId("123123");

        ResponseEntity<CarVariantImageResponse> response = carDetailService.fetchCarVariantImageDetails(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    private CarVariantDetail getCarVariantDetail() {
        CarVariantPrimaryKey carVariantPrimaryKey = new CarVariantPrimaryKey();
        carVariantPrimaryKey.setVariantId(123123L);
        CarVariantDetail carVariantDetail = new CarVariantDetail();
        carVariantDetail.setKey(carVariantPrimaryKey);
        carVariantDetail.setMake("Toyota");
        carVariantDetail.setModel("Corolla");
        carVariantDetail.setVariant("Sedan");
        carVariantDetail.setDisplayName("Toyota Corolla Sedan");
        return carVariantDetail;
    }

    private CarVariantDetailDTO getCarVariantDetailDTO() {
        CarVariantDetailDTO carVariantDetailDTO = new CarVariantDetailDTO();
        carVariantDetailDTO.setMake("Toyota");
        carVariantDetailDTO.setModel("Corolla");
        carVariantDetailDTO.setVariantId(123L);
        carVariantDetailDTO.setVariant("Sedan");
        carVariantDetailDTO.setDisplayName("Toyota Corolla Sedan");
        return carVariantDetailDTO;
    }

    private List<CarDetailColour> getCarDetailColourList() {
        List<CarDetailColour> carDetailColourList = new ArrayList<>();
        CarDetailColour carDetailColour = new CarDetailColour();
        carDetailColour.setImageURL("http://example.com/image.jpg");
        carDetailColourList.add(carDetailColour);
        return carDetailColourList;
    }

    private List<CarColourImageDetailDTO> getCarColourImageDetailDTOList() {
        List<CarColourImageDetailDTO> carColourImageDetailDTOList = new ArrayList<>();
        CarColourImageDetailDTO carColourImageDetailDTO = new CarColourImageDetailDTO();
        carColourImageDetailDTO.setImageURL("http://example.com/image.jpg");
        carColourImageDetailDTOList.add(carColourImageDetailDTO);
        return carColourImageDetailDTOList;
    }
}
