package com.paytm.saga.service;

import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.CarDetailColourResponse;
import com.paytm.saga.dto.CarDetailResponse;
import com.paytm.saga.model.CarDetail;
import com.paytm.saga.model.CarDetailColour;
import com.paytm.saga.model.CarDetailColourPrimaryKey;
import com.paytm.saga.model.CarDetailPrimaryKey;
import com.paytm.saga.recent.repository.CarDetailColourRepository;
import com.paytm.saga.recent.repository.CarDetailRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Slice;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CarDetailServiceTest {

    @Mock
    CarDetailRepository carDetailRepository;
    @Mock
    CarDetailColourRepository carDetailColourRepository;
    @InjectMocks
    CarDetailService carDetailService;

    @Mock
    MetricsHelper metricsHelper;

    @Before
    public void setup() {
        carDetailService = new CarDetailService(carDetailRepository, carDetailColourRepository);
        ReflectionTestUtils.setField(carDetailService, "metricsHelper", metricsHelper);
    }

    @Test
    public void testFetchCarDetails(){
        CarDetail carDetail = new CarDetail();
        CarDetailPrimaryKey carDetailPrimaryKey = new CarDetailPrimaryKey();
        carDetailPrimaryKey.setMake("Audi");
        carDetailPrimaryKey.setModel("TT Coupe");
        carDetailPrimaryKey.setVariantId(2234L);
        carDetail.setKey(carDetailPrimaryKey);
        Slice<CarDetail> carDetailSlice = new PageImpl<>(Collections.singletonList(carDetail));
        when(carDetailRepository.findByMake(any(), any())).thenReturn(carDetailSlice);

        ResponseEntity<CarDetailResponse> response = carDetailService.fetchCardDetails("Audi", null);
        assertEquals("Audi", Objects.requireNonNull(response.getBody()).getData().get(0).getMake());

    }

    @Test
    public void testFetchCarDetailsErrorCase(){
        doThrow(NullPointerException.class).when(carDetailRepository).findByMake(any(), any());
        ResponseEntity<CarDetailResponse> response = carDetailService.fetchCardDetails("Audi", null);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

    }

    @Test
    public void testFetchCarColourDetails(){
        CarDetailColour carDetailColour = new CarDetailColour();
        CarDetailColourPrimaryKey carDetailColourPrimaryKey = new CarDetailColourPrimaryKey();
        carDetailColourPrimaryKey.setMake("Audi");
        carDetailColourPrimaryKey.setModel("TT Coupe");
        carDetailColourPrimaryKey.setColour("BLACK");
        carDetailColour.setKey(carDetailColourPrimaryKey);
        Slice<CarDetailColour> carDetailSlice = new PageImpl<>(Collections.singletonList(carDetailColour));
        when(carDetailColourRepository.findByMake(any(), any())).thenReturn(carDetailSlice);

        ResponseEntity<CarDetailColourResponse> response = carDetailService.fetchCarColourDetails("Audi", null);
        assertEquals("Audi", Objects.requireNonNull(response.getBody()).getData().get(0).getMake());

    }

    @Test
    public void testFetchCarColourDetailsErrorCase(){
        doThrow(NullPointerException.class).when(carDetailColourRepository).findByMake(any(), any());
        ResponseEntity<CarDetailColourResponse> response = carDetailService.fetchCarColourDetails("Audi", null);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

    }

}
