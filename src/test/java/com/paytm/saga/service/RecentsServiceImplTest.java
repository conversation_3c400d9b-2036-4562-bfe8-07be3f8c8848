package com.paytm.saga.service;

import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.DBUpdateException;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.RecentDao;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.repository.RechargeNumberMapRepository;
import com.paytm.saga.service.impl.DeleteCreditCardRecentCard;
import com.paytm.saga.service.impl.DeleteNonCreditCardRecentCard;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.JsonUtils;
import com.timgroup.statsd.StatsDClient;
import junit.framework.TestCase;
import org.apache.logging.log4j.LogManager;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import jakarta.validation.ConstraintViolationException;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class RecentsServiceImplTest {
    @Mock
    MetricsHelper metricsHelper;
    @Mock
    StatsDClient monitoringClient;
    @Mock
	RecentsRepositoryWrapperService recentsRepository;
    @Mock
    private RecentDataToKafkaService recentDataToKafkaService;
    @Mock
    DeleteCreditCardRecentCard deleteCreditCardRecentCard;
    @Mock
    DeleteNonCreditCardRecentCard deleteNonCreditCardRecentCard;
    @Mock
    private RechargeNumberMapService rechargeNumberMapService;
    @Mock
    private RechargeNumberMapRepository rechargeNumberMapRepository;
    @Mock
    private RecentService recentService;
    @Mock
    private RecentDao recentDao;
    @Mock
    ServiceConfig serviceConfig;


    @InjectMocks
    RecentsServiceImpl recentsServiceImpl;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        String config = "{\"disableDropOff\": false, \"categoryMapping\": {\"mobile\": [17, 21], \"financial services\": [131655]}, \"disableChatHistory\": false, \"smsEnabledServices\": [\"financial services\"], \"agentIdentificationLimit\": 300, \"smartReminderPrepaidEndDays\": 3, \"smartReminderPostpaidEndDays\": 3, \"smartReminderPrepaidStartDays\": 5, \"smartReminderCustomerBillLimit\": 60, \"recentDataUpdateAllowedServices\": [\"dth\", \"financial services\", \"tuition fee\",\"mobile\",\"electricity\"], \"fetchRecentLimit\":100, \"categoryWiseTtl\":{\"insurance\":63072000}}";
        try{
            Map<String, Object> serviceConfig1 = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig1);
        } catch (Exception e){
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        when(serviceConfig.getAgentLimit()).thenReturn(300);
    }

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(RecentsServiceImpl.class));

    private List<Recents> mockCCRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1221334");
        recentsPrimaryKey.setOperator("hdcf");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("credit card");
        recents.setMcn("XXXX XXXX XXXX 1234");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        return recentsList;
    }
    private List<Recents> mockNonCCRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("prepaid");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);
        return recentsList;
    }

    private List<Recents> mockMultiNonCCRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("prepaid");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);


        Recents recents2=new Recents();
        RecentsPrimaryKey recentsPrimaryKey2=new RecentsPrimaryKey();
        recentsPrimaryKey2.setCustomerId(1123L);
        recentsPrimaryKey2.setService("mobile");
        recentsPrimaryKey2.setRechargeNumber("9812316169");
        recentsPrimaryKey2.setOperator("jio");
        recentsPrimaryKey2.setPlanBucket("Special Recharge");
        recents2.setKey(recentsPrimaryKey2);
        recents2.setPayType("prepaid");
        recents2.setTxnAmount(100.0);
        recentsList.add(recents2);

        return recentsList;
    }

    private FetchRecentsRequest mockFetchRecentsByRechargeNumberRequest(){
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setService("mobile");
        fetchRecentsRequest.setOperator("airtel");
        fetchRecentsRequest.setRechargeNumber("9812316169");
        return fetchRecentsRequest;
    }
    private List<Recents> mockFetchRecentsByCustIdInRecents(){
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(2L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("prepaid");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList=new ArrayList<>();
        recentsList.add(recents);


        Recents recents2=new Recents();
        RecentsPrimaryKey recentsPrimaryKey2=new RecentsPrimaryKey();
        recentsPrimaryKey2.setCustomerId(2L);
        recentsPrimaryKey2.setService("mobile");
        recentsPrimaryKey2.setRechargeNumber("898789976");
        recentsPrimaryKey2.setOperator("jio");
        recentsPrimaryKey2.setPlanBucket("");
        recents2.setKey(recentsPrimaryKey2);
        recents2.setPayType("postpaid");
        recents2.setTxnAmount(100.0);
        recentsList.add(recents2);

        Recents recents3=new Recents();
        RecentsPrimaryKey recentsPrimaryKey3=new RecentsPrimaryKey();
        recentsPrimaryKey3.setCustomerId(2L);
        recentsPrimaryKey3.setService("electricity");
        recentsPrimaryKey3.setRechargeNumber("824552345");
        recentsPrimaryKey3.setOperator("uppcl");
        recentsPrimaryKey3.setPlanBucket("");
        recents3.setKey(recentsPrimaryKey3);
        recents3.setPayType("prepaid");
        recents3.setTxnAmount(100.0);
        recentsList.add(recents3);

        Recents recents4=new Recents();
        RecentsPrimaryKey recentsPrimaryKey4=new RecentsPrimaryKey();
        recentsPrimaryKey4.setCustomerId(2L);
        recentsPrimaryKey4.setService("electricity");
        recentsPrimaryKey4.setRechargeNumber("7676766578");
        recentsPrimaryKey4.setOperator("pvvnl");
        recentsPrimaryKey4.setPlanBucket("");
        recents4.setKey(recentsPrimaryKey4);
        recents4.setPayType("postpaid");
        recents4.setTxnAmount(100.0);
        recentsList.add(recents4);

        Recents recents5=new Recents();
        RecentsPrimaryKey recentsPrimaryKey5=new RecentsPrimaryKey();
        recentsPrimaryKey5.setCustomerId(2L);
        recentsPrimaryKey5.setService("financial services");
        recentsPrimaryKey5.setRechargeNumber("23452345261346352452");
        recentsPrimaryKey5.setOperator("axis");
        recentsPrimaryKey5.setPlanBucket("");
        recents5.setKey(recentsPrimaryKey5);
        recents5.setPayType("credit card");
        recents5.setTxnAmount(100.0);
        recents5.setMcn("XXXX XXXX XXXX 1234");
        recents5.setCin("23452345261346352452");
        recentsList.add(recents5);

        Recents recents6=new Recents();
        RecentsPrimaryKey recentsPrimaryKey6=new RecentsPrimaryKey();
        recentsPrimaryKey6.setCustomerId(2L);
        recentsPrimaryKey6.setService("financial services");
        recentsPrimaryKey6.setRechargeNumber("*******************");
        recentsPrimaryKey6.setOperator("icici");
        recentsPrimaryKey6.setPlanBucket("");
        recents6.setKey(recentsPrimaryKey6);
        recents6.setPayType("credit card");
        recents6.setTxnAmount(100.0);
        recents6.setMcn("XXXX XXXX XXXX 7865");
        recents6.setPar("*******************");
        recentsList.add(recents6);

        return recentsList;
    }

    private List<Recents> mockFetchRecentsForNickName() {
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1232L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("postpaid");
        recents.setNickName("mobile airtel");
        recents.setConsumerName("consumer name");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList = new ArrayList<>();
        recentsList.add(recents);
        return recentsList;
    }

    @Test
    public void testRemoveRecentForCC() throws RecentDataToKafkaException {
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockCCRecents());
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("XXXX XXXX XXXX 1234");
        deleteRequestBody.setPaytype("credit card");
        deleteRequestBody.setPar("1221334");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        verify(deleteCreditCardRecentCard, times(1))
                .deleteRecentCard(recentsArgument.capture(),deleteRequestBodyArgument.capture());
        TestCase.assertEquals("1221334", recentsArgument.getValue().getKey().getRechargeNumber());
    }

    @Test
    public void testRemoveRecentForCCFinalResponse(){
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockCCRecents());
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("XXXX XXXX XXXX 1234");
        deleteRequestBody.setPaytype("credit card");
        deleteRequestBody.setPar("1221334");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        NickNameResponse nickNameResponse=recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        TestCase.assertEquals(200,nickNameResponse.getStatusCode().intValue());
    }

    @Test
    public void testRemoveRecentForNonCC() throws RecentDataToKafkaException {
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockNonCCRecents());
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("9812316169");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        verify(deleteNonCreditCardRecentCard, times(1))
                .deleteRecentCard(recentsArgument.capture(),deleteRequestBodyArgument.capture());
        TestCase.assertEquals("9812316169", recentsArgument.getValue().getKey().getRechargeNumber());
    }

    @Test
    public void testRemoveMultipleRecentForNonCC() throws RecentDataToKafkaException {
        long customerId=1123L;
        when(recentsRepository.findByCustomerId(customerId,301)).thenReturn(mockMultiNonCCRecents());
        DeleteRequestBody deleteRequestBody=new DeleteRequestBody();
        deleteRequestBody.setCustomerId(customerId);
        deleteRequestBody.setRechargeNumber("9812316169");
        ArgumentCaptor<Recents> recentsArgument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<DeleteRequestBody> deleteRequestBodyArgument = ArgumentCaptor.forClass(DeleteRequestBody.class);
        recentsServiceImpl.removeRecentWrapper(deleteRequestBody);
        verify(deleteNonCreditCardRecentCard, times(2))
                .deleteRecentCard(recentsArgument.capture(),deleteRequestBodyArgument.capture());
        Recents args1=recentsArgument.getAllValues().get(0);
        Recents args2=recentsArgument.getAllValues().get(1);
        TestCase.assertEquals("9812316169", args1.getKey().getRechargeNumber());
        TestCase.assertEquals("airtel", args1.getKey().getOperator());
        TestCase.assertEquals("jio", args2.getKey().getOperator());
        TestCase.assertEquals("9812316169", args2.getKey().getRechargeNumber());
    }

    @Test(expected= ConstraintViolationException.class)
    public void testFetchRecentsByRechargeNumberWhenRechargeNumberNull() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        fetchRecentsRequest.setRechargeNumber(null);
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(0)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }
    @Test(expected=ConstraintViolationException.class)
    public void testFetchRecentsByRechargeNumberWhenServiceNull() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        fetchRecentsRequest.setService(null);
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(0)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }

    @Test(expected=RechargeSagaBaseException.class)
    public void testFetchRecentsByRechargeNumberWhenNonAllowedServiceService() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        fetchRecentsRequest.setService("financial services");
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(0)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }
    @Test(expected=ConstraintViolationException.class)
    public void testFetchRecentsByRechargeNumberWhenOperatorNull() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        fetchRecentsRequest.setOperator(null);
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(0)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }

    @Test(expected=ConstraintViolationException.class)
    public void testFetchRecentsByRechargeNumberWhenRechargeNumberEmpty() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        fetchRecentsRequest.setRechargeNumber("");
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(0)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }
    @Test(expected=ConstraintViolationException.class)
    public void testFetchRecentsByRechargeNumberWhenServiceEmpty() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        fetchRecentsRequest.setService("");
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(0)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }
    @Test(expected=ConstraintViolationException.class)
    public void testFetchRecentsByRechargeNumberWhenOperatorEmpty() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        fetchRecentsRequest.setOperator("");
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(0)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }
    @Test
    public void testFetchRecentsByRechargeNumberWhenCustomerIdsNull() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        when(rechargeNumberMapService.fetchCustomerIdsFromDb((fetchRecentsRequest.getRechargeNumber()), fetchRecentsRequest.getService(), fetchRecentsRequest.getOperator())).thenReturn(null);
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(1)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }

    @Test
    public void testFetchRecentsByRechargeNumberWhenCustomerIdsEmpty() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        List<Long> customerIds = new ArrayList<>();
        when(rechargeNumberMapService.fetchCustomerIdsFromDb((fetchRecentsRequest.getRechargeNumber()), fetchRecentsRequest.getService(), fetchRecentsRequest.getOperator())).thenReturn(customerIds);
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(1)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(0)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);

    }

    @Test
    public void testFetchRecentsByRechargeNumberWithNonNullCustomerIdWithNoRecents() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        List<Long> customerIds = new ArrayList<>();
        List<Recents> recents = new ArrayList<>();
        customerIds.add(1123L);
        when(rechargeNumberMapService.fetchCustomerIdsFromDb((fetchRecentsRequest.getRechargeNumber()), fetchRecentsRequest.getService(), fetchRecentsRequest.getOperator())).thenReturn(customerIds);
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        when(recentService.getRecents(fetchRecentsRequest, customerIds)).thenReturn(recents);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponse = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(1)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(1)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(new FetchRecentsResponse(), fetchRecentsResponse);
        TestCase.assertEquals(fetchRecentsRequest, fetchRecentsRequestArgumentCaptor.getValue());
        TestCase.assertEquals(customerIds, customerIdsArgument.getValue());

    }

    @Test
    public void testFetchRecentsByRechargeNumberWithNonNullCustomerIdWithNonNullRecents() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = mockFetchRecentsByRechargeNumberRequest();
        List<Long> customerIds = new ArrayList<>();
        List<Recents> recents = mockMultiNonCCRecents();
        FetchRecentsResponse fetchRecentsResponse = new FetchRecentsResponse();
        fetchRecentsResponse.setFetchRecentsResponseObjectList(new HashSet<>());
        FetchRecentsResponseObject fetchRecentsResponseObject1 = new FetchRecentsResponseObject();
        fetchRecentsResponseObject1.setCustomerId(recents.get(0).getKey().getCustomerId());
        fetchRecentsResponseObject1.setRechargeNumber(recents.get(0).getKey().getRechargeNumber());
        fetchRecentsResponseObject1.setProductId(recents.get(0).getProductId());
        fetchRecentsResponseObject1.setService(recents.get(0).getKey().getService());
        fetchRecentsResponseObject1.setOperator(recents.get(0).getKey().getOperator());
        fetchRecentsResponseObject1.setCin(recents.get(0).getCin());
        fetchRecentsResponseObject1.setPar(recents.get(0).getPar());
        fetchRecentsResponseObject1.setUpdatedAt(recents.get(0).getUpdatedAt());

        fetchRecentsResponse.getFetchRecentsResponseObjectList().add(fetchRecentsResponseObject1);

        FetchRecentsResponseObject fetchRecentsResponseObject2 = new FetchRecentsResponseObject();
        fetchRecentsResponseObject2.setCustomerId(recents.get(1).getKey().getCustomerId());
        fetchRecentsResponseObject2.setRechargeNumber(recents.get(1).getKey().getRechargeNumber());
        fetchRecentsResponseObject2.setProductId(recents.get(1).getProductId());
        fetchRecentsResponseObject2.setService(recents.get(1).getKey().getService());
        fetchRecentsResponseObject2.setOperator(recents.get(1).getKey().getOperator());
        fetchRecentsResponseObject2.setCin(recents.get(1).getCin());
        fetchRecentsResponseObject2.setPar(recents.get(1).getPar());
        fetchRecentsResponseObject2.setUpdatedAt(recents.get(1).getUpdatedAt());
        fetchRecentsResponse.getFetchRecentsResponseObjectList().add(fetchRecentsResponseObject2);

        customerIds.add(1123L);
        when(rechargeNumberMapService.fetchCustomerIdsFromDb((fetchRecentsRequest.getRechargeNumber()), fetchRecentsRequest.getService(), fetchRecentsRequest.getOperator())).thenReturn(customerIds);
        ArgumentCaptor<FetchRecentsRequest> fetchRecentsRequestArgumentCaptor = ArgumentCaptor.forClass(FetchRecentsRequest.class);
        when(recentService.getRecents(fetchRecentsRequest, customerIds)).thenReturn(recents);
        ArgumentCaptor<String> rechargeNumberArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> serviceArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<List> customerIdsArgument = ArgumentCaptor.forClass(List.class);
        FetchRecentsResponse fetchRecentsResponseResult = recentsServiceImpl.fetchRecentsByRechargeNumber(fetchRecentsRequest);
        verify(rechargeNumberMapService, times(1)).fetchCustomerIdsFromDb(rechargeNumberArgument.capture(), serviceArgument.capture(), operatorArgument.capture());
        verify(recentService, times(1)).getRecents(fetchRecentsRequestArgumentCaptor.capture(), customerIdsArgument.capture());
        TestCase.assertEquals(fetchRecentsRequest.getRechargeNumber(), rechargeNumberArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getService(), serviceArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest.getOperator(), operatorArgument.getValue());
        TestCase.assertEquals(fetchRecentsRequest, fetchRecentsRequestArgumentCaptor.getValue());
        TestCase.assertEquals(customerIds, customerIdsArgument.getValue());
        TestCase.assertEquals(fetchRecentsResponse, fetchRecentsResponseResult);

    }

    @Test(expected=ConstraintViolationException.class)
    public void fetchRecentsByCustId() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        List<String> services = new ArrayList<>();
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,null,null,null,null);

    }
    @Test(expected=ConstraintViolationException.class)
    public void fetchRecentsByCustIdAndPaytype() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setPaytype("postpaid");
        List<String> services = new ArrayList<>();
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,null,null,null,null);

    }
    @Test(expected=ConstraintViolationException.class)
    public void fetchRecentsByCustIdAndService() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("mobile");
        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        recents1.add(recents.get(1));
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,null,null,null,null);

    }
    @Test(expected=ConstraintViolationException.class)
    public void fetchRecentsByCustIdAndServiceEmpty() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("");
        fetchRecentsRequest.setPaytype("prepaid");
        List<String> services = new ArrayList<>();
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        recents1.add(recents.get(2));
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,null,null,null,null);
    }
    @Test(expected=ConstraintViolationException.class)
    public void fetchRecentsByCustIdAndServiceNull() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService(null);
        fetchRecentsRequest.setPaytype("prepaid");
        List<String> services = new ArrayList<>();
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        recents1.add(recents.get(2));
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,null,null,null,null);
    }

    @Test(expected=ConstraintViolationException.class)
    public void fetchRecentsByCustIdAndServiceAndRechargeNumber() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("mobile");
        fetchRecentsRequest.setRechargeNumber("9812316169");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,null,null,null,null);
    }
    @Test(expected=RechargeSagaBaseException.class)
    public void fetchRecentsByCustIdAndServiceAndPaytypes() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("mobile");
        fetchRecentsRequest.setPaytype("postpaid");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,null,null,null,null);
    }
    @Test
    public void fetchRecentsByCustIdAndServiceAndPaytype() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("mobile");
        fetchRecentsRequest.setRechargeNumber("9812316169");
        fetchRecentsRequest.setPaytype("postpaid");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        recents1.add(recents.get(1));
        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(1,fetchRecentsResponse.getFetchRecentsResponseObjectList().size());

    }
    @Test
    public void fetchRecentsByCustIdAndServiceForCC() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("financial services");
        fetchRecentsRequest.setPaytype("credit card");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(4));
        recents1.add(recents.get(5));
        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(2,fetchRecentsResponse.getFetchRecentsResponseObjectList().size());

    }
    @Test
    public void fetchRecentsByCustIdAndServiceInUpperCaseForCC() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("Financial Services");
        fetchRecentsRequest.setPaytype("credit card");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(4));
        recents1.add(recents.get(5));
        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(2,fetchRecentsResponse.getFetchRecentsResponseObjectList().size());

    }
    @Test
    public void fetchRecentsByCustIdAndServiceAndRechargeNumberForCC() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("financial services");
        fetchRecentsRequest.setPaytype("credit card");
        fetchRecentsRequest.setRechargeNumber("XXXX XXXX XXXX 1234");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(4));
        recents1.add(recents.get(5));
        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(1,fetchRecentsResponse.getFetchRecentsResponseObjectList().size());
    }
    @Test
    public void fetchRecentsByCustIdAndServiceAndRechargeNumberUnMaskedForCC() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("financial services");
        fetchRecentsRequest.setPaytype("credit card");
        fetchRecentsRequest.setRechargeNumber("1234 23XX XXXX 1234");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(4));
        recents1.add(recents.get(5));
        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(1,fetchRecentsResponse.getFetchRecentsResponseObjectList().size());
    }
    @Test
    public void fetchRecentsByCustIdAndServiceAndRechargeNumberEmptyForCC() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("financial services");
        fetchRecentsRequest.setPaytype("credit card");
        fetchRecentsRequest.setRechargeNumber("");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(4));
        recents1.add(recents.get(5));
        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(2,fetchRecentsResponse.getFetchRecentsResponseObjectList().size());
    }
    @Test
    public void fetchRecentsByCustIdAndServiceAndRechargeNumberNullForCC() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("financial services");
        fetchRecentsRequest.setPaytype("credit card");
        fetchRecentsRequest.setRechargeNumber(null);

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(4));
        recents1.add(recents.get(5));
        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(2,fetchRecentsResponse.getFetchRecentsResponseObjectList().size());
    }
    @Test(expected=ConstraintViolationException.class)
    public void fetchRecentsByCustIdAndRechargeNumberForCC() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setPaytype("credit card");
        fetchRecentsRequest.setRechargeNumber("XXXX XXXX XXXX 1234");

        List<String> services = new ArrayList<>();
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(4));
        recents1.add(recents.get(5));
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null);


    }
    @Test
    public void fetchRecentsByCustIdAndServiceAndWrongRechargeNumberForCC() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("financial services");
        fetchRecentsRequest.setPaytype("credit card");
        fetchRecentsRequest.setRechargeNumber("XXXX XXXX XXXX 3234");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(4));
        recents1.add(recents.get(5));
        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(0,fetchRecentsResponse.getFetchRecentsResponseObjectList().size());
    }
    @Test
    public void fetchRecentsByCustIdWhenNoRecents() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("financial services");
        fetchRecentsRequest.setPaytype("credit card");
        fetchRecentsRequest.setRechargeNumber("XXXX XXXX XXXX 1234");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents1 = new ArrayList<>();

        when(recentDao.findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(),null,null,null)).thenReturn(recents1);
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        TestCase.assertEquals(null,fetchRecentsResponse.getFetchRecentsResponseObjectList());

    }

    @Test(expected=ConstraintViolationException.class)
    public void fetchRecentsByCustIdAndServiceAndRechargeNumberAndOperator() throws RechargeSagaBaseException {
        FetchRecentsRequest fetchRecentsRequest = new FetchRecentsRequest();
        fetchRecentsRequest.setCustomerId(2L);
        fetchRecentsRequest.setService("mobile");
        fetchRecentsRequest.setRechargeNumber("9812316169");
        fetchRecentsRequest.setOperator("airtel");

        List<String> services = new ArrayList<>();
        services.add(fetchRecentsRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        FetchRecentsResponse fetchRecentsResponse =  recentsServiceImpl.fetchRecentsByCustomerId(fetchRecentsRequest);
        verify(recentDao,times(0)).findByParams(fetchRecentsRequest.getCustomerId(),services,fetchRecentsRequest.getRechargeNumber(), fetchRecentsRequest.getOperator(), null,null);

    }

    @Test
    public void testNickNameService(){
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(mockFetchRecentsForNickName());
        GetHistoryPageDTO getHistoryPageDTO = new GetHistoryPageDTO();
        getHistoryPageDTO.setCustomerId(123L);
        getHistoryPageDTO.setService("mobile");
        getHistoryPageDTO.setRecharge_number("9812316169");
        getHistoryPageDTO.setOperator("airtel");
        String nickname = recentsServiceImpl.getCustomerName(getHistoryPageDTO,true);
        TestCase.assertEquals("mobile airtel", nickname);
    }
    @Test
    public void testNickNameService2(){
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1232L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("postpaid");
        recents.setConsumerName("consumer name");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList = new ArrayList<>();
        recentsList.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        GetHistoryPageDTO getHistoryPageDTO = new GetHistoryPageDTO();
        getHistoryPageDTO.setCustomerId(123L);
        getHistoryPageDTO.setService("mobile");
        getHistoryPageDTO.setRecharge_number("9812316169");
        getHistoryPageDTO.setOperator("airtel");
        String nickname = recentsServiceImpl.getCustomerName(getHistoryPageDTO,true);
        TestCase.assertEquals("consumer name", nickname);
    }
    @Test
    public void testNickNameService3(){
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1232L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("postpaid");
        recents.setTxnAmount(100.0);
        List<Recents> recentsList = new ArrayList<>();
        recentsList.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(recentsList);
        GetHistoryPageDTO getHistoryPageDTO = new GetHistoryPageDTO();
        getHistoryPageDTO.setCustomerId(123L);
        getHistoryPageDTO.setService("mobile");
        getHistoryPageDTO.setRecharge_number("9812316169");
        getHistoryPageDTO.setOperator("airtel");
        String nickname = recentsServiceImpl.getCustomerName(getHistoryPageDTO,true);
        TestCase.assertEquals(null, nickname);
    }
    @Test
    public void testNickNameService4(){
        GetHistoryPageDTO getHistoryPageDTO = new GetHistoryPageDTO();
        getHistoryPageDTO.setCustomerId(123L);
        getHistoryPageDTO.setService("mobile");
        getHistoryPageDTO.setRecharge_number("9812316169");
        getHistoryPageDTO.setOperator("airtel");
        String nickname = recentsServiceImpl.getCustomerName(getHistoryPageDTO,false);
        TestCase.assertEquals(null, nickname);
    }

    @Test
    public void createRecentForInsuranceTestsPositive() throws RechargeSagaBaseException {
        when(recentsRepository.updateRecentWhenNoExistingData(any(),anyInt())).thenReturn(Boolean.TRUE);

        CreateRecentRequest createRecentRequest = new CreateRecentRequest();
        createRecentRequest.setCustomerId(2L);
        createRecentRequest.setService("FASTag Recharge");
        createRecentRequest.setRechargeNumber("UP79X4573");
        createRecentRequest.setOperator("IDBI Bank Fastag");
        createRecentRequest.setProductId(467017232L);
        createRecentRequest.setPaytype("prepaid");
        Map insurance = new HashMap();
        insurance.put("make",25);
        insurance.put("model",245);
        insurance.put("vehicleColor","PEARL WHITE");
        insurance.put("variantId",2230);
        createRecentRequest.setInsuranceCard(insurance);

        List<String> services = new ArrayList<>();
        services.add(createRecentRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        Boolean result = recentsServiceImpl.createRecentForInsurance(createRecentRequest);
        verify(recentDao,times(0)).findByParams(createRecentRequest.getCustomerId(),services,createRecentRequest.getRechargeNumber(), createRecentRequest.getOperator(), null,null);

    }

    @Test(expected = DBUpdateException.class)
    public void createRecentForInsuranceTestsNegative() throws RechargeSagaBaseException {
        when(recentsRepository.updateRecentWhenNoExistingData(any(),anyInt())).thenReturn(Boolean.FALSE);

        CreateRecentRequest createRecentRequest = new CreateRecentRequest();
        createRecentRequest.setCustomerId(2L);
        createRecentRequest.setService("FASTag Recharge");
        createRecentRequest.setRechargeNumber("UP79X4573");
        createRecentRequest.setOperator("IDBI Bank Fastag");
        createRecentRequest.setProductId(467017232L);
        createRecentRequest.setPaytype("prepaid");
        Map insurance = new HashMap();
        insurance.put("make",25);
        insurance.put("model",245);
        insurance.put("vehicleColor","PEARL WHITE");
        insurance.put("variantId",2230);
        createRecentRequest.setInsuranceCard(insurance);

        List<String> services = new ArrayList<>();
        services.add(createRecentRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        Boolean result = recentsServiceImpl.createRecentForInsurance(createRecentRequest);

    }

    @Test(expected = DBUpdateException.class)
    public void createRecentForInsuranceTestsNegativeException() throws RechargeSagaBaseException {
        when(recentsRepository.updateRecentWhenNoExistingData(any(),anyInt())).thenThrow(new RuntimeException());

        CreateRecentRequest createRecentRequest = new CreateRecentRequest();
        createRecentRequest.setCustomerId(2L);
        createRecentRequest.setService("FASTag Recharge");
        createRecentRequest.setRechargeNumber("UP79X4573");
        createRecentRequest.setOperator("IDBI Bank Fastag");
        createRecentRequest.setProductId(467017232L);
        createRecentRequest.setPaytype("prepaid");
        Map insurance = new HashMap();
        insurance.put("make",25);
        insurance.put("model",245);
        insurance.put("vehicleColor","PEARL WHITE");
        insurance.put("variantId",2230);
        createRecentRequest.setInsuranceCard(insurance);

        List<String> services = new ArrayList<>();
        services.add(createRecentRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        Boolean result = recentsServiceImpl.createRecentForInsurance(createRecentRequest);

    }


    @Test
    public void createRecentForInsuranceTestsPositiveCaseTwo() throws RechargeSagaBaseException {
        List list = new ArrayList();
        list.add(new Recents());
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(list);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),anyInt())).thenReturn(Boolean.TRUE);
        doNothing().when(recentDataToKafkaService).setRecentConsumerSource(any(),any());

        CreateRecentRequest createRecentRequest = new CreateRecentRequest();
        createRecentRequest.setCustomerId(2L);
        createRecentRequest.setService("FASTag Recharge");
        createRecentRequest.setRechargeNumber("UP79X4573");
        createRecentRequest.setOperator("IDBI Bank Fastag");
        createRecentRequest.setProductId(467017232L);
        createRecentRequest.setPaytype("prepaid");
        Map insurance = new HashMap();
        insurance.put("make",25);
        insurance.put("model",245);
        insurance.put("vehicleColor","PEARL WHITE");
        insurance.put("variantId",2230);
        createRecentRequest.setInsuranceCard(insurance);

        List<String> services = new ArrayList<>();
        services.add(createRecentRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        Boolean result = recentsServiceImpl.createRecentForInsurance(createRecentRequest);

    }
    @Test(expected = DBUpdateException.class)
    public void createRecentForInsuranceTestsCaseTwonegativen() throws RechargeSagaBaseException {
        List list = new ArrayList();
        list.add(new Recents());
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperator(any(),any(),any(),any())).thenReturn(list);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(),any(),anyInt())).thenReturn(Boolean.FALSE);

        CreateRecentRequest createRecentRequest = new CreateRecentRequest();
        createRecentRequest.setCustomerId(2L);
        createRecentRequest.setService("FASTag Recharge");
        createRecentRequest.setRechargeNumber("UP79X4573");
        createRecentRequest.setOperator("IDBI Bank Fastag");
        createRecentRequest.setProductId(467017232L);
        createRecentRequest.setPaytype("prepaid");
        Map insurance = new HashMap();
        insurance.put("make",25);
        insurance.put("model",245);
        insurance.put("vehicleColor","PEARL WHITE");
        insurance.put("variantId",2230);
        createRecentRequest.setInsuranceCard(insurance);

        List<String> services = new ArrayList<>();
        services.add(createRecentRequest.getService());
        List<Recents> recents = mockFetchRecentsByCustIdInRecents();
        List<Recents> recents1 = new ArrayList<>();
        recents1.add(recents.get(0));
        Boolean result = recentsServiceImpl.createRecentForInsurance(createRecentRequest);

    }

    @Test
    public void testUpdateRecentForInsurance_Success() {
        Recents recents = mockCCRecents().get(0);
        String insuranceCard = "newInsuranceCard";

        List<Recents> existingRecents = new ArrayList<>();
        existingRecents.add(recents);
        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(anyLong(), anyString(), anyString(), anyString(), anyString())).thenReturn(existingRecents);
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(), any(), any())).thenReturn(true);

        Boolean result = recentsServiceImpl.updateRecentForInsurance(recents, insuranceCard);
        assertTrue(result);
        verify(recentsRepository, times(1)).updateRecentWhenDataAlreadyExist(any(), any(), any());
    }

    @Test
    public void testUpdateRecentForInsurance_FailureWithRetry() {
        Recents recents = mockCCRecents().get(0);
        String insuranceCard = "newInsuranceCard";
        when(recentsRepository.updateRecentWhenDataAlreadyExist(any(), any(), any()))
                .thenReturn(false);

        Boolean result = recentsServiceImpl.updateRecentForInsurance(recents, insuranceCard);
        assertFalse(result);
        verify(recentsRepository, times(1)).findBycustomerIdAndserviceAndrechargeNumberAndoperatorAndplanBucket(anyLong(), any(), any(), any(), any());
        verify(recentsRepository, times(0)).updateRecentWhenDataAlreadyExist(any(), any(), any());
    }

    @Test
    public void filterFastagRecents_withFastagServiceAndLowBalanceOperator_shouldReturnMatchingRecents() {
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.FASTAG);
        recents.getKey().setOperator(Constants.FASTAG_LOW_BALANCE_OPERATOR);
        recents.getKey().setRechargeNumber("12345");
        recentsList.add(recents);

        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("12345");

        List<Recents> filteredRecents = recentsServiceImpl.filterFastagRecents(recentsList, deleteRequestBody);

        assertEquals(1, filteredRecents.size());
        assertEquals("12345", filteredRecents.get(0).getKey().getRechargeNumber());
    }

    @Test
    public void filterFastagRecents_withFastagServiceAndLowBalanceOperatorCaseInsensitive_shouldReturnMatchingRecents() {
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.FASTAG.toLowerCase());
        recents.getKey().setOperator(Constants.FASTAG_LOW_BALANCE_OPERATOR);
        recents.getKey().setRechargeNumber("Abc");
        recentsList.add(recents);

        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("abc");

        List<Recents> filteredRecents = recentsServiceImpl.filterFastagRecents(recentsList, deleteRequestBody);

        assertEquals(1, filteredRecents.size());
    }

    @Test
    public void filterFastagRecents_withFastagServiceOperatorCaseSensitive_shouldNotReturnRecents() {
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.FASTAG.toLowerCase());
        recents.getKey().setOperator("Different_Operator");
        recents.getKey().setRechargeNumber("Abc");
        recentsList.add(recents);

        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("abc");

        List<Recents> filteredRecents = recentsServiceImpl.filterFastagRecents(recentsList, deleteRequestBody);

        assertEquals(0, filteredRecents.size());
    }

    @Test
    public void filterFastagRecents_withFastagServiceAndDifferentOperator_shouldReturnMatchingRecents() {
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.FASTAG);
        recents.getKey().setOperator("DIFFERENT_OPERATOR");
        recents.getKey().setRechargeNumber("Abc");
        recentsList.add(recents);

        Recents recents2 = new Recents();
        recents2.setKey(new RecentsPrimaryKey());
        recents2.getKey().setService(Constants.FASTAG);
        recents2.getKey().setOperator("DIFFERENT_OPERATOR");
        recents2.getKey().setRechargeNumber("abc");
        recentsList.add(recents2);

        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("Abc");

        List<Recents> filteredRecents = recentsServiceImpl.filterFastagRecents(recentsList, deleteRequestBody);

        assertEquals(1, filteredRecents.size());
        assertEquals("Abc", filteredRecents.get(0).getKey().getRechargeNumber());
    }

    @Test
    public void filterFastagRecents_withNonFastagService_shouldReturnEmptyList() {
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService("NON_FASTAG");
        recents.getKey().setRechargeNumber("12345");
        recentsList.add(recents);

        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setRechargeNumber("12345");

        List<Recents> filteredRecents = recentsServiceImpl.filterFastagRecents(recentsList, deleteRequestBody);

        assertTrue(filteredRecents.isEmpty());
    }

    @Test
    public void filterRecents_withFastagService_shouldCallFilterFastagRecents() {
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.FASTAG);
        recents.getKey().setRechargeNumber("12345");
        recentsList.add(recents);

        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setService(Constants.FASTAG);
        deleteRequestBody.setRechargeNumber("12345");

        List<Recents> filteredRecents = recentsServiceImpl.filterRecents(recentsList, deleteRequestBody);

        assertEquals(1, filteredRecents.size());
        assertEquals("12345", filteredRecents.get(0).getKey().getRechargeNumber());
    }

    @Test
    public void filterRecents_withNonFastagService_shouldReturnOriginalList() {
        List<Recents> recentsList = new ArrayList<>();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService("NON_FASTAG");
        recents.getKey().setRechargeNumber("12345");
        recentsList.add(recents);

        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setService("NON_FASTAG");
        deleteRequestBody.setRechargeNumber("12345");

        List<Recents> filteredRecents = recentsServiceImpl.filterRecents(recentsList, deleteRequestBody);

        assertEquals(1, filteredRecents.size());
        assertEquals("12345", filteredRecents.get(0).getKey().getRechargeNumber());
    }

    @Test
    public void removeRecent_withFastagServiceAndMatchingRechargeNumber_shouldDeleteRecent() throws RecentDataToKafkaException{
        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setCustomerId(1L);
        deleteRequestBody.setService(Constants.FASTAG);
        deleteRequestBody.setRechargeNumber("12345");

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.FASTAG);
        recents.getKey().setRechargeNumber("12345");
        recents.getKey().setOperator(Constants.FASTAG_LOW_BALANCE_OPERATOR);

        Recents recents2 = new Recents();
        recents2.setKey(new RecentsPrimaryKey());
        recents2.getKey().setService(Constants.FASTAG);
        recents2.getKey().setRechargeNumber("12345");
        recents2.getKey().setOperator("NON_LOW_BALANCE_OPERATOR");

        Recents recents3 = new Recents();
        recents3.setKey(new RecentsPrimaryKey());
        recents3.getKey().setService("Non-Fastag");
        recents3.getKey().setRechargeNumber("12345");
        recents2.getKey().setOperator("RANDOM_OPERATOR");

        List<Recents> recentsList = new ArrayList<>(Arrays.asList(recents, recents2));
        when(recentsRepository.findByCustomerId(anyLong(), anyInt())).thenReturn(recentsList);

        recentsServiceImpl.removeRecent(deleteRequestBody);

        verify(deleteNonCreditCardRecentCard, times((2))).deleteRecentCard(any(Recents.class), eq(deleteRequestBody));
    }

    @Test
    public void removeRecent_withFastagServiceAndNonMatchingRechargeNumber_shouldNotDeleteRecent() throws RecentDataToKafkaException{
        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setCustomerId(1L);
        deleteRequestBody.setService(Constants.FASTAG);
        deleteRequestBody.setRechargeNumber("54321");

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService(Constants.FASTAG);
        recents.getKey().setRechargeNumber("12345");

        List<Recents> recentsList = Collections.singletonList(recents);
        when(recentsRepository.findByCustomerId(anyLong(), anyInt())).thenReturn(recentsList);

        recentsServiceImpl.removeRecent(deleteRequestBody);

        verify(deleteNonCreditCardRecentCard, never()).deleteRecentCard(any(Recents.class), eq(deleteRequestBody));
    }

    @Test
    public void removeRecent_withNonFastagService_shouldNotDeleteFastagRecent() throws RecentDataToKafkaException{
        DeleteRequestBody deleteRequestBody = new DeleteRequestBody();
        deleteRequestBody.setCustomerId(1L);
        deleteRequestBody.setService("NON_FASTAG");
        deleteRequestBody.setRechargeNumber("12345");

        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setService("NON_FASTAG");
        recents.getKey().setRechargeNumber("12345");

        List<Recents> recentsList = Collections.singletonList(recents);
        when(recentsRepository.findByCustomerId(anyLong(), anyInt())).thenReturn(recentsList);

        recentsServiceImpl.removeRecent(deleteRequestBody);

        verify(recentsRepository, never()).deleteRecentByCustomerIdAndServiceAndRecharge(any(Recents.class), anyLong(), anyString(), anyString(), anyString(), anyString());
    }
}
