package com.paytm.saga.service;

import com.paytm.saga.dto.plans.PlansMapCache;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.repository.ChannelHistoryRepository;
import com.paytm.saga.service.aggregator.cardmanager.PlanExpiry;
import com.paytm.saga.service.aggregator.planmanager.PlansLoader;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.ResourceLoader;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PlansLoaderTest {
    @Mock
    private ResourceLoader resourceLoader;
    @InjectMocks
    private PlansLoader plansLoader;
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testLoadPlansMap(){
        ClassPathResource underTest = new ClassPathResource("planbucket.txt");
        when(resourceLoader.getResource("classpath:planbucket.txt")).thenReturn(underTest);
        plansLoader.onStartup();
        String planName=PlansMapCache.getInstance().getPlanName("mobile_airtel_andhra pradesh_99");
        Assert.assertEquals("Data Pack",planName);
    }
}
