package com.paytm.saga.service;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.RecentDataToKafkaException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.Date;


public class RecentDataToKafkaServiceTest {

    @Mock
    KafkaTemplate<String, Object> recentDataKafkaTemplate;

    @Mock
    MetricsHelper metricsHelper;

    @InjectMocks
    RecentDataToKafkaService recentDataToKafkaService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }


    public Recents mockRecent(){
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("251325433154");
        recentsPrimaryKey.setCustomerId(1L);
        recentsPrimaryKey.setOperator("Jio");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setDueDate(new Date());
        recents.setTxnTime(new Date());

        return recents;
    }

    @Test(expected = RecentDataToKafkaException.class)
    public void testInsertIntoKafka() throws RecentDataToKafkaException {
        Recents recents = mockRecent();
        recentDataToKafkaService.pushRecentDataToKafka(recents, Constants.CDC.UPDATE_OP);
    }
}
