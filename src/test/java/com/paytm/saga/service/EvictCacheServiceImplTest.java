package com.paytm.saga.service;

import com.paytm.saga.common.configuration.property.EvictCacheClientConfig;
import com.paytm.saga.dto.EvictCacheRequest;
import com.paytm.saga.dto.EvictCacheResponse;
import com.paytm.saga.dto.UpsertCacheRequest;
import com.paytm.saga.dto.UpsertCacheResponse;
import com.timgroup.statsd.StatsDClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EvictCacheServiceImplTest {

    @InjectMocks
    private EvictCacheServiceImpl evictCacheService;

    @Mock
    private GenericRestClient restTemplateService;
    @Mock
    private StatsDClient monitoringClient;

    @Before
    public void setup() {
        EvictCacheClientConfig evictCacheClientConfig = new EvictCacheClientConfig();
        evictCacheClientConfig.setRetryInterval(5000L);
        evictCacheClientConfig.setRetryLimit(3L);
        ReflectionTestUtils.setField(evictCacheService, "evictCacheClientConfig", evictCacheClientConfig);
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void testUpsertCache() throws RestClientException {
        // Set up mock configuration values
        EvictCacheClientConfig evictCacheClientConfig = new EvictCacheClientConfig();

        evictCacheClientConfig.setUpsertUrl("http://example.com");
        evictCacheClientConfig.setRetryInterval(5000L);
        evictCacheClientConfig.setRetryLimit(3L);
        // Set up mock response entity
        UpsertCacheResponse mockResponse = new UpsertCacheResponse();
        mockResponse.setDisplayMessage("Success!");
        ResponseEntity<UpsertCacheResponse> mockResponseEntity = new ResponseEntity<>(mockResponse, HttpStatus.OK);
        UpsertCacheRequest testData = new UpsertCacheRequest();
        testData.setInstruction("INCREMENT");
        // Set up mock RestTemplateService to return mock response entity
        when(restTemplateService.post(any(),any(),eq(UpsertCacheResponse.class)))
                .thenReturn(mockResponseEntity);

        evictCacheService.upsertCache(testData);

        // Verify that the method made the expected number of retry attempts
//        verify(evictCacheClientConfig, times(1)).getRetryLimit();
        verify(restTemplateService, times(1)).post(any(), any(), eq(UpsertCacheResponse.class));
    }

    @Test
    public void testEvictCache() throws RestClientException {
        List<String> cacheKey=new ArrayList<>();
        cacheKey.add("key");
        EvictCacheRequest evictCacheRequest=new EvictCacheRequest();
        evictCacheRequest.setCacheKey(cacheKey);
        evictCacheService.evictCache(evictCacheRequest);
        verify(restTemplateService, times(1)).post(any(), any(), eq(EvictCacheResponse.class));
    }

    @Test(expected = HttpServerErrorException.class)
    public void testEvictCacheException() throws RestClientException {
        List<String> cacheKey=new ArrayList<>();
        cacheKey.add("key");
        EvictCacheRequest evictCacheRequest=new EvictCacheRequest();
        evictCacheRequest.setCacheKey(cacheKey);
        //when(restTemplateService.post(any(), any(),any())).thenReturn(new ResponseEntity<>(new EvictCacheResponse(), HttpStatus.OK));
        when(restTemplateService.post(any(), any(),any())).thenThrow(new RuntimeException("test exception"));
        evictCacheService.evictCache(evictCacheRequest);
        verify(restTemplateService, times(1)).post(any(), any(), eq(EvictCacheResponse.class));
    }
}
