package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.any;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.DeepLinkTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.ChannelHistoryPage;
import com.paytm.saga.dto.HistoryPage;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.dto.builder.ChannelHistoryFinalizedBuilder;
import com.paytm.saga.dto.builder.DropOffBuilder;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.service.ChannelHistoryFinalizedService;
import com.paytm.saga.service.ChannelHistoryService;
import com.paytm.saga.service.DropOffService;
import com.paytm.saga.service.aggregator.CylinderAggregatorService;
import com.paytm.saga.service.aggregator.cardmanager.CardManager;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.service.aggregator.cardmanager.DateCard;
import com.paytm.saga.service.aggregator.cardmanager.DropOffCard;
import com.paytm.saga.service.aggregator.cardmanager.MarkedAsPaidCard;
import com.paytm.saga.service.aggregator.cardmanager.RechargeCard;
import com.paytm.saga.service.aggregator.cardmanager.ScratchCard;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.OMSToRechargeStatus;

public class CylinderAggregatorServiceTests {
	@Mock
	private ChannelHistoryFinalizedService channelHistoryFinalizedService;
	@Mock
	private ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
	@Mock
	private DateCard dateCardService;
	@Mock
	private ScratchCard scratchCardService;
	@Mock
	private RechargeCard rechargeCard;
	@Mock
	private DropOffCard dropoffCard;
	@Mock
	private ChannelHistoryService channelHistoryService;
	@Mock
	private DropOffService dropOffService;
	@Mock
	private MarkedAsPaidCard markedAsPaidCardService;
	private static final Date TRANSACTION_DATE = DateUtil.minutesIncrDecr(new Date(), -5);
	private static final Date TRANSACTION_UPDATE_DATE = DateUtil.minutesIncrDecr(new Date(), -4);
	private static final Date CREATED_DATE = DateUtil.minutesIncrDecr(new Date(), -3);
	private static final Date FINALIZED_TRANSACTION_DATE = DateUtil
			.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60), -5);
	private static final Date FINALIZED_TRANSACTION_UPDATE_DATE = DateUtil
			.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60), -4);
	private static final Date FINALIZED_CREATED_DATE = DateUtil.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60),
			-3);

	@InjectMocks
	private CylinderAggregatorService cylinderAggregatorService;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	//@Test
	public void noRechargeTxnWithDropOff() {
		Date dropOffTxnDate=DateUtil.stringToDate("02-03-2021 14:22:33", DateFormats.DATE_TIME_FORMAT);
		Date dropOffCreatedDate=DateUtil.stringToDate("02-03-2021 14:23:33", DateFormats.DATE_TIME_FORMAT);
		DropOff dropOff = new DropOffBuilder().setAmount("100").setCategoryId(166690L).setCircle("pan india")
				.setCreatedTime(dropOffCreatedDate).setEventType("VALIDATION").setOperator("indane").setPaytype("recharge")
				.setService("cylinder booking").setCustomerId(285959997L).setRechargeNumber("CX29412355").setTransactionTime(dropOffTxnDate).build();
		HistoryView dropOffCardInfo=new HistoryViewBuilder()
				.setEventDate(dropOff.getTransactionTime())
				.setService(dropOff.getService()).setCircle(dropOff.getCircle())
				.setPayType(dropOff.getPaytype()).setEventType(dropOff.getEventType())
				.setThemeType(CardThemeTypes.DROPOFF_CARD_THEME).build();
		
		CardInfoDto cardInfoDto1 = new CardInfoDto();
		cardInfoDto1.setDropOff(dropOff);
		when(dropoffCard.getCardInfo(cardInfoDto1)).thenReturn(dropOffCardInfo);
		
		when(dropOffService.getDropOff(285959997L, "CX29412355", "cylinder booking", "indane")).thenReturn(dropOff);
		
		
		
		
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(cardInfoDto1.getDropOff().getTransactionTime()).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(cardInfoDto1.getDropOff().getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.build();
		when(dateCardService.addDateInfoCard(cardInfoDto1.getDropOff().getTransactionTime(), cardInfoDto1.getDropOff().getTransactionTime(), true)).thenReturn(dateCard);
		
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412355", "cylinder booking"))
				.thenReturn(new ArrayList<ChannelHistory>());

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412355", "cylinder booking", 0,
				null)).thenReturn(channelHistoryPage);

		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());
		

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412355",
				"cylinder booking", null, "indane", null, null,null,null);
		assertEquals(2, response.getCards().size());

	}
	
	//@Test
	public void oneRechargeTxnWithDropOff() {
		Date dropOffTxnDate=new Date();
		Date dropOffCreatedDate=new Date();
		Date transactionDate = TRANSACTION_DATE;
		Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = CREATED_DATE;
		DropOff dropOff = new DropOffBuilder().setAmount("100").setCategoryId(166690L).setCircle("pan india")
				.setCreatedTime(dropOffCreatedDate).setEventType("VALIDATION").setOperator("indane").setPaytype("recharge")
				.setRechargeNumber("CX29412353").setTransactionTime(dropOffTxnDate).build();
		when(dropOffService.getDropOff(285959998L, "CX29412353", "cylinder booking", "indane")).thenReturn(dropOff);
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setDropOff(dropOff);
		when(dropoffCard.getCardInfo(cardInfoDto)).thenReturn(new HistoryViewBuilder()
				.setEventDate(cardInfoDto.getDropOff().getTransactionTime())
				.setService(cardInfoDto.getDropOff().getService()).setCircle(cardInfoDto.getDropOff().getCircle())
				.setPayType(cardInfoDto.getDropOff().getPaytype()).setEventType(cardInfoDto.getDropOff().getEventType())
				.setThemeType(CardThemeTypes.DROPOFF_CARD_THEME).build());
		
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(cardInfoDto.getDropOff().getTransactionTime()).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(cardInfoDto.getDropOff().getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.build();
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);
		
		
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767").setBillsObj(null)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(eventUpdateDate).setCustomerId(285959998L)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L)
				.setOperator("indane").setOrderId(12862601947L).setPaymentStatus(null).setPaytype("recharge")
				.setProductId(322951958L).setRechargeNumber("CX29412353").setService("cylinder booking").setStatus("7")
				.setTransactionTime(transactionDate).setTransactionUpdateTime(transactionUpdateDate).build();

		channelHistories.add(channelHistory);
		
		
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(cardInfoDto.getDropOff().getTransactionTime()).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(cardInfoDto.getDropOff().getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.build();
		when(dateCardService.addDateInfoCard(transactionDate, cardInfoDto.getDropOff().getTransactionTime(), false)).thenReturn(date2Card);
		
		when(channelHistoryService.getPageOfHistory(285959998L, "CX29412353", "cylinder booking"))
				.thenReturn(channelHistories);
		
		
		HistoryView rechargeViewCard = new HistoryViewBuilder().setEventDate(channelHistory.getTransactionTime())
				.setCreatedDate(
						DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistory.getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
						channelHistory.getInResponseCode(), channelHistory.getPaymentStatus()))
				.setOrderId(channelHistory.getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistory.getDisplayValues()).setEventDate(channelHistory.getTransactionTime())
				.setCircle(channelHistory.getCircle()).setOperator(channelHistory.getOperator())
				.setPayType(channelHistory.getPaytype()).setEventType(channelHistory.getEventType()).setThemeType("")
				.setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto rechargeCardInfoDto = new CardInfoDto();
		rechargeCardInfoDto.setChannelHistory(channelHistory);
		rechargeCardInfoDto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(rechargeCardInfoDto)).thenReturn(rechargeViewCard);
		

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959998L, "CX29412353", "cylinder booking", 1,
				null)).thenReturn(channelHistoryPage);

		when(scratchCardService.getScratchCard(12862601948L)).thenReturn(new ArrayList<HistoryView>());
		

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959998L, "CX29412353",
				"cylinder booking", null, "indane", null, null,null,null);
		assertEquals(4, response.getCards().size());

	}

	@Test
	public void noTxn() {
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking"))
				.thenReturn(new ArrayList<ChannelHistory>());

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412353", "cylinder booking", 0,
				null)).thenReturn(channelHistoryPage);

		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());
//		DropOff dropOff = new DropOffBuilder().setAmount("100").setCategoryId(166690L).setCircle("pan india")
//				.setCreatedTime(new Date()).setEventType("VALIDATION").setOperator("indane").setPaytype("recharge")
//				.setRechargeNumber("CX29412353").setTransactionTime(new Date()).build();
		when(dropOffService.getDropOff(285959997L, "CX29412353", "cylinder booking", "indane")).thenReturn(null);
//		CardInfoDto cardInfoDto = new CardInfoDto();
//		cardInfoDto.setDropOff(dropOff);
//		when(dropOffCard.getCardInfo(cardInfoDto)).thenReturn(null);

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412353",
				"cylinder booking", null, "indane", null, null,null,null);
		assertEquals(response.getCards().size(), 0);
	}

	@Test
	public void singleTxn() {
		Date transactionDate = TRANSACTION_DATE;
		Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = CREATED_DATE;
		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(eventUpdateDate).setCustomerId(285959997L)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L)
				.setOperator("indane").setOrderId(12862601947L).setPaymentStatus(null).setPaytype("recharge")
				.setProductId(322951958L).setRechargeNumber("CX29412353").setService("cylinder booking").setStatus("7")
				.setTransactionTime(transactionDate).setTransactionUpdateTime(transactionUpdateDate).build();

		channelHistories.add(channelHistory);
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking"))
				.thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412353", "cylinder booking",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);

		when(dateCardService.addDateInfoCard(transactionDate, null, false)).thenReturn(null);

		ViewElementInfo dateHeader = new ViewElementInfo();
		dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
		headers.add(dateHeader);
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(headers).build();
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);

		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard = new HistoryViewBuilder().setEventDate(channelHistory.getTransactionTime())
				.setCreatedDate(
						DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistory.getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
						channelHistory.getInResponseCode(), channelHistory.getPaymentStatus()))
				.setOrderId(channelHistory.getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistory.getDisplayValues()).setEventDate(channelHistory.getTransactionTime())
				.setCircle(channelHistory.getCircle()).setOperator(channelHistory.getOperator())
				.setPayType(channelHistory.getPaytype()).setEventType(channelHistory.getEventType()).setThemeType("")
				.setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412353",
				"cylinder booking", null, "indane", null, null,null,null);
		// also confirm date header and date object position
		assertEquals(response.getCards().size(), 2);
	}

	@Test
	public void singleTxn45DaysOld() {
		Date transactionDate = FINALIZED_TRANSACTION_DATE;
		Date transactionUpdateDate = FINALIZED_TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = FINALIZED_CREATED_DATE;
		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(eventUpdateDate).setCustomerId(285959997L)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L)
				.setOperator("indane").setOrderId(12862601947L).setPaymentStatus(null).setPaytype("recharge")
				.setProductId(322951958L).setRechargeNumber("CX29412353").setService("cylinder booking").setStatus("7")
				.setTransactionTime(transactionDate).setTransactionUpdateTime(transactionUpdateDate).build();

		channelHistories.add(channelHistory);
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking"))
				.thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412353", "cylinder booking",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);

		when(dateCardService.addDateInfoCard(transactionDate, null, false)).thenReturn(null);

		ViewElementInfo dateHeader = new ViewElementInfo();
		dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
		headers.add(dateHeader);
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(headers).build();
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);
		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard = new HistoryViewBuilder().setEventDate(channelHistory.getTransactionTime())
				.setCreatedDate(
						DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistory.getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
						channelHistory.getInResponseCode(), channelHistory.getPaymentStatus()))
				.setOrderId(channelHistory.getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistory.getDisplayValues()).setEventDate(channelHistory.getTransactionTime())
				.setCircle(channelHistory.getCircle()).setOperator(channelHistory.getOperator())
				.setPayType(channelHistory.getPaytype()).setEventType(channelHistory.getEventType()).setThemeType("")
				.setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);

		List<ChannelHistoryFinalized> channelFinalizedHistories = new ArrayList<ChannelHistoryFinalized>();
		channelFinalizedHistories.add(new ChannelHistoryFinalizedBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(eventUpdateDate).setCustomerId(285959997L)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L)
				.setOperator("indane").setOrderId(12862601947L).setPaymentStatus(null).setPaytype("recharge")
				.setProductId(322951958L).setRechargeNumber("CX29412353").setService("cylinder booking").setStatus("7")
				.setTransactionTime(transactionDate).setTransactionUpdateTime(transactionUpdateDate).build());

		when(channelHistoryFinalizedUtil.convertHistoryToFinalizedHistory(channelHistories))
				.thenReturn(channelFinalizedHistories);

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412353",
				"cylinder booking", null, "indane", null, null,null,null);
		// also confirm date header and date object position, count of txns
		assertEquals(response.getCards().size(), 2);
	}

	@Test
	public void multipleTxnInChannelHistory() {
		Date transaction1Date = DateUtil.minutesIncrDecr(TRANSACTION_DATE, -2);
		Date transaction1UpdateDate = DateUtil.minutesIncrDecr(TRANSACTION_UPDATE_DATE, -2);
		Date event1UpdateDate = DateUtil.minutesIncrDecr(CREATED_DATE, -2);
		Date transaction2Date = DateUtil.minutesIncrDecr(TRANSACTION_DATE, -1);
		Date transaction2UpdateDate = DateUtil.minutesIncrDecr(TRANSACTION_UPDATE_DATE, -1);
		Date event2UpdateDate = DateUtil.minutesIncrDecr(CREATED_DATE, -1);

		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event2UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601948L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412354")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction2Date)
				.setTransactionUpdateTime(transaction2UpdateDate).build());
		channelHistories.add(new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event1UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752763L).setOperator("indane").setOrderId(12862601947L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412354")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction1Date)
				.setTransactionUpdateTime(transaction1UpdateDate).build());
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412354", "cylinder booking"))
				.thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412354", "cylinder booking",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);
		when(dateCardService.addDateInfoCard(transaction2Date, null, false)).thenReturn(null);

		ViewElementInfo date1Header = new ViewElementInfo();
		date1Header.setValue(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date1headers = new ArrayList<ViewElementInfo>();
		date1headers.add(date1Header);
		HistoryView date1Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date1headers).build();
		when(dateCardService.addDateInfoCard(transaction1Date, transaction2Date, false)).thenReturn(date1Card);

		ViewElementInfo date2Header = new ViewElementInfo();
		date2Header.setValue(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date2headers = new ArrayList<ViewElementInfo>();
		date2headers.add(date2Header);
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date2headers).build();
		when(dateCardService.addDateInfoCard(transaction1Date, transaction1Date, true)).thenReturn(date2Card);
		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard1 = new HistoryViewBuilder()
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistories.get(0).getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistories.get(0).getStatus(),
						channelHistories.get(0).getInResponseCode(), channelHistories.get(0).getPaymentStatus()))
				.setOrderId(channelHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistories.get(0).getDisplayValues())
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCircle(channelHistories.get(0).getCircle()).setOperator(channelHistories.get(0).getOperator())
				.setPayType(channelHistories.get(0).getPaytype()).setEventType(channelHistories.get(0).getEventType())
				.setThemeType("").setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo1Dto = new CardInfoDto();
		cardInfo1Dto.setChannelHistory(channelHistories.get(0));
		cardInfo1Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo1Dto)).thenReturn(rechargeViewCard1);

		HistoryView rechargeViewCard2 = new HistoryViewBuilder()
				.setEventDate(channelHistories.get(1).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistories.get(1).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistories.get(1).getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistories.get(1).getStatus(),
						channelHistories.get(1).getInResponseCode(), channelHistories.get(1).getPaymentStatus()))
				.setOrderId(channelHistories.get(1).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistories.get(1).getDisplayValues())
				.setEventDate(channelHistories.get(1).getTransactionTime())
				.setCircle(channelHistories.get(1).getCircle()).setOperator(channelHistories.get(1).getOperator())
				.setPayType(channelHistories.get(1).getPaytype()).setEventType(channelHistories.get(1).getEventType())
				.setThemeType("").setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo2Dto = new CardInfoDto();
		cardInfo2Dto.setChannelHistory(channelHistories.get(1));
		cardInfo2Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo2Dto)).thenReturn(rechargeViewCard2);

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412354",
				"cylinder booking", null, "indane", null, null,null,null);
		// also confirm date header and date object position, check value of date cards
		assertEquals(4, response.getCards().size());
	}

	@Test
	public void oneTxnInChannelHistoryAndOneInFinalizedChannelHistory() {
		Date transaction1Date = TRANSACTION_DATE;
		Date transaction1UpdateDate = TRANSACTION_UPDATE_DATE;
		Date event1UpdateDate = CREATED_DATE;
		Date transaction2Date = FINALIZED_TRANSACTION_DATE;
		Date transaction2UpdateDate = FINALIZED_TRANSACTION_UPDATE_DATE;
		Date event2UpdateDate = FINALIZED_CREATED_DATE;

		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event1UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601948L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412355")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction1Date)
				.setTransactionUpdateTime(transaction1UpdateDate).build());
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412355", "cylinder booking"))
				.thenReturn(channelHistories);

		List<ChannelHistory> channelFinalizedHistories = new ArrayList<ChannelHistory>();
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event2UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601949L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412355")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction2Date)
				.setTransactionUpdateTime(transaction2UpdateDate).build());
		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(channelFinalizedHistories);
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412355", "cylinder booking",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);
		System.out.println("mock eventDate=" + transaction1Date + ",previousDate=" + null + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction1Date, null, false)).thenReturn(null);

		ViewElementInfo date1Header = new ViewElementInfo();
		date1Header.setValue(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date1headers = new ArrayList<ViewElementInfo>();
		date1headers.add(date1Header);
		HistoryView date1Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction1Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date1headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction1Date + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction1Date, false)).thenReturn(date1Card);

		ViewElementInfo date2Header = new ViewElementInfo();
		date2Header.setValue(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date2headers = new ArrayList<ViewElementInfo>();
		date2headers.add(date2Header);
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date2headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction2Date + ",isLastCard=" + true);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction2Date, true)).thenReturn(date2Card);
		when(scratchCardService.getScratchCard(12862601948L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601949L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard1 = new HistoryViewBuilder()
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistories.get(0).getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistories.get(0).getStatus(),
						channelHistories.get(0).getInResponseCode(), channelHistories.get(0).getPaymentStatus()))
				.setOrderId(channelHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistories.get(0).getDisplayValues())
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCircle(channelHistories.get(0).getCircle()).setOperator(channelHistories.get(0).getOperator())
				.setPayType(channelHistories.get(0).getPaytype()).setEventType(channelHistories.get(0).getEventType())
				.setThemeType("").setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo1Dto = new CardInfoDto();
		cardInfo1Dto.setChannelHistory(channelHistories.get(0));
		cardInfo1Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo1Dto)).thenReturn(rechargeViewCard1);

		HistoryView rechargeViewCard2 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(0).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(0).getStatus(),
								channelFinalizedHistories.get(0).getInResponseCode(),
								channelFinalizedHistories.get(0).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(0).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(0).getCircle())
				.setOperator(channelFinalizedHistories.get(0).getOperator())
				.setPayType(channelFinalizedHistories.get(0).getPaytype())
				.setEventType(channelFinalizedHistories.get(0).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo2Dto = new CardInfoDto();
		cardInfo2Dto.setChannelHistory(channelFinalizedHistories.get(0));
		cardInfo2Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo2Dto)).thenReturn(rechargeViewCard2);

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412355",
				"cylinder booking", null, "indane", null, null,null,null);
		// also confirm date header and date object position, check value of date cards
		assertEquals(4, response.getCards().size());

	}

	@Test
	public void twoTxnsInFinalizedChannelHistory() {
		Date transaction1Date = FINALIZED_TRANSACTION_DATE;
		Date transaction1UpdateDate = FINALIZED_TRANSACTION_UPDATE_DATE;
		Date event1UpdateDate = FINALIZED_CREATED_DATE;
		Date transaction2Date = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_DATE, -2);
		Date transaction2UpdateDate = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_UPDATE_DATE, -2);
		Date event2UpdateDate = DateUtil.dateIncrDecr(FINALIZED_CREATED_DATE, -2);

		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412355", "cylinder booking"))
				.thenReturn(channelHistories);

		List<ChannelHistory> channelFinalizedHistories = new ArrayList<ChannelHistory>();
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event1UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601948L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412355")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction1Date)
				.setTransactionUpdateTime(transaction1UpdateDate).build());
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event2UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601949L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412355")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction2Date)
				.setTransactionUpdateTime(transaction2UpdateDate).build());
		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(channelFinalizedHistories);
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412355", "cylinder booking",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);
		System.out.println("mock eventDate=" + transaction1Date + ",previousDate=" + null + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction1Date, null, false)).thenReturn(null);

		ViewElementInfo date1Header = new ViewElementInfo();
		date1Header.setValue(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date1headers = new ArrayList<ViewElementInfo>();
		date1headers.add(date1Header);
		HistoryView date1Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction1Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date1headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction1Date + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction1Date, false)).thenReturn(date1Card);

		ViewElementInfo date2Header = new ViewElementInfo();
		date2Header.setValue(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date2headers = new ArrayList<ViewElementInfo>();
		date2headers.add(date2Header);
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date2headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction2Date + ",isLastCard=" + true);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction2Date, true)).thenReturn(date2Card);
		when(scratchCardService.getScratchCard(12862601948L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601949L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard1 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(0).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(0).getStatus(),
								channelFinalizedHistories.get(0).getInResponseCode(),
								channelFinalizedHistories.get(0).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(0).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(0).getCircle())
				.setOperator(channelFinalizedHistories.get(0).getOperator())
				.setPayType(channelFinalizedHistories.get(0).getPaytype())
				.setEventType(channelFinalizedHistories.get(0).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo1Dto = new CardInfoDto();
		cardInfo1Dto.setChannelHistory(channelFinalizedHistories.get(0));
		cardInfo1Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo1Dto)).thenReturn(rechargeViewCard1);

		HistoryView rechargeViewCard2 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(1).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(1).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(1).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(1).getStatus(),
								channelFinalizedHistories.get(1).getInResponseCode(),
								channelFinalizedHistories.get(1).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(1).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(1).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(1).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(1).getCircle())
				.setOperator(channelFinalizedHistories.get(1).getOperator())
				.setPayType(channelFinalizedHistories.get(1).getPaytype())
				.setEventType(channelFinalizedHistories.get(1).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo2Dto = new CardInfoDto();
		cardInfo2Dto.setChannelHistory(channelFinalizedHistories.get(1));
		cardInfo2Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo2Dto)).thenReturn(rechargeViewCard2);

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412355",
				"cylinder booking", null, "indane", null, null,null,null);

		assertEquals(4, response.getCards().size());

	}

	@Test
	public void singleTxnWithMultipleStates() {
		Date transactionDate = TRANSACTION_DATE;
		Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = CREATED_DATE;
		Date transactionUpdateOldDate = DateUtil.minutesIncrDecr(TRANSACTION_UPDATE_DATE, -10);
		Date eventUpdateOldDate = DateUtil.minutesIncrDecr(CREATED_DATE, -10);
		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		ChannelHistory channelHistoryCurrentState = new ChannelHistoryBuilder().setAmount("767")
				.setBillsObj(billsObject).setCategoryId(166690L).setCircle("pan india").setCreatedTime(eventUpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752763L).setOperator("indane").setOrderId(12862601947L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412353")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transactionDate)
				.setTransactionUpdateTime(transactionUpdateDate).build();
		ChannelHistory channelHistoryOldState = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(eventUpdateOldDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752763L).setOperator("indane").setOrderId(12862601947L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412353")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transactionDate)
				.setTransactionUpdateTime(transactionUpdateOldDate).build();
		channelHistories.add(channelHistoryCurrentState);
		channelHistories.add(channelHistoryOldState);
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking"))
				.thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412353", "cylinder booking", 1,
				null)).thenReturn(channelHistoryPage);

		when(dateCardService.addDateInfoCard(transactionDate, null, false)).thenReturn(null);

		ViewElementInfo dateHeader = new ViewElementInfo();
		dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
		headers.add(dateHeader);
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(headers).build();
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);

		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard = new HistoryViewBuilder()
				.setEventDate(channelHistoryCurrentState.getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistoryCurrentState.getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistoryCurrentState.getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistoryCurrentState.getStatus(),
						channelHistoryCurrentState.getInResponseCode(), channelHistoryCurrentState.getPaymentStatus()))
				.setOrderId(channelHistoryCurrentState.getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistoryCurrentState.getDisplayValues())
				.setEventDate(channelHistoryCurrentState.getTransactionTime())
				.setCircle(channelHistoryCurrentState.getCircle()).setOperator(channelHistoryCurrentState.getOperator())
				.setPayType(channelHistoryCurrentState.getPaytype())
				.setEventType(channelHistoryCurrentState.getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistoryCurrentState);
		cardInfoDto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412353",
				"cylinder booking", null, "indane", null, null,null,null);
		// also confirm date header and date object position
		assertEquals(response.getCards().size(), 2);
	}

	@Test
	public void oneTxnInChannelHistoryAndSixInFinalizedChannelHistory() {
		Date transaction1Date = TRANSACTION_DATE;
		Date transaction1UpdateDate = TRANSACTION_UPDATE_DATE;
		Date event1UpdateDate = CREATED_DATE;
		Date transaction2Date = FINALIZED_TRANSACTION_DATE;
		Date transaction2UpdateDate = FINALIZED_TRANSACTION_UPDATE_DATE;
		Date event2UpdateDate = FINALIZED_CREATED_DATE;
		Date transaction3Date = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_DATE, -1);
		Date transaction3UpdateDate = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_UPDATE_DATE, -1);
		Date event3UpdateDate = DateUtil.dateIncrDecr(FINALIZED_CREATED_DATE, -1);
		Date transaction4Date = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_DATE, -2);
		Date transaction4UpdateDate = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_UPDATE_DATE, -2);
		Date event4UpdateDate = DateUtil.dateIncrDecr(FINALIZED_CREATED_DATE, -2);

		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event1UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601948L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412355")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction1Date)
				.setTransactionUpdateTime(transaction1UpdateDate).build());
		when(channelHistoryService.getPageOfHistory(285959997L, "CX29412355", "cylinder booking"))
				.thenReturn(channelHistories);

		List<ChannelHistory> channelFinalizedHistories = new ArrayList<ChannelHistory>();
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event2UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601949L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412355")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction2Date)
				.setTransactionUpdateTime(transaction2UpdateDate).build());
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event3UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601950L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412355")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction3Date)
				.setTransactionUpdateTime(transaction3UpdateDate).build());
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle("pan india").setCreatedTime(event4UpdateDate)
				.setCustomerId(285959997L).setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00")
				.setItemId(13353752764L).setOperator("indane").setOrderId(12862601951L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("CX29412355")
				.setService("cylinder booking").setStatus("7").setTransactionTime(transaction4Date)
				.setTransactionUpdateTime(transaction4UpdateDate).build());
		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(true);
		channelHistoryPage.setLastCardId("kjxhcgkjdfgkjdnlkxdgjhlxkdgj");
		channelHistoryPage.setChannelHistories(channelFinalizedHistories);
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L, "CX29412355", "cylinder booking",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);
		System.out.println("mock eventDate=" + transaction1Date + ",previousDate=" + null + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction1Date, null, false)).thenReturn(null);

		ViewElementInfo date1Header = new ViewElementInfo();
		date1Header.setValue(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date1headers = new ArrayList<ViewElementInfo>();
		date1headers.add(date1Header);
		HistoryView date1Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction1Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date1headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction1Date + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction1Date, false)).thenReturn(date1Card);

		ViewElementInfo date2Header = new ViewElementInfo();
		date2Header.setValue(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date2headers = new ArrayList<ViewElementInfo>();
		date2headers.add(date2Header);
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date2headers).build();
		System.out.println(
				"mock eventDate=" + transaction3Date + ",previousDate=" + transaction2Date + ",isLastCard=" + true);
		when(dateCardService.addDateInfoCard(transaction3Date, transaction2Date, false)).thenReturn(date2Card);

		ViewElementInfo date3Header = new ViewElementInfo();
		date3Header.setValue(DateUtil.dateFormatter(transaction3Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date3headers = new ArrayList<ViewElementInfo>();
		date3headers.add(date3Header);
		HistoryView date3Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction3Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction3Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date3headers).build();
		System.out.println(
				"mock eventDate=" + transaction4Date + ",previousDate=" + transaction3Date + ",isLastCard=" + true);
		when(dateCardService.addDateInfoCard(transaction4Date, transaction3Date, false)).thenReturn(date3Card);

		ViewElementInfo date4Header = new ViewElementInfo();
		date4Header.setValue(DateUtil.dateFormatter(transaction4Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date4headers = new ArrayList<ViewElementInfo>();
		date4headers.add(date4Header);
		HistoryView date4Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction4Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction4Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date4headers).build();
		System.out.println(
				"mock eventDate=" + transaction4Date + ",previousDate=" + transaction4Date + ",isLastCard=" + true);

		when(dateCardService.addDateInfoCard(transaction4Date, transaction4Date, true)).thenReturn(date4Card);

		when(scratchCardService.getScratchCard(12862601948L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601949L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601950L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601951L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601952L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601953L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601954L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard1 = new HistoryViewBuilder()
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistories.get(0).getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistories.get(0).getStatus(),
						channelHistories.get(0).getInResponseCode(), channelHistories.get(0).getPaymentStatus()))
				.setOrderId(channelHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistories.get(0).getDisplayValues())
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCircle(channelHistories.get(0).getCircle()).setOperator(channelHistories.get(0).getOperator())
				.setPayType(channelHistories.get(0).getPaytype()).setEventType(channelHistories.get(0).getEventType())
				.setThemeType("").setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo1Dto = new CardInfoDto();
		cardInfo1Dto.setChannelHistory(channelHistories.get(0));
		cardInfo1Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo1Dto)).thenReturn(rechargeViewCard1);

		HistoryView rechargeViewCard2 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(0).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(0).getStatus(),
								channelFinalizedHistories.get(0).getInResponseCode(),
								channelFinalizedHistories.get(0).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(0).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(0).getCircle())
				.setOperator(channelFinalizedHistories.get(0).getOperator())
				.setPayType(channelFinalizedHistories.get(0).getPaytype())
				.setEventType(channelFinalizedHistories.get(0).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo2Dto = new CardInfoDto();
		cardInfo2Dto.setChannelHistory(channelFinalizedHistories.get(0));
		cardInfo2Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo2Dto)).thenReturn(rechargeViewCard2);

		HistoryView rechargeViewCard3 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(1).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(1).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(1).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(1).getStatus(),
								channelFinalizedHistories.get(1).getInResponseCode(),
								channelFinalizedHistories.get(1).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(1).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(1).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(1).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(1).getCircle())
				.setOperator(channelFinalizedHistories.get(1).getOperator())
				.setPayType(channelFinalizedHistories.get(1).getPaytype())
				.setEventType(channelFinalizedHistories.get(1).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo3Dto = new CardInfoDto();
		cardInfo3Dto.setChannelHistory(channelFinalizedHistories.get(1));
		cardInfo3Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo3Dto)).thenReturn(rechargeViewCard3);

		HistoryView rechargeViewCard4 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(2).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(2).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(2).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(2).getStatus(),
								channelFinalizedHistories.get(2).getInResponseCode(),
								channelFinalizedHistories.get(2).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(2).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(2).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(2).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(2).getCircle())
				.setOperator(channelFinalizedHistories.get(2).getOperator())
				.setPayType(channelFinalizedHistories.get(2).getPaytype())
				.setEventType(channelFinalizedHistories.get(2).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo4Dto = new CardInfoDto();
		cardInfo4Dto.setChannelHistory(channelFinalizedHistories.get(2));
		cardInfo4Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo4Dto)).thenReturn(rechargeViewCard4);

		HistoryPage response = cylinderAggregatorService.aggregateHistoryInfo(285959997L, "CX29412355",
				"cylinder booking", null, "indane", null, null,null,null);
		// also confirm date header and date object position, check value of date cards
		assertEquals(7, response.getCards().size());

	}

	// scratch card test case
}
