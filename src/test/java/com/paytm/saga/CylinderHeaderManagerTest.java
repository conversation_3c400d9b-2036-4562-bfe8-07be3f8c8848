package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.service.aggregator.viewmanager.impl.CylinderHeaderManager;
import com.paytm.saga.util.DateUtil;

public class CylinderHeaderManagerTest {
	@InjectMocks
	private CylinderHeaderManager cylinderHeaderManager;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void dateChangeLastCardCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("12-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.get(0).getValue(), "12-12-2020");
	}

	@Test
	public void dateChangeNotLastCardCardWithoutPreviousDate() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("12-12-2020", DateFormats.DATE_FORMAT)).setLastCard(false)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertNull(viewElementInfos);
	}

	@Test
	public void dateChangeNotLastCardCardWithPreviousDate() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("12-12-2020", DateFormats.DATE_FORMAT))
				.setPreviousDate(DateUtil.stringToDate("11-12-2020", DateFormats.DATE_FORMAT)).setLastCard(false)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.get(0).getValue(), "11-12-2020");
	}

	@Test
	public void dateChangeNotLastCardWithPreviousDateEqualsEventDate() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("12-12-2020", DateFormats.DATE_FORMAT))
				.setPreviousDate(DateUtil.stringToDate("12-12-2020", DateFormats.DATE_FORMAT)).setLastCard(false)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertNull(viewElementInfos);
	}

	@Test
	public void dateChangeNotLastCardWithPreviousDateEqualToCurrentDate() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("12-12-2020", DateFormats.DATE_FORMAT)).setPreviousDate(new Date())
				.setLastCard(false).setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.get(0).getValue(), "Today");
	}

	@Test
	public void dateChangeLastCardWithEventDateEqualToCurrentDate() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setLastCard(true).setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.get(0).getValue(), "Today");
	}

	@Test
	public void successIvrsEventDateCurrentDateTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.SUCCESS_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(true).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Payment Successful");
	}
	
	@Test
	public void successNonIvrsEventDateCurrentDateTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.SUCCESS_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(false).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Gas Cylinder Booked");
	}

	@Test
	public void failureCylinderBookingIVRSTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.FAILURE_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(true).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Payment Failed");
	}

	@Test
	public void failureCylinderBookingNonIVRSTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.FAILURE_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(false).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Booking Failed");
	}

	@Test
	public void reversalfailureCylinderBookingTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.REVERSAL_FAILURE_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(false).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Cylinder Booking Cancelled");
		assertEquals(viewElementInfos.get(2).getValue(),
				"Your order has been cancelled. If any amount was deducted, it will be refunded in 3 working days");
	}

	@Test
	public void paymentFailureNonIVRSCylinderBookingTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.PAYMENT_FAILURE_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(false).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Payment Failed");
		assertEquals(viewElementInfos.get(2).getValue(), "This may be due to wrong OTP or incorrect password.");
	}
	@Test
	public void paymentFailureIVRSCylinderBookingTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.PAYMENT_FAILURE_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(true).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Payment Failed");
		assertEquals(viewElementInfos.get(2).getValue(), "Payment for (Booking ID: BOOK_123) failed. This may be due to wrong OTP or incorrect password.");
	}
	
	@Test
	public void pendingTxnIVRSCylinderBookingTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.PENDING_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(true).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Payment Pending");
		assertEquals(viewElementInfos.get(2).getValue(), "Payment for (Booking ID: BOOK_123) is pending. We will notify you once it is successful.");
	}
	
	@Test
	public void pendingTxnNonIVRSCylinderBookingTest() {
		HistoryView historyView = new HistoryViewBuilder().setOperatorLabel("Indane").setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.PENDING_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(false).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Cylinder Booking Pending");
		assertEquals(viewElementInfos.get(2).getValue(), "Indane is taking time to confirm the order. We will continuously try to post your transaction with Indane till midnight and notify you once we receive confirmation.");
	}
	
	@Test
	public void paymentPendingTxnCylinderBookingTest() {
		HistoryView historyView = new HistoryViewBuilder().setOperatorLabel("Indane").setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.PAYMENT_PENDING_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(false).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Payment Pending");
		assertEquals(viewElementInfos.get(2).getValue(), "Your Payment is pending. We will notify you once it is successful.");
	}
	
	@Test
	public void cancelledTxnCylinderBookingTest() {
		HistoryView historyView = new HistoryViewBuilder().setOperatorLabel("Indane").setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.CANCELLED_CYLINDER_BOOKING_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(false).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Cylinder Booking Cancelled");
		assertEquals(viewElementInfos.get(2).getValue(), "Your order has been cancelled. If any amount was deducted, it will be refunded in 3 working days");
	}
	
	@Test
	public void dropOffCardCylinderBookingTest() {
		HistoryView historyView = new HistoryViewBuilder().setOperatorLabel("Indane").setEventDate(new Date()).setPreviousDate(new Date())
				.setThemeType(CardThemeTypes.DROPOFF_CARD_THEME).setBookingId("BOOK_123")
				.setIvrsBooking(false).build();
		List<ViewElementInfo> viewElementInfos = cylinderHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
		assertEquals(viewElementInfos.get(0).getValue(), "Complete your Booking");
	}
}
