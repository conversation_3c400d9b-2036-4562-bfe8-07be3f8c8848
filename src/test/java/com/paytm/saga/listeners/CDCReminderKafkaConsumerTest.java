package com.paytm.saga.listeners;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.CDCReminderListenerException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.NickNameRequest;
import com.paytm.saga.dto.SMSRecoveryPacket;
import com.paytm.saga.dto.cdc.NonPaytmCDC;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.CDCConsumerUtil;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import junit.framework.TestCase;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.IGNORE_EVENT;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class CDCReminderKafkaConsumerTest extends TestCase {
    @Mock
    CDCReminderListener cdcReminderListener;
    @Mock
    Acknowledgment acknowledgment;

    @Mock
    ServiceConfig serviceConfig;

    @Mock
    MetricsHelper metricsHelper;
//    @Mock
//    CDCReminderKafkaConsumer cdcReminderKafkaConsumer;
//
//    @Mock
//    CDCSMSRecoveryKafkaConsumer cdcSMSRecoveryKafkaConsumer;
    @InjectMocks
    CDCReminderKafkaConsumer cdcReminderKafkaConsumer;
    @After
    public void resetMocks(){
        Mockito.reset();
    }


    @Test
    public void testListenP2p(){
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":1682663400000,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        List<String> msgs = new ArrayList<>();
        msgs.add(msg);
        cdcReminderKafkaConsumer.listenP2P(msgs,acknowledgment);
    }
    @Test
    public void testListenP2p1(){
        List<String> msgs = new ArrayList<>();
        msgs.add(null);
        cdcReminderKafkaConsumer.listenP2P(msgs,acknowledgment);
    }
    @Test
    public void testListenSms(){
        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        List<String> msgs = new ArrayList<>();
        msgs.add(msg);
        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
//        verify(metricsHelper,times(2)).recordSuccessRate(arg1.capture(),arg2.capture());
//        assertEquals(Constants.CDC_REMINDER_CONSUMER, arg1.getAllValues().get(1));
//        assertEquals("greater than 60 Min", arg2.getAllValues().get(1));
    }
//    @Test
//    public void testListenSms31MinDelay(){
//        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
//        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
//        long updatedAt=DateUtil.minutesIncrDecr(new Date(),-31).getTime();
//        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":"+updatedAt+",\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
//        List<String> msgs = new ArrayList<>();
//        msgs.add(msg);
//        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
//        verify(metricsHelper,times(2)).recordSuccessRate(arg1.capture(),arg2.capture());
//        assertEquals(Constants.CDC_REMINDER_CONSUMER, arg1.getAllValues().get(1));
//        assertEquals("greater than 30 Min", arg2.getAllValues().get(1));
//    }
//    @Test
//    public void testListenSms15MinDelay(){
//        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
//        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
//        long updatedAt=DateUtil.minutesIncrDecr(new Date(),-16).getTime();
//        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":"+updatedAt+",\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
//        List<String> msgs = new ArrayList<>();
//        msgs.add(msg);
//        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
//        verify(metricsHelper,times(2)).recordSuccessRate(arg1.capture(),arg2.capture());
//        assertEquals(Constants.CDC_REMINDER_CONSUMER, arg1.getAllValues().get(1));
//        assertEquals("greater than 15 Min", arg2.getAllValues().get(1));
//    }
//    @Test
//    public void testListenSms5MinDelay(){
//        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
//        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
//        long updatedAt=DateUtil.minutesIncrDecr(new Date(),-6).getTime();
//        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":"+updatedAt+",\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
//        List<String> msgs = new ArrayList<>();
//        msgs.add(msg);
//        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
//        verify(metricsHelper,times(2)).recordSuccessRate(arg1.capture(),arg2.capture());
//        assertEquals(Constants.CDC_REMINDER_CONSUMER, arg1.getAllValues().get(1));
//        assertEquals("greater than 5 Min", arg2.getAllValues().get(1));
//    }
//    @Test
//    public void testListenSms1MinDelay(){
//        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
//        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
//        long updatedAt=DateUtil.minutesIncrDecr(new Date(),-2).getTime();
//        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":"+updatedAt+",\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
//        List<String> msgs = new ArrayList<>();
//        msgs.add(msg);
//        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
//        verify(metricsHelper,times(2)).recordSuccessRate(arg1.capture(),arg2.capture());
//        assertEquals(Constants.CDC_REMINDER_CONSUMER, arg1.getAllValues().get(1));
//        assertEquals("greater than 1 Min", arg2.getAllValues().get(1));
//    }
//    @Test
//    public void testListenSms2SecDelay(){
//        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
//        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
//        long updatedAt=DateUtil.secondsIncrDecr(new Date(),-2).getTime();
//        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":"+updatedAt+",\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
//        List<String> msgs = new ArrayList<>();
//        msgs.add(msg);
//        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
//        verify(metricsHelper,times(2)).recordSuccessRate(arg1.capture(),arg2.capture());
//        assertEquals(Constants.CDC_REMINDER_CONSUMER, arg1.getAllValues().get(1));
//        assertEquals("less than 1 Min", arg2.getAllValues().get(1));
//    }

    @Test
    public void testListenSms1(){
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"eweh232y493b2igu\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        List<String> msgs = new ArrayList<>();
        msgs.add(msg);
        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
    }

    @Test
    public void testListenSms2(){
        List<String> msgs = new ArrayList<>();
        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
    }
    @Test
    public void testListenSms3(){
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":\"2023-03-26T18:29:59.999Z\",\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":\"2023-03-26T18:29:59.999Z\",\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        List<String> msgs = new ArrayList<>();
        msgs.add(msg);
        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
    }
    @Test
    public void testListenSmsBillPaidEvent(){
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":null,\"due_amount\":null,\"amount\":{\"value\":-1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":null,\"payment_date\":{\"value\":\"2023-03-26T18:29:59.999Z\",\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"eventState\\\":\\\"bill_paid\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        List<String> msgs = new ArrayList<>();
        msgs.add(msg);
        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
    }
    @Test
    public void testListenSmsBillPaidEventPaymentDateNull(){
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":null,\"due_amount\":null,\"amount\":{\"value\":-1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":null,\"payment_date\":{\"value\":\"12\",\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"eventState\\\":\\\"bill_paid\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        List<String> msgs = new ArrayList<>();
        msgs.add(msg);
        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
    }

    @Test
    public void testListenValidation(){
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"83eb7c066aefd41cdd057ac060652be0\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":\"2023-02-21T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":\"2023-08-01T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        NonPaytmCDC nonPaytmCDC = JsonUtils.parseJsonWithCustomMapper(msg, NonPaytmCDC.class);
        ReminderCDC reminderCDC = CDCConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDC);
        assertEquals("validation",reminderCDC.getAfter().getEventSource().getValue());

    }

    @Test
    public void testExtraCase1(){
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"83eb7c066aefd41cdd057ac060652be0\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":\"2023-02-21T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":\"2023-08-01T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"billFetchDate\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\",\\\"sms_date_time\\\":1725712503834}\",\"deletion_ts\":null,\"set\":true}}}";
        NonPaytmCDC nonPaytmCDC = JsonUtils.parseJsonWithCustomMapper(msg, NonPaytmCDC.class);
        ReminderCDC reminderCDC = CDCConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDC);
        assertEquals("validation",reminderCDC.getAfter().getEventSource().getValue());
        assertEquals("{\"last_paid_amount\":\"209\",\"created_source\":\"validationSync\",\"updated_source\":\"validationSync\",\"sms_date_time\":\"1725712503834\",\"billFetchDate\":\"*************\"}",reminderCDC.getAfter().getExtra().getValue());

    }

    @Test
    public void testListenSmsCreatedSource(){
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"83eb7c066aefd41cdd057ac060652be0\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":\"2023-02-21T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":\"2023-08-01T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        NonPaytmCDC nonPaytmCDC = JsonUtils.parseJsonWithCustomMapper(msg, NonPaytmCDC.class);
        ReminderCDC reminderCDC = CDCConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDC);
        assertEquals("sms",reminderCDC.getAfter().getEventSource().getValue());

    }

    @Test
    public void testCsvCreatedSource(){
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"83eb7c066aefd41cdd057ac060652be0\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":\"2023-02-21T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":\"2023-08-01T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"csv\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        NonPaytmCDC nonPaytmCDC = JsonUtils.parseJsonWithCustomMapper(msg, NonPaytmCDC.class);
        ReminderCDC reminderCDC = CDCConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDC);
        assertEquals(Constants.EVENT_SOURCE.CSV,reminderCDC.getAfter().getEventSource().getValue());
    }

    @Test
    public void testWithNullCreatedSource(){
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"83eb7c066aefd41cdd057ac060652be0\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":\"2023-02-21T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":\"2023-08-01T00:00:00+05:30\",\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"null\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        NonPaytmCDC nonPaytmCDC = JsonUtils.parseJsonWithCustomMapper(msg, NonPaytmCDC.class);
        ReminderCDC reminderCDC = CDCConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDC);
        assertEquals(Constants.EVENTSOURCE_SMS,reminderCDC.getAfter().getEventSource().getValue());
    }

    @Test
    public void testListenSms10(){
        ArgumentCaptor<String> arg1 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> arg2 = ArgumentCaptor.forClass(String.class);
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        List<String> msgs = new ArrayList<>();
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(new ArrayList<>());
        when(serviceConfig.isRedirectionToRollbackTopic()).thenReturn(true);
        msgs.add(msg);
        cdcReminderKafkaConsumer.listenSms(msgs,acknowledgment);
//        verify(metricsHelper,times(2)).recordSuccessRate(arg1.capture(),arg2.capture());
//        assertEquals(Constants.CDC_REMINDER_CONSUMER, arg1.getAllValues().get(1));
//        assertEquals("greater than 60 Min", arg2.getAllValues().get(1));
    }
}
