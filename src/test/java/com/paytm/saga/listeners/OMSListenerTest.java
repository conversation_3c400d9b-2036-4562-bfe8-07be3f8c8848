package com.paytm.saga.listeners;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.exception.OMSListenerException;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.service.RPSService;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.dto.catalogue.ProductAttributes;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.service.*;
import org.json.JSONObject;
import com.paytm.saga.service.external.EsService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.Constants.CommonConstants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.OMSResponseModel;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.util.JsonUtils;

import junit.framework.TestCase;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class OMSListenerTest extends TestCase {
	@Mock
	private DCATService dcatService;
	@Mock
	private RPSService rpsService;
	@Mock
	private ChannelHistoryRepositoryWrapperService channelHistoryRepository;
	@Mock
	private MappingService mappingService;
	@Mock
	private ReminderHistoryService reminderHistoryService;
	@Mock
	public RecentsRepository recentsRepository;
	@Mock
	private DropOffDBHelper dropOffDBHelper;
	@Mock
	private KafkaProducerService kafkaProducerService;
	@Mock
	private ChannelHistoryService channelHistoryService;
	@Mock
	private EvictCacheServiceImpl evictCacheService ;
	@Mock
	private RecentService recentService;

	@Mock
	private CVRProductCache cvrProductCache;

	@Mock
	private  EsService esService;
	@Mock
    MetricsHelper metricsHelper;

	private OMSListeners omsListeners;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
		omsListeners = new OMSListeners(dcatService,
				channelHistoryRepository,
                mappingService,
                reminderHistoryService,
                dropOffDBHelper,metricsHelper, channelHistoryService, evictCacheService, recentService,esService,rpsService);
	}

	private OMSResponseModel createOMSResponseModelDummy() {
		String data = "{\"data\":{},\"id\":************,\"payment_status\":1,\"customer_id\":1006891,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"in_create_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https://paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"Customer Name\\\":\\\"JAI BHARAT FURNACES  P.LTD.\\\",\\\"Office code\\\":\\\"2101310\\\",\\\"binder number\\\":\\\"9652\\\",\\\"invoice id\\\":\\\"*********\\\",\\\"Bill date\\\":\\\"10-06-2019\\\",\\\"Due date\\\":\\\"2019-09-27\\\",\\\"amount payable\\\":\\\"310\\\",\\\"bill month\\\":\\\"10-06-2019\\\",\\\"BCITS id\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-04-14T14:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"shipped_at\":\"0000-00-00 00:00:00\",\"delivered_at\":\"2021-02-04T12:23:44.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-04-12T23:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"XXXX XXXX XXXX 2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		return omsData;
	}

	private OMSResponseModel createOMSResponseModelDummyFailure() {
		String data = "{\"data\":{},\"id\":\"123543\",\"payment_status\":2,\"channel_id\":\"WEB 2\",\"customer_id\":100119,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":*************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"airtel\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"airtel\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2023-04-05T13:00:00.000Z\\\",\\\"in_create_time\\\":\\\"2023-04-05T13:00:00.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https: //paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"CustomerName\\\":\\\"JAIBHARATFURNACESP.LTD.\\\",\\\"Officecode\\\":\\\"2101310\\\",\\\"bindernumber\\\":\\\"9652\\\",\\\"invoiceid\\\":\\\"*********\\\",\\\"Billdate\\\":\\\"10-06-2022\\\",\\\"Duedate\\\":\\\"2022-09-27\\\",\\\"amountpayable\\\":\\\"310\\\",\\\"billmonth\\\":\\\"10-09-2022\\\",\\\"BCITSid\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2023-04-05T13:00:00.000Z\",\"updated_at\":\"2023-04-05T13:00:00.000Z\",\"shipped_at\":\"2021-10-28T10:52:20.000Z\",\"delivered_at\":\"2021-10-28T10:52:20.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2023-04-05T13:00:00.000Z\",\"updated_at\":\"2023-04-05T13:00:00.000Z\",\"items\":[{\"id\":1234,\"order_id\":\"3210\",\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"promo_details\":{}},\"attributes\":{},\"product_id\":183412,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":4,\"fulfillment_req\":{\"price\":874,\"recharge_number\":\"9945359495\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7386\\\",\\\"additionaldata\\\":{\\\"Consumer Name\\\": \\\"Aditya2\\\"},\\\"producttype\\\":\\\"Recharge\\\",\\\"nickname\\\":\\\"Aditya\\\"}\",\"created_at\":\"2023-04-05T13:00:00.000Z\",\"updated_at\":\"2023-04-05T13:00:00.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":876,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":186,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":4,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"9945359495\\\"}\",\"product\":{\"id\":**********,\"category_id\":21,\"attributes\":{\"service\":\"mobile\",\"paytype\":\"postpaid\",\"operator\":\"airtel\",\"circle\":\"andhra pradesh\",\"service_label\":\"mobile\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		return omsData;
	}

	private OMSResponseModel createOMSResponseModelDummyPending() {
		String data = "{\"data\":{},\"id\":\"123\",\"payment_status\":2,\"channel_id\":\"WEB 2\",\"customer_id\":100119,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":*************,\"status\":15,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"07\\\",\\\"operator\\\":\\\"airtel\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"airtel\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2023-04-06T13:00:00.000Z\\\",\\\"in_create_time\\\":\\\"2023-04-06T13:00:00.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https: //paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"CustomerName\\\":\\\"JAIBHARATFURNACESP.LTD.\\\",\\\"Officecode\\\":\\\"2101310\\\",\\\"bindernumber\\\":\\\"9652\\\",\\\"invoiceid\\\":\\\"*********\\\",\\\"Billdate\\\":\\\"10-06-2022\\\",\\\"Duedate\\\":\\\"2022-09-27\\\",\\\"amountpayable\\\":\\\"310\\\",\\\"billmonth\\\":\\\"10-09-2022\\\",\\\"BCITSid\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2023-04-06T13:00:00.000Z\",\"updated_at\":\"2023-04-06T13:00:00.000Z\",\"shipped_at\":\"2021-10-28T10:52:20.000Z\",\"delivered_at\":\"2021-10-28T10:52:20.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2023-04-06T13:00:00.000Z\",\"updated_at\":\"2023-04-06T13:00:00.000Z\",\"items\":[{\"id\":1234,\"order_id\":\"1232342\",\"fulfillment_id\":***************,\"status\":15,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"promo_details\":{}},\"attributes\":{},\"product_id\":186,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":4,\"fulfillment_req\":{\"price\":501,\"recharge_number\":\"9945359495\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7386\\\",\\\"additionaldata\\\":{\\\"Consumer Name\\\": \\\"Aditya2\\\"},\\\"producttype\\\":\\\"Recharge\\\",\\\"nickname\\\":\\\"Aditya\\\"}\",\"created_at\":\"2023-04-06T13:00:00.000Z\",\"updated_at\":\"2023-04-06T13:00:00.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":186,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":4,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"9945359495\\\"}\",\"product\":{\"id\":**********,\"category_id\":21,\"attributes\":{\"service\":\"mobile\",\"paytype\":\"postpaid\",\"operator\":\"airtel\",\"circle\":\"andhra pradesh\",\"service_label\":\"mobile\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		return omsData;
	}

	private OMSResponseModel createOMSResponseModelDummySuccess() {
		String data = "{\"data\":{},\"id\":\"123\",\"payment_status\":2,\"channel_id\":\"WEB 2\",\"customer_id\":100119,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":*************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"00\\\",\\\"operator\\\":\\\"airtel\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"airtel\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2023-04-04T13:00:00.000Z\\\",\\\"in_create_time\\\":\\\"2023-04-04T13:00:00.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https: //paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"CustomerName\\\":\\\"JAIBHARATFURNACESP.LTD.\\\",\\\"Officecode\\\":\\\"2101310\\\",\\\"bindernumber\\\":\\\"9652\\\",\\\"invoiceid\\\":\\\"*********\\\",\\\"Billdate\\\":\\\"10-06-2022\\\",\\\"Duedate\\\":\\\"2022-09-27\\\",\\\"amountpayable\\\":\\\"310\\\",\\\"billmonth\\\":\\\"10-09-2022\\\",\\\"BCITSid\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2023-04-04T13:00:00.000Z\",\"updated_at\":\"2023-04-04T13:00:00.000Z\",\"shipped_at\":\"2021-10-28T10:52:20.000Z\",\"delivered_at\":\"2021-10-28T10:52:20.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2023-04-04T13:00:00.000Z\",\"updated_at\":\"2023-04-04T13:00:00.000Z\",\"items\":[{\"id\":1234,\"order_id\":\"12354\",\"fulfillment_id\":***************,\"status\":7,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"promo_details\":{}},\"attributes\":{},\"product_id\":186,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":4,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"9945359495\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7386\\\",\\\"additionaldata\\\":{\\\"Consumer Name\\\": \\\"Aditya2\\\"},\\\"producttype\\\":\\\"Recharge\\\",\\\"nickname\\\":\\\"Aditya\\\"}\",\"created_at\":\"2023-04-04T13:00:00.000Z\",\"updated_at\":\"2023-04-04T13:00:00.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":186,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":4,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"9945359495\\\"}\",\"product\":{\"id\":**********,\"category_id\":21,\"attributes\":{\"service\":\"mobile\",\"paytype\":\"prepaid\",\"operator\":\"airtel\",\"circle\":\"andhra pradesh\",\"service_label\":\"mobile\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		return omsData;
	}
	
	private OMSResponseModel createOMSResponseSuccessModelDummy() {
		String data = "{\"data\":{},\"id\":************,\"payment_status\":1,\"channel_id\":\"\",\"customer_id\":23,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"00\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-10-28T10:52:20.000Z\\\",\\\"in_create_time\\\":\\\"2021-06-13T10:52:20.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https: //paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"CustomerName\\\":\\\"JAIBHARATFURNACESP.LTD.\\\",\\\"Officecode\\\":\\\"2101310\\\",\\\"bindernumber\\\":\\\"9652\\\",\\\"invoiceid\\\":\\\"*********\\\",\\\"Billdate\\\":\\\"10-06-2019\\\",\\\"Duedate\\\":\\\"2019-09-27\\\",\\\"amountpayable\\\":\\\"310\\\",\\\"billmonth\\\":\\\"10-06-2019\\\",\\\"BCITSid\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-06-13T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"shipped_at\":\"2021-10-28T10:52:20.000Z\",\"delivered_at\":\"2021-10-28T10:52:20.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"4054 50XX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-10-28T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"405450XXXXXX2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		return omsData;
	}
	
	private OMSResponseModel createOMSResponseSuccessModelDummyWithNickname() {
		String data="{\"data\":{},\"id\":************,\"payment_status\":1,\"channel_id\":\"\",\"customer_id\":23,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"00\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-10-28T10:52:20.000Z\\\",\\\"in_create_time\\\":\\\"2021-06-13T10:52:20.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https: //paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"CustomerName\\\":\\\"JAIBHARATFURNACESP.LTD.\\\",\\\"Officecode\\\":\\\"2101310\\\",\\\"bindernumber\\\":\\\"9652\\\",\\\"invoiceid\\\":\\\"*********\\\",\\\"Billdate\\\":\\\"10-06-2019\\\",\\\"Duedate\\\":\\\"2019-09-27\\\",\\\"amountpayable\\\":\\\"310\\\",\\\"billmonth\\\":\\\"10-06-2019\\\",\\\"BCITSid\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-06-13T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"shipped_at\":\"2021-10-28T10:52:20.000Z\",\"delivered_at\":\"2021-10-28T10:52:20.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"4054 50XX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\",\\\"nickname\\\":\\\"gurjinder\\\"}\",\"created_at\":\"2021-10-28T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"405450XXXXXX2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		return omsData;
	}

	private OMSResponseModel createOMSResponseModelDummyWithPar() {
		String data = "{\"data\":{},\"id\":************,\"payment_status\":1,\"customer_id\":1006891,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"in_create_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https://paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"Customer Name\\\":\\\"JAI BHARAT FURNACES  P.LTD.\\\",\\\"Office code\\\":\\\"2101310\\\",\\\"binder number\\\":\\\"9652\\\",\\\"invoice id\\\":\\\"*********\\\",\\\"Bill date\\\":\\\"10-06-2019\\\",\\\"Due date\\\":\\\"2019-09-27\\\",\\\"amount payable\\\":\\\"310\\\",\\\"bill month\\\":\\\"10-06-2019\\\",\\\"BCITS id\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-04-14T14:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"shipped_at\":\"0000-00-00 00:00:00\",\"delivered_at\":\"2021-02-04T12:23:44.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-04-12T23:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"XXXX XXXX XXXX 2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		omsData.getItems().get(0).setFulfillmentReq("{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\",\"recharge_number_4\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\"}");
		omsData.getItems().get(0).setMeta_data("{\"panUniqueReference\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"producttype\":\"Recharge\"}");
		return omsData;
	}

	private OMSResponseModel createOMSResponseModelDummyWithCinR3() {
		String data = "{\"data\":{},\"id\":************,\"payment_status\":1,\"customer_id\":1006891,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"in_create_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https://paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"Customer Name\\\":\\\"JAI BHARAT FURNACES  P.LTD.\\\",\\\"Office code\\\":\\\"2101310\\\",\\\"binder number\\\":\\\"9652\\\",\\\"invoice id\\\":\\\"*********\\\",\\\"Bill date\\\":\\\"10-06-2019\\\",\\\"Due date\\\":\\\"2019-09-27\\\",\\\"amount payable\\\":\\\"310\\\",\\\"bill month\\\":\\\"10-06-2019\\\",\\\"BCITS id\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-04-14T14:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"shipped_at\":\"0000-00-00 00:00:00\",\"delivered_at\":\"2021-02-04T12:23:44.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-04-12T23:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"XXXX XXXX XXXX 2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		omsData.getItems().get(0).setFulfillmentReq("{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\",\"recharge_number_3\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\"}");
		omsData.getItems().get(0).setMeta_data("{\"producttype\":\"Recharge\"}");
		return omsData;
	}



	private OMSResponseModel createOMSResponseModelDummyWithCinR2() {
		String data = "{\"data\":{},\"id\":************,\"payment_status\":1,\"customer_id\":1006891,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"in_create_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https://paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"Customer Name\\\":\\\"JAI BHARAT FURNACES  P.LTD.\\\",\\\"Office code\\\":\\\"2101310\\\",\\\"binder number\\\":\\\"9652\\\",\\\"invoice id\\\":\\\"*********\\\",\\\"Bill date\\\":\\\"10-06-2019\\\",\\\"Due date\\\":\\\"2019-09-27\\\",\\\"amount payable\\\":\\\"310\\\",\\\"bill month\\\":\\\"10-06-2019\\\",\\\"BCITS id\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-04-14T14:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"shipped_at\":\"0000-00-00 00:00:00\",\"delivered_at\":\"2021-02-04T12:23:44.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-04-12T23:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"XXXX XXXX XXXX 2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		omsData.getItems().get(0).setFulfillmentReq("{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\",\"recharge_number_2\":\"CIN_20210408031212dd1b4990e4207f88cc709873d0b7306\"}");
		omsData.getItems().get(0).setMeta_data("{\"par\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"producttype\":\"Recharge\"}");
		return omsData;
	}

	private OMSResponseModel createOMSResponseModelDummyWithWrongCinR2() {
		String data = "{\"data\":{},\"id\":************,\"payment_status\":1,\"customer_id\":1006891,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"in_create_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https://paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"Customer Name\\\":\\\"JAI BHARAT FURNACES  P.LTD.\\\",\\\"Office code\\\":\\\"2101310\\\",\\\"binder number\\\":\\\"9652\\\",\\\"invoice id\\\":\\\"*********\\\",\\\"Bill date\\\":\\\"10-06-2019\\\",\\\"Due date\\\":\\\"2019-09-27\\\",\\\"amount payable\\\":\\\"310\\\",\\\"bill month\\\":\\\"10-06-2019\\\",\\\"BCITS id\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-04-14T14:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"shipped_at\":\"0000-00-00 00:00:00\",\"delivered_at\":\"2021-02-04T12:23:44.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-04-12T23:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"XXXX XXXX XXXX 2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		omsData.getItems().get(0).setFulfillmentReq("{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\",\"recharge_number_2\":\"20210408031212dd1b4990e4207f88cc709873d0b7306CIN\"}");
		omsData.getItems().get(0).setMeta_data("{\"par\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"producttype\":\"Recharge\"}");
		return omsData;
	}

	private OMSResponseModel createOMSResponseModelDummyWithNullCinAndPar() {
		String data = "{\"data\":{},\"id\":************,\"payment_status\":1,\"customer_id\":1006891,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"in_create_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https://paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"Customer Name\\\":\\\"JAI BHARAT FURNACES  P.LTD.\\\",\\\"Office code\\\":\\\"2101310\\\",\\\"binder number\\\":\\\"9652\\\",\\\"invoice id\\\":\\\"*********\\\",\\\"Bill date\\\":\\\"10-06-2019\\\",\\\"Due date\\\":\\\"2019-09-27\\\",\\\"amount payable\\\":\\\"310\\\",\\\"bill month\\\":\\\"10-06-2019\\\",\\\"BCITS id\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-04-14T14:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"shipped_at\":\"0000-00-00 00:00:00\",\"delivered_at\":\"2021-02-04T12:23:44.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-04-12T23:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"XXXX XXXX XXXX 2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		omsData.getItems().get(0).setFulfillmentReq("{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\"}");
		omsData.getItems().get(0).setMeta_data("{\"producttype\":\"Recharge\"}");
		return omsData;
	}

	private OMSResponseModel createOMSResponseModelDummyWithNullRN1() {
		String data = "{\"data\":{},\"id\":************,\"payment_status\":1,\"customer_id\":1006891,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"in_create_time\\\":\\\"2021-02-04T12:23:42.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https://paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"Customer Name\\\":\\\"JAI BHARAT FURNACES  P.LTD.\\\",\\\"Office code\\\":\\\"2101310\\\",\\\"binder number\\\":\\\"9652\\\",\\\"invoice id\\\":\\\"*********\\\",\\\"Bill date\\\":\\\"10-06-2019\\\",\\\"Due date\\\":\\\"2019-09-27\\\",\\\"amount payable\\\":\\\"310\\\",\\\"bill month\\\":\\\"10-06-2019\\\",\\\"BCITS id\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-04-14T14:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"shipped_at\":\"0000-00-00 00:00:00\",\"delivered_at\":\"2021-02-04T12:23:44.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":401,\"recharge_number\":\"XXXX XXXX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-04-12T23:23:44.000Z\",\"updated_at\":\"2021-02-04T14:23:44.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":401,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":401,\\\"recharge_number\\\":\\\"XXXX XXXX XXXX 2178\\\"}\",\"product\":{\"id\":**********,\"category_id\":156705,\"attributes\":{\"service\":\"Financial Services\",\"paytype\":\"credit card\",\"operator\":\"neft_citibank\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
		OMSResponseModel omsData = JsonUtils.parseJson(data, OMSResponseModel.class);
		omsData.getItems().get(0).setFulfillmentReq("{\"price\":401}");
		omsData.getItems().get(0).setMeta_data("{\"producttype\":\"Recharge\"}");
		return omsData;
	}

	private ReminderHistory mockReminderHistoryObjectWithPar() {
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setAmount(100.0);
		reminderHistory.setBill_date(new Date());
		reminderHistory.setCircle("Delhi NCR");
		reminderHistory.setCreated_at(new Date());
		reminderHistory.setCurrentMinBillAmount(50.0);
		reminderHistory.setCustomerId(10000060590L);
		reminderHistory.setDue_date(new Date());
		reminderHistory.setIs_automatic(0);
		reminderHistory.setIsPartial(0);
		reminderHistory.setOperator("Airtel");
		reminderHistory.setPaytype("credit card");
		reminderHistory.setProductId(100L);
		reminderHistory.setRechargeNumber("XXXX XXXX XXXX 2178");
		reminderHistory.setPar("20210408031212dd1b4990e4207f88cc709873d0b7306");
		reminderHistory.setService("Financial Services");
		reminderHistory.setStatus(1);
		reminderHistory.setUpdatedAt(new Date());
		return reminderHistory;
	}

	private ReminderHistory mockReminderHistoryObjectPostpaid() {
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setAmount(100.0);
		reminderHistory.setBill_date(new Date());
		reminderHistory.setCircle("andhra pradesh");
		reminderHistory.setCreated_at(new Date());
		reminderHistory.setCurrentMinBillAmount(50.0);
		reminderHistory.setCustomerId(10000060590L);
		reminderHistory.setDue_date(new Date());
		reminderHistory.setIs_automatic(0);
		reminderHistory.setIsPartial(0);
		reminderHistory.setOperator("airtel");
		reminderHistory.setPaytype("postpaid");
		reminderHistory.setProductId(100L);
		reminderHistory.setRechargeNumber("9945359495");
		reminderHistory.setPar(null);
		reminderHistory.setService("mobile");
		reminderHistory.setStatus(1);
		reminderHistory.setUpdatedAt(new Date());
		return reminderHistory;
	}

	private ReminderHistory mockReminderHistoryObjectWithRefId() {
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setAmount(100.0);
		reminderHistory.setBill_date(new Date());
		reminderHistory.setCircle("Delhi NCR");
		reminderHistory.setCreated_at(new Date());
		reminderHistory.setCurrentMinBillAmount(50.0);
		reminderHistory.setCustomerId(10000060590L);
		reminderHistory.setDue_date(new Date());
		reminderHistory.setIs_automatic(0);
		reminderHistory.setIsPartial(0);
		reminderHistory.setOperator("Airtel");
		reminderHistory.setPaytype("credit card");
		reminderHistory.setProductId(100100L);
		reminderHistory.setRechargeNumber("XXXX XXXX XXXX 2178");
		reminderHistory.setReference_id("20210408031212dd1b4990e4207f88cc709873d0b7306");
		reminderHistory.setService("Financial Services");
		reminderHistory.setStatus(1);
		reminderHistory.setUpdatedAt(new Date());
		return reminderHistory;
	}

	@Test
	public void testCCRechargeNewCustomer() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();

		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				omsResponseModel.getCustomerId(), "XXXX XXXX XXXX 2178", "financial services", "neft_citibank"))
						.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument2.getValue().getBillsObj().get("mcn"));
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument.getValue().getBillsObj().get("mcn"));

	}
	
	@Test
	public void testCylinderAgentName() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService(CommonConstants.CYLINDER_SERVICE);
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype(Constants.CommonConstants.POSTPAID_PAYTYPE);
		omsResponseModel.getItems().get(0).setMeta_data("{\"cylinder_agency_name\":\"Agency Name\"}");

		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				any(), any(), any(), any()))
						.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("Agency Name", argument2.getValue().getBillsObj().get(Constants.CYLINDER_AGENCY_NAME));
		assertEquals("Agency Name", argument.getValue().getBillsObj().get(Constants.CYLINDER_AGENCY_NAME));

	}

	@Test
	public void testCylinderRechargeNumberUpto8() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService(CommonConstants.CYLINDER_SERVICE);
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype(Constants.CommonConstants.POSTPAID_PAYTYPE);
		omsResponseModel.getItems().get(0).setMeta_data("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"***********\",\"recharge_number_3\":\"***********\",\"recharge_number_4\":\"***********\",\"recharge_number_5\":\"***********\",\"recharge_number_6\":\"***********\",\"recharge_number_7\":\"***********\",\"recharge_number_8\":\"***********\"}");
		omsResponseModel.getItems().get(0).setFulfillmentReq("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"69768978\",\"recharge_number_3\":\"60002978897154676\",\"recharge_number_4\":\"09878786\",\"recharge_number_5\":\"546556\",\"recharge_number_6\":\"6689787890\",\"recharge_number_7\":\"********\",\"recharge_number_8\":\"79879236\"}");
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("69768978", argument2.getValue().getRecharge_number_2());
		assertEquals("60002978897154676", argument2.getValue().getRecharge_number_3());
		assertEquals("09878786", argument2.getValue().getRecharge_number_4());
		assertEquals("546556", argument2.getValue().getRecharge_number_5());
		assertEquals("6689787890", argument2.getValue().getRecharge_number_6());
		assertEquals("********", argument2.getValue().getRecharge_number_7());
		assertEquals("79879236", argument2.getValue().getRecharge_number_8());
		assertEquals("***********", argument2.getValue().getRechargeNumber());
	}

	@Test
	public void testRentRechargeNumber8() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("Rent Payment");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("Postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(262072L);
		omsResponseModel.getItems().get(0).setMeta_data("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"***********\",\"recharge_number_3\":\"***********\",\"recharge_number_4\":\"***********\",\"recharge_number_5\":\"***********\",\"recharge_number_6\":\"***********\",\"recharge_number_7\":\"***********\",\"recharge_number_8\":\"***********\"}");
		omsResponseModel.getItems().get(0).setFulfillmentReq("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"69768978\",\"recharge_number_3\":\"60002978897154676\",\"recharge_number_4\":\"09878786\",\"recharge_number_5\":\"546556\",\"recharge_number_7\":\"********\",\"recharge_number_8\":\"79879236\"}");
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper, Mockito.times(1)).insertDropOff(argument2.capture());
		assertEquals("69768978", argument2.getValue().getRecharge_number_2());
		assertEquals("60002978897154676", argument2.getValue().getRecharge_number_3());
		assertEquals("09878786", argument2.getValue().getRecharge_number_4());
		assertEquals("546556", argument2.getValue().getRecharge_number_5());
		assertEquals(null, argument2.getValue().getRecharge_number_6());
		assertEquals("***********", argument2.getValue().getRecharge_number_7());
		assertEquals("79879236", argument2.getValue().getRecharge_number_8());
		assertEquals("********", argument2.getValue().getRechargeNumber());
	}

	@Test
	public void testBPRechargeNumber8() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("business payment");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("Postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(262072L);
		omsResponseModel.getItems().get(0).setMeta_data("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"***********\",\"recharge_number_3\":\"***********\",\"recharge_number_4\":\"***********\",\"recharge_number_5\":\"***********\",\"recharge_number_6\":\"***********\",\"recharge_number_7\":\"***********\",\"recharge_number_8\":\"***********\"}");
		omsResponseModel.getItems().get(0).setFulfillmentReq("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"69768978\",\"recharge_number_3\":\"60002978897154676\",\"recharge_number_4\":\"09878786\",\"recharge_number_5\":\"546556\",\"recharge_number_7\":\"********\",\"recharge_number_8\":\"79879236\"}");
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper, Mockito.times(1)).insertDropOff(argument2.capture());
		assertEquals("69768978", argument2.getValue().getRecharge_number_2());
		assertEquals("60002978897154676", argument2.getValue().getRecharge_number_3());
		assertEquals("09878786", argument2.getValue().getRecharge_number_4());
		assertEquals("546556", argument2.getValue().getRecharge_number_5());
		assertEquals(null, argument2.getValue().getRecharge_number_6());
		assertEquals("***********", argument2.getValue().getRecharge_number_7());
		assertEquals("79879236", argument2.getValue().getRecharge_number_8());
		assertEquals("********", argument2.getValue().getRechargeNumber());
	}


	@Test
	public void testBPRechargeNumber7null() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("business payment");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("Postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(262072L);
		omsResponseModel.getItems().get(0).setMeta_data("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"***********\",\"recharge_number_3\":\"***********\",\"recharge_number_4\":\"***********\",\"recharge_number_5\":\"***********\",\"recharge_number_6\":\"***********\",\"recharge_number_7\":null,\"recharge_number_8\":\"***********\"}");
		omsResponseModel.getItems().get(0).setFulfillmentReq("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"69768978\",\"recharge_number_3\":\"60002978897154676\",\"recharge_number_4\":\"09878786\",\"recharge_number_5\":\"546556\",\"recharge_number_7\":null,\"recharge_number_8\":\"79879236\"}");
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper, Mockito.times(1)).insertDropOff(argument2.capture());
		assertEquals("69768978", argument2.getValue().getRecharge_number_2());
		assertEquals("60002978897154676", argument2.getValue().getRecharge_number_3());
		assertEquals("09878786", argument2.getValue().getRecharge_number_4());
		assertEquals("546556", argument2.getValue().getRecharge_number_5());
		assertEquals(null, argument2.getValue().getRecharge_number_6());
		assertEquals(null, argument2.getValue().getRecharge_number_7());
		assertEquals("79879236", argument2.getValue().getRecharge_number_8());
		assertEquals("***********", argument2.getValue().getRechargeNumber());
	}


	@Test
	public void testTuitionRechargeNumber8() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("Tuition Fees");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("Postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(262072L);
		omsResponseModel.getItems().get(0).setMeta_data("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"***********\",\"recharge_number_3\":\"***********\",\"recharge_number_4\":\"***********\",\"recharge_number_5\":\"***********\",\"recharge_number_6\":\"***********\",\"recharge_number_7\":\"***********\",\"recharge_number_8\":\"***********\"}");
		omsResponseModel.getItems().get(0).setFulfillmentReq("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"69768978\",\"recharge_number_3\":\"6000297889754676\",\"recharge_number_4\":\"09878786\",\"recharge_number_5\":\"546556\",\"recharge_number_7\":\"********\",\"recharge_number_8\":\"79879236\"}");
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel, "");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper, Mockito.times(1)).insertDropOff(argument2.capture());
		assertEquals("69768978", argument2.getValue().getRecharge_number_2());
		assertEquals("6000297889754676", argument2.getValue().getRecharge_number_3());
		assertEquals("09878786", argument2.getValue().getRecharge_number_4());
		assertEquals("546556", argument2.getValue().getRecharge_number_5());
		assertEquals(null, argument2.getValue().getRecharge_number_6());
		assertEquals("********", argument2.getValue().getRecharge_number_7());
		assertEquals("79879236", argument2.getValue().getRecharge_number_8());
		assertEquals("***********", argument2.getValue().getRechargeNumber());
	}
	
	@Test
	public void testCylinderNoAgentName() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService(CommonConstants.CYLINDER_SERVICE);
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype(Constants.CommonConstants.POSTPAID_PAYTYPE);
		omsResponseModel.getItems().get(0).setMeta_data("{}");

		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				any(), any(), any(), any()))
						.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertNull(argument2.getValue().getBillsObj().get(Constants.CYLINDER_AGENCY_NAME));
		assertNull(argument.getValue().getBillsObj().get(Constants.CYLINDER_AGENCY_NAME));

	}
	
	@Test
	public void testCCRechargeOldCustomer() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();

		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument2.getValue().getBillsObj().get("mcn"));
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument.getValue().getBillsObj().get("mcn"));

	}
	
	@Test
	public void testCCRechargeRecent() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseSuccessModelDummy();

		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		
		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);

		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());
		assertEquals(new Long(************L), orderId.getValue());
		assertEquals("405450XXXXXX2178", mcn.getValue());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", cin.getValue());
		assertEquals(new Boolean(false), is_tokenized_transaction.getValue());
		assertEquals(new Boolean(false), isNickNameUpdated.getValue());

	}
	
	@Test
	public void testCCRechargeRecentWithNickname() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseSuccessModelDummyWithNickname();

		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		
		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());
		assertEquals(omsResponseModel.getOrderId(), orderId.getValue());
		assertEquals("gurjinder", nickName.getValue());
		assertEquals("405450XXXXXX2178", mcn.getValue());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", cin.getValue());
		assertEquals(new Boolean(false), is_tokenized_transaction.getValue());

	}

	@Test
	public void testFinancialServicesIsSkipable() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		Boolean res = omsListeners.isSkippable(omsResponseModel.getItems().get(0));
		assertEquals(new Boolean(false), res);
	}

	@Test
	public void testCCWhenParIsSent() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyWithPar();


		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				omsResponseModel.getCustomerId(), "XXXX XXXX XXXX 2178", "financial services", "neft_citibank"))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getBillsObj().get("par"));
		assertEquals("XXXX XXXX XXXX 2178", argument2.getValue().getBillsObj().get("mcn"));
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getBillsObj().get("par"));
		assertEquals("XXXX XXXX XXXX 2178", argument.getValue().getBillsObj().get("mcn"));

	}

	@Test
	public void testCCWhenCinIsSent() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyWithCinR3();


		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				omsResponseModel.getCustomerId(), "XXXX XXXX XXXX 2178", "financial services", "neft_citibank"))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument2.getValue().getBillsObj().get("mcn"));
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument.getValue().getBillsObj().get("mcn"));

	}

	@Test
	public void testCCWhenCinIsSentInRecharge_number_2() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyWithCinR2();


		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				omsResponseModel.getCustomerId(), "XXXX XXXX XXXX 2178", "financial services", "neft_citibank"))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument2.getValue().getBillsObj().get("mcn"));
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument.getValue().getBillsObj().get("mcn"));

	}

	@Test
	public void testCCWhenCinIsSentInRecharge_number_2AndParInReminderHistory() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyWithCinR2();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithPar();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithPar());
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				omsResponseModel.getCustomerId(), "XXXX XXXX XXXX 2178", "financial services", "neft_citibank"))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument2.getValue().getBillsObj().get("mcn"));
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument.getValue().getBillsObj().get("mcn"));

	}

	@Test
	public void testInsertDropOffForInsurance() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("insurance");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("insurance");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(64739L);
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument2.capture());
	}

	@Test
	public void testInsertDropOffForApt() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("apartments");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("prepaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(101950L);
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument2.capture());
	}

	@Test
	public void testInsertDropOffForMunicipalTax() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("municipal payments");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(107730L);
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument2.capture());

	}

	@Test
	public void testCCWhenCinIsSentInRecharge_number_2AndParWithEmptyReminderHistory() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyWithCinR2();


		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();

		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				omsResponseModel.getCustomerId(), "XXXX XXXX XXXX 2178", "financial services", "neft_citibank"))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument2.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument2.getValue().getBillsObj().get("mcn"));
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getRechargeNumber());
		assertEquals("20210408031212dd1b4990e4207f88cc709873d0b7306", argument.getValue().getBillsObj().get("cin"));
		assertEquals("XXXX XXXX XXXX 2178", argument.getValue().getBillsObj().get("mcn"));

	}

	@Test
	public void testCCWhenCinIsSentInRecharge_number_2WithWrongFormat() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyWithWrongCinR2();


		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService, Mockito.times(0)).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper, Mockito.times(0)).insertDropOff(argument2.capture());
	}

	@Test
	public void testCCWhenPrimaryKeyConstrainFails() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyWithNullRN1();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService, Mockito.times(0)).save(argument.capture(),argument1.capture());

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper, Mockito.times(0)).insertDropOff(argument2.capture());

	}

	@Test
	public void testCCWhenCinAndParAreNull() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyWithNullCinAndPar();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService, Mockito.times(0)).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper, Mockito.times(0)).insertDropOff(argument2.capture());

	}

	@Test
	public void testOperatorLogicCC() throws Exception{
		OMSResponseModel omsResponseModel = createOMSResponseSuccessModelDummy();
		ObjectMapper objectMapper = new ObjectMapper();

		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectWithRefId();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());

		ProductAttributes attributes = new ProductAttributes();
		attributes.setBankCode("hdfc");
		attributes.setIsPaytmFirstCard("1");
		attributes.setCardNetwork("rupay");
		Product product = new Product();
		product.setProductId(**********L);
		product.setService("financial services");
		product.setOperator("axis");
		product.setPayType("credit card");
		product.setAttributes(objectMapper.writeValueAsString(attributes));
		product.setStatus(1);
		CVRProductCache.getInstance().addProductDetails(product);

		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumerName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumerName.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());

		assertEquals("ccbp", operator.getValue());

	}

	@Test
	public void testInsertDropOffForLoan() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("loan");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("loan");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(198239L);
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				any(), any(), any(), any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument2.capture());

	}
	@Test
	public void testInsertDropOffForChallan() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("challan");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(104154L);
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				any(), any(), any(), any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument2.capture());

	}

	@Test
	public void testInsertDropOffForCableTv() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("cable tv");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(123988L);
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				any(), any(), any(), any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument2.capture());

	}
	@Test
	public void testInsertDropOffForEducation() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("education");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(46007L);
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				any(), any(), any(), any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");

		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument2.capture());

	}

	@Test
	public void testFailureTransaction() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyFailure();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectPostpaid();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithPar());
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				omsResponseModel.getCustomerId(), "9945359495", "mobile", "airtel"))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		assertEquals("9945359495", argument.getValue().getRechargeNumber());
		assertEquals("6", argument.getValue().getStatus());
		assertEquals("08", argument.getValue().getInResponseCode());
		assertEquals("9945359495", argument2.getValue().getRechargeNumber());
		assertEquals("6", argument2.getValue().getStatus());
		assertEquals("08", argument2.getValue().getIn_response_code());


	}

	@Test
	public void testPendingTransaction() {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyPending();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectPostpaid();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithPar());
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				omsResponseModel.getCustomerId(), "9945359495", "mobile", "airtel"))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());

		assertEquals("9945359495", argument.getValue().getRechargeNumber());
		assertEquals("15", argument.getValue().getStatus());
		assertEquals("07", argument.getValue().getInResponseCode());
		assertEquals("9945359495", argument2.getValue().getRechargeNumber());
		assertEquals("15", argument2.getValue().getStatus());
		assertEquals("07", argument2.getValue().getIn_response_code());


	}

	@Test
	public void testOMSFailureCase() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyFailure();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectPostpaid();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());

		JSONObject lastPenidngTxn = new JSONObject();
		lastPenidngTxn.put("txn_id", 12334L);
		lastPenidngTxn.put("txn_status", "07");
		lastPenidngTxn.put("txn_time", "2023-05-05T13:00:00.000Z");
		lastPenidngTxn.put("txn_amount", 847.0);
		lastPenidngTxn.put("channel_id","WEB 2");

		List<Recents> recents=new ArrayList<>();
		Recents recents1=new Recents();
		RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
		recentsPrimaryKey.setService("mobile");
		recentsPrimaryKey.setRechargeNumber("9945359495");
		recentsPrimaryKey.setCustomerId(100119L);
		recentsPrimaryKey.setPlanBucket("");
		recentsPrimaryKey.setOperator("airtel");
		recents1.setKey(recentsPrimaryKey);
		recents1.setDueDate(new Date());
		recents1.setDueAmount(100.0);
		recents1.setUpdatedAt(new Date());
		recents1.setLastPendingTxn(lastPenidngTxn.toString());
		recents.add(recents1);
		when(recentService.fetchRecentsFromDb(any(),any(),any(),any(),any())).thenReturn(recents);

		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());
		assertEquals("{\"txn_id\":123543,\"txn_time\":\"2023-04-05T13:00:00.000Z\",\"txn_amount\":876,\"txn_status\":\"08\",\"channel_id\":\"WEB 2\"}", lastFailureTxn.getValue());
		assertEquals("{\"txn_id\":12334,\"txn_time\":\"2023-05-05T13:00:00.000Z\",\"txn_amount\":847,\"txn_status\":\"07\",\"channel_id\":\"WEB 2\"}",lastPendingTxn.getValue());
		assertEquals("6",status.getValue());
		assertEquals("08",in_response_code.getValue());
		assertTrue(isTransaction.getValue());

	}

	@Test
	public void testOMSSuccessCaseFastag() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummySuccess();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectPostpaid();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());

		JSONObject lastSuccessTxn = new JSONObject();
		lastSuccessTxn.put("txn_id", 12334L);
		lastSuccessTxn.put("txn_status", "00");
		lastSuccessTxn.put("txn_time", "2023-05-05T13:00:00.000Z");
		lastSuccessTxn.put("txn_amount", 847.0);
		lastSuccessTxn.put("channel_id","WEB 2");

		List<Recents> recents=new ArrayList<>();
		Recents recents1=new Recents();
		RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
		recentsPrimaryKey.setService("fastag recharge");
		recentsPrimaryKey.setRechargeNumber("9945359495");
		recentsPrimaryKey.setCustomerId(100119L);
		recentsPrimaryKey.setPlanBucket("");
		recentsPrimaryKey.setOperator("LOW_BALANCE");
		recents1.setKey(recentsPrimaryKey);
		recents1.setDueDate(new Date());
		recents1.setDueAmount(100.0);
		recents1.setUpdatedAt(new Date());
		recents1.setLastPendingTxn(lastSuccessTxn.toString());
		recents.add(recents1);
		when(recentService.fetchRecentsFromDb(any(),any(),any(),any(),any())).thenReturn(recents);

		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());
		//verify(recentsRepository,  Mockito.times(1)).deleteRecentByCustomerIdAndServiceAndRecharge(100119L, "fastag recharge", "9945359495", "LOW_BALANCE", "");
		assertTrue(isTransaction.getValue());

	}



	@Test
	public void testOMSFailureCase2() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyFailure();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectPostpaid();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());
		assertEquals("{\"txn_id\":123543,\"txn_time\":\"2023-04-05T13:00:00.000Z\",\"txn_amount\":876,\"txn_status\":\"08\",\"channel_id\":\"WEB 2\"}", lastFailureTxn.getValue());
		assertEquals(null,lastPendingTxn.getValue());
		assertEquals("6",status.getValue());
		assertEquals("08",in_response_code.getValue());
		assertTrue(isTransaction.getValue());

	}


	@Test
	public void testOMSPendingCase() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyPending();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectPostpaid();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());

		JSONObject lastFailuretxn = new JSONObject();
		lastFailuretxn.put("txn_id", 12334L);
		lastFailuretxn.put("txn_status", "08");
		lastFailuretxn.put("txn_time", "2023-05-30T13:00:00.000Z");
		lastFailuretxn.put("txn_amount", 834.0);
		lastFailuretxn.put("channel_id","WEB 2");

		List<Recents> recents=new ArrayList<>();
		Recents recents1=new Recents();
		RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
		recentsPrimaryKey.setService("mobile");
		recentsPrimaryKey.setRechargeNumber("9945359495");
		recentsPrimaryKey.setCustomerId(100119L);
		recentsPrimaryKey.setPlanBucket("");
		recentsPrimaryKey.setOperator("airtel");
		recents1.setKey(recentsPrimaryKey);
		recents1.setDueDate(new Date());
		recents1.setDueAmount(100.0);
		recents1.setUpdatedAt(new Date());
		recents1.setLastFailureTxn(lastFailuretxn.toString());
		recents.add(recents1);
		when(recentService.fetchRecentsFromDb(any(),any(),any(),any(),any())).thenReturn(recents);

		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());
		assertEquals("{\"txn_id\":12334,\"txn_time\":\"2023-05-30T13:00:00.000Z\",\"txn_amount\":834,\"txn_status\":\"08\",\"channel_id\":\"WEB 2\"}", lastFailureTxn.getValue());
		assertEquals("{\"txn_id\":123,\"txn_time\":\"2023-04-06T13:00:00.000Z\",\"txn_amount\":401,\"txn_status\":\"07\",\"channel_id\":\"WEB 2\"}",lastPendingTxn.getValue());
		assertEquals("15",status.getValue());
		assertEquals("07",in_response_code.getValue());
		assertTrue(isTransaction.getValue());

	}

	@Test
	public void testOMSPendingCase2() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummyPending();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectPostpaid();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());

		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());
		ArgumentCaptor<DropOff> argument2 = ArgumentCaptor.forClass(DropOff.class);
		verify(dropOffDBHelper).insertDropOff(argument2.capture());
		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());
		assertEquals(null, lastFailureTxn.getValue());
		assertEquals("{\"txn_id\":123,\"txn_time\":\"2023-04-06T13:00:00.000Z\",\"txn_amount\":401,\"txn_status\":\"07\",\"channel_id\":\"WEB 2\"}",lastPendingTxn.getValue());
		assertEquals("15",status.getValue());
		assertEquals("07",in_response_code.getValue());
		assertTrue(isTransaction.getValue());

	}

	@Test
	public void testOMSSuccessCase() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummySuccess();
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = mockReminderHistoryObjectPostpaid();
		reminderHistoryList.add(reminderHistory);
		reminderHistoryList.add(mockReminderHistoryObjectWithRefId());

		JSONObject lastFailuretxn = new JSONObject();
		lastFailuretxn.put("txn_id", 12334L);
		lastFailuretxn.put("txn_status", "6");
		lastFailuretxn.put("txn_time", "Tue May 30 14:46:28 IST 2023");
		lastFailuretxn.put("txn_amount", 834.0);

		JSONObject lastPenidngTxn = new JSONObject();
		lastPenidngTxn.put("txn_id", 123434L);
		lastPenidngTxn.put("txn_status", "15");
		lastPenidngTxn.put("txn_time", "Tue May 30 14:48:28 IST 2023");
		lastPenidngTxn.put("txn_amount", 847.0);

		List<Recents> recents=new ArrayList<>();
		Recents recents1=new Recents();
		RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
		recentsPrimaryKey.setService("mobile");
		recentsPrimaryKey.setRechargeNumber("9945359495");
		recentsPrimaryKey.setCustomerId(100119L);
		recentsPrimaryKey.setPlanBucket("");
		recentsPrimaryKey.setOperator("airtel");
		recents1.setKey(recentsPrimaryKey);
		recents1.setDueDate(new Date());
		recents1.setDueAmount(100.0);
		recents1.setUpdatedAt(new Date());
		recents1.setLastFailureTxn(lastFailuretxn.toString());
		recents1.setLastPendingTxn(lastPenidngTxn.toString());
		recents.add(recents1);
		when(recentService.fetchRecentsFromDb(any(),any(),any(),any(),any())).thenReturn(recents);

		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());

		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());

		assertEquals("{\"txn_id\":12334,\"txn_time\":\"Tue May 30 14:46:28 IST 2023\",\"txn_amount\":834,\"txn_status\":\"6\"}", lastFailureTxn.getValue());
		assertEquals("{\"txn_id\":123434,\"txn_time\":\"Tue May 30 14:48:28 IST 2023\",\"txn_amount\":847,\"txn_status\":\"15\"}",lastPendingTxn.getValue());
		assertEquals("00",in_response_code.getValue());
		assertTrue(isTransaction.getValue());

	}



	@Test
	public void testRentPaymetAccountIdBillerId() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("Rent Payment");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("Postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(215903L);
		omsResponseModel.getItems().get(0).setMeta_data("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"***********\",\"recharge_number_3\":\"***********\",\"recharge_number_4\":\"***********\",\"recharge_number_5\":\"***********\",\"recharge_number_6\":\"***********\",\"recharge_number_7\":\"***********\",\"recharge_number_8\":\"***********\"}");
		omsResponseModel.getItems().get(0).setFulfillmentReq("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"Special Recharge\",\"recharge_number\":\"***********\",\"recharge_number_2\":\"69768978\",\"recharge_number_3\":\"60002978897154676\",\"recharge_number_4\":\"09878786\",\"recharge_number_5\":\"546556\",\"recharge_number_7\":\"********\",\"recharge_number_8\":\"79879236\"}");
		omsResponseModel.getFulfillments().get(0).setFulfillment_response("{\"in_code\":\"00\"}");
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());



		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());

		assertEquals("********", recharge_number_1.getValue());
		assertEquals("***********", recharge_number_7.getValue());

	}

	@Test
	public void testBusinessPaymetAccountIdBillerId() throws OMSListenerException {
		OMSResponseModel omsResponseModel = createOMSResponseModelDummy();
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("business payment");
		omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("Postpaid");
		omsResponseModel.getItems().get(0).getProduct().setCategoryId(300048L);
		omsResponseModel.getItems().get(0).setMeta_data("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"\",\"recharge_number\":\"**********\",\"recharge_number_2\":\"PYTM0123456\",\"recharge_number_3\":\"BENE CUSTOMER NAME\",\"recharge_number_4\":\"**********\",\"recharge_number_5\":null,\"recharge_number_6\":null,\"recharge_number_7\":\"********\",\"recharge_number_8\":\"XX 6001\"}");
		omsResponseModel.getItems().get(0).setFulfillmentReq("{\"cylinder_agency_name\":\"Agency Name\",\"price\":10,\"plan_bucket\":\"\",\"recharge_number\":\"**********\",\"recharge_number_2\":\"PYTM0123456\",\"recharge_number_3\":\"BENE CUSTOMER NAME\",\"recharge_number_4\":\"**********\",\"recharge_number_5\":null,\"recharge_number_7\":\"********\",\"recharge_number_8\":\"XX 6001\"}");
		omsResponseModel.getFulfillments().get(0).setFulfillment_response("{\"in_code\":\"00\"}");
		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(reminderHistoryList);
		omsListeners.insertDataIntoCassandra(omsResponseModel,"");
		ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
		ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
		verify(channelHistoryService).save(argument.capture(),argument1.capture());



		ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> orderId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Double> price = ArgumentCaptor.forClass(Double.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> nickName = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> in_response_code = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_1 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_2 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_3 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_4 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_5 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_6 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_7 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number_8 = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> channel = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> consumer_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> cylinder_agency_name = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Boolean> isTransaction = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Date> transactionTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> transactionUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Boolean> isNickNameUpdated = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<String> kafkaPackt = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastFailureTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> lastPendingTxn = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> status = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
		verify(recentService).updateRecentTxnDetails(mcn.capture(),customerId.capture(),
				orderId.capture(), price.capture(), service.capture(),
				operator.capture(),nickName.capture(), transactionTime.capture(),
				planBucket.capture(), cin.capture(), par.capture(),
				in_response_code.capture(), tin.capture(),
				is_tokenized_transaction.capture(), productId.capture(),
				transactionUpdateTime.capture(), recharge_number_1.capture(),
				recharge_number_2.capture(), recharge_number_3.capture(),
				recharge_number_4.capture(), recharge_number_5.capture(),
				recharge_number_6.capture(), recharge_number_7.capture(),
				recharge_number_8.capture(), circle.capture(),
				paytype.capture(),consumer_name.capture(),cylinder_agency_name.capture(),channel.capture(),isNickNameUpdated.capture(),notPaidOnPaytm.capture(),kafkaPackt.capture(),lastFailureTxn.capture(),lastPendingTxn.capture(),isTransaction.capture(),status.capture(),is_new_biller.capture());

		assertEquals("********", recharge_number_1.getValue());
		assertEquals("**********", recharge_number_7.getValue());

	}


}
