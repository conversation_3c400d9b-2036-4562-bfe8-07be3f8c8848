package com.paytm.saga.listeners;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.refEq;
import static org.mockito.Mockito.*;

import com.paytm.saga.common.exception.PlanValidityListenerException;
import com.paytm.saga.common.metrics.MetricsHelper;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.paytm.saga.dto.*;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.*;
import com.paytm.saga.util.DateUtil;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;


import junit.framework.TestCase;


@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class PlanValidityListernsTest extends TestCase {

    @Mock
    private DCATService dcatService;

    @Mock
    private RPSService rpsService;
    @Mock
    private PlanExpiryHistoryRepository planExpiryHistoryRepository;
    @Mock
	RecentsRepositoryWrapperService recentsRepository;

    @Mock
    MetricsHelper metricsHelper;

    @Mock
    CommonService commonService;

    @Mock
    CustomerBillRepository reminderClusterKeyRepository;
    @Mock
    private Acknowledgment acknowledgment;
    @Mock
    KafkaProducerService kafkaProducerService;
    @Mock
    RecentService recentService;

    @Mock
    private StatsDClient monitoringClient;
    @Mock
    DropOffDBHelper dropOffDBHelper;

    private PlanValidityListeners planValidityListeners;

    DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    DateFormat initDateformat = new SimpleDateFormat(Constants.CommonConstants.COMMON_DATE_FORMAT);

    Date date1 = new Date(), date2 = new Date(), date3 =new Date(), date4 = new Date(), date5 = new Date(), date_now_minus_10_days = new Date(), date_now_minus_40_days = new Date();


    private DCATGetPlanResponse getDCATPLanResponseMock() {
        DCATGetPlanResponse d = new DCATGetPlanResponse();
        d.setPlan_bucket("Top Up");
        d.setAddon_benefit1("A");
        d.setAddon_benefit2("B");
        d.setAddon_benefit3("C");
        d.setAddon_benefit4("D");
        d.setSms("1");
        d.setDescription("Data");
        d.setValidity("R");
        d.setTalktime("10");

        return d;
    }
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        planValidityListeners = new PlanValidityListeners(dcatService, rpsService, planExpiryHistoryRepository, recentsRepository,metricsHelper, reminderClusterKeyRepository,dropOffDBHelper,monitoringClient,recentService, commonService);

        date1 = DateUtils.addHours(new Date(),-12);
        date2 = DateUtils.addHours(new Date(),-24);
        date3 = DateUtils.addHours(new Date(),-36);
        date4 = DateUtils.addHours(new Date(),-48);
        date5 = DateUtils.addHours(new Date(),-60);

        date_now_minus_10_days = DateUtils.addDays(new Date(),10);
        date_now_minus_40_days = DateUtils.addDays(new Date(),-40);

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }

    }
    private PlanValidityResponseModel getPlanValidityKafkaResponseMock(){

        PlanValidityResponseModel planValidityResponseModel = new PlanValidityResponseModel();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e){};

        planValidityResponseModel.setTable("bill_airtel");
        planValidityResponseModel.setOperationType("upsert");
        PlanValidityDataResponseModel planValidityData = new PlanValidityDataResponseModel();
        planValidityData.setAmount(100.0);
        planValidityData.setRecharge_number("1231111");
        planValidityData.setValidity_expiry_date(dateString);
        planValidityData.setCircle("A");
        planValidityData.setCustomer_id(1L);
        planValidityData.setOperator("assampowerdistributioncompanyltd.(apdcl)");
        planValidityData.setUpdated_at("2021-03-05 05:44:11");
        planValidityData.setCreated_at("2021-03-05 05:44:11");
        planValidityData.setRecharge_number("1231111");
        planValidityData.setCustomer_id(1L);
        planValidityData.setService("mobile");
        planValidityData.setPlan_bucket("asksgf");
        PlanValidityDataResponseModel planValidityOld = new PlanValidityDataResponseModel();
        planValidityOld.setValidity_expiry_date("2021-03-05 05:44:11");
        planValidityResponseModel.setOld(planValidityOld);
        planValidityData.setNotification_status(0);
        planValidityResponseModel.setData(planValidityData);
        return planValidityResponseModel;
    }

    @Test
    public void listen_ValidMessages_SuccessfullyProcessed() {
        List<String> validMessages = Collections.singletonList("{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"update\",\"ts\":1724784068,\"xid\":31979756036,\"commit\":true,\"data\":{\"id\":2328640900,\"recharge_number\":\"7755055010\",\"customer_id\":92253100,\"service\":\"mobile\",\"operator\":\"jio\",\"circle\":\"all circles\",\"amount\":299,\"validity_expiry_date\":\"2024-09-05 23:59:59\",\"order_date\":\"2024-03-21 09:17:58\",\"latest_recharge_date\":\"2024-08-08 21:30:54\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan\",\"created_at\":\"2024-03-21 04:06:31\",\"updated_at\":\"2024-08-27 18:41:08\",\"cust_mobile\":\"7755055010\",\"cust_email\":\"\",\"rn_customer_id\":null,\"order_ids\":\"23339870686,23536762262,23658880262,23659908609,23899674412\",\"status\":11,\"extra\":\"{\\\"customer_type\\\":1,\\\"updated_data_source\\\":\\\"transaction\\\",\\\"recon_id\\\":\\\"9lWfcxJtBxem6OrAYgC0umBRO3Q=\\\",\\\"user_type\\\":\\\"RU\\\",\\\"source_subtype_2\\\":\\\"FULL_BILL\\\",\\\"paytype\\\":\\\"prepaid\\\",\\\"bill_date\\\":null,\\\"bill_fetch_date\\\":null,\\\"is_automatic\\\":null,\\\"is_data_exhaust\\\":\\\"true\\\",\\\"data_exhaust_value\\\":\\\"100%\\\",\\\"data_exhaust_date\\\":\\\"2024-08-28 00:11:08\\\",\\\"sms_id\\\":\\\"63688d33-4221-4718-b7db-c33033394148\\\",\\\"sms_date_time\\\":\\\"1724784067143\\\"}\",\"notification_status\":1,\"cust_rech_meta\":\"\",\"product_id\":465268141},\"old\":{\"updated_at\":\"2024-08-08 16:01:44\",\"extra\":\"{\\\"customer_type\\\":1,\\\"updated_data_source\\\":\\\"transaction\\\",\\\"recon_id\\\":\\\"9lWfcxJtBxem6OrAYgC0umBRO3Q=\\\",\\\"user_type\\\":\\\"RU\\\",\\\"source_subtype_2\\\":\\\"FULL_BILL\\\",\\\"paytype\\\":\\\"prepaid\\\",\\\"bill_date\\\":null,\\\"bill_fetch_date\\\":null,\\\"is_automatic\\\":null}\"}}");

        planValidityListeners.listen(validMessages, acknowledgment);
        verify(metricsHelper, times(1)).recordSuccessRate(eq("PLAN_VALIDITY_CONSUMER"), eq("NEW_KAFKA_EVENT"));
    }

    @Test
    public void listen_EmptyMessageList_NoExceptionThrown() {
        List<String> emptyMessages = Collections.emptyList();

        planValidityListeners.listen(emptyMessages, acknowledgment);
        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    public void listen_NullMessage_HandleGracefully() {
        List<String> messagesWithNull = Collections.singletonList(null);

        planValidityListeners.listen(messagesWithNull, acknowledgment);
        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    public void whenDateGivenNullToGetTTL() {
        Integer actualDefaultTTL = planValidityListeners.getTTL(null);
        Integer expectedDefaultTTL = 90 * 24 * 60 * 60;
        assertEquals(actualDefaultTTL, expectedDefaultTTL);
    }

    @Test
    public void whenDateGivenToGetTTL() {
        Date date = new Date();
        Integer actualDefaultTTL = planValidityListeners.getTTL(date);
        assertEquals(actualDefaultTTL, new Integer(604800));
    }

    //@Test
    public void whenPayloadWithMessagesGivenToListen() {
        List<String> list = new ArrayList<>();
        PlanValidityListeners planValidityListeners1 = Mockito.spy(planValidityListeners);
        list.add("{\"operationType\":\"test_opr\",\"table\":\"test_tab\",\"PlanValidityDataResponseModel\":{\"recharge_number\":\"22313232\",\"customer_id\":\"21\",\"service\":\"Recharge\",\"operator\":\"Airtel\",\"circle\":\"Delhi\",\"amount\":400}}");
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(list.get(0), PlanValidityResponseModel.class);

        Acknowledgment ack = mock(Acknowledgment.class);
        planValidityListeners1.listen(list, ack);
        verify(ack).acknowledge();
        verify(planValidityListeners1, Mockito.times(1)).insertDataIntoCassandra(refEq(planValidityResponseModel));

    }

    @Test
    public void whenPayloadWithoutMessagesGivenToListen() {
        PlanValidityListeners planValidityListeners1 = Mockito.spy(planValidityListeners);

        List<String> list = new ArrayList<>();
        Acknowledgment ack = mock(Acknowledgment.class);
        planValidityListeners.listen(list, ack);
        verify(ack).acknowledge();
        verify(planValidityListeners1, Mockito.times(0)).insertDataIntoCassandra(Mockito.any(PlanValidityResponseModel.class));

    }

    @Test
    public void insertDataIntoCassandraTestWhenGetOperationTypeDelete() {
        String message = "{\"type\":\"delete\",\"table\":\"test_tab\",\"data\":{\"recharge_number\":\"22313232\",\"customer_id\":\"21\",\"service\":\"Recharge\",\"operator\":\"Airtel\",\"circle\":\"Delhi\",\"amount\":400}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(message, PlanValidityResponseModel.class);

        ArgumentCaptor<PlanExpiryHistory> argument = ArgumentCaptor.forClass(PlanExpiryHistory.class);

        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        verify(planExpiryHistoryRepository, Mockito.times(0)).save(argument.capture(), Mockito.any(Integer.class));

    }

    @Test
    public void updateOperationWithEmptyOldData() {
        String message = "{\"type\":\"update\",\"table\":\"test_tab\",\"old\":{},\"data\":{\"recharge_number\":\"22313232\",\"customer_id\":\"21\",\"service\":\"Recharge\",\"operator\":\"Airtel\",\"circle\":\"Delhi\",\"amount\":400,\"category_name\":\"Recharge\"}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(message, PlanValidityResponseModel.class);

        ArgumentCaptor<PlanExpiryHistory> argument = ArgumentCaptor.forClass(PlanExpiryHistory.class);

        boolean flag=planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        assertEquals(false,flag);

    }

    @Test
    public void insertDataIntoCassandraTestWhenAmountGreaterThanZero() {
        String message = "{\"type\":\"not_delete\",\"table\":\"test_tab\",\"data\":{\"recharge_number\":\"22313232\",\"plan_bucket\":\"new plan bucket\",\"customer_id\":\"21\",\"service\":\"mobile\",\"operator\":\"Airtel\",\"circle\":\"Delhi\",\"amount\":400,\"updated_at\":\"2021-02-04 12:23:42\",\"expiry_date\":\"2021-02-04 12:23:42\",\"created_at\":\"2021-02-04 12:23:42\",\"validity_expiry_date\":\"2021-02-04 12:23:42\"}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(message, PlanValidityResponseModel.class);

        DCATGetPlanResponse d = getDCATPLanResponseMock();

        String rechargeNumber = planValidityResponseModel.getData().getRecharge_number();
        String service = planValidityResponseModel.getData().getService();
        String categoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service);
        String operator = planValidityResponseModel.getData().getOperator();
        Double amount = planValidityResponseModel.getData().getAmount();
        String amountString = Double.valueOf(amount).intValue() + "";
        String circle = planValidityResponseModel.getData().getCircle();


        when(rpsService.getPlanDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(d);

        ArgumentCaptor<PlanExpiryHistory> argument = ArgumentCaptor.forClass(PlanExpiryHistory.class);

        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);
        verify(planExpiryHistoryRepository).save(argument.capture(), Mockito.any(Integer.class));
        PlanExpiryHistory planExpiryHistories = argument.getValue();
        assertEquals(rechargeNumber, planExpiryHistories.getRechargeNumber());
        assertEquals(service, planExpiryHistories.getService());
        assertEquals(circle, planExpiryHistories.getCircle());
        assertEquals(operator, planExpiryHistories.getOperator());


    }

    @Test
    public void insertDataIntoCassandraTestWhenAmountLessThanZero() {
        String message = "{\"type\":\"not_delete\",\"table\":\"test_tab\",\"data\":{\"recharge_number\":\"22313232\",\"plan_bucket\":\"new plan bucket\",\"customer_id\":\"21\",\"service\":\"mobile\",\"operator\":\"Airtel\",\"circle\":\"Delhi\",\"amount\":-400,\"updated_at\":\"2021-02-04 12:23:42\",\"expiry_date\":\"2021-02-04 12:23:42\",\"created_at\":\"2021-02-04 12:23:42\",\"validity_expiry_date\":\"2021-02-04 12:23:42\"}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(message, PlanValidityResponseModel.class);

        ArgumentCaptor<PlanExpiryHistory> argument = ArgumentCaptor.forClass(PlanExpiryHistory.class);

        String rechargeNumber = planValidityResponseModel.getData().getRecharge_number();
        String service = planValidityResponseModel.getData().getService();
        String categoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service);
        String operator = planValidityResponseModel.getData().getOperator();
        Double amount = planValidityResponseModel.getData().getAmount();
        String amountString = Double.valueOf(amount).intValue() + "";
        String circle = planValidityResponseModel.getData().getCircle();


        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);
        verify(planExpiryHistoryRepository).save(argument.capture(), Mockito.any(Integer.class));
        PlanExpiryHistory planExpiryHistories = argument.getValue();
        assertEquals(rechargeNumber, planExpiryHistories.getRechargeNumber());
        assertEquals(service, planExpiryHistories.getService());
        assertEquals(circle, planExpiryHistories.getCircle());
        assertEquals(operator, planExpiryHistories.getOperator());


    }


    @Test
    public void testUpsertIntoRecents() throws PlanValidityListenerException {
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<PlanExpiryHistory> planExpriyHistory = ArgumentCaptor.forClass(PlanExpiryHistory.class);
        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);
        
        //verify(recentsRepository).findByCustomerIdAndServiceAndRechargenumberAndOperatorAndPlan_bucket(argument1.capture(),argument2.capture(),argument3.capture(),argument4.capture(),argument5.capture());
        verify(recentService).insertIntoRecentAndCustomerBill(recent.capture(), planExpriyHistory.capture());

        Recents recentValue = recent.getValue();
        assertEquals(planValidityResponseModel.getData().getCustomer_id(), recentValue.getKey().getCustomerId());
        assertEquals(planValidityResponseModel.getData().getService(), recentValue.getKey().getService());
        assertEquals(planValidityResponseModel.getData().getRecharge_number(), recentValue.getKey().getRechargeNumber());
        assertEquals(planValidityResponseModel.getData().getOperator(), recentValue.getKey().getOperator());
        assertEquals(planValidityResponseModel.getData().getPlan_bucket(), recentValue.getKey().getPlanBucket());
        assertEquals(planValidityResponseModel.getData().getCategory_name(), recentValue.getPlanName());
    }
    @Test
    public void insertDataIntoCassandraTestUpdatedAt(){
        String message = "{\"type\":\"not_delete\",\"table\":\"test_tab\",\"data\":{\"recharge_number\":\"22313232\",\"plan_bucket\":\"new plan bucket\",\"customer_id\":\"21\",\"service\":\"mobile\",\"operator\":\"Airtel\",\"circle\":\"Delhi\",\"amount\":400,\"updated_at\":\"2023-02-22 12:23:42\",\"expiry_date\":\"2023-02-22 12:23:42\",\"created_at\":\"2023-02-12 12:23:42\",\"validity_expiry_date\":\"2023-02-22 12:23:42\"}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(message, PlanValidityResponseModel.class);

        DCATGetPlanResponse d = getDCATPLanResponseMock();

        String rechargeNumber = planValidityResponseModel.getData().getRecharge_number();
        String service = planValidityResponseModel.getData().getService();
        String categoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(service);
        String operator = planValidityResponseModel.getData().getOperator();
        Double amount = planValidityResponseModel.getData().getAmount();
        String amountString = Double.valueOf(amount).intValue() + "";
        String circle = planValidityResponseModel.getData().getCircle();

        ArgumentCaptor<PlanExpiryHistory> argument = ArgumentCaptor.forClass(PlanExpiryHistory.class);

        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);
        verify(planExpiryHistoryRepository).save(argument.capture(), Mockito.any(Integer.class));
        PlanExpiryHistory planExpiryHistories = argument.getValue();
        assertEquals(DateUtil.stringToDate("2023-02-22 17:53:42","yyyy-MM-dd HH:mm:ss"),planExpiryHistories.getUpdated_at());
        assertEquals(DateUtil.stringToDate("2023-02-12 17:53:42","yyyy-MM-dd HH:mm:ss"),planExpiryHistories.getCreated_at());
    }
    public Recents fn(){
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e){};
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();
        Recents recents = new Recents();
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setRechargeNumber(planValidityResponseModel.getData().getRecharge_number());
        recents.getKey().setOperator(planValidityResponseModel.getData().getOperator());
        recents.getKey().setService(planValidityResponseModel.getData().getService());
        recents.getKey().setCustomerId(planValidityResponseModel.getData().getCustomer_id());
        recents.getKey().setPlanBucket("");
        try {
			recents.setDueDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(planValidityResponseModel.getData().getValidity_expiry_date()));
		} catch (ParseException e) {
			System.out.print(e.getMessage());
		}
        recents.setDueAmount(planValidityResponseModel.getData().getAmount());
        recents.getKey().setPlanBucket(planValidityResponseModel.getData().getPlan_bucket());
        recents.setBillUpdateTime(new Date());

        return recents;
    }
    @Test
    public void testInsertIntoCassandraRpsProductIdNull(){
        //String message = "{\"data\":{},\"id\":************,\"payment_status\":1,\"channel_id\":\"\",\"customer_id\":23,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-10-28T10:52:20.000Z\\\",\\\"in_create_time\\\":\\\"2021-06-13T10:52:20.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https: //paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"CustomerName\\\":\\\"JAIBHARATFURNACESP.LTD.\\\",\\\"Officecode\\\":\\\"2101310\\\",\\\"bindernumber\\\":\\\"9652\\\",\\\"invoiceid\\\":\\\"*********\\\",\\\"Billdate\\\":\\\"10-06-2019\\\",\\\"Duedate\\\":\\\"2019-09-27\\\",\\\"amountpayable\\\":\\\"310\\\",\\\"billmonth\\\":\\\"10-06-2019\\\",\\\"BCITSid\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-06-13T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"shipped_at\":\"2021-10-28T10:52:20.000Z\",\"delivered_at\":\"2021-10-28T10:52:20.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":null,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":399,\"recharge_number\":\"4054 50XX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-10-28T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":399,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":null,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":399,\\\"recharge_number\\\":\\\"8755388736\\\"}\",\"product\":{\"id\":null,\"category_id\":156705,\"attributes\":{\"service\":\"mobile\",\"paytype\":\"prepaid\",\"operator\":\"Airtel\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";
        String message = "{\"database\":\"recharge_analytics\",\"table\":\"plan_validity\",\"type\":\"insert\",\"ts\":1610971092,\"xid\":987955,\"commit\":true,\"data\":{\"id\":1436879184,\"recharge_number\":\"9999999999\",\"customer_id\":100,\"service\":\"mobile\",\"operator\":\"jio\",\"circle\":\"delhi ncr\",\"amount\":10,\"validity_expiry_date\":\"2022-11-28 10:52:20.000Z\",\"order_date\":\"2022-10-28 10:52:20.000Z\",\"latest_recharge_date\":\"2021-10-28 10:52:20.000Z\",\"plan_bucket\":\"Special Recharge\",\"category_name\":\"Recharge Plan Test\",\"created_at\":\"2022-10-28 10:52:20.000Z\",\"updated_at\":\"2022-10-28 10:52:20.000Z\",\"cust_mobile\":null,\"cust_email\":null,\"rn_customer_id\":null,\"order_ids\":null,\"status\":1,\"extra\":null,\"notification_status\":1}}";
        PlanValidityResponseModel planValidityResponseModel = JsonUtils.parseJson(message,PlanValidityResponseModel.class);

        String response = "{\"plan_bucket\":\"Special Recharge\",\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":45245}";
        DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);
        when(rpsService.getPlanDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);

        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        ArgumentCaptor<PlanExpiryHistory> argument = ArgumentCaptor.forClass(PlanExpiryHistory.class);

        verify(planExpiryHistoryRepository).save(argument.capture(),Mockito.any(Integer.class));

        assertEquals("28 day", argument.getValue().getDisplay_values().get("validity"));
        assertEquals("2.5GB/day",argument.getValue().getDisplay_values().get("data"));
        assertEquals("100/Day",argument.getValue().getDisplay_values().get("sms"));
        assertEquals(null,argument.getValue().getDisplay_values().get("talktime"));
//        assertEquals("45245",argument.getValue().getBillsObj().get("plan_id"));
//        assertEquals("Special Recharge",argument.getValue().getBillsObj().get("plan_bucket"));
    }
    @Test
    public void testInsertIntoDropOffSuccess(){
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();
        planValidityResponseModel.getData().setService("mobile");
        planValidityResponseModel.getData().setOperator("jio");
        planValidityResponseModel.setOperationType("update");
//        planValidityResponseModel.getOld().setValidity_expiry_date("2022-12-15 05:44:11");
        ArgumentCaptor<DropOff> dropOff = ArgumentCaptor.forClass(DropOff.class);

        List<Recents> recents = new ArrayList<>();
        Recents recent = new Recents();
        recent.setOrderId(2l);
        recents.add(recent);

        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(recents);

        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        verify(dropOffDBHelper).insertDropOff(dropOff.capture());

        assertEquals("RECHARGE",dropOff.getValue().getEventType());
        assertEquals("7",dropOff.getValue().getStatus());
    }
    @Test
    public void testInsertIntoDropOffFailureForNewUser(){
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();
        planValidityResponseModel.getData().setService("mobile");
        planValidityResponseModel.getData().setOperator("jio");
       // planValidityResponseModel.getOld().setValidity_expiry_date("2022-12-15 05:44:11");
        ArgumentCaptor<DropOff> dropOff = ArgumentCaptor.forClass(DropOff.class);

        List<Recents> recents = new ArrayList<>();
        Recents recent = new Recents();
        recent.setOrderId(null);
        recents.add(recent);

        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        verify(dropOffDBHelper,times(0)).insertDropOff(dropOff.capture());
    }
    @Test
    public void testInsertIntoDropOffWhenMultipleRecent(){
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();
        planValidityResponseModel.getData().setService("mobile");
        planValidityResponseModel.getData().setOperator("jio");
        planValidityResponseModel.setOperationType("insert");
      //  planValidityResponseModel.getOld().setValidity_expiry_date("2022-12-15 05:44:11");
        ArgumentCaptor<DropOff> dropOff = ArgumentCaptor.forClass(DropOff.class);

        List<Recents> recents = new ArrayList<>();
        Recents recent = new Recents();
        recent.setOrderId(null);
        recents.add(0,recent);

        Recents recent1 = new Recents();
        recent1.setOrderId(1l);
        recents.add(1,recent1);

        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(recents);

        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        verify(dropOffDBHelper,times(1)).insertDropOff(dropOff.capture());
    }
    @Test
    public void testUpdateIntoRecents() throws PlanValidityListenerException {
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();
        planValidityResponseModel.setOperationType("update");

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<PlanExpiryHistory> planExpriyHistory = ArgumentCaptor.forClass(PlanExpiryHistory.class);
        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        //verify(recentsRepository).findByCustomerIdAndServiceAndRechargenumberAndOperatorAndPlan_bucket(argument1.capture(),argument2.capture(),argument3.capture(),argument4.capture(),argument5.capture());
        verify(recentService).insertIntoRecentAndCustomerBill(recent.capture(), planExpriyHistory.capture());

        Recents recentValue = recent.getValue();
        assertEquals(planValidityResponseModel.getData().getCustomer_id(), recentValue.getKey().getCustomerId());
        assertEquals(planValidityResponseModel.getData().getService(), recentValue.getKey().getService());
        assertEquals(planValidityResponseModel.getData().getRecharge_number(), recentValue.getKey().getRechargeNumber());
        assertEquals(planValidityResponseModel.getData().getOperator(), recentValue.getKey().getOperator());
        assertEquals(planValidityResponseModel.getData().getPlan_bucket(), recentValue.getKey().getPlanBucket());
        assertEquals(planValidityResponseModel.getData().getCategory_name(), recentValue.getPlanName());
        assertNotNull(recentValue.getNewBillUpdatedAt());
    }
    @Test
    public void testUpdateIntoRecentsWhenNoChangeDueDate() throws PlanValidityListenerException {
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();
        planValidityResponseModel.setOperationType("update");
        planValidityResponseModel.getOld().setValidity_expiry_date(null);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<PlanExpiryHistory> planExpriyHistory = ArgumentCaptor.forClass(PlanExpiryHistory.class);
        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        //verify(recentsRepository).findByCustomerIdAndServiceAndRechargenumberAndOperatorAndPlan_bucket(argument1.capture(),argument2.capture(),argument3.capture(),argument4.capture(),argument5.capture());
        verify(recentService).insertIntoRecentAndCustomerBill(recent.capture(), planExpriyHistory.capture());

        Recents recentValue = recent.getValue();
        assertEquals(planValidityResponseModel.getData().getCustomer_id(), recentValue.getKey().getCustomerId());
        assertEquals(planValidityResponseModel.getData().getService(), recentValue.getKey().getService());
        assertEquals(planValidityResponseModel.getData().getRecharge_number(), recentValue.getKey().getRechargeNumber());
        assertEquals(planValidityResponseModel.getData().getOperator(), recentValue.getKey().getOperator());
        assertEquals(planValidityResponseModel.getData().getPlan_bucket(), recentValue.getKey().getPlanBucket());
        assertEquals(planValidityResponseModel.getData().getCategory_name(), recentValue.getPlanName());
        assertNull(recentValue.getNewBillUpdatedAt());
    }
    @Test
    public void testUpdateIntoRecentsWhenNewDueDateNull() throws PlanValidityListenerException {
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();
        planValidityResponseModel.setOperationType("update");
        planValidityResponseModel.getData().setValidity_expiry_date(null);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<PlanExpiryHistory> planExpriyHistory = ArgumentCaptor.forClass(PlanExpiryHistory.class);
        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        //verify(recentsRepository).findByCustomerIdAndServiceAndRechargenumberAndOperatorAndPlan_bucket(argument1.capture(),argument2.capture(),argument3.capture(),argument4.capture(),argument5.capture());
        verify(recentService).insertIntoRecentAndCustomerBill(recent.capture(), planExpriyHistory.capture());

        Recents recentValue = recent.getValue();
        assertEquals(planValidityResponseModel.getData().getCustomer_id(), recentValue.getKey().getCustomerId());
        assertEquals(planValidityResponseModel.getData().getService(), recentValue.getKey().getService());
        assertEquals(planValidityResponseModel.getData().getRecharge_number(), recentValue.getKey().getRechargeNumber());
        assertEquals(planValidityResponseModel.getData().getOperator(), recentValue.getKey().getOperator());
        assertEquals(planValidityResponseModel.getData().getPlan_bucket(), recentValue.getKey().getPlanBucket());
        assertEquals(planValidityResponseModel.getData().getCategory_name(), recentValue.getPlanName());
        assertNull(recentValue.getNewBillUpdatedAt());
    }
    @Test
    public void testInsertIntoRecents() throws PlanValidityListenerException {
        PlanValidityResponseModel planValidityResponseModel = getPlanValidityKafkaResponseMock();
        planValidityResponseModel.setOperationType("insert");
        planValidityResponseModel.getOld().setValidity_expiry_date(null);

        ArgumentCaptor<Recents> recent = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<PlanExpiryHistory> planExpriyHistory = ArgumentCaptor.forClass(PlanExpiryHistory.class);
        planValidityListeners.insertDataIntoCassandra(planValidityResponseModel);

        //verify(recentsRepository).findByCustomerIdAndServiceAndRechargenumberAndOperatorAndPlan_bucket(argument1.capture(),argument2.capture(),argument3.capture(),argument4.capture(),argument5.capture());
        verify(recentService).insertIntoRecentAndCustomerBill(recent.capture(), planExpriyHistory.capture());

        Recents recentValue = recent.getValue();
        assertEquals(planValidityResponseModel.getData().getCustomer_id(), recentValue.getKey().getCustomerId());
        assertEquals(planValidityResponseModel.getData().getService(), recentValue.getKey().getService());
        assertEquals(planValidityResponseModel.getData().getRecharge_number(), recentValue.getKey().getRechargeNumber());
        assertEquals(planValidityResponseModel.getData().getOperator(), recentValue.getKey().getOperator());
        assertEquals(planValidityResponseModel.getData().getPlan_bucket(), recentValue.getKey().getPlanBucket());
        assertEquals(planValidityResponseModel.getData().getCategory_name(), recentValue.getPlanName());
        assertNotNull(recentValue.getNewBillUpdatedAt());
    }


}
