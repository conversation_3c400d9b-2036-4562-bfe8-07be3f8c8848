package com.paytm.saga.listeners;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.CDCReminderListenerException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.SMSRecoveryPacket;
import com.paytm.saga.dto.cdc.NonPaytmCDC;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.service.CommonService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.RecentUtils;
import junit.framework.TestCase;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.paytm.saga.common.constant.Constants.FsRechargeConsumerConstants.IGNORE_EVENT;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class CDCSMSRecoveryKafkaConsumerTest extends TestCase {
    @Mock
    CDCReminderListener cdcReminderListener;
    @Mock
    Acknowledgment acknowledgment;
    @Mock
    MetricsHelper metricsHelper;

    @Mock
    ServiceConfig serviceConfig;
//    @Mock
//    CDCReminderKafkaConsumer cdcReminderKafkaConsumer;
//
//    @Mock
//    CDCSMSRecoveryKafkaConsumer cdcSMSRecoveryKafkaConsumer;
    @InjectMocks
    CDCSMSRecoveryKafkaConsumer cdcSMSRecoveryKafkaConsumer;
    @After
    public void resetMocks(){
        Mockito.reset();
    }


    @Test
    public void testCDCRecoveryInRecents() throws CDCReminderListenerException {
        String msg = "{\"customerId\":*********,\"rechargeNumber\":\"XXXX XXXX XXXX 8645\",\"productId\":**********,\"operator\":\"hdfc\",\"amount\":23,\"bill_fetch_date\":\"2022-02-20T12:31:12.000Z\",\"paytype\":\"credit card\",\"service\":\"financial services\",\"circle\":\"\",\"customer_mobile\":\"**********\",\"customer_email\":\"<EMAIL>\",\"status\":7,\"userData\":\"\",\"createdAt\":\"2018-11-20 11:16:28\",\"updateAt\":\"2018-11-28 06:14:02\",\"billDate\":\"2022-02-20T12:31:12.000Z\",\"extra\":null,\"notificationStatus\":1,\"dueDate\":\""+DateUtil.formatDate(DateUtil.dateIncrDecr(new Date(),7),"yyyy-MM-dd'T'HH:mm:ssXXX")+"\",\"customerOtherInfo\":\"{'customerId':*********,'lastCC':'2003','smsSenderID':'VM-KOTAKB','bankName':'KOTAK','debugKey':'smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003'}\",\"cardNetwork\":\"dummyNetwork\",\"dbEvent\":\"upsert\",\"cdcEventType\":\"u\",\"debugKey\":\"rech_num:XXXX XXXX XXXX 8645::operator:neft_hdfc bank::productId:**********::custId:*********\"}";
        SMSRecoveryPacket smsRecoveryPacket=JsonUtils.parseJson(msg,SMSRecoveryPacket.class);
        List<String> msgs = new ArrayList<>();
        msgs.add(msg);
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(new ArrayList<>());
        //ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        //ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        cdcSMSRecoveryKafkaConsumer.listenRecoveryTopic(msgs,acknowledgment);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        assertNotNull(smsRecoveryPacket);
        //assertEquals(smsRecoveryPacket.getDueDate(),DateUtil.formatDate(customerBill.getValue().getKey().getDue_date(), "yyyy-MM-dd'T'HH:mm:ssXXX"));
        verify(cdcReminderListener,times(1)).insertDataIntoCassandra(reminderCdc.capture());
    }

    @Test
    public void testCDCRecoveryInvalidPacket() throws CDCReminderListenerException {
        List<String> msgs = new ArrayList<>();
        msgs.add(null);
        cdcSMSRecoveryKafkaConsumer.listenRecoveryTopic(msgs,acknowledgment);
        verify(metricsHelper,times(1)).recordSuccessRate(Constants.CDC_RECOVERY_CONSUMER, IGNORE_EVENT);
    }


    @Test
    public void isWhitelistedCidNullWhitelistConfig(){
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(null);
        assertFalse(RecentUtils.isWhitelistedCustId(1234L,serviceConfig));

    }


    @Test
    public void isWhitelistedCidNullCustId(){
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(new ArrayList<>());
        assertFalse(RecentUtils.isWhitelistedCustId(null,serviceConfig));

    }

    @Test
    public void isPercentLive(){
        List<String> r=new ArrayList<>();
        r.add("5");
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(r);
        when(serviceConfig.getPercentOfLiveTraffic()).thenReturn(6);
        RecentUtils.isWhitelistedCustId(12305L,serviceConfig);

    }

    @Test
    public void isPercentLive1(){
        List<String> r=new ArrayList<>();
        r.add("5");
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(r);
        when(serviceConfig.getPercentOfLiveTraffic()).thenReturn(6);
        RecentUtils.isWhitelistedCustId(12307L,serviceConfig);

    }

    @Test
    public void isPercentLive2(){
        List<String> r=new ArrayList<>();
        r.add("5");
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(r);
        when(serviceConfig.getPercentOfLiveTraffic()).thenReturn(null);
        RecentUtils.isWhitelistedCustId(12307L,serviceConfig);

    }
    @Test
    public void isPercentLive3(){
        List<String> r=new ArrayList<>();
        r.add("5");
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(r);
        when(serviceConfig.getPercentOfLiveTraffic()).thenReturn(101);
        RecentUtils.isWhitelistedCustId(12307L,serviceConfig);

    }

    @Test
    public void isPercentLive4(){
        List<String> r=new ArrayList<>();
        r.add("5");
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(r);
        when(serviceConfig.getPercentOfLiveTraffic()).thenReturn(-1);
        RecentUtils.isWhitelistedCustId(12307L,serviceConfig);

    }

    @Test
    public void isPercentLive5(){
        List<String> r=new ArrayList<>();
        r.add("12307");
        when(serviceConfig.getWhitelistedCustIdsForCDCRecoveryConsumer()).thenReturn(r);
        RecentUtils.isWhitelistedCustId(12307L,serviceConfig);

    }

}
