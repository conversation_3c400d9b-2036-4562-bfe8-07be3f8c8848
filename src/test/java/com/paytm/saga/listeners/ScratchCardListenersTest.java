package com.paytm.saga.listeners;


import com.paytm.saga.dto.ScratchCardResponseModel;
import com.paytm.saga.model.ScratchCardHistory;
import com.paytm.saga.repository.ScratchCardHistoryRepository;
import com.paytm.saga.util.JsonUtils;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.same;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class ScratchCardListenersTest extends TestCase {

    @Mock
    ScratchCardHistoryRepository scratchCardHistoryRepository;

    ScratchCardListeners scratchCardListeners;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        scratchCardListeners = new ScratchCardListeners(scratchCardHistoryRepository);
    }

    @Test
    public void listenCalledWithEmptyPayload() {
        List<String> list = new ArrayList<>();
        ScratchCardListeners scratchCardListeners1 = spy(scratchCardListeners);

        Acknowledgment ack = mock(Acknowledgment.class);
        scratchCardListeners1.listen(list, ack);
        verify(scratchCardListeners1, times(0)).insertDataIntoCassandra(new ScratchCardResponseModel());
        verify(ack).acknowledge();

    }

    @Test
    public void listenCalledWithNonEmptyPayload() {
        List<String> list = new ArrayList<>();
        list.add("{\"scratchCardId\":\"21342\",\"userId\":\"2313142\",\"status\":\"test_status\",\"txnId\":\"32421\",\"createdAt\":\"231\",\"updatedAt\":\"22\",\"deleted\":\"true\",\"txnSource\":\"test_source\",\"txnInfo\":{\"vertical_id\":[]}}");
        ScratchCardListeners scratchCardListeners1 = Mockito.spy(scratchCardListeners);
        Acknowledgment ack = mock(Acknowledgment.class);
        ScratchCardResponseModel scratchCardResponseModel = JsonUtils.parseJson(list.get(0), ScratchCardResponseModel.class);

        scratchCardListeners1.listen(list, ack);
        verify(ack).acknowledge();
        verify(scratchCardListeners1).insertDataIntoCassandra(Mockito.any(ScratchCardResponseModel.class));


    }

    @Test
    public void listenCalledWithIsSkippableTrue() {
        List<String> list = new ArrayList<>();
        list.add("{\"scratchCardId\":\"21342\",\"userId\":\"2313142\",\"status\":\"test_status\",\"txnId\":\"32421\",\"createdAt\":\"231\",\"updatedAt\":\"22\",\"deleted\":\"true\",\"txnSource\":\"test_source\",\"txnInfo\":{\"vertical_id\":[]}}");

        Acknowledgment ack = mock(Acknowledgment.class);
        ArgumentCaptor<ScratchCardHistory> argumentCaptor = ArgumentCaptor.forClass(ScratchCardHistory.class);

        scratchCardListeners.listen(list, ack);
        verify(ack).acknowledge();
        verify(scratchCardHistoryRepository, times(0)).save(argumentCaptor.capture());

    }

    @Test
    public void listenCalledWithIsSkippableFalseAndIsDeletableFalse() {

        String message = "{\"scratchCardId\":\"21342\",\"userId\":\"2313142\",\"status\":\"test_status\",\"txnId\":\"1\",\"createdAt\":\"231\",\"updatedAt\":\"22\",\"deleted\":\"true\",\"txnSource\":\"RECHARGES\",\"txnInfo\":{\"vertical_id\":[\"3\",\"4\"]}}";

        ScratchCardResponseModel scratchCardResponseModel = JsonUtils.parseJson(message, ScratchCardResponseModel.class);


        Long orderId = 1L;
        String status = scratchCardResponseModel.getStatus();
        Long scratchCardId = scratchCardResponseModel.getScratchCardId();
        String userId = scratchCardResponseModel.getUserId();
        boolean deleted = scratchCardResponseModel.isDeleted();

        ArgumentCaptor<ScratchCardHistory> argumentCaptor = ArgumentCaptor.forClass(ScratchCardHistory.class);

        scratchCardListeners.insertDataIntoCassandra(scratchCardResponseModel);

        verify(scratchCardHistoryRepository).save(argumentCaptor.capture());

        ScratchCardHistory scratchCardHistory = argumentCaptor.getValue();

        assertEquals(orderId, scratchCardHistory.getOrderId());
        assertEquals(status, scratchCardHistory.getStatus());
        assertEquals(scratchCardId, scratchCardHistory.getScratchCardId());
        assertEquals(userId, scratchCardHistory.getUserId());
        assertEquals(deleted, scratchCardHistory.isDeleted());

    }


    @Test
    public void listenCalledWithIsDeletableTrue() {

        String message = "{\"scratchCardId\":\"21342\",\"userId\":\"2313142\",\"status\":\"REDEEMED\",\"txnId\":\"1\",\"createdAt\":\"231\",\"updatedAt\":\"22\",\"deleted\":\"true\",\"txnSource\":\"RECHARGES\",\"txnInfo\":{\"vertical_id\":[\"3\",\"4\"]}}";
        ScratchCardResponseModel scratchCardResponseModel = JsonUtils.parseJson(message, ScratchCardResponseModel.class);
        ArgumentCaptor<ScratchCardHistory> argumentCaptor = ArgumentCaptor.forClass(ScratchCardHistory.class);
        scratchCardListeners.insertDataIntoCassandra(scratchCardResponseModel);
        verify(scratchCardHistoryRepository, times(0)).save(argumentCaptor.capture());

        verify(scratchCardHistoryRepository, times(1)).deleteScratchCardsBasedOnOrderIdScratchCardId(Long.parseLong(scratchCardResponseModel.getOrderId()), scratchCardResponseModel.getScratchCardId());

    }
}
