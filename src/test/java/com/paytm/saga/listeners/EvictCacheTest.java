package com.paytm.saga.listeners;


import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.service.GenericRestClient;
import com.paytm.saga.service.RestTemplateService;
import com.timgroup.statsd.StatsDClient;
import org.apache.logging.log4j.LogManager;
import org.hamcrest.CoreMatchers;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.retry.annotation.EnableRetry;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.mock;

@RunWith(MockitoJUnitRunner.class)
@EnableRetry
public class EvictCacheTest {

    @Mock
    private GenericRestClient restTemplateService;

    @InjectMocks
    private EvictCacheListener evictCacheListener;

    @Mock
    private StatsDClient monitoringClient;

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(EvictCacheListener.class));

    @Test
    public void testObjectNull() throws Exception{
        Acknowledgment ack = mock(Acknowledgment.class);
        List<String> list  = new ArrayList<>();
        list.add(null);
        evictCacheListener.listen(list,ack);
        assertThat(appender.getOutput(), CoreMatchers.containsString("NULL OBJECT"));
        assertThat(appender.getOutput(), CoreMatchers.containsString("Acknowledging message"));
    }
    
    @Test
    public void testInvalidMessageBody() throws Exception{
    	Acknowledgment ack = mock(Acknowledgment.class);
        List<String> list  = new ArrayList<>();
        list.add(" ");
        evictCacheListener.listen(list,ack);
        System.out.print(appender.getOutput());
        assertThat(appender.getOutput(), CoreMatchers.containsString("validated must not be null"));
        assertThat(appender.getOutput(), CoreMatchers.containsString("Acknowledging message"));
    }
  
    
    @Test
    public void hitBFFAPI() throws Exception{
    	Acknowledgment ack = mock(Acknowledgment.class);
    	List<String> list  = new ArrayList<>();
    	list.add(
    			"{\"cacheKey\":[\"HR_1234\",\"HR_2233\",\"HR_12331\"]}"
    			);
    	evictCacheListener.listen(list,ack);
    	assertThat(appender.getOutput(), CoreMatchers.containsString("cleaning cache for cachekey : [HR_1234, HR_2233, HR_12331]"));
    }
}
