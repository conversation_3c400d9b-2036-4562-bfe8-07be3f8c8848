
package com.paytm.saga.listeners;

import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.es.ESResponse;
import com.paytm.saga.dto.es.EsHits;
import com.paytm.saga.dto.es.HitsEs;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.repository.DropOffRepository;
import com.paytm.saga.repository.ReminderHistoryRepository;
import com.paytm.saga.service.*;
import com.paytm.saga.service.external.EsService;
import com.paytm.saga.service.RPSService;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.OMSUtils;
import junit.framework.TestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class OMSListenersTest extends TestCase {

    @Mock
    Acknowledgment acknowledgment;

    @Mock
    MappingService mappingService;

    @Mock
    ChannelHistoryService channelHistoryService;
    @Mock
    EsService esService;
    @Mock
    CommonCacheService commonCacheService;

    @Mock
    DropOffRepository dropOffRepository;

    @Mock
    ChannelHistoryRepositoryWrapperService channelHistoryRepository;


    @Mock
    ReminderHistoryRepository reminderHistoryRepository;

    @Mock
    EvictCacheServiceImpl evictCacheService ;
    @Mock
    DropOffService dropOffService;
    
    @Mock
    MetricsHelper metricsHelper;;
    
    @Mock
    DropOffDBHelper dropOffDBHelper;

    @Mock
    DCATService dcatService;
    @Mock
    RPSService rpsService;

    @Mock
    KafkaProducerService kafkaProducerService;
    @Mock
    RecentService recentService;


    @Mock
    CVRProductCache cvrProductCache;

    @Mock
    OMSUtils  omsUtils;

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(EvictCacheListener.class));
    @InjectMocks
    ReminderHistoryService reminderHistoryService;

    @InjectMocks
    PlanExpiryHistoryService planExpiryHistoryService;

    DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    DateFormat initDateformat = new SimpleDateFormat(Constants.CommonConstants.COMMON_DATE_FORMAT);

    Date date1 = new Date(), date2 = new Date(), date3 =new Date(), date4 = new Date(), date5 = new Date(), date_now_minus_10_days = new Date();

    OMSListeners omsListeners = null;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        omsListeners = new OMSListeners(dcatService,channelHistoryRepository,mappingService,reminderHistoryService,dropOffDBHelper,metricsHelper, channelHistoryService, evictCacheService, recentService,esService,rpsService);

        date1 = DateUtils.addHours(new Date(),-12);
        date2 = DateUtils.addHours(new Date(),-24);
        date3 = DateUtils.addHours(new Date(),-36);
        date4 = DateUtils.addHours(new Date(),-48);
        date5 = DateUtils.addHours(new Date(),-60);

        date_now_minus_10_days = DateUtils.addDays(new Date(),-10);
        //MockitoAnnotations.initMocks(this);
        String config = "\"{\\\"disableDropOff\\\": false, \\\"categoryMapping\\\": {\\\"mobile\\\": [17, 21], \\\"financial services\\\": [131655]}, \\\"disableChatHistory\\\": false, \\\"smsEnabledServices\\\": [\\\"financial services\\\"], \\\"agentIdentificationLimit\\\": 2, \\\"smartReminderPrepaidEndDays\\\": 3, \\\"smartReminderPostpaidEndDays\\\": 3, \\\"smartReminderPrepaidStartDays\\\": 5, \\\"smartReminderCustomerBillLimit\\\": 60, \\\"recentDataUpdateAllowedServices\\\": [\\\"dth\\\", \\\"financial services\\\", \\\"tuition fee\\\",\\\"mobile\\\",\\\"electricity\\\"], \\\"paymodeCountSupportService\\\": [\\\"215903\\\", \\\"292581\\\"], \\\"failedInResponseStatesService\\\": [\\\"00\\\", \\\"07\\\"]}\"\n";
        try{
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e){
//              logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }

    private OMSResponseModel getOMSResponseModelMock(){

        OMSResponseModel omsResponseModel = new OMSResponseModel();
        String dateString = "";
        try{
            dateString = initDateformat.format(date1);
        } catch (Exception e){};
        omsResponseModel.setCustomerId(1231314L);
        omsResponseModel.setOrderId(231333L);
        omsResponseModel.setPayment_status(3);
        omsResponseModel.setChannelId("WEB 2");
        OMSFulfillmentsModel  omsFulfillmentsModel = new OMSFulfillmentsModel();
        omsFulfillmentsModel.setFulfillment_response("{\"in_code\":\"00\", \"id\": 10}");
        omsFulfillmentsModel.setId(10L);
        List<OMSFulfillmentsModel> omsFulfillmentsModels = new ArrayList<>();
        omsFulfillmentsModels.add(omsFulfillmentsModel);
        omsResponseModel.setFulfillments(omsFulfillmentsModels);
//        omsResponseModel.getFulfillments().get(0).setFulfillment_response("{\"in_code\":\"00\", \"id\": 10}");
        OMSItemModel omsItemModel = new OMSItemModel();
        omsItemModel.setItemId(1233111L);
        omsItemModel.setFulfillmentId(10L);
        omsItemModel.setMrp("5");
        omsItemModel.setVerticalId(4);
        omsItemModel.setStatus(6L);
        omsItemModel.setOrderId(231333L);
        omsItemModel.setPrice(10.5);
        omsItemModel.setFulfillmentReq("{\"price\":20,\"recharge_number\":\"123456789013\"}");

        OMSItemProductModel omsItemProductModel = new OMSItemProductModel();
        OMSItemAttributeModel omsItemAttributeModel = new OMSItemAttributeModel();
        omsItemAttributeModel.setCircle("delhi-ncr");
        omsItemAttributeModel.setOperator("airtel");
        omsItemAttributeModel.setPaytype("postpaid");
        omsItemAttributeModel.setService("mobile");

        omsItemProductModel.setProductId(12331L);
        omsItemProductModel.setCategoryId(11123L);
        omsItemProductModel.setAttributes(omsItemAttributeModel);

        omsItemModel.setProduct(omsItemProductModel);
        omsItemModel.setCreatedAt("2023-05-09T08:31:32.000Z");
        omsItemModel.setUpdatedAt(dateString);

        List<OMSItemModel> omsItemModels = new ArrayList<OMSItemModel>();

        omsItemModels.add(omsItemModel);
        omsResponseModel.setItems(omsItemModels);
        return omsResponseModel;

    }
    private OMSResponseModel getOMSResponseModelMock1(){

        OMSResponseModel omsResponseModel = new OMSResponseModel();
        String dateString = "";
        try{
            dateString = initDateformat.format(date1);
        } catch (Exception e){};
        omsResponseModel.setCustomerId(1231314L);
        omsResponseModel.setOrderId(231333L);
        omsResponseModel.setPayment_status(3);
        omsResponseModel.setChannelId("WEB 2");

        OMSItemModel omsItemModel = new OMSItemModel();
        omsItemModel.setItemId(1233111L);
        omsItemModel.setMrp("5");
        omsItemModel.setVerticalId(4);
        omsItemModel.setStatus(7L);
        omsItemModel.setOrderId(231333L);
        omsItemModel.setPrice(10.5);
        omsItemModel.setFulfillmentReq("{\"price\":20,\"recharge_number\":\"123456789013\"}");

        OMSItemProductModel omsItemProductModel = new OMSItemProductModel();
        OMSItemAttributeModel omsItemAttributeModel = new OMSItemAttributeModel();
        omsItemAttributeModel.setCircle("delhi-ncr");
        omsItemAttributeModel.setOperator("airtel");
        omsItemAttributeModel.setPaytype("postpaid");
        omsItemAttributeModel.setService("mobile");

        omsItemProductModel.setProductId(12331L);
        omsItemProductModel.setCategoryId(11123L);
        omsItemProductModel.setAttributes(omsItemAttributeModel);

        omsItemModel.setProduct(omsItemProductModel);
        omsItemModel.setCreatedAt(dateString);
        omsItemModel.setUpdatedAt(dateString);

        List<OMSItemModel> omsItemModels = new ArrayList<OMSItemModel>();

        omsItemModels.add(omsItemModel);
        omsResponseModel.setItems(omsItemModels);
        return omsResponseModel;

    }

    private DCATGetPlanResponse getDCATPLanResponseMock(){
        DCATGetPlanResponse d = new DCATGetPlanResponse();
        d.setPlan_bucket("Top Up");
        d.setAddon_benefit1("A");
        d.setAddon_benefit2("B");
        d.setAddon_benefit3("C");
        d.setAddon_benefit4("D");
        d.setSms("1");
        d.setDescription("Data");
        d.setValidity("R");
        d.setTalktime("10");

        return d;
    }

    private DropOff getDropOffMockDataValidation(){
        Map<String, String> billsObj = new HashMap<String, String>() {};
        billsObj.put("plan_bucket", "Top Up");

        DropOff d = new DropOff();
        d.setCustomerId(1L);
        d.setAmount(String.valueOf(10.0));
        d.setCircle("delhi-ncr");
        d.setService("mobile");
        d.setPaytype("prepaid");
        d.setEventType("VALIDATION");
        d.setRechargeNumber("1122");
        d.setCategoryId(1L);
        d.setTransactionTime(new Date());
        d.setOrderId(2L);
        d.setProductId(3L);
        d.setOperator("airtel");
        d.setStatus("7");
        d.setIn_response_code("00");
        d.setPayment_status("1");
        d.setDisplayValues(null);
        d.setBillsObj(billsObj);
        d.setItemId(4L);
        return d;
    }


//    @Test
//    public void testlistenRechargeNumber() {
//
//        String data = "{\"data\":{\"info\":{\"s_id\":1,\"pg_cf\":0,\"user_flag\":257},\"phone\":\"5656154570\",\"title\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"subtotal\":20,\"remote_ip\":\"*************\",\"remote_ua\":\"Apache-HttpClient/4.5.3 (Java/1.8.0_181)\",\"channel_id\":\"WEB 2\",\"grandtotal\":20,\"customer_type\":1,\"customer_email\":\"\",\"order_discount\":0,\"total_discount\":0,\"shipping_amount\":0,\"shipping_charges\":0,\"customer_lastname\":\"\",\"customer_firstname\":\"\"},\"status\":1,\"id\":100072275842,\"payment_status\":1,\"customer_id\":**********,\"payment\":[{\"mid\":\"Rechar32004946353223\",\"kind\":1,\"appkey\":\"web:2:1\",\"status\":1,\"provider\":\"paytmnew\",\"pg_amount\":20,\"payment_method\":\"\",\"transaction_response\":{}}],\"address\":[],\"business\":[],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-12-11T04:55:46.000Z\",\"items\":[{\"id\":1349565749713,\"fulfillment_id\":121313131,\"order_id\":100072275841,\"status\":7,\"data\":{\"mrp\":5,\"sku\":\"Electricity\",\"info\":{\"c_sid\":1},\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":20,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"due_amount\":\"1863\",\"promo_details\":{}},\"attributes\":{},\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":76,\"custom_text1\":16,\"custom_text2\":\"TESTCASH35\",\"custom_text4\":{\"di\":{\"gp\":1},\"ii\":{\"w_t\":0,\"w_id\":181000},\"rid\":1,\"rpd\":0,\"rp_id\":2},\"custom_text5\":{\"di\":{},\"if\":0,\"hsn\":\"392610\"},\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":{\"price\":20,\"recharge_number\":\"123456789013\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-12-11T04:55:46.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"info\":\"{\\\"c_sid\\\":1}\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":20.1122345678,\"conv_fee\":0,\"discount\":0,\"meta_data1\":\"{\\\"due_amount\\\":\\\"1863\\\",\\\"promo_details\\\":{},\\\"recharge_benefits\\\":{\\\"data\\\" :\\\"2GB\\\", \\\"addon_benefit\\\" : [\\\"A\\\" ,\\\"B\\\"]},\\\"sms\\\" :\\\"11\\\",\\\"data\\\": \\\"3GB\\\"}\",\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":4,\"custom_text1\":16,\"custom_text2\":\"TESTCASH35\",\"custom_text4\":\"{\\\"di\\\":{\\\"gp\\\":1},\\\"ii\\\":{\\\"w_t\\\":0,\\\"w_id\\\":181000},\\\"rid\\\":1,\\\"rpd\\\":0,\\\"rp_id\\\":2}\",\"custom_text5\":\"{\\\"di\\\":{},\\\"if\\\":0,\\\"hsn\\\":\\\"392610\\\"}\",\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":20,\\\"recharge_number\\\":\\\"123456789013\\\"}\",\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3,\"order_item_id\":1349565749713,\"isPhysical\":false,\"ack_by\":\"2020-12-12T03:55:46.000Z\",\"isSubscription\":false,\"has_metadata\":true,\"bulk_pricing\":0,\"isCOD\":0,\"isRefundAttempted\":false,\"isNSS\":false,\"isLMD\":0,\"isLC\":0,\"isDisputed\":0,\"isNonReturnable\":0,\"isNonCancellable\":0,\"isReplacement\":0,\"hasReplacement\":0,\"isPFA\":0,\"hasLifafa\":0,\"isLifafaAttempted\":0,\"c2i\":false,\"isExchangeForwardItem\":0,\"isExchangeReverseItem\":0,\"isInstallation\":0,\"isCODUnpaid\":0,\"isChildItem\":0,\"warehouse_id\":181000,\"seed_id\":0,\"fulfillment_mode\":0,\"child_site_id\":1,\"kafka_timestamp\":\"2020-12-11T03:55:46.928Z\",\"kafka_time_diff\":928,\"extra_details\":{\"tracking_info\":{}},\"product\":{\"id\":**********,\"parent_id\":null,\"paytm_sku\":\"RECBILL-PAYMENTIGL-267095DA3F808D\",\"sku\":\"Electricity\",\"merchant_id\":267095,\"product_type\":1,\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL)\",\"value\":0,\"mrp_old\":11,\"price\":1,\"mrp\":5,\"shipping_charge\":null,\"brand_id\":565111,\"brand\":\"Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)\",\"url_key\":\"bill-payment-for-jaipur-vidyut-vitran-nigam-limited-jvvnl-RECBILL-PAYMENTIGL-267095DA3F808D\",\"status\":1,\"visibility\":4,\"tax_id\":null,\"vertical_id\":76,\"created_at\":\"2018-04-17T04:05:10.000Z\",\"updated_at\":\"2020-11-05T11:17:32.000Z\",\"image\":\"https://assetscdn1.paytm.com/images/catalog/product/R/RE/RECBILL-PAYMENTIGL-267095DA3F808D/1..jpg\",\"thumbnail\":\"https://assetscdn1.paytm.com/images/catalog/product/R/RE/RECBILL-PAYMENTIGL-267095DA3F808D/1..jpg\",\"fulfillment_service1\":0,\"short_description\":{},\"description\":\"[{\\\"title\\\":\\\"Product Details\\\",\\\"description\\\":\\\"\\\",\\\"attributes\\\":{\\\"Brand\\\":\\\"<a  rel='nofollow' href=\\\\\\\"/jaipur-vidyut-vitran-nigam-ltd-jvvnl-blpid-565111?use_mw=1&discoverability=online\\\\\\\">Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)</a>\\\",\\\"Product Code\\\":\\\"RECBILL-PAYMENTIGL-267095DA3F808D\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"},\\\"show_wt\\\":1},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}}]\",\"meta_title\":null,\"meta_description\":null,\"meta_keyword\":null,\"info\":\"{\\\"dimensions\\\":null,\\\"created_by\\\":\\\"admin\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"discoverability\\\":[1],\\\"productStandardName\\\":0,\\\"hsn\\\":\\\"392610\\\"}\",\"location_id\":null,\"start_date\":null,\"end_date\":null,\"is_in_stock\":1,\"merchant_stock\":1,\"manage_stock\":1,\"autocode\":null,\"fulfillment_mode\":0,\"pay_type\":65454,\"custom_text_1\":null,\"custom_text_2\":\"{\\\"child_site_ids\\\":{\\\"1\\\":{\\\"p\\\":1}}}\",\"custom_text_3\":\"{\\\"user_info\\\":{\\\"email\\\":\\\"<EMAIL>\\\",\\\"id\\\":290941,\\\"ip\\\":\\\"************\\\"}}\",\"custom_int_4\":9,\"custom_int_5\":6,\"custom_int_6\":null,\"merchant_name\":\"IGL - Indraprastha Gas Ltd.\\t\",\"tag\":null,\"conv_fee\":0,\"shipping_charge_old\":0,\"bargain\":null,\"discount\":0,\"max_dispatch_time\":3,\"promo_text\":null,\"category_id\":26,\"need_shipping\":0,\"return_in_days\":1,\"loyalty_points\":0,\"salesforce_case_id\":\"DEFAULT\",\"weight\":0,\"dimensions\":null,\"return_policy_id\":2,\"warranty\":null,\"attributes\":{\"circle\":\"delhi ncr\",\"service\":\"mobile\",\"paytype\":\"postpaid\",\"operator\":\"airtel\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\",\"operator_label\":\"Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)\",\"min_amount\":\"1\",\"max_amount\":\"999999\",\"prefetch\":\"1\",\"input_field-title-1\":\"K Number\",\"input_field-config_key-1\":\"recharge_number\",\"input_field-regex-1\":\"^((?!(210((21[1-2])|(72[1-5,7])|(73[1-4,6]))0))[0-9]+){12}$\",\"district\":\"Regular Bill Payments\",\"sub_district\":\"N/A\",\"sub_division\":\"N/A\",\"state\":\"Rajasthan\",\"board\":\"Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)\",\"input_field-message-1\":\"Please enter your 12 digit K Number\",\"disclaimer\":\"Your service provider will take two working days to consider bill paid in their accounts.\",\"input_selection\":\"N/A\",\"schedulable\":\"1\",\"brandImage\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"show_phone_book\":0,\"amount_title\":\"Amount\",\"amount_read_only\":0,\"input_field-show_phonebook-1\":0,\"input_field-isAlphanumeric-1\":true,\"input_field-optional-1\":\"false\",\"input_field-mandatory-1\":true,\"input_field-read_only-1\":0,\"input_field-type-1\":\"text\",\"input_field-show_field-1\":false,\"prefetch_title\":\"Consumer Details\",\"input_field-is_shown-1\":false,\"bc_app_enabled\":\"1\",\"automatic_type\":\"postpaid\",\"operator_display_label\":\"Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)\",\"paytype_display_label\":\"Postpaid\",\"service_display_label\":\"Electricity\",\"subscription_payment_modes_disabled\":\"[\\\"DEBIT_CARD\\\"]\",\"bbps_txn_type\":\"O\",\"bbps_assured_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/D/DE/DEATESTDEAL4D2C244AF/158.jpg\",\"ref_id\":\"BBPS Reference Number\"},\"custom_int_7\":null,\"custom_int_8\":null,\"custom_text_9\":null,\"fulfillment_service\":3,\"currency_id\":1,\"custom_text_11\":null,\"price_old\":0,\"raw_currency\":{\"price\":1,\"mrp\":5,\"shipping_charge\":null,\"currency_id\":1,\"code\":\"INR\",\"symbol\":\"₹\",\"country_code\":\"IND\"},\"currency\":{\"conversion_rate\":1,\"currency_id\":1,\"code\":\"INR\",\"symbol\":\"₹\",\"country_code\":\"IND\"},\"child_site_ids\":{\"1\":{\"p\":1}},\"pay_type_supported\":{\"COD\":0,\"CC\":1,\"DC\":1,\"NB\":1,\"ESCROW\":0,\"PPI\":1,\"EMI\":0,\"UPI\":1,\"PAYTM_DIGITAL_CREDIT\":1,\"PAY_AT_COUNTER\":1,\"ADVANCE_ACCOUNT\":1},\"pay_type_supported_meta\":{\"COD\":{\"status\":0,\"disallowed\":[]},\"CC\":{\"status\":1,\"disallowed\":[]},\"DC\":{\"status\":1,\"disallowed\":[]},\"NB\":{\"status\":1,\"disallowed\":[\"PPBL\"]},\"ESCROW\":{\"status\":0,\"disallowed\":[]},\"PPI\":{\"status\":1,\"disallowed\":[]},\"EMI\":{\"status\":0,\"disallowed\":[]},\"UPI\":{\"status\":1,\"disallowed\":[\"PPBL\"]},\"PAYTM_DIGITAL_CREDIT\":{\"status\":1,\"disallowed\":[]},\"PAY_AT_COUNTER\":{\"status\":1,\"disallowed\":[]},\"ADVANCE_ACCOUNT\":{\"status\":1,\"disallowed\":[]}},\"type\":\"Recharge\",\"product_title_template\":\"${order_type}${title}${meta_data}[Type] ${configuration}[recharge_number]\",\"attributes_dim\":{},\"filter_attributes\":\"{\\\"Size\\\": \\\"size\\\", \\\"Crust\\\":\\\"crust\\\", \\\"Foodtype\\\":\\\"foodtype\\\", \\\"Tax Exclusive\\\": \\\"tax_exclusive\\\", \\\"Vat Code\\\": \\\"vat_code\\\",\\\"Type\\\":\\\"type\\\",\\\"Portion Size\\\":\\\"portion_size\\\",\\\"Non Sellable\\\":\\\"non_sellable\\\",\\\"Addons Group\\\":\\\"addons_group\\\",\\\"Variations\\\":\\\"variations\\\",\\\"Service Type\\\": \\\"service_type\\\",\\\"Attribute1\\\":\\\"attribute1\\\", \\\"Attribute2\\\": \\\"attribute2\\\"}\",\"seller_variant\":null,\"vertical_attributes\":\"{\\\"Subscription Payment Modes Disabled\\\":\\\"subscription_payment_modes_disabled\\\",\\\"Inputfield Match with 2\\\":\\\"input_field-match_with-2\\\",\\\"Inputfield Help Button Url1 1\\\":\\\"input_field-help_button_url_1-1\\\",\\\"District Min Android Version\\\":\\\"district_min_android_version\\\",\\\"Inputfield Error 1\\\":\\\"input_field-error-1\\\",\\\"Inputfield Match with 1\\\":\\\"input_field-match_with-1\\\",\\\"Inputfield Help Button Url1 2\\\":\\\"input_field-help_button_url_1-2\\\",\\\"Inputfield Help Description1 2\\\":\\\"input_field-help_description_1-2\\\",\\\"Inputfield Error 2\\\":\\\"input_field-error-2\\\",\\\"Inputfield Help Description1 1\\\":\\\"input_field-help_description_1-1\\\",\\\"Hide By Default 19\\\":\\\"hide_by_default_19\\\",\\\"Inputfield Error 3\\\":\\\"input_field-error-3\\\",\\\"Challan Type\\\":\\\"challan_type\\\",\\\"Inputfield Error 4\\\":\\\"input_field-error-4\\\",\\\"Inputfield Error 5\\\":\\\"input_field-error-5\\\",\\\"Nickname Label\\\":\\\"nickname_label\\\",\\\"Pay Type\\\":\\\"paytype\\\",\\\"regEx\\\":\\\"regEx\\\",\\\"Alert Heading\\\":\\\"alert_heading\\\",\\\"testing 1\\\":\\\"testing1\\\",\\\"Board Is Default Selected\\\":\\\"board_isDefaultSelected\\\",\\\"Inputfield Right button type 2\\\":\\\"input_field-right_button_type-2\\\",\\\"Inputfield Right button type 3\\\":\\\"input_field-right_button_type-3\\\",\\\"Inputfield Right button type 1\\\":\\\"input_field-right_button_type-1\\\",\\\"Inputfield Title 5\\\":\\\"input_field-title-5\\\",\\\"Inputfield Title 4\\\":\\\"input_field-title-4\\\",\\\"Inputfield Right button type 4\\\":\\\"input_field-right_button_type-4\\\",\\\"Inputfield Title 6\\\":\\\"input_field-title-6\\\",\\\"Inputfield Right button type 5\\\":\\\"input_field-right_button_type-5\\\",\\\"Inputfield Title 1\\\":\\\"input_field-title-1\\\",\\\"Inputfield Title 3\\\":\\\"input_field-title-3\\\",\\\"Inputfield Title 2\\\":\\\"input_field-title-2\\\",\\\"Declaration Text 1\\\":\\\"declaration_text_1\\\",\\\"Amount regex\\\":\\\"amount_regex\\\",\\\"Declaration Text 2\\\":\\\"declaration_text_2\\\",\\\"Operator Display Label\\\":\\\"operator_display_label\\\",\\\"ULB Code\\\":\\\"ulb_code\\\",\\\"Sub District Min Android Version\\\":\\\"sub_district_min_android_version\\\",\\\"Inputfield Name 3\\\":\\\"input_field-name-3\\\",\\\"Inputfield Name 4\\\":\\\"input_field-name-4\\\",\\\"Inputfield Name 1\\\":\\\"input_field-name-1\\\",\\\"Inputfield Name 2\\\":\\\"input_field-name-2\\\",\\\"Group Config Key\\\":\\\"group_config_key\\\",\\\"Input Selection\\\":\\\"input_selection\\\",\\\"Schedulable\\\":\\\"schedulable\\\",\\\"Default Value 26\\\":\\\"default_value_26\\\",\\\"Hide Field Config Keys\\\":\\\"hide_field_config_keys\\\",\\\"Payment Schedule Label\\\":\\\"payment_schedule_label\\\",\\\"Multiple Selection\\\":\\\"multiple_selection\\\",\\\"Inputfield Name 5\\\":\\\"input_field-name-5\\\",\\\"Alert Popup Type\\\":\\\"alert_popup_type\\\",\\\"Show phone book\\\":\\\"show_phone_book\\\",\\\"Sample Bill\\\":\\\"sample_bill\\\",\\\"Inputfield Right button url 2\\\":\\\"input_field-right_button_url-2\\\",\\\"Automatic Campaign Name\\\":\\\"automatic_campaign_name\\\",\\\"Inputfield Right button url 1\\\":\\\"input_field-right_button_url-1\\\",\\\"One2One Offer Disclaimer\\\":\\\"One2One_offer_disclaimer\\\",\\\"Inputfield Help Description0 1\\\":\\\"input_field-help_description_0-1\\\",\\\"Input Selection Label\\\":\\\"input_selection_label\\\",\\\"Inputfield Right button url 5\\\":\\\"input_field-right_button_url-5\\\",\\\"Inputfield Right button url 4\\\":\\\"input_field-right_button_url-4\\\",\\\"Inputfield Right button url 3\\\":\\\"input_field-right_button_url-3\\\",\\\"Paytype Display Label\\\":\\\"paytype_display_label\\\",\\\"Gas Agency Name\\\":\\\"gas_agency_name\\\",\\\"Pay Type Label\\\":\\\"paytype_label\\\",\\\"Inputfield Hide field config key 2\\\":\\\"input_field-hide_field_config_key-2\\\",\\\"abc\\\":\\\"testing14April\\\",\\\"Inputfield Hide field config key 1\\\":\\\"input_field-hide_field_config_key-1\\\",\\\"Inputfield Character limit 2\\\":\\\"input_field-character_limit-2\\\",\\\"Operator Type\\\":\\\"operator_type\\\",\\\"Inputfield Character limit 3\\\":\\\"input_field-character_limit-3\\\",\\\"Declaration CTA 2\\\":\\\"declaration_cta_2\\\",\\\"Inputfield Character limit 1\\\":\\\"input_field-character_limit-1\\\",\\\"Checkbox Screen Title\\\":\\\"checkbox_screen_title\\\",\\\"Inputfield Character limit 4\\\":\\\"input_field-character_limit-4\\\",\\\"Inputfield Character limit 5\\\":\\\"input_field-character_limit-5\\\",\\\"Inputfield Hide field config key 4\\\":\\\"input_field-hide_field_config_key-4\\\",\\\"Amount title\\\":\\\"amount_title\\\",\\\"Inputfield Hide field config key 3\\\":\\\"input_field-hide_field_config_key-3\\\",\\\"Inputfield Hide field config key 6\\\":\\\"input_field-hide_field_config_key-6\\\",\\\"Dynamic Plan Category Label\\\":\\\"dynamic_plan_category_label\\\",\\\"Payment Schedule Option 1 Label\\\":\\\"payment_schedule_option_1_label\\\",\\\"Product Type Label\\\":\\\"producttype_label\\\",\\\"Inputfield Hide field config key 5\\\":\\\"input_field-hide_field_config_key-5\\\",\\\"Services\\\":\\\"Services\\\",\\\"Right Button Type\\\":\\\"right_button_type\\\",\\\"Alert Text 2\\\":\\\"alert_text_2\\\",\\\"Alert Text 1\\\":\\\"alert_text_1\\\",\\\"BBPS Txn Type\\\":\\\"bbps_txn_type\\\",\\\"Payment Schedule Option 1 Value\\\":\\\"payment_schedule_option_1_value\\\",\\\"Right Button Url\\\":\\\"right_button_url\\\",\\\"Amount read only\\\":\\\"amount_read_only\\\",\\\"Inputfield Ime action 2\\\":\\\"input_field-ime_action-2\\\",\\\"Inputfield Help Description3 1\\\":\\\"input_field-help_description_3-1\\\",\\\"Inputfield Ime action 1\\\":\\\"input_field-ime_action-1\\\",\\\"Inputfield Match width 5\\\":\\\"input_field-match_width-5\\\",\\\"User Consent\\\":\\\"user_consent\\\",\\\"Inputfield Show phonebook 1\\\":\\\"input_field-show_phonebook-1\\\",\\\"Inputfield Show phonebook 2\\\":\\\"input_field-show_phonebook-2\\\",\\\"Flow Image\\\":\\\"flow_image\\\",\\\"Inputfield Show phonebook 3\\\":\\\"input_field-show_phonebook-3\\\",\\\"Alert Checkbox\\\":\\\"alert_checkbox\\\",\\\"Inputfield Show phonebook 4\\\":\\\"input_field-show_phonebook-4\\\",\\\"Inputfield Show phonebook 5\\\":\\\"input_field-show_phonebook-5\\\",\\\"Operator Cgst\\\":\\\"Operator_cgst\\\",\\\"Inputfield Ime action 5\\\":\\\"input_field-ime_action-5\\\",\\\"Inputfield Ime action 4\\\":\\\"input_field-ime_action-4\\\",\\\"Checkbox Flow Type\\\":\\\"checkbox_flow_type\\\",\\\"Sub District Label\\\":\\\"sub_district_label\\\",\\\"Inputfield Ime action 3\\\":\\\"input_field-ime_action-3\\\",\\\"Inputfield Help Description3 2\\\":\\\"input_field-help_description_3-2\\\",\\\"Inputfield Label 5\\\":\\\"input_field-label-5\\\",\\\"Inputfield Label 4\\\":\\\"input_field-label-4\\\",\\\"Show View Bill\\\":\\\"show_view_bill\\\",\\\"Inputfield IsAlphanumeric 5\\\":\\\"input_field-isAlphanumeric-5\\\",\\\"Inputfield IsAlphanumeric 4\\\":\\\"input_field-isAlphanumeric-4\\\",\\\"Inputfield IsAlphanumeric 3\\\":\\\"input_field-isAlphanumeric-3\\\",\\\"Inputfield Label 3\\\":\\\"input_field-label-3\\\",\\\"Inputfield Match width 4\\\":\\\"input_field-match_width-4\\\",\\\"Inputfield IsAlphanumeric 2\\\":\\\"input_field-isAlphanumeric-2\\\",\\\"Inputfield Label 2\\\":\\\"input_field-label-2\\\",\\\"Inputfield Match width 3\\\":\\\"input_field-match_width-3\\\",\\\"Inputfield IsAlphanumeric 1\\\":\\\"input_field-isAlphanumeric-1\\\",\\\"Inputfield Label 1\\\":\\\"input_field-label-1\\\",\\\"Inputfield Match width 2\\\":\\\"input_field-match_width-2\\\",\\\"Inputfield Match width 1\\\":\\\"input_field-match_width-1\\\",\\\"Circle\\\":\\\"circle\\\",\\\"Is Bank Mandate Enabled\\\":\\\"is_bank_mandate_enabled\\\",\\\"Locality\\\":\\\"locality\\\",\\\"Max amount\\\":\\\"max_amount\\\",\\\"coursfewfewfewe\\\":\\\"Coursewfwfewe\\\",\\\"Inputfield Help Title1 1\\\":\\\"input_field-help_title_1-1\\\",\\\"Inputfield Help Title1 2\\\":\\\"input_field-help_title_1-2\\\",\\\"testing new\\\":\\\"Testingnew\\\",\\\"BBPS Logo Url\\\":\\\"bbps_logo_url\\\",\\\"Operator Igst\\\":\\\"Operator_igst\\\",\\\"State\\\":\\\"state\\\",\\\"Web Only 1\\\":\\\"web_only_1\\\",\\\"Service Label\\\":\\\"service_label\\\",\\\"District\\\":\\\"district\\\",\\\"Operator\\\":\\\"operator\\\",\\\"Inputfield Help Description2 2\\\":\\\"input_field-help_description_2-2\\\",\\\"Inputfield Help Description2 1\\\":\\\"input_field-help_description_2-1\\\",\\\"Test Attribute\\\":\\\"test1\\\",\\\"Remindable\\\":\\\"remindable\\\",\\\"Inputfield Optional 5\\\":\\\"input_field-optional-5\\\",\\\"Inputfield Optional 4\\\":\\\"input_field-optional-4\\\",\\\"Inputfield Optional 1\\\":\\\"input_field-optional-1\\\",\\\"Inputfield Optional 3\\\":\\\"input_field-optional-3\\\",\\\"Inputfield Optional 2\\\":\\\"input_field-optional-2\\\",\\\"Operator Label\\\":\\\"operator_label\\\",\\\"Alert Image 2\\\":\\\"alert_image_2\\\",\\\"Alert Image 1\\\":\\\"alert_image_1\\\",\\\"Inputfield Sample bill 3\\\":\\\"input_field-sample_bill-3\\\",\\\"Inputfield Sample bill 4\\\":\\\"input_field-sample_bill-4\\\",\\\"Inputfield Sample bill 5\\\":\\\"input_field-sample_bill-5\\\",\\\"Block Type\\\":\\\"block_type\\\",\\\"District Label\\\":\\\"district_label\\\",\\\"Inputfield Mandatory 4\\\":\\\"input_field-mandatory-4\\\",\\\"Inputfield Mandatory 3\\\":\\\"input_field-mandatory-3\\\",\\\"Inputfield Mandatory 5\\\":\\\"input_field-mandatory-5\\\",\\\"Inputfield Mandatory 2\\\":\\\"input_field-mandatory-2\\\",\\\"Recharge Status\\\":\\\"status\\\",\\\"Inputfield Mandatory 1\\\":\\\"input_field-mandatory-1\\\",\\\"Inputfield Read only 5\\\":\\\"input_field-read_only-5\\\",\\\"Inputfield Read only 4\\\":\\\"input_field-read_only-4\\\",\\\"Operator Sgst\\\":\\\"Operator_sgst\\\",\\\"Inputfield Read only 3\\\":\\\"input_field-read_only-3\\\",\\\"Inputfield Read only 2\\\":\\\"input_field-read_only-2\\\",\\\"Service Provider\\\":\\\"service_provider\\\",\\\"Circle Label\\\":\\\"circle_label\\\",\\\"BBPS Assured Logo\\\":\\\"bbps_assured_logo\\\",\\\"Sub Division Min Android Version\\\":\\\"sub_division_min_android_version\\\",\\\"Inputfield Sample bill 1\\\":\\\"input_field-sample_bill-1\\\",\\\"Vending Disclaimer\\\":\\\"vending_disclaimer\\\",\\\"Inputfield Sample bill 2\\\":\\\"input_field-sample_bill-2\\\",\\\"Inputfield Read only 1\\\":\\\"input_field-read_only-1\\\",\\\"Inputfield Help Title0 1\\\":\\\"input_field-help_title_0-1\\\",\\\"Recharge Info\\\":\\\"info\\\",\\\"Error Display Type\\\":\\\"error_display_type\\\",\\\"Brand Name\\\":\\\"brand_name\\\",\\\"Fee Value\\\":\\\"fee_value\\\",\\\"Card Opt In\\\":\\\"card_opt_in\\\",\\\"Operator Alert\\\":\\\"operator_alert\\\",\\\"Grouping Services\\\":\\\"groupingservices\\\",\\\"Gas Agency Code\\\":\\\"gas_agency_code\\\",\\\"Board Min Android Version\\\":\\\"borad_min_android_version\\\",\\\"Recharge Product Type\\\":\\\"producttype\\\",\\\"Gas Agency Address\\\":\\\"gas_agency_address\\\",\\\"Location\\\":\\\"Location\\\",\\\"Tax Type\\\":\\\"tax_type\\\",\\\"Utility Type\\\":\\\"utility_type\\\",\\\"Show Field Attribute for testing\\\":\\\"show_field_test_attribute\\\",\\\"Nickname Example\\\":\\\"nickname_example\\\",\\\"testing 23april\\\":\\\"testing23april\\\",\\\"Image\\\":\\\"image\\\",\\\"Automatic Type\\\":\\\"automatic_type\\\",\\\"Board Label\\\":\\\"board_label\\\",\\\"Invoice Message\\\":\\\"invoice_message\\\",\\\"Board\\\":\\\"board\\\",\\\"Inputfield Dynamic length 3\\\":\\\"input_field-dynamic_length-3\\\",\\\"Inputfield Dynamic length 2\\\":\\\"input_field-dynamic_length-2\\\",\\\"Right Button Label\\\":\\\"right_button_label\\\",\\\"Inputfield Dynamic length 1\\\":\\\"input_field-dynamic_length-1\\\",\\\"Amount message\\\":\\\"amount_message\\\",\\\"Post Order View Type\\\":\\\"post_order_view_type\\\",\\\"Prefetch\\\":\\\"prefetch\\\",\\\"Inputfield Hide field condition 4\\\":\\\"input_field-hide_field_condition-4\\\",\\\"Inputfield Hide field condition 3\\\":\\\"input_field-hide_field_condition-3\\\",\\\"Inputfield Hide field condition 2\\\":\\\"input_field-hide_field_condition-2\\\",\\\"Updated Info\\\":\\\"updated_info\\\",\\\"Inputfield Hide field condition 1\\\":\\\"input_field-hide_field_condition-1\\\",\\\"Checkbox New Screen\\\":\\\"checkbox_new_screen\\\",\\\"Inputfield Hide field condition 5\\\":\\\"input_field-hide_field_condition-5\\\",\\\"Choose Operator\\\":\\\"choose_operator\\\",\\\"Operator Brand\\\":\\\"operator_brand\\\",\\\"Automatic Campaign Id\\\":\\\"automatic_campaign_id\\\",\\\"Disclaimer\\\":\\\"disclaimer\\\",\\\"Confirm Text 1\\\":\\\"confirm_text_1\\\",\\\"BBPS Biller\\\":\\\"bbps_biller\\\",\\\"Society\\\":\\\"society\\\",\\\"Alert Subheading 1\\\":\\\"alert_subheading_1\\\",\\\"Payment Schedule Option 2 Label\\\":\\\"payment_schedule_option_2_label\\\",\\\"Inputfield Min 5\\\":\\\"input_field-min-5\\\",\\\"Inputfield Type 5\\\":\\\"input_field-type-5\\\",\\\"Alert Subheading 2\\\":\\\"alert_subheading_2\\\",\\\"Inputfield Min 4\\\":\\\"input_field-min-4\\\",\\\"Inputfield Type 4\\\":\\\"input_field-type-4\\\",\\\"Inputfield Min 3\\\":\\\"input_field-min-3\\\",\\\"Inputfield Type 3\\\":\\\"input_field-type-3\\\",\\\"Inputfield Min 2\\\":\\\"input_field-min-2\\\",\\\"Inputfield Type 2\\\":\\\"input_field-type-2\\\",\\\"Inputfield Min 1\\\":\\\"input_field-min-1\\\",\\\"Inputfield Type 1\\\":\\\"input_field-type-1\\\",\\\"Url\\\":\\\"url\\\",\\\"Inputfield Hide 4\\\":\\\"input_field-hide-4\\\",\\\"Inputfield Hide 5\\\":\\\"input_field-hide-5\\\",\\\"Inputfield Text space separation 5\\\":\\\"input_field-text_space_separation-5\\\",\\\"Fetch Amount\\\":\\\"fetch_amount\\\",\\\"Inputfield Hide 2\\\":\\\"input_field-hide-2\\\",\\\"Inputfield Hide 3\\\":\\\"input_field-hide-3\\\",\\\"State Label\\\":\\\"state_label\\\",\\\"Inputfield Hide 1\\\":\\\"input_field-hide-1\\\",\\\"Vending Charge\\\":\\\"vending_charge\\\",\\\"Bc App Enabled\\\":\\\"bc_app_enabled\\\",\\\"Inputfield Text space separation 2\\\":\\\"input_field-text_space_separation-2\\\",\\\"Inputfield Text space separation 1\\\":\\\"input_field-text_space_separation-1\\\",\\\"Payment Schedule Option 2 Value\\\":\\\"payment_schedule_option_2_value\\\",\\\"Inputfield Hide 6\\\":\\\"input_field-hide-6\\\",\\\"Inputfield Text space separation 4\\\":\\\"input_field-text_space_separation-4\\\",\\\"Inputfield Text space separation 3\\\":\\\"input_field-text_space_separation-3\\\",\\\"Inputfield Keyboard type 1\\\":\\\"input_field-keyboard_type-1\\\",\\\"testing 13april\\\":\\\"testing13april\\\",\\\"Inputfield Keyboard type 2\\\":\\\"input_field-keyboard_type-2\\\",\\\"Inputfield Keyboard type 3\\\":\\\"input_field-keyboard_type-3\\\",\\\"Inputfield Help 1\\\":\\\"input_field-help-1\\\",\\\"Inputfield Keyboard type 4\\\":\\\"input_field-keyboard_type-4\\\",\\\"Inputfield Help 4\\\":\\\"input_field-help-4\\\",\\\"Inputfield Help 3\\\":\\\"input_field-help-3\\\",\\\"Inputfield Help 5\\\":\\\"input_field-help-5\\\",\\\"Inputfield Keyboard type 5\\\":\\\"input_field-keyboard_type-5\\\",\\\"Min amount\\\":\\\"min_amount\\\",\\\"Convenience Fee\\\":\\\"convenience_fee\\\",\\\"Inputfield Help Title2 1\\\":\\\"input_field-help_title_2-1\\\",\\\"Confirm Text 19\\\":\\\"confirm_text_19\\\",\\\"Declaration Caption\\\":\\\"declaration_caption\\\",\\\"Corporation\\\":\\\"corporation\\\",\\\"Error Message\\\":\\\"error_msg\\\",\\\"Checkbox Display Type\\\":\\\"checkbox_display_type\\\",\\\"Type\\\":\\\"type\\\",\\\"Alert Message\\\":\\\"alert_message\\\",\\\"Sub District\\\":\\\"sub_district\\\",\\\"Testing All1\\\":\\\"testing_all1\\\",\\\"Fee Type\\\":\\\"fee_type\\\",\\\"Declaration CTA\\\":\\\"declaration_cta\\\",\\\"Inputfield Show field 5\\\":\\\"input_field-show_field-5\\\",\\\"Inputfield Show field 4\\\":\\\"input_field-show_field-4\\\",\\\"State Min Android Version\\\":\\\"state_min_android_version\\\",\\\"Inputfield Show field 3\\\":\\\"input_field-show_field-3\\\",\\\"Inputfield Show field 2\\\":\\\"input_field-show_field-2\\\",\\\"Inputfield Show field 1\\\":\\\"input_field-show_field-1\\\",\\\"Hide Fast Forward\\\":\\\"hide_fastforward\\\",\\\"test_v_operator\\\":\\\"test_v_operator\\\",\\\"T Value\\\":\\\"t_value\\\",\\\"Packages\\\":\\\"Packages\\\",\\\"Inputfield Help Button Label1 1\\\":\\\"input_field-help_button_label_1-1\\\",\\\"Inputfield Help Button Label1 2\\\":\\\"input_field-help_button_label_1-2\\\",\\\"Testing\\\":\\\"Testing\\\",\\\"Inputfield Regex 4\\\":\\\"input_field-regex-4\\\",\\\"Inputfield Regex 3\\\":\\\"input_field-regex-3\\\",\\\"Inputfield Regex 2\\\":\\\"input_field-regex-2\\\",\\\"Ref Id\\\":\\\"ref_id\\\",\\\"Inputfield Regex 1\\\":\\\"input_field-regex-1\\\",\\\"Brand Image Url\\\":\\\"brandImage\\\",\\\"Utility Type 2\\\":\\\"utility_type_2\\\",\\\"Inputfield Regex 5\\\":\\\"input_field-regex-5\\\",\\\"Hide By Default 1\\\":\\\"hide_by_default_1\\\",\\\"City\\\":\\\"city\\\",\\\"Hide Field Condition\\\":\\\"hide_field_condition\\\",\\\"Prefetch title\\\":\\\"prefetch_title\\\",\\\"Max Amount\\\":\\\"max_amount\\\",\\\"Sub Division\\\":\\\"sub_division\\\",\\\"Info Message\\\":\\\"info_message\\\",\\\"Inputfield Max 5\\\":\\\"input_field-max-5\\\",\\\"Show Display Values\\\":\\\"show_display_values\\\",\\\"Inputfield Max 4\\\":\\\"input_field-max-4\\\",\\\"Hide By Default\\\":\\\"hide_by_default\\\",\\\"One2One Offer Text\\\":\\\"One2One_offer_text\\\",\\\"Inputfield Type 6\\\":\\\"input_field-type-6\\\",\\\"Inputfield Message 3\\\":\\\"input_field-message-3\\\",\\\"Inputfield Message 4\\\":\\\"input_field-message-4\\\",\\\"Checkbox Flow Key\\\":\\\"checkbox_flow_key\\\",\\\"Inputfield Message 1\\\":\\\"input_field-message-1\\\",\\\"Button Value 19\\\":\\\"button_value_19\\\",\\\"Inputfield Message 2\\\":\\\"input_field-message-2\\\",\\\"Chat Channel Id\\\":\\\"chat_channel_id\\\",\\\"Inputfield Max 3\\\":\\\"input_field-max-3\\\",\\\"Inputfield Max 2\\\":\\\"input_field-max-2\\\",\\\"Service Type\\\":\\\"service_type\\\",\\\"Inputfield Max 1\\\":\\\"input_field-max-1\\\",\\\"Inputfield Message 5\\\":\\\"input_field-message-5\\\",\\\"Inputfield Config key 4\\\":\\\"input_field-config_key-4\\\",\\\"Operator Desc\\\":\\\"operator_desc\\\",\\\"Inputfield Config key 3\\\":\\\"input_field-config_key-3\\\",\\\"Right Button Opentype\\\":\\\"right_button_opentype\\\",\\\"Inputfield Config key 6\\\":\\\"input_field-config_key-6\\\",\\\"Inputfield Config key 5\\\":\\\"input_field-config_key-5\\\",\\\"Inputfield Help Button Url0 1\\\":\\\"input_field-help_button_url_0-1\\\",\\\"Message\\\":\\\"message\\\",\\\"Inputfield Config key 2\\\":\\\"input_field-config_key-2\\\",\\\"Inputfield Config key 1\\\":\\\"input_field-config_key-1\\\",\\\"Error Image\\\":\\\"error_image\\\",\\\"Button Value 1\\\":\\\"button_value_1\\\",\\\"Automatic Campaign Max Usage Count\\\":\\\"automatic_campaign_max_usage_count\\\",\\\"Utility Type 1\\\":\\\"utility_type_1\\\",\\\"Inputfield Is shown 1\\\":\\\"input_field-is_shown-1\\\",\\\"Inputfield Right button label 1\\\":\\\"input_field-right_button_label-1\\\",\\\"Inputfield Is shown 2\\\":\\\"input_field-is_shown-2\\\",\\\"Inputfield Is shown 3\\\":\\\"input_field-is_shown-3\\\",\\\"Inputfield Is shown 4\\\":\\\"input_field-is_shown-4\\\",\\\"Inputfield Help Button Label0 1\\\":\\\"input_field-help_button_label_0-1\\\",\\\"Inputfield Right button label 5\\\":\\\"input_field-right_button_label-5\\\",\\\"Agency Address Changes\\\":\\\"agency_address\\\",\\\"Ff Disable\\\":\\\"ff_disable\\\",\\\"Inputfield Right button label 4\\\":\\\"input_field-right_button_label-4\\\",\\\"Inputfield Right button label 3\\\":\\\"input_field-right_button_label-3\\\",\\\"Inputfield Right button label 2\\\":\\\"input_field-right_button_label-2\\\",\\\"Inputfield Url 4\\\":\\\"input_field-url-4\\\",\\\"Sub Division Label\\\":\\\"sub_division_label\\\",\\\"Inputfield Url 3\\\":\\\"input_field-url-3\\\",\\\"Inputfield Url 2\\\":\\\"input_field-url-2\\\",\\\"Inputfield Url 1\\\":\\\"input_field-url-1\\\",\\\"Hide Enable\\\":\\\"hide_enable\\\",\\\"Inputfield Is shown 5\\\":\\\"input_field-is_shown-5\\\",\\\"Inputfield Url 5\\\":\\\"input_field-url-5\\\",\\\"Service\\\":\\\"service\\\",\\\"Agency Code\\\":\\\"agency_code\\\",\\\"Property Type\\\":\\\"property_type\\\",\\\"Testing All\\\":\\\"testing_all\\\",\\\"Service Display Label\\\":\\\"service_display_label\\\",\\\"BBPS Logo Landing\\\":\\\"bbps_logo_landing\\\"}\",\"allowed_fields\":\"\",\"vertical_label\":\"recharge\",\"attribute_config\":null,\"variable_price\":1,\"offline_prod\":false,\"hsn\":\"392610\",\"attributes_dim_values\":{},\"return_policy_text\":\"No returns , No Replacement allowed on this product.. Cancellation allowed. \",\"cancellable\":1,\"replace_in_days\":0,\"policy_text\":\"No returns , No Replacement allowed on this product.. Cancellation allowed. \",\"validate\":1,\"bulk_pricing\":0,\"no_follow\":0,\"no_index\":0,\"gift_item\":0,\"schedulable\":1,\"not_cacellable_post_ship\":0,\"tax_data\":{},\"purchase_quantity\":{\"min\":1,\"max\":5},\"newurl\":\"https://catalog-staging.paytm.com/v1/p/bill-payment-for-jaipur-vidyut-vitran-nigam-limited-jvvnl-RECBILL-PAYMENTIGL-267095DA3F808D\",\"url\":\"https://catalog-staging.paytm.com/v1/mobile/product/**********\",\"seourl\":\"https://catalog-staging.paytm.com/v1/p/bill-payment-for-jaipur-vidyut-vitran-nigam-limited-jvvnl-RECBILL-PAYMENTIGL-267095DA3F808D\",\"validation\":[],\"product_replacement_attribute\":\"size\",\"condensed_text\":null,\"return_policy\":{\"return_policy_title\":\"No returns , No Replacement allowed on this product.\",\"return_policy_text\":\"\",\"cancellation_policy_title\":\"Cancellation allowed\",\"cancellation_policy_text\":null},\"category_L1\":16,\"category_L2\":26},\"category_tree\":[{\"id\":16,\"name\":\"Recharge or Pay for\",\"parent_id\":null},{\"id\":26,\"name\":\"Electricity\",\"parent_id\":16}]}],\"info\":257,\"phone\":\"5656154570\",\"title\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"subtotal\":20,\"remote_ip\":\"*************\",\"remote_ua\":\"Apache-HttpClient/4.5.3 (Java/1.8.0_181)\",\"channel_id\":\"WEB 2\",\"grandtotal\":20,\"customer_type\":1,\"customer_email\":\"\",\"order_discount\":0,\"total_discount\":0,\"shipping_amount\":0,\"shipping_charges\":0,\"customer_lastname\":\"\",\"customer_firstname\":\"\",\"collectableAmount\":0,\"selling_price\":20,\"site_id\":1,\"user_flag\":257,\"order_info\":\"{\\\"s_id\\\":1,\\\"pg_cf\\\":0,\\\"user_flag\\\":257}\",\"isMobileVerified\":true,\"isEmailVerified\":false,\"isNewCustomer\":false,\"isScwWallet\":true,\"isPrimeWallet\":false,\"isReseller\":false,\"isMallReseller\":false,\"amount_to_collect\":20,\"ship_by_date\":\"2020-12-11 09:25:45\",\"isReplacement\":false,\"hasLifafa\":false,\"isLifafaAttempted\":false,\"isCOD\":0,\"payments\":[{\"mid\":\"Rechar32004946353223\",\"kind\":1,\"appkey\":\"web:2:1\",\"status\":1,\"provider\":\"paytmnew\",\"pg_amount\":20,\"payment_method\":\"\",\"transaction_response\":{}}],\"query\":{},\"headers\":{},\"request_id\":\"**********.747-*************-874-5398137-1\"}";
//
//        List<String> messages = new ArrayList<String>();
//        messages.add(data);
//        OMSResponseModel omsResponseModel= JsonUtils.parseJson(data, OMSResponseModel.class);
//        omsListeners.listen(messages, acknowledgment);
//
//        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
//        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);
//
//        verify(channelHistoryService).save(argument.capture(),argument1.capture());
//        assertEquals("123456789013", argument.getValue().getRechargeNumber());
//
//    }

    @Test
    public void testlistenRechargeNumberInsertCassandra() {

        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        omsListeners.insertDataIntoCassandra(omsResponseModel,"");

        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);

        verify(channelHistoryService).save(argument.capture(),argument1.capture());
        assertEquals("123456789013", argument.getValue().getRechargeNumber());

    }
    @Test
    public void testlistenUpsertTxnCount() throws Exception {

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"paymodeCountSupportService\":[\"11123\",\"2\",\"3\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        ESResponse esResponse = new ESResponse();
        EsHits hits = new EsHits();
        HitsEs hitsEs = new HitsEs();
        Map<String,Object> map = new HashMap<>();
        map.put(Constants.EsConstatnts.ORDER_ID,"100075886622");
        hitsEs.setSource(map);
        ArrayList<HitsEs> hitsEs1 = new ArrayList<>();
        hitsEs1.add(hitsEs);
        hits.setHits(hitsEs1);
        esResponse.setHits(hits);
        esResponse.setTook(10);
//        when(esService.getEsDetailsForOrderCount(any())).thenReturn(esResponse);
        omsListeners.upsertTxnCount(omsResponseModel);

        assertNotNull(omsResponseModel);
    }
    @Test
    public void testlistenUpsertTxnCountFailedStatus() throws Exception {

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"paymodeCountSupportService\":[\"11123\",\"2\",\"3\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        OMSResponseModel omsResponseModel = getOMSResponseModelMock1();
        ESResponse esResponse = new ESResponse();
        EsHits hits = new EsHits();
        HitsEs hitsEs = new HitsEs();
        Map<String,Object> map = new HashMap<>();
        map.put(Constants.EsConstatnts.ORDER_ID,"100075886622");
        hitsEs.setSource(map);
        ArrayList<HitsEs> hitsEs1 = new ArrayList<>();
        hitsEs1.add(hitsEs);
        hits.setHits(hitsEs1);
        esResponse.setHits(hits);
        esResponse.setTook(10);
//        when(esService.getEsDetailsForOrderCount(any())).thenReturn(esResponse);
        omsListeners.upsertTxnCount(omsResponseModel);

        assertNotNull(omsResponseModel);
    }
    @Test
    public void testlistenUpsertTxnCountSameOrderId() throws Exception {

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"paymodeCountSupportService\":[\"11123\",\"2\",\"3\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        ESResponse esResponse = new ESResponse();
        EsHits hits = new EsHits();
        HitsEs hitsEs = new HitsEs();
        Map<String,Object> map = new HashMap<>();
        map.put(Constants.EsConstatnts.ORDER_ID,"231333");
        hitsEs.setSource(map);
        ArrayList<HitsEs> hitsEs1 = new ArrayList<>();
        hitsEs1.add(hitsEs);
        hits.setHits(hitsEs1);
        esResponse.setHits(hits);
        esResponse.setTook(10);
//        when(esService.getEsDetailsForOrderCount(any())).thenReturn(esResponse);
        omsListeners.upsertTxnCount(omsResponseModel);

        assertNotNull(omsResponseModel);
    }
    @Test
    public void testlistenUpsertTxnCountOfOrderWithDiffrentMonth() throws Exception {

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"paymodeCountSupportService\":[\"11123\",\"2\",\"3\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        omsResponseModel.getItems().get(0).setCreatedAt("2023-05-09T08:31:32.000Z");
        ESResponse esResponse = new ESResponse();
        EsHits hits = new EsHits();
        HitsEs hitsEs = new HitsEs();
        Map<String,Object> map = new HashMap<>();
        map.put(Constants.EsConstatnts.ORDER_ID,"231333");
        hitsEs.setSource(map);
        ArrayList<HitsEs> hitsEs1 = new ArrayList<>();
        hitsEs1.add(hitsEs);
        hits.setHits(hitsEs1);
        esResponse.setHits(hits);
        esResponse.setTook(10);
//        when(esService.getEsDetailsForOrderCount(any())).thenReturn(esResponse);
        omsListeners.upsertTxnCount(omsResponseModel);

        assertNotNull(omsResponseModel);
    }
    @Test
    public void testlistenUpsertTxnCountInvalidCatFormat() throws Exception {

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"paymodeCountSupportService\":[\"11123\",\"2\",\"3\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        omsResponseModel.getItems().get(0).getProduct().setCategoryId(null);
//        when(esService.getEsDetailsForOrderCount(any())).thenReturn(new ESResponse());
        omsListeners.upsertTxnCount(omsResponseModel);

        assertNotNull(omsResponseModel);
    }
    @Test
    public void testInsertCassandra_withRNMforDTH() {

        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        omsResponseModel.getItems().get(0).setMeta_data("{\"RMN\":\"12212121313\"}");
        omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("dth");
        omsListeners.insertDataIntoCassandra(omsResponseModel,"");

        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);

        verify(channelHistoryService).save(argument.capture(),argument1.capture());
        assertEquals("123456789013", argument.getValue().getRechargeNumber());
        assertEquals("12212121313", argument.getValue().getBillsObj().get("RMN"));

    }


    @Test
    public void testInsertCassandra_withoutRMN_with_SubsID_forDTH() {

        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        omsResponseModel.getItems().get(0).setMeta_data("{\"due_amount\":\"1863\",\"promo_details\":{},\"recharge_benefits\":{\"data\" :\"2GB\", \"addon_benefit\" : [\"A\" ,\"B\"]},\"sms\" :\"11\",\"data\": \"3GB\" , \"key\" : [{\"subId\" : \"1313\"}] }");
        omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("dth");
        omsListeners.insertDataIntoCassandra(omsResponseModel,"");

        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);

        verify(channelHistoryService).save(argument.capture(),argument1.capture());
        assertEquals("1313", argument.getValue().getRechargeNumber());
        assertEquals("123456789013", argument.getValue().getBillsObj().get("RMN"));

    }

    @Test
    public void testInsertCassandra_withRMN_with_SubsID_forDTH() {

        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        omsResponseModel.getItems().get(0).setMeta_data("{\"due_amount\":\"1863\",\"promo_details\":{},\"recharge_benefits\":{\"data\" :\"2GB\", \"addon_benefit\" : [\"A\" ,\"B\"]},\"sms\" :\"11\",\"data\": \"3GB\" , \"key\" : [{\"subId\" : \"1313\"}] , \"RMN\" : \"11122\"}");
        omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("dth");
        omsListeners.insertDataIntoCassandra(omsResponseModel,"");

        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);

        verify(channelHistoryService).save(argument.capture(),argument1.capture());
        assertEquals("123456789013", argument.getValue().getRechargeNumber());
        assertEquals("11122", argument.getValue().getBillsObj().get("RMN"));

    }


    @Test
    public void testlistenRechargeNumberInsertCassandraDcatNotNullAndPaytypePrepaid() {

        OMSResponseModel omsResponseModel = getOMSResponseModelMock();
        omsResponseModel.getItems().get(0).getProduct().getAttributes().setPaytype("prepaid");
        omsResponseModel.getItems().get(0).getProduct().getAttributes().setService("mobile");
        omsResponseModel.getItems().get(0).setMeta_data("{\"plan_bucket\":\"test_bucket\",\"data\":\"21132\",\"validity\":\"30\",\"talktime\":\"300\",\"sms\":\"200\"}");

        omsListeners.insertDataIntoCassandra(omsResponseModel,"");

        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);

        verify(channelHistoryService).save(argument.capture(),argument1.capture());
        System.out.println(argument.getValue().getOperator());

        assertEquals("123456789013", argument.getValue().getRechargeNumber());
        assertEquals("airtel",argument.getValue().getOperator());
        assertEquals("prepaid",argument.getValue().getPaytype());
        assertEquals("mobile",argument.getValue().getService());

    }
    @Test
    public void testInsertIntoCassandraRpsProductIdNonNull(){
        String message = "{\"data\":{},\"id\":************,\"payment_status\":1,\"channel_id\":\"\",\"customer_id\":23,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-10-28T10:52:20.000Z\\\",\\\"in_create_time\\\":\\\"2021-06-13T10:52:20.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https: //paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"CustomerName\\\":\\\"JAIBHARATFURNACESP.LTD.\\\",\\\"Officecode\\\":\\\"2101310\\\",\\\"bindernumber\\\":\\\"9652\\\",\\\"invoiceid\\\":\\\"*********\\\",\\\"Billdate\\\":\\\"10-06-2019\\\",\\\"Duedate\\\":\\\"2019-09-27\\\",\\\"amountpayable\\\":\\\"310\\\",\\\"billmonth\\\":\\\"10-06-2019\\\",\\\"BCITSid\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-06-13T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"shipped_at\":\"2021-10-28T10:52:20.000Z\",\"delivered_at\":\"2021-10-28T10:52:20.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":185,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":399,\"recharge_number\":\"4054 50XX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-10-28T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":399,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":185,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":399,\\\"recharge_number\\\":\\\"8755388736\\\"}\",\"product\":{\"id\":185,\"category_id\":156705,\"attributes\":{\"service\":\"mobile\",\"paytype\":\"prepaid\",\"operator\":\"Airtel\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";;
        OMSResponseModel omsResponseModel = JsonUtils.parseJson(message,OMSResponseModel.class);

        String response = "{\"plan_bucket\":null,\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":null}";
        DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);
        when(rpsService.getPlanDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);
        omsListeners.insertDataIntoCassandra(omsResponseModel,"");

        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);

        verify(channelHistoryService).save(argument.capture(), argument1.capture());

        assertEquals("28 day", argument.getValue().getDisplayValues().get("validity"));
        assertEquals("2.5GB/day",argument.getValue().getDisplayValues().get("data"));
        assertEquals("100/Day",argument.getValue().getDisplayValues().get("sms"));
        assertEquals(null,argument.getValue().getBillsObj().get("plan_id"));
        assertEquals(null,argument.getValue().getBillsObj().get("plan_bucket"));

    }

    @Test
    public void testInsertIntoCassandraRpsProductIdNull(){
        String message = "{\"data\":{},\"id\":************,\"payment_status\":1,\"channel_id\":\"\",\"customer_id\":23,\"address\":[],\"fulfillments\":[{\"id\":***************,\"order_id\":************,\"status\":6,\"tracking_number\":\"***********\",\"merchant_id\":267095,\"merchant_track_id\":\"***********\",\"fulfillment_service_id\":3,\"fulfillment_response\":\"{\\\"in_code\\\":\\\"08\\\",\\\"operator\\\":\\\"neft_citibank\\\",\\\"operator_id\\\":*********,\\\"gateway_code_map\\\":\\\"SUCCESS\\\",\\\"gateway_name\\\":\\\"jvvnlvcid\\\",\\\"short_operator_name\\\":\\\"neft_citibank\\\",\\\"gw_txn_error_code\\\":\\\"SUCCESS\\\",\\\"in_updation_time\\\":\\\"2021-10-28T10:52:20.000Z\\\",\\\"in_create_time\\\":\\\"2021-06-13T10:52:20.000Z\\\",\\\"operatorRefNumber\\\":\\\"15328\\\",\\\"ordersummary_url\\\":\\\"https: //paytm.me/pHkz-Iy\\\",\\\"in_txn_id\\\":***************,\\\"bbpsTxnId\\\":\\\"********************\\\",\\\"CustomerName\\\":\\\"JAIBHARATFURNACESP.LTD.\\\",\\\"Officecode\\\":\\\"2101310\\\",\\\"bindernumber\\\":\\\"9652\\\",\\\"invoiceid\\\":\\\"*********\\\",\\\"Billdate\\\":\\\"10-06-2019\\\",\\\"Duedate\\\":\\\"2019-09-27\\\",\\\"amountpayable\\\":\\\"310\\\",\\\"billmonth\\\":\\\"10-06-2019\\\",\\\"BCITSid\\\":\\\"15328\\\"}\",\"post_actions\":\"Success\",\"created_at\":\"2021-06-13T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"shipped_at\":\"2021-10-28T10:52:20.000Z\",\"delivered_at\":\"2021-10-28T10:52:20.000Z\",\"returned_at\":\"0000-00-00 00:00:00\"}],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-01-11T04:55:46.000Z\",\"items\":[{\"id\":1234,\"order_id\":************,\"fulfillment_id\":***************,\"status\":6,\"data\":{\"price\":11,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"data\":\"2 GB\",\"sms\":\"200\",\"due_amount\":\"1863\",\"cin\":\"20210408031212dd1b4990e4207f88cc709873d0b7306\",\"promo_details\":{}},\"attributes\":{},\"product_id\":null,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"fulfillment_req\":{\"price\":399,\"recharge_number\":\"4054 50XX XXXX 2178\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"meta_data\":\"{\\\"cin\\\":\\\"20210408031212dd1b4990e4207f88cc709873d0b7306\\\",\\\"producttype\\\":\\\"Recharge\\\"}\",\"created_at\":\"2021-10-28T10:52:20.000Z\",\"updated_at\":\"2021-10-28T10:52:20.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":399,\"conv_fee\":0,\"discount\":0,\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":null,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":56,\"custom_text1\":16,\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":399,\\\"recharge_number\\\":\\\"8755388736\\\"}\",\"product\":{\"id\":null,\"category_id\":156705,\"attributes\":{\"service\":\"mobile\",\"paytype\":\"prepaid\",\"operator\":\"Airtel\",\"circle\":\"Delhi NCR\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\"},\"fulfillment_service\":3}}]}";;
        OMSResponseModel omsResponseModel = JsonUtils.parseJson(message,OMSResponseModel.class);

        String response = "{\"plan_bucket\":\"Special Recharge\",\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":45245}";
        DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);
        when(rpsService.getPlanDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);

        omsListeners.insertDataIntoCassandra(omsResponseModel,"");

        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);

        verify(channelHistoryService).save(argument.capture(), argument1.capture());

        assertEquals("28 day", argument.getValue().getDisplayValues().get("validity"));
        assertEquals("2.5GB/day",argument.getValue().getDisplayValues().get("data"));
        assertEquals("100/Day",argument.getValue().getDisplayValues().get("sms"));
        assertEquals("45245",argument.getValue().getBillsObj().get("plan_id"));
        assertEquals("Special Recharge",argument.getValue().getBillsObj().get("plan_bucket"));
    }


    @Test
    public void testExcludedCustIds() {

        String data = "{\"data\":{\"info\":{\"s_id\":1,\"pg_cf\":0,\"user_flag\":257},\"phone\":\"5656154570\",\"title\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"subtotal\":20,\"remote_ip\":\"*************\",\"remote_ua\":\"Apache-HttpClient/4.5.3 (Java/1.8.0_181)\",\"channel_id\":\"WEB 2\",\"grandtotal\":20,\"customer_type\":1,\"customer_email\":\"\",\"order_discount\":0,\"total_discount\":0,\"shipping_amount\":0,\"shipping_charges\":0,\"customer_lastname\":\"\",\"customer_firstname\":\"\"},\"status\":1,\"id\":100072275842,\"payment_status\":1,\"customer_id\":243003266,\"payment\":[{\"mid\":\"Rechar32004946353223\",\"kind\":1,\"appkey\":\"web:2:1\",\"status\":1,\"provider\":\"paytmnew\",\"pg_amount\":20,\"payment_method\":\"\",\"transaction_response\":{}}],\"address\":[],\"business\":[],\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-12-11T04:55:46.000Z\",\"items\":[{\"id\":1349565749713,\"fulfillment_id\":121313131,\"order_id\":100072275841,\"status\":7,\"data\":{\"mrp\":5,\"sku\":\"Electricity\",\"info\":{\"c_sid\":1},\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":20,\"conv_fee\":0,\"discount\":0,\"meta_data\":{\"due_amount\":\"1863\",\"promo_details\":{}},\"attributes\":{},\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":76,\"custom_text1\":16,\"custom_text2\":\"TESTCASH35\",\"custom_text4\":{\"di\":{\"gp\":1},\"ii\":{\"w_t\":0,\"w_id\":181000},\"rid\":1,\"rpd\":0,\"rp_id\":2},\"custom_text5\":{\"di\":{},\"if\":0,\"hsn\":\"392610\"},\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":{\"price\":20,\"recharge_number\":\"123456789013\"},\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3},\"created_at\":\"2020-12-11T03:55:46.000Z\",\"updated_at\":\"2020-12-11T04:55:46.000Z\",\"mrp\":5,\"sku\":\"Electricity\",\"info\":\"{\\\"c_sid\\\":1}\",\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"price\":20.1122345678,\"conv_fee\":0,\"discount\":0,\"meta_data1\":\"{\\\"due_amount\\\":\\\"1863\\\",\\\"promo_details\\\":{},\\\"recharge_benefits\\\":{\\\"data\\\" :\\\"2GB\\\", \\\"addon_benefit\\\" : [\\\"A\\\" ,\\\"B\\\"]},\\\"sms\\\" :\\\"11\\\",\\\"data\\\": \\\"3GB\\\"}\",\"attributes\":\"{}\",\"max_refund\":20,\"product_id\":**********,\"promo_code\":\"TESTCASH35\",\"merchant_id\":267095,\"qty_ordered\":1,\"vertical_id\":4,\"custom_text1\":16,\"custom_text2\":\"TESTCASH35\",\"custom_text4\":\"{\\\"di\\\":{\\\"gp\\\":1},\\\"ii\\\":{\\\"w_t\\\":0,\\\"w_id\\\":181000},\\\"rid\\\":1,\\\"rpd\\\":0,\\\"rp_id\\\":2}\",\"custom_text5\":\"{\\\"di\\\":{},\\\"if\\\":0,\\\"hsn\\\":\\\"392610\\\"}\",\"ship_by_date\":\"2020-12-11 09:25:45\",\"selling_price\":20,\"fulfillment_req\":\"{\\\"price\\\":20,\\\"recharge_number\\\":\\\"123456789013\\\"}\",\"is_subscription\":false,\"shipping_amount\":0,\"shipping_charges\":0,\"promo_description\":\"kyc\",\"fulfillment_service\":3,\"order_item_id\":1349565749713,\"isPhysical\":false,\"ack_by\":\"2020-12-12T03:55:46.000Z\",\"isSubscription\":false,\"has_metadata\":true,\"bulk_pricing\":0,\"isCOD\":0,\"isRefundAttempted\":false,\"isNSS\":false,\"isLMD\":0,\"isLC\":0,\"isDisputed\":0,\"isNonReturnable\":0,\"isNonCancellable\":0,\"isReplacement\":0,\"hasReplacement\":0,\"isPFA\":0,\"hasLifafa\":0,\"isLifafaAttempted\":0,\"c2i\":false,\"isExchangeForwardItem\":0,\"isExchangeReverseItem\":0,\"isInstallation\":0,\"isCODUnpaid\":0,\"isChildItem\":0,\"warehouse_id\":181000,\"seed_id\":0,\"fulfillment_mode\":0,\"child_site_id\":1,\"kafka_timestamp\":\"2020-12-11T03:55:46.928Z\",\"kafka_time_diff\":928,\"extra_details\":{\"tracking_info\":{}},\"product\":{\"id\":**********,\"parent_id\":null,\"paytm_sku\":\"RECBILL-PAYMENTIGL-267095DA3F808D\",\"sku\":\"Electricity\",\"merchant_id\":267095,\"product_type\":1,\"name\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL)\",\"value\":0,\"mrp_old\":11,\"price\":1,\"mrp\":5,\"shipping_charge\":null,\"brand_id\":565111,\"brand\":\"Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)\",\"url_key\":\"bill-payment-for-jaipur-vidyut-vitran-nigam-limited-jvvnl-RECBILL-PAYMENTIGL-267095DA3F808D\",\"status\":1,\"visibility\":4,\"tax_id\":null,\"vertical_id\":76,\"created_at\":\"2018-04-17T04:05:10.000Z\",\"updated_at\":\"2020-11-05T11:17:32.000Z\",\"image\":\"https://assetscdn1.paytm.com/images/catalog/product/R/RE/RECBILL-PAYMENTIGL-267095DA3F808D/1..jpg\",\"thumbnail\":\"https://assetscdn1.paytm.com/images/catalog/product/R/RE/RECBILL-PAYMENTIGL-267095DA3F808D/1..jpg\",\"fulfillment_service1\":0,\"short_description\":{},\"description\":\"[{\\\"title\\\":\\\"Product Details\\\",\\\"description\\\":\\\"\\\",\\\"attributes\\\":{\\\"Brand\\\":\\\"<a  rel='nofollow' href=\\\\\\\"/jaipur-vidyut-vitran-nigam-ltd-jvvnl-blpid-565111?use_mw=1&discoverability=online\\\\\\\">Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)</a>\\\",\\\"Product Code\\\":\\\"RECBILL-PAYMENTIGL-267095DA3F808D\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"},\\\"show_wt\\\":1},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}},{\\\"title\\\":\\\"Return Policy\\\",\\\"attributes\\\":{\\\"Return Policy\\\":\\\"No returns , No Replacement allowed on this product.. Cancellation allowed.\\\"}}]\",\"meta_title\":null,\"meta_description\":null,\"meta_keyword\":null,\"info\":\"{\\\"dimensions\\\":null,\\\"created_by\\\":\\\"admin\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"discoverability\\\":[1],\\\"productStandardName\\\":0,\\\"hsn\\\":\\\"392610\\\"}\",\"location_id\":null,\"start_date\":null,\"end_date\":null,\"is_in_stock\":1,\"merchant_stock\":1,\"manage_stock\":1,\"autocode\":null,\"fulfillment_mode\":0,\"pay_type\":65454,\"custom_text_1\":null,\"custom_text_2\":\"{\\\"child_site_ids\\\":{\\\"1\\\":{\\\"p\\\":1}}}\",\"custom_text_3\":\"{\\\"user_info\\\":{\\\"email\\\":\\\"<EMAIL>\\\",\\\"id\\\":290941,\\\"ip\\\":\\\"************\\\"}}\",\"custom_int_4\":9,\"custom_int_5\":6,\"custom_int_6\":null,\"merchant_name\":\"IGL - Indraprastha Gas Ltd.\\t\",\"tag\":null,\"conv_fee\":0,\"shipping_charge_old\":0,\"bargain\":null,\"discount\":0,\"max_dispatch_time\":3,\"promo_text\":null,\"category_id\":26,\"need_shipping\":0,\"return_in_days\":1,\"loyalty_points\":0,\"salesforce_case_id\":\"DEFAULT\",\"weight\":0,\"dimensions\":null,\"return_policy_id\":2,\"warranty\":null,\"attributes\":{\"circle\":\"delhi ncr\",\"service\":\"mobile\",\"paytype\":\"postpaid\",\"operator\":\"airtel\",\"service_label\":\"Electricity\",\"paytype_label\":\"Bill Payment\",\"operator_label\":\"Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)\",\"min_amount\":\"1\",\"max_amount\":\"999999\",\"prefetch\":\"1\",\"input_field-title-1\":\"K Number\",\"input_field-config_key-1\":\"recharge_number\",\"input_field-regex-1\":\"^((?!(210((21[1-2])|(72[1-5,7])|(73[1-4,6]))0))[0-9]+){12}$\",\"district\":\"Regular Bill Payments\",\"sub_district\":\"N/A\",\"sub_division\":\"N/A\",\"state\":\"Rajasthan\",\"board\":\"Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)\",\"input_field-message-1\":\"Please enter your 12 digit K Number\",\"disclaimer\":\"Your service provider will take two working days to consider bill paid in their accounts.\",\"input_selection\":\"N/A\",\"schedulable\":\"1\",\"brandImage\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"show_phone_book\":0,\"amount_title\":\"Amount\",\"amount_read_only\":0,\"input_field-show_phonebook-1\":0,\"input_field-isAlphanumeric-1\":true,\"input_field-optional-1\":\"false\",\"input_field-mandatory-1\":true,\"input_field-read_only-1\":0,\"input_field-type-1\":\"text\",\"input_field-show_field-1\":false,\"prefetch_title\":\"Consumer Details\",\"input_field-is_shown-1\":false,\"bc_app_enabled\":\"1\",\"automatic_type\":\"postpaid\",\"operator_display_label\":\"Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)\",\"paytype_display_label\":\"Postpaid\",\"service_display_label\":\"Electricity\",\"subscription_payment_modes_disabled\":\"[\\\"DEBIT_CARD\\\"]\",\"bbps_txn_type\":\"O\",\"bbps_assured_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/D/DE/DEATESTDEAL4D2C244AF/158.jpg\",\"ref_id\":\"BBPS Reference Number\"},\"custom_int_7\":null,\"custom_int_8\":null,\"custom_text_9\":null,\"fulfillment_service\":3,\"currency_id\":1,\"custom_text_11\":null,\"price_old\":0,\"raw_currency\":{\"price\":1,\"mrp\":5,\"shipping_charge\":null,\"currency_id\":1,\"code\":\"INR\",\"symbol\":\"₹\",\"country_code\":\"IND\"},\"currency\":{\"conversion_rate\":1,\"currency_id\":1,\"code\":\"INR\",\"symbol\":\"₹\",\"country_code\":\"IND\"},\"child_site_ids\":{\"1\":{\"p\":1}},\"pay_type_supported\":{\"COD\":0,\"CC\":1,\"DC\":1,\"NB\":1,\"ESCROW\":0,\"PPI\":1,\"EMI\":0,\"UPI\":1,\"PAYTM_DIGITAL_CREDIT\":1,\"PAY_AT_COUNTER\":1,\"ADVANCE_ACCOUNT\":1},\"pay_type_supported_meta\":{\"COD\":{\"status\":0,\"disallowed\":[]},\"CC\":{\"status\":1,\"disallowed\":[]},\"DC\":{\"status\":1,\"disallowed\":[]},\"NB\":{\"status\":1,\"disallowed\":[\"PPBL\"]},\"ESCROW\":{\"status\":0,\"disallowed\":[]},\"PPI\":{\"status\":1,\"disallowed\":[]},\"EMI\":{\"status\":0,\"disallowed\":[]},\"UPI\":{\"status\":1,\"disallowed\":[\"PPBL\"]},\"PAYTM_DIGITAL_CREDIT\":{\"status\":1,\"disallowed\":[]},\"PAY_AT_COUNTER\":{\"status\":1,\"disallowed\":[]},\"ADVANCE_ACCOUNT\":{\"status\":1,\"disallowed\":[]}},\"type\":\"Recharge\",\"product_title_template\":\"${order_type}${title}${meta_data}[Type] ${configuration}[recharge_number]\",\"attributes_dim\":{},\"filter_attributes\":\"{\\\"Size\\\": \\\"size\\\", \\\"Crust\\\":\\\"crust\\\", \\\"Foodtype\\\":\\\"foodtype\\\", \\\"Tax Exclusive\\\": \\\"tax_exclusive\\\", \\\"Vat Code\\\": \\\"vat_code\\\",\\\"Type\\\":\\\"type\\\",\\\"Portion Size\\\":\\\"portion_size\\\",\\\"Non Sellable\\\":\\\"non_sellable\\\",\\\"Addons Group\\\":\\\"addons_group\\\",\\\"Variations\\\":\\\"variations\\\",\\\"Service Type\\\": \\\"service_type\\\",\\\"Attribute1\\\":\\\"attribute1\\\", \\\"Attribute2\\\": \\\"attribute2\\\"}\",\"seller_variant\":null,\"vertical_attributes\":\"{\\\"Subscription Payment Modes Disabled\\\":\\\"subscription_payment_modes_disabled\\\",\\\"Inputfield Match with 2\\\":\\\"input_field-match_with-2\\\",\\\"Inputfield Help Button Url1 1\\\":\\\"input_field-help_button_url_1-1\\\",\\\"District Min Android Version\\\":\\\"district_min_android_version\\\",\\\"Inputfield Error 1\\\":\\\"input_field-error-1\\\",\\\"Inputfield Match with 1\\\":\\\"input_field-match_with-1\\\",\\\"Inputfield Help Button Url1 2\\\":\\\"input_field-help_button_url_1-2\\\",\\\"Inputfield Help Description1 2\\\":\\\"input_field-help_description_1-2\\\",\\\"Inputfield Error 2\\\":\\\"input_field-error-2\\\",\\\"Inputfield Help Description1 1\\\":\\\"input_field-help_description_1-1\\\",\\\"Hide By Default 19\\\":\\\"hide_by_default_19\\\",\\\"Inputfield Error 3\\\":\\\"input_field-error-3\\\",\\\"Challan Type\\\":\\\"challan_type\\\",\\\"Inputfield Error 4\\\":\\\"input_field-error-4\\\",\\\"Inputfield Error 5\\\":\\\"input_field-error-5\\\",\\\"Nickname Label\\\":\\\"nickname_label\\\",\\\"Pay Type\\\":\\\"paytype\\\",\\\"regEx\\\":\\\"regEx\\\",\\\"Alert Heading\\\":\\\"alert_heading\\\",\\\"testing 1\\\":\\\"testing1\\\",\\\"Board Is Default Selected\\\":\\\"board_isDefaultSelected\\\",\\\"Inputfield Right button type 2\\\":\\\"input_field-right_button_type-2\\\",\\\"Inputfield Right button type 3\\\":\\\"input_field-right_button_type-3\\\",\\\"Inputfield Right button type 1\\\":\\\"input_field-right_button_type-1\\\",\\\"Inputfield Title 5\\\":\\\"input_field-title-5\\\",\\\"Inputfield Title 4\\\":\\\"input_field-title-4\\\",\\\"Inputfield Right button type 4\\\":\\\"input_field-right_button_type-4\\\",\\\"Inputfield Title 6\\\":\\\"input_field-title-6\\\",\\\"Inputfield Right button type 5\\\":\\\"input_field-right_button_type-5\\\",\\\"Inputfield Title 1\\\":\\\"input_field-title-1\\\",\\\"Inputfield Title 3\\\":\\\"input_field-title-3\\\",\\\"Inputfield Title 2\\\":\\\"input_field-title-2\\\",\\\"Declaration Text 1\\\":\\\"declaration_text_1\\\",\\\"Amount regex\\\":\\\"amount_regex\\\",\\\"Declaration Text 2\\\":\\\"declaration_text_2\\\",\\\"Operator Display Label\\\":\\\"operator_display_label\\\",\\\"ULB Code\\\":\\\"ulb_code\\\",\\\"Sub District Min Android Version\\\":\\\"sub_district_min_android_version\\\",\\\"Inputfield Name 3\\\":\\\"input_field-name-3\\\",\\\"Inputfield Name 4\\\":\\\"input_field-name-4\\\",\\\"Inputfield Name 1\\\":\\\"input_field-name-1\\\",\\\"Inputfield Name 2\\\":\\\"input_field-name-2\\\",\\\"Group Config Key\\\":\\\"group_config_key\\\",\\\"Input Selection\\\":\\\"input_selection\\\",\\\"Schedulable\\\":\\\"schedulable\\\",\\\"Default Value 26\\\":\\\"default_value_26\\\",\\\"Hide Field Config Keys\\\":\\\"hide_field_config_keys\\\",\\\"Payment Schedule Label\\\":\\\"payment_schedule_label\\\",\\\"Multiple Selection\\\":\\\"multiple_selection\\\",\\\"Inputfield Name 5\\\":\\\"input_field-name-5\\\",\\\"Alert Popup Type\\\":\\\"alert_popup_type\\\",\\\"Show phone book\\\":\\\"show_phone_book\\\",\\\"Sample Bill\\\":\\\"sample_bill\\\",\\\"Inputfield Right button url 2\\\":\\\"input_field-right_button_url-2\\\",\\\"Automatic Campaign Name\\\":\\\"automatic_campaign_name\\\",\\\"Inputfield Right button url 1\\\":\\\"input_field-right_button_url-1\\\",\\\"One2One Offer Disclaimer\\\":\\\"One2One_offer_disclaimer\\\",\\\"Inputfield Help Description0 1\\\":\\\"input_field-help_description_0-1\\\",\\\"Input Selection Label\\\":\\\"input_selection_label\\\",\\\"Inputfield Right button url 5\\\":\\\"input_field-right_button_url-5\\\",\\\"Inputfield Right button url 4\\\":\\\"input_field-right_button_url-4\\\",\\\"Inputfield Right button url 3\\\":\\\"input_field-right_button_url-3\\\",\\\"Paytype Display Label\\\":\\\"paytype_display_label\\\",\\\"Gas Agency Name\\\":\\\"gas_agency_name\\\",\\\"Pay Type Label\\\":\\\"paytype_label\\\",\\\"Inputfield Hide field config key 2\\\":\\\"input_field-hide_field_config_key-2\\\",\\\"abc\\\":\\\"testing14April\\\",\\\"Inputfield Hide field config key 1\\\":\\\"input_field-hide_field_config_key-1\\\",\\\"Inputfield Character limit 2\\\":\\\"input_field-character_limit-2\\\",\\\"Operator Type\\\":\\\"operator_type\\\",\\\"Inputfield Character limit 3\\\":\\\"input_field-character_limit-3\\\",\\\"Declaration CTA 2\\\":\\\"declaration_cta_2\\\",\\\"Inputfield Character limit 1\\\":\\\"input_field-character_limit-1\\\",\\\"Checkbox Screen Title\\\":\\\"checkbox_screen_title\\\",\\\"Inputfield Character limit 4\\\":\\\"input_field-character_limit-4\\\",\\\"Inputfield Character limit 5\\\":\\\"input_field-character_limit-5\\\",\\\"Inputfield Hide field config key 4\\\":\\\"input_field-hide_field_config_key-4\\\",\\\"Amount title\\\":\\\"amount_title\\\",\\\"Inputfield Hide field config key 3\\\":\\\"input_field-hide_field_config_key-3\\\",\\\"Inputfield Hide field config key 6\\\":\\\"input_field-hide_field_config_key-6\\\",\\\"Dynamic Plan Category Label\\\":\\\"dynamic_plan_category_label\\\",\\\"Payment Schedule Option 1 Label\\\":\\\"payment_schedule_option_1_label\\\",\\\"Product Type Label\\\":\\\"producttype_label\\\",\\\"Inputfield Hide field config key 5\\\":\\\"input_field-hide_field_config_key-5\\\",\\\"Services\\\":\\\"Services\\\",\\\"Right Button Type\\\":\\\"right_button_type\\\",\\\"Alert Text 2\\\":\\\"alert_text_2\\\",\\\"Alert Text 1\\\":\\\"alert_text_1\\\",\\\"BBPS Txn Type\\\":\\\"bbps_txn_type\\\",\\\"Payment Schedule Option 1 Value\\\":\\\"payment_schedule_option_1_value\\\",\\\"Right Button Url\\\":\\\"right_button_url\\\",\\\"Amount read only\\\":\\\"amount_read_only\\\",\\\"Inputfield Ime action 2\\\":\\\"input_field-ime_action-2\\\",\\\"Inputfield Help Description3 1\\\":\\\"input_field-help_description_3-1\\\",\\\"Inputfield Ime action 1\\\":\\\"input_field-ime_action-1\\\",\\\"Inputfield Match width 5\\\":\\\"input_field-match_width-5\\\",\\\"User Consent\\\":\\\"user_consent\\\",\\\"Inputfield Show phonebook 1\\\":\\\"input_field-show_phonebook-1\\\",\\\"Inputfield Show phonebook 2\\\":\\\"input_field-show_phonebook-2\\\",\\\"Flow Image\\\":\\\"flow_image\\\",\\\"Inputfield Show phonebook 3\\\":\\\"input_field-show_phonebook-3\\\",\\\"Alert Checkbox\\\":\\\"alert_checkbox\\\",\\\"Inputfield Show phonebook 4\\\":\\\"input_field-show_phonebook-4\\\",\\\"Inputfield Show phonebook 5\\\":\\\"input_field-show_phonebook-5\\\",\\\"Operator Cgst\\\":\\\"Operator_cgst\\\",\\\"Inputfield Ime action 5\\\":\\\"input_field-ime_action-5\\\",\\\"Inputfield Ime action 4\\\":\\\"input_field-ime_action-4\\\",\\\"Checkbox Flow Type\\\":\\\"checkbox_flow_type\\\",\\\"Sub District Label\\\":\\\"sub_district_label\\\",\\\"Inputfield Ime action 3\\\":\\\"input_field-ime_action-3\\\",\\\"Inputfield Help Description3 2\\\":\\\"input_field-help_description_3-2\\\",\\\"Inputfield Label 5\\\":\\\"input_field-label-5\\\",\\\"Inputfield Label 4\\\":\\\"input_field-label-4\\\",\\\"Show View Bill\\\":\\\"show_view_bill\\\",\\\"Inputfield IsAlphanumeric 5\\\":\\\"input_field-isAlphanumeric-5\\\",\\\"Inputfield IsAlphanumeric 4\\\":\\\"input_field-isAlphanumeric-4\\\",\\\"Inputfield IsAlphanumeric 3\\\":\\\"input_field-isAlphanumeric-3\\\",\\\"Inputfield Label 3\\\":\\\"input_field-label-3\\\",\\\"Inputfield Match width 4\\\":\\\"input_field-match_width-4\\\",\\\"Inputfield IsAlphanumeric 2\\\":\\\"input_field-isAlphanumeric-2\\\",\\\"Inputfield Label 2\\\":\\\"input_field-label-2\\\",\\\"Inputfield Match width 3\\\":\\\"input_field-match_width-3\\\",\\\"Inputfield IsAlphanumeric 1\\\":\\\"input_field-isAlphanumeric-1\\\",\\\"Inputfield Label 1\\\":\\\"input_field-label-1\\\",\\\"Inputfield Match width 2\\\":\\\"input_field-match_width-2\\\",\\\"Inputfield Match width 1\\\":\\\"input_field-match_width-1\\\",\\\"Circle\\\":\\\"circle\\\",\\\"Is Bank Mandate Enabled\\\":\\\"is_bank_mandate_enabled\\\",\\\"Locality\\\":\\\"locality\\\",\\\"Max amount\\\":\\\"max_amount\\\",\\\"coursfewfewfewe\\\":\\\"Coursewfwfewe\\\",\\\"Inputfield Help Title1 1\\\":\\\"input_field-help_title_1-1\\\",\\\"Inputfield Help Title1 2\\\":\\\"input_field-help_title_1-2\\\",\\\"testing new\\\":\\\"Testingnew\\\",\\\"BBPS Logo Url\\\":\\\"bbps_logo_url\\\",\\\"Operator Igst\\\":\\\"Operator_igst\\\",\\\"State\\\":\\\"state\\\",\\\"Web Only 1\\\":\\\"web_only_1\\\",\\\"Service Label\\\":\\\"service_label\\\",\\\"District\\\":\\\"district\\\",\\\"Operator\\\":\\\"operator\\\",\\\"Inputfield Help Description2 2\\\":\\\"input_field-help_description_2-2\\\",\\\"Inputfield Help Description2 1\\\":\\\"input_field-help_description_2-1\\\",\\\"Test Attribute\\\":\\\"test1\\\",\\\"Remindable\\\":\\\"remindable\\\",\\\"Inputfield Optional 5\\\":\\\"input_field-optional-5\\\",\\\"Inputfield Optional 4\\\":\\\"input_field-optional-4\\\",\\\"Inputfield Optional 1\\\":\\\"input_field-optional-1\\\",\\\"Inputfield Optional 3\\\":\\\"input_field-optional-3\\\",\\\"Inputfield Optional 2\\\":\\\"input_field-optional-2\\\",\\\"Operator Label\\\":\\\"operator_label\\\",\\\"Alert Image 2\\\":\\\"alert_image_2\\\",\\\"Alert Image 1\\\":\\\"alert_image_1\\\",\\\"Inputfield Sample bill 3\\\":\\\"input_field-sample_bill-3\\\",\\\"Inputfield Sample bill 4\\\":\\\"input_field-sample_bill-4\\\",\\\"Inputfield Sample bill 5\\\":\\\"input_field-sample_bill-5\\\",\\\"Block Type\\\":\\\"block_type\\\",\\\"District Label\\\":\\\"district_label\\\",\\\"Inputfield Mandatory 4\\\":\\\"input_field-mandatory-4\\\",\\\"Inputfield Mandatory 3\\\":\\\"input_field-mandatory-3\\\",\\\"Inputfield Mandatory 5\\\":\\\"input_field-mandatory-5\\\",\\\"Inputfield Mandatory 2\\\":\\\"input_field-mandatory-2\\\",\\\"Recharge Status\\\":\\\"status\\\",\\\"Inputfield Mandatory 1\\\":\\\"input_field-mandatory-1\\\",\\\"Inputfield Read only 5\\\":\\\"input_field-read_only-5\\\",\\\"Inputfield Read only 4\\\":\\\"input_field-read_only-4\\\",\\\"Operator Sgst\\\":\\\"Operator_sgst\\\",\\\"Inputfield Read only 3\\\":\\\"input_field-read_only-3\\\",\\\"Inputfield Read only 2\\\":\\\"input_field-read_only-2\\\",\\\"Service Provider\\\":\\\"service_provider\\\",\\\"Circle Label\\\":\\\"circle_label\\\",\\\"BBPS Assured Logo\\\":\\\"bbps_assured_logo\\\",\\\"Sub Division Min Android Version\\\":\\\"sub_division_min_android_version\\\",\\\"Inputfield Sample bill 1\\\":\\\"input_field-sample_bill-1\\\",\\\"Vending Disclaimer\\\":\\\"vending_disclaimer\\\",\\\"Inputfield Sample bill 2\\\":\\\"input_field-sample_bill-2\\\",\\\"Inputfield Read only 1\\\":\\\"input_field-read_only-1\\\",\\\"Inputfield Help Title0 1\\\":\\\"input_field-help_title_0-1\\\",\\\"Recharge Info\\\":\\\"info\\\",\\\"Error Display Type\\\":\\\"error_display_type\\\",\\\"Brand Name\\\":\\\"brand_name\\\",\\\"Fee Value\\\":\\\"fee_value\\\",\\\"Card Opt In\\\":\\\"card_opt_in\\\",\\\"Operator Alert\\\":\\\"operator_alert\\\",\\\"Grouping Services\\\":\\\"groupingservices\\\",\\\"Gas Agency Code\\\":\\\"gas_agency_code\\\",\\\"Board Min Android Version\\\":\\\"borad_min_android_version\\\",\\\"Recharge Product Type\\\":\\\"producttype\\\",\\\"Gas Agency Address\\\":\\\"gas_agency_address\\\",\\\"Location\\\":\\\"Location\\\",\\\"Tax Type\\\":\\\"tax_type\\\",\\\"Utility Type\\\":\\\"utility_type\\\",\\\"Show Field Attribute for testing\\\":\\\"show_field_test_attribute\\\",\\\"Nickname Example\\\":\\\"nickname_example\\\",\\\"testing 23april\\\":\\\"testing23april\\\",\\\"Image\\\":\\\"image\\\",\\\"Automatic Type\\\":\\\"automatic_type\\\",\\\"Board Label\\\":\\\"board_label\\\",\\\"Invoice Message\\\":\\\"invoice_message\\\",\\\"Board\\\":\\\"board\\\",\\\"Inputfield Dynamic length 3\\\":\\\"input_field-dynamic_length-3\\\",\\\"Inputfield Dynamic length 2\\\":\\\"input_field-dynamic_length-2\\\",\\\"Right Button Label\\\":\\\"right_button_label\\\",\\\"Inputfield Dynamic length 1\\\":\\\"input_field-dynamic_length-1\\\",\\\"Amount message\\\":\\\"amount_message\\\",\\\"Post Order View Type\\\":\\\"post_order_view_type\\\",\\\"Prefetch\\\":\\\"prefetch\\\",\\\"Inputfield Hide field condition 4\\\":\\\"input_field-hide_field_condition-4\\\",\\\"Inputfield Hide field condition 3\\\":\\\"input_field-hide_field_condition-3\\\",\\\"Inputfield Hide field condition 2\\\":\\\"input_field-hide_field_condition-2\\\",\\\"Updated Info\\\":\\\"updated_info\\\",\\\"Inputfield Hide field condition 1\\\":\\\"input_field-hide_field_condition-1\\\",\\\"Checkbox New Screen\\\":\\\"checkbox_new_screen\\\",\\\"Inputfield Hide field condition 5\\\":\\\"input_field-hide_field_condition-5\\\",\\\"Choose Operator\\\":\\\"choose_operator\\\",\\\"Operator Brand\\\":\\\"operator_brand\\\",\\\"Automatic Campaign Id\\\":\\\"automatic_campaign_id\\\",\\\"Disclaimer\\\":\\\"disclaimer\\\",\\\"Confirm Text 1\\\":\\\"confirm_text_1\\\",\\\"BBPS Biller\\\":\\\"bbps_biller\\\",\\\"Society\\\":\\\"society\\\",\\\"Alert Subheading 1\\\":\\\"alert_subheading_1\\\",\\\"Payment Schedule Option 2 Label\\\":\\\"payment_schedule_option_2_label\\\",\\\"Inputfield Min 5\\\":\\\"input_field-min-5\\\",\\\"Inputfield Type 5\\\":\\\"input_field-type-5\\\",\\\"Alert Subheading 2\\\":\\\"alert_subheading_2\\\",\\\"Inputfield Min 4\\\":\\\"input_field-min-4\\\",\\\"Inputfield Type 4\\\":\\\"input_field-type-4\\\",\\\"Inputfield Min 3\\\":\\\"input_field-min-3\\\",\\\"Inputfield Type 3\\\":\\\"input_field-type-3\\\",\\\"Inputfield Min 2\\\":\\\"input_field-min-2\\\",\\\"Inputfield Type 2\\\":\\\"input_field-type-2\\\",\\\"Inputfield Min 1\\\":\\\"input_field-min-1\\\",\\\"Inputfield Type 1\\\":\\\"input_field-type-1\\\",\\\"Url\\\":\\\"url\\\",\\\"Inputfield Hide 4\\\":\\\"input_field-hide-4\\\",\\\"Inputfield Hide 5\\\":\\\"input_field-hide-5\\\",\\\"Inputfield Text space separation 5\\\":\\\"input_field-text_space_separation-5\\\",\\\"Fetch Amount\\\":\\\"fetch_amount\\\",\\\"Inputfield Hide 2\\\":\\\"input_field-hide-2\\\",\\\"Inputfield Hide 3\\\":\\\"input_field-hide-3\\\",\\\"State Label\\\":\\\"state_label\\\",\\\"Inputfield Hide 1\\\":\\\"input_field-hide-1\\\",\\\"Vending Charge\\\":\\\"vending_charge\\\",\\\"Bc App Enabled\\\":\\\"bc_app_enabled\\\",\\\"Inputfield Text space separation 2\\\":\\\"input_field-text_space_separation-2\\\",\\\"Inputfield Text space separation 1\\\":\\\"input_field-text_space_separation-1\\\",\\\"Payment Schedule Option 2 Value\\\":\\\"payment_schedule_option_2_value\\\",\\\"Inputfield Hide 6\\\":\\\"input_field-hide-6\\\",\\\"Inputfield Text space separation 4\\\":\\\"input_field-text_space_separation-4\\\",\\\"Inputfield Text space separation 3\\\":\\\"input_field-text_space_separation-3\\\",\\\"Inputfield Keyboard type 1\\\":\\\"input_field-keyboard_type-1\\\",\\\"testing 13april\\\":\\\"testing13april\\\",\\\"Inputfield Keyboard type 2\\\":\\\"input_field-keyboard_type-2\\\",\\\"Inputfield Keyboard type 3\\\":\\\"input_field-keyboard_type-3\\\",\\\"Inputfield Help 1\\\":\\\"input_field-help-1\\\",\\\"Inputfield Keyboard type 4\\\":\\\"input_field-keyboard_type-4\\\",\\\"Inputfield Help 4\\\":\\\"input_field-help-4\\\",\\\"Inputfield Help 3\\\":\\\"input_field-help-3\\\",\\\"Inputfield Help 5\\\":\\\"input_field-help-5\\\",\\\"Inputfield Keyboard type 5\\\":\\\"input_field-keyboard_type-5\\\",\\\"Min amount\\\":\\\"min_amount\\\",\\\"Convenience Fee\\\":\\\"convenience_fee\\\",\\\"Inputfield Help Title2 1\\\":\\\"input_field-help_title_2-1\\\",\\\"Confirm Text 19\\\":\\\"confirm_text_19\\\",\\\"Declaration Caption\\\":\\\"declaration_caption\\\",\\\"Corporation\\\":\\\"corporation\\\",\\\"Error Message\\\":\\\"error_msg\\\",\\\"Checkbox Display Type\\\":\\\"checkbox_display_type\\\",\\\"Type\\\":\\\"type\\\",\\\"Alert Message\\\":\\\"alert_message\\\",\\\"Sub District\\\":\\\"sub_district\\\",\\\"Testing All1\\\":\\\"testing_all1\\\",\\\"Fee Type\\\":\\\"fee_type\\\",\\\"Declaration CTA\\\":\\\"declaration_cta\\\",\\\"Inputfield Show field 5\\\":\\\"input_field-show_field-5\\\",\\\"Inputfield Show field 4\\\":\\\"input_field-show_field-4\\\",\\\"State Min Android Version\\\":\\\"state_min_android_version\\\",\\\"Inputfield Show field 3\\\":\\\"input_field-show_field-3\\\",\\\"Inputfield Show field 2\\\":\\\"input_field-show_field-2\\\",\\\"Inputfield Show field 1\\\":\\\"input_field-show_field-1\\\",\\\"Hide Fast Forward\\\":\\\"hide_fastforward\\\",\\\"test_v_operator\\\":\\\"test_v_operator\\\",\\\"T Value\\\":\\\"t_value\\\",\\\"Packages\\\":\\\"Packages\\\",\\\"Inputfield Help Button Label1 1\\\":\\\"input_field-help_button_label_1-1\\\",\\\"Inputfield Help Button Label1 2\\\":\\\"input_field-help_button_label_1-2\\\",\\\"Testing\\\":\\\"Testing\\\",\\\"Inputfield Regex 4\\\":\\\"input_field-regex-4\\\",\\\"Inputfield Regex 3\\\":\\\"input_field-regex-3\\\",\\\"Inputfield Regex 2\\\":\\\"input_field-regex-2\\\",\\\"Ref Id\\\":\\\"ref_id\\\",\\\"Inputfield Regex 1\\\":\\\"input_field-regex-1\\\",\\\"Brand Image Url\\\":\\\"brandImage\\\",\\\"Utility Type 2\\\":\\\"utility_type_2\\\",\\\"Inputfield Regex 5\\\":\\\"input_field-regex-5\\\",\\\"Hide By Default 1\\\":\\\"hide_by_default_1\\\",\\\"City\\\":\\\"city\\\",\\\"Hide Field Condition\\\":\\\"hide_field_condition\\\",\\\"Prefetch title\\\":\\\"prefetch_title\\\",\\\"Max Amount\\\":\\\"max_amount\\\",\\\"Sub Division\\\":\\\"sub_division\\\",\\\"Info Message\\\":\\\"info_message\\\",\\\"Inputfield Max 5\\\":\\\"input_field-max-5\\\",\\\"Show Display Values\\\":\\\"show_display_values\\\",\\\"Inputfield Max 4\\\":\\\"input_field-max-4\\\",\\\"Hide By Default\\\":\\\"hide_by_default\\\",\\\"One2One Offer Text\\\":\\\"One2One_offer_text\\\",\\\"Inputfield Type 6\\\":\\\"input_field-type-6\\\",\\\"Inputfield Message 3\\\":\\\"input_field-message-3\\\",\\\"Inputfield Message 4\\\":\\\"input_field-message-4\\\",\\\"Checkbox Flow Key\\\":\\\"checkbox_flow_key\\\",\\\"Inputfield Message 1\\\":\\\"input_field-message-1\\\",\\\"Button Value 19\\\":\\\"button_value_19\\\",\\\"Inputfield Message 2\\\":\\\"input_field-message-2\\\",\\\"Chat Channel Id\\\":\\\"chat_channel_id\\\",\\\"Inputfield Max 3\\\":\\\"input_field-max-3\\\",\\\"Inputfield Max 2\\\":\\\"input_field-max-2\\\",\\\"Service Type\\\":\\\"service_type\\\",\\\"Inputfield Max 1\\\":\\\"input_field-max-1\\\",\\\"Inputfield Message 5\\\":\\\"input_field-message-5\\\",\\\"Inputfield Config key 4\\\":\\\"input_field-config_key-4\\\",\\\"Operator Desc\\\":\\\"operator_desc\\\",\\\"Inputfield Config key 3\\\":\\\"input_field-config_key-3\\\",\\\"Right Button Opentype\\\":\\\"right_button_opentype\\\",\\\"Inputfield Config key 6\\\":\\\"input_field-config_key-6\\\",\\\"Inputfield Config key 5\\\":\\\"input_field-config_key-5\\\",\\\"Inputfield Help Button Url0 1\\\":\\\"input_field-help_button_url_0-1\\\",\\\"Message\\\":\\\"message\\\",\\\"Inputfield Config key 2\\\":\\\"input_field-config_key-2\\\",\\\"Inputfield Config key 1\\\":\\\"input_field-config_key-1\\\",\\\"Error Image\\\":\\\"error_image\\\",\\\"Button Value 1\\\":\\\"button_value_1\\\",\\\"Automatic Campaign Max Usage Count\\\":\\\"automatic_campaign_max_usage_count\\\",\\\"Utility Type 1\\\":\\\"utility_type_1\\\",\\\"Inputfield Is shown 1\\\":\\\"input_field-is_shown-1\\\",\\\"Inputfield Right button label 1\\\":\\\"input_field-right_button_label-1\\\",\\\"Inputfield Is shown 2\\\":\\\"input_field-is_shown-2\\\",\\\"Inputfield Is shown 3\\\":\\\"input_field-is_shown-3\\\",\\\"Inputfield Is shown 4\\\":\\\"input_field-is_shown-4\\\",\\\"Inputfield Help Button Label0 1\\\":\\\"input_field-help_button_label_0-1\\\",\\\"Inputfield Right button label 5\\\":\\\"input_field-right_button_label-5\\\",\\\"Agency Address Changes\\\":\\\"agency_address\\\",\\\"Ff Disable\\\":\\\"ff_disable\\\",\\\"Inputfield Right button label 4\\\":\\\"input_field-right_button_label-4\\\",\\\"Inputfield Right button label 3\\\":\\\"input_field-right_button_label-3\\\",\\\"Inputfield Right button label 2\\\":\\\"input_field-right_button_label-2\\\",\\\"Inputfield Url 4\\\":\\\"input_field-url-4\\\",\\\"Sub Division Label\\\":\\\"sub_division_label\\\",\\\"Inputfield Url 3\\\":\\\"input_field-url-3\\\",\\\"Inputfield Url 2\\\":\\\"input_field-url-2\\\",\\\"Inputfield Url 1\\\":\\\"input_field-url-1\\\",\\\"Hide Enable\\\":\\\"hide_enable\\\",\\\"Inputfield Is shown 5\\\":\\\"input_field-is_shown-5\\\",\\\"Inputfield Url 5\\\":\\\"input_field-url-5\\\",\\\"Service\\\":\\\"service\\\",\\\"Agency Code\\\":\\\"agency_code\\\",\\\"Property Type\\\":\\\"property_type\\\",\\\"Testing All\\\":\\\"testing_all\\\",\\\"Service Display Label\\\":\\\"service_display_label\\\",\\\"BBPS Logo Landing\\\":\\\"bbps_logo_landing\\\"}\",\"allowed_fields\":\"\",\"vertical_label\":\"recharge\",\"attribute_config\":null,\"variable_price\":1,\"offline_prod\":false,\"hsn\":\"392610\",\"attributes_dim_values\":{},\"return_policy_text\":\"No returns , No Replacement allowed on this product.. Cancellation allowed. \",\"cancellable\":1,\"replace_in_days\":0,\"policy_text\":\"No returns , No Replacement allowed on this product.. Cancellation allowed. \",\"validate\":1,\"bulk_pricing\":0,\"no_follow\":0,\"no_index\":0,\"gift_item\":0,\"schedulable\":1,\"not_cacellable_post_ship\":0,\"tax_data\":{},\"purchase_quantity\":{\"min\":1,\"max\":5},\"newurl\":\"https://catalog-staging.paytm.com/v1/p/bill-payment-for-jaipur-vidyut-vitran-nigam-limited-jvvnl-RECBILL-PAYMENTIGL-267095DA3F808D\",\"url\":\"https://catalog-staging.paytm.com/v1/mobile/product/**********\",\"seourl\":\"https://catalog-staging.paytm.com/v1/p/bill-payment-for-jaipur-vidyut-vitran-nigam-limited-jvvnl-RECBILL-PAYMENTIGL-267095DA3F808D\",\"validation\":[],\"product_replacement_attribute\":\"size\",\"condensed_text\":null,\"return_policy\":{\"return_policy_title\":\"No returns , No Replacement allowed on this product.\",\"return_policy_text\":\"\",\"cancellation_policy_title\":\"Cancellation allowed\",\"cancellation_policy_text\":null},\"category_L1\":16,\"category_L2\":26},\"category_tree\":[{\"id\":16,\"name\":\"Recharge or Pay for\",\"parent_id\":null},{\"id\":26,\"name\":\"Electricity\",\"parent_id\":16}]}],\"info\":257,\"phone\":\"5656154570\",\"title\":\"Bill Payment for Jaipur Vidyut Vitran Nigam Limited (JVVNL) 123456789013\",\"subtotal\":20,\"remote_ip\":\"*************\",\"remote_ua\":\"Apache-HttpClient/4.5.3 (Java/1.8.0_181)\",\"channel_id\":\"WEB 2\",\"grandtotal\":20,\"customer_type\":1,\"customer_email\":\"\",\"order_discount\":0,\"total_discount\":0,\"shipping_amount\":0,\"shipping_charges\":0,\"customer_lastname\":\"\",\"customer_firstname\":\"\",\"collectableAmount\":0,\"selling_price\":20,\"site_id\":1,\"user_flag\":257,\"order_info\":\"{\\\"s_id\\\":1,\\\"pg_cf\\\":0,\\\"user_flag\\\":257}\",\"isMobileVerified\":true,\"isEmailVerified\":false,\"isNewCustomer\":false,\"isScwWallet\":true,\"isPrimeWallet\":false,\"isReseller\":false,\"isMallReseller\":false,\"amount_to_collect\":20,\"ship_by_date\":\"2020-12-11 09:25:45\",\"isReplacement\":false,\"hasLifafa\":false,\"isLifafaAttempted\":false,\"isCOD\":0,\"payments\":[{\"mid\":\"Rechar32004946353223\",\"kind\":1,\"appkey\":\"web:2:1\",\"status\":1,\"provider\":\"paytmnew\",\"pg_amount\":20,\"payment_method\":\"\",\"transaction_response\":{}}],\"query\":{},\"headers\":{},\"request_id\":\"**********.747-*************-874-5398137-1\"}";

        List<String> messages = new ArrayList<String>();
        messages.add(data);
        OMSResponseModel omsResponseModel= JsonUtils.parseJson(data, OMSResponseModel.class);
        omsListeners.listen(messages, acknowledgment);

        ArgumentCaptor<ChannelHistory> argument = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument1 = ArgumentCaptor.forClass(String.class);


    }


}
