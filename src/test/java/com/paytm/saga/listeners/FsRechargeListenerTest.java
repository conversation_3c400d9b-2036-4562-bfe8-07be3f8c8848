package com.paytm.saga.listeners;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.FsRechargeListenerException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FsRechargeConsumerModel;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.DCATService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.util.JsonUtils;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class FsRechargeListenerTest extends TestCase {

    @Mock
    DCATService dcatService;

    @Mock
    RecentService recentService;
    @Mock
    MetricsHelper metricsHelper;;

    @InjectMocks
    FsRechargeListener fsRechargeListener=null;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        fsRechargeListener = new FsRechargeListener(recentService,dcatService,metricsHelper);
        String config = "{\"disableDropOff\": false, \"categoryMapping\": {\"mobile\": [17, 21], \"financial services\": [131655]}, \"disableChatHistory\": false, \"smsEnabledServices\": [\"financial services\"], \"agentIdentificationLimit\": 300, \"smartReminderPrepaidEndDays\": 3, \"smartReminderPostpaidEndDays\": 3, \"smartReminderPrepaidStartDays\": 5, \"smartReminderCustomerBillLimit\": 60, \"recentDataUpdateAllowedServices\": [\"dth\", \"financial services\", \"tuition fee\",\"mobile\",\"electricity\"]}";
        try{
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e){
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }

    private FsRechargeConsumerModel mockFsRechargeConsumerModel(){
        FsRechargeConsumerModel fsRechargeConsumerModel = new FsRechargeConsumerModel();
        Map<String,Object> recentData = new HashMap<>();
        recentData.put("creditCardId","2019010324100df027330a57ee2c98f45c89b8c06a62e");
        recentData.put("panUniqueReference","56456d4cdscvs5225545gdffd");
        fsRechargeConsumerModel.setCustomerId(111110009L);
        fsRechargeConsumerModel.setRecharge_number("99999998888");
        fsRechargeConsumerModel.setRecharge_number_2("4564125562");
        fsRechargeConsumerModel.setAmount("2000");
        fsRechargeConsumerModel.setService("electricity");
        fsRechargeConsumerModel.setOperator("bescom");
        fsRechargeConsumerModel.setPaytype("postpaid");
        fsRechargeConsumerModel.setCircle("bangalore");
        fsRechargeConsumerModel.setCategoryId(456456456L);
        fsRechargeConsumerModel.setVerticalId(2);
        fsRechargeConsumerModel.setOrderId(111222333L);
        fsRechargeConsumerModel.setMerchantId(2365478L);
        fsRechargeConsumerModel.setItemId(11111111L);
        fsRechargeConsumerModel.setInResponseCode("00");
        fsRechargeConsumerModel.setRecentData(recentData);
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\",\"plan_bucket\":\"Data Pack\",\"bin_number\":\"416645\",\"additionalData\":{\"cardVariant\":null},\"customer_mobile\":\"9823209909\",\"enable_bill_payment\":\"1\",\"payment_request_type\":\"ELECTRICITY_PAYMENT\"}");
        fsRechargeConsumerModel.setOriginalPid(999988899L);
        fsRechargeConsumerModel.setCatalogProductID(9999888992L);

        return fsRechargeConsumerModel;

    }

    private FsRechargeConsumerModel mockFsRechargeConsumerModelForCC(){
        FsRechargeConsumerModel fsRechargeConsumerModel = new FsRechargeConsumerModel();
        Map<String,Object> recentData = new HashMap<>();
        recentData.put("creditCardId","");
        recentData.put("panUniqueReference","2019010324100df027330a57ee2c98f45c89b8c06a62e");
        fsRechargeConsumerModel.setCustomerId(111110009L);
        fsRechargeConsumerModel.setRecharge_number("XXXX XXXX XXXX 4312");
        fsRechargeConsumerModel.setRecharge_number_2(null);
        fsRechargeConsumerModel.setRecharge_number_4(null);
        fsRechargeConsumerModel.setRecharge_number_3(null);
        fsRechargeConsumerModel.setAmount("2000");
        fsRechargeConsumerModel.setService("financial services");
        fsRechargeConsumerModel.setOperator("visa_hdfc");
        fsRechargeConsumerModel.setPaytype("credit card");
        fsRechargeConsumerModel.setCircle("bangalore");
        fsRechargeConsumerModel.setCategoryId(456456456L);
        fsRechargeConsumerModel.setVerticalId(2);
        fsRechargeConsumerModel.setOrderId(111222333L);
        fsRechargeConsumerModel.setMerchantId(2365478L);
        fsRechargeConsumerModel.setItemId(11111111L);
        fsRechargeConsumerModel.setInResponseCode("00");
        fsRechargeConsumerModel.setRecentData(recentData);
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"panUniqueReference\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\"}");
        fsRechargeConsumerModel.setOriginalPid(999988899L);
        fsRechargeConsumerModel.setCatalogProductID(9999888992L);

        return fsRechargeConsumerModel;

    }


    @Test
    public void testInsertDataIntoCassandraWithNullPayload() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = null;
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithNullOrderId() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setOrderId(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithNullCustomerId() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setCustomerId(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }
    @Test
    public void testInsertDataIntoCassandraWithNullPaytype() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setPaytype(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithNullService() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithNullOperator() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setOperator(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithNullRechargeNumber() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setRecharge_number(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithNullPid() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setOriginalPid(null);
        fsRechargeConsumerModel.setCatalogProductID(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithNullRecentData() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setRecentData(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithNullInResponseCode() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setInResponseCode(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithEmptyRecentData() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        Map<String,Object> recentdata= new HashMap<>();
        fsRechargeConsumerModel.setRecentData(recentdata);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }
    @Test
    public void testInsertDataIntoCassandraWithNonSuccessResponseCode() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setInResponseCode("06");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithOrignalPidNull() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setOriginalPid(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());

    }

    @Test
    public void testInsertDataIntoCassandraWithCatalogProductIdNull() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setCatalogProductID(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());

    }
    @Test
    public void testInsertDataIntoCassandraWithPostpaidTransaction() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());
        assertEquals("",argument.getValue().getPlanBucket());


    }

    @Test
    public void testInsertDataIntoCassandraWithMobilePrepaidAndNonNullPlanBucket() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setOriginalPid(null);
        fsRechargeConsumerModel.setService("mobile");
        fsRechargeConsumerModel.setPaytype("prepaid");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());
        assertEquals("Data Pack",argument.getValue().getPlanBucket());

    }

    @Test
    public void testInsertDataIntoCassandraWithMobilePrepaidAndNullPlanBucketAndNonNullDcatResponse() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("mobile");
        fsRechargeConsumerModel.setPaytype("prepaid");
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\",\"plan_bucket\":null,\"bin_number\":\"416645\",\"additionalData\":{\"cardVariant\":null},\"customer_mobile\":\"9823209909\",\"enable_bill_payment\":\"1\",\"payment_request_type\":\"ELECTRICITY_PAYMENT\"}");
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());

        DCATGetPlanResponse dcatGetPlanResponse = new DCATGetPlanResponse();
        dcatGetPlanResponse.setPlan_bucket("Special Recharge");
        when(dcatService.getPlanDetails(DCATcategoryId,Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, fsRechargeConsumerModel.getOperator(), fsRechargeConsumerModel.getAmount(), fsRechargeConsumerModel.getCircle(), fsRechargeConsumerModel.getService())).thenReturn(dcatGetPlanResponse);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());
        assertEquals("Special Recharge",argument.getValue().getPlanBucket());

    }
    @Test
    public void testInsertDataIntoCassandraWithMobilePrepaidAndNullPlanBucketAndNullDcatResponse() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("mobile");
        fsRechargeConsumerModel.setPaytype("prepaid");
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\",\"plan_bucket\":null,\"bin_number\":\"416645\",\"additionalData\":{\"cardVariant\":null},\"customer_mobile\":\"9823209909\",\"enable_bill_payment\":\"1\",\"payment_request_type\":\"ELECTRICITY_PAYMENT\"}");
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());

        DCATGetPlanResponse dcatGetPlanResponse = new DCATGetPlanResponse();
        when(dcatService.getPlanDetails(DCATcategoryId,Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, fsRechargeConsumerModel.getOperator(), fsRechargeConsumerModel.getAmount(), fsRechargeConsumerModel.getCircle(), fsRechargeConsumerModel.getService())).thenReturn(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());
        assertEquals("",argument.getValue().getPlanBucket());

    }

    @Test
    public void testInsertDataIntoCassandraWithDTHPrepaidAndDishTvOperatorAndNullPlanBucketAndNullDcatResponse() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("dth");
        fsRechargeConsumerModel.setPaytype("prepaid");
        fsRechargeConsumerModel.setOperator("dishtv");
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\",\"plan_bucket\":null,\"bin_number\":\"416645\",\"additionalData\":{\"cardVariant\":null},\"customer_mobile\":\"9823209909\",\"enable_bill_payment\":\"1\",\"payment_request_type\":\"ELECTRICITY_PAYMENT\"}");
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());

        DCATGetPlanResponse dcatGetPlanResponse = new DCATGetPlanResponse();
        when(dcatService.getPlanDetails(DCATcategoryId,Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, fsRechargeConsumerModel.getOperator(), fsRechargeConsumerModel.getAmount(), fsRechargeConsumerModel.getCircle(), fsRechargeConsumerModel.getService())).thenReturn(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());
        assertEquals(Constants.DTH_PLAN_BUCKET,argument.getValue().getPlanBucket());

    }

    @Test
    public void testInsertDataIntoCassandraWithDTHPrepaidAndSunTvOperatorAndNullPlanBucketAndNullDcatResponse() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("dth");
        fsRechargeConsumerModel.setPaytype("prepaid");
        fsRechargeConsumerModel.setOperator("suntv");
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\",\"plan_bucket\":null,\"bin_number\":\"416645\",\"additionalData\":{\"cardVariant\":null},\"customer_mobile\":\"9823209909\",\"enable_bill_payment\":\"1\",\"payment_request_type\":\"ELECTRICITY_PAYMENT\"}");
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());

        DCATGetPlanResponse dcatGetPlanResponse = new DCATGetPlanResponse();
        when(dcatService.getPlanDetails(DCATcategoryId,Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, fsRechargeConsumerModel.getOperator(), fsRechargeConsumerModel.getAmount(), fsRechargeConsumerModel.getCircle(), fsRechargeConsumerModel.getService())).thenReturn(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());
        assertEquals(Constants.DTH_PLAN_BUCKET,argument.getValue().getPlanBucket());

    }
    @Test
    public void testInsertDataIntoCassandraWithServiceNonInAllowedServicesInServiceConfig() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("gas");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithCCData() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModelForCC();
        fsRechargeConsumerModel.setRecharge_number_4("tetshh12367423h8");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(Constants.CommonConstants.CC_DEFAULT_OPERATOR,argument.getValue().getOperator());
    }
    
    @Test
    public void  testInsertDataIntoCassandraWithNullAllowedServicesFromServiceConfig() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        FeatureConfigCache.getInstance().setFeatureConfigMap(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
    }

    @Test
    public void testInsertDataIntoCassandraWithMobilePostpaidData() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("mobile");
        fsRechargeConsumerModel.setPaytype("postpaid");
        fsRechargeConsumerModel.setOperator("airtel");
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\",\"bin_number\":\"416645\",\"additionalData\":{\"cardVariant\":null},\"customer_mobile\":\"9823209909\",\"enable_bill_payment\":\"1\",\"payment_request_type\":\"ELECTRICITY_PAYMENT\"}");
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());

        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());


    }

    @Test
    public void testInsertDataIntoCassandraWithMobilePrepaidWithNullMetaData() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("mobile");
        fsRechargeConsumerModel.setPaytype("prepaid");
        fsRechargeConsumerModel.setOperator("airtel");
        fsRechargeConsumerModel.setMetaData(null);
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());
        when(dcatService.getPlanDetails(DCATcategoryId,Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, fsRechargeConsumerModel.getOperator(), fsRechargeConsumerModel.getAmount(), fsRechargeConsumerModel.getCircle(), fsRechargeConsumerModel.getService())).thenReturn(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());


    }

    @Test
    public void testInsertDataIntoCassandraWithMobilePrepaidWithExceptionInParsingMetaData() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("mobile");
        fsRechargeConsumerModel.setPaytype("prepaid");
        fsRechargeConsumerModel.setOperator("airtel");
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\",\"bin_number\":\"416645\",\"additionalData\":{\"cardVariant\":null},\"customer_mobile\":\"9823209909\",\"enable_bill_payment\":\"1\",\"payment_request_type\":\"ELECTRICITY_PAYMENT\"");
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());
        when(dcatService.getPlanDetails(DCATcategoryId,Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, fsRechargeConsumerModel.getOperator(), fsRechargeConsumerModel.getAmount(), fsRechargeConsumerModel.getCircle(), fsRechargeConsumerModel.getService())).thenReturn(null);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());


    }

    @Test
    public void testInsertDataIntoCassandraWithMobilePrepaidWithNullPlanBucketInMetaDataAndNonNullDcatResponseWithNullPlanBucket() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModel();
        fsRechargeConsumerModel.setService("mobile");
        fsRechargeConsumerModel.setPaytype("prepaid");
        fsRechargeConsumerModel.setOperator("airtel");
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e\",\"bin_number\":\"416645\",\"additionalData\":{\"cardVariant\":null},\"customer_mobile\":\"9823209909\",\"enable_bill_payment\":\"1\",\"payment_request_type\":\"ELECTRICITY_PAYMENT\"}");
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(fsRechargeConsumerModel.getService().toLowerCase());
        DCATGetPlanResponse dcatGetPlanResponse = new DCATGetPlanResponse();
        dcatGetPlanResponse.setPlan_id("123");
        when(dcatService.getPlanDetails(DCATcategoryId,Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, fsRechargeConsumerModel.getOperator(), fsRechargeConsumerModel.getAmount(), fsRechargeConsumerModel.getCircle(), fsRechargeConsumerModel.getService())).thenReturn(dcatGetPlanResponse);
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals(fsRechargeConsumerModel,argument.getValue());


    }

    @Test
    public void testInsertDataIntoCassandraWithCCDataWithInvalidMetaData() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModelForCC();
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"panUniqueReference\":\"2019010324100df027330a57ee2c98f45c89b8c06a62e}");
        fsRechargeConsumerModel.setRecharge_number_4("12234tetste1123t");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());

    }

    @Test
    public void testInsertDataIntoCassandraWithCCDataWithNonNullRechargeNumber4AndNonNullPar() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModelForCC();
        fsRechargeConsumerModel.setRecharge_number_4("12234tetste1123t");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals("2019010324100df027330a57ee2c98f45c89b8c06a62e",argument.getValue().getRecharge_number());

    }

    @Test
    public void testInsertDataIntoCassandraWithCCDataWithNonNullRechargeNumber4AndNullPar() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModelForCC();
        fsRechargeConsumerModel.setRecharge_number_4("12234tetste1123t");
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\"}");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());


    }

    @Test
    public void testInsertDataIntoCassandraWithCCDataWithNonNullRechargenumber3() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModelForCC();
        fsRechargeConsumerModel.setRecharge_number_3("12234tetste1123t");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals("12234tetste1123t",argument.getValue().getRecharge_number());


    }

    @Test
    public void testInsertDataIntoCassandraWithCCDataWithNonNullCin() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModelForCC();
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\",\"cin\":\"2027330a57ee2c98f45c89b8c06a62e\"}");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(1)).updateRecentData(argument.capture());
        assertEquals("2027330a57ee2c98f45c89b8c06a62e",argument.getValue().getRecharge_number());


    }

    @Test
    public void testInsertDataIntoCassandraWithCCDataWithValidMetaDataWithNullRechargeNumbers() throws FsRechargeListenerException {
        FsRechargeConsumerModel fsRechargeConsumerModel = mockFsRechargeConsumerModelForCC();
        fsRechargeConsumerModel.setMetaData("{\"id\":\"583273746\"}");
        ArgumentCaptor<FsRechargeConsumerModel> argument = ArgumentCaptor.forClass(FsRechargeConsumerModel.class);
        fsRechargeListener.insertDataIntoCassandra(fsRechargeConsumerModel);
        verify(recentService,times(0)).updateRecentData(argument.capture());



    }


}
