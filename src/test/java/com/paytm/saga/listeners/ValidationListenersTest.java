
package com.paytm.saga.listeners;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.text.DateFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.Constants.CommonConstants;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.*;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.*;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.model.primaryKeys.SmartRecentsPrimaryKey;
import com.paytm.saga.recent.repository.SmartRecentsRepository;
import com.paytm.saga.repository.ReminderHistoryRepository;
import com.paytm.saga.service.*;
import com.paytm.saga.service.RPSService;
import com.paytm.saga.util.JsonUtils;
import junit.framework.TestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import com.paytm.saga.dto.CustomerDataResponseModel;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.dto.ResponsePage;
import com.paytm.saga.dto.ValidationKafkaResponseModel;
import com.paytm.saga.dto.ValidationResponseDisplayValues;
import com.paytm.saga.dto.ValidationResponseMetaBenefitsModel;
import com.paytm.saga.dto.ValidationResponseMetaModel;
import com.paytm.saga.service.CommonCacheService;
import com.paytm.saga.service.DCATService;
import com.paytm.saga.service.DropOffService;
import com.paytm.saga.service.MappingService;
import com.paytm.saga.service.PlanExpiryHistoryService;
import com.paytm.saga.service.ReminderHistoryService;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class ValidationListenersTest extends TestCase {

    @Mock
    Acknowledgment acknowledgment;

    @Mock
    MappingService mappingService;

    @Mock
    CommonCacheService commonCacheService;

    @Mock
    ReminderHistoryRepository reminderHistoryRepository;

    @Mock
    RecentsRepositoryWrapperService recentsRepository;

    @Mock
    RPSService rpsService;

    @Mock
    SmartRecentsRepository smartRecentsRepository;

    @Mock
    DropOffService dropOffService;
    
    @Mock
    MetricsHelper metricsHelper;
    @Mock
    KafkaProducerService kafkaProducerService;
    @Mock
    DCATService dcatService;
    @Mock
    SmartRecentsService smartRecentsService;
    @Mock
	private DropOffDBHelper dropOffDBHelper;

    @InjectMocks
    ReminderHistoryService reminderHistoryService;

    @InjectMocks
    PlanExpiryHistoryService planExpiryHistoryService;

    DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    DateFormat valdationInitDateformat = new SimpleDateFormat(Constants.CommonConstants.COMMON_DATE_FORMAT);

    Date date1 = new Date(), date2 = new Date(), date3 =new Date(), date4 = new Date(), date5 = new Date(), date_now_minus_10_days = new Date();

    //@InjectMocks
    ValidationListeners validationlistener = null;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);

        validationlistener = new ValidationListeners(mappingService,dcatService,recentsRepository,smartRecentsRepository,smartRecentsService,dropOffDBHelper,metricsHelper, rpsService, kafkaProducerService);

        date1 = DateUtils.addHours(new Date(),-12);
        date2 = DateUtils.addHours(new Date(),-24);
        date3 = DateUtils.addHours(new Date(),-36);
        date4 = DateUtils.addHours(new Date(),-48);
        date5 = DateUtils.addHours(new Date(),-60);

        date_now_minus_10_days = DateUtils.addDays(new Date(),-10);
    }

    private ValidationKafkaResponseModel getValidationKafkaResponseMock(){

        ValidationKafkaResponseModel validationKafkaResponseModel = new ValidationKafkaResponseModel();
        String dateString = "";
        try{
            dateString = valdationInitDateformat.format(date1);
        } catch (Exception e){};
        validationKafkaResponseModel.setTimestamps_init(dateString);
        validationKafkaResponseModel.setProductInfo_service("mobile");
        validationKafkaResponseModel.setCustomerInfo_channel_id("web");
        validationKafkaResponseModel.setProductInfo_category_id(17L);
        validationKafkaResponseModel.setProductId(12344L);
        validationKafkaResponseModel.setProductInfo_circle("delhi-ncr");
        validationKafkaResponseModel.setProductInfo_operator("airtel");
        validationKafkaResponseModel.setProductInfo_paytype("prepaid");
        validationKafkaResponseModel.setProductInfo_verticalId(1);
        validationKafkaResponseModel.setCustomerInfo_customer_id(1L);
        validationKafkaResponseModel.setUserData_amount("10.0");
        validationKafkaResponseModel.setUserData_recharge_number("1122");
        validationKafkaResponseModel.setValidationSuccessful(true);
        validationKafkaResponseModel.setCustomerInfo_channel_id("WEB 2");

        CustomerDataResponseModel cs = new CustomerDataResponseModel();
        cs.setFlowType("A");
        cs.setRef_id("B");
        cs.setCustomerName("C");
        cs.setBillDate("D");
        cs.setBillDueDate("E");
        cs.setTotalAmount("1");

        List<ValidationResponseDisplayValues> disp = new ArrayList<ValidationResponseDisplayValues>();
        disp.add(new ValidationResponseDisplayValues("A", "B"));
        validationKafkaResponseModel.setDisplayValues(disp);

        validationKafkaResponseModel.setCustomerDataResponse(cs);

        ValidationResponseMetaModel vm = new ValidationResponseMetaModel();
        vm.setPlan_bucket("Special");
        vm.setDescription("AAA");
        vm.setTalktime("1");
        vm.setData("1GB");
        vm.setPlan_id("1221241");
        vm.setValidity("1");

        List<String> addonBenefits = new ArrayList<String>();
        addonBenefits.add("Hotstar");
        addonBenefits.add("A");
        addonBenefits.add("B");
        addonBenefits.add("C");
        vm.setAddon_benefit(addonBenefits);

        ValidationResponseMetaBenefitsModel vmB = new ValidationResponseMetaBenefitsModel();
        vmB.setDescription("AAA");
        vmB.setTalktime("1");
        vmB.setData("1GB");
        vmB.setValidity("1");
        vmB.setAddon_benefit(addonBenefits);

        ValidationResponseMetaAdditionalPayload validationResponseMetaAdditionalPayload = new ValidationResponseMetaAdditionalPayload();
        validationResponseMetaAdditionalPayload.setMultipleRMN(false);

        vm.setAdditionalPayload(validationResponseMetaAdditionalPayload);

        vm.setRecharge_benefits(vmB);

        validationKafkaResponseModel.setMetaData(vm);


        return validationKafkaResponseModel;
    }

    private DCATGetPlanResponse getDCATPLanResponseMock(){
        DCATGetPlanResponse d = new DCATGetPlanResponse();
        d.setPlan_bucket("Top Up");
        d.setAddon_benefit1("A");
        d.setAddon_benefit2("B");
        d.setAddon_benefit3("C");
        d.setAddon_benefit4("D");
        d.setSms("1");
        d.setDescription("Data");
        d.setValidity("R");
        d.setTalktime("10");

        return d;
    }
    private ArrayList<Recents> getRecentsMockDataValidationOldUser(){

        RecentsPrimaryKey key_1 = new RecentsPrimaryKey();
        key_1.setCustomerId(null);
        key_1.setRechargeNumber(null);
        key_1.setService(null);
        key_1.setOperator(null);
        key_1.setPlanBucket(null);
        Recents recents_1 = new Recents();
        recents_1.setKey(key_1);
        recents_1.setOrderId(null);
        RecentsPrimaryKey key_2 = new RecentsPrimaryKey();
        key_2.setCustomerId(null);
        key_2.setRechargeNumber(null);
        key_2.setService(null);
        key_2.setOperator(null);
        key_2.setPlanBucket(null);
        Recents recents_2 = new Recents();
        recents_2.setKey(key_2);
        recents_2.setOrderId(1849L);

        ArrayList<Recents> recents = new ArrayList<>();
        recents.add((Recents) recents_1);
        recents.add((Recents) recents_2);
        return recents;
    }
    private ArrayList<Recents> getRecentsMockDataValidationNewUser(){

        RecentsPrimaryKey key_1 = new RecentsPrimaryKey();
        key_1.setCustomerId(null);
        key_1.setRechargeNumber(null);
        key_1.setService(null);
        key_1.setOperator(null);
        key_1.setPlanBucket(null);
        Recents recents_1 = new Recents();
        recents_1.setKey(key_1);
        recents_1.setOrderId(null);
        RecentsPrimaryKey key_2 = new RecentsPrimaryKey();
        key_2.setCustomerId(null);
        key_2.setRechargeNumber(null);
        key_2.setService(null);
        key_2.setOperator(null);
        key_2.setPlanBucket(null);
        Recents recents_2 = new Recents();
        recents_2.setKey(key_2);
        recents_2.setOrderId(null);

        ArrayList<Recents> recents = new ArrayList<>();
        recents.add((Recents) recents_1);
        recents.add((Recents) recents_2);
        return recents;
    }
    private DropOff getDropOffMockDataValidation(){
        Map<String, String> billsObj = new HashMap<String, String>() {};
        billsObj.put("plan_bucket", "Top Up");

        DropOff d = new DropOff();
        d.setCustomerId(1L);
        d.setAmount(String.valueOf(10.0));
        d.setCircle("delhi-ncr");
        d.setService("mobile");
        d.setPaytype("prepaid");
        d.setEventType("VALIDATION");
        d.setRechargeNumber("1122");
        d.setCategoryId(1L);
        d.setTransactionTime(new Date());
        d.setOrderId(2L);
        d.setProductId(3L);
        d.setOperator("airtel");
        d.setStatus("7");
        d.setIn_response_code("00");
        d.setPayment_status("1");
        d.setDisplayValues(null);
        d.setBillsObj(billsObj);
        d.setItemId(4L);
        return d;
    }

    private SmartRecents getSmartRecentMock(){
        SmartRecents smartRecents = new SmartRecents();
        SmartRecentsPrimaryKey smartRecentsPrimaryKey = new SmartRecentsPrimaryKey();
        smartRecentsPrimaryKey.setCustomerId(1L);
        smartRecentsPrimaryKey.setOperator("bses");
        smartRecentsPrimaryKey.setService("electricity");
        smartRecents.setKey(smartRecentsPrimaryKey);
        smartRecents.setCircle("bihar");
        return smartRecents;
    }
    @Test
    public void testSaveDropOffNewUser(){
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setMetaData(null);
        DCATGetPlanResponse d = getDCATPLanResponseMock();
        ArrayList<Recents> rec = getRecentsMockDataValidationNewUser();
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);
        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(d1.getCustomerId(),d1.getRechargeNumber(),d1.getService())).thenReturn(rec);
        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());
        assertEquals("VALIDATION", argument.getValue().getEventType());
        assertEquals("10.0", argument.getValue().getAmount());

    }

    @Test
    public void testSaveDropOffOldUser(){
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setMetaData(null);
        DCATGetPlanResponse d = getDCATPLanResponseMock();
        ArrayList<Recents> rec = getRecentsMockDataValidationOldUser();
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);
        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(d1.getCustomerId(),d1.getRechargeNumber(),d1.getService())).thenReturn(rec);
        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper , times(0)).insertDropOff(argument.capture());
    }
    @Test
    public void testCreateLagFalse() {
        ValidationKafkaResponseModel validationKafkaResponseModel = new ValidationKafkaResponseModel();
        Date targetTime = new Date(); //now
        String transactionTime = DateUtils.addSeconds(targetTime, -1*Constants.CommonConstants.VALIDATION_CONSUMER_LAG_TIME - 20 ).toInstant().toString();
        validationKafkaResponseModel.setTimestamps_init(transactionTime);
        Boolean t = validationlistener.createLag(validationKafkaResponseModel);
        assertEquals(new Boolean(false), t);
    }


    @Test
    public void testCreateLagTrue() {
        ValidationKafkaResponseModel validationKafkaResponseModel = new ValidationKafkaResponseModel();
        Date targetTime = new Date(); //now
        String transactionTime = DateUtils.addSeconds(targetTime, -1*Constants.CommonConstants.VALIDATION_CONSUMER_LAG_TIME + 10).toInstant().toString();
        Format formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
        validationKafkaResponseModel.setTimestamps_init(transactionTime);

        Boolean t = validationlistener.createLag(validationKafkaResponseModel);

        assertEquals(new Boolean(true), t);
    }

    @Test
    public void testIsSkipableFalse() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(false), res);
    }
    
    @Test
    public void testIsSkipableTrueForCC() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        validationKafkaResponseModel.getMetaData().setCin(null);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);
        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableFalseForCC() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        validationKafkaResponseModel.getMetaData().setCin("121232");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);
        assertEquals(new Boolean(false), res);
    }
    

    @Test
    public void testInsertIntoCassandraForCC() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        validationKafkaResponseModel.getMetaData().setCin("121232");
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);
        assertEquals("121232", d1.getRechargeNumber());
    }

    

    

    private ValidationKafkaResponseModel getValidationKafkaResponseMockForCC(){
        ValidationKafkaResponseModel validationKafkaResponseModel = new ValidationKafkaResponseModel();
        String dateString = "";
        try{
            dateString = valdationInitDateformat.format(date1);
        } catch (Exception e){};
        validationKafkaResponseModel.setTimestamps_init(dateString);
        validationKafkaResponseModel.setProductInfo_service(Constants.FINANCIAL_SERVICE);
        validationKafkaResponseModel.setCustomerInfo_channel_id("web");
        validationKafkaResponseModel.setProductInfo_category_id(17L);
        validationKafkaResponseModel.setProductId(12344L);
        validationKafkaResponseModel.setProductInfo_operator("visa_sbi");
        validationKafkaResponseModel.setProductInfo_paytype(Constants.CREDIT_CARD_PAYTYPE);
        validationKafkaResponseModel.setProductInfo_verticalId(1);
        validationKafkaResponseModel.setCustomerInfo_customer_id(1L);
        validationKafkaResponseModel.setUserData_amount("10.0");
        validationKafkaResponseModel.setUserData_recharge_number("XXXX XXXX XXXX 1339");
        validationKafkaResponseModel.setValidationSuccessful(true);
        ValidationResponseMetaModel vm = new ValidationResponseMetaModel();
        vm.setCin("1234");
        validationKafkaResponseModel.setMetaData(vm);
        validationKafkaResponseModel.setCustomerDataResponse(new CustomerDataResponseModel());
        return validationKafkaResponseModel;
    }

    @Test
    public void testIsSkipableTrueChannelId() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setCustomerInfo_channel_id("digital-reminder");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }


    @Test
    public void testIsSkipableTrueCustomerId() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setCustomerInfo_customer_id(0L);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableTrueCategoryId() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(false), res);
    }


    @Test
    public void testIsSkipableTrueValidationFailure() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setValidationSuccessful(false);
        validationKafkaResponseModel.setRecGw_operatorResCode("BROO1");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableTrueValidationStateNull() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setValidationSuccessful(null);
        validationKafkaResponseModel.setRecGw_operatorResCode("BROO1");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableTrueValidationStateUsedCachedDataNull() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setValidationSuccessful(null);
        validationKafkaResponseModel.setUseRedisData(null);
        validationKafkaResponseModel.setRecGw_operatorResCode("BROO1");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableTrueValidationStateFalseUseCachedDataNull() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setValidationSuccessful(false);
        validationKafkaResponseModel.setUseRedisData(null);
        validationKafkaResponseModel.setRecGw_operatorResCode("BROO1");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableFalseValidationStateTrueUseCachedDataNull() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setValidationSuccessful(true);
        validationKafkaResponseModel.setUseRedisData(null);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(false), res);
    }

    @Test
    public void testIsSkipableFalseValidationStateNullUseCachedDataUsed() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setValidationSuccessful(null);
        validationKafkaResponseModel.setUseRedisData(true);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(false), res);
    }

    @Test
    public void testIsSkipableFalsePrepaid() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setUserData_amount("10.0");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(false), res);
    }


    @Test
    public void testIsSkipableTruePrepaid() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setValidationSuccessful(null);
        validationKafkaResponseModel.setUserData_amount("0");
        validationKafkaResponseModel.setRecGw_operatorResCode("BROO1");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableFalsePostpaid() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_paytype("postpaid");
        validationKafkaResponseModel.getCustomerDataResponse().setCurrentBillAmount("10");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(false), res);
    }


    @Test
    public void testIsSkipableTruePostpaid() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getCustomerDataResponse().setCurrentBillAmount("0");
        validationKafkaResponseModel.setProductInfo_paytype("postpaid");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }


    @Test
    public void testIsSkipableTrueCylinder() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_service("cylinder booking");
        validationKafkaResponseModel.getCustomerDataResponse().setFlowType("100");

        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);
    }


    @Test
    public void testInsertIntoCassandra() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setMetaData(null);
        DCATGetPlanResponse d = getDCATPLanResponseMock();
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(validationKafkaResponseModel.getProductInfo_service().toLowerCase());


        when(rpsService.getPlanDetails(DCATcategoryId, Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, validationKafkaResponseModel.getProductInfo_operator(), "10", validationKafkaResponseModel.getProductInfo_circle(), validationKafkaResponseModel.getProductId().toString(),validationKafkaResponseModel.getUserData_recharge_number(),validationKafkaResponseModel.getProductInfo_service()))
                .thenReturn(d);

        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("Top Up", argument.getValue().getBillsObj().get("plan_bucket"));
        assertEquals("10", argument.getValue().getDisplayValues().get("talktime"));
        assertEquals("VALIDATION", argument.getValue().getEventType());
        assertEquals("10.0", argument.getValue().getAmount());

    }

    @Test
    public void testInsertIntoCassandraWithoutPlan() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();

        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("Special", argument.getValue().getBillsObj().get("plan_bucket"));
        assertEquals("1", argument.getValue().getDisplayValues().get("talktime"));
        assertEquals("VALIDATION", argument.getValue().getEventType());
        assertEquals("10.0", argument.getValue().getAmount());

    }

    @Test
    public void testInsertIntoCassandraWithSkip() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setCustomerInfo_channel_id("digital-reminder");


        DCATGetPlanResponse d = getDCATPLanResponseMock();
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(validationKafkaResponseModel.getProductInfo_service().toLowerCase());


        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper , times(0)).insertDropOff(argument.capture());

    }

    @Test
    public void testInsertIntoCassandraWithDisplayValueConsumerName() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getDisplayValues().add(new ValidationResponseDisplayValues("Consumer Name", "ABC"));

        validationKafkaResponseModel.getCustomerDataResponse().setCustomerName(null);
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("Special", argument.getValue().getBillsObj().get("plan_bucket"));
        assertEquals("1", argument.getValue().getDisplayValues().get("talktime"));
        assertEquals("ABC", argument.getValue().getBillsObj().get("consumerName"));
        assertEquals("VALIDATION", argument.getValue().getEventType());
        assertEquals("10.0", argument.getValue().getAmount());

    }

    @Test
    public void testInsertIntoCassandraWithCustomerDateResponseConsumerName() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getDisplayValues().add(new ValidationResponseDisplayValues("Consumer Name", "ABC"));

        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("Special", argument.getValue().getBillsObj().get("plan_bucket"));
        assertEquals("1", argument.getValue().getDisplayValues().get("talktime"));
        assertEquals("C", argument.getValue().getBillsObj().get("consumerName"));
        assertEquals("VALIDATION", argument.getValue().getEventType());
        assertEquals("10.0", argument.getValue().getAmount());

    }

    @Test
    public void testInsertIntoCassandraWithEmptyMeta() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getDisplayValues().add(new ValidationResponseDisplayValues("Consumer Name", "ABC"));
        validationKafkaResponseModel.setMetaData(null);
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        DCATGetPlanResponse d = getDCATPLanResponseMock();
        String DCATcategoryId = Constants.CommonConstants.DCAT_CATEGORY_MAP.get(validationKafkaResponseModel.getProductInfo_service().toLowerCase());


        when(rpsService.getPlanDetails(DCATcategoryId, Constants.CommonConstants.DCAT_VERSION, Constants.CommonConstants.DCAT_CHANNEL, validationKafkaResponseModel.getProductInfo_operator(), "10", validationKafkaResponseModel.getProductInfo_circle(), validationKafkaResponseModel.getProductId().toString(),validationKafkaResponseModel.getUserData_recharge_number(),validationKafkaResponseModel.getProductInfo_service()))
                .thenReturn(d);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("Top Up", argument.getValue().getBillsObj().get("plan_bucket"));
        assertEquals("10", argument.getValue().getDisplayValues().get("talktime"));
        assertEquals("C", argument.getValue().getBillsObj().get("consumerName"));
        assertEquals("VALIDATION", argument.getValue().getEventType());
        assertEquals("10.0", argument.getValue().getAmount());

/*
        DCATGetPlanResponse d = new DCATGetPlanResponse();
        d.setPlan_bucket("Top Up");
        d.setAddon_benefit1("A");
        d.setAddon_benefit2("B");
        d.setAddon_benefit3("C");
        d.setAddon_benefit4("D");
        d.setSms("1");
        d.setTalktime("1");
        d.setDescription("Data");
        d.setValidity("R");
        d.setTalktime("10");
*/

    }

    @Test
    public void testInsertIntoCassandraWithMeta() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getDisplayValues().add(new ValidationResponseDisplayValues("Consumer Name", "ABC"));

        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("Special", argument.getValue().getBillsObj().get("plan_bucket"));
        assertEquals("AAA", argument.getValue().getDisplayValues().get("description"));
        assertEquals("C", argument.getValue().getBillsObj().get("consumerName"));
        assertEquals("VALIDATION", argument.getValue().getEventType());
        assertEquals("10.0", argument.getValue().getAmount());

    }

    @Test
    public void testlistenDefaultValidation() {

        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();

        String data = "{\"catalogProductID\":63795480,\"originalPid\":63795480,\"serverId\":\"ffrbluenode_billpayments_v1_44_125_14885\",\"currentGw\":\"pspcl\",\"validationSuccessful\":true,\"connectionError\":null,\"noBill\":null,\"routeToGwForValidation\":true,\"useRedisData\":null,\"publishToCluster\":null,\"gwMachineId\":\"docker_utilcontainerisation_billpayments_v1_74_159\",\"dueAmount\":\"100\",\"currentTime\":\"2021-02-08T12:23:23.270Z\",\"rechargeGwResponse_operatorResponseCode\":\"S0002\",\"userData_price\":100,\"userData_recharge_number\":\"9896464045\",\"userData_amount\":129,\"userData_recharge_number_length\":10,\"userData_recharge_number_2_length\":0,\"productInfo_operator\":\"airtel\",\"productInfo_circle\":\"delhi ncr\",\"productInfo_service\":\"electricity\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"\",\"productInfo_validationtimeout\":120000,\"productInfo_category_id\":26,\"productInfo_brand\":\"Punjab State Power Corporation Ltd. (PSPCL)\",\"productInfo_merchant_id\":136155,\"productInfo_verticalId\":76,\"customerInfo_channel_id\":\"BOU 1\",\"customerInfo_customer_id\":\"1000302244\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_firstname\":\"\",\"customerInfo_customer_mobile\":5023489872,\"customerInfo_remote_ip\":\"************\",\"timestamps_init\":\"2021-03-04T12:23:22.709Z\",\"timestamps_current\":\"2021-02-08T12:23:22.709Z\",\"customerDataResponse\":{\"customerId\":\"1000302244\",\"subscriberNumber\":\"2001771391\",\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":\"100\",\"billDueDate\":\"30-07-2021\",\"billNumber\":\"001336\",\"incentiveAmount\":\"5600\",\"totalAmount\":\"5712\",\"incentiveDate\":\"30-07-2021\",\"nextBillFetchDate\":\"2021-07-31\",\"billDate\":\"15-07-2021\"}}";

        List<String> messages = new ArrayList<String>();
        messages.add(data);
        validationKafkaResponseModel = JsonUtils.parseJson(data, ValidationKafkaResponseModel.class);
        validationlistener.listen_DEFAULT_VALIDATION(messages, acknowledgment);

        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);

        verify(dropOffDBHelper).insertDropOff(argument.capture());
        assertEquals(validationKafkaResponseModel.getUserData_recharge_number(), argument.getValue().getRechargeNumber());


    }
    
    
    @Test
    public void testInsertIntoCassandraCCNewUserWithMeta() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        
        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument1 = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument1.capture());

        assertEquals("XXXX XXXX XXXX 1339", argument1.getValue().getBillsObj().get("mcn"));
        assertEquals("1234", argument1.getValue().getBillsObj().get("cin"));
        assertEquals("1234", argument1.getValue().getRechargeNumber());
        assertEquals("VALIDATION", argument1.getValue().getEventType());
        assertEquals("10.0", argument1.getValue().getAmount());
    }
    
    @Test
    public void testInsertIntoCassandraCCExistingUserWithMeta() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        ResponsePage<ChannelHistory> responsePage=new ResponsePage<ChannelHistory>(1, new ArrayList<ChannelHistory>(), null, false);
        //when(channelHistoryService.getPageOfHistory(validationKafkaResponseModel.getCustomerInfo_customer_id(), validationKafkaResponseModel.getMetaData().getCin(), validationKafkaResponseModel.getProductInfo_service(), 1, null)).thenReturn(responsePage);
        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument1 = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument1.capture());

        assertEquals("XXXX XXXX XXXX 1339", argument1.getValue().getBillsObj().get("mcn"));
        assertEquals("1234", argument1.getValue().getBillsObj().get("cin"));
        assertEquals("1234", argument1.getValue().getRechargeNumber());
        assertEquals("VALIDATION", argument1.getValue().getEventType());
        assertEquals("10.0", argument1.getValue().getAmount());
        assertEquals(null, argument1.getValue().getBillsObj().get("newUser"));
    }

    @Test
    public void testInsertIntoCassandraCCExistingUserWithPar() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        validationKafkaResponseModel.setUserData_recharge_number_4("1ab234");
        validationKafkaResponseModel.getMetaData().setCin(null);
        validationKafkaResponseModel.getMetaData().setPar("1ab234");

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument1 = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument1.capture());

        assertEquals("XXXX XXXX XXXX 1339", argument1.getValue().getBillsObj().get("mcn"));
        assertEquals("1ab234", argument1.getValue().getBillsObj().get("par"));
        assertEquals("1ab234", argument1.getValue().getRechargeNumber());
        assertEquals("VALIDATION", argument1.getValue().getEventType());
        assertEquals("10.0", argument1.getValue().getAmount());
        assertEquals(null, argument1.getValue().getBillsObj().get("newUser"));
    }

    @Test
    public void testInsertIntoCassandraCCExistingUserWithCinInRecharge_number_2() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        validationKafkaResponseModel.setUserData_recharge_number_2("1ab234_CIN");
        validationKafkaResponseModel.getMetaData().setCin("1ab234");

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument1 = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument1.capture());

        assertEquals("XXXX XXXX XXXX 1339", argument1.getValue().getBillsObj().get("mcn"));
        assertEquals("1ab234", argument1.getValue().getBillsObj().get("cin"));
        assertEquals("1ab234", argument1.getValue().getRechargeNumber());
        assertEquals("VALIDATION", argument1.getValue().getEventType());
        assertEquals("10.0", argument1.getValue().getAmount());
        assertEquals(null, argument1.getValue().getBillsObj().get("newUser"));
    }

    @Test
    public void testInsertIntoCassandraCCExistingUserWithCinInRecharge_number_2WithWrongFormat() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        validationKafkaResponseModel.setUserData_recharge_number_2("1ab234-CIN");
        validationKafkaResponseModel.getMetaData().setCin(null);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument1 = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper, Mockito.times(0)).insertDropOff(argument1.capture());

    }

    @Test
    public void testInsertIntoCassandraCCExistingUserWithCinInRecharge_number_3() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        validationKafkaResponseModel.setUserData_recharge_number_3("1ab234");
        validationKafkaResponseModel.getMetaData().setCin("1ab234");
        validationKafkaResponseModel.getMetaData().setPar(null);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument1 = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument1.capture());

        assertEquals("XXXX XXXX XXXX 1339", argument1.getValue().getBillsObj().get("mcn"));
        assertEquals("1ab234", argument1.getValue().getBillsObj().get("cin"));
        assertEquals("1ab234", argument1.getValue().getRechargeNumber());
        assertEquals("VALIDATION", argument1.getValue().getEventType());
        assertEquals("10.0", argument1.getValue().getAmount());
        assertEquals(null, argument1.getValue().getBillsObj().get("newUser"));
    }

    @Test
    public void testInsertIntoCassandraCCExistingUserWithoutParAndCin() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMockForCC();
        validationKafkaResponseModel.setUserData_recharge_number_2("21132311111");
        validationKafkaResponseModel.setUserData_recharge_number_3(null);
        validationKafkaResponseModel.setUserData_recharge_number_4(null);
        validationKafkaResponseModel.getMetaData().setCin(null);
        validationKafkaResponseModel.getMetaData().setPar(null);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument1 = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper, Mockito.times(0)).insertDropOff(argument1.capture());

    }

    @Test
    public void testIsShippableFalseForRent() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(262072L);
        validationKafkaResponseModel.setProductInfo_service("Rent Payment");
        validationKafkaResponseModel.setProductInfo_paytype("Postpaid");
        CustomerDataResponseModel cust = new CustomerDataResponseModel();
        cust.setCurrentBillAmount("34");
        validationKafkaResponseModel.setCustomerDataResponse(cust);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper, Mockito.times(0)).insertDropOff(argument.capture());

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsShippableFalseForTuition() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(262072L);
        validationKafkaResponseModel.setProductInfo_service("Tuition Fees");
        validationKafkaResponseModel.setProductInfo_paytype("Postpaid");
        CustomerDataResponseModel cust = new CustomerDataResponseModel();
        cust.setCurrentBillAmount("34");
        validationKafkaResponseModel.setCustomerDataResponse(cust);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper, Mockito.times(0)).insertDropOff(argument.capture());

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testlistenRechargeValidation() {

        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();

        String data = "{\"catalogProductID\":63795480,\"originalPid\":63795480,\"serverId\":\"ffrbluenode_billpayments_v1_44_125_14885\",\"currentGw\":\"pspcl\",\"validationSuccessful\":true,\"connectionError\":null,\"noBill\":null,\"routeToGwForValidation\":true,\"useRedisData\":null,\"publishToCluster\":null,\"gwMachineId\":\"docker_utilcontainerisation_billpayments_v1_74_159\",\"dueAmount\":\"100\",\"currentTime\":\"2021-02-08T12:23:23.270Z\",\"rechargeGwResponse_operatorResponseCode\":\"S0002\",\"userData_price\":100,\"userData_recharge_number\":\"9896464045\",\"userData_amount\":129,\"userData_recharge_number_length\":10,\"userData_recharge_number_2_length\":0,\"productInfo_operator\":\"airtel\",\"productInfo_circle\":\"delhi ncr\",\"productInfo_service\":\"mobile\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"\",\"productInfo_validationtimeout\":120000,\"productInfo_category_id\":26,\"productInfo_brand\":\"Punjab State Power Corporation Ltd. (PSPCL)\",\"productInfo_merchant_id\":136155,\"productInfo_verticalId\":76,\"customerInfo_channel_id\":\"BOU 1\",\"customerInfo_customer_id\":\"1000302244\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_firstname\":\"\",\"customerInfo_customer_mobile\":5023489872,\"customerInfo_remote_ip\":\"************\",\"timestamps_init\":\"2021-03-04T12:23:22.709Z\",\"timestamps_current\":\"2021-02-08T12:23:22.709Z\",\"customerDataResponse\":{\"customerId\":\"1000302244\",\"subscriberNumber\":\"2001771391\",\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":\"100\",\"billDueDate\":\"30-07-2021\",\"billNumber\":\"001336\",\"incentiveAmount\":\"5600\",\"totalAmount\":\"5712\",\"incentiveDate\":\"30-07-2021\",\"nextBillFetchDate\":\"2021-07-31\",\"billDate\":\"15-07-2021\"}}";

        List<String> messages = new ArrayList<String>();
        messages.add(data);
        validationKafkaResponseModel = JsonUtils.parseJson(data, ValidationKafkaResponseModel.class);
        validationlistener.listen_RECHARGE_VALIDATION(messages, acknowledgment);

        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());
        assertEquals(validationKafkaResponseModel.getUserData_recharge_number(), argument.getValue().getRechargeNumber());


    }

    @Test
    public void testlisten_UTILITY_ELECTRICITY_VALIDATION() {

        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();

        String data = "{\"catalogProductID\":63795480,\"originalPid\":63795480,\"serverId\":\"ffrbluenode_billpayments_v1_44_125_14885\",\"currentGw\":\"pspcl\",\"validationSuccessful\":true,\"connectionError\":null,\"noBill\":null,\"routeToGwForValidation\":true,\"useRedisData\":null,\"publishToCluster\":null,\"gwMachineId\":\"docker_utilcontainerisation_billpayments_v1_74_159\",\"dueAmount\":\"100\",\"currentTime\":\"2021-02-08T12:23:23.270Z\",\"rechargeGwResponse_operatorResponseCode\":\"S0002\",\"userData_price\":100,\"userData_recharge_number\":\"9896464045\",\"userData_amount\":129,\"userData_recharge_number_length\":10,\"userData_recharge_number_2_length\":0,\"productInfo_operator\":\"airtel\",\"productInfo_circle\":\"delhi ncr\",\"productInfo_service\":\"mobile\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"\",\"productInfo_validationtimeout\":120000,\"productInfo_category_id\":26,\"productInfo_brand\":\"Punjab State Power Corporation Ltd. (PSPCL)\",\"productInfo_merchant_id\":136155,\"productInfo_verticalId\":76,\"customerInfo_channel_id\":\"BOU 1\",\"customerInfo_customer_id\":\"1000302244\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_firstname\":\"\",\"customerInfo_customer_mobile\":5023489872,\"customerInfo_remote_ip\":\"************\",\"timestamps_init\":\"2021-03-04T12:23:22.709Z\",\"timestamps_current\":\"2021-02-08T12:23:22.709Z\",\"customerDataResponse\":{\"customerId\":\"1000302244\",\"subscriberNumber\":\"2001771391\",\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":\"100\",\"billDueDate\":\"30-07-2021\",\"billNumber\":\"001336\",\"incentiveAmount\":\"5600\",\"totalAmount\":\"5712\",\"incentiveDate\":\"30-07-2021\",\"nextBillFetchDate\":\"2021-07-31\",\"billDate\":\"15-07-2021\"}}";

        List<String> messages = new ArrayList<String>();
        messages.add(data);
        validationKafkaResponseModel = JsonUtils.parseJson(data, ValidationKafkaResponseModel.class);
        validationlistener.listen_UTILITY_ELECTRICITY_VALIDATION(messages, acknowledgment);

        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);

        verify(dropOffDBHelper).insertDropOff(argument.capture());
        assertEquals(validationKafkaResponseModel.getUserData_recharge_number(), argument.getValue().getRechargeNumber());


    }

    @Test
    public void testInsertIntoCassandraWithBillAmount() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setDueAmount("500");
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("500", argument.getValue().getAmount());

    }

    @Test
    public void testInsertIntoCassandraWithUserDataAmount() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("10.0", argument.getValue().getAmount());

    }

    @Test
    public void testInsertIntoCassandraWithMutipleRMN() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getMetaData().getAdditionalPayload().setMultipleRMN(true);
        validationKafkaResponseModel.setProductInfo_service("dth");
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        assertEquals(new Boolean(true), res);

    }

    @Test
    public void testInsertIntoCassandraWithUserIntentWithoutCurrentAmount() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getMetaData().setUserIntent(1);
        validationKafkaResponseModel.setProductInfo_service("dth");
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);
        assertEquals(null, d1.getAmount());

    }

    @Test
    public void testInsertIntoCassandraWithUserIntentWithCurrentAmount() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getMetaData().setUserIntent(1);
        validationKafkaResponseModel.setProductInfo_service("dth");
        validationKafkaResponseModel.setDueAmount("499");
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("499", argument.getValue().getAmount());

    }
    
    @Test
    public void testInsertIntoCassandraCylinderAgentName() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getMetaData().setCylinder_agency_name("Agency Name");
        validationKafkaResponseModel.setProductInfo_service(CommonConstants.CYLINDER_SERVICE);
        validationKafkaResponseModel.setDueAmount("499");
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("Agency Name", argument.getValue().getBillsObj().get(Constants.CYLINDER_AGENCY_NAME));

    }
    
    @Test
    public void testInsertIntoCassandraCylinderNoAgentName() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        //validationKafkaResponseModel.getMetaData().setCylinder_agency_name("Agency Name");
        validationKafkaResponseModel.setProductInfo_service(CommonConstants.CYLINDER_SERVICE);
        validationKafkaResponseModel.setDueAmount("499");
        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertNull(argument.getValue().getBillsObj().get(Constants.CYLINDER_AGENCY_NAME));

    }


    @Test
    public void testInsertIntoCassandraWithMetaRNM() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getDisplayValues().add(new ValidationResponseDisplayValues("Consumer Name", "ABC"));
        validationKafkaResponseModel.getMetaData().setRMN("111111");
        validationKafkaResponseModel.setProductInfo_service("dth");

        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("Special", argument.getValue().getBillsObj().get("plan_bucket"));
        assertEquals("AAA", argument.getValue().getDisplayValues().get("description"));
        assertEquals("C", argument.getValue().getBillsObj().get("consumerName"));
        assertEquals("VALIDATION", argument.getValue().getEventType());
        assertEquals("10.0", argument.getValue().getAmount());
        assertEquals("111111", argument.getValue().getBillsObj().get("RMN"));

    }
    
    
    @Test
    public void testInsertIntoCassandraDTHDefaultBucket() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getDisplayValues().add(new ValidationResponseDisplayValues("Consumer Name", "ABC"));
        validationKafkaResponseModel.getMetaData().setRMN("111111");
        validationKafkaResponseModel.setProductInfo_service("dth");
        validationKafkaResponseModel.setProductInfo_operator("dishtv");
        validationKafkaResponseModel.setMetaData(null);

        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("dthDefaultBucket", argument.getValue().getBillsObj().get("plan_bucket"));

    }
    
    @Test
    public void testInsertIntoCassandraDTHDefaultBucketSuntv() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.getDisplayValues().add(new ValidationResponseDisplayValues("Consumer Name", "ABC"));
        validationKafkaResponseModel.getMetaData().setRMN("111111");
        validationKafkaResponseModel.setProductInfo_service("dth");
        validationKafkaResponseModel.setProductInfo_operator("suntv");
        validationKafkaResponseModel.setMetaData(null);

        DropOff d1 = validationlistener.prepareDropOffData(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("dthDefaultBucket", argument.getValue().getBillsObj().get("plan_bucket"));

    }
    private ValidationKafkaResponseModel mockValidationKafkaResponseModel() {
    	String data="{\"catalogProductID\":1200137283,\"originalPid\":1200137283,\"serverId\":\"ffrbluenode_billpayments_v1_44_125_9009\",\"currentGw\":\"mpeznonrapdrpnew\",\"validationSuccessful\":true,\"connectionError\":null,\"noBill\":null,\"routeToGwForValidation\":true,\"useRedisData\":null,\"publishToCluster\":null,\"gwMachineId\":\"docker_utilcontainerisation_billpayments_v1_74_159\",\"dueAmount\":\"558\",\"currentTime\":\"2021-02-24T09:51:20.938Z\",\"rechargeGwResponse_operatorResponseCode\":\"DATA FOUND\",\"userData_price\":558,\"userData_recharge_number\":\"********\",\"userData_amount\":558,\"userData_recharge_number_length\":8,\"userData_recharge_number_2_length\":0,\"productInfo_operator\":\"mastercard_rblbank\",\"productInfo_circle\":\"\",\"productInfo_service\":\"financial services\",\"productInfo_paytype\":\"credit card\",\"productInfo_producttype\":\"\",\"productInfo_validationtimeout\":120000,\"productInfo_category_id\":156705,\"productInfo_brand\":\"MP Poorv Kshetra Vidyut Vitaran - Jabalpur\",\"productInfo_merchant_id\":267095,\"productInfo_verticalId\":56,\"customerInfo_channel_id\":\"WEB 2\",\"customerInfo_customer_id\":\"**********\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_firstname\":\"\",\"customerInfo_customer_mobile\":**********,\"customerInfo_remote_ip\":\"************\",\"timestamps_init\":\"2021-02-24T09:51:20.594Z\",\"timestamps_current\":\"2021-02-24T09:51:20.594Z\",\"customerDataResponse\":{\"customerId\":\"**********\",\"subscriberNumber\":\"********\",\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":\"558.0\",\"billDueDate\":\"15-09-2017\",\"billDate\":\"31-08-2017\",\"customerName\":\"JODHAN / VISHRAM SINGH LODHI\",\"address\":\"BANDA BANDRI ROAD,\"},\"metaData\":{\"due_amount\":\"558\",\"cin\":\"13\"},\"displayValues\":[{\"label\":\"Consumer Id\",\"value\":\"554602237176\"},{\"label\":\"Consumer Name\",\"value\":\"JODHAN / VISHRAM SINGH LODHI\"},{\"label\":\"Due Date\",\"value\":\"15-09-2017\"},{\"label\":\"Amount\",\"value\":\"558.0\"}]}";
    	ValidationKafkaResponseModel validationKafkaResponseModel=JsonUtils.parseJson(data, ValidationKafkaResponseModel.class);
    	return validationKafkaResponseModel;
    }
    @Test
    public void testIsSkipableFalseForInsurance() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(64739L);
        validationKafkaResponseModel.setProductInfo_service("insurance");
        validationKafkaResponseModel.setProductInfo_paytype("insurance");
        CustomerDataResponseModel cust = new CustomerDataResponseModel();
        cust.setCurrentBillAmount("34");
        validationKafkaResponseModel.setCustomerDataResponse(cust);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument.capture());

        assertEquals(new Boolean(false), res);
    }
    @Test
    public void testIsSkipableFalseForLoan() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(198239L);
        validationKafkaResponseModel.setProductInfo_service("loan");
        validationKafkaResponseModel.setProductInfo_paytype("loan");
        CustomerDataResponseModel cust = new CustomerDataResponseModel();
        cust.setCurrentBillAmount("34");
        validationKafkaResponseModel.setCustomerDataResponse(cust);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument.capture());

        assertEquals(new Boolean(false), res);
    }
    @Test
    public void testIsSkipableFalseForChallan() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(104154L);
        validationKafkaResponseModel.setProductInfo_service("challan");
        validationKafkaResponseModel.setProductInfo_paytype("postpaid");
        CustomerDataResponseModel cust = new CustomerDataResponseModel();
        cust.setCurrentBillAmount("34");
        validationKafkaResponseModel.setCustomerDataResponse(cust);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument.capture());

        assertEquals(new Boolean(false), res);
    }
    @Test
    public void testIsSkipableFalseForCableTv() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(123988L);
        validationKafkaResponseModel.setProductInfo_service("cable tv");
        validationKafkaResponseModel.setProductInfo_paytype("postpaid");
        CustomerDataResponseModel cust = new CustomerDataResponseModel();
        cust.setCurrentBillAmount("34");
        validationKafkaResponseModel.setCustomerDataResponse(cust);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument.capture());

        assertEquals(new Boolean(false), res);
    }
    @Test
    public void testIsSkipableFalseForApt() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(101950L);
        validationKafkaResponseModel.setProductInfo_service("apartments");
        validationKafkaResponseModel.setProductInfo_paytype("prepaid");
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument.capture());

        assertEquals(new Boolean(false), res);
    }
    @Test
    public void testIsSkipableFalseForEdu() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(46007L);
        validationKafkaResponseModel.setProductInfo_service("education");
        validationKafkaResponseModel.setProductInfo_paytype("fee payment");
        CustomerDataResponseModel cust = new CustomerDataResponseModel();
        cust.setCurrentBillAmount("34");
        validationKafkaResponseModel.setCustomerDataResponse(cust);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument.capture());

        assertEquals(new Boolean(false), res);
    }
    @Test
    public void testIsSkipableFalseForMunicipal() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        validationKafkaResponseModel.setProductInfo_category_id(107730L);
        validationKafkaResponseModel.setProductInfo_service("municipal payments");
        validationKafkaResponseModel.setProductInfo_paytype("postpaid");
        CustomerDataResponseModel cust = new CustomerDataResponseModel();
        cust.setCurrentBillAmount("34");
        validationKafkaResponseModel.setCustomerDataResponse(cust);
        Boolean res = validationlistener.isSkipable(validationKafkaResponseModel);

        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);
        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);
        verify(dropOffDBHelper,Mockito.times(1)).insertDropOff(argument.capture());

        assertEquals(new Boolean(false), res);
    }

    @Test
    public void testInsertDropOffRpsProductIdNonNull(){
        String message = "{\"catalogProductID\":97040306,\"originalPid\":97040306,\"serverId\":\"ffrbluenode_billpayments_v1_36_127_20072\",\"currentGw\":\"reliancejiov2\",\"validationSuccessful\":true,\"connectionError\":null,\"noBill\":null,\"routeToGwForValidation\":true,\"useRedisData\":null,\"publishToCluster\":null,\"gwMachineId\":\"digitalingwnode_billpayments_v1_35_219\",\"currentTime\":\"2021-10-28T08:16:45.238Z\",\"rechargeGwResponse_operatorResponseCode\":\"\",\"userData_category_id\":\"17\",\"userData_channel_id\":\"b562cdef-b677-4784-ae8b-703a95023bc0\",\"userData_price\":199,\"userData_recharge_number\":\"8287899746\",\"userData_totalamount\":199,\"userData_amount\":199,\"userData_recharge_number_length\":10,\"userData_recharge_number_2_length\":0,\"productInfo_operator\":\"jio\",\"productInfo_circle\":\"delhi ncr\",\"productInfo_service\":\"mobile\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"recharge\",\"productInfo_validationtimeout\":120000,\"productInfo_category_id\":17,\"productInfo_brand\":\"Jio\",\"productInfo_merchant_id\":2,\"productInfo_verticalId\":4,\"customerInfo_channel_id\":\"androidapp 9.0.0\",\"customerInfo_customer_id\":\"23\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_firstname\":\"\",\"customerInfo_customer_mobile\":9873797846,\"customerInfo_remote_ip\":\"*************\",\"timestamps_init\":\"2021-10-28T08:16:45.103Z\",\"timestamps_current\":\"2021-10-28T08:16:45.103Z\",\"billPaymentSource\":\"\",\"customerDataResponse\":{\"customerId\":null,\"subscriberNumber\":null,\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":null,\"billDueDate\":null},\"metaData\":{\"couponDescription\":\"Data: 1.5 GB/day Voice: Unlimited Voice Calls \",\"data\":\"1.5 GB/Day\",\"plan_id\":155673818,\"post_order_view_type\":\"storefront_view\",\"sms\":\"NA\",\"talktime\":\"NA\",\"validity\":\"28 Days\",\"errorMessageCode\":null,\"producttype\":\"Recharge\",\"amount_plan_desc\":\"0\",\"recharge_benefits\":{\"validity\":\"28 Days\",\"talktime\":\"NA\",\"couponDescription\":\"Data: 1.5 GB/day Voice: Unlimited Voice Calls \",\"data\":\"1.5 GB/Day\",\"sms\":\"NA\",\"addon_benefit\":null}},\"displayValues\":[]}";
        ValidationKafkaResponseModel validationKafkaResponseModel = JsonUtils.parseJson(message,ValidationKafkaResponseModel.class);

        String response = "{\"plan_bucket\":null,\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":null}";
        DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);

        when(rpsService.getPlanDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);
        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);

        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);

        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("28 day", argument.getValue().getDisplayValues().get("validity"));
        assertEquals("2.5GB/day",argument.getValue().getDisplayValues().get("data"));
        assertEquals("100/Day",argument.getValue().getDisplayValues().get("sms"));
        assertEquals(null,argument.getValue().getBillsObj().get("plan_bucket"));
    }

    @Test
    public void testInsertDropOffRpsProductIdNull(){
        String message = "{\"catalogProductID\":97040306,\"originalPid\":97040306,\"serverId\":\"ffrbluenode_billpayments_v1_36_127_20072\",\"currentGw\":\"reliancejiov2\",\"validationSuccessful\":true,\"connectionError\":null,\"noBill\":null,\"routeToGwForValidation\":true,\"useRedisData\":null,\"publishToCluster\":null,\"gwMachineId\":\"digitalingwnode_billpayments_v1_35_219\",\"currentTime\":\"2021-10-28T08:16:45.238Z\",\"rechargeGwResponse_operatorResponseCode\":\"\",\"userData_category_id\":\"17\",\"userData_channel_id\":\"b562cdef-b677-4784-ae8b-703a95023bc0\",\"userData_price\":199,\"userData_recharge_number\":\"8287899746\",\"userData_totalamount\":199,\"userData_amount\":199,\"userData_recharge_number_length\":10,\"userData_recharge_number_2_length\":0,\"productInfo_operator\":\"jio\",\"productInfo_circle\":\"delhi ncr\",\"productInfo_service\":\"mobile\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"recharge\",\"productInfo_validationtimeout\":120000,\"productInfo_category_id\":17,\"productInfo_brand\":\"Jio\",\"productInfo_merchant_id\":2,\"productInfo_verticalId\":4,\"customerInfo_channel_id\":\"androidapp 9.0.0\",\"customerInfo_customer_id\":\"23\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_firstname\":\"\",\"customerInfo_customer_mobile\":9873797846,\"customerInfo_remote_ip\":\"*************\",\"timestamps_init\":\"2021-10-28T08:16:45.103Z\",\"timestamps_current\":\"2021-10-28T08:16:45.103Z\",\"billPaymentSource\":\"\",\"customerDataResponse\":{\"customerId\":null,\"subscriberNumber\":null,\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":null,\"billDueDate\":null},\"metaData\":{\"couponDescription\":\"Data: 1.5 GB/day Voice: Unlimited Voice Calls \",\"data\":\"1.5 GB/Day\",\"plan_id\":155673818,\"post_order_view_type\":\"storefront_view\",\"sms\":\"NA\",\"talktime\":\"NA\",\"validity\":\"28 Days\",\"errorMessageCode\":null,\"producttype\":\"Recharge\",\"amount_plan_desc\":\"0\",\"recharge_benefits\":{\"validity\":\"28 Days\",\"talktime\":\"NA\",\"couponDescription\":\"Data: 1.5 GB/day Voice: Unlimited Voice Calls \",\"data\":\"1.5 GB/Day\",\"sms\":\"NA\",\"addon_benefit\":null}},\"displayValues\":[]}";
        ValidationKafkaResponseModel validationKafkaResponseModel = JsonUtils.parseJson(message,ValidationKafkaResponseModel.class);

        String response = "{\"plan_bucket\":\"Special Recharge\",\"data\":\"2.5GB/day\",\"price\":\"399.0\",\"validity\":\"28 day\",\"talktime\":null,\"sms\":\"100/Day\",\"description\":\"Calls : Unlimited Local, STD & Roaming | Data : 2.5GB/Day | SMS : 100/Day | Details: Disney+ Hotstar Mobile subscription for 3 months\",\"addon_benefit\":[\"Disney+ Hotstar Mobile for 3 months\",\"Apollo 24|7 Circle\",\"Get Rs. 100 cashback on FASTag\",\"Free Hellotunes\",\"Wynk Music Free\"],\"addon_benefit1\":\"Disney+ Hotstar Mobile for 3 months\",\"addon_benefit2\":\"Apollo 24|7 Circle\",\"addon_benefit3\":\"Get Rs. 100 cashback on FASTag\",\"addon_benefit4\":\"Free Hellotunes\",\"productId\":33}";
        DCATGetPlanResponse dcatGetPlanResponse = JsonUtils.parseJson(response,DCATGetPlanResponse.class);

        when(rpsService.getPlanDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(dcatGetPlanResponse);
        validationlistener.insertDataIntoCassandra(validationKafkaResponseModel);

        ArgumentCaptor<DropOff> argument = ArgumentCaptor.forClass(DropOff.class);

        verify(dropOffDBHelper).insertDropOff(argument.capture());

        assertEquals("28 day", argument.getValue().getDisplayValues().get("validity"));
        assertEquals("2.5GB/day",argument.getValue().getDisplayValues().get("data"));
        assertEquals("100/Day",argument.getValue().getDisplayValues().get("sms"));
        assertEquals("33",argument.getValue().getBillsObj().get("plan_id"));
        assertEquals("Special Recharge",argument.getValue().getBillsObj().get("plan_bucket"));
    }

    @Test
    public void testValidationBFR001() {
        ValidationKafkaResponseModel validationKafkaResponseModel = getValidationKafkaResponseMock();
        String data = "{\"catalogProductID\":63795480,\"originalPid\":63795480,\"serverId\":\"ffrbluenode_billpayments_v1_44_125_14885\",\"currentGw\":\"pspcl\",\"validationSuccessful\":false,\"connectionError\":null,\"noBill\":null,\"routeToGwForValidation\":true,\"useRedisData\":null,\"publishToCluster\":null,\"gwMachineId\":\"docker_utilcontainerisation_billpayments_v1_74_159\",\"dueAmount\":\"100\",\"currentTime\":\"2021-02-08T12:23:23.270Z\",\"rechargeGwResponse_operatorResponseCode\":\"BFR001\",\"userData_price\":100,\"userData_recharge_number\":\"9896464045\",\"userData_amount\":129,\"userData_recharge_number_length\":10,\"userData_recharge_number_2_length\":0,\"productInfo_operator\":\"airtel\",\"productInfo_circle\":\"delhi ncr\",\"productInfo_service\":\"electricity\",\"productInfo_paytype\":\"prepaid\",\"productInfo_producttype\":\"\",\"productInfo_validationtimeout\":120000,\"productInfo_category_id\":26,\"productInfo_brand\":\"Punjab State Power Corporation Ltd. (PSPCL)\",\"productInfo_merchant_id\":136155,\"productInfo_verticalId\":76,\"customerInfo_channel_id\":\"BOU 1\",\"customerInfo_customer_id\":\"1000302244\",\"customerInfo_customer_email\":\"\",\"customerInfo_customer_firstname\":\"\",\"customerInfo_customer_mobile\":5023489872,\"customerInfo_remote_ip\":\"************\",\"timestamps_init\":\"2021-03-04T12:23:22.709Z\",\"timestamps_current\":\"2021-02-08T12:23:22.709Z\",\"customerDataResponse\":{\"customerId\":\"1000302244\",\"subscriberNumber\":\"2001771391\",\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":\"100\",\"billDueDate\":\"30-07-2021\",\"billNumber\":\"001336\",\"incentiveAmount\":\"5600\",\"totalAmount\":\"5712\",\"incentiveDate\":\"30-07-2021\",\"nextBillFetchDate\":\"2021-07-31\",\"billDate\":\"15-07-2021\"}}";
        List<String> messages = new ArrayList<String>();
        messages.add(data);
        validationKafkaResponseModel = JsonUtils.parseJson(data, ValidationKafkaResponseModel.class);
        validationKafkaResponseModel.setProductInfo_service("electricity");
        validationKafkaResponseModel.setProductInfo_paytype("postpaid");
        //validationKafkaResponseModel.setRecGw_operatorResCode("BFR001");
        validationKafkaResponseModel.setValidationSuccessful(true);
        validationlistener.listen_DEFAULT_VALIDATION(messages, acknowledgment);
        ArgumentCaptor<SmartRecents> argument = ArgumentCaptor.forClass(SmartRecents.class);
        verify(smartRecentsService).save(argument.capture());
        assertEquals(validationKafkaResponseModel.getCustomerInfo_customer_id(), argument.getValue().getKey().getCustomerId());
    }
}
