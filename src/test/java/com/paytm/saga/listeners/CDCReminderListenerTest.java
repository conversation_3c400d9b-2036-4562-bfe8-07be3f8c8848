package com.paytm.saga.listeners;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.CDCReminderListenerException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.CustomerBillDao;
import com.paytm.saga.dto.SMSRecoveryPacket;
import com.paytm.saga.dto.cdc.*;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.Recents;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.service.CommonService;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.service.RecentDataToKafkaService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.AESUtil;
import com.paytm.saga.util.CDCConsumerUtil;
import com.paytm.saga.util.JsonUtils;
import junit.framework.TestCase;
import org.junit.After;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import static com.paytm.saga.common.constant.Constants.FASTAG;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class CDCReminderListenerTest extends TestCase {
    @Mock
    RecentService recentService;
    @Mock
    CommonService commonService;
    @Mock
    CustomerBillRepository customerBillRepository;

    @Mock
    CustomerBillDao customerBillDao;
    @Mock
	RecentsRepositoryWrapperService recentsRepository;
    @Mock
    RecentDataToKafkaService recentDataToKafkaService;
    @Mock
    Acknowledgment acknowledgment;
    @Mock
    MetricsHelper metricsHelper;

    @Mock
    ServiceConfig serviceConfig;
    @Mock
    AESUtil aesUtil;
//    @Mock
//    CDCReminderKafkaConsumer cdcReminderKafkaConsumer;
//
//    @Mock
//    CDCSMSRecoveryKafkaConsumer cdcSMSRecoveryKafkaConsumer;
    @InjectMocks
    CDCReminderListener cdcReminderListener;
    @After
    public void resetMocks(){
        Mockito.reset();
    }

    @Test
    public void testP2pCdcUpdate() throws RechargeSagaBaseException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);

        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
        verify(customerBillDao).save(customerBill.capture(),ttl.capture());
    }
    @Test
    public void testP2pCdcUpdateWhenDueDateOlder() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
    @Test
    public void testP2pCdcUpdateWhenDueAmountZero() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
    @Test
    public void testSmsCdcUpdate() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
        verify(customerBillDao).save(customerBill.capture(),ttl.capture());
    }

    @Test
    public void testSmsCdcUpdateWhenBillPaidEvent() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":null,\"amount\":{\"value\":-1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":null,\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"eventState\\\":\\\"bill_paid\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }


    @Test
    public void testSmsCdcUpdateWhenDueDateOlder() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
    @Test
    public void testSmsCdcUpdateWhenDueAmountZero() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
    @Test
    public void testSmsCdcDeleteForCC(){
        String msg = "{\"ts_ms\":*************,\"op\":\"d\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRechargeCdcListener(recents.capture(),customerId.capture(),service.capture(),rechargeNumber.capture()
                ,operator.capture(),planBucket.capture());
    }
    @Test
    public void testSmsCdcDeleteForCCWithEncryption(){
        String msg = "{\"ts_ms\":*************,\"op\":\"d\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":********,\"service\":\"financial services\",\"recharge_number\":\"XXXX XXXX XXXX 1234\",\"operator\":\"hdfc\",\"plan_bucket\":\"\",\"notification_status\":1,\"status\":4,\"bill_date\":*************,\"due_amount\":1078.0,\"due_date\":*************,\"paytype\":\"credit card\",\"product_id\":*********,\"update_at\":*************,\"create_at\":*************,\"card_network\":\"dummyNetwork\",\"bank_name\":\"HDFC\",\"extra\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"sms\\\",\\\"updated_source\\\":\\\"sms\\\"}\",\"event_source\":\"sms\",\"customer_other_info\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"is_encrypted\":1,\"remindLaterDate\":*************,\"remindLaterFlow\":true,\"billFetchDate\":*************,\"oldBillFetchDate\":\"2023-02-18T12:34:56Z\",\"recon_id\":\"abc123\",\"payment_date\":*************}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
        verify(recentsRepository).deleteRecentByCustomerIdAndServiceAndRechargeCdcListener(recents.capture(),customerId.capture(),service.capture(),rechargeNumber.capture()
            ,operator.capture(),planBucket.capture());
    }
    @Test
    public void testSmsCdcDeleteForCC1(){
        String msg = "{\"ts_ms\":*************,\"op\":\"d\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
        verify(recentsRepository,times(1)).deleteRecentByCustomerIdAndServiceAndRechargeCdcListener(recents.capture(),customerId.capture(),service.capture(),rechargeNumber.capture()
                ,operator.capture(),planBucket.capture());
    }
    @Test
    public void testSmsCdcDeleteForCC2(){
        String msg = "{\"ts_ms\":*************,\"op\":\"d\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
        verify(recentsRepository,times(0)).deleteRecentByCustomerIdAndServiceAndRechargeCdcListener(recents.capture(),customerId.capture(),service.capture(),rechargeNumber.capture()
                ,operator.capture(),planBucket.capture());
    }
    @Test
    public void testSmsCdcDeleteForNonCC(){
        String msg = "{\"ts_ms\":*************,\"op\":\"d\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"345234524234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<Recents> recents = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucket = ArgumentCaptor.forClass(String.class);
        verify(recentsRepository,times(0)).deleteRecentByCustomerIdAndServiceAndRechargeCdcListener(recents.capture(),customerId.capture(),service.capture(),rechargeNumber.capture()
                ,operator.capture(),planBucket.capture());
    }
    @Test
    public void testIsSkippableWhenMarkedAsPaid() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":15,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(0)).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
    @Test
    public void testIsSkippableWhenNotificationStatusZero() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":0,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":15,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(0)).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
    @Test
    public void testIsSkippableWhenDeleteForSmsMobile() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"d\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":0,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":15,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(0)).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
//    @Test
//    public void testIsSkippableWhenInsetForSmsWithNullCreateAt() throws CDCReminderListenerException {
//        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
//        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
//        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
//        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
//        verify(recentService,times(0)).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
//    }
    @Test
    public void testIsSkippableWhenInsertForP2pWithNullTxnTimeAndCreatedAt() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(0)).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
    @Test
    public void testIsSkippableWhenInsertForP2pWithNullTxnTime() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }
    @Test
    public void testIsSkippableWhenInsertForP2pWithNullCreatedAt() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }



    @Test
    public void testIsSkippableWhenValidationSyncDeleteOp() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"d\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"9876543210\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(0)).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }

    @Test
    public void testIsSkippableWhenValidationSyncInsert() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"9876543210\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(0)).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
    }

    @Test
    public void testValidationCdcUpdate() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"9876543210\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
        verify(customerBillDao).save(customerBill.capture(),ttl.capture());
    }

    @Test
    public void testValidationIsEligibleForCustomerBills() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"mobile\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"9876543210\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"jio\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"updated_source\\\":\\\"validationSync\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"validation\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        Boolean isEligibleForCustomerBills = cdcReminderListener.checkValidBillForCustomerBills(reminderCDC);
        assertTrue(isEligibleForCustomerBills);
    }

    @Test
    public void testCustomerBillsWhendueDateIsNullAmountIsNull() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        // when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        Mockito.verifyNoInteractions(customerBillDao);
    }
    @Test
    public void testCustomerBillsWhendueDateIsPastAmountIsNull() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        verify(customerBillDao).save(customerBill.capture(),ttl.capture());
    }
    @Test
    public void testCustomerBillsWhendueDateIsFutureAmountIsNull() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        verify(customerBillDao).save(customerBill.capture(),ttl.capture());
    }
    @Test
    public void testCustomerBillsWhendueDateIsNullAmountIsZero() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":0,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        // when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        Mockito.verifyNoInteractions(customerBillDao);
    }
    @Test
    public void testCustomerBillsWhendueDateIsPastAmountIsZero() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        // when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        Mockito.verifyNoInteractions(customerBillDao);
    }
    @Test
    public void testCustomerBillsWhendueDateIsFutureAmountIsZero() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        // when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        Mockito.verifyNoInteractions(customerBillDao);
    }
    @Test
    public void testCustomerBillsWhendueDateIsNullAmountIsValid() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":100,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        // when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        Mockito.verifyNoInteractions(customerBillDao);
    }
    @Test
    public void testCustomerBillsWhendueDateIsPastAmountIsValid() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":100,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        verify(customerBillDao).save(customerBill.capture(),ttl.capture());
    }
    @Test
    public void testCustomerBillsWhendueDateIsFutureAmountIsValid() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"u\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"financial services\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":100,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"credit card\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
        verify(customerBillDao).save(customerBill.capture(),ttl.capture());
    }

    @Test
    public void testSmsCdcUpdateFastLowBalance() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"fastag recharge\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"type\\\":\\\"LOW_BALANCE\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCDCArgumentCaptor = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(1)).updateRecentFromReminderCdcConsumer(reminderCDCArgumentCaptor.capture());
        Assert.assertEquals(reminderCDCArgumentCaptor.getValue().getAfter().getOperator().getValue(),Constants.FASTAG_LOW_BALANCE_OPERATOR);
        Assert.assertNotNull(reminderCDCArgumentCaptor.getValue().getAfter().getDueDate().getValue());
    }

    @Test
    public void testSmsCdcUpdateFastLowBalanceSkipOldEvent() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"fastag recharge\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"type\\\":\\\"LOW_BALANCE\\\",\\\"low_balance_date\\\":\\\"2024-06-04 12:07:32\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCDCArgumentCaptor = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(0)).updateRecentFromReminderCdcConsumer(reminderCDCArgumentCaptor.capture());
    }

    @Test
    public void testSmsCdcUpdateFastLowBalanceNegativeAmount() throws CDCReminderListenerException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"fastag recharge\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"XXXX XXXX XXXX 1234\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"hdfc\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"amount\":{\"value\":-10.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":null,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"prepaid\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":*********,\"deletion_ts\":null,\"set\":true},\"update_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"create_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"card_network\":{\"value\":\"dummyNetwork\",\"deletion_ts\":null,\"set\":true},\"bank_name\":{\"value\":\"HDFC\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"type\\\":\\\"LOW_BALANCE\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"sms\",\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"currentMinBillAmount\\\":\\\"209\\\"}\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCDCArgumentCaptor = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService,times(1)).updateRecentFromReminderCdcConsumer(reminderCDCArgumentCaptor.capture());
        Assert.assertEquals(reminderCDCArgumentCaptor.getValue().getAfter().getOperator().getValue(),Constants.FASTAG_LOW_BALANCE_OPERATOR);
        Assert.assertNotNull(reminderCDCArgumentCaptor.getValue().getAfter().getDueDate().getValue());
        Assert.assertEquals(reminderCDCArgumentCaptor.getValue().getAfter().getAmount().getValue(), Constants.FASTAG_LOW_BALANCE_DEFAULT_AMOUNT);
    }
    @Test
    public void test_insertDataIntoCassandra_where_reminderCDCContains_custom_plan_bucket() throws RechargeSagaBaseException {
        String msg = "{\"ts_ms\":*************,\"op\":\"i\",\"source\":{\"version\":\"${project.version}\",\"connector\":\"cassandra\",\"name\":\"recon_connector\",\"ts_ms\":*************,\"snapshot\":\"false\",\"db\":\"NULL\",\"sequence\":null,\"cluster\":\"rechargesaga_cluster\",\"file\":\"CommitLog-6-*************.log\",\"pos\":********,\"keyspace\":\"recent\",\"table\":\"recents\"},\"after\":{\"customer_id\":{\"value\":********,\"deletion_ts\":null,\"set\":true},\"service\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"recharge_number\":{\"value\":\"7200000006361538\",\"deletion_ts\":null,\"set\":true},\"operator\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"plan_bucket\":{\"value\":\"\",\"deletion_ts\":null,\"set\":true},\"bill_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"due_amount\":{\"value\":1078.0,\"deletion_ts\":null,\"set\":true},\"due_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"paytype\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true},\"product_id\":{\"value\":10001,\"deletion_ts\":null,\"set\":true},\"payment_date\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"updated_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"created_at\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"txn_amount\":{\"value\":1000,\"deletion_ts\":null,\"set\":true},\"transaction_id\":{\"value\":*************,\"deletion_ts\":null,\"set\":true},\"notification_status\":{\"value\":1,\"deletion_ts\":null,\"set\":true},\"status\":{\"value\":4,\"deletion_ts\":null,\"set\":true},\"customer_other_info\":{\"value\":\"{\\\"nickname\\\":\\\"shubham\\\",\\\"tag\\\":\\\"cook\\\"}\",\"deletion_ts\":null,\"set\":true},\"extra\":{\"value\":\"{\\\"last_paid_amount\\\":\\\"241\\\",\\\"exhausted_date\\\":\\\"2024-05-07 19:09:00\\\"}\",\"deletion_ts\":null,\"set\":true},\"additional_info\":{\"value\":\"{\\\"param_1\\\":\\\"xyz\\\",\\\"param_2\\\":\\\"xyz\\\",\\\"param_3\\\":\\\"xyz\\\",\\\"param_4\\\":\\\"xyz\\\",\\\"param_5\\\":\\\"xyz\\\",\\\"amount\\\":1080,\\\"category_id\\\":26}\",\"deletion_ts\":null,\"set\":true},\"user_data\":{\"value\":\"{\\\"recharge_number_2\\\":\\\"input2\\\",\\\"recharge_number_3\\\":\\\"input3\\\",\\\"recharge_number_4\\\":\\\"input6\\\",\\\"recharge_number_5\\\":\\\"input4\\\",\\\"recharge_number_6\\\":\\\"input5\\\"}\",\"deletion_ts\":null,\"set\":true},\"event_source\":{\"value\":\"p2p\",\"deletion_ts\":null,\"set\":true}}}";
        ReminderCDC reminderCDC = JsonUtils.parseJson(msg, ReminderCDC.class);
        System.out.println(reminderCDC);
        when(commonService.getSmartReminderTTL(Mockito.any())).thenReturn(455677);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);

        ArgumentCaptor<ReminderCDC> reminderCdc = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService).updateRecentFromReminderCdcConsumer(reminderCdc.capture());
        verify(customerBillDao).save(customerBill.capture(),ttl.capture());
    }

    @Test
    public void testInsertDataIntoCassandra_FastagService_PrepaidQualified_NullDueDate() {
        // Arrange
        ReminderCDC reminderCDC = createFastagReminderCDC();
        reminderCDC.getAfter().setDueDate(null); // Set due date to null

        // Act
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);

        // Assert
        assertNotNull(reminderCDC.getAfter().getDueDate());
    }

//    @Test
//    public void testInsertDataIntoCassandra_FastagService_NullAmount() {
//        // Arrange
//        ReminderCDC reminderCDC = createFastagReminderCDC();
//        reminderCDC.getAfter().setAmount(null); // Set amount to null
//
//        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
//
//        // Assert
//        assertNotNull(reminderCDC.getAfter().getAmount());
//        assertEquals(Constants.FASTAG_LOW_BALANCE_DEFAULT_AMOUNT, reminderCDC.getAfter().getAmount().getValue(), 0.001);
//    }

    private ReminderCDC createFastagReminderCDC() {
        ReminderCDC reminderCDC = new ReminderCDC();
        ReminderAfter after = new ReminderAfter();

        LongEntity customerIdField = new LongEntity();
        customerIdField.setValue(123456789L);
        after.setCustomerId(customerIdField);

        StringEntity rechargeNumberField = new StringEntity();
        rechargeNumberField.setValue("1234567890");
        after.setRechargeNumber(rechargeNumberField);

        StringEntity serviceField = new StringEntity();
        serviceField.setValue(FASTAG);
        after.setService(serviceField);

        StringEntity operatorField = new StringEntity();
        operatorField.setValue("ICICI");
        after.setOperator(operatorField);

        StringEntity extra = new StringEntity();
        extra.setValue("{\"type\":\"LOW_BALANCE\"}");
        after.setExtra(extra);

        reminderCDC.setAfter(after);
        reminderCDC.setOp("c");

        return reminderCDC;
    }

     @Test
    public void testCDCListenerIgnoreOlderSMSFastag() throws CDCReminderListenerException {
        String msg = " {\"customerId\":*********,\"rechargeNumber\":\"MH02CB9538\",\"productId\":*********,\"operator\":\"icici bank\",\"amount\":50,\"dueDate\":null,\"billDate\":\"2025-04-09T10:54:39.512Z\",\"billFetchDate\":null,\"nextBillFetchDate\":null,\"customerOtherInfo\":\"{\\\"customerId\\\":*********,\\\"rechargeNumber\\\":\\\"MH02CB9538\\\",\\\"operator\\\":\\\"ICICI\\\",\\\"billDate\\\":*************,\\\"billFetchDate\\\":*************,\\\"service\\\":\\\"fastag recharge\\\",\\\"smsClass\\\":\\\"1\\\",\\\"smsExtra\\\":false,\\\"smsSenderID\\\":\\\"VA-ICICIT\\\",\\\"modelVersion\\\":\\\"v2.0\\\",\\\"amount\\\":86,\\\"isLowBalance\\\":true,\\\"debugKey\\\":\\\"smsSenderID:VA-ICICIT_custId:*********_rechargeNumber:MH02CB9538\\\"}\",\"paytype\":\"prepaid\",\"service\":\"fastag recharge\",\"categoryId\":null,\"customerMobile\":null,\"customerEmail\":null,\"notificationStatus\":1,\"bankName\":null,\"cardNetwork\":null,\"status\":14,\"dbEvent\":\"upsertWithoutRead\",\"debugKey\":\"rech_num:MH02CB9538::operator:icici bank::productId:*********::custId:*********\",\"extra\":\"{\\\"type\\\":\\\"LOW_BALANCE\\\",\\\"recon_id\\\":\\\"Vvsa4yD27+8opRWnBmNofvUBOoQ=\\\",\\\"user_type\\\":\\\"NON_RU\\\",\\\"smsDateTime\\\":\\\"*************\\\"}\",\"circle\":\"icici\",\"cdcEventType\":\"u\",\"paymentDate\":null,\"userData\":null,\"isAutomatic\":null,\"updateAt\":\"2025-04-09 16:45:09\",\"publishedDate\":null,\"retry\":null,\"nextBillFetchStartDate\":null,\"dueAmount\":50,\"createdAt\":\"2025-04-09 16:45:09\",\"remindLaterDate\":null,\"remindLaterFlow\":null,\"oldBillFetchDate\":null,\"is_encrypted\":0,\"decryptedRechargeNumber\":\"MH02CB9538\"}";

        SMSRecoveryPacket smsRecoveryPacket = JsonUtils.parseJsonWithCustomMapper(msg, SMSRecoveryPacket.class);
        NonPaytmCDC nonPaytmCDC = CDCConsumerUtil.convertRecoveryCodeToCDCFormat(smsRecoveryPacket);

        ReminderCDC reminderCDC = CDCConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDC);
        cdcReminderListener.insertDataIntoCassandra(reminderCDC);
        ArgumentCaptor<ReminderCDC> reminderCDCArgumentCaptor = ArgumentCaptor.forClass(ReminderCDC.class);
        verify(recentService, times(0)).updateRecentFromReminderCdcConsumer(reminderCDCArgumentCaptor.capture());
    }
}
