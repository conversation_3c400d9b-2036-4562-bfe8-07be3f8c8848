package com.paytm.saga.listeners;

import com.fasterxml.jackson.databind.JsonNode;
import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.ReminderListenerException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dao.impl.CustomerBillDao;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.model.*;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.repository.*;
import com.paytm.saga.service.*;
import com.paytm.saga.service.impl.ServiceConfig;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import junit.framework.TestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.Acknowledgment;

import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.paytm.saga.common.constant.Constants.CommonConstants.RECO_REFRESH_ERROR_EVENT;
import static com.paytm.saga.common.constant.Constants.CommonConstants.REMINDER_CONSUMER;
import static com.paytm.saga.common.constant.Constants.ERROR_EVENT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class ReminderListenersTest extends TestCase {

    @Mock
    MappingService mappingService;

    @Mock
    Acknowledgment acknowledgment;

    @Mock
    ChannelHistoryService channelHistoryService;

    @Mock
    CommonCacheService commonCacheService;

    @Mock
    DropOffRepository dropOffRepository;

    @Mock
    MetricsHelper metricsHelper;
    @Mock
    DropOffDBHelper dropOffDBHelper;

    @Mock
    ReminderHistoryRepositoryWrapperService reminderHistoryRepository;

    @Mock
    ChannelHistoryRepository channelHistoryRepository;
    @Mock
    CVRProductCache cvrProductCache;
    @Mock
    RecentService recentService;

    @Mock
    RecentsRepositoryWrapperService recentsRepository;
    @Mock
    CustomerBillRepository customerBillRepository;

    @Mock
    CustomerBillDao customerBillDao;
    @Mock
    DropOffService dropOffService;

    @Mock
    DCATService dcatService;
    @Mock
    KafkaProducerService kafkaProducerService;
    @Mock
    ReminderListeners reminderListeners1;

    @Mock
    ServiceConfig serviceConfig;

    @Mock
    ReminderResponseModelEncryptionHandler encryptionHandlerReminderResponseModel;

    private ReminderResponseModel reminderResponseModel;

    DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    DateFormat initDateformat = new SimpleDateFormat(Constants.CommonConstants.COMMON_DATE_FORMAT);

    Date date1 = new Date(), date2 = new Date(), date3 =new Date(), date4 = new Date(), date5 = new Date(), date_now_minus_10_days = new Date(), date_now_minus_40_days = new Date();

    @InjectMocks
    ReminderListeners reminderListeners = null;

    @Mock
    CommonService commonService;

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(AutomaticDataListener.class));


    @Before
    public void setup() {

        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30}";
        Map<String, Object> serviceConfig1=null;
        try {
            serviceConfig1 = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig1);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }

        MockitoAnnotations.initMocks(this);

        reminderListeners = new ReminderListeners(reminderHistoryRepository, channelHistoryRepository, channelHistoryService, metricsHelper, recentsRepository, customerBillRepository, dropOffDBHelper, recentService, kafkaProducerService, commonService,serviceConfig, encryptionHandlerReminderResponseModel, customerBillDao);

        date1 = DateUtils.addHours(new Date(),-12);
        date2 = DateUtils.addHours(new Date(),-24);
        date3 = DateUtils.addHours(new Date(),-36);
        date4 = DateUtils.addHours(new Date(),-48);
        date5 = DateUtils.addHours(new Date(),-60);

        date_now_minus_10_days = DateUtils.addDays(new Date(),10);
        date_now_minus_40_days = DateUtils.addDays(new Date(),-40);

    }

    @After
    public void resetMocks(){
        Mockito.reset();
    }

    private ReminderResponseModel getReminderKafkaResponseMock(){

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e) {
        }
        ;


        reminderResponseModel.setTable("bill_airtel");
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderData.setAmount(100.0);
        reminderData.setRecharge_number("1231111");
        reminderData.setDue_date(dateString);
        reminderData.setBill_date("2021-03-06 11:14:11");
        reminderData.setCircle("A");
        reminderData.setCustomer_id(1L);
        reminderData.setIs_automatic(1);
        reminderData.setService("mobile");
        reminderData.setPaytype("postpaid");
        reminderData.setService("mobile");
        reminderData.setOperator("assampowerdistributioncompanyltd.(apdcl)");
        reminderData.setCurrentMinBillAmount(100.0);
        reminderData.setNotificationStatus(1);

        reminderData.setProduct_id(2L);
        reminderData.setStatus(33);
        reminderData.setUpdated_at("2021-03-05 05:44:11");
        reminderData.setCreated_at("2021-03-05 05:44:11");
        reminderData.setPaymentDate("2021-03-05 05:44:11");

        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(99.0);

        reminderResponseModel.setData(reminderData);
        reminderData.setUser_data("{\"recharge_number_2\":\"7699876\",\"recharge_number_3\":\"7698758\",\"recharge_number_4\":\"7699867\"}");
        reminderResponseModel.setOld(oldData);

        return reminderResponseModel;
    }

    private ReminderResponseModel getReminderKafkaResponseWithAutomaticStateUpdateMock(){

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e) {
        }
        ;


        reminderResponseModel.setTable("bill_airtel");
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderData.setAmount(100.0);
        reminderData.setRecharge_number("1231111");
        reminderData.setDue_date("2021-03-06 11:14:11");
        reminderData.setBill_date("2021-03-06 11:14:11");
        reminderData.setCircle("A");
        reminderData.setCustomer_id(1L);
        reminderData.setIs_automatic(1);
        reminderData.setPaytype("prepaid");
        reminderData.setOperator("assampowerdistributioncompanyltd.(apdcl)");
        reminderData.setCurrentMinBillAmount(100.0);
        reminderData.setService("electricity");

        reminderData.setProduct_id(2L);
        reminderData.setStatus(33);
        reminderData.setUpdated_at("2021-03-05 05:44:11");
        reminderData.setCreated_at("2021-03-05 05:44:11");
        reminderData.setPaymentDate("2021-03-05 05:44:11");

        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(99.0);
        oldData.setIs_automatic(0);

        reminderResponseModel.setData(reminderData);
        reminderData.setUser_data("{\"recharge_number_2\":\"7699876\",\"recharge_number_3\":\"7698758\",\"recharge_number_4\":\"7699867\"}");
        reminderResponseModel.setOld(oldData);

        return reminderResponseModel;
    }

    private ReminderResponseModel getReminderKafkaResponseRentPaymentMock(){

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e) {
        }
        ;


        reminderResponseModel.setTable("bills_rentpayment");
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderData.setAmount(100.0);
        reminderData.setRecharge_number("**********");
        reminderData.setDue_date(dateString);
        reminderData.setBill_date(dateString);
        reminderData.setCircle(null);
        reminderData.setCustomer_id(100L);
        reminderData.setIs_automatic(0);
        reminderData.setPaytype("postpaid");
        reminderData.setOperator("rent payment");
        reminderData.setCurrentMinBillAmount(100.0);
        reminderData.setService("rent payment");

        reminderData.setProduct_id(1201345018L);
        reminderData.setStatus(4);
        reminderData.setUpdated_at("2023-07-01 05:44:11");
        reminderData.setCreated_at("2023-07-01 05:44:11");
        reminderData.setPaymentDate("2023-07-01 05:44:11");
        reminderData.setEarlyPaymentAmount(100.0);
        reminderData.setEarlyPaymentDate("2023-07-01 05:44:11");

        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(99.0);
        oldData.setIs_automatic(0);

        reminderResponseModel.setData(reminderData);
        reminderData.setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\",\"recharge_number_7\":********,\"recharge_number_8\":\"XX 8528\"}");
        reminderResponseModel.setOld(oldData);

        return reminderResponseModel;
    }

    private ReminderResponseModel getReminderKafkaResponseTuitionFeeMock(){

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e) {
        }
        ;


        reminderResponseModel.setTable("bills_rentpayment");
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderData.setAmount(100.0);
        reminderData.setRecharge_number("********");
        reminderData.setDue_date(dateString);
        reminderData.setBill_date(dateString);
        reminderData.setCircle(null);
        reminderData.setCustomer_id(100L);
        reminderData.setIs_automatic(0);
        reminderData.setPaytype("postpaid");
        reminderData.setOperator("tuition fee");
        reminderData.setCurrentMinBillAmount(100.0);
        reminderData.setService("tuition fees");

        reminderData.setProduct_id(1201345018L);
        reminderData.setStatus(4);
        reminderData.setUpdated_at("2023-07-01 05:44:11");
        reminderData.setCreated_at("2023-07-01 05:44:11");
        reminderData.setPaymentDate("2023-07-01 05:44:11");

        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(99.0);
        oldData.setIs_automatic(0);

        reminderResponseModel.setData(reminderData);
        reminderData.setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\",\"recharge_number_7\":\"XXYYCC\",\"recharge_number_8\":\"XX 8528\"}");
        reminderResponseModel.setOld(oldData);

        return reminderResponseModel;
    }
    private ReminderResponseModel getReminderKafkaResponseForNotificationStatus(){

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e) {
        }
        ;

        reminderResponseModel.setTable("bill_airtel");
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderData.setAmount(100.0);
        reminderData.setRecharge_number("1231111");
        reminderData.setDue_date("2021-03-06 11:14:11");
        reminderData.setBill_date("2021-03-06 11:14:11");
        reminderData.setCircle("A");
        reminderData.setCustomer_id(1L);
        reminderData.setNotificationStatus(1);
        reminderData.setPaytype("prepaid");
        reminderData.setOperator("assampowerdistributioncompanyltd.(apdcl)");
        reminderData.setCurrentMinBillAmount(100.0);
        reminderData.setService("electricity");

        reminderData.setProduct_id(2L);
        reminderData.setStatus(33);
        reminderData.setUpdated_at("2021-03-05 05:44:11");
        reminderData.setCreated_at("2021-03-05 05:44:11");
        reminderData.setPaymentDate("2021-03-05 05:44:11");

        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(99.0);
        oldData.setNotificationStatus(1);

        reminderResponseModel.setData(reminderData);
        reminderData.setUser_data("{\"recharge_number_2\":\"7699876\",\"recharge_number_3\":\"7698758\",\"recharge_number_4\":\"7699867\"}");
        reminderResponseModel.setOld(oldData);

        return reminderResponseModel;
    }


    @Test
    public void testIsSkipableTrueNullreminderResponseModel() {
        ReminderResponseModel reminderResponseModel =null;


        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }
    @Test
    public void testIsSkipableTrueNullData() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setData(null);

        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }
    @Test
    public void testIsSkipableFalseNullBillDate() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setBill_date(null);

        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(false), res);
    }
    @Test
    public void testIsSkipableTrueCreditcardPaytypeDeleteOperationtype() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setPaytype("credit card");
        reminderResponseModel.setOperationType("delete");
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableTrueCreditcardPaytypeDeleteOperationtypeWhenAmountisZero() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setPaytype("credit card");
        reminderResponseModel.setOperationType("delete");
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getData().setDue_date("2023-03-10 11:14:11");
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableTrueCreditcardPaytypeDeleteOperationtypeWhenDueDateIsNull() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setPaytype("credit card");
        reminderResponseModel.setOperationType("delete");
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getData().setDue_date(null);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }
    @Test
    public void testIsSkipableTrueCreditcardPaytypeNullRechargenumber() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setPaytype("credit card");
        reminderResponseModel.getData().setRecharge_number(null);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }
    @Test
    public void testIsSkipableTrueCreditcardPaytypeNullParNullReference_id() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setPaytype("credit card");
        reminderResponseModel.getData().setPar(null);
        reminderResponseModel.getData().setReference_id(null);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }
    @Test
    public void testIsSkipableTrueUpdateOperationtypeNullOlddata() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setOld(null);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableTrueStatus() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setStatus(1);

        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableFalseStatus() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(false), res);
    }


    @Test
    public void testIsSkipableTrueTable() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setTable("bills_airtelprepaid");
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(false), res);
    }


    @Test
    public void testIsSkipableFalseTable() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(false), res);
    }

    @Test
    public void testIsSkipableTrueNullDueDate() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setDue_date(null);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(false), res);
    }

    @Test
    public void testIsSkipableTrueNullResponseObj() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setData(null);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableTrueNotRequiredFieldsUpdated() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setDue_date("2021-03-05 05:44:11");
        reminderResponseModel.getData().setBill_date("2021-03-05 05:44:11");


        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableFalseWhenStatusIsPaymentDone() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setStatus(11);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);
        assertEquals(new Boolean(false), res);
    }

    //@Test
    public void testIsSkipableFalseRequiredFieldsUpdated() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setDue_date("2031-03-05 05:44:11");
        reminderResponseModel.getData().setBill_date("2031-03-05 05:44:11");


        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(10.0);
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(false), res);
    }

    @Test
    public void testIsSkipableTrueMoreThan30DaysDueDate() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = dateformat.format(date_now_minus_40_days);

        reminderResponseModel.getData().setDue_date(dateString);
        reminderResponseModel.getData().setBill_date("2021-03-05 05:44:11");


        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(10.0);
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(true), res);
    }


    @Test
    public void testIsSkipableFalseLessThan30DaysDueDate() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();

        String dateString = dateformat.format(date_now_minus_10_days);

        reminderResponseModel.getData().setDue_date(dateString);
        reminderResponseModel.getData().setBill_date("2021-03-05 05:44:11");


        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setAmount(10.0);
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);

        assertEquals(new Boolean(false), res);
    }


    @Test
    public void testInsertIntoCassandraEmptyPayload() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setData(null);


        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        verify(reminderHistoryRepository , times(0)).save(argument.capture(), anyInt());

    }

    @Test
    public void testInsertIntoCassandra() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());

    }

    @Test
    public void testInsertIntoCassandraWithPartialSmsTrue() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setPar("a1214acc");
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, true);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getOriginalAmount());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), argument.getValue().getOriginalMinBillAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), argument.getValue().getCurrentMinBillAmount());

    }



    @Test
    public void testUpdateRecentBillFromReminderListenerWith56Params() throws Exception {
        // Arrange
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setConsumerName("testConsumer");
        reminderResponseModel.getData().setCustomerOtherInfo("{\"consumerName\":\"Manan Sharma\",\"Amount\":65000,\"DueDate\":\"2024-10-19\",\"CustConvFee\":null,\"CustConvDesc\":null,\"BillDate\":\"2024-09-25\",\"BillNumber\":\"12345001\",\"BillPeriod\":\"MONTHLY\"}");
        // Act
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        // Capture arguments
        ArgumentCaptor<Long> customerIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cinCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> parCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tinCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> isTokenizedTransactionCaptor = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDateCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDateCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> dueAmountCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> minDueAmountCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> originalDueAmountCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> originalMinDueAmountCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> currentOutstandingAmountCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> productIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automaticStatusCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> billUpdateTimeCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> isMarkAsPaidCaptor = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> markAsPaidTimeCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updatedAtCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumberCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2Captor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3Captor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4Captor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5Captor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6Captor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7Captor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8Captor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circleCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytypeCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcnCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytmCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDateCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmountCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmountCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAtCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCardCaptor = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidationCaptor = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxnCaptor = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSourceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> isNewBillerCaptor = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDateCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmountCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconIdCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardIdCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlagCaptor = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatusCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDateCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> cardVariantCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkinCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extraCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDateCaptor = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> consentValidTillCaptor = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Integer> notificationStatusCaptor = ArgumentCaptor.forClass(Integer.class);
        // Verify method call
        verify(recentService).updateRecentBillFromReminderListener(
            customerIdCaptor.capture(),
            serviceCaptor.capture(),
            operatorCaptor.capture(),
            planBucketCaptor.capture(),
            cinCaptor.capture(),
            parCaptor.capture(),
            tinCaptor.capture(),
            isTokenizedTransactionCaptor.capture(),
            billDateCaptor.capture(),
            dueDateCaptor.capture(),
            dueAmountCaptor.capture(),
            minDueAmountCaptor.capture(),
            originalDueAmountCaptor.capture(),
            originalMinDueAmountCaptor.capture(),
            productIdCaptor.capture(),
            automaticStatusCaptor.capture(),
            billUpdateTimeCaptor.capture(),
            isMarkAsPaidCaptor.capture(),
            markAsPaidTimeCaptor.capture(),
            markAsPaidAmountCaptor.capture(),
            updatedAtCaptor.capture(),
            rechargeNumberCaptor.capture(),
            rechargeNumber2Captor.capture(),
            rechargeNumber3Captor.capture(),
            rechargeNumber4Captor.capture(),
            rechargeNumber5Captor.capture(),
            rechargeNumber6Captor.capture(),
            rechargeNumber7Captor.capture(),
            rechargeNumber8Captor.capture(),
            circleCaptor.capture(),
            paytypeCaptor.capture(),
            mcnCaptor.capture(),
            notificationStatusCaptor.capture(),
            notPaidOnPaytmCaptor.capture(),
            txnDateCaptor.capture(),
            txnAmountCaptor.capture(),
            newBillUpdatedAtCaptor.capture(),
            isSavedCardCaptor.capture(),
            isValidationCaptor.capture(),
            isTxnCaptor.capture(),
            eventSourceCaptor.capture(),
            reminderResponseModelCaptor.capture(),
            isNewBillerCaptor.capture(),
            earlyPaymentDateCaptor.capture(),
            earlyPaymentAmountCaptor.capture(),
            pgCardIdCaptor.capture(),
                        reconIdCaptor.capture(),
            nextBillFetchDateFlagCaptor.capture(),
            reminderStatusCaptor.capture(),
            oldBillFetchDateCaptor.capture(),
            cardVariantCaptor.capture(),
            cardSkinCaptor.capture(),
            extraCaptor.capture(),
            remindLaterDateCaptor.capture(),
            consentValidTillCaptor.capture(),
                        any()
        );
        // Assert captured values
        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerIdCaptor.getValue());
        assertEquals(reminderResponseModel.getData().getService(), serviceCaptor.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operatorCaptor.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumberCaptor.getValue());
        assertEquals(reminderResponseModel.getData().getAmount(), dueAmountCaptor.getValue());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), minDueAmountCaptor.getValue());
        assertEquals(reminderResponseModel.getData().getProduct_id(), productIdCaptor.getValue());
        assertEquals(reminderResponseModel.getData().getIs_automatic(), automaticStatusCaptor.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumberCaptor.getValue());
        assertEquals(true,extraCaptor.getValue().contains(Constants.SUBSCRIBER_DETAILS));

    }
    @Test
    public void testPG_Deleted_AMW() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setPar("a1214acc");
        String extra = "{\"type\":\"PG_Deleted_AMW\"}";
        reminderResponseModel.getData().setExtra(extra);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, true);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getReference_id(), argument.getValue().getReference_id());
    }

    @Test
    public void testReconId() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setPar("a1214acc");
        String extra = "{\"recon_id\":\"Test\"}";
        reminderResponseModel.getData().setExtra(extra);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        assertEquals(reminderResponseModel.getData().getRecondId(), "Test");
    }

    @Test
    public void testReconIdPrepareRecents() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setPar("a1214acc");
        String extra = "{\"recon_id\":\"Test\"}";
        reminderResponseModel.getData().setExtra(extra);
        reminderResponseModel.getData().setRecondId("Test");
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        Recents recent = reminderListeners.prepareRecentsData(new ReminderHistory(),reminderResponseModel);
        assertEquals("Test", recent.getReconId());
    }

    @Test
    public void testInsertIntoCassandraParitalAmountFalse() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date("2021-03-05 11:14:11");
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        Integer isPartial = 0;
        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(isPartial, argument.getValue().getIsPartial());

    }

    @Test
    public void testInsertIntoCassandraParitalAmountTrue() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        Integer isPartial = 1;
        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(isPartial, argument.getValue().getIsPartial());

    }

    @Test
    public void testInsertIntoCassandraNotPaidOnPaytmTrue() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);
        reminderResponseModel.getOld().setDue_date("2021-03-05 11:14:11");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        ArgumentCaptor<ChannelHistory> argument3 = ArgumentCaptor.forClass(ChannelHistory.class);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());
        ArgumentCaptor<String> argument4 = ArgumentCaptor.forClass(String.class);
        verify(channelHistoryService).save(argument3.capture(),argument4.capture());

        verify(channelHistoryService , times(1)).save(argument3.capture(),argument4.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument3.getValue().getRechargeNumber());


    }

    @Test
    public void testInsertIntoCassandraDeleteOperation() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);
        reminderResponseModel.setOperationType("delete");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(null, argument.getValue().getBill_date());


    }

    //@Test
    public void testlisten() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();

        String data = "{\"database\":\"digital_reminder\",\"table\":\"bills_archive\",\"type\":\"update\",\"ts\":1613132123,\"xid\":1007658,\"commit\":true,\"data\":{\"id\":231309,\"customer_id\":123457,\"recharge_number\":\"9999900000\",\"product_id\":194,\"operator\":\"assampowerdistributioncompanyltd.(apdcl)\",\"amount\":10.00,\"bill_date\":\"2031-03-05 11:14:11\",\"due_date\":\"2031-03-05 11:14:11\",\"bill_fetch_date\":null,\"next_bill_fetch_date\":\"2020-02-20 00:00:00\",\"gateway\":\"apdcl\",\"paytype\":null,\"service\":\"electricity\",\"circle\":null,\"customer_mobile\":\"9643825427\",\"customer_email\":\"<EMAIL>\",\"payment_channel\":null,\"retry_count\":0,\"status\":0,\"reason\":null,\"extra\":null,\"published_date\":null,\"created_at\":\"2020-08-21 11:47:37\",\"updated_at\":\"2021-02-12 12:15:23\",\"user_data\":null,\"notification_status\":1,\"payment_date\":\"2020-02-2000:00:00\",\"service_id\":0,\"customerOtherInfo\":null,\"is_automatic\":0},\"old\":{\"amount\":120.00,\"updated_at\":\"2021-02-1212:14:55\"}}";
        List<String> messages = new ArrayList<String>();
        messages.add(data);
        reminderResponseModel = JsonUtils.parseJson(data, ReminderResponseModel.class);
        //reminderListeners.listen(messages, acknowledgment);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        //verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());


    }


    private ReminderResponseModel getReminderKafkaCCResponseMock(){

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e){};


        reminderResponseModel.setTable("bills_creditcard");
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderData.setAmount(100.0);
        reminderData.setRecharge_number("5376 52XX XXXX 6618");
        reminderData.setReference_id("**********");
        reminderData.setDue_date(dateString);
        reminderData.setOldBillFetchDate(dateString);
        reminderData.setBill_date("2021-03-06 11:14:11");
        reminderData.setCircle("");
        reminderData.setCustomer_id(1L);
        reminderData.setIs_automatic(1);
        reminderData.setPaytype(Constants.CREDIT_CARD_PAYTYPE);
        reminderData.setOperator("mastercard_indusindbank");
        reminderData.setService(Constants.FINANCIAL_SERVICE);
        reminderData.setProduct_id(2L);
        reminderData.setStatus(33);
        reminderData.setUpdated_at("2021-03-05 05:44:11");
        reminderData.setCreated_at("2021-03-05 05:44:11");
        reminderData.setNotificationStatus(1);


        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(oldData);

        return reminderResponseModel;
    }


    @Test
    public void testInsertIntoCassandraCC() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setNotificationStatus(1);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getOriginalAmount());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), argument.getValue().getOriginalMinBillAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), argument.getValue().getCurrentMinBillAmount());



    }

    @Test
    public void testInsertIntoCassandraCCWithParSent() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setPar("a1214acc");
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getOriginalAmount());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), argument.getValue().getOriginalMinBillAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), argument.getValue().getCurrentMinBillAmount());

    }

    @Test
    public void testInsertIntoCassandraCCWithDeleteEvent() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setNotificationStatus(1);
        reminderResponseModel.getData().setPar("a1214acc");
        reminderResponseModel.setOperationType("delete");

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(0.0, argument.getValue().getCurrentMinBillAmount());


    }

    @Test
    public void testInsertIntoCassandraCCWithMcnNull() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setRecharge_number(null);
        reminderResponseModel.setOperationType("insert");
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository, Mockito.times(0)).save(argument.capture(), argument1.capture());

    }

    @Test
    public void testInsertIntoCassandraCCWithoutUniqueCCId() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setReference_id(null);
        reminderResponseModel.getData().setPar(null);
        reminderResponseModel.setOperationType("insert");
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository, Mockito.times(0)).save(argument.capture(), argument1.capture());

    }

    @Test
    public void testNewCCBill() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();

        ReminderDataResponseModel old=reminderResponseModel.getOld();
        old.setBill_date("2021-03-06 11:14:11");
        String dateString = null;
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e){};
        old.setDue_date(dateString);
        old.setAmount(100.0);
        old.setCustomerOtherInfo("{\"currentMinBillAmount\":50}");
        old.setCreated_at("2021-03-05 05:44:11");
        old.setUpdated_at("2021-03-05 06:44:11");

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("update");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getOriginalAmount());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), argument.getValue().getOriginalMinBillAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getCurrentMinBillAmount(), argument.getValue().getCurrentMinBillAmount());

    }




    @Test
    public void testCCBillDelete() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);
        reminderResponseModel.getData().setNotificationStatus(1);

        reminderResponseModel.setOperationType("delete");
        reminderResponseModel.getData().setPaytype("credit card");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(0.0, argument.getValue().getAmount());
        assertEquals(0.0, argument.getValue().getCurrentMinBillAmount());

    }

    @Test
    public void testBillDeleteWithPaytypeNull() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("delete");
        reminderResponseModel.getData().setPaytype(null);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(100.0, argument.getValue().getAmount());
    }

    @Test
    public void testPartialPaymentCCBill() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setCustomerOtherInfo("{\"currentMinBillAmount\":20}");
        ReminderDataResponseModel old=reminderResponseModel.getOld();
        //old.setBill_date("2021-03-06 11:14:11");
        old.setAmount(50.0);
        old.setCustomerOtherInfo("{\"currentMinBillAmount\":20}");
        old.setCreated_at("2021-03-05 05:44:11");
        old.setUpdated_at("2021-03-05 06:44:11");

        List<ReminderHistory> reminderHistories=new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory=new ReminderHistory();
        try {
            reminderHistory.setDue_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(reminderResponseModel.getData().getDue_date()));
        }catch(Exception e) {}

        reminderHistory.setOriginalAmount(100.0);
        reminderHistory.setOriginalMinBillAmount(50.0);
        reminderHistory.setReference_id(reminderResponseModel.getData().getReference_id());


        reminderHistories.add(reminderHistory);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("update");
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(20.0, argument.getValue().getCurrentMinBillAmount());



    }

    @Test
    public void testInsertIntoChannelHistoryCase1() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        ArgumentCaptor<ChannelHistory> argument3 = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<Integer> argument4 = ArgumentCaptor.forClass(Integer.class);
        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        verify(channelHistoryRepository, times(0)).save(argument3.capture(),argument4.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());

    }
    @Test
    public void testInsertIntoChannelHistoryCase2() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.getOld().setBill_date("2022-08-07 11:14:11");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        ArgumentCaptor<ChannelHistory> argument3 = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<Integer> argument4 = ArgumentCaptor.forClass(Integer.class);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        verify(channelHistoryRepository, times(0)).save(argument3.capture(),argument4.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());

    }
    @Test
    public void testInsertIntoChannelHistoryCase3() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.getOld().setDue_date("2022-08-07 11:14:11");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        ArgumentCaptor<ChannelHistory> argument3 = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument4 = ArgumentCaptor.forClass(String.class);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        verify(channelHistoryService, times(1)).save(argument3.capture(),argument4.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument3.getValue().getRechargeNumber());

    }

    @Test
    public void testInsertIntoChannelHistoryCase4() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.getOld().setDue_date("2022-08-07 11:14:11");
        reminderResponseModel.getOld().setBill_date("2022-08-07 10:14:11");

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        ArgumentCaptor<ChannelHistory> argument3 = ArgumentCaptor.forClass(ChannelHistory.class);
        ArgumentCaptor<String> argument4 = ArgumentCaptor.forClass(String.class);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());

        verify(channelHistoryService, times(1)).save(argument3.capture(),argument4.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument3.getValue().getRechargeNumber());

    }

    @Test
    public void testInsertIntoReminderClusterKey() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        ArgumentCaptor<CustomerBill> argument = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<Integer> argument2 = ArgumentCaptor.forClass(Integer.class);
        when(commonService.getSmartReminderTTL(any())).thenReturn(5111);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(customerBillDao).save(argument.capture(),argument2.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getKey().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getKey().getOperator());
        assertEquals(reminderResponseModel.getData().getService(), argument.getValue().getKey().getService());
        assertEquals(reminderResponseModel.getData().getCustomer_id(), argument.getValue().getKey().getCustomerId());
    }
    @Test
    public void testUpsertIntoRecents() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(),nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());


        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());

    }

    @Test
    public void testUpsertIntoRecentsWithAutomaticSateUpdate() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseWithAutomaticStateUpdateMock();
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);

        verify(recentService).updateRecentAutomaticStateInfo(automatic_status.capture(), customerid.capture(), service.capture(), rechargeNumber.capture(),operator.capture(), plan_bucket.capture());

        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals(reminderResponseModel.getData().getIs_automatic(), automatic_status.getValue());


    }
    @Test
    public void testIsNotPaidOnPaytmWithNonUpdate(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setOperationType("insert");
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.FALSE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithoutOldBill(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.setOld(null);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.FALSE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateChangedAndAmountChangedWithNonZeroOldAmount(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date("2022-08-07 11:14:11");
        reminderResponseModel.getOld().setAmount(234.0);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.TRUE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateChangedAndAmountNotChangedWithNonZeroCurrentAmount(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date("2022-08-07 11:14:11");
        reminderResponseModel.getOld().setAmount(null);
        reminderResponseModel.getData().setAmount(500.0);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.TRUE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateNotChangedAndAmountNotChanged(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date(null);
        reminderResponseModel.getOld().setAmount(null);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.FALSE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateNotChangedAndAmountChangedStatusNotChanged(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date(null);
        reminderResponseModel.getOld().setAmount(224.0);
        reminderResponseModel.getOld().setStatus(null);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.FALSE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateNotChangedAndAmountChangedStatusChangedWithNonZeroCurrentAmount(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date(null);
        reminderResponseModel.getOld().setAmount(224.0);
        reminderResponseModel.getData().setAmount(100.0);
        reminderResponseModel.getOld().setStatus(11);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.FALSE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateNotChangedAndAmountChangedStatusChangedWithZeroCurrentAmountStatusNotEquals6(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date(null);
        reminderResponseModel.getOld().setAmount(224.0);
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getOld().setStatus(11);
        reminderResponseModel.getData().setStatus(6);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.TRUE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateNotChangedAndAmountChangedStatusChangedWithZeroCurrentAmountStatusNotEquals14(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date(null);
        reminderResponseModel.getOld().setAmount(224.0);
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getOld().setStatus(11);
        reminderResponseModel.getData().setStatus(14);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.TRUE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateNotChangedAndAmountChangedStatusChangedWithZeroCurrentAmountStatusNotEquals6or14(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date(null);
        reminderResponseModel.getOld().setAmount(224.0);
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getOld().setStatus(11);
        reminderResponseModel.getData().setStatus(5);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.FALSE,isNotPaidOnPaytm);
    }

    @Test
    public void testIsNotPaidOnPaytmWithUpdateWithOldBillDueDateNotChangedAndAmountChangedStatusChangedFromZeroToZero(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getOld().setDue_date(null);
        reminderResponseModel.getOld().setAmount(0.0);
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getOld().setStatus(11);
        reminderResponseModel.getData().setStatus(6);
        Boolean isNotPaidOnPaytm = reminderListeners.isNotPaidOnPaytm(reminderResponseModel);

        assertEquals(Boolean.FALSE,isNotPaidOnPaytm);
    }
    public Recents fn(){
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e){};
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        Recents recents = null;
        recents = JsonUtils.parseJson(reminderResponseModel.getData().getUser_data(), Recents.class);
        if(recents==null) {
            recents=new Recents();
        }
        recents.setKey(new RecentsPrimaryKey());
        recents.getKey().setRechargeNumber(reminderResponseModel.getData().getRecharge_number());
        recents.getKey().setOperator(reminderResponseModel.getData().getOperator());
        recents.getKey().setService(reminderResponseModel.getData().getService());
        recents.getKey().setCustomerId(reminderResponseModel.getData().getCustomer_id());
        recents.getKey().setPlanBucket("");
        recents.setTin("");
        recents.setCin(reminderResponseModel.getData().getReference_id());
        recents.setPar(reminderResponseModel.getData().getPar());
        recents.setDueDate(new Date());
        recents.setBillDate(new Date());
        recents.setDueAmount(reminderResponseModel.getData().getAmount());
        recents.setMinDueAmount(reminderResponseModel.getData().getCurrentMinBillAmount());
        recents.setOriginalDueAmount(null);
        recents.setOriginalMinDueAmount(null);
        recents.setAutomaticStatus(reminderResponseModel.getData().getIs_automatic());
        recents.getKey().setPlanBucket("");
        recents.setBillUpdateTime(new Date());
        recents.setOrderId(1l);
        return recents;
    }
    @Test
    public void testInsertIntoDropOffSuccess(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setService("mobile");
        reminderResponseModel.getData().setOperator("jio");
        reminderResponseModel.getOld().setDue_date("2022-12-15 05:44:11");
        ArgumentCaptor<DropOff> dropOff = ArgumentCaptor.forClass(DropOff.class);

        List<Recents> recents = new ArrayList<>();
        Recents recent = new Recents();
        recent.setOrderId(2l);
        recents.add(recent);

        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(recents);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(dropOffDBHelper).insertDropOff(dropOff.capture());

        assertEquals("RECHARGE",dropOff.getValue().getEventType());
        assertEquals("7",dropOff.getValue().getStatus());
    }
    @Test
    public void testInsertIntoDropOffFailureWhenNoNewBill(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setService("mobile");
        reminderResponseModel.getData().setOperator("jio");

        ArgumentCaptor<DropOff> dropOff = ArgumentCaptor.forClass(DropOff.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(dropOffDBHelper,times(0)).insertDropOff(dropOff.capture());
    }
    @Test
    public void testInsertIntoDropOffFailureForNewUser(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setService("mobile");
        reminderResponseModel.getData().setOperator("jio");
        reminderResponseModel.getOld().setDue_date("2022-12-15 05:44:11");
        ArgumentCaptor<DropOff> dropOff = ArgumentCaptor.forClass(DropOff.class);

        List<Recents> recents = new ArrayList<>();
        Recents recent = new Recents();
        recent.setOrderId(null);
        recents.add(recent);

        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(recents);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(dropOffDBHelper,times(0)).insertDropOff(dropOff.capture());
    }
    @Test
    public void testInsertIntoDropOffWhenMultipleRecent(){
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setService("mobile");
        reminderResponseModel.getData().setOperator("jio");
        reminderResponseModel.getOld().setDue_date("2022-12-15 05:44:11");
        ArgumentCaptor<DropOff> dropOff = ArgumentCaptor.forClass(DropOff.class);

        List<Recents> recents = new ArrayList<>();
        Recents recent = new Recents();
        recent.setOrderId(null);
        recents.add(0,recent);

        Recents recent1 = new Recents();
        recent1.setOrderId(1l);
        recents.add(1,recent1);

        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(recents);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(dropOffDBHelper,times(1)).insertDropOff(dropOff.capture());
    }
    @Test
    public void testUpdateNotificationStatusInRecentsWhenDueDateNull() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseForNotificationStatus();
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelArgumentCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);

        List<Recents> recents = new ArrayList<>();
        recents.add(fn());

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(recentService).updateNotificationStatusInRecents(notificationStatus.capture(), operator.capture(), rechargeNumber.capture(),
                reminderResponseModelArgumentCaptor.capture());

        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals(reminderResponseModel.getData().getNotificationStatus(), notificationStatus.getValue());


    }
    @Test
    public void testUpdateNotificationStatusFailureForOpInsert() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseForNotificationStatus();
        reminderResponseModel.setOperationType("insert");
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelArgumentCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(recentService,times(0)).updateNotificationStatusInRecents(notificationStatus.capture(), operator.capture(), rechargeNumber.capture(),
                reminderResponseModelArgumentCaptor.capture());
    }
    @Test
    public void testUpdateNotificationStatusInRecentsFailure() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseForNotificationStatus();
        reminderResponseModel.getOld().setNotificationStatus(null);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelArgumentCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(recentService,times(0)).updateNotificationStatusInRecents(notificationStatus.capture(), operator.capture(), rechargeNumber.capture(),
                reminderResponseModelArgumentCaptor.capture());
    }
    @Test
    public void testUpdateNotificationStatusOldStatusNull() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseForNotificationStatus();
        reminderResponseModel.getOld().setNotificationStatus(null);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelArgumentCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(recentService,times(0)).updateNotificationStatusInRecents(notificationStatus.capture(), operator.capture(), rechargeNumber.capture(),
                reminderResponseModelArgumentCaptor.capture());
    }
    @Test
    public void testUpdateNotificationStatusOldNull() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseForNotificationStatus();
        reminderResponseModel.setOld(null);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelArgumentCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(recentService,times(0)).updateNotificationStatusInRecents(notificationStatus.capture(), operator.capture(), rechargeNumber.capture(),
                reminderResponseModelArgumentCaptor.capture());
    }
    @Test
    public void testUpdateNotificationStatusInRecentsWhenDueDateNullInCCPar() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseForNotificationStatus();
        reminderResponseModel.getData().setPaytype("credit card");
        reminderResponseModel.getData().setPar("3252345234523423423452");

        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelArgumentCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);

        List<Recents> recents = new ArrayList<>();
        Recents recent = fn();
        recent.getKey().setRechargeNumber("3252345234523423423452");
        recents.add(recent);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(recentService).updateNotificationStatusInRecents(notificationStatus.capture(), operator.capture(), rechargeNumber.capture(),
                reminderResponseModelArgumentCaptor.capture());

        assertEquals(reminderResponseModel.getData().getPar(), rechargeNumber.getValue());
        assertEquals("ccbp", operator.getValue());
        assertEquals(reminderResponseModel.getData().getNotificationStatus(), notificationStatus.getValue());


    }
    @Test
    public void testUpdateNotificationStatusInRecentsWhenDueDateNullInCCRefId() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseForNotificationStatus();
        reminderResponseModel.getData().setPaytype("credit card");
        reminderResponseModel.getData().setReference_id("3252345234523423423452");

        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelArgumentCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);

        List<Recents> recents = new ArrayList<>();
        Recents recent = fn();
        recent.getKey().setRechargeNumber("3252345234523423423452");
        recents.add(recent);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(recentService).updateNotificationStatusInRecents(notificationStatus.capture(), operator.capture(), rechargeNumber.capture(),
                reminderResponseModelArgumentCaptor.capture());

        assertEquals(reminderResponseModel.getData().getReference_id(), rechargeNumber.getValue());
        assertEquals("ccbp", operator.getValue());
        assertEquals(reminderResponseModel.getData().getNotificationStatus(), notificationStatus.getValue());


    }
    @Test
    public void testUpdateNotificationStatusOldCC2() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseForNotificationStatus();
        reminderResponseModel.setOperationType("update");
        reminderResponseModel.setOld(null);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModelArgumentCaptor = ArgumentCaptor.forClass(ReminderResponseModel.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(recentService,times(0)).updateNotificationStatusInRecents(notificationStatus.capture(), operator.capture(), rechargeNumber.capture(),
                reminderResponseModelArgumentCaptor.capture());
    }
    @Test
    public void testPartialPaymentCCBillWithSameDueDateAndDifferentBillDate() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setCustomerOtherInfo("{\"currentMinBillAmount\":20}");
        ReminderDataResponseModel old=reminderResponseModel.getOld();
        old.setBill_date("2021-03-07 11:14:11");
        old.setAmount(200.00);
        old.setCustomerOtherInfo("{\"currentMinBillAmount\":20}");
        old.setCreated_at("2021-03-05 05:44:11");
        old.setUpdated_at("2021-03-05 06:44:11");

        List<ReminderHistory> reminderHistories=new ArrayList<ReminderHistory>();
        ReminderHistory reminderHistory=new ReminderHistory();
        try {
            reminderHistory.setDue_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(reminderResponseModel.getData().getDue_date()));
        }catch(Exception e) {}

        reminderHistory.setOriginalAmount(200.0);
        reminderHistory.setOriginalMinBillAmount(50.0);
        reminderHistory.setReference_id(reminderResponseModel.getData().getReference_id());

        reminderHistories.add(reminderHistory);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);
        when(reminderHistoryRepository.findByCustomerIdAndRechargeNumberAndService(reminderResponseModel.getData().getCustomer_id(), reminderResponseModel.getData().getRecharge_number(),reminderResponseModel.getData().getService())).thenReturn(reminderHistories);
        reminderResponseModel.setOperationType("update");
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());
        Integer isPartial = 1;
        assertEquals(reminderResponseModel.getData().getRecharge_number(), argument.getValue().getRechargeNumber());
        assertEquals(reminderResponseModel.getData().getOperator(), argument.getValue().getOperator());
        assertEquals(reminderResponseModel.getData().getAmount(), argument.getValue().getAmount());
        assertEquals(20.0, argument.getValue().getCurrentMinBillAmount());
        assertEquals(isPartial,argument.getValue().getIsPartial());
        assertEquals(200.0,argument.getValue().getOriginalAmount());
    }

    @Test
    public void testNewRentPaymentBill() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        when(commonService.getSmartReminderTTL(any())).thenReturn(5111);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());
        verify(customerBillDao).save(argument2.capture(),argument1.capture());
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(),nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********", rechargeNumber.getValue());
        assertEquals("**********", rechargeNumber7.getValue());
        assertEquals("********", argument.getValue().getRechargeNumber());
        assertEquals("********",argument2.getValue().getKey().getRechargeNumber());
    }

    @Test
    public void testNewRentPaymentBillMatchIFSC() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\"}");

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("rent payment");
        recentsPrimaryKey.setRechargeNumber("********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("rent payment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.TRUE);
        recents1.setRechargeNumber2("SBIN0000924");
        recents1.setRechargeNumber7("**********");
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********",rechargeNumber.getValue());
        assertEquals("**********",rechargeNumber7.getValue());

    }

    @Test
    public void testNewRentPaymentBillMatchIFSC2() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\"}");

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("rent payment");
        recentsPrimaryKey.setRechargeNumber("**********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("rent payment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0000924");
        recents1.setRechargeNumber7("********");
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********",rechargeNumber.getValue());
        assertEquals("**********",rechargeNumber7.getValue());
    }

    @Test
    public void testNewBusinessPaymentBill() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.setTable("bills_businesspayment");
        reminderResponseModel.getData().setService("business payment");
        reminderResponseModel.getData().setOperator("biller_businesspayment_bank_vendorpayment");
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("business payment");
        product.setOperator("biller_businesspayment_bank_vendorpayment");
        product.setCategoryId(300048L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(commonService.getSmartReminderTTL(any())).thenReturn(5111);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());
        verify(customerBillDao).save(argument2.capture(),argument1.capture());

        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********", rechargeNumber.getValue());
        assertEquals("**********", rechargeNumber7.getValue());
        assertEquals("********", argument.getValue().getRechargeNumber());
        assertEquals("********",argument2.getValue().getKey().getRechargeNumber());
    }


    @Test
    public void testNewBusinessPaymentBillIsAutomaticIs3() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.setTable("bills_businesspayment");
        reminderResponseModel.getData().setService("business payment");
        reminderResponseModel.getData().setOperator("biller_businesspayment_bank_vendorpayment");
        reminderResponseModel.getData().setIs_automatic(3);
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("business payment");
        product.setOperator("biller_businesspayment_bank_vendorpayment");
        product.setCategoryId(300048L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        when(commonService.getSmartReminderTTL(any())).thenReturn(5111);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);


        verify(reminderHistoryRepository).save(argument.capture(), argument1.capture());
        verify(customerBillDao).save(argument2.capture(),argument1.capture());

        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********", rechargeNumber.getValue());
        assertEquals("**********", rechargeNumber7.getValue());
        assertEquals("********", argument.getValue().getRechargeNumber());
        assertEquals("********",argument2.getValue().getKey().getRechargeNumber());
    }



    @Test
    public void testNewRentPaymentBillMissMatchIFSC2WithRechargeNumber7IsNull() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\"}");

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("rent payment");
        recentsPrimaryKey.setRechargeNumber("**********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("rent payment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0924");
        recents1.setRechargeNumber7("2675844");
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmnt = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);


        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmnt.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("**********",rechargeNumber.getValue());
        assertEquals(null,rechargeNumber7.getValue());
    }

    @Test
    public void testNewBusinessPaymentBillMissMatchIFSC2WithRechargeNumber7IsNull() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\"}");
        reminderResponseModel.setTable("bills_businesspayment");
        reminderResponseModel.getData().setService("business payment");
        reminderResponseModel.getData().setOperator("biller_businesspayment_bank_vendorpayment");
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("business payment");
        product.setOperator("biller_businesspayment_bank_vendorpayment");
        product.setCategoryId(300048L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("business payment");
        recentsPrimaryKey.setRechargeNumber("**********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("biller_businesspayment_bank_vendorpayment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0924");
        recents1.setRechargeNumber7("2675844");
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmnt = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmnt.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("**********",rechargeNumber.getValue());
        assertEquals(null,rechargeNumber7.getValue());
    }


    @Test
    public void testNewBusinessPaymentWithRechargeNumber7() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\",\"recharge_number_7\":\"2675844\"}");
        reminderResponseModel.setTable("bills_businesspayment");
        reminderResponseModel.getData().setService("business payment");
        reminderResponseModel.getData().setOperator("biller_businesspayment_bank_vendorpayment");
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("business payment");
        product.setOperator("biller_businesspayment_bank_vendorpayment");
        product.setCategoryId(300048L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("business payment");
        recentsPrimaryKey.setRechargeNumber("2675844");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("biller_businesspayment_bank_vendorpayment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0924");
        recents1.setRechargeNumber7("***********");
        recents1.setIsNewBiller(true);
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmnt = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmnt.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("2675844",rechargeNumber.getValue());
        assertEquals("**********",rechargeNumber7.getValue());
    }

    @Test
    public void testNewBusinessPaymentWithRechargeNumber7newBillerFalse() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\",\"recharge_number_7\":\"2675844\"}");
        reminderResponseModel.setTable("bills_businesspayment");
        reminderResponseModel.getData().setService("business payment");
        reminderResponseModel.getData().setOperator("biller_businesspayment_bank_vendorpayment");
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("business payment");
        product.setOperator("biller_businesspayment_bank_vendorpayment");
        product.setCategoryId(300048L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("business payment");
        recentsPrimaryKey.setRechargeNumber("2675844");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("biller_businesspayment_bank_vendorpayment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0924");
        recents1.setRechargeNumber7("***********");
        recents1.setIsNewBiller(null);
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmnt = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmnt.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());


        assertEquals("2675844",rechargeNumber.getValue());
        assertEquals("**********",rechargeNumber7.getValue());
    }

    @Test
    public void testNewBusinessPaymentWithRechargeNumber7isEmptynewBillerFalse() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\"}");
        reminderResponseModel.setTable("bills_businesspayment");
        reminderResponseModel.getData().setService("business payment");
        reminderResponseModel.getData().setOperator("biller_businesspayment_bank_vendorpayment");
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("business payment");
        product.setOperator("biller_businesspayment_bank_vendorpayment");
        product.setCategoryId(300048L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("business payment");
        recentsPrimaryKey.setRechargeNumber("***********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("biller_businesspayment_bank_vendorpayment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0924");
        recents1.setRechargeNumber7("********");
        recents1.setIsNewBiller(null);
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmnt = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmnt.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("**********",rechargeNumber.getValue());
        assertEquals(null,rechargeNumber7.getValue());
    }

    @Test
    public void testNewBusinessPaymentWithRechargeNumber7isEmpty() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\"}");
        reminderResponseModel.setTable("bills_businesspayment");
        reminderResponseModel.getData().setService("business payment");
        reminderResponseModel.getData().setOperator("biller_businesspayment_bank_vendorpayment");
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("business payment");
        product.setOperator("biller_businesspayment_bank_vendorpayment");
        product.setCategoryId(300048L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("business payment");
        recentsPrimaryKey.setRechargeNumber("***********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("biller_businesspayment_bank_vendorpayment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0924");
        recents1.setRechargeNumber7("2675844,");
        recents1.setIsNewBiller(null);
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmnt = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmnt.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("**********",rechargeNumber.getValue());
        assertEquals(null,rechargeNumber7.getValue());
    }

    @Test
    public void testNewRentPaymentWhenRechargeNumber7NotNull() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\",\"recharge_number_7\":\"********\"}");

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("rent payment");
        recentsPrimaryKey.setRechargeNumber("**********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("rent payment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0000924");
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmnt = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmnt.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********",rechargeNumber.getValue());
        assertEquals("**********",rechargeNumber7.getValue());
    }


    @Test
    public void testNewBusinessPaymentWhenRechargeNumber7NotNull() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\",\"recharge_number_7\":\"********\"}");

        reminderResponseModel.setTable("bills_businesspayment");
        reminderResponseModel.getData().setService("business payment");
        reminderResponseModel.getData().setOperator("biller_businesspayment_bank_vendorpayment");
        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("business payment");
        product.setOperator("biller_businesspayment_bank_vendorpayment");
        product.setCategoryId(300048L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("business payment");
        recentsPrimaryKey.setRechargeNumber("**********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("biller_businesspayment_bank_vendorpayment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.FALSE);
        recents1.setRechargeNumber2("SBIN0000924");
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmnt = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmnt.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********",rechargeNumber.getValue());
        assertEquals("**********",rechargeNumber7.getValue());
    }

    @Test
    public void testNewRentPaymentWhenRechargeNumber7NotNullNoRecent() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\",\"recharge_number_7\":\"********\"}");

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(null);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********",rechargeNumber.getValue());
        assertEquals("**********",rechargeNumber7.getValue());
    }

    @Test
    public void testNewRentPaymentWhenRechargeNumber7NullNoRecent() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\"}");

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(null);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(), earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("**********",rechargeNumber.getValue());
        assertEquals(null,rechargeNumber7.getValue());
    }

    @Test
    public void testNewRentPaymentWhenRechargeNumber7NotNull2() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\",\"recharge_number_7\":\"********\"}");

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("rent payment");
        recentsPrimaryKey.setRechargeNumber("********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("rent payment");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.TRUE);
        recents1.setRechargeNumber2("SBIN0000924");
        recents.add(recents1);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("********",rechargeNumber.getValue());
        assertEquals("**********",rechargeNumber7.getValue());
    }



    @Test
    public void testNewRentPaymentBillNoRecents() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseRentPaymentMock();
        reminderResponseModel.getData().setUser_data("{\"recharge_number_2\":\"SBIN0000924\",\"recharge_number_3\":\"Sakthi Kailash S\"}");

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("rent payment");
        product.setOperator("rent payment");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);

        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findByCustomerIdAndService(any(),any())).thenReturn(null);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals("**********",rechargeNumber.getValue());


    }
    @Test
    public void testInsertIntoCassandraSkippingForNotificationStatusZero() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setNotificationStatus(0);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository,times(0)).save(argument.capture(), argument1.capture());
    }

    @Test
    public void testInsertIntoCassandraNotSkippingPaytmPostPaid() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setNotificationStatus(1);
        reminderResponseModel.getData().setService("paytm postpaid");
        reminderResponseModel.getData().setCustomerOtherInfo("{\"customerId\":\"232161071\",\"subscriberNumber\":\"490013566990\",\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":2150,\"billDueDate\":\"2024-01-29\",\"billDate\":\"2024-01-08\",\"complianceRespCd\":\"\",\"responseCode\":\"000\",\"complianceReason\":\"\",\"refId\":\"VQTC4W6Q49U7DTCWBYT1VUOF5G940082327\",\"msgId\":\"O2J9XNTSNIY9V9QP5818K11FG3340082327\",\"customerName\":\"Veeribai Yudhisterlal Katariya\",\"billNumber\":\"\",\"billPeriod\":\"MONTHLY\",\"additionalFees\":{\"Early Payment Amount\":\"215000\",\"Late Payment Amount\":\"219000\"},\"earlyPaymentDate\":\"2024-01-17\",\"incentiveAmount\":20,\"incentiveDueDate\":\"2024-01-17\"}");
        reminderResponseModel.setOld(new ReminderDataResponseModel());
        reminderResponseModel.getOld().setUpdated_at("2021-03-05 06:44:11");
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository,times(1)).save(argument.capture(), argument1.capture());
    }
    @Test
    public void testInsertIntoCassandraNotSkippingPaytmPostPaidForOldDueDate() {
        String kafkaPacket="{\"database\":\"digital_reminder\",\"table\":\"bills_paytmpostpaid\",\"type\":\"update\",\"ts\":1699093428,\"xid\":16049929154,\"commit\":true,\"data\":{\"id\":2072786,\"customer_id\":146084972,\"recharge_number\":\"c1166807-20df-4cf5-9eb5-024952c56873\",\"product_id\":340214842,\"operator\":\"aditya birla finance ltd\",\"amount\":0.00,\"bill_date\":\"2023-11-01 02:03:27\",\"due_date\":\"2023-09-07 00:00:00\",\"bill_fetch_date\":\"2023-11-04 15:53:48\",\"next_bill_fetch_date\":null,\"gateway\":null,\"paytype\":\"postpaid\",\"service\":\"paytm postpaid\",\"circle\":null,\"customer_mobile\":null,\"customer_email\":null,\"payment_channel\":null,\"retry_count\":0,\"status\":11,\"reason\":null,\"extra\":\"{\\\"billSource\\\":\\\"paytmPostpaid-2023-11-04 15:53:48\\\",\\\"lastSuccessBFD\\\":\\\"2023-11-01T02:45:05.000Z\\\",\\\"billFetchDate\\\":\\\"2023-11-04 15:53:48\\\",\\\"lastDueDt\\\":\\\"2023-09-07T00:00:00.000Z\\\",\\\"lastBillDt\\\":\\\"2023-11-01T02:03:27.000Z\\\",\\\"lastAmount\\\":3.6,\\\"customer_type\\\":null}\",\"published_date\":null,\"created_at\":\"2023-08-31 20:55:55\",\"updated_at\":\"2023-11-04 10:23:48\",\"user_data\":null,\"notification_status\":1,\"payment_date\":null,\"service_id\":0,\"customerOtherInfo\":\"{\\\"customerId\\\":146084972,\\\"rechargeNumber\\\":\\\"c1166807-20df-4cf5-9eb5-024952c56873\\\",\\\"currentBillAmount\\\":0,\\\"dueAmount\\\":0,\\\"unBilledAmount\\\":0,\\\"dueDate\\\":\\\"2023-09-07 00:00:00\\\",\\\"billDate\\\":\\\"2023-11-01 02:03:27\\\",\\\"nextBillFetchDate\\\":null,\\\"billFetchDate\\\":\\\"2023-11-04 15:53:48\\\",\\\"productId\\\":340214842,\\\"productType\\\":\\\"POSTPAID\\\",\\\"custMobile\\\":null,\\\"eventType\\\":null,\\\"billGen\\\":false,\\\"operator\\\":\\\"aditya birla finance ltd\\\",\\\"service\\\":\\\"paytm postpaid\\\",\\\"paytype\\\":\\\"postpaid\\\"}\",\"is_automatic\":0},\"old\":{\"amount\":3.60,\"bill_fetch_date\":\"2023-11-01 02:45:05\",\"next_bill_fetch_date\":\"2023-12-01 00:00:00\",\"status\":4,\"extra\":\"{\\\"billSource\\\":\\\"paytmPostpaid-2023-11-01 02:45:05\\\",\\\"lastSuccessBFD\\\":\\\"2023-10-01T02:14:44.000Z\\\",\\\"billFetchDate\\\":\\\"2023-11-01 02:45:05\\\",\\\"lastDueDt\\\":\\\"2023-09-07T00:00:00.000Z\\\",\\\"lastBillDt\\\":\\\"2023-10-01T01:47:40.000Z\\\",\\\"lastAmount\\\":3.6,\\\"customer_type\\\":null}\",\"updated_at\":\"2023-10-31 21:15:05\",\"customerOtherInfo\":\"{\\\"customerId\\\":146084972,\\\"rechargeNumber\\\":\\\"c1166807-20df-4cf5-9eb5-024952c56873\\\",\\\"currentBillAmount\\\":0,\\\"dueAmount\\\":3.6,\\\"unBilledAmount\\\":0.4,\\\"dueDate\\\":\\\"2023-09-07 00:00:00\\\",\\\"billDate\\\":\\\"2023-11-01 02:03:27\\\",\\\"nextBillFetchDate\\\":\\\"2023-12-01 00:00:00\\\",\\\"billFetchDate\\\":\\\"2023-11-01 02:45:05\\\",\\\"productId\\\":340214842,\\\"productType\\\":\\\"POSTPAID\\\",\\\"custMobile\\\":null,\\\"eventType\\\":\\\"BILL_GEN\\\",\\\"billGen\\\":true,\\\"operator\\\":\\\"aditya birla finance ltd\\\",\\\"service\\\":\\\"paytm postpaid\\\",\\\"paytype\\\":\\\"postpaid\\\"}\"}}";
        ReminderResponseModel reminderResponseModel = JsonUtils.parseJson(kafkaPacket,ReminderResponseModel.class);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository,times(1)).save(argument.capture(), argument1.capture());
    }

    @Test
    public void testInsertIntoCassandraSkippingIfDataSourceNonNull() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setNotificationStatus(1);
        reminderResponseModel.getData().setService("paytm postpaid");
        reminderResponseModel.getData().setDataSource("ru_sms");
        reminderResponseModel.setOld(new ReminderDataResponseModel());
        reminderResponseModel.getOld().setUpdated_at("2021-03-05 06:44:11");
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository,times(0)).save(argument.capture(), argument1.capture());
    }
    @Test
    public void testInsertIntoCassandraSkippingForNotificationStatusTwo() {

        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setNotificationStatus(2);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);

        verify(reminderHistoryRepository,times(0)).save(argument.capture(), argument1.capture());
    }
    @Test
    public void testUpsertIntoRecentsWhenPaymentDateNull() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setPaymentDate(null);
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());



        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals(null, newBillUpdatedAt.getValue());
    }
    @Test
    public void testUpsertIntoRecentsWhenNewBillUpdated() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e){};
        reminderResponseModel.getOld().setDue_date(dateString);
        reminderResponseModel.getData().setPaymentDate(null);
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertNotNull(newBillUpdatedAt.getValue());
    }
    @Test
    public void testUpsertIntoRecentsWhenNewBillUpdated2() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = "";
        try{
            dateString = dateformat.format(date_now_minus_10_days);
        } catch (Exception e){};
        reminderResponseModel.getOld().setDue_date(dateString);
        reminderResponseModel.getData().setPaymentDate(null);
        reminderResponseModel.getOld().setAmount(null);
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertNotNull(newBillUpdatedAt.getValue());
    }
    @Test
    public void testUpsertIntoRecentsWhenEventSourceTxn() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"transaction\",\"created_source\":\"transaction\"}");
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(), reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals(null,eventSource.getValue());
    }
    @Test
    public void testUpsertIntoRecentsWhenEventSourceNotTxn() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"validation\",\"created_source\":\"validation\"}");
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals("validation",eventSource.getValue());
    }
    @Test
    public void testUpsertIntoRecentsWhenUpdatedSourceValidation() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"validationSync\",\"created_source\":\"transaction\"}");
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals("true",isValidation.getValue().toString());
    }
    @Test
    public void testUpsertIntoRecentsWhenUpdatedSourceSavedCard() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"savedCard\",\"created_source\":\"transaction\"}");
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals("true",isSavedCard.getValue().toString());
    }
    @Test
    public void testUpsertIntoRecentsWhenUpdatedSourceTransaction() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"transaction\",\"created_source\":\"transaction\"}");
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals("true",isTxn.getValue().toString());
    }

    @Test
    public void testExcludedCustId() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaCCResponseMock();
        reminderResponseModel.getData().setCustomer_id(243003266L);
        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        verify(reminderHistoryRepository,times(0)).save(argument.capture(), argument1.capture());
    }


    @Test
    public void testNewTuitionNoRecentExists() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseTuitionFeeMock();

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("tuition fee");
        product.setOperator("tuition fee");
        product.setCategoryId(215903L);
        product.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product);


        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(any(),any(),any())).thenReturn(null);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(Long.valueOf(1201345018),product_id.getValue());
        assertEquals("tuition fee",operator.getValue());
    }

    @Test
    public void testIsSkipableForSyncBillAutomatic1() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = dateformat.format(date_now_minus_10_days);
        reminderResponseModel.getData().setDue_date(dateString);
        reminderResponseModel.getData().setBill_date("2021-03-05 15:44:11");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setUpdated_at(dateformat.format(new Date()));
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);
        assertEquals(new Boolean(false), res);
    }

    @Test
    public void testIsSkipableForSyncBillAutomatic2() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = dateformat.format(date_now_minus_10_days);
        reminderResponseModel.getData().setDue_date(dateString);
        reminderResponseModel.getData().setBill_date("2021-03-05 15:44:11");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setUpdated_at(dateformat.format(new Date()));
        oldData.setIs_automatic(1);
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);
        assertEquals(new Boolean(false), res);
    }

    @Test
    public void testIsSkipableForSyncBillAutomatic3() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = dateformat.format(date_now_minus_10_days);
        reminderResponseModel.getData().setDue_date(null);
        reminderResponseModel.getData().setBill_date("2021-03-05 15:44:11");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setUpdated_at(dateformat.format(new Date()));
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);
        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableForSyncBillAutomatic4() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = dateformat.format(date_now_minus_10_days);
        reminderResponseModel.getData().setDue_date(dateString);
        reminderResponseModel.getData().setService("A");
        reminderResponseModel.getData().setBill_date("2021-03-05 15:44:11");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setUpdated_at(dateformat.format(new Date()));
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);
        assertEquals(new Boolean(true), res);
    }

    @Test
    public void testIsSkipableForSyncBillAutomatic5() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = dateformat.format(date_now_minus_10_days);
        reminderResponseModel.getData().setDue_date(dateString);
        reminderResponseModel.getData().setService("insurance");
        reminderResponseModel.getData().setBill_date("2021-03-05 15:44:11");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setUpdated_at(dateformat.format(new Date()));
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);
        assertEquals(new Boolean(false), res);
    }


    @Test
    public void testIsSkipableForSyncBillAutomatic6() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = dateformat.format(date_now_minus_10_days);
        reminderResponseModel.getData().setDue_date(dateString);
        reminderResponseModel.getData().setService("mobile");
        reminderResponseModel.getData().setPaytype("prepaid");
        reminderResponseModel.getData().setBill_date("2021-03-05 15:44:11");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setUpdated_at(dateformat.format(new Date()));
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);
        assertEquals(new Boolean(true), res);
    }


    @Test
    public void testIsSkipableForSyncBillAutomatic7() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        String dateString = dateformat.format(date_now_minus_10_days);
        reminderResponseModel.getData().setDue_date(dateString);
        reminderResponseModel.getData().setService("insurance");
        reminderResponseModel.getData().setBill_date("2021-03-05 15:44:11");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        reminderResponseModel.setOld(oldData);
        Boolean res = reminderListeners.isSkipable(reminderResponseModel, false);
        assertEquals(new Boolean(true), res);
    }







    @Test
    public void testNewTuitionRecentExists() throws ReminderListenerException {

        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseTuitionFeeMock();

        Product product = new Product();
        product.setProductId(1201345018L);
        product.setService("tuition fees");
        product.setOperator("tuition fee");
        product.setCategoryId(215903L);
        product.setStatus(1);

        Product product1 = new Product();
        product1.setProductId(13456L);
        product1.setService("tuition fees");
        product1.setOperator("school tuition fee");
        product1.setCategoryId(215903L);
        product1.setStatus(1);
        CVRProductCache.getInstance().addProductDetails(product1);


        List<Recents> recents=new ArrayList<>();
        Recents recents1=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setService("tuition fees");
        recentsPrimaryKey.setRechargeNumber("********");
        recentsPrimaryKey.setCustomerId(100L);
        recentsPrimaryKey.setPlanBucket("");
        recentsPrimaryKey.setOperator("shcool tuition fee");
        recents1.setKey(recentsPrimaryKey);
        recents1.setDueDate(new Date());
        recents1.setDueAmount(100.0);
        recents1.setUpdatedAt(new Date());
        recents1.setRentTFData("new biller info");
        recents1.setIsNewBiller(Boolean.TRUE);
        recents1.setRechargeNumber2("SBIN0000924");
        recents1.setProductId(13456L);
        recents.add(recents1);


        ArgumentCaptor<ReminderHistory> argument = ArgumentCaptor.forClass(ReminderHistory.class);
        ArgumentCaptor<Integer> argument1 = ArgumentCaptor.forClass(Integer.class);

        reminderResponseModel.setOperationType("insert");

        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<CustomerBill> argument2 = ArgumentCaptor.forClass(CustomerBill.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);

        when(recentsRepository.findBycustomerIdAndserviceAndrechargeNumber(any(),any(),any())).thenReturn(recents);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);


        verify(recentService,times(1)).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(), nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());

        assertEquals(Long.valueOf(13456),product_id.getValue());
        assertEquals("shcool tuition fee",operator.getValue());
    }

    @Test
    public void getPartialSMSFlagWhenReminderResponseIsNull() {
        String message = null;
        ReminderResponseModel reminderResponseModel = null;
        Boolean res = reminderListeners.getPartialSMSFlag(reminderResponseModel, message);
        assertEquals(new Boolean(false), res);
    }

    @Test
    public void getPartialSMSFlagWhenDueAmountIsUpdated() {
        String message = "{\"database\":\"digital_reminder\",\"table\":\"bills_creditcard\",\"type\":\"update\",\"ts\":**********,\"xid\":*********,\"commit\":true,\"data\":{\"id\":2029770,\"customer_id\":**********,\"recharge_number\":\"123456XXXXXX1201\",\"product_id\":**********,\"reference_id\":\"V0010013021313533662636897980\",\"operator\":\"neft_hdfcbank\",\"amount\":0.0,\"bill_date\":\"2023-08-1200:00:00\",\"due_date\":\"2023-10-1400:00:00\",\"bill_fetch_date\":\"2023-10-0613:04:36\",\"next_bill_fetch_date\":null,\"gateway\":null,\"paytype\":\"creditcard\",\"service\":\"financialservices\",\"circle\":\"\",\"customer_mobile\":null,\"customer_email\":null,\"payment_channel\":null,\"retry_count\":0,\"status\":4,\"reason\":null,\"extra\":\"{\\\"updated_source\\\":\\\"sms\\\"}\",\"published_date\":null,\"created_at\":\"2022-07-1507:12:50\",\"updated_at\":\"2023-10-0607:33:41\",\"user_data\":null,\"notification_status\":1,\"payment_date\":\"2023-08-1000:00:00\",\"service_id\":0,\"customerOtherInfo\":\"{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":null,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":\\\"2023-10-1400:00:00\\\",\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0613:04:36\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"e6aa56d0-6448-11ee-8017-a64ed43e01fb\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}\",\"is_automatic\":0,\"par_id\":\"V0010013021313533662636897980\",\"tin\":null,\"bank_name\":\"HDFC\",\"card_network\":\"Visa\"},\"old\":{\"amount\":null,\"bill_fetch_date\":\"2023-10-0513:02:03\",\"updated_at\":\"2023-10-0507:31:09\"}}";

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"sms\"}");
        Boolean res = reminderListeners.getPartialSMSFlag(reminderResponseModel, message);
        assertEquals(new Boolean(true), res);
    }

    @Test
    public void getPartialSMSFlagWhenMinDueAmountIsUpdated() {
        String message = "{\"database\":\"digital_reminder\",\"table\":\"bills_creditcard\",\"type\":\"update\",\"ts\":**********,\"xid\":*********,\"commit\":true,\"data\":{\"id\":2029770,\"customer_id\":**********,\"recharge_number\":\"123456XXXXXX1201\",\"product_id\":**********,\"reference_id\":\"V0010013021313533662636897980\",\"operator\":\"neft_hdfcbank\",\"amount\":0.0,\"bill_date\":\"2023-08-1200:00:00\",\"due_date\":\"2023-10-1400:00:00\",\"bill_fetch_date\":\"2023-10-0613:04:36\",\"next_bill_fetch_date\":null,\"gateway\":null,\"paytype\":\"creditcard\",\"service\":\"financialservices\",\"circle\":\"\",\"customer_mobile\":null,\"customer_email\":null,\"payment_channel\":null,\"retry_count\":0,\"status\":4,\"reason\":null,\"extra\":\"{\\\"updated_source\\\":\\\"sms\\\"}\",\"published_date\":null,\"created_at\":\"2022-07-1507:12:50\",\"updated_at\":\"2023-10-0607:33:41\",\"user_data\":null,\"notification_status\":1,\"payment_date\":\"2023-08-1000:00:00\",\"service_id\":0,\"customerOtherInfo\":\"{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":123.0,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":\\\"2023-10-1400:00:00\\\",\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0613:04:36\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"e6aa56d0-6448-11ee-8017-a64ed43e01fb\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}\",\"is_automatic\":0,\"par_id\":\"V0010013021313533662636897980\",\"tin\":null,\"bank_name\":\"HDFC\",\"card_network\":\"Visa\"},\"old\":{\"customerOtherInfo\":\"{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":null,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":null,\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0513:02:03\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"612a1b90-637f-11ee-8855-4961b7c02700\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}\",\"bill_fetch_date\":\"2023-10-0513:02:03\",\"updated_at\":\"2023-10-0507:31:09\"}}";

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setCustomerOtherInfo("{\"currentMinBillAmount\":10.0, \"customerId\":12345}");
        reminderResponseModel.getOld().setCustomerOtherInfo("{\"currentMinBillAmount\":12.0, \"customerId\":12345}");
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"sms\"}");
        Boolean res = reminderListeners.getPartialSMSFlag(reminderResponseModel, message);
        assertEquals(new Boolean(false), res);
    }

    @Test
    public void getPartialSMSFlagWhenOldCustomerOtherInfoIsNotPresent() {
        String message = "{\"database\":\"digital_reminder\",\"table\":\"bills_creditcard\",\"type\":\"update\",\"ts\":**********,\"xid\":*********,\"commit\":true,\"data\":{\"id\":2029770,\"customer_id\":**********,\"recharge_number\":\"123456XXXXXX1201\",\"product_id\":**********,\"reference_id\":\"V0010013021313533662636897980\",\"operator\":\"neft_hdfcbank\",\"amount\":0.0,\"bill_date\":\"2023-08-1200:00:00\",\"due_date\":\"2023-10-1400:00:00\",\"bill_fetch_date\":\"2023-10-0613:04:36\",\"next_bill_fetch_date\":null,\"gateway\":null,\"paytype\":\"creditcard\",\"service\":\"financialservices\",\"circle\":\"\",\"customer_mobile\":null,\"customer_email\":null,\"payment_channel\":null,\"retry_count\":0,\"status\":4,\"reason\":null,\"extra\":\"{\\\"updated_source\\\":\\\"sms\\\"}\",\"published_date\":null,\"created_at\":\"2022-07-1507:12:50\",\"updated_at\":\"2023-10-0607:33:41\",\"user_data\":null,\"notification_status\":1,\"payment_date\":\"2023-08-1000:00:00\",\"service_id\":0,\"customerOtherInfo\":\"{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":null,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":\\\"2023-10-1400:00:00\\\",\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0613:04:36\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"e6aa56d0-6448-11ee-8017-a64ed43e01fb\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}\",\"is_automatic\":0,\"par_id\":\"V0010013021313533662636897980\",\"tin\":null,\"bank_name\":\"HDFC\",\"card_network\":\"Visa\"},\"old\":{\"customerOtherInfo\":\"{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":null,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":null,\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0513:02:03\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"612a1b90-637f-11ee-8855-4961b7c02700\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}\",\"bill_fetch_date\":\"2023-10-0513:02:03\",\"updated_at\":\"2023-10-0507:31:09\"}}";

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getOld().setCustomerOtherInfo("{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":null,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":null,\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0513:02:03\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"612a1b90-637f-11ee-8855-4961b7c02700\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}");
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"sms\"}");
        Boolean res = reminderListeners.getPartialSMSFlag(reminderResponseModel, message);
        assertEquals(new Boolean(false), res);
    }

    @Test
    public void getPartialSMSFlagWhenNewCustomerOtherInfoIsNotPresent() {
        String message = "{\"database\":\"digital_reminder\",\"table\":\"bills_creditcard\",\"type\":\"update\",\"ts\":**********,\"xid\":*********,\"commit\":true,\"data\":{\"id\":2029770,\"customer_id\":**********,\"recharge_number\":\"123456XXXXXX1201\",\"product_id\":**********,\"reference_id\":\"V0010013021313533662636897980\",\"operator\":\"neft_hdfcbank\",\"amount\":0.0,\"bill_date\":\"2023-08-1200:00:00\",\"due_date\":\"2023-10-1400:00:00\",\"bill_fetch_date\":\"2023-10-0613:04:36\",\"next_bill_fetch_date\":null,\"gateway\":null,\"paytype\":\"creditcard\",\"service\":\"financialservices\",\"circle\":\"\",\"customer_mobile\":null,\"customer_email\":null,\"payment_channel\":null,\"retry_count\":0,\"status\":4,\"reason\":null,\"extra\":\"{\\\"updated_source\\\":\\\"sms\\\"}\",\"published_date\":null,\"created_at\":\"2022-07-1507:12:50\",\"updated_at\":\"2023-10-0607:33:41\",\"user_data\":null,\"notification_status\":1,\"payment_date\":\"2023-08-1000:00:00\",\"service_id\":0,\"customerOtherInfo\":\"{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":null,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":\\\"2023-10-1400:00:00\\\",\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0613:04:36\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"e6aa56d0-6448-11ee-8017-a64ed43e01fb\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}\",\"is_automatic\":0,\"par_id\":\"V0010013021313533662636897980\",\"tin\":null,\"bank_name\":\"HDFC\",\"card_network\":\"Visa\"},\"old\":{\"customerOtherInfo\":\"{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":null,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":null,\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0513:02:03\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"612a1b90-637f-11ee-8855-4961b7c02700\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}\",\"bill_fetch_date\":\"2023-10-0513:02:03\",\"updated_at\":\"2023-10-0507:31:09\"}}";

        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setCustomerOtherInfo("{\\\"customerId\\\":**********,\\\"lastCC\\\":\\\"1201\\\",\\\"currentBillAmount\\\":0,\\\"currentMinBillAmount\\\":10.0,\\\"billDate\\\":\\\"2023-08-1200:00:00\\\",\\\"billDueDate\\\":\\\"2023-10-1400:00:00\\\",\\\"smsSenderID\\\":\\\"AD-HDFCBK\\\",\\\"bankName\\\":\\\"HDFC\\\",\\\"billConsumeTimestamp\\\":\\\"2023-10-0613:04:36\\\",\\\"msgId\\\":\\\"\\\",\\\"appCount\\\":null,\\\"appVersion\\\":\\\"\\\",\\\"rtspId\\\":-1,\\\"category\\\":null,\\\"refId\\\":\\\"e6aa56d0-6448-11ee-8017-a64ed43e01fb\\\",\\\"smsDateTime\\\":*************,\\\"debugKey\\\":\\\"smsSenderID:AD-HDFCBK_custId:**********_lastCC:1201_Id:2029770_MCN:123456XXXXXX1201_operator:neft_hdfcbank\\\"}");
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"sms\"}");
        Boolean res = reminderListeners.getPartialSMSFlag(reminderResponseModel, message);
        assertEquals(new Boolean(false), res);
    }

    @Test
    public void getPartialSMSFlagWhenNewExtraFieldIsNotPresent() {
        String message = null;
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setExtra(null);
        Boolean res = reminderListeners.getPartialSMSFlag(reminderResponseModel, message);
        assertEquals(new Boolean(false), res);
    }


    @Test
    public void notPaidOnPaytmAmountAndStatusCheckTrueCase() {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setAmount(0.0);
        reminderResponseModel.getData().setStatus(6);

        Boolean res = reminderListeners.notPaidOnPaytmAmountAndStatusCheck(reminderResponseModel);
        assertEquals(new Boolean(true), res);
}

    @Test
    public void notPaidOnPaytmAmountAndStatusCheckFalseCase() {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setAmount(10.0);
        reminderResponseModel.getData().setStatus(6);

        Boolean res = reminderListeners.notPaidOnPaytmAmountAndStatusCheck(reminderResponseModel);
        assertEquals(new Boolean(false), res);
    }

    @Test
    public void compareUniqueCCIdWhenParIsPResent() {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setPar("qw123");

        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setPar("qw123");

        Boolean res =  reminderListeners.compareUniqueCCId(reminderResponseModel, reminderHistory);
        assertEquals(new Boolean(true), res);
    }

    @Test
    public void compareUniqueCCIdWhenReferenceIdIsPresent() {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setReference_id("qw123");

        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setReference_id("qw123");

        Boolean res = reminderListeners.compareUniqueCCId(reminderResponseModel, reminderHistory);
        assertEquals(new Boolean(true), res);
    }

    @Test
    public void compareUniqueCCIdFalseCase() {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel reminderData = new ReminderDataResponseModel();
        reminderResponseModel.setData(reminderData);
        reminderResponseModel.setOld(reminderData);
        reminderResponseModel.getData().setReference_id("123");

        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setReference_id("qw123");

        Boolean res = reminderListeners.compareUniqueCCId(reminderResponseModel, reminderHistory);
        assertEquals(new Boolean(false), res);
    }

    @Test
    public void ReminderListenerListen() {
        List<String> input = new ArrayList<>();
        input.add("{\"database\":\"digital_reminder\",\"table\":\"bills_pvvnl\",\"type\":\"update\",\"ts\":1725780940,\"xid\":56786341079,\"commit\":true,\"data\":{\"id\":12140943,\"customer_id\":189076287,\"recharge_number\":\"3718713000\",\"product_id\":467044907,\"operator\":\"paschimanchal vidyut vitran nigam limited (pvvnl)\",\"amount\":7310.00,\"bill_date\":\"2024-09-08 00:00:00\",\"due_date\":\"2024-09-15 00:00:00\",\"bill_fetch_date\":\"2024-09-08 13:05:39\",\"next_bill_fetch_date\":\"2024-09-18 00:00:00\",\"gateway\":\"euronetpostpaid\",\"paytype\":\"postpaid\",\"service\":\"electricity\",\"circle\":\"\",\"customer_mobile\":\"9818196852\",\"customer_email\":\"\",\"payment_channel\":null,\"retry_count\":0,\"status\":4,\"reason\":\"\",\"extra\":\"{\\\"recon_id\\\":\\\"hEzXNHF08Q2dbkOsD4hBsz9glSI=\\\",\\\"lastDueDt\\\":\\\"2024-08-19 05:30:00\\\",\\\"user_type\\\":\\\"RU\\\",\\\"billSource\\\":\\\"ffr\\\",\\\"lastAmount\\\":7310,\\\"lastBillDt\\\":\\\"2024-08-12 05:30:00\\\",\\\"billFetchDate\\\":\\\"2024-09-08 13:05:39\\\",\\\"customer_type\\\":null,\\\"created_source\\\":\\\"validationSync\\\",\\\"lastSuccessBFD\\\":\\\"2024-08-13 07:15:47\\\",\\\"updated_source\\\":\\\"sms\\\",\\\"errorMessageCode\\\":null,\\\"isAmountEditable\\\":true,\\\"source_subtype_2\\\":\\\"FULL_BILL\\\",\\\"updated_data_source\\\":\\\"ffr\\\",\\\"frontendErrorMessage\\\":null,\\\"isGroupDisplayEnabled\\\":false}\",\"published_date\":\"2024-09-08 13:05:36\",\"created_at\":\"2024-06-20 20:35:01\",\"updated_at\":\"2024-09-08 07:35:40\",\"user_data\":\"{}\",\"notification_status\":1,\"payment_date\":null,\"service_id\":0,\"customerOtherInfo\":\"{\\\"customerId\\\":\\\"0\\\",\\\"subscriberNumber\\\":\\\"3718713000\\\",\\\"subscriberName\\\":null,\\\"subscriberEmailId\\\":null,\\\"subscriberDOB\\\":null,\\\"subscriberAltNumber\\\":null,\\\"subscriberAddress\\\":null,\\\"subscriberGender\\\":null,\\\"subscriberCity\\\":null,\\\"minReloadAmount\\\":null,\\\"currentBillAmount\\\":\\\"7310\\\",\\\"billDueDate\\\":\\\"2024-09-15\\\",\\\"billDate\\\":\\\"2024-09-08\\\",\\\"isAmountEditable\\\":\\\"1\\\",\\\"isGroupDisplayEnabled\\\":\\\"0\\\"}\",\"is_automatic\":0,\"old_bill_fetch_date\":null,\"remind_later_date\":null},\"old\":{\"amount\":3691.00,\"bill_date\":\"2024-08-12 00:00:00\",\"due_date\":\"2024-08-19 00:00:00\",\"bill_fetch_date\":\"2024-08-13 07:15:47\",\"next_bill_fetch_date\":\"2024-09-09 00:00:00\",\"retry_count\":1,\"status\":1,\"extra\":\"{\\\"recon_id\\\":\\\"gW69qoz6RPtevZLW5i6gmNHEPf0=\\\",\\\"lastDueDt\\\":\\\"2024-07-14 05:30:00\\\",\\\"user_type\\\":\\\"RU\\\",\\\"billSource\\\":\\\"ffr\\\",\\\"lastAmount\\\":3656,\\\"lastBillDt\\\":\\\"2024-07-07 05:30:00\\\",\\\"billFetchDate\\\":\\\"2024-08-13 07:15:47\\\",\\\"customer_type\\\":null,\\\"created_source\\\":\\\"validationSync\\\",\\\"lastSuccessBFD\\\":\\\"2024-07-07 12:46:59\\\",\\\"updated_source\\\":\\\"sms\\\",\\\"errorMessageCode\\\":null,\\\"isAmountEditable\\\":null,\\\"source_subtype_2\\\":\\\"FULL_BILL\\\",\\\"updated_data_source\\\":\\\"ffr\\\",\\\"frontendErrorMessage\\\":null,\\\"isGroupDisplayEnabled\\\":null}\",\"updated_at\":\"2024-09-08 07:35:36\",\"customerOtherInfo\":null}}");

        reminderListeners.listen(input, new Acknowledgment() {
            @Override
            public void acknowledge() {

            }
        });
    }

    @Test
    public void testSuntvReminderListener() throws ReminderListenerException, ParseException {
        Product product = new Product();
        product.setProductId(1235420470L);
        product.setService("dth");
        product.setOperator("suntv");
        product.setCategoryId(1235420470L);
        product.setStatus(1);
        product.setAttributes("{\"min_amount\":\"10.0\"}");
        CVRProductCache.getInstance().addProductDetails(product);

        List<String> input = new ArrayList<>();
        input.add("{\"database\":\"digital_reminder\",\"table\":\"bills_suntv\",\"type\":\"insert\",\"ts\":1726130680,\"xid\":229139,\"commit\":true,\"data\":{\"id\":14,\"customer_id\":1887,\"recharge_number\":\"02811479599\",\"product_id\":1235420470,\"operator\":\"suntv\",\"amount\":null,\"bill_date\":null,\"due_date\":\"2024-09-30 00:00:00\",\"bill_fetch_date\":\"2024-09-10 16:12:58\",\"next_bill_fetch_date\":\"2024-09-23 23:59:59\",\"gateway\":\"euronetprepaid\",\"paytype\":\"prepaid\",\"service\":\"dth\",\"circle\":\"\",\"customer_mobile\":\"\",\"customer_email\":\"\",\"payment_channel\":null,\"retry_count\":0,\"status\":4,\"reason\":null,\"extra\":\"{\\\"lastSuccessBFD\\\":null,\\\"billFetchDate\\\":\\\"2024-09-10 16:12:58\\\",\\\"lastDueDt\\\":null,\\\"lastBillDt\\\":null,\\\"lastAmount\\\":null,\\\"billSource\\\":\\\"ValidationSync-2024-09-12 14:14:40\\\",\\\"customer_type\\\":null,\\\"updated_source\\\":\\\"validationSync\\\",\\\"updated_data_source\\\":\\\"validationSync\\\",\\\"created_source\\\":\\\"validationSync\\\",\\\"source_subtype_2\\\":\\\"FULL_BILL\\\",\\\"isGroupDisplayEnabled\\\":null,\\\"isAmountEditable\\\":null,\\\"recon_id\\\":\\\"1VxXbxE7CffLk7zpLidV12mDl9U=\\\",\\\"user_type\\\":\\\"RU\\\"}\",\"published_date\":null,\"created_at\":\"2024-09-12 08:44:40\",\"updated_at\":\"2024-09-12 08:44:40\",\"user_data\":\"{}\",\"notification_status\":1,\"payment_date\":null,\"service_id\":4,\"customerOtherInfo\":\"{\\\"customerId\\\":\\\"1883\\\",\\\"subscriberNumber\\\":\\\"222222220004\\\",\\\"subscriberName\\\":null,\\\"subscriberEmailId\\\":null,\\\"subscriberDOB\\\":null,\\\"subscriberAltNumber\\\":null,\\\"subscriberAddress\\\":null,\\\"subscriberGender\\\":null,\\\"subscriberCity\\\":null,\\\"minReloadAmount\\\":null,\\\"currentBillAmount\\\":null,\\\"billDueDate\\\":\\\"2024-09-30\\\"}\",\"is_automatic\":0,\"remind_later_date\":null}}");

        reminderListeners.listen(input, new Acknowledgment() {
            @Override
            public void acknowledge() {

            }
        });

    }

    @Test
    public void testUpsertIntoRecentsWhenEventSourceSMSAfterTxn() throws Exception {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        reminderResponseModel.getData().setExtra("{\"updated_source\":\"validation\",\"created_source\":\"sms\"}");
        //ArgumentCaptor<Recents> argument = ArgumentCaptor.forClass(Recents.class);
        ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> par = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> tin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_tokenized_transaction = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> billDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> dueDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> original_min_due_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> current_outstanding_amount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Long> product_id = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> automatic_status = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Boolean> is_mark_as_paid = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> mark_as_paid_time = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber2 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber3 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber4 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber5 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber6 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber7 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumber8 = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> circle = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> mcn = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> notPaidOnPaytm = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> txnDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> txnAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Double> markAsPaidAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<Date> newBillUpdatedAt = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<ReminderResponseModel> reminderResponseModel1 = ArgumentCaptor.forClass(ReminderResponseModel.class);
        ArgumentCaptor<Integer> notificationStatus = ArgumentCaptor.forClass(Integer.class);
        reminderListeners.insertDataIntoCassandra(reminderResponseModel, false);
        ArgumentCaptor<Boolean> isSavedCard = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isValidation = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Boolean> isTxn = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<String> eventSource = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> is_new_biller = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Date> earlyPaymentDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Double> earlyPaymentAmount = ArgumentCaptor.forClass(Double.class);
        ArgumentCaptor<String> reconId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pgCardId = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Boolean> nextBillFetchDateFlag = ArgumentCaptor.forClass(Boolean.class);
        ArgumentCaptor<Integer> reminderStatus = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Date> oldBillFetchDate = ArgumentCaptor.forClass(Date.class);

        ArgumentCaptor<String> cardVariant = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cardSkin = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> extra = ArgumentCaptor.forClass(String.class);

        ArgumentCaptor<Date> remindLaterDate = ArgumentCaptor.forClass(Date.class);
        ArgumentCaptor<Date> consentValidTill = ArgumentCaptor.forClass(Date.class);



        verify(recentService).updateRecentBillFromReminderListener(customerid.capture(), service.capture(),
                operator.capture(), plan_bucket.capture(), cin.capture(), par.capture(), tin.capture(),
                is_tokenized_transaction.capture(), billDate.capture(),
                dueDate.capture(), due_amount.capture(), min_due_amount.capture(), original_due_amount.capture(),
                original_min_due_amount.capture(), product_id.capture(),
                automatic_status.capture(), bill_update_time.capture(),
                is_mark_as_paid.capture(), mark_as_paid_time.capture(),markAsPaidAmount.capture(),
                updated_at.capture(), rechargeNumber.capture(),
                rechargeNumber2.capture(), rechargeNumber3.capture(),
                rechargeNumber4.capture(), rechargeNumber5.capture(),
                rechargeNumber6.capture(), rechargeNumber7.capture(),rechargeNumber8.capture(),
                circle.capture(),paytype.capture(), mcn.capture(), notificationStatus.capture(),notPaidOnPaytm.capture(),txnDate.capture(),txnAmount.capture(),newBillUpdatedAt.capture(),isSavedCard.capture(),isValidation.capture(),isTxn.capture(),eventSource.capture(),reminderResponseModel1.capture(),is_new_biller.capture(),earlyPaymentDate.capture(),earlyPaymentAmount.capture(),pgCardId.capture(),reconId.capture(),nextBillFetchDateFlag.capture(),reminderStatus.capture(),oldBillFetchDate.capture(),cardVariant.capture(),cardSkin.capture(),extra.capture(),remindLaterDate.capture(),original_min_due_amount.capture(), consentValidTill.capture());
        assertEquals(reminderResponseModel.getData().getCustomer_id(), customerid.getValue());
        assertEquals(reminderResponseModel.getData().getService(), service.getValue());
        assertEquals(reminderResponseModel.getData().getRecharge_number(), rechargeNumber.getValue());
        assertEquals(reminderResponseModel.getData().getOperator(), operator.getValue());
        assertEquals(null,eventSource.getValue());
    }

    @Test
    public void testOldDueDateSkipCheckForInvalidDueDate() {
        ReminderResponseModel reminderResponseModel = getReminderKafkaResponseMock();
        Boolean res = reminderListeners.oldDueDateSkipCheck(reminderResponseModel);
        assertEquals(new Boolean(false), res);

        reminderResponseModel.getData().setDue_date("0001-01-01 00:00:00");
        Boolean resInvalid = reminderListeners.oldDueDateSkipCheck(reminderResponseModel);
        assertEquals(new Boolean(true), resInvalid);
    }

    @Test
    public void testAirtelPrepaidBillFetchToShowReco() throws Exception {
        ReminderResponseModel payload = getAirtelPrepaidReminderMock();
        payload.getData().setStatus(4);

        Recents recents = mockRecents("mobile", "prepaid", Constants.EVENTSOURCE_SMS, "2024-09-19 09:30:00");
        recents.getKey().setRechargeNumber("9686136002");
        recents.getKey().setCustomerId(1146052229L);
        recents.getKey().setOperator("airtel");
        recents.setDueAmount(10.0);

        when(serviceConfig.isAirtelPrepaidCSVRecentEnabled()).thenReturn(true);

        Method method = reminderListeners.getClass().getDeclaredMethod("processAirtelPrepaidRecord", ReminderResponseModel.class, Recents.class);
        method.setAccessible(true);

        method.invoke(reminderListeners, payload, recents);
        assertNotNull(recents);
        assertNull(recents.getDueAmount());
        assertNotNull(recents.getDueDate());
        assertEquals(Constants.EVENT_SOURCE.CSV, recents.getEventSource());

    }

    @Test
    public void testAirtelPrepaidBillFetchToDismissReco() throws Exception {
        ReminderResponseModel payload = getAirtelPrepaidReminderMock();
        payload.getData().setStatus(14);

        Recents recents = mockRecents("mobile", "prepaid", Constants.EVENTSOURCE_SMS, "2024-09-19 09:30:00");
        recents.getKey().setRechargeNumber("9686136002");
        recents.getKey().setCustomerId(1146052229L);
        recents.getKey().setOperator("airtel");
        recents.setDueAmount(10.0);

        when(serviceConfig.isAirtelPrepaidCSVRecentEnabled()).thenReturn(true);

        Method method = reminderListeners.getClass().getDeclaredMethod("processAirtelPrepaidRecord", ReminderResponseModel.class, Recents.class);
        method.setAccessible(true);

        method.invoke(reminderListeners, payload, recents);
        assertNotNull(recents);
        assertEquals((double) 0, recents.getDueAmount());
        assertNull(recents.getDueDate());
        assertEquals(Constants.EVENT_SOURCE.CSV, recents.getEventSource());

    }

    @Test
    public void testGetCurrentOutstandingAmount_NullReminderDataResponseModel() {
        Double result = reminderListeners.getCurrentOutstandingAmount(null);
        assertNull(result);
    }

    @Test
    public void testGetCurrentOutstandingAmount_NullCustomerOtherInfo() {
        ReminderDataResponseModel reminderDataResponseModel = new ReminderDataResponseModel();
        reminderDataResponseModel.setCustomerOtherInfo(null);

        Double result = reminderListeners.getCurrentOutstandingAmount(reminderDataResponseModel);
        assertNull(result);
    }

    @Test
    public void testGetCurrentOutstandingAmount_ValidCustomerOtherInfo() {
        String customerOtherInfoJson = "{\"current_outstanding_amount\": 100.0}";
        ReminderDataResponseModel reminderDataResponseModel = new ReminderDataResponseModel();
        reminderDataResponseModel.setCustomerOtherInfo(customerOtherInfoJson);

        ReminderCustomerOtherInfo reminderCustomerOtherInfo = new ReminderCustomerOtherInfo();
        reminderCustomerOtherInfo.setCurrentOutstandingAmount(100.0);

        Double result = reminderListeners.getCurrentOutstandingAmount(reminderDataResponseModel);
        assertNotNull(result);
        assertEquals(Double.valueOf(100.0), result);
    }

    @Test
    public void testGetCurrentOutstandingAmount_InvalidCustomerOtherInfo() {
        String customerOtherInfoJson = "{\"invalidField\": 100.0}";
        ReminderDataResponseModel reminderDataResponseModel = new ReminderDataResponseModel();
        reminderDataResponseModel.setCustomerOtherInfo(customerOtherInfoJson);
        Double result = reminderListeners.getCurrentOutstandingAmount(reminderDataResponseModel);
        assertNull(result);
    }

    @Test
    public void updateRecentsAutomaticState_whenAutomaticStateIsChanged() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel old = new ReminderDataResponseModel();
        old.setIs_automatic(0);
        reminderResponseModel.setOld(old);
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setIs_automatic(0);
        data.setSubscriptionIsAutomatic(0);
        data.setRecharge_number("**********");
        data.setOperator("jvvnl");
        data.setExtra("{\"created_source\":\"SMS_PARSING_TELECOM\",\"updated_data_source\":\"SMS_PARSING_TELECOM\",\"subscriptionUpdatedAt\":\"2026-04-29 12:25:00\"}");
        data.setCustomer_id(123L);
        data.setService("electricity");
        reminderResponseModel.setData(data);
        reminderListeners.updateRecentsAutomaticState(reminderResponseModel);
        ArgumentCaptor<Integer> automaticStateCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Long> customerIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketCaptor = ArgumentCaptor.forClass(String.class);
        verify(recentService, times(1)).updateRecentAutomaticStateInfo(automaticStateCaptor.capture(),customerIdCaptor.capture(),serviceCaptor.capture(),rechargeNumberCaptor.capture(),operatorCaptor.capture(),planBucketCaptor.capture());

    }

    @Test
    public void updateRecentsAutomaticState_whenOperationTypeIsNotUpdate() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("delete");
        ReminderDataResponseModel old = new ReminderDataResponseModel();
        old.setIs_automatic(0);
        reminderResponseModel.setOld(old);
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setIs_automatic(0);
        data.setRecharge_number("**********");
        data.setOperator("jvvnl");
        data.setCustomer_id(123L);
        data.setService("electricity");
        reminderResponseModel.setData(data);
        reminderListeners.updateRecentsAutomaticState(reminderResponseModel);
        ArgumentCaptor<Integer> automaticStateCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Long> customerIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketCaptor = ArgumentCaptor.forClass(String.class);
        verify(recentService, times(0)).updateRecentAutomaticStateInfo(automaticStateCaptor.capture(),customerIdCaptor.capture(),serviceCaptor.capture(),rechargeNumberCaptor.capture(),operatorCaptor.capture(),planBucketCaptor.capture());

    }

    @Test
    public void updateRecentsAutomaticState_whenOldIsNullChangedAndExtraIsNull() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
//        ReminderDataResponseModel old = new ReminderDataResponseModel();
//        old.setIs_automatic(0);
//        reminderResponseModel.setOld(old);
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setIs_automatic(0);
        data.setRecharge_number("**********");
        data.setOperator("jvvnl");
        data.setCustomer_id(123L);
        data.setService("electricity");
        reminderResponseModel.setData(data);
        reminderListeners.updateRecentsAutomaticState(reminderResponseModel);
        ArgumentCaptor<Integer> automaticStateCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Long> customerIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketCaptor = ArgumentCaptor.forClass(String.class);
        verify(recentService, times(0)).updateRecentAutomaticStateInfo(automaticStateCaptor.capture(),customerIdCaptor.capture(),serviceCaptor.capture(),rechargeNumberCaptor.capture(),operatorCaptor.capture(),planBucketCaptor.capture());

    }

    @Test
    public void updateRecentsAutomaticState_whenAutomaticStateIsNotChangedAndExtraIsNull() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel old = new ReminderDataResponseModel();
        old.setAmount(0.0);
        reminderResponseModel.setOld(old);
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setIs_automatic(0);
        data.setRecharge_number("**********");
        data.setOperator("jvvnl");
        data.setCustomer_id(123L);
        data.setService("electricity");
        reminderResponseModel.setData(data);
        reminderListeners.updateRecentsAutomaticState(reminderResponseModel);
        ArgumentCaptor<Integer> automaticStateCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Long> customerIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketCaptor = ArgumentCaptor.forClass(String.class);
        verify(recentService, times(0)).updateRecentAutomaticStateInfo(automaticStateCaptor.capture(),customerIdCaptor.capture(),serviceCaptor.capture(),rechargeNumberCaptor.capture(),operatorCaptor.capture(),planBucketCaptor.capture());

    }

    @Test
    public void updateRecentsAutomaticState_whenAutomaticStateIsNotChangedAndExtraDoesNotContainsIsPaymodeChanged() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel old = new ReminderDataResponseModel();
        old.setAmount(0.0);
        reminderResponseModel.setOld(old);
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setIs_automatic(0);
        data.setRecharge_number("**********");
        data.setOperator("jvvnl");
        data.setCustomer_id(123L);
        data.setService("electricity");
        data.setExtra("{\"updated_source\":\"sms\"}");
        reminderResponseModel.setData(data);
        reminderListeners.updateRecentsAutomaticState(reminderResponseModel);
        ArgumentCaptor<Integer> automaticStateCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Long> customerIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketCaptor = ArgumentCaptor.forClass(String.class);
        verify(recentService, times(0)).updateRecentAutomaticStateInfo(automaticStateCaptor.capture(),customerIdCaptor.capture(),serviceCaptor.capture(),rechargeNumberCaptor.capture(),operatorCaptor.capture(),planBucketCaptor.capture());

    }

    @Test
    public void updateRecentsAutomaticState_whenAutomaticStateIsNotChangedAndExtraContainsIsPaymodeChanged() throws ReminderListenerException {
        ReminderResponseModel reminderResponseModel = new ReminderResponseModel();
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel old = new ReminderDataResponseModel();
        old.setAmount(0.0);
        reminderResponseModel.setOld(old);
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setIs_automatic(0);
        data.setSubscriptionIsAutomatic(0);
        data.setRecharge_number("**********");
        data.setOperator("jvvnl");
        data.setCustomer_id(123L);
        data.setService("electricity");
        data.setExtra("{\"updated_source\":\"sms\",\"isPaymodeChanged\":1,\"subscriptionUpdatedAt\":\"2026-04-29 12:25:00\"}");
        reminderResponseModel.setData(data);
        reminderListeners.updateRecentsAutomaticState(reminderResponseModel);
        ArgumentCaptor<Integer> automaticStateCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Long> customerIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> serviceCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> rechargeNumberCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> planBucketCaptor = ArgumentCaptor.forClass(String.class);
        verify(recentService, times(1)).updateRecentAutomaticStateInfo(automaticStateCaptor.capture(),customerIdCaptor.capture(),serviceCaptor.capture(),rechargeNumberCaptor.capture(),operatorCaptor.capture(),planBucketCaptor.capture());

    }

    @Test
    public void testUpdateRecentsConsentValidTill_Success() throws Exception {
        reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setConsentValidTill("2023-12-31 23:59:59");
        data.setRecharge_number("XXXX XXXX XXXX 1234");
        data.setReference_id("1234567890");
        data.setPaytype("Credit card");
        data.setOperator("operator");
        data.setCustomer_id(1L);
        data.setService("service");
        reminderResponseModel.setData(data);
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setConsentValidTill("2023-12-30 23:59:59");
        reminderResponseModel.setOld(oldData);
        doNothing().when(recentService).updateRecentConsentValidTillInfo(any(Date.class), anyLong(), anyString(), anyString(), anyString(), anyString());
        doNothing().when(kafkaProducerService).sendMessage(anyString());
        doNothing().when(metricsHelper).recordSuccessRate(anyString(), anyString());
        reminderListeners.updateRecentsConsentValidTill(reminderResponseModel);
        verify(recentService, times(1)).updateRecentConsentValidTillInfo(any(Date.class), eq(1L), eq("service"), eq("1234567890"), eq("ccbp"), eq(""));
        verify(kafkaProducerService, times(1)).sendMessage("1");
        verify(metricsHelper, times(1)).recordSuccessRate(REMINDER_CONSUMER, "RECENT_CONSENT_VALID_TILL_ONLY");
    }
    @Test
    public void testUpdateRecentsConsentValidTill_DateParsingException() throws ReminderListenerException {
        reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setConsentValidTill("2023-12-31 23:59:59");
        data.setRecharge_number("XXXX XXXX XXXX 1234");
        data.setPar("1234567890");
        data.setPaytype("Credit card");
        data.setOperator("operator");
        data.setCustomer_id(1L);
        data.setService("service");
        reminderResponseModel.setData(data);
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setConsentValidTill("2023-12-30 23:59:59");
        reminderResponseModel.setOld(oldData);
        reminderResponseModel.getData().setConsentValidTill("invalid-date");
        reminderListeners.updateRecentsConsentValidTill(reminderResponseModel);
        verify(recentService, times(0)).updateRecentConsentValidTillInfo(any(Date.class), anyLong(), anyString(), anyString(), anyString(), anyString());
    }
    @Test
    public void testUpdateRecentsConsentValidTill_UpdateException() throws Exception {
        reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setConsentValidTill("2023-12-31 23:59:59");
        data.setRecharge_number("XXXX XXXX XXXX 1234");
        data.setPar("1234567890");
        data.setPaytype("Credit card");
        data.setOperator("operator");
        data.setCustomer_id(1L);
        data.setService("service");
        reminderResponseModel.setData(data);
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setConsentValidTill("2023-12-30 23:59:59");
        reminderResponseModel.setOld(oldData);
        doThrow(new RuntimeException("Update Exception")).when(recentService).updateRecentConsentValidTillInfo(any(Date.class), anyLong(), anyString(), anyString(), anyString(), anyString());
        reminderListeners.updateRecentsConsentValidTill(reminderResponseModel);
        verify(recentService, times(1)).updateRecentConsentValidTillInfo(any(Date.class), eq(1L), eq("service"), eq("1234567890"), eq("ccbp"), eq(""));
        verify(metricsHelper, times(1)).recordSuccessRate(REMINDER_CONSUMER, ERROR_EVENT);
    }
    @Test
    public void testUpdateRecentsConsentValidTill_KafkaException() throws Exception {
        reminderResponseModel = new ReminderResponseModel();
        ReminderDataResponseModel data = new ReminderDataResponseModel();
        data.setConsentValidTill("2023-12-31 23:59:59");
        data.setRecharge_number("XXXX XXXX XXXX 1234");
        data.setReference_id("1234567890");
        data.setPaytype("Credit card");
        data.setOperator("operator");
        data.setCustomer_id(1L);
        data.setService("service");
        reminderResponseModel.setData(data);
        reminderResponseModel.setOperationType("update");
        ReminderDataResponseModel oldData = new ReminderDataResponseModel();
        oldData.setConsentValidTill("2023-12-30 23:59:59");
        reminderResponseModel.setOld(oldData);
        doNothing().when(recentService).updateRecentConsentValidTillInfo(any(Date.class), anyLong(), anyString(), anyString(), anyString(), anyString());
        doThrow(new RuntimeException("Kafka Exception")).when(kafkaProducerService).sendMessage(anyString());
        reminderListeners.updateRecentsConsentValidTill(reminderResponseModel);
        verify(recentService, times(1)).updateRecentConsentValidTillInfo(any(Date.class), eq(1L), eq("service"), eq("1234567890"), eq("ccbp"), eq(""));
        verify(kafkaProducerService, times(1)).sendMessage("1");
        verify(metricsHelper, times(1)).recordSuccessRate(REMINDER_CONSUMER, RECO_REFRESH_ERROR_EVENT);
    }

    private ReminderResponseModel getAirtelPrepaidReminderMock() {
        String message = "{\"database\":\"digital_reminder\",\"table\":\"bills_airtelprepaid2\",\"type\":\"update\",\"ts\":1725523217,\"xid\":55827653655,\"commit\":true,\"data\":{\"id\":23133801,\"customer_id\":1146052229,\"recharge_number\":\"9686136002\",\"product_id\":340182643,\"operator\":\"airtel\",\"amount\":0.00,\"due_date\":\"2024-09-19 00:00:00\",\"bill_fetch_date\":\"2024-09-05 13:30:16\",\"next_bill_fetch_date\":\"2024-09-06 00:00:00\",\"gateway\":null,\"paytype\":\"prepaid\",\"service\":\"mobile\",\"circle\":null,\"customer_mobile\":null,\"customer_email\":null,\"retry_count\":0,\"status\":131,\"reason\":\"1-4 False to true transition i.e Users plan has expired there\",\"extra\":\"{\\\"last_response\\\":{\\\"2024-09-05\\\":true},\\\"recon_id\\\":\\\"b4EvWOxr2N18TS2t+BsXhXcUUQY=\\\",\\\"user_type\\\":\\\"NON_RU\\\",\\\"source_subtype_2\\\":\\\"PARTIAL_BILL\\\",\\\"updated_data_source\\\":\\\"AIRTEL_PREPAID_BILL_FETCH\\\",\\\"last_transition\\\":\\\"2024-09-05\\\"}\",\"published_date\":\"2024-09-05 13:30:17\",\"created_at\":\"2024-06-06 15:38:09\",\"updated_at\":\"2024-09-05 08:00:17\",\"notification_status\":1,\"payment_date\":\"2024-07-22 13:41:13\",\"service_id\":0,\"customerOtherInfo\":\"\\\"{\\\\\\\"circleId\\\\\\\":\\\\\\\"34\\\\\\\",\\\\\\\"expiryFlag\\\\\\\":true,\\\\\\\"airtelPrepaid\\\\\\\":true,\\\\\\\"gstEnabled\\\\\\\":false}\\\"\",\"old_bill_fetch_date\":null},\"old\":{\"due_date\":\"2024-07-21 14:00:00\",\"bill_fetch_date\":\"2024-07-16 04:07:15\",\"next_bill_fetch_date\":\"2024-09-05 00:00:00\",\"status\":1,\"reason\":\"1-1 False to false\",\"extra\":\"{\\\"last_response\\\":{\\\"2024-09-03\\\":false},\\\"recon_id\\\":\\\"/cYnDha2Zne6Zs5wYEKeGFcKKSQ=\\\",\\\"user_type\\\":\\\"NON_RU\\\",\\\"source_subtype_2\\\":\\\"PARTIAL_BILL\\\",\\\"updated_data_source\\\":\\\"AIRTEL_PREPAID_BILL_FETCH\\\",\\\"last_transition\\\":\\\"2024-07-19\\\"}\",\"published_date\":\"2024-09-03 14:06:39\",\"updated_at\":\"2024-09-03 08:36:39\",\"payment_date\":\"2024-07-22 08:11:13\",\"customerOtherInfo\":\"{\\\"circleId\\\":\\\"34\\\",\\\"expiryFlag\\\":false,\\\"airtelPrepaid\\\":true,\\\"gstEnabled\\\":false}\"}}";
        return JsonUtils.parseJson(message, ReminderResponseModel.class);
    }

    private Recents mockRecents(String service, String payType, String eventSource, String updatedAt) {
        Recents recents=new Recents();
        RecentsPrimaryKey recentsPrimaryKey=new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService(service);
        recentsPrimaryKey.setRechargeNumber("9999900000");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType(payType);
        recents.setTxnAmount(100.0);
        recents.setEventSource(eventSource);
        recents.setUpdatedAt(DateUtil.stringToDate(updatedAt, "yyyy-MM-dd HH:mm:ss"));
        recents.setDueDate(DateUtil.hoursIncrDecr(DateUtil.stringToDate(updatedAt, "yyyy-MM-dd HH:mm:ss"), 72));
        return recents;
    }


}
