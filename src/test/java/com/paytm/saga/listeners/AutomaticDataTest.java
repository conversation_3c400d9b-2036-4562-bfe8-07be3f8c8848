package com.paytm.saga.listeners;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.dao.impl.CustomerBillDao;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.model.AutomaticResponseModel;
import com.paytm.saga.model.CustomerBill;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.CustomerBillRepository;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.model.AutomaticData;
import com.paytm.saga.repository.AutomaticDataRepository;
import com.paytm.saga.service.CommonService;
import com.paytm.saga.service.RecentService;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.hamcrest.CoreMatchers;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AutomaticDataTest {

	@Mock
    RecentsRepository recentsRepository;
	@Mock
	RecentService recentService;

    @Mock
    private AutomaticDataRepository automaticDataRepository;
    @Mock
    private StatsDClient monitoringClient;

	@Mock
	private CommonService commonService;

	@Mock
	private CustomerBillRepository customerBillRepository;

	@Mock
	private CustomerBillDao customerBillDao;

    @InjectMocks
    private AutomaticDataListener automaticDataListner;


    @Before
    public void setUp() {
        ReflectionTestUtils.setField(automaticDataListner, "ttlDays", 20);
    }


	@Rule
	public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(AutomaticDataListener.class));

	private AutomaticResponseModel getAutomaticDataKafkaResponseMock(){

		AutomaticDataListener automaticDataListener = new AutomaticDataListener();
		AutomaticResponseModel automaticResponseModel = new AutomaticResponseModel();
		automaticResponseModel.setRechargeNumber("1231111");
		automaticResponseModel.setService("mobile");
		automaticResponseModel.setCustomerId(1L);
		automaticResponseModel.setOperatorName("assampowerdistributioncompanyltd.(apdcl)");
        automaticResponseModel.setProductId(1);
		automaticResponseModel.setPayType("postpaid");
		automaticResponseModel.setPlanBucket(null);

		automaticResponseModel.setAutomaticDate(new Date());


		return automaticResponseModel;
	}

	private AutomaticResponseModel getAutomaticDataKafkaResponseMockForAutomatic5(){

		AutomaticDataListener automaticDataListener = new AutomaticDataListener();
		AutomaticResponseModel automaticResponseModel = new AutomaticResponseModel();
		automaticResponseModel.setRechargeNumber("1231111");
		automaticResponseModel.setService("mobile");
		automaticResponseModel.setCustomerId(1L);
		automaticResponseModel.setOperatorName("airtel");
		automaticResponseModel.setProductId(1);
		automaticResponseModel.setIsAutomatic(5);

		automaticResponseModel.setAutomaticDate(new Date());


		return automaticResponseModel;
	}
	
	@Test
	public void testUpdateInRecents() throws RechargeSagaBaseException {
		AutomaticResponseModel automaticResponseModel = getAutomaticDataKafkaResponseMock();
		ArgumentCaptor<Long> customerid = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> recharge_number = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> paytype = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Date> automatic_date = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> updated_at = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> bill_update_time = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Integer> subscriberId = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Boolean> shouldUpdateAutomaticDate = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Double> automaticAmount = ArgumentCaptor.forClass(Double.class);
		Integer ttlVal= 100;
		automaticDataListner.updateDataIntoRecents(automaticResponseModel);
		verify(recentService).updateAutomaticInfo(customerid.capture(), automatic_date.capture(), service.capture(), operator.capture(), plan_bucket.capture(), bill_update_time.capture(), updated_at.capture(), recharge_number.capture(), productId.capture(), subscriberId.capture(),shouldUpdateAutomaticDate.capture(), automaticAmount.capture());
		assertEquals(automaticResponseModel.getCustomerId(), customerid.getValue());
		assertEquals(automaticResponseModel.getService(), service.getValue());
		assertEquals(automaticResponseModel.getRechargeNumber(), recharge_number.getValue());
		assertEquals(automaticResponseModel.getOperatorName(), operator.getValue());
		assertEquals("", plan_bucket.getValue());

	}
	@Test
	public void testUpdateInRecentsForEmptyField() throws RechargeSagaBaseException {
		AutomaticResponseModel automaticResponseModel = getAutomaticDataKafkaResponseMock();
		ArgumentCaptor<Long> customerId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<String> service = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> operator = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> plan_bucket = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> rechargeNumber = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<Date> updatedAt = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> billUpdateTime = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Date> automatic_date = ArgumentCaptor.forClass(Date.class);
		ArgumentCaptor<Long> productId = ArgumentCaptor.forClass(Long.class);
		ArgumentCaptor<Integer> subscriberId = ArgumentCaptor.forClass(Integer.class);
		ArgumentCaptor<Boolean> shouldUpdateAutomaticDate = ArgumentCaptor.forClass(Boolean.class);
		ArgumentCaptor<Double> automaticAmount = ArgumentCaptor.forClass(Double.class);
		Integer ttlVal= 100;
		//when(recentsRepository.findByCustomerIdAndServiceAndRechargenumberAndOperatorAndPlan_bucket(argument1.capture(),argument2.capture(),argument3.capture(),argument4.capture(),argument5.capture())).thenReturn(fn1());
		automaticDataListner.updateDataIntoRecents(automaticResponseModel);

		verify(recentService).updateAutomaticInfo(customerId.capture(),
				automatic_date.capture(), service.capture(),operator.capture(), 
				plan_bucket.capture(),billUpdateTime.capture(), updatedAt.capture(),
				rechargeNumber.capture(),productId.capture(), subscriberId.capture(), shouldUpdateAutomaticDate.capture(), automaticAmount.capture());
		assertEquals(automaticResponseModel.getCustomerId(), customerId.getValue());
		assertEquals(automaticResponseModel.getService(), service.getValue());
		assertEquals(automaticResponseModel.getRechargeNumber(), rechargeNumber.getValue());
		assertEquals(automaticResponseModel.getOperatorName(), operator.getValue());
		assertEquals(automaticResponseModel.getAutomaticDate(), automatic_date.getValue());
	}
	public Recents fn(){
		AutomaticResponseModel automaticResponseModel = getAutomaticDataKafkaResponseMock();
		Recents recents = new Recents();
		recents.setKey(new RecentsPrimaryKey());
		recents.getKey().setRechargeNumber(automaticResponseModel.getRechargeNumber());
		recents.getKey().setOperator(automaticResponseModel.getOperatorName());
		recents.getKey().setService(automaticResponseModel.getService());
		recents.getKey().setCustomerId(automaticResponseModel.getCustomerId());
		recents.getKey().setPlanBucket("");
		recents.setAutomaticDate(automaticResponseModel.getAutomaticDate());
		return recents;
	}
	public Recents fn1(){
		AutomaticResponseModel automaticResponseModel = getAutomaticDataKafkaResponseMock();
		Recents recents = new Recents();
		recents.setKey(new RecentsPrimaryKey());
		recents.getKey().setRechargeNumber(automaticResponseModel.getRechargeNumber());
		recents.getKey().setOperator(automaticResponseModel.getOperatorName());
		recents.getKey().setService(automaticResponseModel.getService());
		recents.getKey().setCustomerId(automaticResponseModel.getCustomerId());
		recents.getKey().setPlanBucket("");
		recents.setAutomaticDate(null);
		return recents;
	}


    @Test
    public void testValidMessage() throws Exception {
        Acknowledgment ack = mock(Acknowledgment.class);
        List<String> list = new ArrayList<>();

        Date date = DateUtils.addDays(new Date(), 5);
        DateFormat localFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat gmtFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = localFormat.format(date);

        //gmtFormat.setTimeZone(TimeZone.getTimeZone("GMT"));


        list.add(
                "{\"subscriber_id\":104207876,\"customerId\":310329952,\"productId\":274545379,\"is_automatic\":1,\"rechargeNumber\":\"150424500\",\"operatorName\":\"bses yamuna\",\"service\":\"electricity\",\"updatedData\":{\"fulfillmentTime\":\n"
                        + "\n" + "{\"old\":\"\",\"new\":\"" + gmtFormat.format(date) + "\"}\n" + "}}");

        automaticDataListner.listen(list, ack);
        ArgumentCaptor<AutomaticData> argument = ArgumentCaptor.forClass(AutomaticData.class);

        verify(automaticDataRepository).save(argument.capture(), Mockito.any(Integer.class));
        AutomaticData automaticData = argument.getValue();

        assertEquals(automaticData.getAutomaticDate(),
                localFormat.parse(dateString));
        assertEquals("150424500", automaticData.getKey().getRechargeNumber());
        assertEquals("electricity", automaticData.getKey().getService());
        assertEquals("bses yamuna", automaticData.getKey().getOperator());

    }

    @Test
    public void testInValidAutomaticDate() throws Exception {
        Acknowledgment ack = mock(Acknowledgment.class);
        List<String> list = new ArrayList<>();
        list.add(
                "{\"subscriber_id\":104207876,\"customerId\":310329952,\"productId\":274545379,\"is_automatic\":1,\"rechargeNumber\":\"150424500\",\"operatorName\":\"bses yamuna\",\"service\":\"electricity\",\"updatedData\":{\"fulfillmentTime\":\n"
                        + "\n" + "{\"old\":\"\",\"new\":\"2022.04.01 10:50:59\"}\n" + "}}");

        automaticDataListner.listen(list, ack);

        assertThat(appender.getOutput(), CoreMatchers.containsString("The object to be validated must not be null"));

    }

    @Test
    public void testMissingMandatoryParameter() throws Exception {
        Acknowledgment ack = mock(Acknowledgment.class);
        List<String> list = new ArrayList<>();

        Date date = DateUtils.addDays(new Date(), 5);
        DateFormat gmtFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //gmtFormat.setTimeZone(TimeZone.getTimeZone("GMT"));

        list.add(
                "{\"subscriber_id\":104207876,\"productId\":274545379,\"is_automatic\":1,\"rechargeNumber\":\"150424500\",\"operatorName\":\"bses yamuna\",\"service\":\"electricity\",\"updatedData\":{\"fulfillmentTime\":\n"
                        + "\n" + "{\"old\":\"\",\"new\":\"" + gmtFormat.format(date) + "\"}\n" + "}}");

        automaticDataListner.listen(list, ack);

        assertThat(appender.getOutput(), CoreMatchers.containsString("Invalid message body customerId can't be empty"));

    }


    @Test
    public void testAutomaticDateSmallerThanCurrentDate() throws Exception {
        Acknowledgment ack = mock(Acknowledgment.class);
        List<String> list = new ArrayList<>();
        list.add(
                "{\"subscriber_id\":104207876,\"customerId\":310329952,\"productId\":274545379,\"is_automatic\":1,\"rechargeNumber\":\"150424500\",\"operatorName\":\"bses yamuna\",\"service\":\"electricity\",\"updatedData\":{\"fulfillmentTime\":\n"
                        + "\n" + "{\"old\":\"\",\"new\":\"2022-04-01 10:50:59\"}\n" + "}}");

        automaticDataListner.listen(list, ack);

        assertThat(appender.getOutput(), CoreMatchers.containsString("automatic Date should be greater than current date"));

    }

	@Test
	public void testUpdateInRecentForAutomatic5() throws RechargeSagaBaseException {
		AutomaticResponseModel automaticResponseModel = getAutomaticDataKafkaResponseMockForAutomatic5();
		ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		Product product = new Product();
		product.setProductId(1L);
		product.setService("mobile");
		product.setOperator("airtel");
		product.setStatus(0);
		CVRProductCache.getInstance().addProductDetails(product);
		when(commonService.getSmartReminderTTL(any())).thenReturn(100);
		automaticDataListner.updateDataIntoCustomerBill(automaticResponseModel);

		verify(customerBillDao).save(customerBill.capture(), ttl.capture());
		assertEquals(automaticResponseModel.getCustomerId(), customerBill.getValue().getKey().getCustomerId());
		assertEquals(automaticResponseModel.getService(), customerBill.getValue().getKey().getService());
		assertEquals(automaticResponseModel.getRechargeNumber(), customerBill.getValue().getKey().getRechargeNumber());
		assertEquals(automaticResponseModel.getOperatorName(), customerBill.getValue().getKey().getOperator());
	}


	@Test
	public void testInAutomaticDateForNonRenew() throws Exception {
		Acknowledgment ack = mock(Acknowledgment.class);
		List<String> list = new ArrayList<>();
		list.add(
				"{\"subscriber_id\":104207876,\"customerId\":310329952,\"productId\":274545379,\"is_automatic\":1,\"rechargeNumber\":\"150424500\",\"operatorName\":\"bses yamuna\",\"service\":\"electricity\",\"updatedData\":{\"fulfillmentTime\":\n"
						+ "\n" + "{\"old\":\"\",\"new\":\"0000-00-00 00:00:00\"}\n" + "}}");

		automaticDataListner.listen(list, ack);

		assertThat(appender.getOutput(), CoreMatchers.containsString("Acknowledging message"));

	}

	@Test
	public void testInAutomaticDateForRenew() throws Exception {
		Acknowledgment ack = mock(Acknowledgment.class);
		List<String> list = new ArrayList<>();
		list.add(
				"{\"subscriber_id\":104207876,\"customerId\":310329952,\"productId\":274545379,\"is_automatic\":5,\"rechargeNumber\":\"150424500\",\"operatorName\":\"bses yamuna\",\"service\":\"electricity\",\"updatedData\":{\"fulfillmentTime\":\n"
						+ "\n" + "{\"old\":\"\",\"new\":\"0000-00-00 00:00:00\"}\n" + "}}");

		automaticDataListner.listen(list, ack);

		assertThat(appender.getOutput(), CoreMatchers.containsString("Acknowledging message"));

	}



	@Test
	public void testUpdateInRecentForAutomatic5RunTimeException() throws RechargeSagaBaseException {

		ArgumentCaptor<CustomerBill> customerBill = ArgumentCaptor.forClass(CustomerBill.class);
		ArgumentCaptor<Integer> ttl = ArgumentCaptor.forClass(Integer.class);
		Product product = new Product();
		product.setProductId(1L);
		product.setService("mobile");
		product.setOperator("airtel");
		product.setStatus(0);
		CVRProductCache.getInstance().addProductDetails(product);
		automaticDataListner.updateDataIntoCustomerBill(null);

	}




}
