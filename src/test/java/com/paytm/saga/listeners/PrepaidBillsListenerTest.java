package com.paytm.saga.listeners;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.exception.PlanValidityListenerException;
import com.paytm.saga.common.exception.PrepaidBillsListenerException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.PrepaidBillsListenerDataModel;
import com.paytm.saga.dto.PrepaidBillsListenerModel;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.repository.PlanExpiryHistoryRepository;
import com.paytm.saga.service.RecentsRepositoryWrapperService;
import com.paytm.saga.service.RPSService;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import com.paytm.saga.util.TTLUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrepaidBillsListenerTest {

    @Mock
    private RPSService rpsService;

    @Mock
    private PlanExpiryHistoryRepository planExpiryHistoryRepository;

    @Mock
    private RecentService recentService;

    @Mock
    private RecentsRepositoryWrapperService recentsRepository;

    @Mock
    private DropOffDBHelper dropOffDBHelper;

    @Mock
    private MetricsHelper metricsHelper;

    @InjectMocks
    private PrepaidBillsListener prepaidBillsListener;

    @Before
    public void setUp() {
        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"prepaidBillsConsumerServices\": [\"mobile\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private PrepaidBillsListenerModel createMockPrepaidBillsListenerModel(){
        PrepaidBillsListenerModel prepaidBillsListenerModel = new PrepaidBillsListenerModel();
        PrepaidBillsListenerDataModel prepaidBillsListenerDataModel = new PrepaidBillsListenerDataModel();
        prepaidBillsListenerDataModel.setRecharge_number("1234567890");
        prepaidBillsListenerDataModel.setCustomer_id(6363783L);
        prepaidBillsListenerDataModel.setBillDate("2024-02-01 11:02:59.177");
        prepaidBillsListenerDataModel.setService("mobile");
        prepaidBillsListenerDataModel.setPaytype("prepaid");
        prepaidBillsListenerDataModel.setOperator("airtel");
        prepaidBillsListenerDataModel.setCircle("delhi");
        prepaidBillsListenerDataModel.setAmount(500.0);
        prepaidBillsListenerDataModel.setProductId(778373L);
        prepaidBillsListenerDataModel.setDueDate("2024-02-01 11:02:59.177");
        prepaidBillsListenerDataModel.setNextBillFetchDate("2024-02-01 11:02:59.177");
        prepaidBillsListenerDataModel.setBillFetchDate("2024-02-01 11:02:59.177");
        prepaidBillsListenerDataModel.setIsAutomatic(1);
        prepaidBillsListenerDataModel.setMetaData(null);
        prepaidBillsListenerDataModel.setCreated_at("2024-02-01 11:02:59.177");
        prepaidBillsListenerDataModel.setUpdated_at("2024-02-01 11:02:59.177");
        prepaidBillsListenerModel.setData(prepaidBillsListenerDataModel);
        prepaidBillsListenerModel.setOld(prepaidBillsListenerDataModel);
        prepaidBillsListenerModel.setOperationType(Constants.OP_UPDATE);
        return prepaidBillsListenerModel;

    }

    @Test
    public void testWhenPrepaidBillsListenerModelIsNull() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = null;

        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);

        verify(recentService, times(0)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(0)).save(any());
        verify(recentService,times(0)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(0)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }

    @Test
    public void testWhenPrepaidBillsListenerModelDataIsNull() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        prepaidBillsListenerModel.setData(null);

        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);

        verify(recentService, times(0)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(0)).save(any());
        verify(recentService,times(0)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(0)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }


    @Test
    public void testWhenUpdateWhenOldDataIsNull() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        List<String> allowedPidsList = new ArrayList<>();
        allowedPidsList.add("778373");
        prepaidBillsListenerModel.setOld(null);
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);

        verify(recentService, times(0)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(0)).save(any());
        verify(recentService,times(0)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(0)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }

    @Test
    public void testWhenUpdateWhenNoDataIsChanged() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        prepaidBillsListenerModel.getOld().setRecharge_number(null);
        prepaidBillsListenerModel.getOld().setAmount(null);
        prepaidBillsListenerModel.getOld().setCustomer_id(null);
        prepaidBillsListenerModel.getOld().setService(null);
        prepaidBillsListenerModel.getOld().setOperator(null);
        prepaidBillsListenerModel.getOld().setCircle(null);
        prepaidBillsListenerModel.getOld().setBillDate(null);
        prepaidBillsListenerModel.getOld().setDueDate(null);
        prepaidBillsListenerModel.getOld().setBillFetchDate(null);
        prepaidBillsListenerModel.getOld().setNextBillFetchDate(null);
        prepaidBillsListenerModel.getOld().setIsAutomatic(null);
        prepaidBillsListenerModel.getOld().setStatus(null);

        List<String> allowedPidsList = new ArrayList<>();
        allowedPidsList.add("778373");
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);

        verify(recentService, times(0)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(0)).save(any());
        verify(recentService,times(0)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(0)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }

    @Test
    public void testWhenDeleteOperationSuccessWithNonAllowedPids() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        prepaidBillsListenerModel.setOperationType(Constants.OP_DELETE);
        List<String> allowedPidsList = new ArrayList<>();
        allowedPidsList.add("778373");
//        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);

        verify(recentService, times(1)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(0)).save(any());
        verify(recentService,times(0)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(0)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }

    @Test
    public void testWhenDeleteOperationSuccessWithAllowedPids() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        prepaidBillsListenerModel.setOperationType(Constants.OP_DELETE);
        List<String> allowedPidsList = new ArrayList<>();
        allowedPidsList.add("778373");
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);

        verify(recentService, times(1)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(0)).save(any());
        verify(recentService,times(0)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(0)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }

    @Test
    public void testWhenDeleteOperationSuccessWithAllowedPidsConfigEmpty() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        prepaidBillsListenerModel.setOperationType(Constants.OP_DELETE);
        List<String> allowedPidsList = new ArrayList<>();
//        allowedPidsList.add("778373");
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);

        verify(recentService, times(1)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(0)).save(any());
        verify(recentService,times(0)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(0)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }


    @Test
    public void testWhenInsertOperationSuccessButCategoryIdNotInDCATMapAndNoInsertInDropoff() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        prepaidBillsListenerModel.getData().setService("fasttag");
        prepaidBillsListenerModel.setOperationType(Constants.OP_INSERT);
        List<String> allowedPidsList = new ArrayList<>();
//        allowedPidsList.add("778373");
        Integer ttl = TTLUtils.getPlanValidityTTL(DateUtil.stringToDate(prepaidBillsListenerModel.getData().getDueDate(), DateFormats.RECENTS_DATE_FORMAT));
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);
//        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(new ArrayList<>());
        verify(recentService, times(0)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(1)).save(any(),eq(ttl));
        verify(recentService,times(1)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(1)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }

    @Test
    public void testWhenInsertOperationSuccessButCategoryIdInDCATMapButAmountNotgreaterThanZeroAndNoInsertInDropoff() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        prepaidBillsListenerModel.getData().setAmount(0.0);
        prepaidBillsListenerModel.setOperationType(Constants.OP_INSERT);
        List<String> allowedPidsList = new ArrayList<>();
//        allowedPidsList.add("778373");
        Integer ttl = TTLUtils.getPlanValidityTTL(DateUtil.stringToDate(prepaidBillsListenerModel.getData().getDueDate(), DateFormats.RECENTS_DATE_FORMAT));
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);
//        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(new ArrayList<>());
        verify(recentService, times(0)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(0)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(1)).save(any(),eq(ttl));
        verify(recentService,times(1)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(1)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }

    @Test
    public void testWhenInsertOperationSuccessAndNoInsertInDropoff() throws PrepaidBillsListenerException, PlanValidityListenerException {
        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
        prepaidBillsListenerModel.setOperationType(Constants.OP_INSERT);
        List<String> allowedPidsList = new ArrayList<>();
//        allowedPidsList.add("778373");
        Integer ttl = TTLUtils.getPlanValidityTTL(DateUtil.stringToDate(prepaidBillsListenerModel.getData().getDueDate(), DateFormats.RECENTS_DATE_FORMAT));
        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);
//        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(new ArrayList<>());
        verify(recentService, times(0)).unsetAutomaticForDeleteEventFromPrepaidBills(any(),any());
        verify(rpsService,times(1)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
        verify(planExpiryHistoryRepository,times(1)).save(any(),eq(ttl));
        verify(recentService,times(1)).insertIntoRecentAndCustomerBill(any(),any());
        verify(recentsRepository,times(1)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));

    }

//    @Test
//    public void testWhenInsertOperationSuccessAndNoInsertInDropoff() throws PrepaidBillsListenerException, PlanValidityListenerException {
//        PrepaidBillsListenerModel prepaidBillsListenerModel = createMockPrepaidBillsListenerModel();
//        prepaidBillsListenerModel.setOperationType(Constants.OP_INSERT);
//        List<String> allowedPidsList = new ArrayList<>();
////        allowedPidsList.add("778373");
//        Integer ttl = TTLUtils.getPlanValidityTTL(DateUtil.stringToDate(prepaidBillsListenerModel.getData().getDueDate(), DateFormats.RECENTS_DATE_FORMAT));
//        FeatureConfigCache.getInstance().getFeatureConfigMap().put(Constants.SERVICE_CONFIG_CONSTANTS.PREPAID_BILLS_CONSMUER_PIDS_ENABLED,allowedPidsList);
//        prepaidBillsListener.insertDataIntoCassandra(prepaidBillsListenerModel);
//        when(recentsRepository.findBycustomerIdAndrechargeNumberAndservice(any(),any(),any())).thenReturn(new ArrayList<>());
//        verify(recentService, times(0)).unsetAutomaticForDeleteEventFromPrepaidBills(any());
//        verify(rpsService,times(1)).getPlanDetails(any(),any(),any(),any(),any(),any(),any(),any(),any());
//        verify(planExpiryHistoryRepository,times(1)).save(any(),eq(ttl));
//        verify(recentService,times(1)).insertIntoRecentAndCustomerBill(any(),any());
//        verify(recentsRepository,times(1)).findBycustomerIdAndrechargeNumberAndservice(any(),any(),any());
//        verify(dropOffDBHelper,times(0)).insertDropOff(any(DropOff.class));
//
//    }
}