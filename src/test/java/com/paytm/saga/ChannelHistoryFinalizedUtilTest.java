package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.paytm.saga.common.metrics.MetricsHelper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.dto.ChannelHistoryPage;
import com.paytm.saga.dto.ResponsePage;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.dto.builder.ChannelHistoryFinalizedBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.service.ChannelHistoryFinalizedService;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.util.DateUtil;

public class ChannelHistoryFinalizedUtilTest {
	@Mock
	private ChannelHistoryFinalizedService channelHistoryFinalizedService;
	@Mock
	private MetricsHelper metricsHelper;
	private ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil=null;
	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
		channelHistoryFinalizedUtil = new ChannelHistoryFinalizedUtil(channelHistoryFinalizedService,metricsHelper);
	}

	@Test
	public void noRecordInFinalizedTable() {
		ResponsePage<ChannelHistoryFinalized> responsePage = new ResponsePage<ChannelHistoryFinalized>(0,
				new ArrayList<ChannelHistoryFinalized>(), null, false);
		when(channelHistoryFinalizedService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking", 3, null))
				.thenReturn(responsePage);

		ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L,
				"CX29412353", "cylinder booking", 1, null);
		assertEquals(0, channelHistoryPage.getChannelHistories().size());
		assertEquals(false, channelHistoryPage.isAvailableNext());
		assertEquals(null, channelHistoryPage.getLastCardId());

	}

	@Test
	public void oneRecordInFinalizedTable() {
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("billsObj", "billsObj");
		Map<String, String> displayObj = new HashMap<>();
		displayObj.put("displayObj", "displayObj");
		List<ChannelHistoryFinalized> channelHistoryFinalizeds = new ArrayList<ChannelHistoryFinalized>();
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(new Date()).setDisplayValues(displayObj)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("7")
				.setTransactionTime(new Date()).setTransactionUpdateTime(new Date()).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		ResponsePage<ChannelHistoryFinalized> responsePage = new ResponsePage<ChannelHistoryFinalized>(1,
				channelHistoryFinalizeds, null, false);

		when(channelHistoryFinalizedService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking", 3, null))
				.thenReturn(responsePage);

		ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L,
				"CX29412353", "cylinder booking", 1, null);
		assertEquals(1, channelHistoryPage.getChannelHistories().size());
		assertEquals(false, channelHistoryPage.isAvailableNext());
		assertEquals(null, channelHistoryPage.getLastCardId());
	}

	@Test
	public void convertChannelHistoryToFinalized() {
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("billsObj", "billsObj");
		Map<String, String> displayObj = new HashMap<>();
		displayObj.put("displayObj", "displayObj");
		Date createdDate = DateUtil.dateIncrDecr(new Date(), 3);
		Date txnUpdateDate = DateUtil.dateIncrDecr(new Date(), 2);
		Date txnDate = DateUtil.dateIncrDecr(new Date(), 1);
		List<ChannelHistory> channelHistoryList = new ArrayList<ChannelHistory>();
		channelHistoryList.add(new ChannelHistoryBuilder().setAmount("100").setBillsObj(billsObj).setCategoryId(1L)
				.setCircle("pan india").setCreatedTime(createdDate).setDisplayValues(displayObj)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("7")
				.setTransactionTime(txnDate).setTransactionUpdateTime(txnUpdateDate).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		List<ChannelHistoryFinalized> channelHistoryFinalizeds = channelHistoryFinalizedUtil
				.convertHistoryToFinalizedHistory(channelHistoryList);
		assertEquals(channelHistoryFinalizeds.get(0).getAmount(), channelHistoryList.get(0).getAmount());
		assertEquals(channelHistoryFinalizeds.get(0).getBillsObj(), channelHistoryList.get(0).getBillsObj());
		assertEquals(channelHistoryFinalizeds.get(0).getCategoryId(), channelHistoryList.get(0).getCategoryId());
		assertEquals(channelHistoryFinalizeds.get(0).getCircle(), channelHistoryList.get(0).getCircle());
		assertEquals(channelHistoryFinalizeds.get(0).getCreatedTime(), channelHistoryList.get(0).getCreatedTime());
		assertEquals(channelHistoryFinalizeds.get(0).getCustomerId(), channelHistoryList.get(0).getCustomerId());
		assertEquals(channelHistoryFinalizeds.get(0).getDisplayValues(), channelHistoryList.get(0).getDisplayValues());
		assertEquals(channelHistoryFinalizeds.get(0).getEventType(), channelHistoryList.get(0).getEventType());
		assertEquals(channelHistoryFinalizeds.get(0).getInResponseCode(),
				channelHistoryList.get(0).getInResponseCode());
		assertEquals(channelHistoryFinalizeds.get(0).getItemId(), channelHistoryList.get(0).getItemId());
		assertEquals(channelHistoryFinalizeds.get(0).getOperator(), channelHistoryList.get(0).getOperator());
		assertEquals(channelHistoryFinalizeds.get(0).getOrderId(), channelHistoryList.get(0).getOrderId());
		assertEquals(channelHistoryFinalizeds.get(0).getPaymentStatus(), channelHistoryList.get(0).getPaymentStatus());
		assertEquals(channelHistoryFinalizeds.get(0).getPaytype(), channelHistoryList.get(0).getPaytype());
		assertEquals(channelHistoryFinalizeds.get(0).getProductId(), channelHistoryList.get(0).getProductId());
		assertEquals(channelHistoryFinalizeds.get(0).getRecharge_number_2(),
				channelHistoryList.get(0).getRecharge_number_2());
		assertEquals(channelHistoryFinalizeds.get(0).getRecharge_number_3(),
				channelHistoryList.get(0).getRecharge_number_3());
		assertEquals(channelHistoryFinalizeds.get(0).getRecharge_number_4(),
				channelHistoryList.get(0).getRecharge_number_4());
		assertEquals(channelHistoryFinalizeds.get(0).getRechargeNumber(),
				channelHistoryList.get(0).getRechargeNumber());
		assertEquals(channelHistoryFinalizeds.get(0).getService(), channelHistoryList.get(0).getService());
		assertEquals(channelHistoryFinalizeds.get(0).getStatus(), channelHistoryList.get(0).getStatus());
		assertEquals(channelHistoryFinalizeds.get(0).getTransactionTime(),
				channelHistoryList.get(0).getTransactionTime());
		assertEquals(channelHistoryFinalizeds.get(0).getTransactionUpdateTime(),
				channelHistoryList.get(0).getTransactionUpdateTime());
	}

	@Test
	public void convertFinalizedToHistoryWithDuplicateRechargeFinalizedEvents(){
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("billsObj", "billsObj");
		Map<String, String> displayObj = new HashMap<>();
		displayObj.put("displayObj", "displayObj");
		List<ChannelHistoryFinalized> channelHistoryFinalizeds = new ArrayList<ChannelHistoryFinalized>();
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(new Date()).setDisplayValues(displayObj)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("7")
				.setTransactionTime(new Date()).setTransactionUpdateTime(new Date()).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(DateUtil.dateIncrDecr(new Date(),-1)).setDisplayValues(displayObj)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("1")
				.setTransactionTime(DateUtil.dateIncrDecr(new Date(),-1)).setTransactionUpdateTime(DateUtil.dateIncrDecr(new Date(),-1)).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		ResponsePage<ChannelHistoryFinalized> responsePage = new ResponsePage<ChannelHistoryFinalized>(2,
				channelHistoryFinalizeds, null, false);

		when(channelHistoryFinalizedService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking", 3, null))
				.thenReturn(responsePage);

		ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L,
				"CX29412353", "cylinder booking", 1, null);
		assertEquals(1, channelHistoryPage.getChannelHistories().size());
		assertEquals(false, channelHistoryPage.isAvailableNext());
		assertEquals(null, channelHistoryPage.getLastCardId());
	}

	@Test
	public void convertFinalizedToHistoryWithDuplicateNotPaidOnPaytmFinalizedEvents(){
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("billsObj", "billsObj");
		Map<String, String> displayObj = new HashMap<>();
		displayObj.put("displayObj", "displayObj");
		List<ChannelHistoryFinalized> channelHistoryFinalizeds = new ArrayList<ChannelHistoryFinalized>();
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(new Date()).setDisplayValues(displayObj)
				.setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("7")
				.setTransactionTime(new Date()).setTransactionUpdateTime(new Date()).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(DateUtil.dateIncrDecr(new Date(),-1)).setDisplayValues(displayObj)
				.setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("1")
				.setTransactionTime(DateUtil.dateIncrDecr(new Date(),-1)).setTransactionUpdateTime(DateUtil.dateIncrDecr(new Date(),-1)).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		ResponsePage<ChannelHistoryFinalized> responsePage = new ResponsePage<ChannelHistoryFinalized>(2,
				channelHistoryFinalizeds, null, false);

		when(channelHistoryFinalizedService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking", 3, null))
				.thenReturn(responsePage);

		ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L,
				"CX29412353", "cylinder booking", 1, null);
		assertEquals(2, channelHistoryPage.getChannelHistories().size());
		assertEquals(false, channelHistoryPage.isAvailableNext());
		assertEquals(null, channelHistoryPage.getLastCardId());
	}

	@Test
	public void convertFinalizedToHistoryWithDuplicateMarkAsPaidFinalizedEvents(){
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("billsObj", "billsObj");
		Map<String, String> displayObj = new HashMap<>();
		displayObj.put("displayObj", "displayObj");
		List<ChannelHistoryFinalized> channelHistoryFinalizeds = new ArrayList<ChannelHistoryFinalized>();
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(new Date()).setDisplayValues(displayObj)
				.setEventType("MARK_AS_PAID").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("7")
				.setTransactionTime(new Date()).setTransactionUpdateTime(new Date()).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(DateUtil.dateIncrDecr(new Date(),-1)).setDisplayValues(displayObj)
				.setEventType("MARK_AS_PAID").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("1")
				.setTransactionTime(DateUtil.dateIncrDecr(new Date(),-1)).setTransactionUpdateTime(DateUtil.dateIncrDecr(new Date(),-1)).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		ResponsePage<ChannelHistoryFinalized> responsePage = new ResponsePage<ChannelHistoryFinalized>(2,
				channelHistoryFinalizeds, null, false);

		when(channelHistoryFinalizedService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking", 3, null))
				.thenReturn(responsePage);

		ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L,
				"CX29412353", "cylinder booking", 1, null);
		assertEquals(2, channelHistoryPage.getChannelHistories().size());
		assertEquals(false, channelHistoryPage.isAvailableNext());
		assertEquals(null, channelHistoryPage.getLastCardId());
	}

	@Test
	public void convertFinalizedToHistoryWithDuplicateRechargeAndNonRechargeFinalizedEvents(){
		Map<String, String> billsObj = new HashMap<>();
		billsObj.put("billsObj", "billsObj");
		Map<String, String> displayObj = new HashMap<>();
		displayObj.put("displayObj", "displayObj");
		List<ChannelHistoryFinalized> channelHistoryFinalizeds = new ArrayList<ChannelHistoryFinalized>();
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(new Date()).setDisplayValues(displayObj)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("7")
				.setTransactionTime(new Date()).setTransactionUpdateTime(new Date()).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(DateUtil.dateIncrDecr(new Date(),-1)).setDisplayValues(displayObj)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("1")
				.setTransactionTime(DateUtil.dateIncrDecr(new Date(),-1)).setTransactionUpdateTime(DateUtil.dateIncrDecr(new Date(),-1)).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(DateUtil.dateIncrDecr(new Date(),-5)).setDisplayValues(displayObj)
				.setEventType("MARK_AS_PAID").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("1")
				.setTransactionTime(DateUtil.dateIncrDecr(new Date(),-5)).setTransactionUpdateTime(DateUtil.dateIncrDecr(new Date(),-5)).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(DateUtil.dateIncrDecr(new Date(),-10)).setDisplayValues(displayObj)
				.setEventType("NOT_PAID_ON_PAYTM").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("1")
				.setTransactionTime(DateUtil.dateIncrDecr(new Date(),-10)).setTransactionUpdateTime(DateUtil.dateIncrDecr(new Date(),-10)).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		channelHistoryFinalizeds.add(new ChannelHistoryFinalizedBuilder().setAmount("100").setBillsObj(billsObj)
				.setCategoryId(1L).setCircle("pan india").setCreatedTime(DateUtil.dateIncrDecr(new Date(),-15)).setDisplayValues(displayObj)
				.setEventType("MARK_AS_PAID").setFinalisedState(true).setInResponseCode("3").setItemId(2L)
				.setOperator("indane").setPaymentStatus("5").setOrderId(121212L).setPaytype("recharge")
				.setProductId(123L).setRecharge_number_2("12213").setRecharge_number_3("12312")
				.setRecharge_number_4("23123").setRechargeNumber("CX29412353").setStatus("1")
				.setTransactionTime(DateUtil.dateIncrDecr(new Date(),-15)).setTransactionUpdateTime(DateUtil.dateIncrDecr(new Date(),-15)).setCustomerId(285959997L)
				.setService("cylinder booking").build());
		ResponsePage<ChannelHistoryFinalized> responsePage = new ResponsePage<ChannelHistoryFinalized>(2,
				channelHistoryFinalizeds, null, false);

		when(channelHistoryFinalizedService.getPageOfHistory(285959997L, "CX29412353", "cylinder booking", 3, null))
				.thenReturn(responsePage);

		ChannelHistoryPage channelHistoryPage = channelHistoryFinalizedUtil.convertFinalizedToHistory(285959997L,
				"CX29412353", "cylinder booking", 1, null);
		assertEquals(4, channelHistoryPage.getChannelHistories().size());
		assertEquals(false, channelHistoryPage.isAvailableNext());
		assertEquals(null, channelHistoryPage.getLastCardId());
	}
}
