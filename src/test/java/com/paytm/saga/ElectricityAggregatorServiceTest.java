package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.DeepLinkTypes;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.ChannelHistoryPage;
import com.paytm.saga.dto.HistoryPage;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.dto.builder.ChannelHistoryFinalizedBuilder;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.dto.builder.ReminderHistoryBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ChannelHistoryFinalized;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.ChannelHistoryFinalizedService;
import com.paytm.saga.service.ChannelHistoryService;
import com.paytm.saga.service.DropOffService;
import com.paytm.saga.service.ReminderHistoryService;
import com.paytm.saga.service.aggregator.ElectricityAggregatorService;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.service.aggregator.cardmanager.CurrentBillCard;
import com.paytm.saga.service.aggregator.cardmanager.DateCard;
import com.paytm.saga.service.aggregator.cardmanager.DropOffCard;
import com.paytm.saga.service.aggregator.cardmanager.MarkedAsPaidCard;
import com.paytm.saga.service.aggregator.cardmanager.RechargeCard;
import com.paytm.saga.service.aggregator.cardmanager.ScratchCard;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.OMSToRechargeStatus;

public class ElectricityAggregatorServiceTest {
	@InjectMocks
	private ElectricityAggregatorService electricityAggregatorService;
	
	@Mock
	private ChannelHistoryFinalizedService channelHistoryFinalizedService;
	@Mock
	private ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
	@Mock
	private DateCard dateCardService;
	@Mock
	private ScratchCard scratchCardService;
	@Mock
	private RechargeCard rechargeCard;
	@Mock
	private DropOffCard dropoffCard;
	@Mock
	private ChannelHistoryService channelHistoryService;
	@Mock
	private DropOffService dropOffService;
	@Mock
	private ReminderHistoryService reminderHistoryService;
	@Mock
	private MarkedAsPaidCard markedAsPaidCardService;
	@Mock
	private CurrentBillCard currentBillCard;
	private static final Date TRANSACTION_DATE = DateUtil.minutesIncrDecr(new Date(), -5);
	private static final Date TRANSACTION_UPDATE_DATE = DateUtil.minutesIncrDecr(new Date(), -4);
	private static final Date CREATED_DATE = DateUtil.minutesIncrDecr(new Date(), -3);
	private static final Date FINALIZED_TRANSACTION_DATE = DateUtil
			.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60), -5);
	private static final Date FINALIZED_TRANSACTION_UPDATE_DATE = DateUtil
			.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60), -4);
	private static final Date FINALIZED_CREATED_DATE = DateUtil.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60),
			-3);
	

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void noTxn() {
		Long customerId = 1008952334L;
		String rechargeNumber = "0557622222";
		String service = "electricity";
		String operator = "hubli electricity supply company ltd. (hescom)";
		when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
				.thenReturn(new ArrayList<ChannelHistory>());

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 0, null))
				.thenReturn(channelHistoryPage);

		when(scratchCardService.getScratchCard(13035933673L)).thenReturn(new ArrayList<HistoryView>());
		when(dropOffService.getDropOff(customerId, rechargeNumber, service, operator)).thenReturn(null);

		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
				"cylinder booking", operator)).thenReturn(new ArrayList<ReminderHistory>());

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(customerId, rechargeNumber, service,
				null, operator, null, null,null,null);
		assertEquals(response.getCards().size(), 0);
	}

	@Test
	public void singleTxn() {
		Date transactionDate = TRANSACTION_DATE;
		Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = CREATED_DATE;
		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(26L).setOperator("hubli electricity supply company ltd. (hescom)")
				.setCreatedTime(eventUpdateDate).setCustomerId(450471692L).setEventType("RECHARGE")
				.setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L).setOrderId(12862601947L)
				.setPaytype("postpaid").setProductId(110083094L).setRechargeNumber("0070204115")
				.setService("electricity").setStatus("7").setTransactionTime(transactionDate)
				.setTransactionUpdateTime(transactionUpdateDate).build();

		channelHistories.add(channelHistory);
		when(channelHistoryService.getPageOfHistory(450471692L, "0070204115", "electricity"))
				.thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(450471692L, "0070204115", "electricity",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);

		when(dateCardService.addDateInfoCard(transactionDate, null, false)).thenReturn(null);

		ViewElementInfo dateHeader = new ViewElementInfo();
		dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
		headers.add(dateHeader);
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(headers).build();
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);

		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard = new HistoryViewBuilder().setEventDate(channelHistory.getTransactionTime())
				.setCreatedDate(
						DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistory.getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
						channelHistory.getInResponseCode(), channelHistory.getPaymentStatus()))
				.setOrderId(channelHistory.getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistory.getDisplayValues()).setEventDate(channelHistory.getTransactionTime())
				.setCircle(channelHistory.getCircle()).setOperator(channelHistory.getOperator())
				.setPayType(channelHistory.getPaytype()).setEventType(channelHistory.getEventType()).setThemeType("")
				.setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(450471692L, "0070204115",
				"electricity", null, "hubli electricity supply company ltd. (hescom)", null, null,null,null);
		// also confirm date header and date object position
		assertEquals(response.getCards().size(), 2);
	}

	@Test
	public void singleTxn45DaysOld() {
		Date transactionDate = FINALIZED_TRANSACTION_DATE;
		Date transactionUpdateDate = FINALIZED_TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = FINALIZED_CREATED_DATE;
		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(26L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(450471692L)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
				.setOperator("paschim gujarat vij company limited").setOrderId(12862601947L).setPaymentStatus(null)
				.setPaytype("postpaid").setProductId(110083094L).setRechargeNumber("38864034684")
				.setService("electricity").setStatus("7").setTransactionTime(transactionDate)
				.setTransactionUpdateTime(transactionUpdateDate).build();

		channelHistories.add(channelHistory);
		when(channelHistoryService.getPageOfHistory(450471692L, "38864034684", "electricity"))
				.thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(450471692L, "38864034684", "electricity",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);

		when(dateCardService.addDateInfoCard(transactionDate, null, false)).thenReturn(null);

		ViewElementInfo dateHeader = new ViewElementInfo();
		dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
		headers.add(dateHeader);
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(headers).build();
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);
		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard = new HistoryViewBuilder().setEventDate(channelHistory.getTransactionTime())
				.setCreatedDate(
						DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistory.getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
						channelHistory.getInResponseCode(), channelHistory.getPaymentStatus()))
				.setOrderId(channelHistory.getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistory.getDisplayValues()).setEventDate(channelHistory.getTransactionTime())
				.setCircle(channelHistory.getCircle()).setOperator(channelHistory.getOperator())
				.setPayType(channelHistory.getPaytype()).setEventType(channelHistory.getEventType()).setThemeType("")
				.setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);

		List<ChannelHistoryFinalized> channelFinalizedHistories = new ArrayList<ChannelHistoryFinalized>();
		channelFinalizedHistories.add(new ChannelHistoryFinalizedBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(166690L).setCircle(null).setCreatedTime(eventUpdateDate).setCustomerId(285959997L)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L)
				.setOperator("paschim gujarat vij company limited").setOrderId(12862601947L).setPaymentStatus(null)
				.setPaytype("recharge").setProductId(322951958L).setRechargeNumber("38864034684")
				.setService("electricity").setStatus("7").setTransactionTime(transactionDate)
				.setTransactionUpdateTime(transactionUpdateDate).build());

		when(channelHistoryFinalizedUtil.convertHistoryToFinalizedHistory(channelHistories))
				.thenReturn(channelFinalizedHistories);

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(450471692L, "38864034684",
				"electricity", null, "paschim gujarat vij company limited", null, null,null,null);
		// also confirm date header and date object position, count of txns

		assertEquals(response.getCards().size(), 2);
	}

	@Test
	public void multipleTxnInChannelHistory() {
		Date transaction1Date = DateUtil.minutesIncrDecr(TRANSACTION_DATE, -2);
		Date transaction1UpdateDate = DateUtil.minutesIncrDecr(TRANSACTION_UPDATE_DATE, -2);
		Date event1UpdateDate = DateUtil.minutesIncrDecr(CREATED_DATE, -2);
		Date transaction2Date = DateUtil.minutesIncrDecr(TRANSACTION_DATE, -1);
		Date transaction2UpdateDate = DateUtil.minutesIncrDecr(TRANSACTION_UPDATE_DATE, -1);
		Date event2UpdateDate = DateUtil.minutesIncrDecr(CREATED_DATE, -1);

		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject).setCategoryId(26L)
				.setCircle(null).setCreatedTime(event2UpdateDate).setCustomerId(1013384681L).setEventType("RECHARGE")
				.setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
				.setOperator("west bengal state electricity distribution company limited (wbsedcl)")
				.setOrderId(13029567479L).setPaymentStatus(null).setPaytype("postpaid").setProductId(319238046L)
				.setRechargeNumber("*********").setService("electricity").setStatus("7")
				.setTransactionTime(transaction2Date).setTransactionUpdateTime(transaction2UpdateDate).build());
		channelHistories.add(new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject).setCategoryId(26L)
				.setCircle(null).setCreatedTime(event1UpdateDate).setCustomerId(1013384681L).setEventType("RECHARGE")
				.setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L)
				.setOperator("west bengal state electricity distribution company limited (wbsedcl)")
				.setOrderId(13058445443L).setPaymentStatus(null).setPaytype("postpaid").setProductId(319238046L)
				.setRechargeNumber("*********").setService("electricity").setStatus("7")
				.setTransactionTime(transaction1Date).setTransactionUpdateTime(transaction1UpdateDate).build());
		when(channelHistoryService.getPageOfHistory(1013384681L, "*********", "electricity"))
				.thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(1013384681L, "*********", "electricity",
				channelHistories.size(), null)).thenReturn(channelHistoryPage);
		when(dateCardService.addDateInfoCard(transaction2Date, null, false)).thenReturn(null);

		ViewElementInfo date1Header = new ViewElementInfo();
		date1Header.setValue(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date1headers = new ArrayList<ViewElementInfo>();
		date1headers.add(date1Header);
		HistoryView date1Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date1headers).build();
		when(dateCardService.addDateInfoCard(transaction1Date, transaction2Date, false)).thenReturn(date1Card);

		ViewElementInfo date2Header = new ViewElementInfo();
		date2Header.setValue(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date2headers = new ArrayList<ViewElementInfo>();
		date2headers.add(date2Header);
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date2headers).build();
		when(dateCardService.addDateInfoCard(transaction1Date, transaction1Date, true)).thenReturn(date2Card);
		when(scratchCardService.getScratchCard(13029567479L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(13058445443L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard1 = new HistoryViewBuilder()
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistories.get(0).getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistories.get(0).getStatus(),
						channelHistories.get(0).getInResponseCode(), channelHistories.get(0).getPaymentStatus()))
				.setOrderId(channelHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistories.get(0).getDisplayValues())
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCircle(channelHistories.get(0).getCircle()).setOperator(channelHistories.get(0).getOperator())
				.setPayType(channelHistories.get(0).getPaytype()).setEventType(channelHistories.get(0).getEventType())
				.setThemeType("").setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo1Dto = new CardInfoDto();
		cardInfo1Dto.setChannelHistory(channelHistories.get(0));
		cardInfo1Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo1Dto)).thenReturn(rechargeViewCard1);

		HistoryView rechargeViewCard2 = new HistoryViewBuilder()
				.setEventDate(channelHistories.get(1).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistories.get(1).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistories.get(1).getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistories.get(1).getStatus(),
						channelHistories.get(1).getInResponseCode(), channelHistories.get(1).getPaymentStatus()))
				.setOrderId(channelHistories.get(1).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistories.get(1).getDisplayValues())
				.setEventDate(channelHistories.get(1).getTransactionTime())
				.setCircle(channelHistories.get(1).getCircle()).setOperator(channelHistories.get(1).getOperator())
				.setPayType(channelHistories.get(1).getPaytype()).setEventType(channelHistories.get(1).getEventType())
				.setThemeType("").setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo2Dto = new CardInfoDto();
		cardInfo2Dto.setChannelHistory(channelHistories.get(1));
		cardInfo2Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo2Dto)).thenReturn(rechargeViewCard2);

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(1013384681L, "*********",
				"electricity", null, "west bengal state electricity distribution company limited (wbsedcl)", null,
				null,null,null);
		// also confirm date header and date object position, check value of date cards
		assertEquals(4, response.getCards().size());
	}

	@Test
	public void oneTxnInChannelHistoryAndOneInFinalizedChannelHistory() {
		Date transaction1Date = TRANSACTION_DATE;
		Date transaction1UpdateDate = TRANSACTION_UPDATE_DATE;
		Date event1UpdateDate = CREATED_DATE;
		Date transaction2Date = FINALIZED_TRANSACTION_DATE;
		Date transaction2UpdateDate = FINALIZED_TRANSACTION_UPDATE_DATE;
		Date event2UpdateDate = FINALIZED_CREATED_DATE;
		Long categoryId = 26L;
		Long customerId = 538959528L;
		String circle = null;
		String service = "electricity";
		String rechargeNumber = "*********";
		String operator = "west bengal state electricity distribution company limited (wbsedcl)";
		String payType = "postpaid";

		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(event1UpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
				.setOperator(operator).setOrderId(12862601948L).setPaymentStatus(null).setPaytype("recharge")
				.setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service).setStatus("7")
				.setTransactionTime(transaction1Date).setTransactionUpdateTime(transaction1UpdateDate).build());
		when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service)).thenReturn(channelHistories);

		List<ChannelHistory> channelFinalizedHistories = new ArrayList<ChannelHistory>();
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(event2UpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00").setItemId(13353752764L)
				.setOperator(operator).setOrderId(12862601949L).setPaymentStatus(null).setPaytype(payType)
				.setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service).setStatus("7")
				.setTransactionTime(transaction2Date).setTransactionUpdateTime(transaction2UpdateDate).build());
		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(channelFinalizedHistories);
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service,
				channelHistories.size(), null)).thenReturn(channelHistoryPage);
		System.out.println("mock eventDate=" + transaction1Date + ",previousDate=" + null + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction1Date, null, false)).thenReturn(null);

		ViewElementInfo date1Header = new ViewElementInfo();
		date1Header.setValue(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date1headers = new ArrayList<ViewElementInfo>();
		date1headers.add(date1Header);
		HistoryView date1Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction1Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date1headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction1Date + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction1Date, false)).thenReturn(date1Card);

		ViewElementInfo date2Header = new ViewElementInfo();
		date2Header.setValue(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date2headers = new ArrayList<ViewElementInfo>();
		date2headers.add(date2Header);
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date2headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction2Date + ",isLastCard=" + true);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction2Date, true)).thenReturn(date2Card);
		when(scratchCardService.getScratchCard(12862601948L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601949L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard1 = new HistoryViewBuilder()
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistories.get(0).getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistories.get(0).getStatus(),
						channelHistories.get(0).getInResponseCode(), channelHistories.get(0).getPaymentStatus()))
				.setOrderId(channelHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistories.get(0).getDisplayValues())
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCircle(channelHistories.get(0).getCircle()).setOperator(channelHistories.get(0).getOperator())
				.setPayType(channelHistories.get(0).getPaytype()).setEventType(channelHistories.get(0).getEventType())
				.setThemeType("").setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo1Dto = new CardInfoDto();
		cardInfo1Dto.setChannelHistory(channelHistories.get(0));
		cardInfo1Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo1Dto)).thenReturn(rechargeViewCard1);

		HistoryView rechargeViewCard2 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(0).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(0).getStatus(),
								channelFinalizedHistories.get(0).getInResponseCode(),
								channelFinalizedHistories.get(0).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(0).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(0).getCircle())
				.setOperator(channelFinalizedHistories.get(0).getOperator())
				.setPayType(channelFinalizedHistories.get(0).getPaytype())
				.setEventType(channelFinalizedHistories.get(0).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo2Dto = new CardInfoDto();
		cardInfo2Dto.setChannelHistory(channelFinalizedHistories.get(0));
		cardInfo2Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo2Dto)).thenReturn(rechargeViewCard2);

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(customerId, rechargeNumber, service,
				null, operator, null, null,null,null);
		// also confirm date header and date object position, check value of date cards
		assertEquals(4, response.getCards().size());

	}

	@Test
	public void twoTxnsInFinalizedChannelHistory() {
		Date transaction1Date = FINALIZED_TRANSACTION_DATE;
		Date transaction1UpdateDate = FINALIZED_TRANSACTION_UPDATE_DATE;
		Date event1UpdateDate = FINALIZED_CREATED_DATE;
		Date transaction2Date = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_DATE, -2);
		Date transaction2UpdateDate = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_UPDATE_DATE, -2);
		Date event2UpdateDate = DateUtil.dateIncrDecr(FINALIZED_CREATED_DATE, -2);
		Long categoryId = 26L;
		Long customerId = 538959529L;
		String circle = null;
		String service = "electricity";
		String rechargeNumber = "*********";
		String operator = "west bengal state electricity distribution company limited (wbsedcl)";
		String payType = "postpaid";

		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service)).thenReturn(channelHistories);

		List<ChannelHistory> channelFinalizedHistories = new ArrayList<ChannelHistory>();
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(event1UpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00").setItemId(13353752764L)
				.setOperator(operator).setOrderId(12862601948L).setPaymentStatus(null).setPaytype(payType)
				.setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service).setStatus("7")
				.setTransactionTime(transaction1Date).setTransactionUpdateTime(transaction1UpdateDate).build());
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(event2UpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
				.setOperator(operator).setOrderId(12862601949L).setPaymentStatus(null).setPaytype(payType)
				.setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service).setStatus("7")
				.setTransactionTime(transaction2Date).setTransactionUpdateTime(transaction2UpdateDate).build());
		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(channelFinalizedHistories);
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service,
				channelHistories.size(), null)).thenReturn(channelHistoryPage);
		System.out.println("mock eventDate=" + transaction1Date + ",previousDate=" + null + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction1Date, null, false)).thenReturn(null);

		ViewElementInfo date1Header = new ViewElementInfo();
		date1Header.setValue(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date1headers = new ArrayList<ViewElementInfo>();
		date1headers.add(date1Header);
		HistoryView date1Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction1Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date1headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction1Date + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction1Date, false)).thenReturn(date1Card);

		ViewElementInfo date2Header = new ViewElementInfo();
		date2Header.setValue(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date2headers = new ArrayList<ViewElementInfo>();
		date2headers.add(date2Header);
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date2headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction2Date + ",isLastCard=" + true);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction2Date, true)).thenReturn(date2Card);
		when(scratchCardService.getScratchCard(12862601948L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601949L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard1 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(0).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(0).getStatus(),
								channelFinalizedHistories.get(0).getInResponseCode(),
								channelFinalizedHistories.get(0).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(0).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(0).getCircle())
				.setOperator(channelFinalizedHistories.get(0).getOperator())
				.setPayType(channelFinalizedHistories.get(0).getPaytype())
				.setEventType(channelFinalizedHistories.get(0).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo1Dto = new CardInfoDto();
		cardInfo1Dto.setChannelHistory(channelFinalizedHistories.get(0));
		cardInfo1Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo1Dto)).thenReturn(rechargeViewCard1);

		HistoryView rechargeViewCard2 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(1).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(1).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(1).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(1).getStatus(),
								channelFinalizedHistories.get(1).getInResponseCode(),
								channelFinalizedHistories.get(1).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(1).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(1).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(1).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(1).getCircle())
				.setOperator(channelFinalizedHistories.get(1).getOperator())
				.setPayType(channelFinalizedHistories.get(1).getPaytype())
				.setEventType(channelFinalizedHistories.get(1).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo2Dto = new CardInfoDto();
		cardInfo2Dto.setChannelHistory(channelFinalizedHistories.get(1));
		cardInfo2Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo2Dto)).thenReturn(rechargeViewCard2);

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(customerId, rechargeNumber, service,
				null, operator, null, null,null,null);

		assertEquals(4, response.getCards().size());

	}

	@Test
	public void singleTxnWithMultipleStates() {
		Date transactionDate = TRANSACTION_DATE;
		Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = CREATED_DATE;
		Date transactionUpdateOldDate = DateUtil.minutesIncrDecr(TRANSACTION_UPDATE_DATE, -10);
		Date eventUpdateOldDate = DateUtil.minutesIncrDecr(CREATED_DATE, -10);
		Long categoryId = 26L;
		Long customerId = 538959629L;
		String circle = null;
		String service = "electricity";
		String rechargeNumber = "*********";
		String operator = "west bengal state electricity distribution company limited (wbsedcl)";
		String payType = "postpaid";
		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		ChannelHistory channelHistoryCurrentState = new ChannelHistoryBuilder().setAmount("767")
				.setBillsObj(billsObject).setCategoryId(categoryId).setCircle(circle).setCreatedTime(eventUpdateDate)
				.setCustomerId(customerId).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752763L).setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
				.setPaytype(payType).setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service)
				.setStatus("7").setTransactionTime(transactionDate).setTransactionUpdateTime(transactionUpdateDate)
				.build();
		ChannelHistory channelHistoryOldState = new ChannelHistoryBuilder().setAmount("767").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(eventUpdateOldDate)
				.setCustomerId(customerId).setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00")
				.setItemId(13353752763L).setOperator(operator).setOrderId(12862601947L).setPaymentStatus(null)
				.setPaytype(payType).setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service)
				.setStatus("7").setTransactionTime(transactionDate).setTransactionUpdateTime(transactionUpdateOldDate)
				.build();
		channelHistories.add(channelHistoryCurrentState);
		channelHistories.add(channelHistoryOldState);
		when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service)).thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 1, null))
				.thenReturn(channelHistoryPage);

		when(dateCardService.addDateInfoCard(transactionDate, null, false)).thenReturn(null);

		ViewElementInfo dateHeader = new ViewElementInfo();
		dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
		headers.add(dateHeader);
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(headers).build();
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);

		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard = new HistoryViewBuilder()
				.setEventDate(channelHistoryCurrentState.getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistoryCurrentState.getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistoryCurrentState.getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistoryCurrentState.getStatus(),
						channelHistoryCurrentState.getInResponseCode(), channelHistoryCurrentState.getPaymentStatus()))
				.setOrderId(channelHistoryCurrentState.getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistoryCurrentState.getDisplayValues())
				.setEventDate(channelHistoryCurrentState.getTransactionTime())
				.setCircle(channelHistoryCurrentState.getCircle()).setOperator(channelHistoryCurrentState.getOperator())
				.setPayType(channelHistoryCurrentState.getPaytype())
				.setEventType(channelHistoryCurrentState.getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistoryCurrentState);
		cardInfoDto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(customerId, rechargeNumber, service,
				null, operator, null, null,null,null);
		// also confirm date header and date object position
		assertEquals(2, response.getCards().size());
	}

	@Test
	public void oneTxnInChannelHistoryAndSixInFinalizedChannelHistory() {
		Date transaction1Date = TRANSACTION_DATE;
		Date transaction1UpdateDate = TRANSACTION_UPDATE_DATE;
		Date event1UpdateDate = CREATED_DATE;
		Date transaction2Date = FINALIZED_TRANSACTION_DATE;
		Date transaction2UpdateDate = FINALIZED_TRANSACTION_UPDATE_DATE;
		Date event2UpdateDate = FINALIZED_CREATED_DATE;
		Date transaction3Date = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_DATE, -1);
		Date transaction3UpdateDate = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_UPDATE_DATE, -1);
		Date event3UpdateDate = DateUtil.dateIncrDecr(FINALIZED_CREATED_DATE, -1);
		Date transaction4Date = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_DATE, -2);
		Date transaction4UpdateDate = DateUtil.dateIncrDecr(FINALIZED_TRANSACTION_UPDATE_DATE, -2);
		Date event4UpdateDate = DateUtil.dateIncrDecr(FINALIZED_CREATED_DATE, -2);
		Long categoryId = 26L;
		Long customerId = 5666959629L;
		String circle = null;
		String service = "electricity";
		String rechargeNumber = "*********";
		String operator = "west bengal state electricity distribution company limited (wbsedcl)";
		String payType = "postpaid";

		Map<String, String> billsObject = new HashMap<>();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(event1UpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
				.setOperator(operator).setOrderId(12862601948L).setPaymentStatus(null).setPaytype(payType)
				.setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service).setStatus("7")
				.setTransactionTime(transaction1Date).setTransactionUpdateTime(transaction1UpdateDate).build());
		when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service)).thenReturn(channelHistories);

		List<ChannelHistory> channelFinalizedHistories = new ArrayList<ChannelHistory>();
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(event2UpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00").setItemId(13353752764L)
				.setOperator(operator).setOrderId(12862601949L).setPaymentStatus(null).setPaytype(payType)
				.setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service).setStatus("7")
				.setTransactionTime(transaction2Date).setTransactionUpdateTime(transaction2UpdateDate).build());
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(event3UpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00").setItemId(13353752764L)
				.setOperator(operator).setOrderId(12862601950L).setPaymentStatus(null).setPaytype(payType)
				.setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service).setStatus("7")
				.setTransactionTime(transaction3Date).setTransactionUpdateTime(transaction3UpdateDate).build());
		channelFinalizedHistories.add(new ChannelHistoryBuilder().setAmount("768").setBillsObj(billsObject)
				.setCategoryId(categoryId).setCircle(circle).setCreatedTime(event4UpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(true).setInResponseCode("00").setItemId(13353752764L)
				.setOperator(operator).setOrderId(12862601951L).setPaymentStatus(null).setPaytype(payType)
				.setProductId(322951958L).setRechargeNumber(rechargeNumber).setService(service).setStatus("7")
				.setTransactionTime(transaction4Date).setTransactionUpdateTime(transaction4UpdateDate).build());
		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(true);
		channelHistoryPage.setLastCardId("kjxhcgkjdfgkjdnlkxdgjhlxkdgj");
		channelHistoryPage.setChannelHistories(channelFinalizedHistories);
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service,
				channelHistories.size(), null)).thenReturn(channelHistoryPage);
		System.out.println("mock eventDate=" + transaction1Date + ",previousDate=" + null + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction1Date, null, false)).thenReturn(null);

		ViewElementInfo date1Header = new ViewElementInfo();
		date1Header.setValue(DateUtil.dateFormatter(transaction1Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date1headers = new ArrayList<ViewElementInfo>();
		date1headers.add(date1Header);
		HistoryView date1Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction1Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date1headers).build();
		System.out.println(
				"mock eventDate=" + transaction2Date + ",previousDate=" + transaction1Date + ",isLastCard=" + false);
		when(dateCardService.addDateInfoCard(transaction2Date, transaction1Date, false)).thenReturn(date1Card);

		ViewElementInfo date2Header = new ViewElementInfo();
		date2Header.setValue(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date2headers = new ArrayList<ViewElementInfo>();
		date2headers.add(date2Header);
		HistoryView date2Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction2Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction2Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date2headers).build();
		System.out.println(
				"mock eventDate=" + transaction3Date + ",previousDate=" + transaction2Date + ",isLastCard=" + true);
		when(dateCardService.addDateInfoCard(transaction3Date, transaction2Date, false)).thenReturn(date2Card);

		ViewElementInfo date3Header = new ViewElementInfo();
		date3Header.setValue(DateUtil.dateFormatter(transaction3Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date3headers = new ArrayList<ViewElementInfo>();
		date3headers.add(date3Header);
		HistoryView date3Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction3Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction3Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date3headers).build();
		System.out.println(
				"mock eventDate=" + transaction4Date + ",previousDate=" + transaction3Date + ",isLastCard=" + true);
		when(dateCardService.addDateInfoCard(transaction4Date, transaction3Date, false)).thenReturn(date3Card);

		ViewElementInfo date4Header = new ViewElementInfo();
		date4Header.setValue(DateUtil.dateFormatter(transaction4Date, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> date4headers = new ArrayList<ViewElementInfo>();
		date4headers.add(date4Header);
		HistoryView date4Card = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transaction4Date).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transaction4Date, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(date4headers).build();
		System.out.println(
				"mock eventDate=" + transaction4Date + ",previousDate=" + transaction4Date + ",isLastCard=" + true);

		when(dateCardService.addDateInfoCard(transaction4Date, transaction4Date, true)).thenReturn(date4Card);

		when(scratchCardService.getScratchCard(12862601948L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601949L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601950L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601951L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601952L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601953L)).thenReturn(new ArrayList<HistoryView>());
		when(scratchCardService.getScratchCard(12862601954L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard1 = new HistoryViewBuilder()
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistories.get(0).getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistories.get(0).getStatus(),
						channelHistories.get(0).getInResponseCode(), channelHistories.get(0).getPaymentStatus()))
				.setOrderId(channelHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistories.get(0).getDisplayValues())
				.setEventDate(channelHistories.get(0).getTransactionTime())
				.setCircle(channelHistories.get(0).getCircle()).setOperator(channelHistories.get(0).getOperator())
				.setPayType(channelHistories.get(0).getPaytype()).setEventType(channelHistories.get(0).getEventType())
				.setThemeType("").setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo1Dto = new CardInfoDto();
		cardInfo1Dto.setChannelHistory(channelHistories.get(0));
		cardInfo1Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo1Dto)).thenReturn(rechargeViewCard1);

		HistoryView rechargeViewCard2 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(0).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(0).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(0).getStatus(),
								channelFinalizedHistories.get(0).getInResponseCode(),
								channelFinalizedHistories.get(0).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(0).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(0).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(0).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(0).getCircle())
				.setOperator(channelFinalizedHistories.get(0).getOperator())
				.setPayType(channelFinalizedHistories.get(0).getPaytype())
				.setEventType(channelFinalizedHistories.get(0).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo2Dto = new CardInfoDto();
		cardInfo2Dto.setChannelHistory(channelFinalizedHistories.get(0));
		cardInfo2Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo2Dto)).thenReturn(rechargeViewCard2);

		HistoryView rechargeViewCard3 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(1).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(1).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(1).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(1).getStatus(),
								channelFinalizedHistories.get(1).getInResponseCode(),
								channelFinalizedHistories.get(1).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(1).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(1).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(1).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(1).getCircle())
				.setOperator(channelFinalizedHistories.get(1).getOperator())
				.setPayType(channelFinalizedHistories.get(1).getPaytype())
				.setEventType(channelFinalizedHistories.get(1).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo3Dto = new CardInfoDto();
		cardInfo3Dto.setChannelHistory(channelFinalizedHistories.get(1));
		cardInfo3Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo3Dto)).thenReturn(rechargeViewCard3);

		HistoryView rechargeViewCard4 = new HistoryViewBuilder()
				.setEventDate(channelFinalizedHistories.get(2).getTransactionTime())
				.setCreatedDate(DateUtil.dateFormatter(channelFinalizedHistories.get(2).getTransactionTime(),
						DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelFinalizedHistories.get(2).getAmount()))
				.setStatus(
						OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelFinalizedHistories.get(2).getStatus(),
								channelFinalizedHistories.get(2).getInResponseCode(),
								channelFinalizedHistories.get(2).getPaymentStatus()))
				.setOrderId(channelFinalizedHistories.get(2).getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelFinalizedHistories.get(2).getDisplayValues())
				.setEventDate(channelFinalizedHistories.get(2).getTransactionTime())
				.setCircle(channelFinalizedHistories.get(2).getCircle())
				.setOperator(channelFinalizedHistories.get(2).getOperator())
				.setPayType(channelFinalizedHistories.get(2).getPaytype())
				.setEventType(channelFinalizedHistories.get(2).getEventType()).setThemeType("").setHeadings(null)
				.setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfo4Dto = new CardInfoDto();
		cardInfo4Dto.setChannelHistory(channelFinalizedHistories.get(2));
		cardInfo4Dto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfo4Dto)).thenReturn(rechargeViewCard4);

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(customerId, rechargeNumber, service,
				null, operator, null, null,null,null);
		// also confirm date header and date object position, check value of date cards
		assertEquals(7, response.getCards().size());

	}

	//@Test
	public void upcomingActiveBill() {
		Date transactionDate = TRANSACTION_DATE;
		Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = CREATED_DATE;

		Long customerId = 4448952334L;
		String rechargeNumber = "2222222222";
		String service = "electricity";
		String operator = "hubli electricity supply company ltd. (hescom)";
		String payType = "postpaid";
		when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service))
				.thenReturn(new ArrayList<ChannelHistory>());

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 0, null))
				.thenReturn(channelHistoryPage);

		when(scratchCardService.getScratchCard(13035933673L)).thenReturn(new ArrayList<HistoryView>());
		when(dropOffService.getDropOff(customerId, rechargeNumber, service, operator)).thenReturn(null);

		List<ReminderHistory> reminderHistories = new ArrayList<ReminderHistory>();
		reminderHistories.add(new ReminderHistoryBuilder().setAmount(100.5).setBill_date(transactionDate)
				.setCreated_at(eventUpdateDate).setCustomerId(customerId).setDue_date(transactionUpdateDate)
				.setIsPartial(0).setOperator(operator).setPaytype(payType).setRechargeNumber(rechargeNumber)
				.setService(service).setStatus(1).setUpdatedAt(eventUpdateDate).build());
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
				service, operator)).thenReturn(reminderHistories);

		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setReminderHistory(reminderHistories.get(0));
		cardInfoDto.setPreviousTxnHistory(false);

		HistoryView billView = new HistoryViewBuilder().setService(reminderHistories.get(0).getService())
				.setOperator(reminderHistories.get(0).getOperator()).setAmount(reminderHistories.get(0).getAmount())
				.setDueDate(
						DateUtil.dateFormatter(reminderHistories.get(0).getDue_date(), DateFormats.DATE_TIME_FORMAT_2))
				.setBillDate(
						DateUtil.dateFormatter(reminderHistories.get(0).getBill_date(), DateFormats.DATE_TIME_FORMAT_2))
				.setEventType(EventTypes.BILL).setEventDate(transactionDate).build();

		when(currentBillCard.getCardInfo(cardInfoDto)).thenReturn(billView);
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true))
				.thenReturn(new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
						.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate)
						.setLastCard(false)
						.setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT)).build());
		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(customerId, rechargeNumber, service,
				null, operator, null, null,null,null);
		assertEquals(2, response.getCards().size());
	}
}
