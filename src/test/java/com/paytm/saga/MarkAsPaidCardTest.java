package com.paytm.saga;

import static org.junit.Assert.assertEquals;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.cardmanager.MarkedAsPaidCard;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;

public class MarkAsPaidCardTest {
	@Mock
	public ViewManager displayValuesManager;
	@Mock
	public ViewManager headersViewManager;
	@Mock
	public ViewManager ctaManager;
	@InjectMocks
	public MarkedAsPaidCard markedAsPaidCard;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testMarkAsPaidCard() {
		Map<String, String> billsObj = new HashMap<String, String>();
		billsObj.put(Constants.ReminderConstants.BILL_DATE, "2021-01-12 00:03:11");
		billsObj.put(Constants.ReminderConstants.DUE_DATE, "2021-03-12 00:03:11");
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setService("").setOperator("").setAmount("100")
				.setBillsObj(billsObj).setTransactionTime(new Date()).build();

		HistoryView markAsPaidCard = markedAsPaidCard.getMarkedAsPaidCard(channelHistory);
		assertEquals(markAsPaidCard.getThemeType(), CardThemeTypes.MARKED_AS_PAID_CARD_THEME);

	}
}
