package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

import java.util.*;

import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.dto.*;
import com.paytm.saga.service.*;
import com.paytm.saga.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import org.springframework.http.ResponseEntity;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.controller.RechargeSagaController;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.AggregatorServiceInterface;
public class RechargeSagaControllerTest {
	@Mock
	public AggregatorServiceInterface electricityAggregatorService;
	@Mock
	public ChannelHistoryService channelHistoryService;
	@Mock
	public NewThemeService newThemeService;

	@Mock
	public RecentsService recentsService;

	@Mock
	public CustomerNameService customerNameService;

	@Mock
	public DropOffService dropOffService;
	
	@InjectMocks
	public RechargeSagaController rechargeSagaController;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}
	public static String NAME_REQUIRED = "isNameRequired";


	private GetHistoryPageDTO mockHistoryRequest() {
		GetHistoryPageDTO getHistoryPageDTO = new GetHistoryPageDTO();
		getHistoryPageDTO.setCircle("circle");
		getHistoryPageDTO.setService("electricity");
		getHistoryPageDTO.setCustomerId(123L);
		getHistoryPageDTO.setOperator("operator");
		getHistoryPageDTO.setPayType("postpaid");
		getHistoryPageDTO.setRecharge_number("recharge_number_1");
		getHistoryPageDTO.setRecharge_number_2("recharge_number_2");
		getHistoryPageDTO.setRecharge_number_3("recharge_number_3");
		getHistoryPageDTO.setRecharge_number_4("recharge_number_4");
		return getHistoryPageDTO;
	}

	private CustomerNameRequest mockHistoryCustomerNameRequest() {
		CustomerNameRequest customerNameRequest = new CustomerNameRequest();
		customerNameRequest.setService("electricity");
		customerNameRequest.setCustomer_id(123L);
		customerNameRequest.setOperator("operator");
		customerNameRequest.setRecharge_number("recharge_number_1");
		customerNameRequest.setPid(null);
		return customerNameRequest;
	}
	private CustomerNameResponse mockHistoryResponseCustomerName(){
		CustomerNameResponse customerNameResponse = new CustomerNameResponse();
		customerNameResponse.setCustomer_id(123L);
		customerNameResponse.setRecharge_number("recharge_number_1");
		customerNameResponse.setService("electricity");
		customerNameResponse.setCustomer_name(mockRecentData().getNickName());
		return customerNameResponse;
	}

	private Recents mockRecentData(){
		RecentsPrimaryKey key = new RecentsPrimaryKey();
		key.setCustomerId(123L);
		key.setRechargeNumber("recharge_number_1");
		key.setService("electricity");
		key.setOperator("operator");
		key.setPlanBucket(null);
		Recents recent = new Recents();
		recent.setKey(key);
		recent.setNickName("electricity home");
		return recent;
	}

	private DropOff mockDropOffData(){
		DropOff dropOff = new DropOff();
		dropOff.setCustomerId(23L);
		dropOff.setService("electricity");
		dropOff.setStatus("210");
		dropOff.setRechargeNumber("recharge_number");
		dropOff.setOperator("tsspdcl");
		return dropOff;
	}


	@Test
	public void getHistoryTest() throws RechargeSagaBaseException {
		GetHistoryPageDTO getHistoryPageDTO = mockHistoryRequest();
		ChannelHistory channelHistory = new ChannelHistory();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(channelHistory);
		when(channelHistoryService.getPageOfHistory(getHistoryPageDTO.getCustomerId(),
				getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService())).thenReturn(channelHistories);
		String currentOperatorInfo = (getHistoryPageDTO.getOperator() + ":" + getHistoryPageDTO.getService() + ":"
				+ getHistoryPageDTO.getCircle()).toLowerCase();
		ChannelDetail channelDetail = new ChannelDetail();
		channelDetail.setCustomerId(getHistoryPageDTO.getCustomerId());
		channelDetail.setOperator(getHistoryPageDTO.getOperator());
		channelDetail.setRechargeNumber(getHistoryPageDTO.getRecharge_number());
		channelDetail.setService(getHistoryPageDTO.getService());
		HistoryPage historyPage = new HistoryPage(new ArrayList<HistoryView>(), 0, "lastCardId", false, "success", 200,
				channelDetail);
		when(electricityAggregatorService.aggregateHistoryInfo(getHistoryPageDTO.getCustomerId(),
				getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService().toLowerCase(),
				getHistoryPageDTO.getPagingState(), currentOperatorInfo, null, null, "androidapp", "1.5.0"))
						.thenReturn(historyPage);
		when(newThemeService.getThemes(getHistoryPageDTO.getThemehash())).thenReturn(null);
		when(recentsService.getCustomerName(getHistoryPageDTO,false )).thenReturn(mockRecentData().getNickName());
		HashMap<String, String> map = new HashMap<String, String>();
		map.put(NAME_REQUIRED, String.valueOf(false));

		ResponseEntity<HistoryPage> historyRes = rechargeSagaController.getHistory(getHistoryPageDTO, "androidapp",
				"1.5.0",true,map,123L);
		assertEquals(getHistoryPageDTO.getRecharge_number(),
				historyRes.getBody().getChannelDetails().getRechargeNumber());
		assertEquals(getHistoryPageDTO.getRecharge_number_2(),
				historyRes.getBody().getChannelDetails().getRechargeNumber2());
		assertEquals(getHistoryPageDTO.getRecharge_number_3(),
				historyRes.getBody().getChannelDetails().getRechargeNumber3());
		assertEquals(getHistoryPageDTO.getRecharge_number_4(),
				historyRes.getBody().getChannelDetails().getRechargeNumber4());
	}

	@Test
	public void getHistoryWithNickNameTest() throws RechargeSagaBaseException {
		GetHistoryPageDTO getHistoryPageDTO = mockHistoryRequest();
		ChannelHistory channelHistory = new ChannelHistory();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(channelHistory);
		when(channelHistoryService.getPageOfHistory(getHistoryPageDTO.getCustomerId(),
				getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService())).thenReturn(channelHistories);
		String currentOperatorInfo = (getHistoryPageDTO.getOperator() + ":" + getHistoryPageDTO.getService() + ":"
				+ getHistoryPageDTO.getCircle()).toLowerCase();
		ChannelDetail channelDetail = new ChannelDetail();
		channelDetail.setCustomerId(getHistoryPageDTO.getCustomerId());
		channelDetail.setOperator(getHistoryPageDTO.getOperator());
		channelDetail.setRechargeNumber(getHistoryPageDTO.getRecharge_number());
		channelDetail.setService(getHistoryPageDTO.getService());
		HistoryPage historyPage = new HistoryPage(new ArrayList<HistoryView>(), 0, "lastCardId", false, "success", 200,
				channelDetail);
		when(electricityAggregatorService.aggregateHistoryInfo(getHistoryPageDTO.getCustomerId(),
				getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService().toLowerCase(),
				getHistoryPageDTO.getPagingState(), currentOperatorInfo, null, null, "androidapp", "1.5.0"))
				.thenReturn(historyPage);
		when(newThemeService.getThemes(getHistoryPageDTO.getThemehash())).thenReturn(null);
		when(recentsService.getCustomerName(getHistoryPageDTO,true )).thenReturn(mockRecentData().getNickName());
		HashMap<String, String> map = new HashMap<String, String>();
		map.put(NAME_REQUIRED, String.valueOf(true));
		ResponseEntity<HistoryPage> historyRes = rechargeSagaController.getHistory(getHistoryPageDTO,"androidapp","1.5.0",true,map,123L);
		assertEquals("electricity home",
				historyRes.getBody().getCustomer_name());
	}

	@Test
	public void getHistoryWithNickNameTestForThemeDetails() throws RechargeSagaBaseException {
		GetHistoryPageDTO getHistoryPageDTO = mockHistoryRequest();
		getHistoryPageDTO.setNative(true);
		getHistoryPageDTO.setThemehash("1234243453233432");
		ChannelHistory channelHistory = new ChannelHistory();
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories.add(channelHistory);
		when(channelHistoryService.getPageOfHistory(getHistoryPageDTO.getCustomerId(),
				getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService())).thenReturn(channelHistories);
		String currentOperatorInfo = (getHistoryPageDTO.getOperator() + ":" + getHistoryPageDTO.getService() + ":"
				+ getHistoryPageDTO.getCircle()).toLowerCase();
		ChannelDetail channelDetail = new ChannelDetail();
		channelDetail.setCustomerId(getHistoryPageDTO.getCustomerId());
		channelDetail.setOperator(getHistoryPageDTO.getOperator());
		channelDetail.setRechargeNumber(getHistoryPageDTO.getRecharge_number());
		channelDetail.setService(getHistoryPageDTO.getService());
		HistoryPage historyPage = new HistoryPage(new ArrayList<HistoryView>(), 0, "lastCardId", false, "success", 200,
				channelDetail);
		ThemeResponse themeResponse = new ThemeResponse();
		String themeStr="[{\"themeType\":\"themeType1\",\"themeDetail\":{\"header1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"footer1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"footer2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true}}}]";
		themeResponse.setThemes(JsonUtils.parseJsonToList(themeStr, ThemeDto.class));
		when(newThemeService.getThemesForNativeCategory()).thenReturn(themeResponse);
		when(electricityAggregatorService.aggregateHistoryInfo(getHistoryPageDTO.getCustomerId(),
				getHistoryPageDTO.getRecharge_number(), getHistoryPageDTO.getService().toLowerCase(),
				getHistoryPageDTO.getPagingState(), currentOperatorInfo, null, null, "androidapp", "1.5.0"))
				.thenReturn(historyPage);
		when(recentsService.getCustomerName(getHistoryPageDTO,true )).thenReturn(mockRecentData().getNickName());
		HashMap<String, String> map = new HashMap<String, String>();
		map.put(NAME_REQUIRED, String.valueOf(true));
		ResponseEntity<HistoryPage> historyRes = rechargeSagaController.getHistory(getHistoryPageDTO,"androidapp","1.5.0",true,map,123L);
		assertEquals("electricity home",
				historyRes.getBody().getCustomer_name());
		assertNotNull(historyRes.getBody().getThemeDetails());
	}

	@Test
	public void getCustomerNameTest(){
		CustomerNameRequest customerNameRequest = mockHistoryCustomerNameRequest();
		when(customerNameService.getCustomerName(customerNameRequest.getCustomer_id(),customerNameRequest.getService(),
				customerNameRequest.getRecharge_number(),customerNameRequest.getOperator())).thenReturn(mockRecentData().getNickName());
		when(customerNameService.getCustomerNameResponse(customerNameRequest)).thenReturn(mockHistoryResponseCustomerName());
		CustomerNameResponse customerNameResponse = customerNameService.getCustomerNameResponse(customerNameRequest);
		assertEquals("electricity home",customerNameResponse.getCustomer_name());
	}

	@Test
	public void getDropOffTest(){
		HashMap<String, String> requestMap = new HashMap<String, String>();
		requestMap.put("service", "electricity");
		requestMap.put("isCoft",String.valueOf(false));
		when(dropOffService.getDropOff(23L,"recharge_number","electricty","tsspdcl")).thenReturn(mockDropOffData());
		Map<String, DropOffResponse> dropOffResponse = dropOffService.getDropOffResponse(23L, Collections.singletonList("electricity"), null,
				null, requestMap.get("isCoft"));

		assertEquals(0,dropOffResponse.size());
	}
}
