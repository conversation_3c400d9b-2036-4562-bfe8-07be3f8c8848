package com.paytm.saga;

import static org.junit.Assert.assertEquals;

import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.service.aggregator.viewmanager.impl.ElectricityCTAManager;

public class ElectricityCTAManagerTest {

	@InjectMocks
	private ElectricityCTAManager electricityCTAManager;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void newBillCardType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setService("electricity")
				.setStatus("SUCCESS").setEventDate(new Date()).build();
		historyView.setThemeType(CardThemeTypes.NEW_BILL_CARD_THEME);

		List<ViewElementInfo> ctas = electricityCTAManager.getCTAs(historyView);
		assertEquals(2, ctas.size());

	}
	@Test
	public void billOverDueCardType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setService("electricity")
				.setStatus("SUCCESS").setEventDate(new Date()).build();
		historyView.setThemeType(CardThemeTypes.BILL_OVER_DUE_CARD_THEME);

		List<ViewElementInfo> ctas = electricityCTAManager.getCTAs(historyView);
		assertEquals(2, ctas.size());

	}
	@Test
	public void billPartialPaid() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setService("electricity")
				.setStatus("SUCCESS").setEventDate(new Date()).build();
		historyView.setThemeType(CardThemeTypes.BILL_PARTIAL_PAID_CARD_THEME);

		List<ViewElementInfo> ctas = electricityCTAManager.getCTAs(historyView);
		assertEquals(2, ctas.size());

	}
	@Test
	public void billAfterDueDate() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setService("electricity")
				.setStatus("SUCCESS").setEventDate(new Date()).build();
		historyView.setThemeType(CardThemeTypes.BILL_AFTER_OVER_DUE_CARD_THEME);

		List<ViewElementInfo> ctas = electricityCTAManager.getCTAs(historyView);
		assertEquals(2, ctas.size());

	}
	@Test
	public void billExpiredCard() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setService("electricity")
				.setStatus("SUCCESS").setEventDate(new Date()).build();
		historyView.setThemeType(CardThemeTypes.BILL_EXPIRED_CARD_THEME);

		List<ViewElementInfo> ctas = electricityCTAManager.getCTAs(historyView);
		assertEquals(1, ctas.size());

	}
	@Test
	public void dropOffCard() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setService("electricity")
				.setStatus("SUCCESS").setEventDate(new Date()).build();
		historyView.setThemeType(CardThemeTypes.DROPOFF_CARD_THEME);

		List<ViewElementInfo> ctas = electricityCTAManager.getCTAs(historyView);
		assertEquals(1, ctas.size());

	}
	@Test
	public void failureCard() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setService("electricity")
				.setStatus("SUCCESS").setEventDate(new Date()).build();
		historyView.setThemeType(CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME);

		List<ViewElementInfo> ctas = electricityCTAManager.getCTAs(historyView);
		assertEquals(1, ctas.size());

	}
}
