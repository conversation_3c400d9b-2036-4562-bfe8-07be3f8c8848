package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.cardmanager.RechargeCard;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;

public class RechargeCardTest {
	@Mock
	private ViewManager ctaManager;
	@Mock
	private ViewManager displayValuesManager;
	@Mock
	private ViewManager headersViewManager;
	@InjectMocks
	private RechargeCard rechargeCard;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void successThemeType() {
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100").setPaytype("recharge")
				.setService("cylinder booking").setEventType("recharge").setStatus("7").setOrderId(123L)
				.setTransactionTime(new Date()).build();
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		HistoryView historyView = rechargeCard.getCardInfo(cardInfoDto);
		assertEquals(historyView.getThemeType(), "success_card");
		assertNull(historyView.getBookingId());
		assertEquals(historyView.getOperatorLabel(), "operator");
		assertEquals(historyView.isIvrsBooking(), false);
	}

	@Test
	public void ivrsAndOperatorLabelTest() {
		Map<String, String> billsObject = new HashMap<String, String>();
		billsObject.put("operatorLabel", "Indane Gas");
		billsObject.put("isCylinderIVRSBooking", "yes");
		billsObject.put("cylinderBookingId", "12345");
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100").setPaytype("recharge")
				.setService("cylinder booking").setOperator("indane gas").setEventType("recharge").setStatus("7").setOrderId(123L)
				.setTransactionTime(new Date()).setBillsObj(billsObject).build();
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		HistoryView historyView = rechargeCard.getCardInfo(cardInfoDto);
		assertEquals(historyView.getThemeType(), "success_card");
		assertEquals(historyView.getBookingId(), "12345");
		assertEquals(historyView.getOperatorLabel(), "Indane Gas");
		assertEquals(historyView.isIvrsBooking(), true);
	}

	@Test
	public void failureThemeType() {
		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100").setPaytype("recharge")
				.setService("cylinder booking").setEventType("recharge").setStatus("6").setOrderId(123L)
				.setTransactionTime(new Date()).build();
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		HistoryView historyView = rechargeCard.getCardInfo(cardInfoDto);
		assertEquals(historyView.getThemeType(), "failure_card");
	}

	@Test
	public void reversalFailureThemeType() {

		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100").setPaytype("recharge")
				.setService("cylinder booking").setEventType("recharge").setStatus("6").setOrderId(123L)
				.setTransactionTime(new Date()).setInResponseCode("11").build();
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		HistoryView historyView = rechargeCard.getCardInfo(cardInfoDto);
		assertEquals(historyView.getThemeType(), "failure_card");

	}

	@Test
	public void paymentFailureThemeType() {

		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100").setPaytype("recharge")
				.setService("cylinder booking").setEventType("recharge").setStatus("6").setOrderId(123L)
				.setTransactionTime(new Date()).setPaymentStatus("4").build();
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		HistoryView historyView = rechargeCard.getCardInfo(cardInfoDto);
		assertEquals(historyView.getThemeType(), "failure_card");

	}

	@Test
	public void pendingTxnThemeType() {

		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100").setPaytype("recharge")
				.setService("cylinder booking").setEventType("recharge").setStatus("5").setOrderId(123L)
				.setTransactionTime(new Date()).build();
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		HistoryView historyView = rechargeCard.getCardInfo(cardInfoDto);
		assertEquals(historyView.getThemeType(), "pending_card");

	}

	@Test
	public void userCancelTxnThemeType() {

		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("100").setPaytype("recharge")
				.setService("cylinder booking").setEventType("recharge").setStatus("6").setInResponseCode("10")
				.setOrderId(123L).setTransactionTime(new Date()).build();
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		HistoryView historyView = rechargeCard.getCardInfo(cardInfoDto);
		assertEquals(historyView.getThemeType(), "failure_card");

	}

	// failure, reversal_failure,payment failure,pending,user cancel
}
