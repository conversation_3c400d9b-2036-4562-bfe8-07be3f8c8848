package com.paytm.saga;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.custom.validator.ReferenceIdValidator;
import com.paytm.saga.dto.MarkAsPaidRequest;

public class ReferenceIdValidatorTest {
	@InjectMocks
	private ReferenceIdValidator referenceIdValidator;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}
	
	@Test
	public void isValidTestCase1() {
		MarkAsPaidRequest markAsPaidRequest=new MarkAsPaidRequest();
		markAsPaidRequest.setPaytype(Constants.CREDIT_CARD_PAYTYPE);
		assertFalse(referenceIdValidator.isValid(markAsPaidRequest, null));
	}
	
	@Test
	public void isValidTestCase2() {
		MarkAsPaidRequest markAsPaidRequest=new MarkAsPaidRequest();
		markAsPaidRequest.setPaytype(Constants.CREDIT_CARD_PAYTYPE);
		markAsPaidRequest.setReferenceId("123");
		assertTrue(referenceIdValidator.isValid(markAsPaidRequest, null));
	}
	
	@Test
	public void isValidTestCase3() {
		MarkAsPaidRequest markAsPaidRequest=new MarkAsPaidRequest();
		markAsPaidRequest.setPaytype("mobile");
		assertTrue(referenceIdValidator.isValid(markAsPaidRequest, null));
	}
}
