package com.paytm.saga;

import com.paytm.saga.appenders.LogAppenderResource;
import com.paytm.saga.common.exception.NickNameServiceException;
import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.NickNameRequest;
import com.paytm.saga.dto.NickNameResponse;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.service.RecentService;
import com.paytm.saga.service.RecentsServiceImpl;
import com.timgroup.statsd.StatsDClient;
import org.apache.logging.log4j.LogManager;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;


import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class NickNameTest {

    @Rule
    public LogAppenderResource appender = new LogAppenderResource(LogManager.getLogger(RecentsServiceImpl.class));
    @Mock
    private RecentsRepository recentsRepository;
    @Mock
    private MetricsHelper metricsHelper;
    @Mock
    private RecentService recentService;
    @Mock
    private KafkaTemplate kafkaTemplate;
    @Mock
    private StatsDClient monitoringClient;
    @InjectMocks
    private RecentsServiceImpl recentsService;

    private NickNameRequest prepareNonCreditCardData() {
        NickNameRequest nickNameRequest = new NickNameRequest();
        nickNameRequest.setCustomerId(7091L);
        nickNameRequest.setRechargeNumber("1222222");
        nickNameRequest.setService("electricity");
        nickNameRequest.setOperator("up bigli nigam");
        nickNameRequest.setPaytype("upi");
        nickNameRequest.setNickName("nick");
        return nickNameRequest;
    }

    private List<Recents> myRecent() {
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(7091L);
        recentsPrimaryKey.setRechargeNumber("1222222");
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setOperator("up bigli nigam");
        recent.setKey(recentsPrimaryKey);
        List<Recents> recents = new ArrayList<>();
        recents.add(recent);
        return recents;
    }

    private NickNameRequest prepareCreditCardDataWithPanUnique() {
        NickNameRequest nickNameRequest = new NickNameRequest();
        nickNameRequest.setCustomerId(7091L);
        nickNameRequest.setRechargeNumber("122222");
        nickNameRequest.setService("mobile");
        nickNameRequest.setOperator("jio");
        nickNameRequest.setPaytype("credit card");
        nickNameRequest.setNickName("nick");
        nickNameRequest.setPanUniqueReference("191019");
        return nickNameRequest;
    }

    private NickNameRequest prepareCreditCardDataWithReferenceId() {
        NickNameRequest nickNameRequest = new NickNameRequest();
        nickNameRequest.setCustomerId(7091L);
        nickNameRequest.setRechargeNumber("122222");
        nickNameRequest.setService("mobile");
        nickNameRequest.setOperator("jio");
        nickNameRequest.setPaytype("credit card");
        nickNameRequest.setNickName("nick");
        nickNameRequest.setReferenceId("191019");
        return nickNameRequest;
    }

    @Test
    public void testNullData() throws NickNameServiceException {
        NickNameResponse nickNameResponse = recentsService.updateNickName(null);
        assertEquals((Integer) 400, nickNameResponse.getStatusCode());
    }

    @Test
    public void testWithNonCreditCard() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);
        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);
        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());

        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("electricity", arg.getValue().getService());
        assertEquals("1222222", arg.getValue().getRechargeNumber());
        assertEquals("up bigli nigam", arg.getValue().getOperator());
    }

    @Test
    public void testNickNameInavlidLength() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        nickNameRequest.setNickName("dsfdfvefv efdfadfedfadfasdfsdffwfwfadfa");
        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);
        assertEquals(new Integer(400),nickNameResponse.getStatusCode());
    }

    @Test
    public void testNickNameIfRecordNotExists() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        nickNameRequest.setNickName("Test Name");
        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);
        assertEquals(new Integer(400),nickNameResponse.getStatusCode());
    }

    @Test
    public void testWithCreditCardPanUnique() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareCreditCardDataWithPanUnique();

        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);
        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);
        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());

        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("mobile", arg.getValue().getService());
        assertEquals("122222", arg.getValue().getRechargeNumber());
    }
    @Test
    public void testWithCreditCardRefereceId() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareCreditCardDataWithPanUnique();
        Recents recents = new Recents();

        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);

        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);

        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());
        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("mobile", arg.getValue().getService());
        assertEquals("122222", arg.getValue().getRechargeNumber());
    }

    @Test
    public void testNonCCWithSpaceInNickname() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        nickNameRequest.setNickName("abcd ab");
        Recents recents = new Recents();

        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);

        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);

        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());
        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("electricity", arg.getValue().getService());
        assertEquals("1222222", arg.getValue().getRechargeNumber());
    }

    @Test
    public void testNonCCWithHyphenInNickname() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        nickNameRequest.setNickName("abcd-ab");
        Recents recents = new Recents();

        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);

        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);

        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());
        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("electricity", arg.getValue().getService());
        assertEquals("1222222", arg.getValue().getRechargeNumber());
    }

    @Test
    public void testNonCCWithNumbersInNickname() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        nickNameRequest.setNickName("abcd123");
        Recents recents = new Recents();

        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);

        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);

        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());
        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("electricity", arg.getValue().getService());
        assertEquals("1222222", arg.getValue().getRechargeNumber());
    }

    @Test
    public void testNonCCWithSpaceAndNumbersInNickname() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        nickNameRequest.setNickName("abcd 123");
        Recents recents = new Recents();

        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);

        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);

        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());
        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("electricity", arg.getValue().getService());
        assertEquals("1222222", arg.getValue().getRechargeNumber());
    }

    @Test
    public void testNonCCWithHyphenAndNumbersInNickname() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        nickNameRequest.setNickName("abcd-123");
        Recents recents = new Recents();

        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);

        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);

        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());
        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("electricity", arg.getValue().getService());
        assertEquals("1222222", arg.getValue().getRechargeNumber());
    }

    @Test
    public void testNonCCWithSpaceAndNumbersAndHyphenInNickname() throws NickNameServiceException {
        NickNameRequest nickNameRequest = prepareNonCreditCardData();
        nickNameRequest.setNickName("abcd -123");
        Recents recents = new Recents();

        ArgumentCaptor<NickNameRequest> arg = ArgumentCaptor.forClass(NickNameRequest.class);

        when(recentService.fetchAndUpdateNickNameInRecents(any())).thenReturn(true);

        NickNameResponse nickNameResponse = recentsService.updateNickName(nickNameRequest);

        verify(recentService).fetchAndUpdateNickNameInRecents(arg.capture());
        assertEquals((Integer) 200, nickNameResponse.getStatusCode());
        assertEquals("[NickNameService.updateNickName] nickname updated", nickNameResponse.getDisplayMessage());
        assertEquals((Long) 7091L, arg.getValue().getCustomerId());
        assertEquals("electricity", arg.getValue().getService());
        assertEquals("1222222", arg.getValue().getRechargeNumber());
    }
}