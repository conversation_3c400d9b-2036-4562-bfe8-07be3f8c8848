package com.paytm.saga.common.configuration;
import com.paytm.saga.common.constant.Constants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import static org.mockito.Mockito.spy;

public class KafkaConsumerEnableConditionTest {
    @Mock
    private ConditionContext conditionContext;
    @Mock
    private AnnotatedTypeMetadata annotatedTypeMetadata;
    private KafkaConsumerEnableCondition kafkaConsumerEnableCondition;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        kafkaConsumerEnableCondition = spy(new KafkaConsumerEnableCondition());
    }
    @Test
    public void testMatchesWhenEnvVarIsTrue() {
        Mockito.doReturn("true").when(kafkaConsumerEnableCondition).getEnvironmentVariable(Constants.EnvVariableConstants.OMS_CONSUMER_ENABLE);
        Assert.assertTrue(kafkaConsumerEnableCondition.matches(conditionContext, annotatedTypeMetadata));
    }
    @Test
    public void testMatchesWhenEnvVarIsFalse() {
        Mockito.doReturn("false").when(kafkaConsumerEnableCondition).getEnvironmentVariable(Constants.EnvVariableConstants.OMS_CONSUMER_ENABLE);
        Assert.assertFalse(kafkaConsumerEnableCondition.matches(conditionContext, annotatedTypeMetadata));
    }
    @Test
    public void testMatchesWhenEnvVarIsNotSet() {
        Mockito.doReturn(null).when(kafkaConsumerEnableCondition).getEnvironmentVariable(Constants.EnvVariableConstants.OMS_CONSUMER_ENABLE);
        Assert.assertFalse(kafkaConsumerEnableCondition.matches(conditionContext, annotatedTypeMetadata));
    }
}