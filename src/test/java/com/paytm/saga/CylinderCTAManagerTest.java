package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.DropOffBuilder;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.service.aggregator.viewmanager.impl.CylinderCTAManager;
import com.paytm.saga.util.DateUtil;

public class CylinderCTAManagerTest {

	private final Logger logger = LogManager.getLogger(CylinderCTAManagerTest.class);

	@InjectMocks
	private CylinderCTAManager cylinderCTAManager;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void successTrackDeliveryThemeType() {
		try {
			HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge")
					.setEventType("RECHARGE").setService("cylinder booking").setStatus("SUCCESS")
					.setEventDate(DateUtil.dateIncrDecr(DateUtil.timestampToDate(new Date()), -2)).build();
			historyView.setThemeType(this.getRechargeThemeType(historyView));

			List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);
			assertEquals("Track Delivery", ctas.get(0).getValue());
			assertEquals("track_delivery", ctas.get(0).getActionType());

		} catch (ParseException e) {
			logger.error("ParseException: "+ e.getMessage());
		}

	}

	@Test
	public void successBookAgainThemeType() {
		try {
			HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge")
					.setEventType("RECHARGE").setService("cylinder booking").setStatus("SUCCESS")
					.setEventDate(DateUtil.dateIncrDecr(DateUtil.timestampToDate(new Date()), -3)).build();
			historyView.setThemeType(this.getRechargeThemeType(historyView));

			List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);

			assertEquals("Book Again", ctas.get(0).getValue());
			assertEquals("repeat_recharge", ctas.get(0).getActionType());

		} catch (ParseException e) {
			logger.error("ParseException: " + e.getMessage());
		}

	}

	@Test
	public void cancelThemeType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setEventType("RECHARGE")
				.setService("cylinder booking").setStatus("CANCEL").build();
		historyView.setThemeType(this.getRechargeThemeType(historyView));
		List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);
		assertEquals("Retry", ctas.get(0).getValue());
		assertEquals("retry_recharge", ctas.get(0).getActionType());
	}

	@Test
	public void dropOffThemeType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge")
				.setEventType("VALIDATION").setService("cylinder booking").setStatus("VALIDATION").build();
		DropOff dropOff = new DropOffBuilder().setAmount("1").setPaytype("recharge").setEventType("VALIDATION")
				.setService("cylinder booking").setStatus("VALIDATION").build();
		historyView.setThemeType(this.getDropOffThemeType(dropOff));
		List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);
		assertEquals("Continue Payment", ctas.get(0).getValue());
		assertEquals("retry_recharge", ctas.get(0).getActionType());
	}

	@Test
	public void reversalThemeType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setEventType("RECHARGE")
				.setService("cylinder booking").setStatus("REVERSAL_FAILURE").build();
		historyView.setThemeType(this.getRechargeThemeType(historyView));
		List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);
		assertEquals("Retry", ctas.get(0).getValue());
		assertEquals("retry_recharge", ctas.get(0).getActionType());
	}

	@Test
	public void paymentFailureThemeType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setEventType("RECHARGE")
				.setService("cylinder booking").setStatus("PAYMENT_FAILURE").build();
		historyView.setThemeType(this.getRechargeThemeType(historyView));
		List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);
		assertEquals("Retry", ctas.get(0).getValue());
		assertEquals("retry_recharge", ctas.get(0).getActionType());
	}

	@Test
	public void failureThemeType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setEventType("RECHARGE")
				.setService("cylinder booking").setStatus("FAILURE").build();
		historyView.setThemeType(this.getRechargeThemeType(historyView));
		List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);
		assertEquals("Retry", ctas.get(0).getValue());
		assertEquals("retry_recharge", ctas.get(0).getActionType());
	}

	@Test
	public void paymentPendingThemeType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setEventType("RECHARGE")
				.setService("cylinder booking").setStatus("PAYMENT_PENDING").build();
		historyView.setThemeType(this.getRechargeThemeType(historyView));
		List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);
		assertNull(ctas);
	}

	@Test
	public void pendingThemeType() {
		HistoryView historyView = new HistoryViewBuilder().setAmount(1).setPayType("recharge").setEventType("RECHARGE")
				.setService("cylinder booking").setStatus("PENDING").build();
		historyView.setThemeType(this.getRechargeThemeType(historyView));
		List<ViewElementInfo> ctas = cylinderCTAManager.getCTAs(historyView);
		assertNull(ctas);
	}

	private String getRechargeThemeType(HistoryView channelHistory) {
		String cardInfo = (channelHistory.getPayType() + "_" + channelHistory.getService() + "_"
				+ channelHistory.getEventType() + "_" + channelHistory.getStatus()).toLowerCase();
		String response = "";
		switch (cardInfo) {
		case "prepaid_mobile_recharge_success":
			response = CardThemeTypes.SUCCESS_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_failure":
			response = CardThemeTypes.FAILURE_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_reversal_failure":
			response = CardThemeTypes.FAILURE_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_payment_failure":
			response = CardThemeTypes.FAILURE_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_pending":
			response = CardThemeTypes.PENDING_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_payment_pending":
			response = CardThemeTypes.PENDING_RECHARGE_CARD_THEME;
			break;
		case "prepaid_mobile_recharge_cancel":
			response = CardThemeTypes.CANCELLED_RECHARGE_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_success":
			response = CardThemeTypes.SUCCESS_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_failure":
			response = CardThemeTypes.FAILURE_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_reversal_failure":
			response = CardThemeTypes.REVERSAL_FAILURE_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_payment_failure":
			response = CardThemeTypes.PAYMENT_FAILURE_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_pending":
			response = CardThemeTypes.PENDING_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_payment_pending":
			response = CardThemeTypes.PAYMENT_PENDING_CYLINDER_BOOKING_CARD_THEME;
			break;
		case "recharge_cylinder booking_recharge_cancel":
			response = CardThemeTypes.CANCELLED_CYLINDER_BOOKING_CARD_THEME;
			break;
		}
		return response;
	}

	private String getDropOffThemeType(DropOff dropOff) {
		String cardInfo = (dropOff.getPaytype() + "_" + dropOff.getService() + "_" + dropOff.getEventType())
				.toLowerCase();
		String response = "";
		switch (cardInfo) {
		case "recharge_cylinder booking_validation":
			response = CardThemeTypes.DROPOFF_CARD_THEME;
			break;
		}
		return response;
	}
}
