package com.paytm.saga;

import static org.junit.Assert.assertEquals;

import java.util.Date;
import java.util.HashMap;

import com.paytm.saga.dto.HistoryView;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.builder.DropOffBuilder;
import com.paytm.saga.service.aggregator.cardmanager.DropOffCard;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;

public class DropOffCardTest {
	@Mock
	private ViewManager ctaManager;
	@Mock
	private ViewManager headersViewManager;
	@InjectMocks
	private DropOffCard dropOffCard;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void getMobileDropOffCard() {
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setDropOff(new DropOffBuilder().setAmount("100").setCategoryId(1L).setService("mobile")
				.setOperator("jio").setPaytype("prepaid").setEventType("VALIDATION").setRechargeNumber("9812316169")
				.setTransactionTime(new Date()).setCustomerId(302314234L).setCircle("haryana").build());
		cardInfoDto.setClient("androidapp");
		cardInfoDto.setVersion("9.6.0");
		assertEquals(CardThemeTypes.DROPOFF_CARD_THEME, dropOffCard.getCardInfo(cardInfoDto).getThemeType());
	}

	@Test
	public void getCylinderDropOffCard() {
		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setDropOff(new DropOffBuilder().setAmount("100").setCategoryId(1L).setService("cylinder booking")
				.setOperator("indane").setPaytype("recharge").setEventType("VALIDATION").setRechargeNumber("9812316169")
				.setTransactionTime(new Date()).setCustomerId(302314234L).setCircle("pan india").build());
		cardInfoDto.setClient("androidapp");
		cardInfoDto.setVersion("9.6.0");
		assertEquals(CardThemeTypes.DROPOFF_CARD_THEME, dropOffCard.getCardInfo(cardInfoDto).getThemeType());
	}

	@Test
	public void getElectricityDropOffCardWithBillsObj() {
		CardInfoDto cardInfoDto = new CardInfoDto();
		HashMap<String,String> billsObj = new HashMap<>();
		billsObj.put("expiry", "2022-04-03 12:22:10");
		System.out.println(billsObj.get("expiry"));
		cardInfoDto.setDropOff(new DropOffBuilder().setAmount("100").setCategoryId(1L).setService("electricity")
				.setOperator("bses").setPaytype("recharge").setEventType("VALIDATION").setRechargeNumber("9812316169")
				.setTransactionTime(new Date()).setCustomerId(302314234L).setCircle("pan india").setBillsObj(billsObj).build());
		cardInfoDto.setClient("androidapp");
		cardInfoDto.setVersion("9.6.0");
		assertEquals(true, dropOffCard.getCardInfo(cardInfoDto).getThemeType().isEmpty());
		assertEquals("2022-04-03 12:22:10", dropOffCard.getCardInfo(cardInfoDto).getPlanExpiryDate());
	}

	@Test
	public void getElectricityDropOffCardWithBillsObjAndDropOffDate() {
		CardInfoDto cardInfoDto = new CardInfoDto();
		HashMap<String,String> billsObj = new HashMap<>();
		billsObj.put("expiry", "2022-04-03 12:22:10");
		System.out.println(billsObj.get("expiry"));
		cardInfoDto.setDropOff(new DropOffBuilder().setAmount("100").setCategoryId(1L).setService("electricity")
				.setOperator("bses").setPaytype("recharge").setEventType("VALIDATION").setRechargeNumber("9812316169")
				.setTransactionTime(new Date()).setCustomerId(302314234L).setCircle("pan india").setBillsObj(billsObj).build());
		cardInfoDto.setClient("androidapp");
		cardInfoDto.setVersion("9.6.0");
		assertEquals(true, dropOffCard.getCardInfo(cardInfoDto).getThemeType().isEmpty());
		assertEquals("2022-04-03 12:22:10", dropOffCard.getCardInfo(cardInfoDto).getPlanExpiryDate());
	}

}
