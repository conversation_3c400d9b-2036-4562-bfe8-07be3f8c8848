package com.paytm.saga.builders;

import com.paytm.saga.dto.FetchRecentsResponseObject;
import com.paytm.saga.dto.builder.RecentsResponseBuilder;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class RecentsResponseBuilderTest {

    private Recents mockCCRecents() {
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("financial services");
        recentsPrimaryKey.setRechargeNumber("1221334");
        recentsPrimaryKey.setOperator("hdcf");
        recentsPrimaryKey.setPlanBucket("");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("credit card");
        recents.setMcn("XXXX XXXX XXXX 1234");
        recents.setTxnAmount(100.0);
        recents.setProductId(112L);
        recents.setUpdatedAt(new Date());
        recents.setCin("123645fdfs");
        recents.setPar("54541564dsfds");

        return recents;
    }

    private Recents mockNonCCRecents() {
        Recents recents = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setCustomerId(1123L);
        recentsPrimaryKey.setService("mobile");
        recentsPrimaryKey.setRechargeNumber("9812316169");
        recentsPrimaryKey.setOperator("airtel");
        recentsPrimaryKey.setPlanBucket("Special Recharge");
        recents.setKey(recentsPrimaryKey);
        recents.setPayType("prepaid");
        recents.setTxnAmount(100.0);
        recents.setProductId(112L);
        recents.setUpdatedAt(new Date());

        return recents;
    }

    @Test
    public void testrecentToRecentsResponseObjectWithNullRecents() {
        FetchRecentsResponseObject fetchRecentsResponseObject = new FetchRecentsResponseObject();
        Recents recents = null;
        FetchRecentsResponseObject fetchRecentsResponseObjectFromBuilder = RecentsResponseBuilder.recentToRecentsResponseObject.apply(recents);
        assertEquals(fetchRecentsResponseObject, fetchRecentsResponseObjectFromBuilder);
    }

    @Test
    public void testrecentToRecentsResponseObjectWithNonNullNonCCRecents() {
        FetchRecentsResponseObject fetchRecentsResponseObject = new FetchRecentsResponseObject();

        Recents recent = mockNonCCRecents();
        fetchRecentsResponseObject.setCustomerId(recent.getKey().getCustomerId());
        fetchRecentsResponseObject.setRechargeNumber(recent.getKey().getRechargeNumber());
        fetchRecentsResponseObject.setOperator(recent.getKey().getOperator());
        fetchRecentsResponseObject.setService(recent.getKey().getService());
        fetchRecentsResponseObject.setProductId(recent.getProductId());
        fetchRecentsResponseObject.setUpdatedAt(recent.getUpdatedAt());
        FetchRecentsResponseObject fetchRecentsResponseObjectFromBuilder = RecentsResponseBuilder.recentToRecentsResponseObject.apply(recent);
        assertEquals(fetchRecentsResponseObject, fetchRecentsResponseObjectFromBuilder);
    }

    @Test
    public void testrecentToRecentsResponseObjectWithNonNullCCRecents() {
        FetchRecentsResponseObject fetchRecentsResponseObject = new FetchRecentsResponseObject();

        Recents recent = mockCCRecents();
        fetchRecentsResponseObject.setCustomerId(recent.getKey().getCustomerId());
        fetchRecentsResponseObject.setOperator(recent.getKey().getOperator());
        fetchRecentsResponseObject.setService(recent.getKey().getService());
        fetchRecentsResponseObject.setProductId(recent.getProductId());
        fetchRecentsResponseObject.setUpdatedAt(recent.getUpdatedAt());
        fetchRecentsResponseObject.setRechargeNumber(recent.getMcn());
        fetchRecentsResponseObject.setCin(recent.getCin());
        fetchRecentsResponseObject.setPar(recent.getPar());
        FetchRecentsResponseObject fetchRecentsResponseObjectFromBuilder = RecentsResponseBuilder.recentToRecentsResponseObject.apply(recent);
        assertEquals(fetchRecentsResponseObject, fetchRecentsResponseObjectFromBuilder);
    }
}
