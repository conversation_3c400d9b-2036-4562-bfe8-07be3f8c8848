package com.paytm.saga.producers;

import com.paytm.saga.common.configuration.property.RemovePgTokenKafkaProducerConfig;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class RemovePgTokenKafkaProducerTest {

    @Mock
    private RemovePgTokenKafkaProducerConfig removePgTokenKafkaProducerConfig;

    @Mock
    private KafkaTemplate<String, String> removePgTokenKafkaTemplate;

    @InjectMocks
    private RemovePgTokenKafkaProducer kafkaProducer;

    @Captor
    private ArgumentCaptor<String> topicCaptor;

    @Captor
    private ArgumentCaptor<String> messageCaptor;

    @Test
    public void testSendMessageSuccessfully() {
        String topicName = "testTopic";
        String message = "Test Message";
        when(removePgTokenKafkaProducerConfig.getTopicName()).thenReturn(topicName);
        CompletableFuture<SendResult<String, String>> future = CompletableFuture.completedFuture(new SendResult<>(null, null));
        when(removePgTokenKafkaTemplate.send(topicName, message)).thenReturn(future);
        kafkaProducer.send(message);
        verify(removePgTokenKafkaTemplate).send(topicCaptor.capture(), messageCaptor.capture());
        assertEquals(topicName, topicCaptor.getValue());
        assertEquals(message, messageCaptor.getValue());
    }

    @Test
    public void testSendMessageFailure() {
        String errorMessage = "Simulated error";
        String topicName = "testTopic";
        String message = "Test Message";
        when(removePgTokenKafkaProducerConfig.getTopicName()).thenReturn("testTopic");
        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        future.completeExceptionally(new RuntimeException(errorMessage));
        when(removePgTokenKafkaTemplate.send(topicName, message)).thenReturn(future);
        kafkaProducer.send(message);
        verify(removePgTokenKafkaTemplate).send(topicCaptor.capture(), messageCaptor.capture());
        assertEquals(message, messageCaptor.getValue());
    }

}