package com.paytm.saga.controller;

import com.paytm.saga.dto.RemovePgTokenKafkaRequest;
import com.paytm.saga.dto.RemovePgTokenResponse;
import com.paytm.saga.service.RemovePgTokenService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PGControllerTest {

    @Mock
    RemovePgTokenService removePgTokenService;

    @InjectMocks
    private PgCardController pgCardController;


    @Test
    public void testRemovePgToken() {
        RemovePgTokenKafkaRequest requestPayload = new RemovePgTokenKafkaRequest();
        RemovePgTokenResponse serviceResponse = new RemovePgTokenResponse();
        serviceResponse.setMessage("Success");
        when(removePgTokenService.pushTopicToKafka(any())).thenReturn(serviceResponse);
        ResponseEntity<Object> response=pgCardController.removePgToken(requestPayload,new HashMap<>());
        Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assert.assertEquals(serviceResponse, response.getBody());
        verify(removePgTokenService, times(1)).pushTopicToKafka(any(RemovePgTokenKafkaRequest.class));
    }

}
