package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.paytm.saga.common.exception.RechargeSagaBaseException;
import com.paytm.saga.controller.RechargeSagaController;
import com.paytm.saga.dto.ReminderStatusRequest;
import com.paytm.saga.dto.ReminderStatusResponse;
import com.paytm.saga.service.MarkAsPaidService;
import com.paytm.saga.util.JsonUtils;

public class RechargeSagaControllerUnitTest {
	@Mock
	public MarkAsPaidService markAsPaidService;

	@InjectMocks
	public RechargeSagaController rechargeSagaController;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	private ReminderStatusRequest mockMarkAsPaidRequest() {
		String data = "{\"rechargeNumber\": \"111111\",\"operator\" : \"airtel\",\"customerId\" : 121222,\"referenceId\" : \"XXXX\", \"paytype\" : \"prepaid\",\"circle\" : \"Andra Pradesh\",\"service\" : \"mobile\",\"productId\" : 1222323234,\"amount\" : 10}";
		return JsonUtils.parseJson(data, ReminderStatusRequest.class);
	}

	@Test
	public void updateDropOffReminderStatusTest() throws RechargeSagaBaseException {
		ReminderStatusResponse markAsPaidResponse = new ReminderStatusResponse(HttpStatus.OK.value(), "Success", null,
				null, HttpStatus.OK);
		when(markAsPaidService.createDropOffForDoNotRemindme(Mockito.any(ReminderStatusRequest.class)))
				.thenReturn(markAsPaidResponse);
		ResponseEntity<ReminderStatusResponse> response = rechargeSagaController
				.updateDropOffReminderStatus(mockMarkAsPaidRequest());
		assertEquals("Success", response.getBody().getDisplayMessage());
		assertEquals(HttpStatus.OK, response.getBody().getHttpStatus());
	}
}
