package com.paytm.saga.dao;

import com.datastax.oss.driver.api.core.ConsistencyLevel;
import com.datastax.oss.driver.api.core.cql.SimpleStatement;
import com.paytm.saga.dao.impl.RecentDao;
import com.paytm.saga.service.impl.ServiceConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.cassandra.core.CassandraOperations;
import org.springframework.data.cassandra.core.query.Query;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RecentDaoTest {

    @Mock
    private CassandraOperations template;

    @Mock
    private ServiceConfig serviceConfig;

    @InjectMocks
    private RecentDao recentDao;

    @Test
    public void testFindByParams() throws Exception {

        when(serviceConfig.getRecentReadTimeout()).thenReturn(1000L);
        when(serviceConfig.getAgentLimit()).thenReturn(100);

        Long customerId = 1111L;
        List<String> services = Arrays.asList("water");
        String rechargeNumber = "22222";
        String operator = "djb";



        recentDao.findByParams(customerId,  services, rechargeNumber, operator, serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout());

        ArgumentCaptor<Query> queryArgument = ArgumentCaptor.forClass(Query.class);
        ArgumentCaptor<Class> objectArgument = ArgumentCaptor.forClass(Class.class);

        verify(template).select(queryArgument.capture(), objectArgument.capture());

        Query query = queryArgument.getValue();
        assertTrue(query.toString().contains("22222"));
        assertTrue(query.toString().contains("djb"));
        assertTrue(query.toString().contains("water"));

    }

    @Test
    public void testFindByParamsIfFinancialService() throws Exception {

        when(serviceConfig.getRecentReadTimeout()).thenReturn(1000L);
        when(serviceConfig.getAgentLimit()).thenReturn(100);

        Long customerId = 1111L;
        List<String> services = Arrays.asList("financial services");
        String rechargeNumber = "22222";
        String operator = "hdfc";

        recentDao.findByParams(customerId,  services, rechargeNumber, operator,serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout());

        ArgumentCaptor<Query> queryArgument = ArgumentCaptor.forClass(Query.class);
        ArgumentCaptor<Class> objectArgument = ArgumentCaptor.forClass(Class.class);

        verify(template).select(queryArgument.capture(), objectArgument.capture());

        Query query = queryArgument.getValue();
        assertTrue(!query.toString().contains("22222"));
        assertTrue(query.toString().contains("financial services"));
        assertTrue(!query.toString().contains("hdfc"));

    }

    @Test
    public void testFindByParamsMultipleServices() throws Exception {

        when(serviceConfig.getRecentReadTimeout()).thenReturn(1000L);
        when(serviceConfig.getAgentLimit()).thenReturn(100);

        Long customerId = 1111L;
        List<String> services = Arrays.asList("financial services","water");
        String rechargeNumber = "22222";
        String operator = "hdfc";

        recentDao.findByParams(customerId,  services, rechargeNumber, operator, serviceConfig.getAgentLimit() + 1, serviceConfig.getRecentReadTimeout());

        ArgumentCaptor<Query> queryArgument = ArgumentCaptor.forClass(Query.class);
        ArgumentCaptor<Class> classArgument = ArgumentCaptor.forClass(Class.class);

        verify(template).select(queryArgument.capture(), classArgument.capture());

        Query query = queryArgument.getValue();
        assertTrue(!query.toString().contains("22222"));
        assertTrue(query.toString().contains("financial services"));
        assertTrue(query.toString().contains("water"));
        assertTrue(!query.toString().contains("hdfc"));

    }

    @Test
    public void testFindByQuery() throws Exception {
        recentDao.findByQuery("");
        ArgumentCaptor<SimpleStatement> statementArgument = ArgumentCaptor.forClass(SimpleStatement.class);
        ArgumentCaptor<Class> classArgument = ArgumentCaptor.forClass(Class.class);

        verify(template).select(statementArgument.capture(), classArgument.capture());
        SimpleStatement simpleStatement = statementArgument.getValue();
        assertEquals(ConsistencyLevel.LOCAL_ONE, simpleStatement.getConsistencyLevel());


    }

}
