package com.paytm.saga;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.exception.PgServiceException;
import com.paytm.saga.common.exception.SavedCardServiceException;
import com.paytm.saga.dto.*;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.CommonCache;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.*;
import com.paytm.saga.service.external.PGService;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.JsonUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.MatcherAssert.assertThat;

public class SavedCardServiceTest {
    @Mock
    private PGService pgService;
    @Mock
    private DCATService dcatService;
    @Mock
    private CommonCacheService commonCacheService;
    @InjectMocks
    private SavedCardService savedCardService;
    @Mock
    ReminderHistoryRepositoryWrapperService reminderHistoryRepository;
    @Mock
    private ChannelHistoryRepositoryWrapperService channelHistoryRepository;
    @Mock
    private ReminderHistoryService reminderHistoryService;

    private static String Cin1 = "2018090311830414d671b94de77d6ed191ae850888bcd";
    private static String Par1 = "V0010013021295362620166880000";


    Date date1 = new Date(), date2 = new Date(), date3 =new Date(), date4 = new Date(), date5 = new Date(), date_now_minus_10_days = new Date(), date_now_minus_40_days = new Date();

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }



    private List<SavedCardDetails> mockSavedCardV2DetailsOnlyPlatform() {
        List<SavedCardDetails> list = new ArrayList<>();
        date1 = DateUtils.addHours(new Date(),-24);
        date2 = DateUtils.addHours(new Date(),-48);
        date3 = DateUtils.addHours(new Date(),-72);
        date4 = DateUtils.addHours(new Date(),-96);

        SavedCardDetails savedCardDetails = new SavedCardDetails();
        savedCardDetails.setSavedCardId(Cin1);
        savedCardDetails.setCardScheme("VISA");
        savedCardDetails.setExpiryDate("102022");
        savedCardDetails.setIssuingBankName("CITI");
        savedCardDetails.setIssuingBankCardVariant("Regalia");
        savedCardDetails.setCardSuffix("0456");
        savedCardDetails.setCardType("CC");
        savedCardDetails.setDisplayName("CITI");
        savedCardDetails.setCardCoft(false);
        savedCardDetails.setCardFirstSixDigits("452055");
        savedCardDetails.setIssuerCode("CITI");
        savedCardDetails.setEligibleForCoft(true);

        list.add(savedCardDetails);
        return list;
    }

    private List<SavedCardDetails> mockSavedCardV2DetailsForPaytmFirst() {
        List<SavedCardDetails> list = new ArrayList<>();
        date1 = DateUtils.addHours(new Date(),-24);
        date2 = DateUtils.addHours(new Date(),-48);
        date3 = DateUtils.addHours(new Date(),-72);
        date4 = DateUtils.addHours(new Date(),-96);

        SavedCardDetails savedCardDetails = new SavedCardDetails();
        savedCardDetails.setSavedCardId(Cin1);
        savedCardDetails.setCardScheme("VISA");
        savedCardDetails.setExpiryDate("102022");
        savedCardDetails.setIssuingBankName("CITI");
        savedCardDetails.setIssuingBankCardVariant("Regalia");
        savedCardDetails.setCardSuffix("0456");
        savedCardDetails.setCardType("CC");
        savedCardDetails.setDisplayName("CITI Paytm");
        savedCardDetails.setCardCoft(false);
        savedCardDetails.setCardFirstSixDigits("452055");
        savedCardDetails.setIssuerCode("CITI");
        savedCardDetails.setEligibleForCoft(true);
        list.add(savedCardDetails);
        return list;
    }

    private String isPaytmFirstCardTest(String displayName) {
        return (displayName !=null && displayName.toLowerCase().contains(Constants.CommonConstants.PAYTM_FIRST_CARD_DISPLAY_NAME))
                ? "1"
                : "0";
    }

    private List<SavedCardDetails> mockSavedCardV2DetailsOnlyCoft() {
        List<SavedCardDetails> list = new ArrayList<>();
        date1 = DateUtils.addHours(new Date(),-24);
        date2 = DateUtils.addHours(new Date(),-48);
        date3 = DateUtils.addHours(new Date(),-72);
        date4 = DateUtils.addHours(new Date(),-96);

        SavedCardDetails savedCardDetails = new SavedCardDetails();
        savedCardDetails.setSavedCardId("618b6273d83f4545dc117294");
        savedCardDetails.setCardScheme("VISA");
        savedCardDetails.setExpiryDate("082022");
        savedCardDetails.setIssuingBankName("CITI");
        savedCardDetails.setIssuerCode("CITI");
        savedCardDetails.setIssuingBankCardVariant("Regalia");
        savedCardDetails.setCardSuffix("3216");
        savedCardDetails.setCardType("CC");
        savedCardDetails.setDisplayName("CITI");
        savedCardDetails.setCardCoft(true);
        savedCardDetails.setPanUniqueReference(Par1);
        savedCardDetails.setTokenBin("*********");
        savedCardDetails.setTokenStatus("ACTIVE");

        list.add(savedCardDetails);
        return list;
    }



    private List<SavedCardDetails> mockSavedCardV2DetailsPlatformAndCoft() {
        List<SavedCardDetails> combinedList = Stream.of(mockSavedCardV2DetailsOnlyPlatform(), mockSavedCardV2DetailsOnlyCoft())
                .flatMap(x -> x.stream())
                .collect(Collectors.toList());
        return combinedList;
    }

    private List<SavedCardDetails> mockSavedCardDetailsForPaytmFirst() {
        List<SavedCardDetails> cardList = mockSavedCardV2DetailsOnlyPlatform();
        cardList.stream().forEach(s -> s.setDisplayName(Constants.CommonConstants.PAYTM_FIRST_CARD_DISPLAY_NAME));
        return cardList;
    }

    private ChannelHistory mockChannelHistoryDataForCinCard(){
        Date TRANSACTION_DATE = DateUtil.minutesIncrDecr(new Date(), -5);

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767")
                .setCategoryId(26L).setCircle(null).setCreatedTime(new Date()).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator("paschim gujarat vij company limited").setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype("postpaid").setProductId(110083094L).setRechargeNumber(Cin1)
                .setService("electricity").setStatus("7").setTransactionTime(TRANSACTION_DATE)
                .setTransactionUpdateTime(TRANSACTION_DATE).build();
        return channelHistory;
    }

    private ChannelHistory mockChannelHistoryDataForCoftCard(){
        Date TRANSACTION_DATE = DateUtil.minutesIncrDecr(new Date(), -5);

        ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("767")
                .setCategoryId(26L).setCircle(null).setCreatedTime(new Date()).setCustomerId(450471692L)
                .setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752764L)
                .setOperator("paschim gujarat vij company limited").setOrderId(12862601947L).setPaymentStatus(null)
                .setPaytype("postpaid").setProductId(110083094L).setRechargeNumber(Par1)
                .setService("electricity").setStatus("7").setTransactionTime(TRANSACTION_DATE)
                .setTransactionUpdateTime(TRANSACTION_DATE).build();
        return channelHistory;
    }

    private ProductInfo mockProductInfo() {
        ProductInfo productInfo = new ProductInfo();
        productInfo.setBankName("sbi");
        productInfo.setCardType("CC");
        productInfo.setCategoryId(2L);
        productInfo.setOperator("sbi_visa");
        productInfo.setProductId(1L);
        productInfo.setService("financial services");
        return productInfo;
    }

    private ReminderHistory mockReminderHistoryObjectForCinCard() {
        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setAmount(100.0);
        reminderHistory.setBill_date(new Date());
        reminderHistory.setCircle("Delhi NCR");
        reminderHistory.setCreated_at(new Date());
        reminderHistory.setCurrentMinBillAmount(50.0);
        reminderHistory.setCustomerId(10000060590L);
        reminderHistory.setDue_date(new Date());
        reminderHistory.setIs_automatic(0);
        reminderHistory.setIsPartial(0);
        reminderHistory.setOperator("Airtel");
        reminderHistory.setPaytype("credit card");
        reminderHistory.setProductId(100L);
        reminderHistory.setRechargeNumber("4054 50XX XXXX 2178");
        reminderHistory.setReference_id(Cin1);
        reminderHistory.setService("Financial Services");
        reminderHistory.setStatus(1);
        reminderHistory.setUpdatedAt(new Date());
        return reminderHistory;
    }

    private ReminderHistory mockReminderHistoryObjectForCoftCard() {
        ReminderHistory reminderHistory = new ReminderHistory();
        reminderHistory.setAmount(100.0);
        reminderHistory.setBill_date(new Date());
        reminderHistory.setCircle("Delhi NCR");
        reminderHistory.setCreated_at(new Date());
        reminderHistory.setCurrentMinBillAmount(50.0);
        reminderHistory.setCustomerId(10000060590L);
        reminderHistory.setDue_date(new Date());
        reminderHistory.setIs_automatic(0);
        reminderHistory.setIsPartial(0);
        reminderHistory.setOperator("Airtel");
        reminderHistory.setPaytype("credit card");
        reminderHistory.setProductId(100L);
        reminderHistory.setRechargeNumber("1234 50XX XXXX 2178");
        reminderHistory.setPar(Par1);
        reminderHistory.setService("Financial Services");
        reminderHistory.setStatus(1);
        reminderHistory.setUpdatedAt(new Date());
        return reminderHistory;
    }


    private DCATCategoryResponseModel mockCategoryAPIResponse() {
        //String respStr = "{\"bottomStripUtilities\":[],\"showFastForward\":0,\"showUpgrade\":0,\"groupingCollapsible\":0,\"metaDescription\":\"null\",\"quotes\":null,\"disclaimerFastForward\":null,\"protectionUrl\":{\"web\":null,\"html5\":null,\"defaultValue\":\"\",\"android\":null,\"ios\":null},\"showHelp\":0,\"skipDeals\":1,\"topLevelCategoryHeader\":{},\"productListInLastGrouping\":1,\"upperAmountThreshold\":null,\"categoryHeader\":\"Pay Your Credit Card Bill\",\"staticForm\":0,\"applicableCities\":[],\"displayName\":\"Credit-Card\",\"lowerAmountThreshold\":null,\"disclaimerHtml\":\"\",\"inputFields\":[{\"configKey\":\"credit_card_number\",\"regex\":\"\",\"showPhoneBook\":0,\"title\":\"Enter Credit Card Number\",\"type\":\"input\"}],\"fastForwardText\":\"Fast Forward\",\"alert\":null,\"buttonObject\":{\"prefetch\":[{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"},{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"}]},\"recentsPrefill\":0,\"disclaimer\":\"Currently we support payment for all Visa credit cards and credit cards issued by IndusInd & Kotak Mahindra Bank.\",\"extraDescription\":null,\"canonicalUrl\":null,\"dealsFastForward\":0,\"extnAttrs\":{\"prefillFirstRecent\":0,\"hideEnabled\":0,\"recentTempType\":\"\"},\"message\":null,\"verticalId\":56,\"metaTitle\":\"null\",\"attributes\":[{\"attrId\":9931,\"name\":\"card_network\",\"description\":null,\"type\":{\"app\":null,\"web\":null,\"html5\":null,\"defaultValue\":\"GRID\",\"android\":null,\"bcandroidapp\":null,\"ios\":null,\"windows\":null},\"displayName\":\"Card Network Type\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true},{\"attrId\":9934,\"name\":\"operator_label\",\"description\":null,\"type\":{\"app\":\"GRID\",\"web\":\"DROPDOWN\",\"html5\":\"GRID\",\"defaultValue\":\"DROPDOWN\",\"android\":\"GRID\",\"bcandroidapp\":null,\"ios\":\"GRID\",\"windows\":null},\"displayName\":\"Select your Bank\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true}],\"gaKey\":\"\",\"metaKeyword\":\"null\",\"categoryId\":131655,\"groupings\":{\"aggKey\":\"operator_label\",\"aggs\":[{\"value\":\"Paytm First\",\"displayValue\":\"Paytm First\",\"bank_code\":\"CITI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Visa\",\"displayValue\":\"Visa\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"productList\":[{\"price\":4,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"Paytm First\",\"enable_visa_direct\":\"1\",\"displayName\":\"Bill Payment of Paytm First Card\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"CITI\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"paytmfirstCC\",\"schedulable\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"Visa\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\",\"min\":\"1\",\"max\":\"999999\"}],\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"login_mandate\":\"1\",\"insurer_location\":\"N/A\",\"credit_card_length\":\"16\",\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/26.png\",\"lender\":\"Citibank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/14.png\",\"is_paytm_first_card\":\"1\"}]}],\"is_paytm_first_card\":\"1\"},{\"value\":\"Citi\",\"displayValue\":\"Citi\",\"bank_code\":\"CITI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Visa\",\"displayValue\":\"Visa\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"productList\":[{\"price\":4,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"Paytm First\",\"enable_visa_direct\":\"1\",\"displayName\":\"Bill Payment of Paytm First Card\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"CITI\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"paytmfirstCC\",\"schedulable\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"Visa\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\",\"min\":\"1\",\"max\":\"999999\"}],\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"login_mandate\":\"1\",\"insurer_location\":\"N/A\",\"credit_card_length\":\"16\",\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/26.png\",\"lender\":\"Citibank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/14.png\",\"is_paytm_first_card\":\"1\"}]}]},{\"value\":\"RBL Bank\",\"displayValue\":\"RBL Bank\",\"bank_code\":\"RATN\",\"schedulable\":\"0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Master\",\"displayValue\":\"Master\",\"schedulable\":\"0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"productList\":[{\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/Pa/Paytm_1963/1601369121742_10.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"RBL Bank\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill payment of  RBL Bank credit card\",\"catalogProductId\":**********,\"disclaimer\":\"Your bank may take upto 3 working days in reflecting this payment to your credit card account.\",\"bank_code\":\"RATN\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"Mastercard_RBLBank\",\"schedulable\":\"0\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"Master\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PREBILL-PAYMENTMAST60269155A9D137/0x1920/70/0.PNG\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"payment_mode\":\"NB/UPI / DC\"}]}]},{\"value\":\"SBI\",\"displayValue\":\"SBI\",\"bank_code\":\"SBI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Master\",\"displayValue\":\"Master\",\"schedulable\":\"0\",\"productList\":[{\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/30.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill payment of SBI credit card\",\"catalogProductId\":**********,\"disclaimer\":\"Your bank may take upto 3 working days in reflecting this payment to your credit card account.\",\"bank_code\":\"SBI\",\"payment_mode\":\"NB/UPI / DC\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"Mastercard_SBI\",\"schedulable\":\"0\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"Master\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PREBILL-PAYMENTMAST6026919DA41871/0.PNG\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"VISA\",\"displayValue\":\"VISA\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"clientVisibility\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"1\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"},{\"login_mandate\":\"1\",\"price\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(.[0-9]{0,2})?))(([1-9][0-9]{0,5})(.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PRESBI-BANKMAST602691A168507D/1..png\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"},{\"login_mandate\":\"1\",\"price\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"HDFC Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(.[0-9]{0,2})?))(([1-9][0-9]{0,5})(.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]}]},{\"value\":\"SBI Bank\",\"displayValue\":\"SBI Bank\",\"bank_code\":\"SBI\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"MAESTRO\",\"displayValue\":\"MAESTRO\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/dd/maestro/maestro.jpg\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/Pa/Paytm_1963/1601369121742_10.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/dd/maestro/maestro.jpg\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"MAESTRO\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"MASTER\",\"displayValue\":\"MASTER\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/https://assetscdn.paytm.com/images/catalog/product/P/PR/PREINDUSIND-BANDUMM2489463421637C/1.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/30.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"https://assetscdn.paytm.com/images/catalog/product/P/PR/PREINDUSIND-BANDUMM2489463421637C/1.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"MASTER\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"RUPAY\",\"displayValue\":\"RUPAY\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/rupa/RUPAY.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/31.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/rupa/RUPAY.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"RUPAY\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"VISA\",\"displayValue\":\"VISA\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]}]}]},\"relatedCategories\":[],\"logoUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/M/MS/MSSHRTBLZ_GREY_1INGRMM/1602595341546_16.png\",\"description\":\"\",\"aggrAttrs\":[[{\"aggType\":\"terms\",\"value\":\"operator_label\"},{\"aggType\":\"terms\",\"value\":\"card_network\"}]]}";
        String respStr="{\"bottomStripUtilities\":[],\"showFastForward\":0,\"showUpgrade\":0,\"groupingCollapsible\":0,\"metaDescription\":\"null\",\"quotes\":null,\"disclaimerFastForward\":null,\"protectionUrl\":{\"web\":null,\"html5\":null,\"defaultValue\":\"\",\"android\":null,\"ios\":null},\"showHelp\":0,\"skipDeals\":1,\"topLevelCategoryHeader\":{},\"productListInLastGrouping\":1,\"upperAmountThreshold\":null,\"categoryHeader\":\"Pay Your Credit Card Bill\",\"staticForm\":0,\"applicableCities\":[],\"displayName\":\"Credit-Card\",\"lowerAmountThreshold\":null,\"disclaimerHtml\":\"\",\"inputFields\":[{\"configKey\":\"credit_card_number\",\"regex\":\"\",\"showPhoneBook\":0,\"title\":\"Enter Credit Card Number\",\"type\":\"input\"}],\"fastForwardText\":\"Fast Forward\",\"alert\":null,\"buttonObject\":{\"prefetch\":[{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"},{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"}]},\"recentsPrefill\":0,\"disclaimer\":\"Currently we support payment for all Visa credit cards and credit cards issued by IndusInd & Kotak Mahindra Bank.\",\"extraDescription\":null,\"canonicalUrl\":null,\"dealsFastForward\":0,\"extnAttrs\":{\"prefillFirstRecent\":0,\"hideEnabled\":0,\"recentTempType\":\"\"},\"message\":null,\"verticalId\":56,\"metaTitle\":\"null\",\"attributes\":[{\"attrId\":9931,\"name\":\"card_network\",\"description\":null,\"type\":{\"app\":null,\"web\":null,\"html5\":null,\"defaultValue\":\"GRID\",\"android\":null,\"bcandroidapp\":null,\"ios\":null,\"windows\":null},\"displayName\":\"Card Network Type\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true},{\"attrId\":9934,\"name\":\"operator_label\",\"description\":null,\"type\":{\"app\":\"GRID\",\"web\":\"DROPDOWN\",\"html5\":\"GRID\",\"defaultValue\":\"DROPDOWN\",\"android\":\"GRID\",\"bcandroidapp\":null,\"ios\":\"GRID\",\"windows\":null},\"displayName\":\"Select your Bank\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true}],\"gaKey\":\"\",\"metaKeyword\":\"null\",\"categoryId\":131655,\"groupings\":{\"aggKey\":\"operator_label\",\"aggs\":[{\"value\":\"CITI\",\"displayValue\":\"Paytm First\",\"bank_code\":\"CITI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Visa\",\"displayValue\":\"Visa\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"productList\":[{\"price\":4,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"Paytm First\",\"enable_visa_direct\":\"1\",\"displayName\":\"Bill Payment of Paytm First Card\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"CITI\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"paytmfirstCC\",\"schedulable\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"Visa\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\",\"min\":\"1\",\"max\":\"999999\"}],\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"login_mandate\":\"1\",\"insurer_location\":\"N/A\",\"credit_card_length\":\"16\",\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/26.png\",\"lender\":\"Citibank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/14.png\",\"is_paytm_first_card\":\"1\"}]}],\"is_paytm_first_card\":\"1\"},{\"value\":\"Citi\",\"displayValue\":\"Citi\",\"bank_code\":\"CITI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"CITI\",\"displayValue\":\"Visa\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"productList\":[{\"price\":4,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"Paytm First\",\"enable_visa_direct\":\"1\",\"displayName\":\"Bill Payment of Paytm First Card\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"CITI\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"paytmfirstCC\",\"schedulable\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"Visa\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\",\"min\":\"1\",\"max\":\"999999\"}],\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"login_mandate\":\"1\",\"insurer_location\":\"N/A\",\"credit_card_length\":\"16\",\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/26.png\",\"lender\":\"Citibank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/14.png\",\"is_paytm_first_card\":\"1\"}]}]},{\"value\":\"RBL Bank\",\"displayValue\":\"RBL Bank\",\"bank_code\":\"RATN\",\"schedulable\":\"0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Master\",\"displayValue\":\"Master\",\"schedulable\":\"0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"productList\":[{\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/Pa/Paytm_1963/1601369121742_10.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"RBL Bank\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill payment of  RBL Bank credit card\",\"catalogProductId\":**********,\"disclaimer\":\"Your bank may take upto 3 working days in reflecting this payment to your credit card account.\",\"bank_code\":\"RATN\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"Mastercard_RBLBank\",\"schedulable\":\"0\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"Master\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PREBILL-PAYMENTMAST60269155A9D137/0x1920/70/0.PNG\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"payment_mode\":\"NB/UPI / DC\"}]}]},{\"value\":\"SBI\",\"displayValue\":\"SBI\",\"bank_code\":\"SBI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Master\",\"displayValue\":\"Master\",\"schedulable\":\"0\",\"productList\":[{\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/30.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill payment of SBI credit card\",\"catalogProductId\":**********,\"disclaimer\":\"Your bank may take upto 3 working days in reflecting this payment to your credit card account.\",\"bank_code\":\"SBI\",\"payment_mode\":\"NB/UPI / DC\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"Mastercard_SBI\",\"schedulable\":\"0\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"Master\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PREBILL-PAYMENTMAST6026919DA41871/0.PNG\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"VISA\",\"displayValue\":\"VISA\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"clientVisibility\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"1\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"},{\"login_mandate\":\"1\",\"price\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(.[0-9]{0,2})?))(([1-9][0-9]{0,5})(.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PRESBI-BANKMAST602691A168507D/1..png\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"},{\"login_mandate\":\"1\",\"price\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"HDFC Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(.[0-9]{0,2})?))(([1-9][0-9]{0,5})(.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]}]},{\"value\":\"SBI Bank\",\"displayValue\":\"SBI Bank\",\"bank_code\":\"SBI\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"MAESTRO\",\"displayValue\":\"MAESTRO\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/dd/maestro/maestro.jpg\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/Pa/Paytm_1963/1601369121742_10.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/dd/maestro/maestro.jpg\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"MAESTRO\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"MASTER\",\"displayValue\":\"MASTER\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/https://assetscdn.paytm.com/images/catalog/product/P/PR/PREINDUSIND-BANDUMM2489463421637C/1.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/30.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"https://assetscdn.paytm.com/images/catalog/product/P/PR/PREINDUSIND-BANDUMM2489463421637C/1.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"MASTER\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"RUPAY\",\"displayValue\":\"RUPAY\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/rupa/RUPAY.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/31.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/rupa/RUPAY.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"RUPAY\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"VISA\",\"displayValue\":\"VISA\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]}]}]},\"relatedCategories\":[],\"logoUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/M/MS/MSSHRTBLZ_GREY_1INGRMM/1602595341546_16.png\",\"description\":\"\",\"aggrAttrs\":[[{\"aggType\":\"terms\",\"value\":\"operator_label\"},{\"aggType\":\"terms\",\"value\":\"card_network\"}]]}";
        try {
            return JsonUtils.parseJson(respStr, DCATCategoryResponseModel.class);
        } catch (Exception e) {

        }
        return null;
    }



    private DCATCategoryResponseModel mockPaytmFirstCategoryAPIResponse() {
        String respStr="{\"bottomStripUtilities\":[],\"showFastForward\":0,\"showUpgrade\":0,\"groupingCollapsible\":0,\"metaDescription\":\"null\",\"quotes\":null,\"disclaimerFastForward\":null,\"protectionUrl\":{\"web\":null,\"html5\":null,\"defaultValue\":\"\",\"android\":null,\"ios\":null},\"showHelp\":0,\"skipDeals\":1,\"topLevelCategoryHeader\":{},\"productListInLastGrouping\":1,\"upperAmountThreshold\":null,\"categoryHeader\":\"Pay Your Credit Card Bill\",\"staticForm\":0,\"applicableCities\":[],\"displayName\":\"Credit-Card\",\"lowerAmountThreshold\":null,\"disclaimerHtml\":\"\",\"inputFields\":[{\"configKey\":\"credit_card_number\",\"regex\":\"\",\"showPhoneBook\":0,\"title\":\"Enter Credit Card Number\",\"type\":\"input\"}],\"fastForwardText\":\"Fast Forward\",\"alert\":null,\"buttonObject\":{\"prefetch\":[{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"},{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"}]},\"recentsPrefill\":0,\"disclaimer\":\"Currently we support payment for all Visa credit cards and credit cards issued by IndusInd & Kotak Mahindra Bank.\",\"extraDescription\":null,\"canonicalUrl\":null,\"dealsFastForward\":0,\"extnAttrs\":{\"prefillFirstRecent\":0,\"hideEnabled\":0,\"recentTempType\":\"\"},\"message\":null,\"verticalId\":56,\"metaTitle\":\"null\",\"attributes\":[{\"attrId\":9931,\"name\":\"card_network\",\"description\":null,\"type\":{\"app\":null,\"web\":null,\"html5\":null,\"defaultValue\":\"GRID\",\"android\":null,\"bcandroidapp\":null,\"ios\":null,\"windows\":null},\"displayName\":\"Card Network Type\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true},{\"attrId\":9934,\"name\":\"operator_label\",\"description\":null,\"type\":{\"app\":\"GRID\",\"web\":\"DROPDOWN\",\"html5\":\"GRID\",\"defaultValue\":\"DROPDOWN\",\"android\":\"GRID\",\"bcandroidapp\":null,\"ios\":\"GRID\",\"windows\":null},\"displayName\":\"Select your Bank\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true}],\"gaKey\":\"\",\"metaKeyword\":\"null\",\"categoryId\":131655,\"groupings\":{\"aggKey\":\"operator_label\",\"aggs\":[{\"value\":\"Paytm First\",\"displayValue\":\"Paytm First\",\"bank_code\":\"CITI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Visa\",\"displayValue\":\"Visa\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"productList\":[{\"price\":4,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"Paytm First\",\"enable_visa_direct\":\"1\",\"displayName\":\"Bill Payment of Paytm First Card\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"CITI\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"paytmfirstCC\",\"schedulable\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"Visa\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\",\"min\":\"1\",\"max\":\"999999\"}],\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"login_mandate\":\"1\",\"insurer_location\":\"N/A\",\"credit_card_length\":\"16\",\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/26.png\",\"lender\":\"Citibank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/14.png\",\"is_paytm_first_card\":\"1\"}]}],\"is_paytm_first_card\":\"1\"},{\"value\":\"Paytm First\",\"displayValue\":\"Paytm First\",\"bank_code\":\"CITI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Visa\",\"displayValue\":\"Visa\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.jpg\",\"productList\":[{\"price\":4,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"Paytm First\",\"enable_visa_direct\":\"1\",\"displayName\":\"Bill Payment of Paytm First Card\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"CITI\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"paytmfirstCC\",\"schedulable\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"Visa\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\",\"min\":\"1\",\"max\":\"999999\"}],\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"login_mandate\":\"1\",\"insurer_location\":\"N/A\",\"credit_card_length\":\"16\",\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/26.png\",\"lender\":\"Citibank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/14.png\",\"is_paytm_first_card\":\"1\"}]}]},{\"value\":\"RBL Bank\",\"displayValue\":\"RBL Bank\",\"bank_code\":\"RATN\",\"schedulable\":\"0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Master\",\"displayValue\":\"Master\",\"schedulable\":\"0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"productList\":[{\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/Pa/Paytm_1963/1601369121742_10.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"RBL Bank\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill payment of  RBL Bank credit card\",\"catalogProductId\":**********,\"disclaimer\":\"Your bank may take upto 3 working days in reflecting this payment to your credit card account.\",\"bank_code\":\"RATN\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"Mastercard_RBLBank\",\"schedulable\":\"0\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"Master\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PREBILL-PAYMENTMAST60269155A9D137/0x1920/70/0.PNG\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"payment_mode\":\"NB/UPI / DC\"}]}]},{\"value\":\"SBI\",\"displayValue\":\"SBI\",\"bank_code\":\"SBI\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"Master\",\"displayValue\":\"Master\",\"schedulable\":\"0\",\"productList\":[{\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/30.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill payment of SBI credit card\",\"catalogProductId\":**********,\"disclaimer\":\"Your bank may take upto 3 working days in reflecting this payment to your credit card account.\",\"bank_code\":\"SBI\",\"payment_mode\":\"NB/UPI / DC\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"operator\":\"Mastercard_SBI\",\"schedulable\":\"0\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"Master\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PREBILL-PAYMENTMAST6026919DA41871/0.PNG\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"VISA\",\"displayValue\":\"VISA\",\"schedulable\":\"1\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"clientVisibility\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"1\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"},{\"login_mandate\":\"1\",\"price\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(.[0-9]{0,2})?))(([1-9][0-9]{0,5})(.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/S/ST/STAGING-PRESBI-BANKMAST602691A168507D/1..png\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"},{\"login_mandate\":\"1\",\"price\":1,\"pg_info\":\"{ \\\"payment_modes_enabled \\\": [ \\\"IMPS\\\"] }\",\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"SBI\",\"enable_visa_direct\":\"0\",\"insurer_location\":\"N/A\",\"displayName\":\"HDFC Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"neft_SBI\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(.[0-9]{0,2})?))(([1-9][0-9]{0,5})(.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]}]},{\"value\":\"SBI Bank\",\"displayValue\":\"SBI Bank\",\"bank_code\":\"SBI\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"MAESTRO\",\"displayValue\":\"MAESTRO\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/dd/maestro/maestro.jpg\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/Pa/Paytm_1963/1601369121742_10.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/dd/maestro/maestro.jpg\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"MAESTRO\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"MASTER\",\"displayValue\":\"MASTER\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/https://assetscdn.paytm.com/images/catalog/product/P/PR/PREINDUSIND-BANDUMM2489463421637C/1.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/30.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"https://assetscdn.paytm.com/images/catalog/product/P/PR/PREINDUSIND-BANDUMM2489463421637C/1.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"MASTER\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"RUPAY\",\"displayValue\":\"RUPAY\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/rupa/RUPAY.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/31.png\",\"enable_bill_payment\":\"0\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/rupa/RUPAY.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"RUPAY\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]},{\"value\":\"VISA\",\"displayValue\":\"VISA\",\"schedulable\":\"0\",\"operator_min_android_version\":\"6.1.0\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"productList\":[{\"login_mandate\":\"1\",\"price\":1,\"productId\":**********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/32.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"SBI Bank\",\"enable_visa_direct\":\"1\",\"insurer_location\":\"N/A\",\"displayName\":\"SBI Bank\",\"catalogProductId\":**********,\"operator_min_android_version\":\"6.1.0\",\"bank_code\":\"SBI\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"verticalId\":56,\"max_amount\":\"999999\",\"loan_payment_mode\":\"N/A\",\"operator\":\"SBI Bank\",\"schedulable\":\"0\",\"image\":\"http://paytmimg.s3.amazonaws.com/games/Visa/1%20%281%29.png\",\"min_amount\":\"1\",\"pgmid\":\"1\",\"paytype_label\":\"Credit card\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":1,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":1,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"inputFields\":[{\"configKey\":\"price\",\"regex\":\"^((999999)|((?!(9{6}(\\\\.[0-9]{0,2})?))(([1-9][0-9]{0,5})(\\\\.[0-9]{1,2})?)))$\",\"showPhoneBook\":0,\"min\":\"1\",\"max\":\"999999\",\"readOnly\":0,\"type\":\"amount\",\"title\":\"Amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/24.png\",\"lender\":\"SBI Bank\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PR/PREHDFC-BANKDUMM248946E1C9F293/15.png\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\"}]}]}]},\"relatedCategories\":[],\"logoUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/M/MS/MSSHRTBLZ_GREY_1INGRMM/1602595341546_16.png\",\"description\":\"\",\"aggrAttrs\":[[{\"aggType\":\"terms\",\"value\":\"operator_label\"},{\"aggType\":\"terms\",\"value\":\"card_network\"}]]}";
        try {
            return JsonUtils.parseJson(respStr, DCATCategoryResponseModel.class);
        } catch (Exception e) {

        }
        return null;
    }

    public CommonCache mockCommonCache() {
        String str = "{\"cacheKey\":\"0:financial services:citi:visa\",\"cacheValue\":\"{\\\"productId\\\":**********,\\\"operator\\\":\\\"neft_CITI\\\",\\\"categoryId\\\":131655,\\\"service\\\":\\\"financial services\\\",\\\"cardType\\\":\\\"CC\\\",\\\"bankName\\\":\\\"CITI\\\",\\\"cardNetwork\\\":\\\"VISA\\\"}\"}";
        return JsonUtils.parseJson(str, CommonCache.class);
    }

    public CommonCache mockCommonCacheForAmex() {
        String str = "{\"cacheKey\":\"0:financial services:citi:amex\",\"cacheValue\":\"{\\\"productId\\\":**********,\\\"operator\\\":\\\"neft_CITI\\\",\\\"categoryId\\\":131655,\\\"service\\\":\\\"financial services\\\",\\\"cardType\\\":\\\"CC\\\",\\\"bankName\\\":\\\"CITI\\\",\\\"cardNetwork\\\":\\\"AMEX\\\"}\"}";
        return JsonUtils.parseJson(str, CommonCache.class);
    }

    public CommonCache mockCommonCacheForMasterCard() {
        String str = "{\"cacheKey\":\"0:financial services:citi:mastercard\",\"cacheValue\":\"{\\\"productId\\\":**********,\\\"operator\\\":\\\"neft_CITI\\\",\\\"categoryId\\\":131655,\\\"service\\\":\\\"financial services\\\",\\\"cardType\\\":\\\"CC\\\",\\\"bankName\\\":\\\"CITI\\\",\\\"cardNetwork\\\":\\\"MASTERCARD\\\"}\"}";
        return JsonUtils.parseJson(str, CommonCache.class);
    }

    public CommonCache mockCommonCacheForVisa() {
        String str = "{\"cacheKey\":\"0:financial services:citi:visa\",\"cacheValue\":\"{\\\"productId\\\":**********,\\\"operator\\\":\\\"neft_CITI\\\",\\\"categoryId\\\":131655,\\\"service\\\":\\\"financial services\\\",\\\"cardType\\\":\\\"CC\\\",\\\"bankName\\\":\\\"CITI\\\",\\\"cardNetwork\\\":\\\"VISA\\\"}\"}";
        return JsonUtils.parseJson(str, CommonCache.class);
    }
    public CommonCache mockCommonCacheForDiners() {
        String str = "{\"cacheKey\":\"0:financial services:citi:diners\",\"cacheValue\":\"{\\\"productId\\\":**********,\\\"operator\\\":\\\"neft_CITI\\\",\\\"categoryId\\\":131655,\\\"service\\\":\\\"financial services\\\",\\\"cardType\\\":\\\"CC\\\",\\\"bankName\\\":\\\"CITI\\\",\\\"cardNetwork\\\":\\\"DINERS\\\"}\"}";
        return JsonUtils.parseJson(str, CommonCache.class);
    }
    public CommonCache mockCommonCacheForDiscover() {
        String str = "{\"cacheKey\":\"0:financial services:citi:discover\",\"cacheValue\":\"{\\\"productId\\\":**********,\\\"operator\\\":\\\"neft_CITI\\\",\\\"categoryId\\\":131655,\\\"service\\\":\\\"financial services\\\",\\\"cardType\\\":\\\"CC\\\",\\\"bankName\\\":\\\"CITI\\\",\\\"cardNetwork\\\":\\\"DISCOVER\\\"}\"}";
        return JsonUtils.parseJson(str, CommonCache.class);
    }

    private List<SavedCardDetails> mockSavedCard() {
        List<SavedCardDetails> list = new ArrayList<>();
        try {
        	String respStr1="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5aa002ce4a1645eaf0f1f\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"2167\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005518426506297875\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false}";
        	list.add(JsonUtils.parseJson(respStr1, SavedCardDetails.class));
        	String respStr2="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5ab0d2ce4a1645eaf0f21\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"0355\",\"cardType\":\"DC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005521101687722780\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false}";
        	list.add(JsonUtils.parseJson(respStr2, SavedCardDetails.class));
        	String respStr3="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61cdb8a69ecd892c08b16c03\",\"cardScheme\":\"VISA\",\"expiryDate\":\"102024\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"0336\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013021313533662636897980\",\"tokenBin\":\"*********\",\"tokenStatus\":\"FAILED\",\"cardFirstSixDigits\":\"471865\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":false}";
        	list.add(JsonUtils.parseJson(respStr3, SavedCardDetails.class));
        	String respStr4="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"20200818298502506a05614888769322b778ca6be8fbf\",\"cardScheme\":\"VISA\",\"expiryDate\":\"012024\",\"issuingBankName\":\"ICICI Bank\",\"issuingBankCardVariant\":\"Diners Black\",\"cardSuffix\":\"2168\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
        	list.add(JsonUtils.parseJson(respStr4, SavedCardDetails.class));
        	String respStr5="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"202111096578085bba73e3e45b87ffc1b2f7050423312\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"3216\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"476136\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":true}";
        	list.add(JsonUtils.parseJson(respStr5, SavedCardDetails.class));
        	String respStr6="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"2021101172350a57dd55adcff1e73cf095b8fd85e622e\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"0355\",\"cardType\":\"DC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
        	list.add(JsonUtils.parseJson(respStr6, SavedCardDetails.class));

        } catch (Exception e) {

        }
        return list;
    }

    private List<SavedCardDetails> mockSavedCardWithSkinDetails() {
        List<SavedCardDetails> list = new ArrayList<>();
        try {
        	String respStr1="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5aa002ce4a1645eaf0f1f\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"2167\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005518426506297875\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false, \"colors\":[{\"target\":\"FOREGROUND\",\"value\":\"0xFF5733\"},{\"target\":\"BACKGROUND\",\"value\":\"0xCAAAA3\"},{\"target\":\"LABEL\",\"value\":\"0x000000\"}],\"mediaAssets\":[{\"assetType\":\"NETWORK_LOGO\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"source\":\"DATABASE_MATCH\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"CARD_BACKGROUND\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"COMBINED_CARD_ART\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}}]}";
        	list.add(JsonUtils.parseJson(respStr1, SavedCardDetails.class));
        	String respStr2="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5ab0d2ce4a1645eaf0f21\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"0355\",\"cardType\":\"DC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005521101687722780\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false, \"colors\":[{\"target\":\"FOREGROUND\",\"value\":\"0xFF5733\"},{\"target\":\"BACKGROUND\",\"value\":\"0xCAAAA3\"},{\"target\":\"LABEL\",\"value\":\"0x000000\"}],\"mediaAssets\":[{\"assetType\":\"NETWORK_LOGO\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"source\":\"DATABASE_MATCH\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"CARD_BACKGROUND\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"COMBINED_CARD_ART\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}}]}";
        	list.add(JsonUtils.parseJson(respStr2, SavedCardDetails.class));
        	String respStr3="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61cdb8a69ecd892c08b16c03\",\"cardScheme\":\"VISA\",\"expiryDate\":\"102024\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"0336\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013021313533662636897980\",\"tokenBin\":\"*********\",\"tokenStatus\":\"FAILED\",\"cardFirstSixDigits\":\"471865\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":false, \"colors\":[{\"target\":\"FOREGROUND\",\"value\":\"0xFF5733\"},{\"target\":\"BACKGROUND\",\"value\":\"0xCAAAA3\"},{\"target\":\"LABEL\",\"value\":\"0x000000\"}],\"mediaAssets\":[{\"assetType\":\"NETWORK_LOGO\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"source\":\"DATABASE_MATCH\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"CARD_BACKGROUND\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"COMBINED_CARD_ART\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}}]}";
        	list.add(JsonUtils.parseJson(respStr3, SavedCardDetails.class));
        	String respStr4="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"20200818298502506a05614888769322b778ca6be8fbf\",\"cardScheme\":\"VISA\",\"expiryDate\":\"012024\",\"issuingBankName\":\"ICICI Bank\",\"issuingBankCardVariant\":\"Diners Black\",\"cardSuffix\":\"2168\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true,\"colors\":[{\"target\":\"FOREGROUND\",\"value\":\"0xFF5733\"},{\"target\":\"BACKGROUND\",\"value\":\"0xCAAAA3\"},{\"target\":\"LABEL\",\"value\":\"0x000000\"}],\"mediaAssets\":[{\"assetType\":\"NETWORK_LOGO\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"source\":\"DATABASE_MATCH\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"CARD_BACKGROUND\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"COMBINED_CARD_ART\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}}]}";
        	list.add(JsonUtils.parseJson(respStr4, SavedCardDetails.class));
        	String respStr5="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"202111096578085bba73e3e45b87ffc1b2f7050423312\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"3216\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"476136\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":true,\"colors\":[{\"target\":\"FOREGROUND\",\"value\":\"0xFF5733\"},{\"target\":\"BACKGROUND\",\"value\":\"0xCAAAA3\"},{\"target\":\"LABEL\",\"value\":\"0x000000\"}],\"mediaAssets\":[{\"assetType\":\"NETWORK_LOGO\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"source\":\"DATABASE_MATCH\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"CARD_BACKGROUND\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"COMBINED_CARD_ART\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}}]}";
        	list.add(JsonUtils.parseJson(respStr5, SavedCardDetails.class));
        	String respStr6="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"2021101172350a57dd55adcff1e73cf095b8fd85e622e\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"0355\",\"cardType\":\"DC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true,\"colors\":[{\"target\":\"FOREGROUND\",\"value\":\"0xFF5733\"},{\"target\":\"BACKGROUND\",\"value\":\"0xCAAAA3\"},{\"target\":\"LABEL\",\"value\":\"0x000000\"}],\"mediaAssets\":[{\"assetType\":\"NETWORK_LOGO\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"source\":\"DATABASE_MATCH\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"CARD_BACKGROUND\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}},{\"assetType\":\"COMBINED_CARD_ART\",\"source\":\"DEFAULT_MATCH\",\"updatedAt\":\"2023-12-20T15:27:59Z\",\"profiles\":{\"ldpi\":\"\",\"mdpi\":\"\",\"hdpi\":\"\",\"xhdpi\":\"\",\"xxhdpi\":\"\"}}]}";
        	list.add(JsonUtils.parseJson(respStr6, SavedCardDetails.class));

        } catch (Exception e) {

        }
        return list;
    }

    private List<SavedCardDetails> mockSavedCardTokenStatusCheck() {
        List<SavedCardDetails> list = new ArrayList<>();
        try {
            String respStr1="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5aa002ce4a1645eaf0f1f\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"2101\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005518426506297875\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false}";
            list.add(JsonUtils.parseJson(respStr1, SavedCardDetails.class));
            String respStr2="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5ab0d2ce4a1645eaf0f21\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"2010\",\"cardType\":\"DC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005521101687722780\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false}";
            list.add(JsonUtils.parseJson(respStr2, SavedCardDetails.class));
            String respStr3="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61cdb8a69ecd892c08b16c03\",\"cardScheme\":\"VISA\",\"expiryDate\":\"102024\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"3200\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013021313533662636897980\",\"tokenBin\":\"*********\",\"tokenStatus\":\"FAILED\",\"cardFirstSixDigits\":\"471865\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":false}";
            list.add(JsonUtils.parseJson(respStr3, SavedCardDetails.class));
            String respStr4="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"20200818298502506a05614888769322b778ca6be8fbf\",\"cardScheme\":\"VISA\",\"expiryDate\":\"012024\",\"issuingBankName\":\"ICICI Bank\",\"issuingBankCardVariant\":\"Diners Black\",\"cardSuffix\":\"2168\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
            list.add(JsonUtils.parseJson(respStr4, SavedCardDetails.class));
            String respStr5="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"202111096578085bba73e3e45b87ffc1b2f7050423312\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"2170\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"476136\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":true}";
            list.add(JsonUtils.parseJson(respStr5, SavedCardDetails.class));
            String respStr6="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5aa002ce4a1645eaf0f1f\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"3366\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005518426506297865\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"472654\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false}";
            list.add(JsonUtils.parseJson(respStr6, SavedCardDetails.class));
        } catch (Exception e) {

        }
        return list;
    }


    private List<SavedCardDetails> mockSavedCardWithSameSuffix() {
        List<SavedCardDetails> list = new ArrayList<>();
        try {
        	String respStr1="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5aa002ce4a1645eaf0f1f\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"2169\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005518426506297875\",\"tokenBin\":\"*********\",\"tokenStatus\":\"FAILED\",\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false}";
        	list.add(JsonUtils.parseJson(respStr1, SavedCardDetails.class));
        	String respStr2="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61d5ab0d2ce4a1645eaf0f21\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"0355\",\"cardType\":\"DC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013022005521101687722780\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":false}";
        	list.add(JsonUtils.parseJson(respStr2, SavedCardDetails.class));
        	String respStr3="{\"cardCoft\":true,\"eligibleForCoft\":false,\"savedCardId\":\"61cdb8a69ecd892c08b16c03\",\"cardScheme\":\"VISA\",\"expiryDate\":\"102024\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"0336\",\"cardType\":\"CC\",\"isCardCoft\":true,\"panUniqueReference\":\"V0010013021313533662636897980\",\"tokenBin\":\"*********\",\"tokenStatus\":\"ACTIVE\",\"cardFirstSixDigits\":\"471865\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":false}";
        	list.add(JsonUtils.parseJson(respStr3, SavedCardDetails.class));
        	String respStr4="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"20200818298502506a05614888769322b778ca6be8fbf\",\"cardScheme\":\"VISA\",\"expiryDate\":\"012024\",\"issuingBankName\":\"ICICI Bank\",\"issuingBankCardVariant\":\"Diners Black\",\"cardSuffix\":\"2168\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
        	list.add(JsonUtils.parseJson(respStr4, SavedCardDetails.class));
        	String respStr5="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"202111096578085bba73e3e45b87ffc1b2f7050423312\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"3216\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"476136\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":true}";
        	list.add(JsonUtils.parseJson(respStr5, SavedCardDetails.class));
        	String respStr6="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"2021101172350a57dd55adcff1e73cf095b8fd85e622e\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"2167\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
        	list.add(JsonUtils.parseJson(respStr6, SavedCardDetails.class));
        } catch (Exception e) {

        }
        return list;
    }


    private List<SavedCardDetails> mockSavedCardWithV1SameSuffix() {
        List<SavedCardDetails> list = new ArrayList<>();
        try {
            String respStr4="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"20200818298502506a05614888769322b778ca6be8fbf\",\"cardScheme\":\"VISA\",\"expiryDate\":\"012024\",\"issuingBankName\":\"ICICI Bank\",\"issuingBankCardVariant\":\"Diners Black\",\"cardSuffix\":\"2167\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
            list.add(JsonUtils.parseJson(respStr4, SavedCardDetails.class));
            String respStr5="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"202111096578085bba73e3e45b87ffc1b2f7050423312\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"3216\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"476136\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":true}";
            list.add(JsonUtils.parseJson(respStr5, SavedCardDetails.class));
            String respStr6="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"2021101172350a57dd55adcff1e73cf095b8fd85e622e\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"2167\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
            list.add(JsonUtils.parseJson(respStr6, SavedCardDetails.class));
        } catch (Exception e) {

        }
        return list;
    }

    private List<SavedCardDetails> mockSavedCardForV1False() {
        List<SavedCardDetails> list = new ArrayList<>();
        try {
            String respStr4="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"20200818298502506a05614888769322b778ca6be8fbf\",\"cardScheme\":\"VISA\",\"expiryDate\":\"012024\",\"issuingBankName\":\"ICICI Bank\",\"issuingBankCardVariant\":\"Diners Black\",\"cardSuffix\":\"2167\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"472642\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
            list.add(JsonUtils.parseJson(respStr4, SavedCardDetails.class));
            String respStr5="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"202111096578085bba73e3e45b87ffc1b2f7050423312\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"HDFC Bank\",\"cardSuffix\":\"3216\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"476136\",\"issuerCode\":\"HDFC\",\"isEligibleForCoft\":true}";
            list.add(JsonUtils.parseJson(respStr5, SavedCardDetails.class));
            String respStr6="{\"cardCoft\":false,\"eligibleForCoft\":true,\"savedCardId\":\"2021101172350a57dd55adcff1e73cf095b8fd85e622e\",\"cardScheme\":\"VISA\",\"expiryDate\":\"122023\",\"issuingBankName\":\"ICICI Bank\",\"cardSuffix\":\"0355\",\"cardType\":\"CC\",\"isCardCoft\":false,\"cardFirstSixDigits\":\"401806\",\"issuerCode\":\"ICICI\",\"isEligibleForCoft\":true}";
            list.add(JsonUtils.parseJson(respStr6, SavedCardDetails.class));
        } catch (Exception e) {

        }
        return list;
    }

    private DCATCategoryResponseModel mockCategoryAPIResponseNew() {
        String respStr="{\"bottomStripUtilities\":[],\"showFastForward\":0,\"showUpgrade\":0,\"groupingCollapsible\":0,\"metaDescription\":\"Pay the bill of your Visa, Master, Amex, and Diners Credit Cards of all major banks using the Paytm credit cards bill payment facility & get cashback offers on Paytm\",\"quotes\":[],\"disclaimerFastForward\":null,\"protectionUrl\":{\"web\":null,\"html5\":null,\"defaultValue\":\"\",\"android\":null,\"ios\":null},\"showHelp\":0,\"skipDeals\":1,\"topLevelCategoryHeader\":{},\"productListInLastGrouping\":1,\"upperAmountThreshold\":null,\"categoryHeader\":\"Credit Card Bill Payment\",\"logoUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/M/MS/MSSHRTBLZ_GREY_1INGRMM/1602595341546_16.png\",\"staticForm\":0,\"applicableCities\":[],\"displayName\":\"Credit-Card\",\"lowerAmountThreshold\":null,\"description\":\"<h1><strong>Make Online Credit Card Payment at Paytm</strong></h1>\\n<p>Don’t worry about your due dates for Credit Card bill payment as you can pay your credit card bills from anywhere and anytime on Paytm. Paytm allows you to make credit card bill payment for the Visa, Master, American Express (Amex), and Diners credit card of all the major bank. Be it SBI, HDFC, IndusInd Bank, Citi Bank and many other.</p>\\n\\n<p>So just #PaytmKaro and pay your credit card bill instantly on Paytm and leave all the stress behind. Paytm’s credit card bill payment service is very easy and just takes a few steps to get processed. Moreover, you get many different payment options for your Credit Card payment.</p>\\n\\n<h2><strong>Flexible Payment For Online Credit Card Payment</strong></h2>\\n\\n<p>Paytm provide you with flexible payment methods for your Credit Card bill payment. You can pay through a debit card(less than 2000), net banking and UPI </p> \\n\\n<h3><strong>How to Make Credit Card Bill Payment on Paytm</strong></h3>\\n<p>It is very easy and convenient to pay your credit card bills on Paytm.com or Paytm mobile app. All you have to do is follow these simple steps — <br/>\\n\\n1. Go to credit card payment page on Paytm<br/>\\n2. Click on ‘Proceed’<br/>\\n3. Enter the details and bill amount<br/>\\n4. Proceed with the payment</p>\\n\\n<h4><strong>Frequently Asked Questions – Credit Card Payment</strong></h4>\\n<div itemscope=\\\"\\\" itemtype=\\\"http://schema.org/FAQPage\\\"> <div itemscope itemprop=\\\"mainEntity\\\"  itemtype=\\\"http://schema.org/Question\\\"> <h5 itemprop=\\\"name text\\\" class=\\\"albos-qna-question\\\"><b>Can I only pay the minimum due amount?</b></h5><div itemprop=\\\"acceptedAnswer\\\" itemscope=\\\"\\\" itemtype=\\\"http://schema.org/Answer\\\"><p itemprop=\\\"text\\\" class=\\\"albos-qna-answer\\\">Yes, you can only the minimum due amount. This will saveRecentToCustomerBill you from any penalty amount, however, if you do not pay the complete due amount, you will invite some unnecessary interest.\\n</div></div> \\n<div itemscope itemprop=\\\"mainEntity\\\" itemtype=\\\"http://schema.org/Question\\\"> <h5 itemprop=\\\"name text\\\" class=\\\"albos-qna-question\\\"><b>Do I need to pay my credit card in full?</b></h5><div itemprop=\\\"acceptedAnswer\\\" itemscope=\\\"\\\" itemtype=\\\"http://schema.org/Answer\\\"><p itemprop=\\\"text\\\" class=\\\"albos-qna-answer\\\">You should always try to pay the full due amount every month before the due date. Failing to do so can attract unnecessary interest or even a penalty.</div></div>\\n<div itemscope itemprop=\\\"mainEntity\\\" itemtype=\\\"http://schema.org/Question\\\"><h5 itemprop=\\\"name text\\\" class=\\\"albos-qna-question\\\"><b>When should I pay my credit card bill?</b></h5><div itemprop=\\\"acceptedAnswer\\\" itemscope=\\\"\\\" itemtype=\\\"http://schema.org/Answer\\\"><p itemprop=\\\"text\\\" class=\\\"albos-qna-answer\\\">Ideally, you should always pay your credit card bills before or till the due date. If you fail to pay your credit card bill before that then you need to be paying extra interest for the outstanding amount.\\n</p></div></div>\\n<div itemscope itemprop=\\\"mainEntity\\\" itemtype=\\\"http://schema.org/Question\\\"> <h5 itemprop=\\\"name text\\\" class=\\\"albos-qna-question\\\"><b>How much should I pay on my credit card to avoid interest?</b></h5><div itemprop=\\\"acceptedAnswer\\\" itemscope=\\\"\\\" itemtype=\\\"http://schema.org/Answer\\\"><p itemprop=\\\"text\\\" class=\\\"albos-qna-answer\\\">To avoid any additional interest on your credit card payment, make sure to pay the full credit card bill before the end of the due date.  \\n</p></div></div>\\n<div itemscope itemprop=\\\"mainEntity\\\" itemtype=\\\"http://schema.org/Question\\\"> <h5 itemprop=\\\"name text\\\" class=\\\"albos-qna-question\\\"><b>Does late payment affect my credit Score?</b></h5> <div itemprop=\\\"acceptedAnswer\\\" itemscope=\\\"\\\" itemtype=\\\"http://schema.org/Answer\\\"><p itemprop=\\\"text\\\" class=\\\"albos-qna-answer\\\">The loss of the credit score depends on many factors such as-<br/>\\n1. How long are you delaying your payment<br/>\\n2. How frequently the delays in payment occur<br/>\\n3. Your current credit score<br/>\\nTry to make your payments before your due dates to improve your credit score. \\n</p></div></div>\\n<div itemscope itemprop=\\\"mainEntity\\\" itemtype=\\\"http://schema.org/Question\\\"> <h5 itemprop=\\\"name text\\\" class=\\\"albos-qna-question\\\"><b>\\nHow long does it take to process the credit card bill payment on paytm?\\n</b></h5><div itemprop=\\\"acceptedAnswer\\\" itemscope=\\\"\\\" itemtype=\\\"http://schema.org/Answer\\\"><p itemprop=\\\"text\\\" class=\\\"albos-qna-answer\\\">It takes a few seconds to get the payment processed from our side. You will receive a message and an email regarding the payment as soon as it gets processed.</p></div><div itemscope itemprop=\\\"mainEntity\\\" itemtype=\\\"http://schema.org/Question\\\"> <h5 itemprop=\\\"name text\\\" class=\\\"albos-qna-question\\\"><b>\\nHow will I get confirmation after making the payment?\\n</b></h5><div itemprop=\\\"acceptedAnswer\\\" itemscope=\\\"\\\" itemtype=\\\"http://schema.org/Answer\\\"><p itemprop=\\\"text\\\" class=\\\"albos-qna-answer\\\">You will receive a message and an email on your registered mobile number and email ID after the payment gets processed on Paytm.</p>\\n<h5><b>Popular Searches on Paytm</b></h5>\\n<a href=https://paytm.com/recharge/jio-prepaid-mobile-online-recharge>Jio Online Recharge</a> | <a href=https://paytm.com/electricity-bill-payment/karnataka/bangalore-electricity-supply-company-ltd-bescom>BESCOM</a> | <a href=https://paytm.com/dth-recharge/sun-direct>Sun Direct Recharge</a> | <a href=https://paytm.com/fastag>Fastag</a> | <a href=https://paytm.com/electricity-bill-payment/tamil-nadu>TNEB Bill Payment</a> | <a href=https://paytm.com/movies>Movie Tickets</a> | <a href=https://paytm.com/insurance-premium-payment/lic-of-india>LIC Premium Payment</a> | <a href=https://paytm.com/challan-bill-payment>eChallan</a>\",\"disclaimerHtml\":\"\",\"inputFields\":[{\"configKey\":\"credit_card_number\",\"regex\":\"\",\"showPhoneBook\":0,\"title\":\"Enter Credit Card Number\",\"type\":\"input\"}],\"fastForwardText\":\"Fast Forward\",\"alert\":null,\"buttonObject\":{\"prefetch\":[{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"},{\"pre\":\"Proceed\",\"post\":\"Proceed\",\"processing\":\"Processing\"}]},\"recentsPrefill\":0,\"disclaimer\":\"Pay for all Visa, Mastercard, American Express, Diners & Rupay Credit Cards issued by all major banks.\",\"extraDescription\":\"\",\"canonicalUrl\":\"\",\"aggrAttrs\":[[{\"aggType\":\"terms\",\"value\":\"operator_label\"},{\"aggType\":\"terms\",\"value\":\"card_network\"}]],\"dealsFastForward\":0,\"extnAttrs\":{\"complianceText\":\"This card will be saved for your convenience for faster bill payment facilitation in future.\",\"prefillFirstRecent\":0,\"catProperties\":null,\"catServices\":null,\"bbps_logo_landing\":0,\"hideEnabled\":0},\"message\":\"\",\"verticalId\":56,\"metaTitle\":\"Credit Card Payment - Pay Bills of Visa, Master, Amex & Diners Credit Cards\",\"attributes\":[{\"attrId\":1211,\"name\":\"card_network\",\"description\":null,\"type\":{\"app\":null,\"web\":null,\"html5\":null,\"defaultValue\":\"GRID\",\"android\":null,\"bcandroidapp\":null,\"ios\":null,\"windows\":null},\"displayName\":\"Card Network Type\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true},{\"attrId\":1179,\"name\":\"operator_label\",\"description\":null,\"type\":{\"app\":\"GRID\",\"web\":\"DROPDOWN\",\"html5\":\"GRID\",\"defaultValue\":\"DROPDOWN\",\"android\":\"GRID\",\"bcandroidapp\":null,\"ios\":\"GRID\",\"windows\":null},\"displayName\":\"Select your Bank\",\"error\":null,\"placeholder\":null,\"showField\":1,\"minValue\":null,\"maxValue\":null,\"defaultValue\":\"N/A\",\"allowedValues\":[],\"cart_verify_type\":\"CC_BILL_PAYMENT\",\"createtoken_alipg_call\":true}],\"gaKey\":\"onus_credit_card\",\"metaKeyword\":\"\",\"categoryId\":156705,\"groupings\":{\"aggKey\":\"operator_label\",\"aggs\":[{\"value\":\"HDFC Bank\",\"displayValue\":\"HDFC Bank\",\"bank_code\":\"HDFC\",\"schedulable\":\"0\",\"operator_min_android_version\":\"8.1.1\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"8.1.1\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"VISA\",\"displayValue\":\"VISA\",\"schedulable\":\"0\",\"operator_min_android_version\":\"8.1.1\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"8.1.1\",\"productList\":[{\"price\":1,\"productId\":*********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/22.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"gwParams\":\"{ \\\"payment_modes_enabled\\\": [ \\\"IMPS\\\"] }\",\"operator_label\":\"HDFC Bank\",\"pulse_myorders_event_flow\":\"1\",\"enable_visa_direct\":\"0\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill Payment of HDFC Bank Credit Card\",\"catalogProductId\":*********,\"operator_min_android_version\":\"8.1.1\",\"disclaimer\":\"99.2% transactions for this bank settle to card in less than 10 minutes\",\"bank_code\":\"HDFC\",\"threshold_turn_around_time\":\"600\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"bgCol\":\"linear-gradient(106.3deg, #323E4A 0%, #1E262E 100%)\",\"pulse_category_name\":\"onus_credit_card\",\"verticalId\":56,\"operator\":\"NEFT_HDFCBank\",\"schedulable\":\"0\",\"operator_display_label\":\"HDFC Bank\",\"paytype_display_label\":\"Bill Payment\",\"image\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTCRED108391061E54033/2.png\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":0,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":0,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"operator_min_ios_version\":\"8.1.1\",\"inputFields\":[{\"configKey\":\"price\",\"showPhoneBook\":0,\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"readOnly\":0,\"title\":\"Amount\",\"type\":\"amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/9.png\",\"recharge_number_label\":\"Amount\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTCRED1083910EA9A0B2D/0x1920/70/0.PNG\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/8.png\",\"service_display_label\":\"Credit Card\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"chat_channel_id\":\"e00a3b98-a8b4-467c-b2eb-6432e6d0334a\"}]}]},{\"value\":\"ICICI Bank\",\"displayValue\":\"ICICI Bank\",\"bank_code\":\"ICICI\",\"schedulable\":\"0\",\"operator_min_android_version\":\"8.1.1\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"8.1.1\",\"aggKey\":\"card_network\",\"aggs\":[{\"value\":\"VISA\",\"displayValue\":\"VISA\",\"schedulable\":\"0\",\"operator_min_android_version\":\"8.1.1\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/operators/*************.png\",\"operator_min_ios_version\":\"8.1.1\",\"productList\":[{\"price\":1,\"productId\":*********,\"card_network_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/22.png\",\"enable_bill_payment\":\"1\",\"service_label\":\"Financial Services\",\"operator_label\":\"ICICI Bank\",\"pulse_myorders_event_flow\":\"1\",\"enable_visa_direct\":\"0\",\"request_type\":\"CC_BILL_PAYMENT\",\"displayName\":\"Bill Payment of ICICI Bank Credit Card\",\"catalogProductId\":*********,\"operator_min_android_version\":\"8.1.1\",\"disclaimer\":\"98.3% transactions for this bank settle to card in less than 2 hours\",\"bank_code\":\"ICICI\",\"threshold_turn_around_time\":\"600\",\"credit_card_length\":\"16\",\"extnAttrs\":{\"conv_fee\":0},\"bgCol\":\"linear-gradient(106.3deg, #AA3E27 0%, #730D12 94.27%)\",\"pulse_category_name\":\"onus_credit_card\",\"verticalId\":56,\"operator\":\"neft_ICICIBank\",\"schedulable\":\"0\",\"operator_display_label\":\"ICICI Bank\",\"paytype_display_label\":\"Bill Payment\",\"image\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTCRED108391061E54033/2.png\",\"paytype_label\":\"Credit Card Bill Payment\",\"card_network\":\"VISA\",\"payTypeSupported\":{\"cc\":0,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":0,\"dc\":1,\"upi\":1},\"prefetch\":\"0\",\"operator_min_ios_version\":\"8.1.1\",\"inputFields\":[{\"configKey\":\"price\",\"showPhoneBook\":0,\"regex\":\"((^[1-9][0-9]{0,5})|(^[1-9][0-9]{0,5}[.][0-9]{0,2}))$\",\"readOnly\":0,\"title\":\"Amount\",\"type\":\"amount\",\"message\":\"\"}],\"card_bg\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/11.png\",\"recharge_number_label\":\"Amount\",\"imageUrl\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTCRED108391088BA5D87/0x1920/70/0.PNG\",\"bank_name_logo\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/10.png\",\"service_display_label\":\"Credit Card\",\"proceed_directly_to_PG\":\"1\",\"service\":\"Financial Services\",\"paytype\":\"Credit card\",\"chat_channel_id\":\"e00a3b98-a8b4-467c-b2eb-6432e6d0334a\"}]}]}]},\"relatedCategories\":[]}";

        try {
            return JsonUtils.parseJson(respStr, DCATCategoryResponseModel.class);
        } catch (Exception e) {

        }
        return null;
    }

    @Test
    public void getSavedCardswithTokenStatusCheck() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardTokenStatusCheck();
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true,false);
        assertEquals(4, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals("XXXX XXXX XXXX 3366", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2168", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2101", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2170", savedCardResponse.getSavedCardApiResponses().get(3).getRechargeNumber());

    }


    @Test
    public void getSavedCardInfoForPlatform() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardV2DetailsOnlyPlatform();
        when(pgService.getSavedCards(1L, true)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V1, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true,false);
        assertEquals(1, savedCardResponse.getSavedCardApiResponses().size());
        Assert.assertNotNull(savedCardResponse.getSavedCardApiResponses().get(0).getAdditionalInfo().isCardCoft());
        Assert.assertNotNull(savedCardResponse.getSavedCardApiResponses().get(0).getAdditionalInfo().getCin());
        Assert.assertNotNull(savedCardResponse.getSavedCardApiResponses().get(0).getProduct().getProductId());
        Assert.assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getProduct().getCardType(), "CC");
    }

    @Test
    public void getSavedCardInfoV2CoftTrue() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCard();
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true,false);
        assertEquals(3, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals("XXXX XXXX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2168", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
    }

    @Test
    public void getSavedCardInfoV2CoftFalseSameSuffix() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardWithSameSuffix();
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,false,false);
        assertEquals(4, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals("XXXX XXXX XXXX 0336", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("4761 36XX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("4018 06XX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
        assertEquals("4726 42XX XXXX 2168", savedCardResponse.getSavedCardApiResponses().get(3).getRechargeNumber());
    }

    @Test
    public void getSavedCardInfoV2CoftTrueSameSuffix() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardWithSameSuffix();
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true,false);
        assertEquals(4, savedCardResponse.getSavedCardApiResponses().size());

        assertEquals("XXXX XXXX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2168", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 0336", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(3).getRechargeNumber());

    }

    @Test
    public void getSavedCardInfoV1CoftFalseSameSuffix() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardWithV1SameSuffix();
        when(pgService.getSavedCards(1L, true)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache(Mockito.any())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V1, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,false, false);
        assertEquals(3, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals("4726 42XX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("4761 36XX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("4018 06XX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
    }

    @Test
    public void getSavedCardInfoV1CoftTrue() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardForV1False();
        when(pgService.getSavedCards(1L, true)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V1, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(3, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals("XXXX XXXX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 0355", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
    }

    @Test
    public void getSavedCardInfoV1CoftFalse() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardForV1False();
        when(pgService.getSavedCards(1L, true)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V1, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,false, false);
        assertEquals(3, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals("4726 42XX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("4761 36XX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("4018 06XX XXXX 0355", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
    }

    @Test
    public void getSavedCardInfoV2CoftFalse() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCard();
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,false, false);
        assertEquals(3, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals("4761 36XX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("4726 42XX XXXX 2168", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
    }

    @Test
    public void getSavedCardsWithCardSkinDetails() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardWithSkinDetails();
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setIsCardSkinRequired(true);
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,false, false);
        assertEquals(3, savedCardResponse.getSavedCardApiResponses().size());
        assertNotNull(savedCardResponse.getSavedCardApiResponses().get(1).getColors());
        assertNotNull(savedCardResponse.getSavedCardApiResponses().get(1).getMediaAssets());
        assertNotNull(savedCardResponse.getSavedCardApiResponses().get(0).getColors());
        assertNotNull(savedCardResponse.getSavedCardApiResponses().get(0).getMediaAssets());
        assertNotNull(savedCardResponse.getSavedCardApiResponses().get(2).getColors());
        assertNotNull(savedCardResponse.getSavedCardApiResponses().get(2).getMediaAssets());
        assertEquals("4761 36XX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("4726 42XX XXXX 2168", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
    }

    @Test
    public void getSavedCardInfoForPlatformAndCoft() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardV2DetailsPlatformAndCoft();
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(savedCardDetailsList.size(), savedCardResponse.getSavedCardApiResponses().size());
        List<SavedCardApiResponse> responseList = savedCardResponse.getSavedCardApiResponses();

        for(SavedCardApiResponse card : responseList) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            if(card.getAdditionalInfo().isCardCoft()) {
                Assert.assertNotNull(card.getAdditionalInfo().getPanUniqueReference());
                Assert.assertNotNull(card.getAdditionalInfo().getTin());
            } else {
                Assert.assertNotNull(card.getAdditionalInfo().getCin());
            }
            Assert.assertNotNull(card.getProduct().getProductId());
            Assert.assertEquals(card.getProduct().getCardType(), "CC");
        }
    }

    @Test
    public void getSavedCardInfoForSameplatformAndCoft() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> cinCards = mockSavedCardV2DetailsOnlyPlatform();
        List<SavedCardDetails> coftCards = mockSavedCardV2DetailsOnlyCoft();
        cinCards.get(0).setCardSuffix(coftCards.get(0).getCardSuffix());
        cinCards.get(0).setCardScheme(coftCards.get(0).getCardScheme());
        cinCards.get(0).setIssuingBankName(coftCards.get(0).getIssuingBankName());
        List<SavedCardDetails> savedCardDetailsList = Stream.of(cinCards, coftCards)
                .flatMap(x -> x.stream())
                .collect(Collectors.toList());

        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(1, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals(true, savedCardResponse.getSavedCardApiResponses().get(0).getAdditionalInfo().isCardCoft());
    }

    @Test
    public void getSavedCardInfoForWithAmexCard() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsListCin = mockSavedCardV2DetailsOnlyPlatform();
        savedCardDetailsListCin.get(0).setCardScheme(Constants.AMEX);
        List<SavedCardDetails> savedCardDetailsListCoft = mockSavedCardV2DetailsOnlyCoft();
        savedCardDetailsListCoft.get(0).setCardScheme(Constants.AMEX);

        List<SavedCardDetails> savedCardDetailsList = Stream.of(savedCardDetailsListCin, savedCardDetailsListCoft)
                .flatMap(x -> x.stream())
                .collect(Collectors.toList());

        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        List<CommonCache> commonCaches = new ArrayList<>();
        commonCaches.add(mockCommonCacheForAmex());
        when(commonCacheService.getCache(anyString())).thenReturn(commonCaches);
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(savedCardDetailsList.size(), savedCardResponse.getSavedCardApiResponses().size());
        List<SavedCardApiResponse> responseList = savedCardResponse.getSavedCardApiResponses();

        for(SavedCardApiResponse card : responseList) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            if(card.getAdditionalInfo().isCardCoft()) {
                Assert.assertNotNull(card.getAdditionalInfo().getPanUniqueReference());
                Assert.assertNotNull(card.getAdditionalInfo().getTin());
                assertEquals("XXXX XXXX XXX" +
                                savedCardDetailsListCoft.get(0).getCardSuffix().charAt(0) +
                                " " +
                                savedCardDetailsListCoft.get(0).getCardSuffix().substring(1),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXXXX  X" +
                                savedCardDetailsListCoft.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay());
            } else {
                Assert.assertNotNull(card.getAdditionalInfo().getCin());
                assertEquals("XXXX XXXX XXX" +
                                savedCardDetailsListCin.get(0).getCardSuffix().charAt(0) +
                                " " +
                                savedCardDetailsListCin.get(0).getCardSuffix().substring(1),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXXXX  X" +
                                savedCardDetailsListCin .get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay());
            }
        }
    }

    @Test
    public void getSavedCardInfoForWithDinersCard() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsListCin = mockSavedCardV2DetailsOnlyPlatform();
        savedCardDetailsListCin.get(0).setCardScheme(Constants.DINERS);
        List<SavedCardDetails> savedCardDetailsListCoft = mockSavedCardV2DetailsOnlyCoft();
        savedCardDetailsListCoft.get(0).setCardScheme(Constants.DINERS);

        List<SavedCardDetails> savedCardDetailsList = Stream.of(savedCardDetailsListCin, savedCardDetailsListCoft)
                .flatMap(x -> x.stream())
                .collect(Collectors.toList());

        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);List<CommonCache> commonCaches = new ArrayList<>();
        commonCaches.add(mockCommonCacheForDiners());
        when(commonCacheService.getCache(anyString())).thenReturn(commonCaches);
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(savedCardDetailsList.size(), savedCardResponse.getSavedCardApiResponses().size());
        List<SavedCardApiResponse> responseList = savedCardResponse.getSavedCardApiResponses();

        for(SavedCardApiResponse card : responseList) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            if(card.getAdditionalInfo().isCardCoft()) {
                Assert.assertNotNull(card.getAdditionalInfo().getPanUniqueReference());
                Assert.assertNotNull(card.getAdditionalInfo().getTin());
                assertEquals("XXXX XXXX XX" +
                                savedCardDetailsListCoft.get(0).getCardSuffix().charAt(0) +
                                savedCardDetailsListCoft.get(0).getCardSuffix().charAt(1) +
                                " " +
                                savedCardDetailsListCoft.get(0).getCardSuffix().substring(2),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXXXX  " +
                                savedCardDetailsListCoft.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay());
            } else {
                Assert.assertNotNull(card.getAdditionalInfo().getCin());
                assertEquals("XXXX XXXX XX" +
                                savedCardDetailsListCin.get(0).getCardSuffix().charAt(0) +
                                savedCardDetailsListCin.get(0).getCardSuffix().charAt(1) +
                                " " +
                                savedCardDetailsListCin.get(0).getCardSuffix().substring(2),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXXXX  " +
                                savedCardDetailsListCin.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay());
            }
        }
    }

    @Test
    public void getSavedCardInfoForWithMasterCard() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsListCin = mockSavedCardV2DetailsOnlyPlatform();
        savedCardDetailsListCin.get(0).setCardScheme(Constants.MASTERCARD);
        List<SavedCardDetails> savedCardDetailsListCoft = mockSavedCardV2DetailsOnlyCoft();
        savedCardDetailsListCoft.get(0).setCardScheme(Constants.MASTERCARD);

        List<SavedCardDetails> savedCardDetailsList = Stream.of(savedCardDetailsListCin, savedCardDetailsListCoft)
                .flatMap(x -> x.stream())
                .collect(Collectors.toList());
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);List<CommonCache> commonCaches = new ArrayList<>();
        commonCaches.add(mockCommonCacheForMasterCard());
        when(commonCacheService.getCache(anyString())).thenReturn(commonCaches);
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(savedCardDetailsList.size(), savedCardResponse.getSavedCardApiResponses().size());
        List<SavedCardApiResponse> responseList = savedCardResponse.getSavedCardApiResponses();

        for(SavedCardApiResponse card : responseList) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            if(card.getAdditionalInfo().isCardCoft()) {
                Assert.assertNotNull(card.getAdditionalInfo().getPanUniqueReference());
                Assert.assertNotNull(card.getAdditionalInfo().getTin());
                assertEquals("XXXX XXXX XXXX " +
                                savedCardDetailsListCoft.get(0).getCardSuffix(),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXX  XXXX  " +
                                savedCardDetailsListCoft.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay());
            } else {
                Assert.assertNotNull(card.getAdditionalInfo().getCin());
                assertEquals("XXXX XXXX XXXX " +
                                savedCardDetailsListCin.get(0).getCardSuffix(),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXX  XXXX  " +
                                savedCardDetailsListCin.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay()  );
            }
        }
    }

    @Test
    public void getSavedCardInfoForWithVisa() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsListCin = mockSavedCardV2DetailsOnlyPlatform();
        savedCardDetailsListCin.get(0).setCardScheme(Constants.VISA);
        List<SavedCardDetails> savedCardDetailsListCoft = mockSavedCardV2DetailsOnlyCoft();
        savedCardDetailsListCoft.get(0).setCardScheme(Constants.VISA);

        List<SavedCardDetails> savedCardDetailsList = Stream.of(savedCardDetailsListCin, savedCardDetailsListCoft)
                .flatMap(x -> x.stream())
                .collect(Collectors.toList());
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);List<CommonCache> commonCaches = new ArrayList<>();
        commonCaches.add(mockCommonCacheForVisa());
        when(commonCacheService.getCache(anyString())).thenReturn(commonCaches);
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(savedCardDetailsList.size(), savedCardResponse.getSavedCardApiResponses().size());
        List<SavedCardApiResponse> responseList = savedCardResponse.getSavedCardApiResponses();

        for(SavedCardApiResponse card : responseList) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            if(card.getAdditionalInfo().isCardCoft()) {
                Assert.assertNotNull(card.getAdditionalInfo().getPanUniqueReference());
                Assert.assertNotNull(card.getAdditionalInfo().getTin());
                assertEquals("XXXX XXXX XXXX " +
                                savedCardDetailsListCoft.get(0).getCardSuffix(),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXX  XXXX  " +
                                savedCardDetailsListCoft.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay());
            } else {
                Assert.assertNotNull(card.getAdditionalInfo().getCin());
                assertEquals("XXXX XXXX XXXX " +
                                savedCardDetailsListCin.get(0).getCardSuffix(),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXX  XXXX  " +
                                savedCardDetailsListCin.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay()  );
            }
        }
    }

    @Test
    public void getSavedCardInfoForWithDiscover() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsListCin = mockSavedCardV2DetailsOnlyPlatform();
        savedCardDetailsListCin.get(0).setCardScheme(Constants.DISCOVER);
        List<SavedCardDetails> savedCardDetailsListCoft = mockSavedCardV2DetailsOnlyCoft();
        savedCardDetailsListCoft.get(0).setCardScheme(Constants.DISCOVER);

        List<SavedCardDetails> savedCardDetailsList = Stream.of(savedCardDetailsListCin, savedCardDetailsListCoft)
                .flatMap(x -> x.stream())
                .collect(Collectors.toList());
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);List<CommonCache> commonCaches = new ArrayList<>();
        commonCaches.add(mockCommonCacheForDiscover());
        when(commonCacheService.getCache(anyString())).thenReturn(commonCaches);
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(savedCardDetailsList.size(), savedCardResponse.getSavedCardApiResponses().size());
        List<SavedCardApiResponse> responseList = savedCardResponse.getSavedCardApiResponses();

        for(SavedCardApiResponse card : responseList) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            if(card.getAdditionalInfo().isCardCoft()) {
                Assert.assertNotNull(card.getAdditionalInfo().getPanUniqueReference());
                Assert.assertNotNull(card.getAdditionalInfo().getTin());
                assertEquals("XXXX XXXX XXXX " +
                                savedCardDetailsListCoft.get(0).getCardSuffix(),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXX  XXXX  " +
                                savedCardDetailsListCoft.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay());
            } else {
                Assert.assertNotNull(card.getAdditionalInfo().getCin());
                assertEquals("XXXX XXXX XXXX " +
                                savedCardDetailsListCin.get(0).getCardSuffix(),
                        card.getRechargeNumber());
                assertEquals("XXXX  XXXX  XXXX  " +
                                savedCardDetailsListCin.get(0).getCardSuffix(),
                        card.getRechargeNumberForDisplay()  );
            }
        }
    }

    @Test
    public void getSavedCardInfoPaytmFirst() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardDetailsForPaytmFirst();
        when(pgService.getSavedCards(anyLong(), anyBoolean())).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        assertEquals(savedCardDetailsList.size(), savedCardResponse.getSavedCardApiResponses().size());
        List<SavedCardApiResponse> responseList = savedCardResponse.getSavedCardApiResponses();

        for(SavedCardApiResponse card : responseList) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            if(card.getAdditionalInfo().isCardCoft()) {
                Assert.assertNotNull(card.getAdditionalInfo().getPanUniqueReference());
                Assert.assertNotNull(card.getAdditionalInfo().getTin());
            } else {
                Assert.assertNotNull(card.getAdditionalInfo().getCin());
            }
            Assert.assertNotNull(card.getProduct().getProductId());
            Assert.assertEquals(card.getProduct().getCardType(), "CC");
        }
    }

    @Test
    public void getSavedCardInfoForPaytmFirst() throws PgServiceException, SavedCardServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCardV2DetailsForPaytmFirst();
        when(pgService.getSavedCards(anyLong(), anyBoolean())).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true,false);
        assertEquals(savedCardDetailsList.size(), savedCardResponse.getSavedCardApiResponses().size());
        List<SavedCardApiResponse> responseList = savedCardResponse.getSavedCardApiResponses();
        for (SavedCardDetails savedCardDetails : savedCardDetailsList) {
            String PaytmFirstcard = isPaytmFirstCardTest(savedCardDetails.getDisplayName());
            assertEquals("1",PaytmFirstcard);
        }
        for(SavedCardApiResponse card : responseList) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            Assert.assertNotNull(card.getProduct().getProductId());
            Assert.assertEquals(card.getProduct().getCardType(), "CC");
        }

    }



    @Test
    public void getCachedSavedCardInfo() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(anyLong(), anyBoolean())).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        List<CommonCache> commonCaches = new ArrayList<>();
        commonCaches.add(mockCommonCache());
        when(commonCacheService.getCache(anyString())).thenReturn(commonCaches);
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            Assert.assertNotNull(card.getAdditionalInfo().isCardCoft());
            if(card.getAdditionalInfo().isCardCoft()) {
                Assert.assertNotNull(card.getAdditionalInfo().getPanUniqueReference());
                Assert.assertNotNull(card.getAdditionalInfo().getTin());
            } else {
                Assert.assertNotNull(card.getAdditionalInfo().getCin());
            }
            Assert.assertNotNull(card.getProduct().getProductId());
            Assert.assertEquals(card.getProduct().getCardType(), "CC");
        }
    }

    @Test
    public void getSavedCardInfoWithBillsMIN_DUE() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();

        reminderHistoryList.add(mockReminderHistoryObjectForCinCard());
        reminderHistoryList.add(mockReminderHistoryObjectForCoftCard());
        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCinCard());
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.MIN_DUE, card.getBills().get(0).getBillState());
        }
    }

    @Test
    public void getSavedCardInfoWithFullyPaid() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setAmount(0.0);
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setAmount(0.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCinCard());
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.FULLY_PAID, card.getBills().get(0).getBillState());
        }
    }

    @Test
    public void getSavedCardInfoWithNewUserFullyPaid() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setAmount(0.0);
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setAmount(0.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        //channelHistoryList.add(mockChannelHistoryDataForCoftCard());
        //channelHistoryList.add(mockChannelHistoryDataForCinCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.NO_BILL, card.getBills().get(0).getBillState());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsDUE_DATE_CROSSED() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setDue_date(new Date());
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setDue_date(new Date());
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());
        channelHistoryList.add(mockChannelHistoryDataForCinCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.MIN_DUE, card.getBills().get(0).getBillState());
            if(card.getAdditionalInfo().isCardCoft()) {
                assertEquals(card.getBills().get(0).getAmount(), reminderHistoryForCoftCard.getAmount());
            } else {
                assertEquals(card.getBills().get(0).getAmount(), reminderHistoryForCinCard.getAmount());
            }
        }
    }

    @Test
    public void getSavedCardInfoWithBillsNoBill() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());
        channelHistoryList.add(mockChannelHistoryDataForCinCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);

        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.NO_BILL, card.getBills().get(0).getBillState());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsMarkAsPaid() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setAmount(0.0);
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setAmount(0.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        ChannelHistory historyCinCard = mockChannelHistoryDataForCinCard();
        historyCinCard.setEventType("MARK_AS_PAID");
        ChannelHistory historyCoftCard = mockChannelHistoryDataForCoftCard();
        historyCoftCard.setEventType("MARK_AS_PAID");
        channelHistoryList.add(historyCinCard);
        channelHistoryList.add(historyCoftCard);


        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.MARK_AS_PAID, card.getBills().get(0).getBillState());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsMinDueDateWithCurrentBill() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setOriginalMinBillAmount(10.0);
        reminderHistoryForCinCard.setOriginalAmount(80.0);
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setOriginalMinBillAmount(10.0);
        reminderHistoryForCoftCard.setOriginalAmount(80.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");

        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.MIN_DUE, card.getBills().get(0).getBillState());
            assertEquals(new Double(50.0), card.getBills().get(0).getMin_due_amount());
            assertEquals(new Double(10.0), card.getBills().get(0).getOriginal_min_due_amount());
            assertEquals(new Double(100.0), card.getBills().get(0).getAmount());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsMinDuePaid() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setOriginalMinBillAmount(10.0);
        reminderHistoryForCinCard.setOriginalAmount(80.0);
        reminderHistoryForCinCard.setCurrentMinBillAmount(0.0);

        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setOriginalMinBillAmount(10.0);
        reminderHistoryForCoftCard.setOriginalAmount(80.0);
        reminderHistoryForCoftCard.setCurrentMinBillAmount(0.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        ChannelHistory historyCinCard = mockChannelHistoryDataForCinCard();
        ChannelHistory historyCoftCard = mockChannelHistoryDataForCoftCard();
        channelHistoryList.add(historyCinCard);
        channelHistoryList.add(historyCoftCard);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");

        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.MIN_DUE_PAID, card.getBills().get(0).getBillState());
            assertEquals(new Double(0.0), card.getBills().get(0).getMin_due_amount());
            assertEquals(new Double(10.0), card.getBills().get(0).getOriginal_min_due_amount());
            assertEquals(new Double(100.0), card.getBills().get(0).getAmount());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsNewUserMinDuePaid() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setOriginalMinBillAmount(10.0);
        reminderHistoryForCinCard.setOriginalAmount(80.0);
        reminderHistoryForCinCard.setCurrentMinBillAmount(0.0);

        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setOriginalMinBillAmount(10.0);
        reminderHistoryForCoftCard.setOriginalAmount(80.0);
        reminderHistoryForCoftCard.setCurrentMinBillAmount(0.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");

        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.NEW_USER_MIN_DUE_PAID, card.getBills().get(0).getBillState());
            assertEquals(new Double(0.0), card.getBills().get(0).getMin_due_amount());
            assertEquals(new Double(10.0), card.getBills().get(0).getOriginal_min_due_amount());
            assertEquals(new Double(100.0), card.getBills().get(0).getAmount());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsNoBillNullAmount() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setAmount(null);
        reminderHistoryForCinCard.setOriginalAmount(80.0);
        reminderHistoryForCinCard.setCurrentMinBillAmount(0.0);

        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setAmount(null);
        reminderHistoryForCoftCard.setOriginalAmount(80.0);
        reminderHistoryForCoftCard.setCurrentMinBillAmount(0.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        ChannelHistory historyCinCard = mockChannelHistoryDataForCinCard();
        ChannelHistory historyCoftCard = mockChannelHistoryDataForCoftCard();
        channelHistoryList.add(historyCinCard);
        channelHistoryList.add(historyCoftCard);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");

        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.NO_BILL, card.getBills().get(0).getBillState());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsNoBillNullAmountWithNoReminderData() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setAmount(null);
        reminderHistoryForCinCard.setOriginalAmount(80.0);
        reminderHistoryForCinCard.setCurrentMinBillAmount(0.0);

        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        ChannelHistory historyCinCard = mockChannelHistoryDataForCinCard();
        ChannelHistory historyCoftCard = mockChannelHistoryDataForCoftCard();
        channelHistoryList.add(historyCinCard);
        channelHistoryList.add(historyCoftCard);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");

        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.NO_BILL, card.getBills().get(0).getBillState());
            if(card.getAdditionalInfo().isCardCoft()) {
                assertEquals(historyCoftCard.getOrderId(), card.getBills().get(0).getOrder_id());
            } else {
                assertEquals(historyCinCard.getOrderId(), card.getBills().get(0).getOrder_id());
            }
        }
    }

    @Test
    public void getSavedCardInfoWithBillsMIN_DUEandPaidOutside() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory cinReminder = mockReminderHistoryObjectForCinCard();
        cinReminder.setStatus(14);
        cinReminder.setLastPaidAmount(100.0);
        reminderHistoryList.add(cinReminder);
        ReminderHistory coftReminder = mockReminderHistoryObjectForCoftCard();
        coftReminder.setStatus(14);
        coftReminder.setLastPaidAmount(100.0);
        reminderHistoryList.add(coftReminder);
        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCinCard());
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");


        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE, true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for (SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(card.getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
            assertEquals(card.getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
            assertEquals(SavedCardBillStateEnum.MIN_DUE, card.getBills().get(0).getBillState());
        }
    }

        @Test
        public void getSavedCardInfoWithFullyPaidAndPaidOutside() throws PgServiceException, SavedCardServiceException {
            when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
            when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
            when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

            List<ReminderHistory> reminderHistoryList = new ArrayList<>();
            ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
            reminderHistoryForCinCard.setLastPaidAmount(100.0);
            reminderHistoryForCinCard.setAmount(0.0);
            reminderHistoryForCinCard.setStatus(14);

            ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
            reminderHistoryForCoftCard.setAmount(0.0);
            reminderHistoryForCoftCard.setLastPaidAmount(100.0);
            reminderHistoryForCoftCard.setStatus(14);
            reminderHistoryList.add(reminderHistoryForCinCard);
            reminderHistoryList.add(reminderHistoryForCoftCard);

            List<ChannelHistory> channelHistoryList = new ArrayList<>();
            channelHistoryList.add(mockChannelHistoryDataForCinCard());
            channelHistoryList.add(mockChannelHistoryDataForCoftCard());

            when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                    anyLong(), any(), any()))
                    .thenReturn(reminderHistoryList);


            when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                    anyLong(), any(), anyString()
            )).thenReturn(channelHistoryList);


            SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
            savedCardApiRequest.setType("savedCard");
            savedCardApiRequest.setCardType("CC");
            savedCardApiRequest.setService("credit card");



            SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

            assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
            for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
                assertEquals(card.getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
                assertEquals(card.getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
                assertEquals(SavedCardBillStateEnum.FULLY_PAID, card.getBills().get(0).getBillState());
            }
        }

    @Test
    public void getSavedCardInfoWithBillsDUE_DATE_CROSSEDAndPaidOutside() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setDue_date(new Date());
        reminderHistoryForCinCard.setStatus(14);
        reminderHistoryForCinCard.setLastPaidAmount(100.0);
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setDue_date(new Date());
        reminderHistoryForCoftCard.setStatus(14);
        reminderHistoryForCoftCard.setLastPaidAmount(100.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());
        channelHistoryList.add(mockChannelHistoryDataForCinCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(card.getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
            assertEquals(card.getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
            assertEquals(SavedCardBillStateEnum.MIN_DUE, card.getBills().get(0).getBillState());
            if(card.getAdditionalInfo().isCardCoft()) {
                assertEquals(card.getBills().get(0).getAmount(), reminderHistoryForCoftCard.getAmount());
            } else {
                assertEquals(card.getBills().get(0).getAmount(), reminderHistoryForCinCard.getAmount());
            }
        }
    }


    @Test
    public void getSavedCardInfoWithBillsNoBillAndPaidOutside() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
         ReminderHistory reminderHistory=new ReminderHistory();
        reminderHistory.setReference_id(Cin1);
        reminderHistory.setStatus(14);
        reminderHistory.setLastPaidAmount(100.0);
        reminderHistoryList.add(reminderHistory);
        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());
        channelHistoryList.add(mockChannelHistoryDataForCinCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);

        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            if(card.getPaidOutside()!=null){
                assertEquals(card.getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
                assertEquals(card.getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
            }
            assertEquals(SavedCardBillStateEnum.NO_BILL, card.getBills().get(0).getBillState());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsMarkAsPaidAndPaidOutside() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setAmount(0.0);
        reminderHistoryForCinCard.setStatus(14);
        reminderHistoryForCinCard.setLastPaidAmount(100.0);
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setAmount(0.0);
        reminderHistoryForCoftCard.setStatus(14);
        reminderHistoryForCoftCard.setLastPaidAmount(100.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        ChannelHistory historyCinCard = mockChannelHistoryDataForCinCard();
        historyCinCard.setEventType("MARK_AS_PAID");
        ChannelHistory historyCoftCard = mockChannelHistoryDataForCoftCard();
        historyCoftCard.setEventType("MARK_AS_PAID");
        channelHistoryList.add(historyCinCard);
        channelHistoryList.add(historyCoftCard);


        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(card.getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
            assertEquals(card.getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
            assertEquals(SavedCardBillStateEnum.MARK_AS_PAID, card.getBills().get(0).getBillState());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsMarkAsPaidAndPaidOutsideCase2() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setAmount(0.0);
        reminderHistoryForCinCard.setStatus(15);
        reminderHistoryForCinCard.setLastPaidAmount(100.0);
        reminderHistoryList.add(reminderHistoryForCinCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        ChannelHistory historyCinCard = mockChannelHistoryDataForCinCard();
        historyCinCard.setEventType("MARK_AS_PAID");
        ChannelHistory historyCoftCard = mockChannelHistoryDataForCoftCard();
        historyCoftCard.setEventType("MARK_AS_PAID");
        channelHistoryList.add(historyCinCard);
        channelHistoryList.add(historyCoftCard);


        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.MARK_AS_PAID, card.getBills().get(0).getBillState());
            assertNotNull(card.getBills().get(0).getMark_as_paid_time());
        }
    }

    @Test
    public void getSavedCardInfoWithBillsMinDueDateWithCurrentBillAndPaidOutside() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setOriginalMinBillAmount(10.0);
        reminderHistoryForCinCard.setOriginalAmount(80.0);
        reminderHistoryForCinCard.setStatus(14);
        reminderHistoryForCinCard.setLastPaidAmount(100.0);
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setOriginalMinBillAmount(10.0);
        reminderHistoryForCoftCard.setOriginalAmount(80.0);
        reminderHistoryForCoftCard.setStatus(14);
        reminderHistoryForCoftCard.setLastPaidAmount(100.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");

        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(card.getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
            assertEquals(card.getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
            assertEquals(SavedCardBillStateEnum.MIN_DUE, card.getBills().get(0).getBillState());
            assertEquals(new Double(50.0), card.getBills().get(0).getMin_due_amount());
            assertEquals(new Double(10.0), card.getBills().get(0).getOriginal_min_due_amount());
            assertEquals(new Double(100.0), card.getBills().get(0).getAmount());
        }
    }


    @Test
    public void getSavedCardInfoWithBillsNoBillNullAmountAndPaidOutside() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setAmount(null);
        reminderHistoryForCinCard.setOriginalAmount(80.0);
        reminderHistoryForCinCard.setCurrentMinBillAmount(0.0);
        reminderHistoryForCinCard.setStatus(14);
        reminderHistoryForCinCard.setLastPaidAmount(100.0);
        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setAmount(null);
        reminderHistoryForCoftCard.setOriginalAmount(80.0);
        reminderHistoryForCoftCard.setCurrentMinBillAmount(0.0);
        reminderHistoryForCoftCard.setStatus(14);
        reminderHistoryForCoftCard.setLastPaidAmount(100.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        ChannelHistory historyCinCard = mockChannelHistoryDataForCinCard();
        ChannelHistory historyCoftCard = mockChannelHistoryDataForCoftCard();
        channelHistoryList.add(historyCinCard);
        channelHistoryList.add(historyCoftCard);

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");

        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.NO_BILL, card.getBills().get(0).getBillState());
            assertEquals(card.getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
        }
    }


    @Test
    public void getSavedCardInfoWithFullyPaidPaidOutsideAndOneisMinimumPaidOutside() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setLastPaidAmount(100.0);
        reminderHistoryForCinCard.setAmount(0.0);
        reminderHistoryForCinCard.setStatus(14);

        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setLastPaidAmount(100.0);
        reminderHistoryForCoftCard.setStatus(14);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCinCard());
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());

        assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
        assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
        assertEquals(SavedCardBillStateEnum.FULLY_PAID, savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());


        assertEquals(savedCardResponse.getSavedCardApiResponses().get(1).getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
        assertEquals(savedCardResponse.getSavedCardApiResponses().get(1).getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
        assertEquals(SavedCardBillStateEnum.MIN_DUE, savedCardResponse.getSavedCardApiResponses().get(1).getBills().get(0).getBillState());


    }


    @Test
    public void getSavedCardInfoWithFullyPaidPaidOutsideAndOneisMinimumNotPaidOutside() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory reminderHistoryForCinCard = mockReminderHistoryObjectForCinCard();
        reminderHistoryForCinCard.setLastPaidAmount(100.0);
        reminderHistoryForCinCard.setAmount(0.0);
        reminderHistoryForCinCard.setStatus(14);

        ReminderHistory reminderHistoryForCoftCard = mockReminderHistoryObjectForCoftCard();
        reminderHistoryForCoftCard.setLastPaidAmount(100.0);
        reminderHistoryList.add(reminderHistoryForCinCard);
        reminderHistoryList.add(reminderHistoryForCoftCard);

        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCinCard());
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());

        assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
        assertEquals(savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getLast_paid_amount(),Double.valueOf(100.0));
        assertEquals(SavedCardBillStateEnum.FULLY_PAID, savedCardResponse.getSavedCardApiResponses().get(0).getBills().get(0).getBillState());


        assertNotEquals(savedCardResponse.getSavedCardApiResponses().get(1).getPaidOutside(), Constants.PAID_OUTSIDE_PAYTM);
        assertEquals(SavedCardBillStateEnum.MIN_DUE, savedCardResponse.getSavedCardApiResponses().get(1).getBills().get(0).getBillState());


    }
    @Test
    public void getSavedCardInfoWhenOnlyActiveBillsTrue() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCard();
        when(pgService.getSavedCards(1L, true)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V1, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE, true, true);
        assertEquals(0, savedCardResponse.getSavedCardApiResponses().size());
    }

    @Test
    public void getSavedCardInfoWhenOnlyActiveBillsFalse() throws SavedCardServiceException, PgServiceException {
        List<SavedCardDetails> savedCardDetailsList = mockSavedCard();
        when(pgService.getSavedCards(1L, false)).thenReturn(savedCardDetailsList);
        when(commonCacheService.getCache("0:financial services:citi:visa")).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponseNew());
        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setService("credit card");
        savedCardApiRequest.setCardType("CC");
        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE, true, false);
        assertEquals(3, savedCardResponse.getSavedCardApiResponses().size());
        assertEquals("XXXX XXXX XXXX 3216", savedCardResponse.getSavedCardApiResponses().get(0).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2168", savedCardResponse.getSavedCardApiResponses().get(1).getRechargeNumber());
        assertEquals("XXXX XXXX XXXX 2167", savedCardResponse.getSavedCardApiResponses().get(2).getRechargeNumber());
    }
    @Test
    public void getSavedCardInfoWithBillsCOMPLETE_DUE() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory rem1 = mockReminderHistoryObjectForCinCard();
        rem1.setCurrentMinBillAmount(null);
        ReminderHistory rem2 = mockReminderHistoryObjectForCoftCard();
        rem2.setCurrentMinBillAmount(null);
        reminderHistoryList.add(rem1);
        reminderHistoryList.add(rem2);
        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCinCard());
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertEquals(SavedCardBillStateEnum.COMPLETE_DUE, card.getBills().get(0).getBillState());
        }
    }
    @Test
    public void getSavedCardInfoWithBillsCOMPLETE_DUE2() throws PgServiceException, SavedCardServiceException {
        when(pgService.getSavedCards(1L, false)).thenReturn(mockSavedCardV2DetailsPlatformAndCoft());
        when(commonCacheService.getCache(anyString())).thenReturn(new ArrayList<>());
        when(dcatService.hitGetCategoryApi(Constants.FINANCIAL_SERVICES)).thenReturn(mockCategoryAPIResponse());

        List<ReminderHistory> reminderHistoryList = new ArrayList<>();
        ReminderHistory rem1 = mockReminderHistoryObjectForCinCard();
        rem1.setCurrentMinBillAmount(null);
        rem1.setAmount(0.0);
        ReminderHistory rem2 = mockReminderHistoryObjectForCoftCard();
        rem2.setCurrentMinBillAmount(null);
        rem2.setAmount(0.0);
        reminderHistoryList.add(rem1);
        reminderHistoryList.add(rem2);
        List<ChannelHistory> channelHistoryList = new ArrayList<>();
        channelHistoryList.add(mockChannelHistoryDataForCinCard());
        channelHistoryList.add(mockChannelHistoryDataForCoftCard());

        when(reminderHistoryService.findByCustomerIdAndRechargeNumberInAndServiceIn(
                anyLong(), any(), any()))
                .thenReturn(reminderHistoryList);


        when(channelHistoryRepository.findByCustomerIdAndRechargeNumberInAndService(
                anyLong(), any(), anyString()
        )).thenReturn(channelHistoryList);


        SavedCardApiRequest savedCardApiRequest = new SavedCardApiRequest();
        savedCardApiRequest.setType("savedCard");
        savedCardApiRequest.setCardType("CC");
        savedCardApiRequest.setService("credit card");



        SavedCardResponse savedCardResponse = savedCardService.getSavedCards(1L, savedCardApiRequest, Constants.API_VERISON_V2, Constants.SAVED_CARDS_BILLS_APPEND_FLAG_TRUE,true, false);

        assertEquals(2, savedCardResponse.getSavedCardApiResponses().size());
        for(SavedCardApiResponse card : savedCardResponse.getSavedCardApiResponses()) {
            assertNotEquals(SavedCardBillStateEnum.COMPLETE_DUE, card.getBills().get(0).getBillState());
        }
    }
}

