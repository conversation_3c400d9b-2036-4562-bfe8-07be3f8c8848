package com.paytm.saga;

import org.mockito.ArgumentMatcher;

import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.model.DropOff;
import com.paytm.saga.model.ReminderHistory;

public class CardInfoDtoMatcher implements ArgumentMatcher<CardInfoDto> {

	public CardInfoDtoMatcher(CardInfoDto cardInfoDTO) {
		this.cardInfoDTO = cardInfoDTO;
	}

	private CardInfoDto cardInfoDTO;

	@Override
	public boolean matches(CardInfoDto obj) {
		if (cardInfoDTO != null && obj != null) {
			if (cardInfoDTO.getDropOff() != null && obj.getDropOff() != null) {
				DropOff dropOff1 = cardInfoDTO.getDropOff();
				DropOff dropOff2 = obj.getDropOff();
				return dropOff1.getOperator().equals(dropOff2.getOperator())
						&& dropOff1.getService().equals(dropOff2.getService())
						&& dropOff1.getRechargeNumber().equals(dropOff2.getRechargeNumber())
						&& dropOff1.getAmount().equals(dropOff2.getAmount())
						&& dropOff1.getEventType().equals(dropOff2.getEventType())
						&& obj.isPreviousTxnHistory() == cardInfoDTO.isPreviousTxnHistory()
						&& obj.isIgnoreCTA() == cardInfoDTO.isIgnoreCTA();
			} else if (cardInfoDTO.getReminderHistory() != null && obj.getReminderHistory() != null) {
				ReminderHistory reminderHistory1 = cardInfoDTO.getReminderHistory();
				ReminderHistory reminderHistory2 = obj.getReminderHistory();

				// private String client;
				// private String version;

				return reminderHistory1.getOperator().equals(reminderHistory2.getOperator())
						&& reminderHistory1.getService().equals(reminderHistory2.getService())
						&& reminderHistory1.getRechargeNumber().equals(reminderHistory2.getRechargeNumber())
						&& reminderHistory1.getAmount().equals(reminderHistory2.getAmount())
						&& obj.isPreviousTxnHistory() == cardInfoDTO.isPreviousTxnHistory()
						&& obj.isIgnoreCTA() == cardInfoDTO.isIgnoreCTA();

			}
		}
		return false;
	}
}
