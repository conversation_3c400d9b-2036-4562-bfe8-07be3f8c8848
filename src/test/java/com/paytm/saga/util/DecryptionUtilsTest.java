package com.paytm.saga.util;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.service.RecentService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class DecryptionUtilsTest {


    @Before
    public void setup() {
        String config = "{\"billerDecryptionKey\":\"Zi5F0boOKlfZiq0Bs4I7CzY+Y+FOh7OXS2O3paEITTg=\",\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"newAccountServices\": [\"mobile\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        MockitoAnnotations.initMocks(this);

    }



    @Test
    public void testGetEncryptedText(){
        assertEquals(DecryptionUtil.getEncryptedText("1234"),"6077D2D29D675720E533913308C89EB2");
    }
    @Test
    public void testGetDecryptedText(){
        assertEquals(DecryptionUtil.getDecryptedText("03376a33d241a3f364b55ab95dd9ad90"),"**********");

    }


}
