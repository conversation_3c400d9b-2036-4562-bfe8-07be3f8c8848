package com.paytm.saga.util;

import com.paytm.saga.common.configuration.property.AES256Configurations;
import com.paytm.saga.common.exception.AES256Exception;
import com.paytm.saga.common.metrics.MetricsHelper;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import static com.paytm.saga.common.constant.EncryptionConstants.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class AESUtilTest {

	private AESUtil aesUtil;
	private AES256Configurations aes256Configurations;
	private MetricsHelper metricsHelper;

	@Rule
	public ExpectedException thrown = ExpectedException.none();

	@Before
	public void setUp() {
		aes256Configurations = mock(AES256Configurations.class);
		metricsHelper = mock(MetricsHelper.class);
		aesUtil = new AESUtil(aes256Configurations, metricsHelper);
	}

	@Test
	public void testEncrypt() throws AES256Exception {
		String text = "Hello, Spring Boot!";
		when(aes256Configurations.getEncryptionPassword()).thenReturn("vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3");
		when(aes256Configurations.getEncryptionIvHex()).thenReturn("68e232d2639555a0cf08aaed9d50a025");
		String encryptedText = aesUtil.encrypt(text);
		assertNotNull(encryptedText);
		assertNotEquals(text, encryptedText);
	}

	@Test
	public void testDecrypt() throws AES256Exception {
		String text = "Hello, Spring Boot!";
		when(aes256Configurations.getEncryptionPassword()).thenReturn("vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3");
		when(aes256Configurations.getEncryptionIvHex()).thenReturn("68e232d2639555a0cf08aaed9d50a025");
		String encryptedText = aesUtil.encrypt(text);
		String decryptedText = aesUtil.decrypt(encryptedText);
		assertNotNull(decryptedText);
		assertEquals(text, decryptedText);
	}

	@Test
	public void testEncryptWithException() throws AES256Exception {
		thrown.expect(AES256Exception.class);
		thrown.expectMessage("Error in encrypting the request");

		when(aes256Configurations.getEncryptionPassword()).thenReturn(null);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(null);

		aesUtil.encrypt("hello world");
		verify(metricsHelper).pushToDD(AES256_ERROR, ENCRYPTION);
	}

	@Test
	public void testDecryptWithException() throws AES256Exception {
		thrown.expect(AES256Exception.class);
		thrown.expectMessage("Error in decrypting the request");

		when(aes256Configurations.getEncryptionPassword()).thenReturn(null);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(null);

		aesUtil.decrypt("decrypted text");
		verify(metricsHelper).pushToDD(AES256_ERROR, DECRYPTION);
	}

	@Test
	public void testStaticEncrypt() {
		String text = "Hello, Spring Boot!";
		String key = "vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3";
		String ivHex = "68e232d2639555a0cf08aaed9d50a025";

		when(aes256Configurations.getEncryptionPassword()).thenReturn(null);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(null);

		String encryptedText = AESUtil.encrypt(text, key, ivHex);
		assertNotNull(encryptedText);
		assertNotEquals(text, encryptedText);
	}

	@Test
	public void testStaticDecrypt() {
		String text = "Hello, Spring Boot!";
		String key = "vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3";
		String ivHex = "68e232d2639555a0cf08aaed9d50a025";

		when(aes256Configurations.getEncryptionPassword()).thenReturn(null);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(null);

		String encryptedText = AESUtil.encrypt(text, key, ivHex);
		String decryptedText = AESUtil.decrypt(encryptedText, key, ivHex);
		assertNotNull(decryptedText);
		assertEquals(text, decryptedText);
	}
}