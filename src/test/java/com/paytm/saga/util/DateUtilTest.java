package com.paytm.saga.util;

import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Calendar;
import java.util.Date;

@RunWith(MockitoJUnitRunner.class)
public class DateUtilTest extends TestCase {

    @Test
    public void isDateOlderThanXDays_ReturnsFalse_WhenDateIsNull() {
        assertFalse(DateUtil.isDateOlderThanXDays(null, 10));
    }

    @Test
    public void isDateOlderThanXDays_ReturnsFalse_WhenDateIsNotOlder() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, -5);
        Date date = cal.getTime();
        assertFalse(DateUtil.isDateOlderThanXDays(date, 10));
    }

    @Test
    public void isDateOlderThanXDays_ReturnsTrue_WhenDateIsOlder() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, -15);
        Date date = cal.getTime();
        assertTrue(DateUtil.isDateOlderThanXDays(date, 10));
    }

   @Test
public void isDateOlderThanXDays_ReturnsFalse_WhenDateIsExactlyXDaysOld() {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DAY_OF_YEAR, -10);
    Date date = cal.getTime();
    Date zeroTimeDate = DateUtil.getZeroTimeDate(date);
    assertFalse(DateUtil.isDateOlderThanXDays(zeroTimeDate, 10));
}

    @Test
    public void testGetLatestDate_BothDatesNull() {
        assertNull(DateUtil.getLatestDate(null, null));
    }

    @Test
    public void testGetLatestDate_FirstDateNull() {
        Date date2 = new Date();
        assertEquals(date2, DateUtil.getLatestDate(null, date2));
    }

    @Test
    public void testGetLatestDate_SecondDateNull() {
        Date date1 = new Date();
        assertEquals(date1, DateUtil.getLatestDate(date1, null));
    }

    @Test
    public void testGetLatestDate_BothDatesNonNull() {
        Date date1 = new Date();
        Date date2 = new Date(date1.getTime() - 1000); // date2 is 1 second before date1
        assertEquals(date1, DateUtil.getLatestDate(date1, date2));
    }

    @Test
    public void testGetLatestDate_BothDatesEqual() {
        Date date1 = new Date();
        Date date2 = new Date(date1.getTime());
        assertEquals(date1, DateUtil.getLatestDate(date1, date2));
    }
}