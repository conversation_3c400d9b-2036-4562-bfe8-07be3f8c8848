package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.enums.EventState;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import static com.paytm.saga.common.constant.Constants.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class RecentUtilsRentFlowTest {

    private static final String DATE_FORMAT = Constants.DATE_FORMAT;
    private static final String TXN_TIME = "txn_time";
    private static final String TXN_ID = "txn_id";
    private static final String TXN_AMOUNT = "txn_amount";
    private static final String CHANNEL_ID = "channel_id";

    private Recents recent;
    private SimpleDateFormat dateFormat;


    @Before
    public void setUp() {
        recent = new Recents();
        recent.setKey(new RecentsPrimaryKey());
        dateFormat = new SimpleDateFormat(DATE_FORMAT);
    }

    @Test
    public void testGetFinalState_RechargeFailure() {
        // Given
        Recents recent = new Recents();
        recent.setRecoEvent(false);
        RecentsPrimaryKey primaryKey=new RecentsPrimaryKey();
        primaryKey.setService(RENT_PAYMENT);
        recent.setKey(primaryKey);

        JSONObject failedTxn = new JSONObject();
        failedTxn.put(TXN_TIME, new SimpleDateFormat(DATE_FORMAT).format(new Date()));
        failedTxn.put(TXN_ID, 12345L);
        failedTxn.put(TXN_AMOUNT, 100.0);
        failedTxn.put(CHANNEL_ID, "APP");
        Map extras = RecentUtils.getExtras(recent);
        recent.setLastFailureTxn(failedTxn.toString());

        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_FAILURE_CONSENT_PENDING, result);
    }

    @Test
    public void testGetFinalState_AutomaticRechargeFailure() {
        // Given
        Recents recent = new Recents();
        recent.setRecoEvent(false);

        JSONObject failedTxn = new JSONObject();
        failedTxn.put(TXN_TIME, new SimpleDateFormat(DATE_FORMAT).format(new Date()));
        failedTxn.put(TXN_ID, 12345L);
        failedTxn.put(TXN_AMOUNT, 100.0);
        failedTxn.put(CHANNEL_ID, ReminderConstants.AUTOMATIC_CHANNEL);

        recent.setLastFailureTxn(failedTxn.toString());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_AUTOMATIC_FAILURE, result);
    }

    @Test
    public void testGetFinalState_RechargePending() {
        // Given
        Recents recent = new Recents();
        recent.setRecoEvent(false);
        RecentsPrimaryKey primaryKey=new RecentsPrimaryKey();
        primaryKey.setService(RENT_PAYMENT);
        recent.setKey(primaryKey);
        JSONObject pendingTxn = new JSONObject();
        pendingTxn.put(TXN_TIME, new SimpleDateFormat(DATE_FORMAT).format(new Date()));
        pendingTxn.put(TXN_ID, 12345L);
        pendingTxn.put(TXN_AMOUNT, 100.0);
        pendingTxn.put(CHANNEL_ID, "APP");

        recent.setLastPendingTxn(pendingTxn.toString());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_PENDING_CONSENT_PENDING, result);
    }

    @Test
    public void testGetFinalState_AutomaticRechargePending() {
        // Given
        Recents recent = new Recents();
        recent.setRecoEvent(false);

        JSONObject pendingTxn = new JSONObject();
        pendingTxn.put(TXN_TIME, new SimpleDateFormat(DATE_FORMAT).format(new Date()));
        pendingTxn.put(TXN_ID, 12345L);
        pendingTxn.put(TXN_AMOUNT, 100.0);
        pendingTxn.put(CHANNEL_ID, ReminderConstants.AUTOMATIC_CHANNEL);

        recent.setLastPendingTxn(pendingTxn.toString());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_AUTOMATIC_PENDING, result);
    }

    @Test
    public void testGetFinalState_NewAccountBrowsePlan() {
        // Given
        Recents recent = new Recents();
        recent.setRecoEvent(false);
        recent.setPayType(CommonConstants.PREPAID_PAYTYPE);

        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService(SERVICE_MOBILE);
        recent.setKey(key);

        recent.setDueAmount(0.0);
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.NEW_ACCOUNT_BROWSE_PLAN, result);
    }

    @Test
    public void testGetFinalState_NewAccount() {
        // Given
        Recents recent = new Recents();
        recent.setIsNewBiller(true);
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        key.setService("mobile");
        recent.setKey(key);
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.NEW_ACCOUNT, result);
    }

    @Test
    public void testGetFinalState_NewAccountNoConsent() {
        // Given
        Recents recent = new Recents();
        recent.setIsNewBiller(true);
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        key.setService("rent payment");
        recent.setKey(key);
        recent.setRentConsent(2); // REJECTED
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.NEW_ACCOUNT_NO_CONSENT, result);
    }

    @Test
    public void testGetFinalState_NewAccountConsentNotReceived() {
        // Given
        Recents recent = new Recents();
        recent.setIsNewBiller(true);
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        key.setService("rent payment");
        recent.setKey(key);
        recent.setRentConsent(4); // REJECTED
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.NEW_ACCOUNT_CONSENT_NOTRECEIVED, result);
    }

    @Test
    public void testGetFinalState_NewAccountConsentNoInfo() {
        // Given
        Recents recent = new Recents();
        recent.setIsNewBiller(true);
        RecentsPrimaryKey key=new RecentsPrimaryKey();
        key.setService("rent payment");
        recent.setKey(key);
        recent.setRentConsent(0); // DEFAULT
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.NEW_ACCOUNT, result);
    }

    @Test
    public void testGetFinalState_RechargeSuccess() {
        // Given
        Recents recent = new Recents();
        recent.setRecoEvent(true);
        recent.setOrderId(12345L);
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_SUCCESS, result);
    }

    @Test
    public void testGetFinalState_AutomaticFailure() {
        // Given
        recent.setRecoEvent(false);
        JSONObject failedTxn = createTransactionJson(true);
        recent.setLastFailureTxn(failedTxn.toString());
        recent.setTxnTime(new Date());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_AUTOMATIC_FAILURE, result);
    }

    @Test
    public void testGetFinalState_AutomaticPending() {
        // Given
        recent.setRecoEvent(false);
        JSONObject pendingTxn = createTransactionJson(true);
        recent.setLastPendingTxn(pendingTxn.toString());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_AUTOMATIC_PENDING, result);
    }

    @Test
    public void testGetFinalState_RentPaymentFailureNoConsent() {
        // Given
        recent.setRecoEvent(false);
        recent.getKey().setService("rent payment");
        recent.setRentConsent(2); // REJECTED

        JSONObject failedTxn = createTransactionJson(false);
        recent.setLastFailureTxn(failedTxn.toString());
        recent.setTxnTime(new Date());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_FAILURE_NO_CONSENT, result);
    }

    @Test
    public void testGetFinalState_RentPaymentFailureConsentNotReceived() {
        // Given
        recent.setRecoEvent(false);
        recent.getKey().setService("rent payment");
        recent.setRentConsent(4); // REJECTED

        JSONObject failedTxn = createTransactionJson(false);
        recent.setLastFailureTxn(failedTxn.toString());
        recent.setTxnTime(new Date());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_FAILURE_CONSENT_NOTRECEIVED, result);
    }

    @Test
    public void testGetFinalState_RentPaymentFailureConsentPending() {
        // Given
        recent.setRecoEvent(false);
        recent.getKey().setService("rent payment");
        recent.setRentConsent(3); // REJECTED

        JSONObject failedTxn = createTransactionJson(false);
        recent.setLastFailureTxn(failedTxn.toString());
        recent.setTxnTime(new Date());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_FAILURE_CONSENT_PENDING, result);
    }

    @Test
    public void testGetFinalState_RentPaymentPendingConsentPending() {
        // Given
        recent.setRecoEvent(false);
        recent.getKey().setService("rent payment");
        recent.setRentConsent(3); // NOT_RECEIVED

        JSONObject pendingTxn = createTransactionJson(false);
        recent.setLastPendingTxn(pendingTxn.toString());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_PENDING_CONSENT_PENDING, result);
    }

    @Test
    public void testGetFinalState_RentPaymentPendingConsentNotRecieved() {
        // Given
        recent.setRecoEvent(false);
        recent.getKey().setService("rent payment");
        recent.setRentConsent(4); // NOT_RECEIVED

        JSONObject pendingTxn = createTransactionJson(false);
        recent.setLastPendingTxn(pendingTxn.toString());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_PENDING_CONSENT_NOTRECEIVED, result);
    }


    @Test
    public void testGetFinalState_NewAccountBrowsePlanCase2() {
        // Given
        recent.setRecoEvent(false);
        recent.getKey().setService("MOBILE");
        recent.setPayType("PREPAID");
        recent.setDueAmount(0.0);
        recent.setIsNewBiller(true);
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.NEW_ACCOUNT_BROWSE_PLAN, result);
    }

    @Test
    public void testGetFinalState_NewAccountCase2() {
        // Given
        recent.setIsNewBiller(true);
        recent.setOrderId(null);
        recent.setLastPendingTxn(null);
        recent.setLastFailureTxn(null);
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.NEW_ACCOUNT, result);
    }

    @Test
    public void testGetFinalState_RechargeSuccessCase2() {
        // Given
        recent.setRecoEvent(true);
        recent.setOrderId(12345L);
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_SUCCESS, result);
    }

    @Test
    public void testGetFinalState_RegularFailure() {
        // Given
        recent.setRecoEvent(false);
        JSONObject failedTxn = createTransactionJson(false);
        recent.setLastFailureTxn(failedTxn.toString());
        recent.setTxnTime(new Date());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_FAILURE, result);
    }

    @Test
    public void testGetFinalState_RegularPending() {
        // Given
        recent.setRecoEvent(false);
        JSONObject pendingTxn = createTransactionJson(false);
        recent.setLastPendingTxn(pendingTxn.toString());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_PENDING, result);
    }

    @Test
    public void testGetFinalState_WithBillDue() {
        // Given
        recent.setRecoEvent(false);
        recent.setDueAmount(100.0);
        Date futureDate = new Date(System.currentTimeMillis() + 86400000); // tomorrow
        recent.setDueDate(futureDate);

        JSONObject pendingTxn = createTransactionJson(false);
        recent.setLastPendingTxn(pendingTxn.toString());
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.RECHARGE_PENDING, result);
    }

    @Test(expected = Exception.class)
    public void testGetFinalState_InvalidTransactionJson() {
        // Given
        recent.setRecoEvent(false);
        recent.setLastFailureTxn("invalid json");
        Map extras = RecentUtils.getExtras(recent);
        // When
        EventState result = RecentUtils.getFinalState(recent,extras);

        // Then
        assertEquals(EventState.MARK_AS_PAID, result);
    }

    private JSONObject createTransactionJson(boolean isAutomatic) {
        JSONObject txn = new JSONObject();
        txn.put(TXN_TIME, dateFormat.format(new Date()));
        txn.put(TXN_ID, 12345L);
        txn.put(TXN_AMOUNT, 100.0);
        if (isAutomatic) {
            txn.put(CHANNEL_ID, ReminderConstants.AUTOMATIC_CHANNEL);
        }
        return txn;
    }
}