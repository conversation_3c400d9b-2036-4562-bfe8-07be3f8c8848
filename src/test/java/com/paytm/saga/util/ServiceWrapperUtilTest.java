package com.paytm.saga.util;

import com.paytm.recharges.custom_logger.service.ServiceWrapper;
import com.paytm.saga.dto.OMSItemAttributeModel;
import com.paytm.saga.dto.OMSItemModel;
import com.paytm.saga.dto.OMSItemProductModel;
import com.paytm.saga.dto.OMSResponseModel;
import com.paytm.saga.dto.ReminderDataResponseModel;
import com.paytm.saga.dto.ReminderResponseModel;
import com.paytm.saga.dto.catalogue.Product;
import com.paytm.saga.dto.cdc.NonPaytmAfter;
import com.paytm.saga.dto.cdc.NonPaytmCDC;
import com.paytm.saga.dto.cdc.ReminderAfter;
import com.paytm.saga.dto.cdc.ReminderCDC;
import com.paytm.saga.dto.cdc.StringEntity;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class ServiceWrapperUtilTest {

	private static final String TEST_SERVICE = "testService";

	@Test
	public void testFindServiceWrapper_NullInput() {
		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(null);
		assertNotNull(result);
		assertNull(result.getService());
	}

	@Test
	public void testFindServiceWrapper_StringInput() {
		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(TEST_SERVICE);
		assertNotNull(result);
		assertEquals(TEST_SERVICE, result.getService());
	}

	@Test
	public void testFindServiceWrapper_Recents() {
		// Test null Recents
		ServiceWrapper nullResult = ServiceWrapperUtil.findServiceWrapper(new Recents());
		assertNotNull(nullResult);
		assertNull(nullResult.getService());

		// Test valid Recents
		Recents recents = new Recents();
		recents.setKey(new RecentsPrimaryKey());
		recents.getKey().setService(TEST_SERVICE);
		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(recents);
		assertNotNull(result);
		assertEquals(TEST_SERVICE, result.getService());
	}

	@Test
	public void testFindServiceWrapper_ReminderResponseModel() {
		// Test null ReminderResponseModel
		ServiceWrapper nullResult = ServiceWrapperUtil.findServiceWrapper(new ReminderResponseModel());
		assertNotNull(nullResult);
		assertNull(nullResult.getService());

		// Test valid ReminderResponseModel
		ReminderResponseModel model = new ReminderResponseModel();
		model.setData(new ReminderDataResponseModel());
		model.getData().setService(TEST_SERVICE);

		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(model);
		assertNotNull(result);
		assertEquals(TEST_SERVICE, result.getService());
	}

	@Test
	public void testFindServiceWrapper_ReminderCDC() {
		// Test null ReminderCDC
		ServiceWrapper nullResult = ServiceWrapperUtil.findServiceWrapper(new ReminderCDC());
		assertNotNull(nullResult);
		assertNull(nullResult.getService());

		// Test valid ReminderCDC
		ReminderCDC reminderCDC = new ReminderCDC();
		reminderCDC.setAfter(new ReminderAfter());
		reminderCDC.getAfter().setService(new StringEntity(TEST_SERVICE));
		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(reminderCDC);
		assertNotNull(result);
		assertEquals(TEST_SERVICE, result.getService());
	}

	@Test
	public void testFindServiceWrapper_OMSItemModel() {
		// Test null OMSItemModel
		ServiceWrapper nullResult = ServiceWrapperUtil.findServiceWrapper(new OMSItemModel());
		assertNotNull(nullResult);
		assertNull(nullResult.getService());

		// Test valid OMSItemModel
		OMSItemModel omsItemModel = new OMSItemModel();
		omsItemModel.setProduct(new OMSItemProductModel());
		omsItemModel.getProduct().setAttributes(new OMSItemAttributeModel());
		omsItemModel.getProduct().getAttributes().setService(TEST_SERVICE);

		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(omsItemModel);
		assertNotNull(result);
		assertEquals(TEST_SERVICE, result.getService());
	}

	@Test
	public void testFindServiceWrapper_NonPaytmCDC() {
		// Test null NonPaytmCDC
		ServiceWrapper nullResult = ServiceWrapperUtil.findServiceWrapper(new NonPaytmCDC());
		assertNotNull(nullResult);
		assertNull(nullResult.getService());

		// Test valid NonPaytmCDC
		NonPaytmCDC nonPaytmCDC = new NonPaytmCDC();
		nonPaytmCDC.setAfter(new NonPaytmAfter());
		nonPaytmCDC.getAfter().setService(new StringEntity(TEST_SERVICE));

		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(nonPaytmCDC);
		assertNotNull(result);
		assertEquals(TEST_SERVICE, result.getService());
	}

	@Test
	public void testFindServiceWrapper_ReminderHistory() {
		// Test null ReminderHistory
		ServiceWrapper nullResult = ServiceWrapperUtil.findServiceWrapper(new ReminderHistory());
		assertNotNull(nullResult);
		assertNull(nullResult.getService());

		// Test valid ReminderHistory
		ReminderHistory reminderHistory = new ReminderHistory();
		reminderHistory.setService(TEST_SERVICE);

		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(reminderHistory);
		assertNotNull(result);
		assertEquals(TEST_SERVICE, result.getService());
	}

	@Test
	public void testFindServiceWrapper_UnsupportedType() {
		// Test with an unsupported object type
		ServiceWrapper result = ServiceWrapperUtil.findServiceWrapper(new Object());
		assertNotNull(result);
		assertNull(result.getService());
	}

}