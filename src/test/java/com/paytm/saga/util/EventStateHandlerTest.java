package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.enums.EventState;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class EventStateHandlerTest {

    @Before
    public void setUp() {
        String config = "{\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":-5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"newAccountServices\": [\"mobile\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }
    @Test
    public void testBillMarkAsPaid() {
        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.minusHours(2)));
        assertEquals(EventState.MARK_AS_PAID,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testBillMarkAsPaidNoAmount() {
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.minusHours(2)));
        assertEquals(EventState.MARK_AS_PAID_NO_AMOUNT,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testSMSCardNoAmount() {
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.SMS_CARD_NO_AMOUNT,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testTXNSuccess() {
        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setKey(new RecentsPrimaryKey());
        recent.getKey().setService("mobile");
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        recent.setOrderId(1L);
        recent.setIsTransaction(true);
        String config = "{\"recentCategoryLimit\":2,\"disableDropOff\":false,\"disableChatHistory\":false,\"smartReminderPrepaidEndDays\":3,\"smartReminderPrepaidStartDays\":5,\"smartReminderPostpaidEndDays\":3,\"smartReminderCustomerBillLimit\":30,\"disableDropOffService\":false,\"newAccountServices\": [\"mobile\"]}";
        try {
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e) {
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
        assertEquals(EventState.RECHARGE_SUCCESS,
                EventStateHandler.getState(recent));
    }


    @Test
    public void testTXNAutomaticSuccess() {
        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setChannelId(Constants.ReminderConstants.AUTOMATIC_CHANNEL);
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        assertEquals(EventState.RECHARGE_AUTOMATIC_SUCCESS,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testSMSCardMinAmountWithDate() {
        Recents recent = new Recents();
        recent.setDueAmount(null);
        recent.setMinDueAmount(22.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setDueDate(DateUtil.convertToDateViaInstant(date.plusDays(1)));
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.SMS_CARD_MIN_AMOUNT, EventStateHandler.getState(recent));
    }
    public void testSMSCardTxnWhenExtraIsNonEmpty(){
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        recent.setExtra("{\"eventState\":\"bill_paid\"}");
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.SMS_CARD_TXN,
                EventStateHandler.getState(recent));
    }
    @Test
    public void testSMSCardTxnWhenExtraIsEmpty(){
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        recent.setExtra("");
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.SMS_CARD_NO_AMOUNT,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testSMSCardMinAmountWithoutDate() {
        Recents recent = new Recents();
        recent.setDueAmount(null);
        recent.setMinDueAmount(22.0);
        recent.setDueDate(null);
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.SMS_CARD_MIN_AMOUNT,
                EventStateHandler.getState(recent));
    }

    public void testSMSCardTxnWhenExtraIsNull(){
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        recent.setExtra(null);
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.SMS_CARD_NO_AMOUNT,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testSMSCardTxnWhenExtraIsInvalidJson(){
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        recent.setExtra("\"eventState\":\"bill_paid\"}");
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.SMS_CARD_NO_AMOUNT,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testSMSCardTxnWhenExtraIsNotBillPaid(){
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.plusHours(2)));
        recent.setExtra("\"eventState\":\"bill_due\"}");
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.SMS_CARD_NO_AMOUNT,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testSMSCard() {
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setDueAmount(200.0);
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(EventState.MARK_AS_PAID,
                EventStateHandler.getState(recent));
    }
    @Test
    public void testSmartRecentDropOff() {
        Recents recent = new Recents();
        recent.setEventSource(Constants.EVENT_SOURCE.SMART_RECENT_DROPOFF);
        assertEquals(EventState.SMART_RECENT_DROPOFF, EventStateHandler.getState(recent));
    }

    @Test
    public void testSmartRecentValidation() {
        Recents recent = new Recents();
        recent.setEventSource(Constants.EVENT_SOURCE.SMART_RECENT_VALIDATION);
        assertEquals(EventState.SMART_RECENT_VALIDATION, EventStateHandler.getState(recent));
    }

    @Test
    public void testSMSCardNotAmount() {
        Recents recent = new Recents();
        recent.setEventSource(Constants.EVENT_SOURCE.RU_SMS);
        recent.setDueAmount(null);
        recent.setDueDate(new Date());
        recent.setMinDueAmount(null);
        assertEquals(EventState.SMS_CARD_NO_AMOUNT, EventStateHandler.getState(recent));
    }

    @Test
    public void testSMSCardMinDueAmount() {
        Recents recent = new Recents();
        recent.setEventSource(Constants.EVENT_SOURCE.RU_SMS);
        recent.setDueAmount(null);
        recent.setDueDate(new Date());
        recent.setMinDueAmount(22.0);
        assertEquals(EventState.SMS_CARD_MIN_AMOUNT, EventStateHandler.getState(recent));
    }

    @Test
    public void testWhenFullBIll() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("electricity");
        recent.setKey(key);
        recent.setEventSource(Constants.EVENT_SOURCE.RU_SMS);
        recent.setDueAmount(100.0);
        recent.setDueDate(new Date());
        recent.setMinDueAmount(22.0);
        assertEquals(EventState.RECHARGE_SUCCESS, EventStateHandler.getState(recent));
    }

    @Test
    public void testPGDeletedAMW() {
        Recents recent = new Recents();
        recent.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        assertEquals(EventState.SMS_CARD_NO_AMOUNT, EventStateHandler.getState(recent));
    }

    @Test
    public void testPGDeledtedMinAmountWithoutDate() {
        Recents recent = new Recents();
        recent.setDueAmount(null);
        recent.setMinDueAmount(22.0);
        recent.setDueDate(null);
        recent.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        assertEquals(EventState.SMS_CARD_MIN_AMOUNT,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testPGDeletedMarkAsPaid() {
        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setDueAmount(200.0);
        recent.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        assertEquals(EventState.MARK_AS_PAID,
                EventStateHandler.getState(recent));
    }

    @Test
    public void tesPGDeletedWhenFullBIll() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("electricity");
        recent.setKey(key);
        recent.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        recent.setDueAmount(100.0);
        recent.setDueDate(new Date());
        recent.setMinDueAmount(22.0);
        assertEquals(EventState.SMS_CARD, EventStateHandler.getState(recent));
    }

    @Test
    public void testPartialEventState() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("mobile");
        recent.setPayType("prepaid");
        recent.setKey(key);
        recent.setExtra("{\"partialBillState\":\"EXPIRED\"}");
        assertEquals(EventState.PARTIAL_BILL,
                EventStateHandler.getState(recent));
    }

    @Test
    public void testSmsCardNoAmountWhenEventSourceIsCSV(){
        Recents recent = new Recents();
        recent.setEventSource(Constants.EVENT_SOURCE.CSV);
        recent.setDueAmount(null);
        recent.setDueDate(null);
        recent.setMinDueAmount(null);
        assertEquals(EventState.SMS_CARD_NO_AMOUNT,EventStateHandler.getState(recent));

    }
}
