package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.dto.cdc.*;
import junit.framework.TestCase;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;


@RunWith(MockitoJUnitRunner.class)
public class CDCConsumerUtilTest extends TestCase {

    @InjectMocks
    private CDCConsumerUtil cdcConsumerUtil;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test_convertSmsPayloadToReminderCdc(){
        NonPaytmCDC nonPaytmCDCInstance = getNonPaytmCDCInstance();
        ReminderCDC reminderCDC = cdcConsumerUtil.convertSmsPayloadToReminderCdc(nonPaytmCDCInstance);
        assertEquals(reminderCDC.getAfter().getExtra().getValue(), nonPaytmCDCInstance.getAfter().getExtra().getValue());

    }

    private NonPaytmCDC getNonPaytmCDCInstance() {
        NonPaytmCDC nonPaytmCDC = new NonPaytmCDC();
        nonPaytmCDC.setTsMs(1694612345);
        nonPaytmCDC.setOp("CREATE");

        NonPaytmAfter after = new NonPaytmAfter();


        LongEntity customerId = new LongEntity();
        customerId.setValue(123456789L);
        customerId.setSet(true);
        after.setCustomerId(customerId);

        StringEntity service = new StringEntity();
        service.setValue("Mobile Recharge");
        service.setSet(true);
        after.setService(service);

        StringEntity rechargeNumber = new StringEntity();
        rechargeNumber.setValue("98543210");
        rechargeNumber.setSet(true);
        after.setRechargeNumber(rechargeNumber);

        StringEntity operator = new StringEntity();
        operator.setValue("Airtel");
        operator.setSet(true);
        after.setOperator(operator);

        StringEntity billDate = new StringEntity();
        billDate.setValue("2024-05-10");
        billDate.setSet(true);
        after.setBillDate(billDate);

        StringEntity paymentDate = new StringEntity();
        paymentDate.setValue("2024-05-12");
        paymentDate.setSet(true);
        after.setPaymentDate(paymentDate);

        StringEntity circle = new StringEntity();
        circle.setValue("Delhi");
        circle.setSet(true);
        after.setCircle(circle);

        StringEntity eventSource = new StringEntity();
        eventSource.setValue("WEB");
        eventSource.setSet(true);
        after.setEventSource(eventSource);

        DoubleEntity dueAmount = new DoubleEntity();
        dueAmount.setValue(199.50);
        dueAmount.setSet(true);
        after.setDueAmount(dueAmount);

        StringEntity dueDate = new StringEntity();
        dueDate.setValue("2024-06-05");
        dueDate.setSet(true);
        after.setDueDate(dueDate);

        DoubleEntity amount = new DoubleEntity();
        amount.setValue(199.50);
        amount.setSet(true);
        after.setAmount(amount);

        LongEntity orderId = new LongEntity();
        orderId.setValue(987654321L);
        orderId.setSet(true);
        after.setOrderId(orderId);

        StringEntity txnAmount = new StringEntity();
        txnAmount.setValue("199.50");
        txnAmount.setSet(true);
        after.setTxnAmount(txnAmount);

        StringEntity paytype = new StringEntity();
        paytype.setValue("CC");
        paytype.setSet(true);
        after.setPaytype(paytype);

        LongEntity productId = new LongEntity();
        productId.setValue(5678L);
        productId.setSet(true);
        after.setProductId(productId);

        IntegerEntity status = new IntegerEntity();
        status.setValue(1);
        status.setSet(true);
        after.setStatus(status);

        IntegerEntity notificationStatus = new IntegerEntity();
        notificationStatus.setValue(0);
        notificationStatus.setSet(true);
        after.setNotificationStatus(notificationStatus);

        StringEntity userData = new StringEntity();
        userData.setValue("{\"some_key\": \"some_value\"}");
        userData.setSet(true);
        after.setUserData(userData);

        StringEntity additionalInfo = new StringEntity();
        additionalInfo.setValue("{\"additional_key\": \"additional_value\"}");
        additionalInfo.setSet(true);
        after.setAdditionalInfo(additionalInfo);

        LongEntity updatedAt = new LongEntity();
        updatedAt.setValue(1694612345678L);  // Example timestamp in milliseconds
        updatedAt.setSet(true);
        after.setUpdatedAt(updatedAt);

        LongEntity createdAt = new LongEntity();
        createdAt.setValue(1694612345678L); // Example timestamp in milliseconds
        createdAt.setSet(true);
        after.setCreatedAt(createdAt);

        StringEntity customerOtherInfo = new StringEntity();
        customerOtherInfo.setValue("{\"customer_info_key\": \"customer_info_value\"}");
        customerOtherInfo.setSet(true);
        after.setCustomerOtherInfo(customerOtherInfo);

        StringEntity extraEntity = new StringEntity();
        JSONObject extraInfo = new JSONObject();
        extraInfo.put(Constants.PARTIAL_BILL_STATE, Constants.PARTIAL_BILL_STATE);
        extraEntity.setValue(extraInfo.toString());
        extraEntity.setSet(true);
        after.setExtra(extraEntity);

        LongEntity updateAt = new LongEntity();
        updateAt.setValue(1694612345678L);
        updateAt.setSet(true);
        after.setUpdateAt(updateAt);

        LongEntity createAt = new LongEntity();
        createAt.setValue(1694612345678L);
        createAt.setSet(true);
        after.setCreateAt(createAt);

        StringEntity cardNetwork = new StringEntity();
        cardNetwork.setValue("VISA");
        cardNetwork.setSet(true);
        after.setCardNetwork(cardNetwork);

        StringEntity bankName = new StringEntity();
        bankName.setValue("HDFC");
        bankName.setSet(true);
        after.setBankName(bankName);

        nonPaytmCDC.setAfter(after);
        return nonPaytmCDC;
    }
}