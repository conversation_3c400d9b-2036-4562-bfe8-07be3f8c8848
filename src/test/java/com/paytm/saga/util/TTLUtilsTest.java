package com.paytm.saga.util;

import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.FeatureConfigCache;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.Map;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class TTLUtilsTest {
    @InjectMocks
    private TTLUtils ttlUtils;
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        String config = "{\"disableDropOff\": false, \"categoryMapping\": {\"mobile\": [17, 21], \"financial services\": [131655]}, \"disableChatHistory\": false, \"smsEnabledServices\": [\"financial services\"], \"agentIdentificationLimit\": 300, \"smartReminderPrepaidEndDays\": 3, \"smartReminderPostpaidEndDays\": 3, \"smartReminderPrepaidStartDays\": 5, \"smartReminderCustomerBillLimit\": 60, \"recentDataUpdateAllowedServices\": [\"dth\", \"financial services\", \"tuition fee\",\"mobile\",\"electricity\"],\"categoryWiseTtl\":{\"insurance\":63072000}}";
        try{
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e){
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }
    @Test
    public void testTTLForInsurance(){
        String service = "insurance";
        Integer ttl = 2 * 365 * 24 * 60 * 60;
        assertEquals(ttl,TTLUtils.getTTL(service));
    }
    @Test
    public void testTTLForOtherServices(){
        String service = "mobile";
        Integer ttl = 548 * 24 * 60 * 60;
        assertEquals(ttl,TTLUtils.getTTL(service));
    }
    @Test
    public void ttlCase1(){
        /*
        Case:
        when txn time and createdAt both null
        */
        String service="mobile";
        assertEquals(Constants.DEFAULT_TTL,TTLUtils.getTTL(service,null, null));
    }
    @Test
    public void ttlCase2(){
        /*
        Case:
        when txn time is not null and createdAt is null
        */
        String service="mobile";
        //Date txnDate=DateUtil.dateIncrDecr(DateUtil.stringToDate(DateUtil.dateFormatter(new Date(),DateFormats.DATE_FORMAT), DateFormats.DATE_FORMAT),-547);
        Date txnDate=DateUtil.dateIncrDecr(new Date(),-547);
        Integer expectTimeInSeconds=(Constants.DEFAULT_TTL-(547* 24 * 60 * 60));
        assertEquals(expectTimeInSeconds,TTLUtils.getTTL(service,txnDate, null));
    }

    @Test
    public void ttlCase3(){
        /*
        Case:
        when txn time is null and createdAt is not null
        */
        String service="mobile";
        //Date createDate=DateUtil.dateIncrDecr(DateUtil.stringToDate(DateUtil.dateFormatter(new Date(),DateFormats.DATE_FORMAT), DateFormats.DATE_FORMAT),-547);
        Date createDate=DateUtil.dateIncrDecr(new Date(),-547);
        Integer expectTimeInSeconds=(Constants.DEFAULT_TTL-(547* 24 * 60 * 60));
        assertEquals(expectTimeInSeconds,TTLUtils.getTTL(service,null, createDate));
    }
}
