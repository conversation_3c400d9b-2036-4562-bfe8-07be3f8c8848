package com.paytm.saga.util;

import com.paytm.saga.dto.BillObject;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FrequentOrderRequest;
import com.paytm.saga.dto.FrequentOrderResponse;
import com.paytm.saga.enums.EventType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class FrequentOrderUtilTest {

    @InjectMocks
    private FrequentOrderUtil frequentOrderUtil;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        String config = "{\"disableDropOff\": false, \"categoryMapping\": {\"mobile\": [17, 21], \"financial services\": [131655]}, \"disableChatHistory\": false, \"smsEnabledServices\": [\"financial services\"], \"agentIdentificationLimit\": 300, \"smartReminderPrepaidEndDays\": 3, \"smartReminderPostpaidEndDays\": 3, \"smartReminderPrepaidStartDays\": 5, \"smartReminderCustomerBillLimit\": 60, \"recentDataUpdateAllowedServices\": [\"dth\", \"financial services\", \"tuition fee\",\"mobile\",\"electricity\"],\"categoryWiseTtl\":{\"insurance\":63072000}}";
        try{
            Map<String, Object> serviceConfig = JsonUtils.parseMapJson(config);
            FeatureConfigCache.getInstance().setFeatureConfigMap(serviceConfig);
        } catch (Exception e){
            //  logger.error("Error while parsing serviceConfig {} {}",config, e);
        }
    }

    @Test
    public void testCase1(){
        FrequentOrderResponse response=new FrequentOrderResponse();
        FrequentOrderRequest request=new FrequentOrderRequest();
        request.setOnlyReminder(false);
        boolean res=frequentOrderUtil.isValid(response,request);
        assertEquals(true,res);
    }
    @Test
    public void testCase2(){
        FrequentOrderResponse response=new FrequentOrderResponse();
        response.setEventType(EventType.RECHARGE);
        FrequentOrderRequest request=new FrequentOrderRequest();
        request.setOnlyReminder(true);
        boolean res=frequentOrderUtil.isValid(response,request);
        assertEquals(true,res);
    }
    @Test
    public void testCase3(){
        FrequentOrderResponse response=new FrequentOrderResponse();
        response.setEventType(EventType.RECENT);
        response.setBill(new BillObject());
        response.getBill().setIsBillDue(true);
        FrequentOrderRequest request=new FrequentOrderRequest();
        request.setOnlyReminder(true);
        boolean res=frequentOrderUtil.isValid(response,request);
        assertEquals(true,res);
    }
    @Test
    public void testCase4(){
        FrequentOrderResponse response=null;
        FrequentOrderRequest request=new FrequentOrderRequest();
        request.setOnlyReminder(true);
        boolean res=frequentOrderUtil.isValid(response,request);
        assertEquals(false,res);
    }
}
