package com.paytm.saga.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.NumberConstants;
import com.paytm.saga.dto.FeatureConfigCache;
import com.paytm.saga.dto.FrequentOrderResponse;
import com.paytm.saga.enums.EventState;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.json.JSONException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.paytm.saga.common.constant.Constants.CommonConstants.PARTIAL_DUE;
import static com.paytm.saga.common.constant.Constants.CommonConstants.TOTAL_DUE;
import static com.paytm.saga.common.constant.ServiceNameConstants.*;
import static com.paytm.saga.common.constant.ServiceNameConstants.EXCLUDE_RECENT_SERVICES;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class RecentUtilsTest {

    private ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testGetDynamicQuery() throws Exception {
        String response = "[{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"8052270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\", \"planBucket\":\"\"},{\"customerId\":1107199327, \"service\":\"mobile\", \"rechargeNumber\":\"8052270222\", \"operator\":\"airtel\", \"planBucket\":\"22\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        String query = RecentUtils.getDynamicQuery(1107199327L, recentsPrimaryKeys);
        assertEquals(query,
                "select " + CommonConsts.Query.RECENT_SELECT_COLUMNS+ " from recents where customerid = 1107199327 and (service, recharge_number, operator) in (('electricity','8052270003','jaipur vidyut vitran nigam ltd. (jvvnl)'),('mobile','8052270222','airtel'))");
    }
    @Test
    public void isMarkAsPaidTest() throws Exception {
        Recents recents = new Recents();
        recents.setMarkAsPaidTime(new Date());
        recents.setBillUpdateTime(DateUtil.dateIncrDecr(new Date(),-1));
        boolean isMarkAsPaid = RecentUtils.isMarkAsPaid(recents);
		assertTrue(isMarkAsPaid);
    }
    @Test
    public void isMarkAsPaidTest2() throws Exception {
        Recents recents = new Recents();
        recents.setMarkAsPaidTime(new Date());
        boolean isMarkAsPaid = RecentUtils.isMarkAsPaid(recents);
		assertTrue(isMarkAsPaid);
    }
    @Test
    public void isMarkAsPaidTest3() throws Exception {
        Recents recents = new Recents();
        recents.setMarkAsPaidTime(new Date());
        recents.setBillUpdateTime(DateUtil.dateIncrDecr(new Date(),-1));
        recents.setTxnTime(DateUtil.dateIncrDecr(new Date(),-1));
        boolean isMarkAsPaid = RecentUtils.isMarkAsPaid(recents);
		assertTrue(isMarkAsPaid);
    }

    @Test
    public void isMarkAsPaidTest4() throws Exception {
        Recents recents = new Recents();
        recents.setMarkAsPaidTime(DateUtil.dateIncrDecr(new Date(),-2));
        recents.setBillUpdateTime(DateUtil.dateIncrDecr(new Date(),-1));
        recents.setTxnTime(DateUtil.dateIncrDecr(new Date(),-1));
        boolean isMarkAsPaid = RecentUtils.isMarkAsPaid(recents);
		assertFalse(isMarkAsPaid);
    }

    @Test
    public void testGetDynamicNicknameUpdateQuery() throws Exception {
        String response = "[{\"customerId\":1107199327, \"service\":\"electricity\", \"rechargeNumber\":\"8052270003\", \"operator\":\"jaipur vidyut vitran nigam ltd. (jvvnl)\", \"planBucket\":\"\"},{\"customerId\":1107199327, \"service\":\"mobile\", \"rechargeNumber\":\"8052270222\", \"operator\":\"airtel\", \"planBucket\":\"22\"}]";
        List<RecentsPrimaryKey> recentsPrimaryKeys = objectMapper.readValue(response, new TypeReference<List<RecentsPrimaryKey>>() {
        });
        List<Recents> recents=new ArrayList<>();
        for(RecentsPrimaryKey recentsPrimaryKey:recentsPrimaryKeys){
            Recents recents1=new Recents();
            recents1.setKey(recentsPrimaryKey);
            recents.add(recents1);
        }
        Date date=DateUtil.stringToDate("15-08-1990", DateFormats.DATE_FORMAT);
        String query = RecentUtils.getDynamicNicknameUpdateQuery(1107199327L, recents,"nickname",date,100);
        assertEquals("update recents using TTL 100 set nick_name_v2='nickname', updated_at='1990-08-15 00:00:00' where customerid=1107199327 and (service, recharge_number, operator, plan_bucket) in (('electricity','8052270003','jaipur vidyut vitran nigam ltd. (jvvnl)',''),('mobile','8052270222','airtel','22'))", query);
    }

    @Test
    public void testTotalDue(){
        Recents recents = new Recents();
        recents.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        recents.setDueAmount(100.0);
        recents.setPayType("credit card");
        assertEquals(TOTAL_DUE,RecentUtils.getBillState(recents));
    }

    @Test
    public void testPartialDue(){
        Recents recents = new Recents();
        recents.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        recents.setDueAmount(100.0);
        recents.setOriginalDueAmount(200.0);
        recents.setPayType("credit card");
        assertEquals(PARTIAL_DUE,RecentUtils.getBillState(recents));
    }

    @Test
    public void getFrequentOrderResponseAmount_ShouldReturnMarkAsPaidAmount_WhenStateIsMarkAsPaid() {
        Recents recents = new Recents();
        recents.setMarkAsPaidAmount(100.0);
        recents.setIsMarkAsPaid(Boolean.TRUE);
        recents.setMarkAsPaidTime(new Date());
        recents.setDueAmount(200.0);
        assertEquals((Double)100.0, RecentUtils.getFrequentOrderResponseAmount(recents));
    }

    @Test
    public void getFrequentOrderResponseAmount_ShouldReturnAutomaticAmount_WhenStateIsAutomaticBill() {
        Recents recents = new Recents();
        recents.setPayType("prepaid");
        recents.setAutomaticAmount(200.0);
        recents.setChannelId(Constants.ReminderConstants.AUTOMATIC_CHANNEL);
        recents.setMarkAsPaidAmount(null);
        recents.setIsMarkAsPaid(Boolean.FALSE);
        recents.setMarkAsPaidTime(null);
        recents.setDueAmount(300.0);
        recents.setAutomaticStatus(1);
        recents.setDueDate(DateUtil.dateIncrDecr(new Date(),1));
        recents.setAutomaticDate(DateUtil.dateIncrDecr(new Date(),1));
        assertEquals((Double) 200.0, RecentUtils.getFrequentOrderResponseAmount(recents));
    }
    @Test
    public void shouldExcludeRecentBasedOnService_ShouldReturnTrue_WhenServiceIsExcluded() {
        Recents recents = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("merchant loan");
        recents.setKey(key);
        Map<String, Object> featureConfigMap = new HashMap<>();
        featureConfigMap.put(EXCLUDE_RECENT_SERVICES, Collections.singletonList("merchant loan"));
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfigMap);
        boolean result = RecentUtils.shouldExcludeRecentBasedOnService(recents);
        assertTrue(result);
    }
    @Test
    public void shouldExcludeRecentBasedOnService_ShouldReturnFalse_WhenServiceIsNotExcluded() {
        Recents recents = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("merchant loan");
        recents.setKey(key);
        Map<String, Object> featureConfigMap = new HashMap<>();
        featureConfigMap.put(EXCLUDE_RECENT_SERVICES, Collections.singletonList("electricity"));
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfigMap);
        boolean result = RecentUtils.shouldExcludeRecentBasedOnService(recents);
        assertFalse(result);
    }
    @Test
    public void shouldExcludeRecentBasedOnService_ShouldReturnFalse_WhenRecentsIsNull() {
        boolean result = RecentUtils.shouldExcludeRecentBasedOnService(null);
        assertFalse(result);
    }
    @Test
    public void shouldExcludeRecentBasedOnService_ShouldReturnFalse_WhenKeyIsNull() {
        Recents recents = new Recents();
        boolean result = RecentUtils.shouldExcludeRecentBasedOnService(recents);
        assertFalse(result);
    }
    @Test
    public void shouldExcludeRecentBasedOnService_ShouldReturnFalse_WhenServiceIsNull() {
        Recents recents = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        recents.setKey(key);
        boolean result = RecentUtils.shouldExcludeRecentBasedOnService(recents);
        assertFalse(result);
    }

    @Test
    public void isRUPartialBill_ShouldReturnTrue_WhenServiceIsInListAndDueDateIsNull() {
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        boolean result = RecentUtils.isRUPartialBill("service1", null, ruPartialBillRecoServices);
        assertTrue(result);
    }

    @Test
    public void isRUPartialBill_ShouldReturnTrue_WhenServiceIsInListAndDueDateBeforeMinDate() {
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        Date dueDate = DateUtil.stringToDate("2020-12-31", "yyyy-MM-dd");
        boolean result = RecentUtils.isRUPartialBill("service1", dueDate, ruPartialBillRecoServices);
        assertTrue(result);
    }

    @Test
    public void isRUPartialBill_ShouldReturnTrue_WhenServiceIsInListAndDueDateAfterMaxDate() {
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        Date dueDate = DateUtil.stringToDate("2050-01-02", "yyyy-MM-dd");
        boolean result = RecentUtils.isRUPartialBill("service1", dueDate, ruPartialBillRecoServices);
        assertTrue(result);
    }

    @Test
    public void isRUPartialBill_ShouldReturnFalse_WhenServiceIsNotInList() {
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        Date dueDate = DateUtil.stringToDate("2022-01-01", "yyyy-MM-dd");
        boolean result = RecentUtils.isRUPartialBill("service3", dueDate, ruPartialBillRecoServices);
        assertFalse(result);
    }

    @Test
    public void isRUPartialBill_ShouldReturnFalse_WhenDueDateIsWithinRange() {
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        Date dueDate = DateUtil.stringToDate("2022-01-01", "yyyy-MM-dd");
        boolean result = RecentUtils.isRUPartialBill("service1", dueDate, ruPartialBillRecoServices);
        assertFalse(result);
    }

    @Test
    public void isBillDueRUPartial_ShouldReturnTrue_WhenEventSourceIsValidationSync() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("service1");
        recent.setKey(key);
        recent.setEventSource(Constants.VALIDATION_SYNC_SOURCE_KEY);
        recent.setBillDate(new Date());
        recent.setDueDate(null);
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        List<String> updatedSourceList = Arrays.asList("ffr", "validation_sync");
        
        boolean result = RecentUtils.isBillDueRUPartial(recent, ruPartialBillRecoServices, updatedSourceList);
        assertTrue(result);
    }

    @Test
    public void isBillDueRUPartial_ShouldReturnTrue_WhenUpdatedDataSourceIsInList() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("service1");
        recent.setKey(key);
        recent.setEventSource("other_source");
        recent.setBillDate(new Date());
        recent.setDueDate(null);
        recent.setExtra("{\"updated_data_source\":\"ffr\"}");
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        List<String> updatedSourceList = Arrays.asList("ffr", "validation_sync");
        
        boolean result = RecentUtils.isBillDueRUPartial(recent, ruPartialBillRecoServices, updatedSourceList);
        assertTrue(result);
    }

    @Test
    public void isBillDueRUPartial_ShouldReturnFalse_WhenNoValidSource() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("service1");
        recent.setKey(key);
        recent.setEventSource("other_source");
        recent.setBillDate(new Date());
        recent.setDueDate(null);
        recent.setExtra("{\"updated_data_source\":\"other_source\"}");
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        List<String> updatedSourceList = Arrays.asList("ffr", "validation_sync");
        
        boolean result = RecentUtils.isBillDueRUPartial(recent, ruPartialBillRecoServices, updatedSourceList);
        assertFalse(result);
    }

    @Test
    public void isBillDueRUPartial_ShouldReturnFalse_WhenRecentIsNull() {
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        List<String> updatedSourceList = Arrays.asList("ffr", "validation_sync");
        boolean result = RecentUtils.isBillDueRUPartial(null, ruPartialBillRecoServices, updatedSourceList);
        assertFalse(result);
    }

    @Test
    public void isBillDueRUPartial_ShouldReturnFalse_WhenBillDateIsNull() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("service1");
        recent.setKey(key);
        recent.setEventSource("validation_sync");
        recent.setBillDate(null);
        recent.setDueDate(null);
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        List<String> updatedSourceList = Arrays.asList("ffr", "validation_sync");
        
        boolean result = RecentUtils.isBillDueRUPartial(recent, ruPartialBillRecoServices, updatedSourceList);
        assertFalse(result);
    }

    @Test
    public void isBillDueRUPartial_ShouldReturnFalse_WhenMarkedAsPaid() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("service1");
        recent.setKey(key);
        recent.setEventSource("validation_sync");
        recent.setBillDate(new Date());
        recent.setDueDate(null);
        recent.setMarkAsPaidTime(new Date());
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        List<String> updatedSourceList = Arrays.asList("ffr", "validation_sync");
        
        boolean result = RecentUtils.isBillDueRUPartial(recent, ruPartialBillRecoServices, updatedSourceList);
        assertFalse(result);
    }

    @Test
    public void isBillDueRUPartial_ShouldReturnTrue_WhenUpdatedDataSourceIsFFR() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("service1");
        recent.setKey(key);
        recent.setEventSource("other_source");
        recent.setBillDate(new Date());
        recent.setDueDate(null);
        recent.setExtra("{\"updated_data_source\":\"ffr\"}");
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        List<String> updatedSourceList = Arrays.asList("ffr", "validation_sync");
        
        boolean result = RecentUtils.isBillDueRUPartial(recent, ruPartialBillRecoServices, updatedSourceList);
        assertTrue(result);
    }

    @Test
    public void isBillDueRUPartial_ShouldReturnFalse_WhenUpdatedDataSourceIsNotInList() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("service1");
        recent.setKey(key);
        recent.setEventSource("other_source");
        recent.setBillDate(new Date());
        recent.setDueDate(null);
        recent.setExtra("{\"updated_data_source\":\"other_source\"}");
        List<String> ruPartialBillRecoServices = Arrays.asList("service1", "service2");
        List<String> updatedSourceList = Arrays.asList("ffr", "validation_sync");
        
        boolean result = RecentUtils.isBillDueRUPartial(recent, ruPartialBillRecoServices, updatedSourceList);
        assertFalse(result);
    }
    @Test
    public void getOrderId_ShouldReturnPendingOrderId_WhenPendingTxnIsMostRecentAndWithinVisibilityDays() {
        Recents recent = new Recents();
        String pendingDate=DateUtil.formatDate(new Date(),Constants.DATE_FORMAT);
        recent.setLastPendingTxn("{\"txn_time\":\""+pendingDate+"\",\"txn_id\":\"125\"}");
        recent.setLastFailureTxn("{\"txn_time\":\"2023-09-01T03:30:03.098Z\",\"txn_id\":\"124\"}");
        recent.setTxnTime(DateUtil.stringToDate("2023-08-01T10:00:00Z", "yyyy-MM-dd'T'HH:mm:ss'Z'"));
        recent.setOrderId(123L);

        Long result = RecentUtils.getOrderId(recent);

        assertEquals(RecentUtils.getPendingOrderId(recent), result);
    }

    @Test
    public void getOrderId_ShouldReturnFailedOrderId_WhenPendingTxnIsMostRecentButOutsideVisibilityDays() {
        Recents recent = new Recents();
        recent.setLastPendingTxn("{\"txn_time\":\"2023-01-01T03:30:03.098Z\",\"txn_id\":\"125\"}");
        recent.setLastFailureTxn("{\"txn_time\":\"2023-09-01T03:30:03.098Z\",\"txn_id\":\"124\"}");
        recent.setTxnTime(DateUtil.stringToDate("2023-08-01T10:00:00Z", "yyyy-MM-dd'T'HH:mm:ss'Z'"));
        recent.setOrderId(123L);

        Long result = RecentUtils.getOrderId(recent);

        assertEquals(RecentUtils.getFailedOrderId(recent), result);
    }

    @Test
    public void getOrderId_ShouldReturnOrderId_WhenSuccessTxnIsMostRecent() {
        Recents recent = new Recents();
        recent.setLastPendingTxn("{\"txn_time\":\"2023-08-01T03:30:03.098Z\",\"txn_id\":\"125\"}");
        recent.setLastFailureTxn("{\"txn_time\":\"2023-09-01T03:30:03.098Z\",\"txn_id\":\"124\"}");
        recent.setTxnTime(DateUtil.stringToDate("2023-10-01T10:00:00Z", "yyyy-MM-dd'T'HH:mm:ss'Z'"));
        recent.setOrderId(123L);

        Long result = RecentUtils.getOrderId(recent);

        assertEquals(recent.getOrderId(), result);
    }

    @Test
    public void getOrderId_ShouldReturnFailedOrderId_WhenFailedTxnIsMostRecent() {
        Recents recent = new Recents();
        recent.setLastPendingTxn("{\"txn_time\":\"2023-08-01T03:30:03.098Z\",\"txn_id\":\"125\"}");
        recent.setLastFailureTxn("{\"txn_time\":\"2023-10-01T03:30:03.098Z\",\"txn_id\":\"124\"}");
        recent.setTxnTime(DateUtil.stringToDate("2023-09-01T10:00:00Z", "yyyy-MM-dd'T'HH:mm:ss'Z'"));
        recent.setOrderId(123L);

        Long result = RecentUtils.getOrderId(recent);

        assertEquals(RecentUtils.getFailedOrderId(recent), result);
    }
    @Test
    public void getOrderId_ShouldReturnNull_WhenNoTransactionsExist() {
        Recents recent = new Recents();
        Long result = RecentUtils.getOrderId(recent);
        assertNull(result);
    }
    @Test
    public void testIsServicePrepaid() {
        String service = "electricity";
        boolean result = RecentUtils.isServicePrepaid(service);
        assertFalse(result);
    }
    @Test
    public void testIsOperatorPrepaid() {
        String operator = "msedcl";
        boolean result = RecentUtils.isOperatorPrepaid(operator);
        assertFalse(result);
    }
    @Test
    public void testCheckIfRequestContainsPrepaidFlag() {
        Map<String, String> extra = new HashMap<>();
        extra.put("isPrepaid", NumberConstants.PREPAID_VALUE);
        boolean result = RecentUtils.checkIfRequestContainsPrepaidFlag(extra);
        assertTrue(result);
    }
    @Test
    public void testIsPrepaidQualified() {
        String service = "broadband";
        String operator = "bsnl";
        Map<String, String> extra = new HashMap<>();
        extra.put("isPrepaid", NumberConstants.PREPAID_VALUE);
        boolean result = RecentUtils.isPrepaidQualified(service, operator, extra);
        assertFalse(result);
    }


    @Test
    public void checkIfPrepaidFlagIsSet_ShouldReturnTrue_WhenPrepaidFlagIsSet() {
        Recents recent = new Recents();
        recent.setExtra("{\"isPrepaid\":true}");
        Map extras = RecentUtils.getExtras(recent);
        boolean result = RecentUtils.checkIfPrepaidFlagIsSet(extras);
        assertTrue(result);
    }

    @Test
    public void checkIfPrepaidFlagIsSet_ShouldReturnFalse_WhenPrepaidFlagIsNotSet() {
        Recents recent = new Recents();
        recent.setExtra("{\"isPrepaid\":false}");
        Map extras = RecentUtils.getExtras(recent);
        boolean result = RecentUtils.checkIfPrepaidFlagIsSet(extras);
        assertFalse(result);
    }

    @Test
    public void checkIfPrepaidCase_ShouldReturnTrue_WhenPrepaidFlagIsSet() {
        Recents recent = new Recents();
        recent.setExtra("{\"isPrepaid\":true}");
        boolean result = RecentUtils.checkIfPrepaidCase(recent);
        assertTrue(result);
    }

    @Test
    public void checkIfPrepaidCase_ShouldReturnTrue_WhenServiceIsPrepaidAllowed() {
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setOperator("pspcl");
        recentsPrimaryKey.setCustomerId(1234L);
        recent.setKey(recentsPrimaryKey);

        Map<String, Object> featureConfigMap = new HashMap<>();
        featureConfigMap.put(PREPAID_ALLOWED_SERVICES, Collections.singletonList("electricity"));
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfigMap);

        boolean result = RecentUtils.checkIfPrepaidCase(recent);
        assertTrue(result);
    }

    @Test
    public void checkIfPrepaidCase_ShouldReturnTrue_WhenOperatorIsPrepaidAllowed() {
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setOperator("pspcl");
        recentsPrimaryKey.setCustomerId(1234L);
        recent.setKey(recentsPrimaryKey);

        Map<String, Object> featureConfigMap = new HashMap<>();
        featureConfigMap.put(PREPAID_ALLOWED_OPERATORS, Collections.singletonList("pspcl"));
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfigMap);

        boolean result = RecentUtils.checkIfPrepaidCase(recent);
        assertTrue(result);
    }

    @Test
    public void checkIfPrepaidCase_ShouldReturnTrue_CaseInsensitivityTest_WhenServiceIsPrepaidAllowed() {
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setOperator("pspcl");
        recentsPrimaryKey.setCustomerId(1234L);
        recent.setKey(recentsPrimaryKey);

        Map<String, Object> featureConfigMap = new HashMap<>();
        featureConfigMap.put(PREPAID_ALLOWED_SERVICES, Collections.singletonList("ELEctriCiTy"));
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfigMap);

        boolean result = RecentUtils.checkIfPrepaidCase(recent);
        assertTrue(result);
    }

    @Test
    public void checkIfPrepaidCase_ShouldReturnTrue_CaseInsensitivityTest_WhenOperatorIsPrepaidAllowed() {
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setOperator("pspcl");
        recentsPrimaryKey.setCustomerId(1234L);
        recent.setKey(recentsPrimaryKey);

        Map<String, Object> featureConfigMap = new HashMap<>();
        featureConfigMap.put(PREPAID_ALLOWED_OPERATORS, Collections.singletonList("PsPcL"));
        FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfigMap);

        boolean result = RecentUtils.checkIfPrepaidCase(recent);
        assertTrue(result);
    }

    @Test
    public void checkIfPrepaidCase_ShouldReturnFalse_WhenServiceOperatorAreNotPrepaid_PrepaidFlagNotSet() {
        Recents recent = new Recents();
        RecentsPrimaryKey recentsPrimaryKey = new RecentsPrimaryKey();
        recentsPrimaryKey.setService("electricity");
        recentsPrimaryKey.setOperator("pspcl");
        recentsPrimaryKey.setCustomerId(1234L);
        recent.setKey(recentsPrimaryKey);

        boolean result = RecentUtils.checkIfPrepaidCase(recent);
        assertFalse(result);
    }

    private static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSX";

    @Test
    public void testGetFinalTxnDetails_RechargeFailure() throws Exception {
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_FAILURE);
        Recents recent = new Recents();
        String failureTxnTime = "2023-10-01T10:00:00.000Z";
        recent.setLastFailureTxn("{\"txn_time\":\"" + failureTxnTime + "\",\"txn_id\":123,\"txn_amount\":100.0,\"channel_id\":\"WEB\"}");
        RecentUtils.getFinalTxnDetails(response, recent);
        assertEquals(Long.valueOf(123), response.getOrderId());
        assertEquals(Double.valueOf(100.0), response.getTxnAmount());
        assertEquals("WEB", response.getChannel());
        assertTrue(response.isHasPaymentHistory());
    }

    @Test
    public void testGetFinalTxnDetails_RechargePending() throws Exception {
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_PENDING);
        Recents recent = new Recents();
        String pendingTxnTime = "2023-10-02T10:00:00.000Z";
        recent.setLastPendingTxn("{\"txn_time\":\"" + pendingTxnTime + "\",\"txn_id\":124,\"txn_amount\":200.0,\"channel_id\":\"MOBILE\"}");
        RecentUtils.getFinalTxnDetails(response, recent);
        assertEquals(Long.valueOf(124), response.getOrderId());
        assertEquals(Double.valueOf(200.0), response.getTxnAmount());
        assertEquals("MOBILE", response.getChannel());
        assertTrue(response.isHasPaymentHistory());
    }

    @Test
    public void testGetFinalTxnDetails_RechargePending_CONSENT_NOTRECEIVED() throws Exception {
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_PENDING_CONSENT_NOTRECEIVED);
        Recents recent = new Recents();
        String pendingTxnTime = "2023-10-02T10:00:00.000Z";
        recent.setLastPendingTxn("{\"txn_time\":\"" + pendingTxnTime + "\",\"txn_id\":124,\"txn_amount\":200.0,\"channel_id\":\"MOBILE\"}");
        RecentUtils.getFinalTxnDetails(response, recent);
        assertEquals(Long.valueOf(124), response.getOrderId());
        assertEquals(Double.valueOf(200.0), response.getTxnAmount());
        assertEquals("MOBILE", response.getChannel());
        assertTrue(response.isHasPaymentHistory());
    }

    @Test
    public void testGetFinalTxnDetails_NoTransactions() {
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_SUCCESS);
        Recents recent = new Recents();
        RecentUtils.getFinalTxnDetails(response, recent);
        assertNull(response.getTxnDate());
        assertNull(response.getOrderId());
        assertNull(response.getTxnAmount());
        assertNull(response.getChannel());
        assertFalse(response.isHasPaymentHistory());
    }
    @Test
    public void testGetFinalTxnDetails_BothTransactionsPresent() {
        // Given
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_FAILURE);

        Recents recent = new Recents();
        String failureTxnTime = "2023-10-01T10:00:00.000Z";
        String pendingTxnTime = "2023-10-01T09:00:00.000Z";

        recent.setLastFailureTxn("{\"txn_time\":\"" + failureTxnTime +
                "\",\"txn_id\":123,\"txn_amount\":100.0,\"channel_id\":\"WEB\"}");
        recent.setLastPendingTxn("{\"txn_time\":\"" + pendingTxnTime +
                "\",\"txn_id\":456,\"txn_amount\":200.0,\"channel_id\":\"APP\"}");

        // When
        RecentUtils.getFinalTxnDetails(response, recent);

        // Then
        assertEquals(Long.valueOf(123), response.getOrderId());
        assertEquals(Double.valueOf(100.0), response.getTxnAmount());
        assertEquals("WEB", response.getChannel());
        assertTrue(response.isHasPaymentHistory());
    }

    @Test
    public void testGetFinalTxnDetails_OnlyPendingTransaction() {
        // Given
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_PENDING);

        Recents recent = new Recents();
        String pendingTxnTime = "2023-10-01T10:00:00.000Z";
        recent.setLastPendingTxn("{\"txn_time\":\"" + pendingTxnTime +
                "\",\"txn_id\":456,\"txn_amount\":200.0,\"channel_id\":\"APP\"}");

        // When
        RecentUtils.getFinalTxnDetails(response, recent);

        // Then
        assertEquals(Long.valueOf(456), response.getOrderId());
        assertEquals(Double.valueOf(200.0), response.getTxnAmount());
        assertEquals("APP", response.getChannel());
        assertTrue(response.isHasPaymentHistory());
    }

    @Test
    public void testGetFinalTxnDetails_OnlyFailureTransaction() {
        // Given
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_FAILURE);

        Recents recent = new Recents();
        String failureTxnTime = "2023-10-01T10:00:00.000Z";
        recent.setLastFailureTxn("{\"txn_time\":\"" + failureTxnTime +
                "\",\"txn_id\":123,\"txn_amount\":100.0,\"channel_id\":\"WEB\"}");

        // When
        RecentUtils.getFinalTxnDetails(response, recent);

        // Then
        assertEquals(Long.valueOf(123), response.getOrderId());
        assertEquals(Double.valueOf(100.0), response.getTxnAmount());
        assertEquals("WEB", response.getChannel());
        assertTrue(response.isHasPaymentHistory());
    }

    @Test
    public void testGetFinalTxnDetails_Failure_CONSENT_NOTRECEIVEDTransaction() {
        // Given
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_FAILURE_CONSENT_NOTRECEIVED);

        Recents recent = new Recents();
        String failureTxnTime = "2023-10-01T10:00:00.000Z";
        recent.setLastFailureTxn("{\"txn_time\":\"" + failureTxnTime +
                "\",\"txn_id\":123,\"txn_amount\":100.0,\"channel_id\":\"WEB\"}");

        // When
        RecentUtils.getFinalTxnDetails(response, recent);

        // Then
        assertEquals(Long.valueOf(123), response.getOrderId());
        assertEquals(Double.valueOf(100.0), response.getTxnAmount());
        assertEquals("WEB", response.getChannel());
        assertTrue(response.isHasPaymentHistory());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetFinalTxnDetails_InvalidJson() {
        // Given
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_FAILURE);

        Recents recent = new Recents();
        recent.setLastFailureTxn("invalid json");

        // When
        RecentUtils.getFinalTxnDetails(response, recent);

        // Then
        assertNull(response.getOrderId());
        assertNull(response.getTxnAmount());
        assertNull(response.getChannel());
        assertFalse(response.isHasPaymentHistory());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetFinalTxnDetails_MissingFields() {
        // Given
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_FAILURE);

        Recents recent = new Recents();
        recent.setLastFailureTxn("{\"txn_time\":\"2023-10-01T10:00:00.000Z\",\"txn_id\":1233333,\"txn_amount\":100}");

        // When
        RecentUtils.getFinalTxnDetails(response, recent);

        // Then
        assertNull(response.getOrderId());
        assertNull(response.getTxnAmount());
        assertNull(response.getChannel());
        assertFalse(response.isHasPaymentHistory());
    }

    @Test
    public void testGetFinalTxnDetails_MissingEventState() {
        // Given
        FrequentOrderResponse response = new FrequentOrderResponse();
        //response.setEventState(EventState.RECHARGE_FAILURE);

        Recents recent = new Recents();
        recent.setLastFailureTxn("{\"txn_time\":\"2023-10-01T10:00:00.000Z\",\"txn_id\":1233333,\"txn_amount\":100}");

        // When
        RecentUtils.getFinalTxnDetails(response, recent);

        // Then
        assertNull(response.getOrderId());
        assertNull(response.getTxnAmount());
        assertNull(response.getChannel());
        assertFalse(response.isHasPaymentHistory());
    }

    @Test
    public void testGetFinalTxnDetails_MissingTxnDetail() {
        // Given
        FrequentOrderResponse response = new FrequentOrderResponse();
        response.setEventState(EventState.RECHARGE_FAILURE);

        Recents recent = new Recents();
        recent.setLastFailureTxn(null);

        // When
        RecentUtils.getFinalTxnDetails(response, recent);

        // Then
        assertNull(response.getOrderId());
        assertNull(response.getTxnAmount());
        assertNull(response.getChannel());
        assertFalse(response.isHasPaymentHistory());
    }
}

