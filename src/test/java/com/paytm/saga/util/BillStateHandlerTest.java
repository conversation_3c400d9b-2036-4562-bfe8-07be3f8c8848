package com.paytm.saga.util;

import com.paytm.saga.enums.BillState;
import com.paytm.saga.model.Recents;
import com.paytm.saga.model.primaryKeys.RecentsPrimaryKey;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import com.paytm.saga.common.constant.Constants;

import java.time.LocalDateTime;
import java.util.Date;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class BillStateHandlerTest {


    @Test
    public void testBillMarkAsPaid() {
        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.minusHours(2)));
        assertEquals(BillState.NO_DUE,
                BillStateHandler.getState(recent));
    }


    @Test
    public void testBillDueAmountZero() {


        Recents recent = new Recents();
        recent.setDueAmount(0.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setMarkAsPaidTime(DateUtil.convertToDateViaInstant(date));
        recent.setTxnTime(DateUtil.convertToDateViaInstant(date.minusHours(2)));
        assertEquals(BillState.NO_DUE,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testBillAlreadyExpired() {

        Recents recent = new Recents();
        LocalDateTime date = LocalDateTime.now();
        recent.setDueAmount(10.0);
        recent.setDueDate(DateUtil.convertToDateViaInstant(date.minusDays(1)));
        assertEquals(BillState.ALREADY_EXPIRED,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testBillExpiredTomorrow() {

        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setAutomaticDate(DateUtil.convertToDateViaInstant(date));
        recent.setAutomaticStatus(0);
        recent.setDueDate(DateUtil.convertToDateViaInstant(date.plusDays(1)));
        assertEquals(BillState.EXPIRES_TOMORROW,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testBillExpiredTomorrowWithAutomaticContext() {

        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setAutomaticDate(DateUtil.convertToDateViaInstant(date.plusDays(1)));
        recent.setAutomaticStatus(1);
        recent.setDueDate(DateUtil.convertToDateViaInstant(date.plusDays(1)));
        assertEquals(BillState.EXPIRES_TOMORROW_AUTOMATIC,
                BillStateHandler.getState(recent));
    }


    @Test
    public void testBillWillExpiredWithAutomaticContext() {
        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setAutomaticDate(DateUtil.convertToDateViaInstant(date.plusDays(2)));
        recent.setAutomaticStatus(1);
        recent.setDueDate(DateUtil.convertToDateViaInstant(date.plusDays(2)));
        assertEquals(BillState.WILL_EXPIRE_AUTOMATIC,
                BillStateHandler.getState(recent));
    }


    @Test
    public void testBillWillExpired() {

        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setAutomaticDate(DateUtil.convertToDateViaInstant(date));
        recent.setAutomaticStatus(0);
        recent.setDueDate(DateUtil.convertToDateViaInstant(date.plusDays(2)));
        assertEquals(BillState.WILL_EXPIRE,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testBillExpiredTodayWithAutomaticContext() {

        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setAutomaticDate(DateUtil.convertToDateViaInstant(date));
        recent.setAutomaticStatus(1);
        recent.setDueDate(DateUtil.convertToDateViaInstant(date));
        assertEquals(BillState.EXPIRES_TODAY_AUTOMATIC,
                BillStateHandler.getState(recent));
    }


    @Test
    public void testBillExpiredToday() {

        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        LocalDateTime date = LocalDateTime.now();
        recent.setDueDate(DateUtil.convertToDateViaInstant(date));
        assertEquals(BillState.EXPIRES_TODAY,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testNoDatePresentAndDueAmountIsPresent() {
        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        recent.setDueDate(null);
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(BillState.NO_DATE,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testNoDatePresentAndMinAmountIsPresent() {
        Recents recent = new Recents();
        recent.setMinDueAmount(10.0);
        recent.setDueDate(null);
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(BillState.NO_DATE,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testNoDatePresentAndDueAmountIsPresentPGDeletedAMW() {
        Recents recent = new Recents();
        recent.setDueAmount(10.0);
        recent.setDueDate(null);
        recent.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        assertEquals(BillState.NO_DATE,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testNoDatePresentAndMinAmountIsPresentPGDeletedAMW() {
        Recents recent = new Recents();
        recent.setMinDueAmount(10.0);
        recent.setDueDate(null);
        recent.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        assertEquals(BillState.NO_DATE,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testNoBillDue() {
        Recents recent = new Recents();
        recent.setMinDueAmount(10.0);
        recent.setDueDate(null);
        recent.setMarkAsPaidTime(new Date());
        recent.setEventSource(Constants.EVENT_SOURCE.PG_DELETED_AMW);
        assertEquals(BillState.NO_DUE,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testNoDateNoAmountState() {
        Recents recent = new Recents();
        recent.setMinDueAmount(null);
        recent.setDueDate(null);
        recent.setEventSource(Constants.EVENT_SOURCE.SMS);
        assertEquals(BillState.NO_DATE_NO_AMOUNT,
                BillStateHandler.getState(recent));
    }

    @Test
    public void testPartialBillState1() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("mobile");
        recent.setPayType("prepaid");
        recent.setKey(key);
        recent.setExtra("{\"partialBillState\":\"EXPIRED\"}");
        assertEquals(BillState.EXPIRED,
                BillStateHandler.getPartialBillState(recent));
    }

    @Test
    public void testPartialBillState2() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("mobile");
        recent.setPayType("prepaid");
        recent.setKey(key);
        recent.setExtra("{\"partialBillState\":\"EXPIRING_SOON\"}");
        assertEquals(BillState.EXPIRING_SOON,
                BillStateHandler.getPartialBillState(recent));
    }

    @Test
    public void testPartialBillState3() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("mobile");
        recent.setPayType("prepaid");
        recent.setKey(key);
        recent.setExtra("{\"partialBillState\":\"INCOMING_STOPPED\"}");
        assertEquals(BillState.INCOMING_STOPPED,
                BillStateHandler.getPartialBillState(recent));
    }

    @Test
    public void testNoDueWithOutstandingAmount() {
        Recents recent = new Recents();
        RecentsPrimaryKey key = new RecentsPrimaryKey();
        key.setService("financial services");
        recent.setPayType("Credit card");
        recent.setKey(key);
        recent.setDueAmount((double) 0);
        recent.setOriginalDueAmount((double) 0);
        recent.setCurrentOutstandingAmount(1000.0);
        assertEquals(BillState.NO_DUE_OUT_AMT,
            BillStateHandler.getState(recent));
    }
}
