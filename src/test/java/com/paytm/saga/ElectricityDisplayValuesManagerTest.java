package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.service.aggregator.viewmanager.impl.ElectricityDisplayValuesManager;

public class ElectricityDisplayValuesManagerTest {

	@InjectMocks
	private ElectricityDisplayValuesManager electricityDisplayValuesManager;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void rechargeOffDateFormatTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventType(EventTypes.RECHARGE)
				.setBillDate("2021-03-12 12:12:01").setDueDate("2021-03-12 12:12:01").setStatus("Failure").build();
		List<ViewElementInfo> viewElementInfos = electricityDisplayValuesManager.getDisplayValues(historyView);
		assertEquals(2, viewElementInfos.size());
		assertEquals("Fri, 12 Mar", viewElementInfos.get(0).getValue());
		assertEquals("Bill Date", viewElementInfos.get(0).getKey());
		assertEquals("ds1", viewElementInfos.get(0).getType());
		assertEquals("Fri, 12 Mar", viewElementInfos.get(1).getValue());
		assertEquals("Due Date", viewElementInfos.get(1).getKey());
		assertEquals("ds2", viewElementInfos.get(1).getType());
	}

	@Test
	public void dropOffDateFormatTest() {
		HistoryView historyView = new HistoryViewBuilder().setEventType(EventTypes.DROPOFF).setBillDate("03-12-2020")
				.setDueDate("2020-12-03").setStatus("active").build();
		List<ViewElementInfo> viewElementInfos = electricityDisplayValuesManager.getDisplayValues(historyView);
		assertEquals(2, viewElementInfos.size());
		assertEquals("Thu, 03 Dec", viewElementInfos.get(0).getValue());
		assertEquals("Bill Date", viewElementInfos.get(0).getKey());
		assertEquals("ds1", viewElementInfos.get(0).getType());
		assertEquals("Thu, 03 Dec", viewElementInfos.get(1).getValue());
		assertEquals("Due Date", viewElementInfos.get(1).getKey());
		assertEquals("ds2", viewElementInfos.get(1).getType());
	}

}
