package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.paytm.saga.dto.*;
import com.paytm.saga.service.*;
import com.paytm.saga.service.aggregator.AggregatorServiceInterface;
import com.paytm.saga.util.JsonUtils;

import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.recent.repository.RecentsRepository;
import com.paytm.saga.util.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.service.aggregator.cardmanager.OperatorChangeCard;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.service.aggregator.cardmanager.PlanExpiry;
import com.paytm.saga.model.PlanExpiryHistory;
import com.paytm.saga.dto.DCATGetPlanResponse;
import com.paytm.saga.service.aggregator.AggregatorService;
import com.paytm.saga.dto.HistoryPage;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.service.aggregator.cardmanager.DateCard;
import com.paytm.saga.service.aggregator.cardmanager.MarkedAsPaidCard;
import com.paytm.saga.service.aggregator.cardmanager.MnpCard;
import com.paytm.saga.service.aggregator.cardmanager.RechargeCard;
import com.paytm.saga.service.aggregator.cardmanager.ScratchCard;

import com.paytm.saga.dto.ChannelHistoryPage;
import com.paytm.saga.dto.ViewElementInfo;


public class RechargeSagaServiceTests {
	@Mock
	private ChannelHistoryRepositoryWrapperService channelHistoryRepository;
	@Mock
	private RecentsRepository recentsRepository;
	@InjectMocks
	private ChannelHistoryService channelHistoryService;

	@InjectMocks
	private RechargeSagaServiceImpl rechargeSagaService;
	@Mock
	private AggregatorServiceInterface utilityAggregator;
	@Mock
	private NewThemeService newThemeService;
	@Mock
	private HistoryView historyView;
	@Mock
	private AggregatorService aggregatorService;
	@Mock
	private ChannelHistoryFinalizedService channelHistoryFinalizedService;
	@Mock
	private ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
	@Mock
	private DateCard dateCard;
	@Mock
	private MnpCard mnpCard;
	@Mock
	private PlanExpiry planExpiry;
	@Mock 
	private RechargeCard rechargeCard;
	@Mock
	private ScratchCard scratchCard;
	@Mock
	private MarkedAsPaidCard markedAsPaidCard;
	@Mock
	private DCATService dCATService;
	@Mock
	private RPSService rpsService;
	@Mock
	private PlanExpiryHistoryService planExpiryHistoryService;
	@Mock
	private DropOffService dropOffService;
	@Mock
	private OperatorChangeCard operatorChangeCard;

	
	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void contextgggLoads() {
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		ChannelHistory channelHistory = new ChannelHistory();
		channelHistory.setAmount("100");
		channelHistories.add(channelHistory);
		channelHistories.add(channelHistory);
		channelHistories.add(channelHistory);
		channelHistories.add(channelHistory);

		when(channelHistoryRepository.findByCustomerIdAndRechargeNumberAndServiceAndFinalisedState((long) 1, "recharge_number",
				"service",false)).thenReturn(channelHistories);
		List<ChannelHistory> res = channelHistoryService.getPageOfHistory((long) 1, "recharge_number", "service");
		System.out.println("res.size()" + res.size());
		assertEquals(4, res.size());
	}
	
	@Test 
	public void testOperatorChangeCard() {
		OperatorChangeCard operatorChangeCard = new OperatorChangeCard();
		HistoryView historyView = operatorChangeCard.addOperatorChangeCard("jio", "airtel", new Date());
		System.out.println("Theme::"+historyView.getThemeType()+"::currentOperator::"+historyView.getOperator()+
				"::previousOperator::"+historyView.getPreviousOperator()+"::eventType::"+
				historyView.getEventType());
		assertEquals(CardThemeTypes.MNP_CARD_THEME,historyView.getThemeType());
		assertEquals("airtel",historyView.getOperator());
		assertEquals("jio", historyView.getPreviousOperator());
		assertEquals(CardTypes.OPERATOR_CHANGE_CARD, historyView.getEventType());
		List<ViewElementInfo> headers = historyView.getHeadings();
		assertEquals("You changed the operator from "+"jio"+" to "+"airtel",headers.get(0).getValue());
	}
	
	@Test
	public void planExpiryAndCtaRemovalForNoPreviousOrderTest() {

		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();	

		Map<String,String> display_values = new HashMap<String,String>();
		display_values.put("data", "2GB");
		display_values.put("validity","30 days");
		display_values.put("talktime", "1000 hrs");
		display_values.put("sms", "2000");
		display_values.put("addon_benefit1", "20 days extra validity");
		display_values.put("addon_benefit2", "wynk free");
		display_values.put("description", "jio premium plan");

		DCATGetPlanResponse dCATGetPlanResponse = new DCATGetPlanResponse();
		dCATGetPlanResponse.setPlan_bucket("jio premium");
		dCATGetPlanResponse.setData("3GB");
		dCATGetPlanResponse.setValidity("40 days");
		dCATGetPlanResponse.setTalktime("1500 hrs");
		dCATGetPlanResponse.setSms("2000");
		dCATGetPlanResponse.setAddon_benefit1("30 days extra validity");
		dCATGetPlanResponse.setAddon_benefit2("prime video free");
		dCATGetPlanResponse.setDescription("jio premium plan updated");

		List<PlanExpiryHistory> planExpiryHistoryList = new ArrayList<PlanExpiryHistory>();
		PlanExpiryHistory planExpiryHistory = new PlanExpiryHistory("9660027375","mobile","haryana","jio","jio premium",new Date(),(long)123456789,"recharge",new Date(),new Date(1613563377),display_values,200.00);
		planExpiryHistoryList.add(planExpiryHistory);

		when(dCATService.getPlanDetails("7166", "12.12.12","web","jio","200","haryana","mobile")).thenReturn(dCATGetPlanResponse);
		when(planExpiryHistoryService.getPlanExpiryByRecharge("9660027375","jio","haryana","mobile")).thenReturn(planExpiryHistoryList);

		PlanExpiry planExpiry = new PlanExpiry(planExpiryHistoryService,dCATService,rpsService,channelHistoryRepository, recentsRepository);
		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setChannelHistories(channelHistories);
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory((long)123456789,"9660027375", "mobile", 0, null)).thenReturn(channelHistoryPage);

		

		AggregatorService aggregatorService = 	new AggregatorService(channelHistoryFinalizedService, channelHistoryService, channelHistoryFinalizedUtil, dateCard, mnpCard, planExpiry, rechargeCard, null, scratchCard, markedAsPaidCard, dropOffService, operatorChangeCard);
		HistoryPage historyPage = aggregatorService.aggregateHistoryInfo(channelHistories, (long)123456789, "9660027375", "mobile", null, "jio:mobile:haryana", "airtel:mobile:haryana", null,null,null);

		List<HistoryView> cards = new ArrayList<HistoryView>();

		cards = historyPage.getCards();

		for(HistoryView historyView: cards) {
			if(historyView.getEventType()==CardThemeTypes.SUGGESTED_RECHARGE_CARD_THEME) {
				System.out.println(historyView.getPlanExpiryDate()+"::"+historyView.getCta().size()+"::"+historyView.getHeadings().size());
				assertEquals("", historyView.getPlanExpiryDate());
				assertEquals(1, historyView.getCta().size());
				assertEquals(2, historyView.getHeadings().size());
			}
		}
	}
	
	@Test
	public void testNewUserSuggestionCard() {
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();

		Map<String, String> billsObj = new HashMap<String, String>();
		Map<String, String> displayValues = new HashMap<String, String>();
		billsObj.put("plan_bucket", "jio premium");
		billsObj.put("plan_id", "123456");

		displayValues.put("data", "2GB");
		displayValues.put("validity","30 days");
		displayValues.put("talktime", "1000 hrs");
		displayValues.put("sms", "2000");
		displayValues.put("addon_benefit1", "20 days extra validity");
		displayValues.put("addon_benefit2", "wynk free");
		displayValues.put("description", "jio premium plan");

		Date transactionUpdateTime = DateUtil.dateIncrDecr(new Date(), -(CommonConsts.PLAN_EXPIRY_DAYS-1));
		ChannelHistory channelHistory = new ChannelHistory((long)3023, "9811300002", (long)123456, (long)567890123, new Date(),(long)26,null,null,null,"jio","prepaid","mobile", "200",billsObj, "7","MARK_AS_PAID",new Date(),transactionUpdateTime, false, (long)1,displayValues, "haryana", "07","SUCCESS");
		channelHistories.add(channelHistory);

		PlanExpiry planExpiry = new PlanExpiry(planExpiryHistoryService,dCATService,rpsService,channelHistoryRepository,recentsRepository);
		DCATGetPlanResponse dCATGetPlanResponse = new DCATGetPlanResponse();
		dCATGetPlanResponse.setPlan_bucket("jio premium");
		dCATGetPlanResponse.setData("3GB");
		dCATGetPlanResponse.setValidity("40 days");
		dCATGetPlanResponse.setTalktime("1500 hrs");
		dCATGetPlanResponse.setSms("2000");
		dCATGetPlanResponse.setAddon_benefit1("30 days extra validity");
		dCATGetPlanResponse.setAddon_benefit2("prime video free");
		dCATGetPlanResponse.setDescription("jio premium plan updated");

		List<PlanExpiryHistory> planExpiryHistoryList = new ArrayList<PlanExpiryHistory>();
		PlanExpiryHistory planExpiryHistory = new PlanExpiryHistory("9811300002","mobile","haryana","jio","jio premium",new Date(),(long)123456,"recharge",new Date(),new Date(1613563377),displayValues,200.00);
		PlanExpiryHistory planExpiryHistory1 = new PlanExpiryHistory("9811300002","mobile","haryana","jio","jio basic",new Date(),(long)123456,"recharge",new Date(),new Date(1613563377),displayValues,300.00);

		planExpiryHistoryList.add(planExpiryHistory);
		planExpiryHistoryList.add(planExpiryHistory1);

		when(rpsService.getPlanDetails("7166", "12.12.12","web","jio","200","haryana",null,"9660027375","mobile")).thenReturn(dCATGetPlanResponse);
		when(rpsService.getPlanDetails("7166", "12.12.12","web","jio","300","haryana",null,"9660027375","mobile")).thenReturn(dCATGetPlanResponse);
		when(planExpiryHistoryService.getPlanExpiryByRecharge("9811300002","jio","haryana","mobile")).thenReturn(planExpiryHistoryList);

		HistoryView historyView = planExpiry.getRechargeSuggestionCard("9811300002","jio:mobile:haryana", channelHistories);

		assertEquals("300.0", String.valueOf(historyView.getAmount()));
		assertEquals("suggested_recharge", historyView.getThemeType());
	}

	@Test
	public void checkPlanExpiryTest() {

		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		channelHistories = Collections.<ChannelHistory> emptyList();
		Map<String,String> display_values = new HashMap<String,String>();
			display_values.put("data", "2GB");
			display_values.put("validity","30 days");
			display_values.put("talktime", "1000 hrs");
			display_values.put("sms", "2000");
			display_values.put("addon_benefit1", "20 days extra validity");
			display_values.put("addon_benefit2", "wynk free");
			display_values.put("description", "jio premium plan");

		DCATGetPlanResponse dCATGetPlanResponse = new DCATGetPlanResponse();
		dCATGetPlanResponse.setPlan_bucket("jio premium");
		dCATGetPlanResponse.setData("3GB");
		dCATGetPlanResponse.setValidity("40 days");
		dCATGetPlanResponse.setTalktime("1500 hrs");
		dCATGetPlanResponse.setSms("2000");
		dCATGetPlanResponse.setAddon_benefit1("30 days extra validity");
		dCATGetPlanResponse.setAddon_benefit2("prime video free");
		dCATGetPlanResponse.setDescription("jio premium plan updated");

		List<PlanExpiryHistory> planExpiryHistoryList = new ArrayList<PlanExpiryHistory>();
		PlanExpiryHistory planExpiryHistory = new PlanExpiryHistory("9660027375","mobile","haryana","jio","jio premium",new Date(),(long)123456789,"recharge",new Date(),new Date(1613563377),display_values,200.00);

		planExpiryHistoryList.add(planExpiryHistory);
		when(rpsService.getPlanDetails("7166", "12.12.12","web","jio","200","haryana",null,"9660027375","mobile")).thenReturn(dCATGetPlanResponse);
		when(planExpiryHistoryService.getPlanExpiryByRecharge("9660027375","jio","haryana","mobile")).thenReturn(planExpiryHistoryList);



		PlanExpiry planExpiry = new PlanExpiry(planExpiryHistoryService,dCATService,rpsService,channelHistoryRepository,recentsRepository);
		HistoryView historyView = planExpiry.getRechargeSuggestionCard("9660027375","jio:mobile:haryana", channelHistories);
		System.out.println("History View" + historyView.isInvalidPlan());

		List<ViewElementInfo> viewElementInfoList = new ArrayList<ViewElementInfo>();
		viewElementInfoList = historyView.getDisplayValues();

		for (ViewElementInfo viewElementInfo : viewElementInfoList) {

			if(viewElementInfo.getType() == "ds1") {
				assertEquals(viewElementInfo.getValue(),"Data 3GB");
			}

			if(viewElementInfo.getType() == "ds2") {
				assertEquals(viewElementInfo.getValue(),"Validity 40 days");
			}

			if(viewElementInfo.getType() == "ds3") {
				assertEquals(viewElementInfo.getValue(),"Talktime ₹1000 hrs");
			}

			if(viewElementInfo.getType() == "ds4") {
				assertEquals(viewElementInfo.getValue(),"SMS 2000");
			}

			if(viewElementInfo.getType() == "ds5") {
				assertEquals(viewElementInfo.getValue(),"20 days extra validity");
			}

			if(viewElementInfo.getType() == "ds6") {
				assertEquals(viewElementInfo.getValue(),"prime video free");
			}
        }

		assertEquals(false,historyView.isInvalidPlan());
		when(rpsService.getPlanDetails("7166", "12.12.12","web","jio","200","haryana",null,"9660027375","mobile")).thenReturn(null);
		when(planExpiryHistoryService.getPlanExpiryByRecharge("9660027375","jio","haryana","mobile")).thenReturn(planExpiryHistoryList);

		PlanExpiry planExpiry1 = new PlanExpiry(planExpiryHistoryService,dCATService,rpsService,channelHistoryRepository,recentsRepository);
		HistoryView historyView1 = planExpiry1.getRechargeSuggestionCard("9660027375","jio:mobile:haryana", channelHistories);
		System.out.println("History View" + historyView1.isInvalidPlan());

		List<ViewElementInfo> viewElementInfoList1 = new ArrayList<ViewElementInfo>();
		viewElementInfoList1 = historyView1.getDisplayValues();

		for (ViewElementInfo viewElementInfo : viewElementInfoList1) {

			if(viewElementInfo.getType() == "ds1") {
				assertEquals(viewElementInfo.getValue(),"Data 2GB");
			}

			if(viewElementInfo.getType() == "ds2") {
				assertEquals(viewElementInfo.getValue(),"Validity 30 days");
			}

			if(viewElementInfo.getType() == "ds3") {
				assertEquals(viewElementInfo.getValue(),"Talktime ₹1000 hrs");
			}

			if(viewElementInfo.getType() == "ds4") {
				assertEquals(viewElementInfo.getValue(),"SMS 2000");
			}

			if(viewElementInfo.getType() == "ds5") {
				assertEquals(viewElementInfo.getValue(),"20 days extra validity");
			}

			if(viewElementInfo.getType() == "ds6") {
				assertEquals(viewElementInfo.getValue(),"wynk free");
			}
        }
		assertEquals(true,historyView1.isInvalidPlan());
	}


	@Test
	public void testGetHistoryV2ForThemeDetailsNativeCategory() {
		Map<String, Object> featureConfig = new HashMap<>();
		featureConfig.put("disableChatHistory", false);
		FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfig);
		GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
		historyPageRequest.setNative(true);
		historyPageRequest.setThemehash("1234243453233432");

		HistoryPage historyPage = new HistoryPage(null, 0, null, false, "0", 0, new ChannelDetail());

		when(utilityAggregator.aggregateHistoryInfo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(historyPage);
		ThemeResponse themeResponse = new ThemeResponse();
		String themeStr="[{\"themeType\":\"themeType1\",\"themeDetail\":{\"header1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"footer1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"footer2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true}}}]";
		themeResponse.setThemes(JsonUtils.parseJsonToList(themeStr, ThemeDto.class));
		when(newThemeService.getThemesForNativeCategory()).thenReturn(themeResponse);
		HistoryPage response = rechargeSagaService.getHistoryV2(historyPageRequest);
		assertNotNull(response);
		assertNotNull(response.getThemeDetails());
	}

	@Test
	public void testGetHistoryV2ForThemeDetailsNonNativeCategory() {
		Map<String, Object> featureConfig = new HashMap<>();
		featureConfig.put("disableChatHistory", false);
		FeatureConfigCache.getInstance().setFeatureConfigMap(featureConfig);
		GetHistoryPageDTO historyPageRequest = new GetHistoryPageDTO();
		historyPageRequest.setNative(false);
		historyPageRequest.setThemehash("1234243453233431");

		HistoryPage historyPage = new HistoryPage(null, 0, null, false, "0", 0, new ChannelDetail());

		when(utilityAggregator.aggregateHistoryInfo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(historyPage);
		ThemeResponse themeResponse = new ThemeResponse();
		String themeStr="[{\"themeType\":\"themeType1\",\"themeDetail\":{\"header1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"header5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"ds5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta3\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta4\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"cta5\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"footer1\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true},\"footer2\":{\"valueColor\":\"valueColor\",\"keyColor\":\"keyColor\",\"bgColor\":\"808080\",\"borderColor\":\"borderColor\",\"valueFontWeight\":\"valueFontWeight\",\"keyFontWeight\":\"keyFontWeight\",\"rightThumbImage\":\"rightThumbImage\",\"leftThumbImage\":\"leftThumbnail\",\"richTextColor\":\"richTextColor\",\"isAmount\":true}}}]";
		themeResponse.setThemes(JsonUtils.parseJsonToList(themeStr, ThemeDto.class));
		when(newThemeService.getThemesForNativeCategory()).thenReturn(themeResponse);
		HistoryPage response = rechargeSagaService.getHistoryV2(historyPageRequest);
		assertNotNull(response);
	}


}
