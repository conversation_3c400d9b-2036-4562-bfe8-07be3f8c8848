package com.paytm.saga;

import static org.junit.Assert.assertEquals;

import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.service.aggregator.viewmanager.impl.ElectricityHeaderManager;
import com.paytm.saga.util.DateUtil;

public class ElectricityHeaderManagerTest {
	@InjectMocks
	private ElectricityHeaderManager electricityHeaderManager;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void newBillCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("13-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.NEW_BILL_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	
	@Test
	public void billOverDueCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("12-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.BILL_OVER_DUE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	
	@Test
	public void partialBillCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("11-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.BILL_PARTIAL_PAID_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	
	@Test
	public void billAfterDueDateCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("10-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.BILL_AFTER_OVER_DUE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	
	@Test
	public void successRechargeCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("08-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.ELECTRICITY_SUCCESS_RECHARGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	@Test
	public void failureRechargeCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("07-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.ELECTRICITY_FAILURE_RECHARGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	@Test
	public void pendingRechargeCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("06-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.ELECTRICITY_PENDING_RECHARGE_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	
	@Test
	public void billExpiredCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("05-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.BILL_EXPIRED_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	
	@Test
	public void markAsPaidCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("04-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.MARKED_AS_PAID_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 2);
	}
	@Test
	public void dropOffCard() {
		HistoryView historyView = new HistoryViewBuilder()
				.setEventDate(DateUtil.stringToDate("03-12-2020", DateFormats.DATE_FORMAT)).setLastCard(true)
				.setThemeType(CardThemeTypes.DROPOFF_CARD_THEME).build();
		List<ViewElementInfo> viewElementInfos = electricityHeaderManager.getHeaders(historyView);
		assertEquals(viewElementInfos.size(), 3);
	}
}
