package com.paytm.saga;

import static org.junit.Assert.assertEquals;

import java.util.Date;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CommonConsts;
import com.paytm.saga.common.constant.EventTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.builder.ReminderHistoryBuilder;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.aggregator.cardmanager.CurrentBillCard;
import com.paytm.saga.service.aggregator.viewmanager.declaration.ViewManager;
import com.paytm.saga.util.DateUtil;

public class CurrentBillCardTest {
	@Mock
	public ViewManager ctaManager;
	@Mock
	public ViewManager displayValuesManager;
	@Mock
	public ViewManager headersViewManager;
	@InjectMocks
	private CurrentBillCard currentBillCard;

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void newBillTest() {
		CardInfoDto cardInfoDto = new CardInfoDto();
		ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
				.setBill_date(new Date()).setDue_date(new Date()).setAmount(100.0).build();
		cardInfoDto.setReminderHistory(reminderHistory);
		HistoryView historyView = currentBillCard.getCardInfo(cardInfoDto);
		assertEquals(EventTypes.BILL, historyView.getEventType());
		assertEquals(CardThemeTypes.NEW_BILL_CARD_THEME, historyView.getThemeType());
	}

	@Test
	public void partialBillTest() {
		CardInfoDto cardInfoDto = new CardInfoDto();
		ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
				.setBill_date(new Date()).setDue_date(new Date()).setAmount(100.0).setIsPartial(1).build();
		cardInfoDto.setReminderHistory(reminderHistory);
		HistoryView historyView = currentBillCard.getCardInfo(cardInfoDto);
		assertEquals(EventTypes.BILL, historyView.getEventType());
		assertEquals(CardThemeTypes.BILL_PARTIAL_PAID_CARD_THEME, historyView.getThemeType());
	}

	@Test
	public void billOverDueTest() {
		CardInfoDto cardInfoDto = new CardInfoDto();
		ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
				.setBill_date(DateUtil.dateIncrDecr(new Date(), -CommonConsts.BILL_OVERDUE_DAYS))
				.setDue_date(DateUtil.dateIncrDecr(new Date(), -CommonConsts.BILL_OVERDUE_DAYS)).setAmount(100.0)
				.setIsPartial(1).build();
		cardInfoDto.setReminderHistory(reminderHistory);
		HistoryView historyView = currentBillCard.getCardInfo(cardInfoDto);
		assertEquals(EventTypes.BILL, historyView.getEventType());
		assertEquals(CardThemeTypes.BILL_OVER_DUE_CARD_THEME, historyView.getThemeType());
	}

	@Test
	public void billAfterOverDueTest() {
		CardInfoDto cardInfoDto = new CardInfoDto();
		ReminderHistory reminderHistory = new ReminderHistoryBuilder().setService("electricity").setOperator("operator")
				.setBill_date(DateUtil.dateIncrDecr(new Date(), -CommonConsts.BILL_OVERDUE_DAYS - 1))
				.setDue_date(DateUtil.dateIncrDecr(new Date(), -CommonConsts.BILL_OVERDUE_DAYS - 1)).setAmount(100.0)
				.setIsPartial(1).build();
		cardInfoDto.setReminderHistory(reminderHistory);
		HistoryView historyView = currentBillCard.getCardInfo(cardInfoDto);
		assertEquals(EventTypes.BILL, historyView.getEventType());
		assertEquals(CardThemeTypes.BILL_AFTER_OVER_DUE_CARD_THEME, historyView.getThemeType());
	}
}
