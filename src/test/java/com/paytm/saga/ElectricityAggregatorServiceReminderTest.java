package com.paytm.saga;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.paytm.saga.common.DeepLinkManager;
import com.paytm.saga.common.constant.CardThemeTypes;
import com.paytm.saga.common.constant.CardTypes;
import com.paytm.saga.common.constant.Constants;
import com.paytm.saga.common.constant.DateFormats;
import com.paytm.saga.common.constant.DeepLinkTypes;
import com.paytm.saga.dto.CardInfoDto;
import com.paytm.saga.dto.ChannelHistoryPage;
import com.paytm.saga.dto.HistoryPage;
import com.paytm.saga.dto.HistoryView;
import com.paytm.saga.dto.ViewElementInfo;
import com.paytm.saga.dto.builder.ChannelHistoryBuilder;
import com.paytm.saga.dto.builder.HistoryViewBuilder;
import com.paytm.saga.dto.builder.ReminderHistoryBuilder;
import com.paytm.saga.model.ChannelHistory;
import com.paytm.saga.model.ReminderHistory;
import com.paytm.saga.service.ChannelHistoryFinalizedService;
import com.paytm.saga.service.ChannelHistoryService;
import com.paytm.saga.service.DropOffService;
import com.paytm.saga.service.ReminderHistoryService;
import com.paytm.saga.service.aggregator.ElectricityAggregatorService;
import com.paytm.saga.service.aggregator.cardmanager.ChannelHistoryFinalizedUtil;
import com.paytm.saga.service.aggregator.cardmanager.CurrentBillCard;
import com.paytm.saga.service.aggregator.cardmanager.DateCard;
import com.paytm.saga.service.aggregator.cardmanager.DropOffCard;
import com.paytm.saga.service.aggregator.cardmanager.MarkedAsPaidCard;
import com.paytm.saga.service.aggregator.cardmanager.RechargeCard;
import com.paytm.saga.service.aggregator.cardmanager.ScratchCard;
import com.paytm.saga.util.DateUtil;
import com.paytm.saga.util.OMSToRechargeStatus;

public class ElectricityAggregatorServiceReminderTest {
	@Mock
	public ChannelHistoryFinalizedService channelHistoryFinalizedService;
	@Mock
	public ChannelHistoryFinalizedUtil channelHistoryFinalizedUtil;
	@Mock
	public DateCard dateCardService;
	@Mock
	public ScratchCard scratchCardService;
	@Mock
	public CurrentBillCard currentBillCard;
	@Mock
	public RechargeCard rechargeCard;
	@Mock
	public DropOffCard dropoffCard;
	@Mock
	public ChannelHistoryService channelHistoryService;
	@Mock
	protected DropOffService dropOffService;
	@Mock
	public ReminderHistoryService reminderHistoryService;
	@Mock
	public MarkedAsPaidCard markedAsPaidCardService;
	@InjectMocks
	public ElectricityAggregatorService electricityAggregatorService;

	private static final Date TRANSACTION_DATE = DateUtil.minutesIncrDecr(new Date(), -5);
	private static final Date TRANSACTION_UPDATE_DATE = DateUtil.minutesIncrDecr(new Date(), -4);
	private static final Date CREATED_DATE = DateUtil.minutesIncrDecr(new Date(), -3);
	private static final Date FINALIZED_TRANSACTION_DATE = DateUtil
			.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60), -5);
	private static final Date FINALIZED_TRANSACTION_UPDATE_DATE = DateUtil
			.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60), -4);
	private static final Date FINALIZED_CREATED_DATE = DateUtil.minutesIncrDecr(DateUtil.dateIncrDecr(new Date(), -60),
			-3);

	@Before
	public void setup() {
		MockitoAnnotations.initMocks(this);
	}

	//@Test
	public void nsxcoTxn() {
		List<ChannelHistory> channelHistories = new ArrayList<ChannelHistory>();
		Long customerId = 1008952334L;
		String rechargeNumber = "0557622211";
		String service = "electricity";
		String operator = "hublii electricity supply company ltd. (hescom)";
		String paytype = "postpaid";
		Date transactionDate = TRANSACTION_DATE;
		Date transactionUpdateDate = TRANSACTION_UPDATE_DATE;
		Date eventUpdateDate = CREATED_DATE;

		Map<String, String> billsObject = new HashMap<>();
		billsObject.put(Constants.OMSConstants.BILL_DATE, "2021-03-19 12:00:11");
		billsObject.put(Constants.OMSConstants.DUE_DATE, "2021-04-19 12:00:11");

		List<ReminderHistory> reminderHistoryList = new ArrayList<ReminderHistory>();
		ReminderHistory reminderHistory = new ReminderHistoryBuilder().setAmount(100.0).setBill_date(new Date())
				.setCreated_at(new Date()).setCustomerId(customerId).setDue_date(new Date()).setIs_automatic(0)
				.setIsPartial(0).setOperator(operator).setPaytype(paytype).setRechargeNumber(rechargeNumber)
				.setService(service).setStatus(1).setUpdatedAt(new Date()).build();
		reminderHistoryList.add(reminderHistory);
		when(reminderHistoryService.findByCustomerIdAndRechargeNumberAndServiceAndOperator(customerId, rechargeNumber,
				service, operator)).thenReturn(reminderHistoryList);

		CardInfoDto reminderCardDTO = new CardInfoDto();
		reminderCardDTO.setReminderHistory(reminderHistory);

		when(currentBillCard.getCardInfo(reminderCardDTO)).thenReturn(
				new HistoryViewBuilder().setThemeType(CardThemeTypes.BILL_AFTER_OVER_DUE_CARD_THEME).build());

		ChannelHistory channelHistory = new ChannelHistoryBuilder().setAmount("757").setBillsObj(billsObject)
				.setCategoryId(26L).setOperator(operator).setCreatedTime(eventUpdateDate).setCustomerId(customerId)
				.setEventType("RECHARGE").setFinalisedState(false).setInResponseCode("00").setItemId(13353752763L)
				.setOrderId(128623347L).setPaytype(paytype).setProductId(1103094L).setRechargeNumber(rechargeNumber)
				.setService(service).setStatus("6").setTransactionTime(transactionDate)
				.setTransactionUpdateTime(transactionUpdateDate).build();

		channelHistories.add(channelHistory);
		when(channelHistoryService.getPageOfHistory(customerId, rechargeNumber, service)).thenReturn(channelHistories);

		ChannelHistoryPage channelHistoryPage = new ChannelHistoryPage();
		channelHistoryPage.setAvailableNext(false);
		channelHistoryPage.setChannelHistories(new ArrayList<ChannelHistory>());
		when(channelHistoryFinalizedUtil.convertFinalizedToHistory(customerId, rechargeNumber, service, 1, null))
				.thenReturn(channelHistoryPage);

		when(dateCardService.addDateInfoCard(transactionDate, null, false)).thenReturn(null);

		ViewElementInfo dateHeader = new ViewElementInfo();
		dateHeader.setValue(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT));
		List<ViewElementInfo> headers = new ArrayList<ViewElementInfo>();
		headers.add(dateHeader);
		HistoryView dateCard = new HistoryViewBuilder().setEventType(CardTypes.DATE_CHANGE_CARD)
				.setThemeType(CardThemeTypes.DATE_CHANGE_CARD_THEME).setEventDate(transactionDate).setLastCard(false)
				.setCreatedDate(DateUtil.dateFormatter(transactionDate, DateFormats.DATE_TIME_FORMAT))
				.setHeadings(headers).build();
		when(dateCardService.addDateInfoCard(transactionDate, transactionDate, true)).thenReturn(dateCard);

		when(scratchCardService.getScratchCard(12862601947L)).thenReturn(new ArrayList<HistoryView>());

		HistoryView rechargeViewCard = new HistoryViewBuilder().setEventDate(channelHistory.getTransactionTime())
				.setCreatedDate(
						DateUtil.dateFormatter(channelHistory.getTransactionTime(), DateFormats.DATE_TIME_FORMAT))
				.setAmount(Double.parseDouble(channelHistory.getAmount()))
				.setStatus(OMSToRechargeStatus.getRechargeStatusByOMSStatus(channelHistory.getStatus(),
						channelHistory.getInResponseCode(), channelHistory.getPaymentStatus()))
				.setOrderId(channelHistory.getOrderId())
				.setCarddeeplink(DeepLinkManager.getDeeplinkByType(DeepLinkTypes.RECHARGE_CARD_DEEP_LINK))
				.setPlanDetail(channelHistory.getDisplayValues()).setEventDate(channelHistory.getTransactionTime())
				.setCircle(channelHistory.getCircle()).setOperator(channelHistory.getOperator())
				.setPayType(channelHistory.getPaytype()).setEventType(channelHistory.getEventType()).setThemeType("")
				.setHeadings(null).setDisplayValues(null).setCta(null).build();

		CardInfoDto cardInfoDto = new CardInfoDto();
		cardInfoDto.setChannelHistory(channelHistory);
		cardInfoDto.setIgnoreCTA(false);
		when(rechargeCard.getCardInfo(cardInfoDto)).thenReturn(rechargeViewCard);

		HistoryPage response = electricityAggregatorService.aggregateHistoryInfo(customerId, rechargeNumber, service,
				null, operator, null, null, null, null);
		System.out.println("theme type"+response.getCards().get(0).getThemeType());
		System.out.println("theme type"+response.getCards().get(1).getThemeType());
		assertEquals(3, response.getCards().size());

	}
}
