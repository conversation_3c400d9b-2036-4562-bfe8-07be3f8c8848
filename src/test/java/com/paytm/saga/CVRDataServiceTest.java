package com.paytm.saga;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.saga.common.metrics.MetricsHelper;
import com.paytm.saga.dto.catalogue.CvrDataResponse;
import com.paytm.saga.dto.catalogue.CVRProductCache;
import com.paytm.saga.dto.catalogue.ProductMin;
import com.paytm.saga.service.CVRDataService;
import com.paytm.saga.service.GenericRestClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class CVRDataServiceTest {

    @Mock
    private GenericRestClient genericRestClient;

    @Mock
    private MetricsHelper metricsHelper;

    @InjectMocks
    private CVRDataService cvrDataService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWhenSuccessResponse() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        String responseAsString = "{\"status\":200,\"data\":{\"productData\":[{\"id\":********,\"product_id\":0,\"service\":\"\",\"paytype\":\"\",\"operator\":\"idfc first bank limited\",\"circle\":\"\",\"producttype\":\"\",\"paytype_label\":\"\",\"operator_label\":\"\",\"vertical_id\":null,\"status\":null,\"category_id\":null,\"brand\":null,\"merchant_id\":null,\"attributes\":null,\"fulfillment_service\":0,\"validate\":null,\"price\":null,\"thumbnail\":\"\"}]}}";

        CvrDataResponse response = objectMapper.readValue(responseAsString, CvrDataResponse.class);
        Mockito.when(genericRestClient.retryGet(Mockito.any(), Mockito.any(), Mockito.anyMap(), Mockito.any(), Mockito.any())).thenReturn(response);

        boolean isSynced = cvrDataService.syncCVRData();
        assertEquals(true, isSynced);
    }


    @Test
    public void testWhenFailureResponse() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        String responseAsString = "{\"status\":400,\"data\":{\"productData\":[]}}";

        CvrDataResponse response = objectMapper.readValue(responseAsString, CvrDataResponse.class);
        Mockito.when(genericRestClient.retryGet(Mockito.any(), Mockito.any(), Mockito.anyMap(), Mockito.any(), Mockito.any())).thenReturn(response);

        boolean isSynced = cvrDataService.syncCVRData();
        assertEquals(false, isSynced);
    }

    // @Test
    public void testSyncWhenSuccess() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        String responseAsString = "{\"status\":200,\"data\":{\"productData\":[{\"id\":********,\"product_id\":100,\"service\":\"\",\"paytype\":\"\",\"operator\":\"idfc first bank limited\",\"circle\":\"\",\"producttype\":\"\",\"paytype_label\":\"\",\"operator_label\":\"\",\"vertical_id\":null,\"status\":null,\"category_id\":null,\"brand\":null,\"merchant_id\":null,\"attributes\":null,\"fulfillment_service\":0,\"validate\":null,\"price\":null,\"thumbnail\":\"\"}]}}";

        CvrDataResponse response = objectMapper.readValue(responseAsString, CvrDataResponse.class);

        Mockito.when(genericRestClient.retryGet(Mockito.any(), Mockito.any(), Mockito.anyMap(), Mockito.any(), Mockito.any())).thenReturn(response);
        responseAsString = "{\"status\":200,\"data\":{\"productData\":[{\"id\":********,\"product_id\":100,\"service\":\"\",\"paytype\":\"\",\"operator\":\"hdfc bank limited\",\"circle\":\"\",\"producttype\":\"\",\"paytype_label\":\"\",\"operator_label\":\"\",\"vertical_id\":null,\"status\":null,\"category_id\":null,\"brand\":null,\"merchant_id\":null,\"attributes\":null,\"fulfillment_service\":0,\"validate\":null,\"price\":null,\"thumbnail\":\"\"}]}}";
        response = objectMapper.readValue(responseAsString, CvrDataResponse.class);

        Mockito.when(genericRestClient.retryGet(Mockito.any(), Mockito.any(), Mockito.anyMap(), Mockito.any(), Mockito.any())).thenReturn(response);

        boolean isSynced = cvrDataService.syncCVRData();
        System.out.print(objectMapper.writeValueAsString(CVRProductCache.getInstance()));
        ProductMin product = CVRProductCache.getInstance().getProductDetails(********L);
        assertEquals(product.getOperator(), "idfc first bank limited");
        assertEquals(true, isSynced);
        isSynced = cvrDataService.syncCVRData();
        assertEquals(true, isSynced);
        product = CVRProductCache.getInstance().getProductDetails(********L);
        assertEquals(product.getOperator(), "idfc first bank limited");

    }


}
