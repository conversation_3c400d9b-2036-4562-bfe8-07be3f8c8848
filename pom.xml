<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.3</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>
    <repositories>
        <repository>
            <id>localisation-client-java</id>
            <name>localisation</name>
            <url>https://nexusartifactory.paytm.com/repository/localisation-client-java/</url>
        </repository>
        <repository>
            <id>mkpt-nexus-paytm-snapshot</id>
            <url>https://nexusartifactory.paytm.com/repository/pff-maven-snapshots/</url>
        </repository>
    </repositories>
    <groupId>com.paytm</groupId>
    <artifactId>saga</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>recharge-saga</name>
    <description>project for Spring Boot</description>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-cloud.version>2023.0.1</spring-cloud.version>
        <log4j2.version>2.23.1</log4j2.version>
        <kafka.version>2.3.0</kafka.version>
        <vault>5.1.0</vault>
        <sonar.core.codeCoveragePlugin>jacoco</sonar.core.codeCoveragePlugin>
        <sonar.jacoco.reportPath>${basedir}/target/coverage-reports/jacoco-unit.exec
        </sonar.jacoco.reportPath>
        <sonar.language>java</sonar.language>
        <sonar.sources>src/main/java/com/paytm/saga/</sonar.sources>
        <sonar.coverage.exclusions>
            **/dto/**/*,
            **/model/**/*,
            **/repository/**,
            **/recent/repository/**,
            **/controller/**,
            **/enums/**,
            **/exception/**,
            **/validators/impl/**,
            **/dto/builder/**,
            **/custom/validator/**,
            **/common/**,
            **/common/configuration/**,
            **/common/exception/**,
            **/RechargeSagaApplication*.java,
            **/service/external/**,
            **/executors/**,
            **/vault/**
        </sonar.coverage.exclusions>
    </properties>

    <dependencies>
        <!-- Spring Cloud Sleuth -->
        <!--<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>-->
        <!-- Add log4j2 for logging -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <version>3.4.4</version>
        </dependency>

        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>5.0.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.datastax.oss</groupId>
            <artifactId>java-driver-core</artifactId>
            <version>4.17.0</version> <!-- or the latest compatible version -->
        </dependency>

        <dependency>
            <groupId>com.newrelic.agent.java</groupId>
            <artifactId>newrelic-api</artifactId>
            <version>5.3.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.0.1-jre</version>
        </dependency>

        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-artifact</artifactId>
            <version>3.6.3</version>
        </dependency>

        <!-- In order to use log4j2 logging library other than Logback, exclude
            default logback from our dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-cassandra</artifactId>
        </dependency>
        <!-- <dependency> <groupId>org.springframework.boot</groupId> <artifactId>spring-boot-starter</artifactId>
            </dependency> -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- Spring kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>3.5.1</version> <!-- Match closely with Spring Kafka 3.2.3 -->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.11</version>
        </dependency>
        <dependency>
            <groupId>com.datadoghq</groupId>
            <artifactId>java-dogstatsd-client</artifactId>
            <version>2.3</version>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.2.0</version>
        </dependency>
        <!-- Spring cloud vault -->
        <dependency>
            <groupId>com.bettercloud</groupId>
            <artifactId>vault-java-driver</artifactId>
            <version>${vault}</version>
        </dependency>



        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.10.0</version>
        </dependency>
		<!-- In order to use log4j2 logging library other than Logback, exclude
			default logback from our dependencies -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-cassandra</artifactId>
		</dependency>
		<dependency>
			<groupId>com.codahale.metrics</groupId>
			<artifactId>metrics-core</artifactId>
			<version>3.0.2</version>
		</dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20201115</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
		  	<groupId>org.modelmapper</groupId>
		  	<artifactId>modelmapper</artifactId>
		  	<version>2.3.0</version>
		</dependency>

        <dependency>
            <groupId>com.paytm</groupId>
            <artifactId>localisation</artifactId>
            <version>17.0.2-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>

        <dependency>
            <groupId>com.paytm.recharges</groupId>
            <artifactId>recent-utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.paytm.recharges</groupId>
            <artifactId>custom_logger</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <!-- <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-log4j2</artifactId>
       </dependency>
           <groupId>org.apache.logging.log4j</groupId>
           <artifactId>log4j-api</artifactId>
       </dependency>
       <dependency>
           <groupId>org.apache.logging.log4j</groupId>
           <artifactId>log4j-core</artifactId>
       </dependency> -->
        <!-- <dependency> <groupId>org.apache.logging.log4j</groupId> <artifactId>log4j-api</artifactId>
            </dependency> <dependency> <groupId>org.apache.logging.log4j</groupId> <artifactId>log4j-core</artifactId>
            </dependency> -->



<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-data-jpa</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-data-jpa</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

	<dependency>
		<groupId>com.fasterxml.jackson.dataformat</groupId>
		<artifactId>jackson-dataformat-csv</artifactId>
		<version>2.9.8</version>
	</dependency>

    <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <scope>test</scope>
    </dependency>

    <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
    </dependency>

    <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus</artifactId>
        <scope>runtime</scope>
    </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <!--<exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>-->
            </exclusions>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                </configuration>
                <executions>
                    <execution>
                        <id>build-info</id>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                        <configuration>
                            <additionalProperties>
                                <type>executable-jar</type>
                            </additionalProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Code Coverage report generation -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <configuration>
                    <excludes>
                        <exclude>**/*src/main/java/com/paytm/saga/dto/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/model/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/repository/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/recent/repository/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/controller/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/enums/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/exception/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/validators/impl/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/dto/builder/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/custom/validator/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/common/**</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/common/configuration/*</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/common/exception/*</exclude>
                        <exclude>**/*src/main/java/com/paytm/saga/RechargeSagaApplication**</exclude>


                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>generate-code-coverage-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
